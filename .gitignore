# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 临时文件
*.tmp
*.temp
*.log
*.cache

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Java相关
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# 文档编译产物
*.pdf
*.docx
*.pptx

# 敏感信息
config.local.*
*.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 备份文件
*.bak
*.backup
*~

# 忽略大文件
*.zip
*.pdf
docs/classes/IFBS/
docs/classes/MI/

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Environment
.env
.env.local
.env.production
.env.test

# Build outputs
dist/
build/ 