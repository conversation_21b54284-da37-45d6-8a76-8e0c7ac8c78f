"""
生物医学MBSE平台 - AI引擎模块
=====================================

迁移自XML元数据系统v3.0的核心AI引擎，
扩展支持生物医学领域的专业功能。

核心引擎：
- connection: 三维连接分析引擎
- intelligence: AI智能分析引擎  
- caching: 自适应缓存引擎
- loading: 智能预加载引擎
- parsing: 分层解析引擎
- perspectives: 视角管理引擎
"""

__version__ = "1.0.0"
__migration_source__ = "XML元数据系统v3.0"

# 导入核心引擎组件
try:
    from .connection import ConnectionEngine
    from .intelligence import IntelligenceEngine
    from .caching import CachingEngine
    from .loading import LoadingEngine
    _engines_available = True
except ImportError as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"引擎组件导入失败: {e}")
    _engines_available = False

__all__ = ["ConnectionEngine", "IntelligenceEngine", "CachingEngine", "LoadingEngine"]
