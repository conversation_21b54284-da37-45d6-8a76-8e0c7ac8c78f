"""
自适应缓存系统模块

提供多级缓存管理、动态策略切换和智能性能优化功能。

主要组件:
- AdaptiveCacheSystem: 自适应缓存系统主类
- MultiLevelCacheManager: 多级缓存管理器
- DynamicStrategySelector: 动态策略选择器
- CacheLevel: 缓存级别枚举
- CacheStrategy: 缓存策略枚举
- AdaptiveConfig: 自适应配置类

作者: XML元数据系统开发团队
版本: v1.0.0
"""

from .adaptive_cache_system import (
    AdaptiveCacheSystem,
    MultiLevelCacheManager,
    DynamicStrategySelector,
    CacheLevel,
    CacheStrategy,
    AdaptiveConfig,
    CacheItem,
    CacheMetrics,
    PerformanceMetric
)

__all__ = [
    'AdaptiveCacheSystem',
    'MultiLevelCacheManager', 
    'DynamicStrategySelector',
    'CacheLevel',
    'CacheStrategy',
    'AdaptiveConfig',
    'CacheItem',
    'CacheMetrics',
    'PerformanceMetric'
]

__version__ = '1.0.0'
__author__ = 'XML元数据系统开发团队' 