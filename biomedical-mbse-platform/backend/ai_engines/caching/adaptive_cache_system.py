#!/usr/bin/env python3
"""
自适应缓存系统 - Adaptive Cache System

核心功能：
1. 动态缓存策略切换
2. 多级缓存协调管理
3. 智能性能优化
4. 与智能预加载系统集成

基于任务5智能预加载系统的成功经验(93%命中率，18,000/s吞吐量)
作者: XML元数据系统开发团队
版本: v1.0.0
"""

import logging
import asyncio
import numpy as np
import psutil
from typing import Dict, List, Tuple, Optional, Any, Set, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter, deque, OrderedDict
from datetime import datetime, timedelta
import json
import threading
import time
from pathlib import Path

# 导入智能预加载系统组件
try:
    from engines.loading.intelligent_preloader import (
        IntelligentPreloader, AccessPatternAnalyzer, 
        AccessRecord, AccessType, PredictionConfidence
    )
except ImportError:
    # 如果导入失败，提供基础类定义
    class IntelligentPreloader:
        pass
    class AccessPatternAnalyzer:
        pass

# 设置日志
logger = logging.getLogger(__name__)

class CacheLevel(Enum):
    """缓存级别枚举"""
    L1_MEMORY = "l1_memory"      # 内存缓存 - 最快
    L2_SSD = "l2_ssd"           # SSD缓存 - 中等速度
    L3_DISK = "l3_disk"         # 磁盘缓存 - 较慢但容量大

class CacheStrategy(Enum):
    """缓存策略枚举"""
    LRU = "lru"                 # 最近最少使用
    LFU = "lfu"                 # 最少使用频率
    FIFO = "fifo"               # 先进先出
    ARC = "arc"                 # 自适应替换缓存
    ADAPTIVE = "adaptive"       # 自适应策略

class PerformanceMetric(Enum):
    """性能指标枚举"""
    HIT_RATE = "hit_rate"
    RESPONSE_TIME = "response_time"
    MEMORY_USAGE = "memory_usage"
    THROUGHPUT = "throughput"
    EVICTION_RATE = "eviction_rate"

@dataclass
class CacheItem:
    """缓存项"""
    key: str
    data: Any
    level: CacheLevel
    access_count: int = 0
    last_access: datetime = field(default_factory=datetime.now)
    created_at: datetime = field(default_factory=datetime.now)
    size_bytes: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CacheMetrics:
    """缓存指标"""
    hit_rate: float
    miss_rate: float
    response_time_avg: float
    memory_usage_mb: float
    throughput_per_sec: float
    eviction_rate: float
    level_distribution: Dict[CacheLevel, int]
    strategy_effectiveness: Dict[CacheStrategy, float]

@dataclass
class AdaptiveConfig:
    """自适应配置"""
    l1_max_size: int = 1000
    l2_max_size: int = 5000
    l3_max_size: int = 20000
    memory_threshold_percent: float = 80.0
    performance_check_interval: int = 60  # 秒
    strategy_switch_threshold: float = 0.05  # 5%性能差异触发切换
    min_samples_for_switch: int = 100

class MultiLevelCacheManager:
    """多级缓存管理器"""
    
    def __init__(self, config: AdaptiveConfig):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        
        # 三级缓存存储
        self.l1_cache = OrderedDict()  # 内存缓存
        self.l2_cache = OrderedDict()  # SSD缓存
        self.l3_cache = OrderedDict()  # 磁盘缓存
        
        # 缓存统计
        self.cache_stats = {
            'l1_hits': 0, 'l1_misses': 0,
            'l2_hits': 0, 'l2_misses': 0,
            'l3_hits': 0, 'l3_misses': 0,
            'promotions': 0, 'demotions': 0,
            'evictions': 0
        }
        
        # 性能监控
        self.response_times = deque(maxlen=1000)
        self.access_timestamps = deque(maxlen=1000)
        
        self.logger.info("多级缓存管理器初始化完成")
    
    async def get(self, key: str) -> Tuple[Optional[Any], CacheLevel]:
        """获取缓存项"""
        start_time = time.time()
        
        # L1缓存查找
        if key in self.l1_cache:
            item = self.l1_cache[key]
            item.access_count += 1
            item.last_access = datetime.now()
            # 移到最前面（LRU）
            self.l1_cache.move_to_end(key)
            
            self.cache_stats['l1_hits'] += 1
            response_time = time.time() - start_time
            self.response_times.append(response_time)
            self.access_timestamps.append(datetime.now())
            
            return item.data, CacheLevel.L1_MEMORY
        
        self.cache_stats['l1_misses'] += 1
        
        # L2缓存查找
        if key in self.l2_cache:
            item = self.l2_cache[key]
            item.access_count += 1
            item.last_access = datetime.now()
            
            # 提升到L1缓存
            await self._promote_to_l1(key, item)
            
            self.cache_stats['l2_hits'] += 1
            self.cache_stats['promotions'] += 1
            response_time = time.time() - start_time
            self.response_times.append(response_time)
            self.access_timestamps.append(datetime.now())
            
            return item.data, CacheLevel.L2_SSD
        
        self.cache_stats['l2_misses'] += 1
        
        # L3缓存查找
        if key in self.l3_cache:
            item = self.l3_cache[key]
            item.access_count += 1
            item.last_access = datetime.now()
            
            # 提升到L2缓存
            await self._promote_to_l2(key, item)
            
            self.cache_stats['l3_hits'] += 1
            self.cache_stats['promotions'] += 1
            response_time = time.time() - start_time
            self.response_times.append(response_time)
            self.access_timestamps.append(datetime.now())
            
            return item.data, CacheLevel.L3_DISK
        
        self.cache_stats['l3_misses'] += 1
        response_time = time.time() - start_time
        self.response_times.append(response_time)
        self.access_timestamps.append(datetime.now())
        
        return None, None
    
    async def put(self, key: str, data: Any, preferred_level: CacheLevel = CacheLevel.L1_MEMORY):
        """存储缓存项"""
        item = CacheItem(
            key=key,
            data=data,
            level=preferred_level,
            size_bytes=self._estimate_size(data)
        )
        
        if preferred_level == CacheLevel.L1_MEMORY:
            await self._put_l1(key, item)
        elif preferred_level == CacheLevel.L2_SSD:
            await self._put_l2(key, item)
        else:
            await self._put_l3(key, item)
    
    async def _put_l1(self, key: str, item: CacheItem):
        """存储到L1缓存"""
        # 检查容量限制
        if len(self.l1_cache) >= self.config.l1_max_size:
            await self._evict_from_l1()
        
        item.level = CacheLevel.L1_MEMORY
        self.l1_cache[key] = item
    
    async def _put_l2(self, key: str, item: CacheItem):
        """存储到L2缓存"""
        if len(self.l2_cache) >= self.config.l2_max_size:
            await self._evict_from_l2()
        
        item.level = CacheLevel.L2_SSD
        self.l2_cache[key] = item
    
    async def _put_l3(self, key: str, item: CacheItem):
        """存储到L3缓存"""
        if len(self.l3_cache) >= self.config.l3_max_size:
            await self._evict_from_l3()
        
        item.level = CacheLevel.L3_DISK
        self.l3_cache[key] = item
    
    async def _promote_to_l1(self, key: str, item: CacheItem):
        """提升到L1缓存"""
        # 从L2移除
        if key in self.l2_cache:
            del self.l2_cache[key]
        
        # 添加到L1
        await self._put_l1(key, item)
    
    async def _promote_to_l2(self, key: str, item: CacheItem):
        """提升到L2缓存"""
        # 从L3移除
        if key in self.l3_cache:
            del self.l3_cache[key]
        
        # 添加到L2
        await self._put_l2(key, item)
    
    async def _evict_from_l1(self):
        """从L1缓存驱逐"""
        if not self.l1_cache:
            return
        
        # LRU策略：移除最久未使用的项
        oldest_key = next(iter(self.l1_cache))
        item = self.l1_cache.pop(oldest_key)
        
        # 降级到L2
        await self._put_l2(oldest_key, item)
        self.cache_stats['demotions'] += 1
    
    async def _evict_from_l2(self):
        """从L2缓存驱逐"""
        if not self.l2_cache:
            return
        
        oldest_key = next(iter(self.l2_cache))
        item = self.l2_cache.pop(oldest_key)
        
        # 降级到L3
        await self._put_l3(oldest_key, item)
        self.cache_stats['demotions'] += 1
    
    async def _evict_from_l3(self):
        """从L3缓存驱逐"""
        if not self.l3_cache:
            return
        
        oldest_key = next(iter(self.l3_cache))
        del self.l3_cache[oldest_key]
        self.cache_stats['evictions'] += 1
    
    def _estimate_size(self, data: Any) -> int:
        """估算数据大小"""
        try:
            if isinstance(data, str):
                return len(data.encode('utf-8'))
            elif isinstance(data, (int, float)):
                return 8
            elif isinstance(data, dict):
                return len(json.dumps(data).encode('utf-8'))
            else:
                return len(str(data).encode('utf-8'))
        except:
            return 1024  # 默认1KB
    
    def get_metrics(self) -> CacheMetrics:
        """获取缓存指标"""
        total_hits = (self.cache_stats['l1_hits'] + 
                     self.cache_stats['l2_hits'] + 
                     self.cache_stats['l3_hits'])
        total_misses = (self.cache_stats['l1_misses'] + 
                       self.cache_stats['l2_misses'] + 
                       self.cache_stats['l3_misses'])
        total_requests = total_hits + total_misses
        
        hit_rate = total_hits / max(total_requests, 1)
        miss_rate = total_misses / max(total_requests, 1)
        
        avg_response_time = np.mean(self.response_times) if self.response_times else 0.0
        
        # 计算吞吐量
        if len(self.access_timestamps) >= 2:
            time_span = (self.access_timestamps[-1] - self.access_timestamps[0]).total_seconds()
            throughput = len(self.access_timestamps) / max(time_span, 1)
        else:
            throughput = 0.0
        
        # 内存使用估算
        memory_usage = (len(self.l1_cache) * 1024 + 
                       len(self.l2_cache) * 512 + 
                       len(self.l3_cache) * 256) / (1024 * 1024)  # MB
        
        return CacheMetrics(
            hit_rate=hit_rate,
            miss_rate=miss_rate,
            response_time_avg=avg_response_time,
            memory_usage_mb=memory_usage,
            throughput_per_sec=throughput,
            eviction_rate=self.cache_stats['evictions'] / max(total_requests, 1),
            level_distribution={
                CacheLevel.L1_MEMORY: len(self.l1_cache),
                CacheLevel.L2_SSD: len(self.l2_cache),
                CacheLevel.L3_DISK: len(self.l3_cache)
            },
            strategy_effectiveness={}
        )

class DynamicStrategySelector:
    """动态策略选择器"""
    
    def __init__(self, config: AdaptiveConfig):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.current_strategy = CacheStrategy.ADAPTIVE
        self.strategy_performance = defaultdict(list)
        self.strategy_test_results = {}
        self.last_evaluation = datetime.now()
        
    def select_optimal_strategy(self, 
                              current_metrics: CacheMetrics,
                              access_patterns: Dict[str, Any]) -> CacheStrategy:
        """选择最优缓存策略"""
        
        # 记录当前策略性能
        self.strategy_performance[self.current_strategy].append(current_metrics.hit_rate)
        
        # 检查是否需要重新评估策略
        if self._should_reevaluate_strategy():
            return self._evaluate_and_select_strategy(current_metrics, access_patterns)
        
        return self.current_strategy
    
    def _should_reevaluate_strategy(self) -> bool:
        """判断是否需要重新评估策略"""
        time_since_last = (datetime.now() - self.last_evaluation).total_seconds()
        return time_since_last >= self.config.performance_check_interval
    
    def _evaluate_and_select_strategy(self, 
                                    current_metrics: CacheMetrics,
                                    access_patterns: Dict[str, Any]) -> CacheStrategy:
        """评估并选择策略"""
        self.last_evaluation = datetime.now()
        
        # 分析访问模式特征
        pattern_features = self._analyze_access_patterns(access_patterns)
        
        # 基于模式特征推荐策略
        recommended_strategy = self._recommend_strategy_by_pattern(pattern_features)
        
        # 如果推荐策略与当前不同，且有足够样本，考虑切换
        if (recommended_strategy != self.current_strategy and 
            len(self.strategy_performance[self.current_strategy]) >= self.config.min_samples_for_switch):
            
            # 比较策略性能
            if self._should_switch_strategy(recommended_strategy):
                self.logger.info(f"切换缓存策略: {self.current_strategy} -> {recommended_strategy}")
                self.current_strategy = recommended_strategy
        
        return self.current_strategy
    
    def _analyze_access_patterns(self, access_patterns: Dict[str, Any]) -> Dict[str, float]:
        """分析访问模式特征"""
        features = {
            'temporal_locality': 0.0,    # 时间局部性
            'spatial_locality': 0.0,     # 空间局部性
            'access_frequency_variance': 0.0,  # 访问频率方差
            'working_set_size': 0.0      # 工作集大小
        }
        
        # 简化的模式分析
        if 'temporal_patterns' in access_patterns:
            temporal = access_patterns['temporal_patterns']
            if temporal:
                features['temporal_locality'] = np.std(list(temporal.values()))
        
        if 'user_patterns' in access_patterns:
            user_patterns = access_patterns['user_patterns']
            if user_patterns:
                # 计算序列重复度作为空间局部性指标
                all_sequences = []
                for sequences in user_patterns.values():
                    all_sequences.extend(sequences)
                if all_sequences:
                    unique_ratio = len(set(str(seq) for seq in all_sequences)) / len(all_sequences)
                    features['spatial_locality'] = 1.0 - unique_ratio
        
        return features
    
    def _recommend_strategy_by_pattern(self, pattern_features: Dict[str, float]) -> CacheStrategy:
        """基于模式特征推荐策略"""
        temporal_locality = pattern_features.get('temporal_locality', 0.0)
        spatial_locality = pattern_features.get('spatial_locality', 0.0)
        
        # 策略选择逻辑
        if temporal_locality > 0.7:
            return CacheStrategy.LRU  # 高时间局部性适合LRU
        elif spatial_locality > 0.6:
            return CacheStrategy.LFU  # 高空间局部性适合LFU
        elif temporal_locality < 0.3 and spatial_locality < 0.3:
            return CacheStrategy.FIFO  # 低局部性适合FIFO
        else:
            return CacheStrategy.ARC  # 混合模式适合ARC
    
    def _should_switch_strategy(self, new_strategy: CacheStrategy) -> bool:
        """判断是否应该切换策略"""
        current_performance = np.mean(self.strategy_performance[self.current_strategy][-10:])
        
        # 如果新策略有历史性能数据
        if new_strategy in self.strategy_performance and self.strategy_performance[new_strategy]:
            new_performance = np.mean(self.strategy_performance[new_strategy][-10:])
            improvement = new_performance - current_performance
            return improvement > self.config.strategy_switch_threshold
        
        # 如果新策略没有历史数据，给它一个尝试机会
        return True

class AdaptiveCacheSystem:
    """自适应缓存系统主类"""
    
    def __init__(self, config: Optional[AdaptiveConfig] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or AdaptiveConfig()
        
        # 核心组件
        self.cache_manager = MultiLevelCacheManager(self.config)
        self.strategy_selector = DynamicStrategySelector(self.config)
        
        # 集成智能预加载系统
        try:
            self.preloader = IntelligentPreloader()
            self.preloader_integrated = True
            self.logger.info("智能预加载系统集成成功")
        except Exception as e:
            self.logger.warning(f"智能预加载系统集成失败: {e}")
            self.preloader = None
            self.preloader_integrated = False
        
        # 性能监控
        self.performance_history = deque(maxlen=1000)
        self.optimization_results = []
        
        # 后台优化线程
        self.optimization_thread = None
        self.running = False
        
        self.logger.info("自适应缓存系统初始化完成")
    
    async def get(self, key: str, user_context: Optional[Dict[str, Any]] = None) -> Optional[Any]:
        """获取缓存数据"""
        # 尝试从多级缓存获取
        data, cache_level = await self.cache_manager.get(key)
        
        if data is not None:
            # 缓存命中，记录访问模式
            if self.preloader_integrated and user_context:
                await self._record_access_for_preloader(key, user_context, cache_hit=True)
            return data
        
        # 缓存未命中，检查是否可以从预加载系统获取
        if self.preloader_integrated and user_context:
            preloaded_data = await self._try_preloader_fallback(key, user_context)
            if preloaded_data:
                # 将预加载数据存入缓存
                await self.cache_manager.put(key, preloaded_data)
                return preloaded_data
        
        return None
    
    async def put(self, key: str, data: Any, user_context: Optional[Dict[str, Any]] = None):
        """存储缓存数据"""
        # 存储到多级缓存
        await self.cache_manager.put(key, data)
        
        # 记录访问模式
        if self.preloader_integrated and user_context:
            await self._record_access_for_preloader(key, user_context, cache_hit=False)
    
    async def _record_access_for_preloader(self, 
                                         key: str, 
                                         user_context: Dict[str, Any], 
                                         cache_hit: bool):
        """为预加载系统记录访问"""
        if not self.preloader:
            return
        
        try:
            # 处理访问请求以记录模式
            await self.preloader.process_access_request(key, user_context)
        except Exception as e:
            self.logger.error(f"记录预加载访问失败: {e}")
    
    async def _try_preloader_fallback(self, 
                                    key: str, 
                                    user_context: Dict[str, Any]) -> Optional[Any]:
        """尝试从预加载系统获取数据"""
        if not self.preloader:
            return None
        
        try:
            # 尝试从预加载系统获取
            data, metadata = await self.preloader.process_access_request(key, user_context)
            if metadata.get('cache_hit', False):
                return data
        except Exception as e:
            self.logger.error(f"预加载系统回退失败: {e}")
        
        return None
    
    def start_optimization(self):
        """启动后台优化"""
        if self.optimization_thread and self.optimization_thread.is_alive():
            return
        
        self.running = True
        self.optimization_thread = threading.Thread(target=self._optimization_loop)
        self.optimization_thread.daemon = True
        self.optimization_thread.start()
        
        self.logger.info("后台优化线程已启动")
    
    def stop_optimization(self):
        """停止后台优化"""
        self.running = False
        if self.optimization_thread:
            self.optimization_thread.join(timeout=5)
        
        self.logger.info("后台优化线程已停止")
    
    def _optimization_loop(self):
        """优化循环"""
        while self.running:
            try:
                # 获取当前性能指标
                current_metrics = self.cache_manager.get_metrics()
                self.performance_history.append(current_metrics)
                
                # 获取访问模式
                access_patterns = self._get_access_patterns()
                
                # 动态策略选择
                optimal_strategy = self.strategy_selector.select_optimal_strategy(
                    current_metrics, access_patterns
                )
                
                # 内存压力检查
                self._check_memory_pressure()
                
                # 记录优化结果
                optimization_result = {
                    'timestamp': datetime.now(),
                    'metrics': current_metrics,
                    'strategy': optimal_strategy,
                    'memory_usage_percent': psutil.virtual_memory().percent
                }
                self.optimization_results.append(optimization_result)
                
                # 保持结果历史在合理范围内
                if len(self.optimization_results) > 100:
                    self.optimization_results = self.optimization_results[-100:]
                
            except Exception as e:
                self.logger.error(f"优化循环错误: {e}")
            
            # 等待下次优化
            time.sleep(self.config.performance_check_interval)
    
    def _get_access_patterns(self) -> Dict[str, Any]:
        """获取访问模式"""
        if not self.preloader_integrated:
            return {}
        
        try:
            # 从预加载系统获取访问模式
            patterns = self.preloader.pattern_analyzer.analyze_user_access_patterns()
            return patterns
        except Exception as e:
            self.logger.error(f"获取访问模式失败: {e}")
            return {}
    
    def _check_memory_pressure(self):
        """检查内存压力"""
        memory_percent = psutil.virtual_memory().percent
        
        if memory_percent > self.config.memory_threshold_percent:
            self.logger.warning(f"内存使用率过高: {memory_percent:.1f}%")
            # 触发积极的缓存清理
            asyncio.create_task(self._aggressive_cleanup())
    
    async def _aggressive_cleanup(self):
        """积极的缓存清理"""
        # 清理L1缓存中访问次数最少的项
        if self.cache_manager.l1_cache:
            items_to_remove = []
            for key, item in self.cache_manager.l1_cache.items():
                if item.access_count < 2:  # 访问次数少于2次
                    items_to_remove.append(key)
            
            for key in items_to_remove[:len(self.cache_manager.l1_cache)//4]:  # 清理1/4
                if key in self.cache_manager.l1_cache:
                    item = self.cache_manager.l1_cache.pop(key)
                    await self.cache_manager._put_l2(key, item)
        
        self.logger.info(f"积极清理完成，释放了 {len(items_to_remove)} 个L1缓存项")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        current_metrics = self.cache_manager.get_metrics()
        
        # 计算性能趋势
        if len(self.performance_history) >= 2:
            recent_hit_rates = [m.hit_rate for m in list(self.performance_history)[-10:]]
            hit_rate_trend = np.mean(recent_hit_rates[-5:]) - np.mean(recent_hit_rates[:5])
        else:
            hit_rate_trend = 0.0
        
        # 预加载系统集成状态
        preloader_report = {}
        if self.preloader_integrated:
            try:
                preloader_report = self.preloader.get_performance_report()
            except Exception as e:
                self.logger.error(f"获取预加载报告失败: {e}")
        
        report = {
            'adaptive_cache_metrics': {
                'hit_rate': current_metrics.hit_rate,
                'response_time_avg': current_metrics.response_time_avg,
                'memory_usage_mb': current_metrics.memory_usage_mb,
                'throughput_per_sec': current_metrics.throughput_per_sec,
                'hit_rate_trend': hit_rate_trend
            },
            'multi_level_distribution': {
                level.value: count for level, count in current_metrics.level_distribution.items()
            },
            'strategy_info': {
                'current_strategy': self.strategy_selector.current_strategy.value,
                'optimization_count': len(self.optimization_results)
            },
            'system_integration': {
                'preloader_integrated': self.preloader_integrated,
                'preloader_metrics': preloader_report
            },
            'system_resources': {
                'memory_usage_percent': psutil.virtual_memory().percent,
                'memory_available_gb': psutil.virtual_memory().available / (1024**3)
            }
        }
        
        return report

# 导出主要类
__all__ = [
    'AdaptiveCacheSystem',
    'MultiLevelCacheManager',
    'DynamicStrategySelector',
    'AdaptiveConfig',
    'CacheLevel',
    'CacheStrategy',
    'CacheMetrics',
    'CacheItem'
] 