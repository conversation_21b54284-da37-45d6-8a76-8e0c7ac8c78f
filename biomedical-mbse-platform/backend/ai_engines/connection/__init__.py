"""
XML元数据系统 - 三维连接引擎 v3.0

🔗 实现三维数据连接功能：
- 连点成线引擎：发现元素间的直接关系
- 连线成片引擎：构建关系网络和关系群组  
- 连片成面引擎：建立多维视角的关系体系
- 语义聚类器：智能关系分类
- 关系分析器：深度关系挖掘
- 三维构建器：立体化关系建模

📊 连接能力:
- 直接引用关系发现
- 语义相似性分析
- 结构化关系建模
- 业务视角集成
- 跨域关系映射
"""

__version__ = "3.0.0"
__author__ = "XML元数据系统开发团队"

# 核心连接引擎
from .connection_engine import (
    ConnectionEngine,
    Connection,
    ConnectionLine, 
    ConnectionSurface,
    ConnectionVolume,
    ConnectionType,
    ConnectionStrength
)

# 连线成片引擎
from .line_to_surface import (
    LineToSurfaceEngine
)

# 连片成面引擎  
from .surface_to_volume import (
    SurfaceToVolumeEngine
)

# 导出所有公共接口
__all__ = [
    # 核心引擎
    'ConnectionEngine',
    
    # 数据结构
    'Connection',
    'ConnectionLine',
    'ConnectionSurface', 
    'ConnectionVolume',
    
    # 枚举类型
    'ConnectionType',
    'ConnectionStrength',
    
    # 专门引擎
    'LineToSurfaceEngine',
    'SurfaceToVolumeEngine'
] 