"""
XML元数据系统 - 三维数据连接引擎

实现MBSE理念的"连点成线、连线成面、连面成体"：
- 连点成线引擎：发现和建立元素间的直接关系
- 连线成面引擎：构建关系网络和关系群组
- 连面成体引擎：建立多维视角的关系体系
"""

import logging
import time
import networkx as nx
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import xml.etree.ElementTree as ET
from collections import defaultdict, Counter
import re
import hashlib

# 修复导入路径：应该从core.models.element导入，而不是不存在的core.data_structures
try:
    from core.models.element import Element as EnhancedMetadataNode
    _core_element_available = True
except ImportError:
    # 如果core.models.element不可用，创建一个兼容的替代类
    @dataclass
    class EnhancedMetadataNode:
        id: str
        element: ET.Element
        properties: Dict[str, Any] = field(default_factory=dict)
        
        def get_primary_category(self) -> str:
            """获取主要类别"""
            # 简单的分类逻辑
            tag = self.element.tag.lower()
            if 'requirement' in tag or 'req' in tag:
                return '需求域'
            elif 'class' in tag or 'component' in tag or 'block' in tag:
                return '结构域'
            elif 'activity' in tag or 'action' in tag or 'operation' in tag:
                return '行为域'
            elif 'property' in tag or 'attribute' in tag:
                return '属性域'
            elif 'test' in tag or 'verify' in tag:
                return '验证域'
            else:
                return '未知域'
    _core_element_available = False

def _get_parent_element_safe(element: ET.Element, root: ET.Element) -> Optional[ET.Element]:
    """安全获取父元素的兼容方法"""
    # 尝试使用lxml的getparent方法
    if hasattr(element, 'getparent'):
        return element.getparent()
    
    # 对于ElementTree，需要遍历查找父节点
    for parent in root.iter():
        if element in list(parent):
            return parent
    return None

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConnectionType(Enum):
    """连接类型"""
    DIRECT_REFERENCE = "direct_reference"      # 直接引用
    DEPENDENCY = "dependency"                  # 依赖关系
    INHERITANCE = "inheritance"                # 继承关系
    COMPOSITION = "composition"                # 组合关系
    AGGREGATION = "aggregation"                # 聚合关系
    ASSOCIATION = "association"                # 关联关系
    SEMANTIC = "semantic"                      # 语义关系
    CONTEXTUAL = "contextual"                  # 上下文关系

class ConnectionStrength(Enum):
    """连接强度"""
    STRONG = "strong"      # >0.8
    MEDIUM = "medium"      # 0.5-0.8
    WEAK = "weak"          # <0.5

@dataclass
class Connection:
    """连接对象"""
    source_id: str
    target_id: str
    connection_type: ConnectionType
    strength: float
    confidence: float
    direction: str = "forward"  # forward, backward, bidirectional
    properties: Dict[str, Any] = field(default_factory=dict)
    discovery_method: str = ""
    creation_time: datetime = field(default_factory=datetime.now)
    
    def get_strength_level(self) -> ConnectionStrength:
        """获取强度级别"""
        if self.strength > 0.8:
            return ConnectionStrength.STRONG
        elif self.strength > 0.5:
            return ConnectionStrength.MEDIUM
        else:
            return ConnectionStrength.WEAK

@dataclass
class ConnectionLine:
    """连线 - 一组相关连接"""
    line_id: str
    connections: List[Connection]
    line_type: str
    semantic_role: str
    total_strength: float
    path_length: int
    business_meaning: str = ""
    
    def get_connection_count(self) -> int:
        """获取连接数量"""
        return len(self.connections)
    
    def get_endpoints(self) -> Tuple[str, str]:
        """获取端点"""
        if not self.connections:
            return "", ""
        first_conn = self.connections[0]
        last_conn = self.connections[-1]
        return first_conn.source_id, last_conn.target_id

@dataclass
class ConnectionSurface:
    """连面 - 一组相关连线"""
    surface_id: str
    connection_lines: List[ConnectionLine]
    surface_type: str
    business_domain: str
    coverage_elements: Set[str]
    complexity_score: float
    surface_metrics: Dict[str, float] = field(default_factory=dict)
    
    def get_total_connections(self) -> int:
        """获取总连接数"""
        return sum(line.get_connection_count() for line in self.connection_lines)
    
    def get_element_count(self) -> int:
        """获取涉及元素数量"""
        return len(self.coverage_elements)

@dataclass
class ConnectionVolume:
    """连体 - 完整的三维关系体系"""
    volume_id: str
    connection_surfaces: List[ConnectionSurface]
    business_perspectives: Dict[str, Dict[str, Any]]
    technical_perspectives: Dict[str, Dict[str, Any]]
    global_metrics: Dict[str, float] = field(default_factory=dict)
    relationship_graph: Optional[nx.DiGraph] = None
    
    def get_total_elements(self) -> int:
        """获取总元素数"""
        all_elements = set()
        for surface in self.connection_surfaces:
            all_elements.update(surface.coverage_elements)
        return len(all_elements)

class ConnectionEngine:
    """三维连接引擎"""
    
    def __init__(self, performance_mode: bool = False):
        """初始化连接引擎
        
        Args:
            performance_mode: 是否启用性能模式，大型数据集时提升处理速度
        """
        self.performance_mode = performance_mode
        self.connection_discovery_rules = self._initialize_discovery_rules()
        self.relationship_patterns = self._initialize_relationship_patterns()
        self.business_perspective_rules = self._initialize_business_perspectives()
        
        # 性能优化设置
        if self.performance_mode:
            self.max_connections_per_element = 50  # 限制每个元素的最大连接数
            self.semantic_threshold = 0.8  # 提高语义相似度阈值
            self.enable_semantic_cache = True  # 启用语义缓存
        else:
            self.max_connections_per_element = 200
            self.semantic_threshold = 0.6
            self.enable_semantic_cache = False
        
        # 语义缓存
        self._semantic_cache = {} if self.enable_semantic_cache else None
        
        # 连接统计
        self.connection_stats = {
            'total_connections_discovered': 0,
            'strong_connections': 0,
            'medium_connections': 0,
            'weak_connections': 0,
            'connection_lines_created': 0,
            'connection_surfaces_created': 0,
            'connection_volumes_created': 0,
            'performance_mode': self.performance_mode,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        logger.info(f"三维连接引擎初始化完成 - 性能模式: {'启用' if performance_mode else '禁用'}")
    
    def _initialize_discovery_rules(self) -> Dict[str, Dict[str, Any]]:
        """初始化连接发现规则"""
        return {
            "直接引用规则": {
                "patterns": [
                    {"attribute": "href", "type": ConnectionType.DIRECT_REFERENCE, "strength": 0.9},
                    {"attribute": "idref", "type": ConnectionType.DIRECT_REFERENCE, "strength": 0.9},
                    {"attribute": "type", "type": ConnectionType.DEPENDENCY, "strength": 0.8}
                ],
                "confidence": 0.95
            },
            "命名约定规则": {
                "patterns": [
                    {"suffix": "_ref", "type": ConnectionType.DIRECT_REFERENCE, "strength": 0.7},
                    {"suffix": "_id", "type": ConnectionType.ASSOCIATION, "strength": 0.6},
                    {"prefix": "base_", "type": ConnectionType.INHERITANCE, "strength": 0.8}
                ],
                "confidence": 0.7
            },
            "语义关系规则": {
                "patterns": [
                    {"keyword_similarity": 0.8, "type": ConnectionType.SEMANTIC, "strength": 0.6},
                    {"category_match": True, "type": ConnectionType.CONTEXTUAL, "strength": 0.5}
                ],
                "confidence": 0.6
            },
            "结构关系规则": {
                "patterns": [
                    {"parent_child": True, "type": ConnectionType.COMPOSITION, "strength": 0.9},
                    {"sibling": True, "type": ConnectionType.ASSOCIATION, "strength": 0.4}
                ],
                "confidence": 0.8
            }
        }
    
    def _initialize_relationship_patterns(self) -> Dict[str, Dict[str, Any]]:
        """初始化关系模式"""
        return {
            "需求追踪模式": {
                "description": "需求到设计到测试的追踪链",
                "pattern": ["需求域", "结构域", "验证域"],
                "strength_weights": [0.9, 0.8, 0.9],
                "business_value": 0.95
            },
            "架构依赖模式": {
                "description": "组件间的架构依赖关系",
                "pattern": ["结构域", "结构域"],
                "strength_weights": [0.8, 0.8],
                "business_value": 0.85
            },
            "行为控制模式": {
                "description": "行为到结构的控制关系",
                "pattern": ["行为域", "结构域"],
                "strength_weights": [0.7, 0.8],
                "business_value": 0.8
            },
            "数据流模式": {
                "description": "数据在组件间的流动",
                "pattern": ["属性域", "结构域", "属性域"],
                "strength_weights": [0.6, 0.7, 0.6],
                "business_value": 0.7
            }
        }
    
    def _initialize_business_perspectives(self) -> Dict[str, Dict[str, Any]]:
        """初始化业务视角"""
        return {
            "需求分析师": {
                "focus_categories": ["需求域", "验证域"],
                "relationship_priorities": {
                    ConnectionType.DIRECT_REFERENCE: 0.9,
                    ConnectionType.DEPENDENCY: 0.8,
                    ConnectionType.SEMANTIC: 0.7
                },
                "surface_types": ["需求追踪面", "验证覆盖面"]
            },
            "系统架构师": {
                "focus_categories": ["结构域", "关系域"],
                "relationship_priorities": {
                    ConnectionType.COMPOSITION: 0.9,
                    ConnectionType.AGGREGATION: 0.8,
                    ConnectionType.DEPENDENCY: 0.7
                },
                "surface_types": ["架构依赖面", "组件关系面"]
            },
            "项目经理": {
                "focus_categories": ["需求域", "结构域", "验证域"],
                "relationship_priorities": {
                    ConnectionType.DIRECT_REFERENCE: 0.8,
                    ConnectionType.DEPENDENCY: 0.7,
                    ConnectionType.ASSOCIATION: 0.6
                },
                "surface_types": ["项目进度面", "质量管控面"]
            }
        }
    
    def build_3d_connections(self, metadata_nodes: Dict[str, EnhancedMetadataNode]) -> ConnectionVolume:
        """构建完整的三维连接体系"""
        start_time = time.time()
        
        # 第一阶段：连点成线 - 发现元素间的直接连接
        logger.info("🔗 开始连点成线分析...")
        connections = self._discover_point_to_line_connections(metadata_nodes)
        
        # 第二阶段：连线成面 - 构建连接线和关系网络
        logger.info("📈 开始连线成面分析...")
        connection_lines = self._build_line_to_surface_connections(connections, metadata_nodes)
        
        # 第三阶段：连面成体 - 建立完整的关系体系
        logger.info("🧊 开始连面成体分析...")
        connection_surfaces = self._build_surface_to_volume_connections(connection_lines, metadata_nodes)
        
        # 第四阶段：构建完整的三维连接体
        connection_volume = self._build_complete_volume(connection_surfaces, metadata_nodes)
        
        # 更新统计信息
        self._update_connection_stats(connections, connection_lines, connection_surfaces)
        
        processing_time = time.time() - start_time
        logger.info(f"✅ 三维连接分析完成 - 用时: {processing_time:.2f}秒")
        logger.info(f"   📊 发现连接: {len(connections)}, 连线: {len(connection_lines)}, 连面: {len(connection_surfaces)}")
        
        return connection_volume
    
    def discover_connections(self, elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对外API接口：发现元素间的连接关系
        
        Args:
            elements: 元素列表，每个元素包含id、type等属性
            
        Returns:
            List[Dict]: 连接关系列表
        """
        logger.info(f"🔍 开始发现连接关系 - 输入元素数: {len(elements)}")
        
        # 自动性能模式检测：大于1000个元素时启用性能模式
        auto_performance_mode = len(elements) > 1000
        if auto_performance_mode and not self.performance_mode:
            logger.info("⚡ 检测到大型数据集，自动启用性能优化模式")
            # 临时调整性能参数
            original_max_connections = getattr(self, 'max_connections_per_element', 200)
            original_threshold = getattr(self, 'semantic_threshold', 0.6)
            
            self.max_connections_per_element = min(50, max(10, 5000 // len(elements)))
            self.semantic_threshold = 0.8
            self.enable_semantic_cache = True
            self._semantic_cache = {}
        
        # 将简单字典转换为EnhancedMetadataNode格式
        metadata_nodes = {}
        for elem in elements:
            if 'id' in elem:
                # 🔧 修复：确保ID始终是字符串类型
                element_id = str(elem['id'])  # 强制转换为字符串，防止StableID对象问题
                
                # 创建一个简单的XML元素
                xml_elem = ET.Element(elem.get('type', 'element'))
                xml_elem.set('id', element_id)
                
                # 添加其他属性
                for key, value in elem.items():
                    if key != 'id' and key != 'type':
                        xml_elem.set(key, str(value))
                
                # 创建EnhancedMetadataNode
                if _core_element_available:
                    # 使用core.models.element.Element作为EnhancedMetadataNode
                    node = EnhancedMetadataNode(
                        element_id=element_id,  # 使用字符串ID
                        tag=elem.get('type', 'element')
                    )
                    # 存储原始数据
                    node.custom_data = {
                        'xml_element': xml_elem,
                        'properties': elem
                    }
                    # 为兼容性添加属性，确保ID是字符串
                    node.id = element_id  # 使用字符串ID
                    node.element = xml_elem
                    node.properties = elem
                else:
                    # 使用本地EnhancedMetadataNode定义
                    node = EnhancedMetadataNode(
                        id=element_id,  # 使用字符串ID
                        element=xml_elem,
                        properties=elem
                    )
                metadata_nodes[element_id] = node  # 使用字符串ID作为键
        
        # 使用优化的连接发现逻辑
        if auto_performance_mode or self.performance_mode:
            connections = self._discover_connections_optimized(metadata_nodes)
        else:
            connections = self._discover_point_to_line_connections(metadata_nodes)
        
        # 恢复原始性能参数
        if auto_performance_mode and not self.performance_mode:
            self.max_connections_per_element = original_max_connections
            self.semantic_threshold = original_threshold
            self.enable_semantic_cache = False
            self._semantic_cache = None
        
        # 转换为简单字典格式返回
        result = []
        for conn in connections:
            result.append({
                'source_id': conn.source_id,
                'target_id': conn.target_id,
                'connection_type': conn.connection_type.value,
                'strength': conn.strength,
                'confidence': conn.confidence,
                'direction': conn.direction,
                'discovery_method': conn.discovery_method,
                'properties': conn.properties
            })
        
        logger.info(f"✅ 连接发现完成 - 发现连接数: {len(result)}")
        return result
    
    def _discover_connections_optimized(self, metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[Connection]:
        """优化版连接发现（用于大型数据集）"""
        all_connections = []
        total_nodes = len(metadata_nodes)
        
        # 分批处理以避免内存问题
        batch_size = min(100, max(10, total_nodes // 10))
        node_items = list(metadata_nodes.items())
        
        for i in range(0, total_nodes, batch_size):
            batch_nodes = dict(node_items[i:i + batch_size])
            batch_connections = []
            
            # 处理批次内的连接发现
            for source_id, source_node in batch_nodes.items():
                # 1. 引用连接（最重要，必须保留）
                ref_connections = self._discover_reference_connections(source_node, metadata_nodes)
                batch_connections.extend(ref_connections)
                
                # 2. 语义连接（优化版）
                semantic_connections = self._discover_semantic_connections(source_node, metadata_nodes)
                batch_connections.extend(semantic_connections)
                
                # 3. 结构连接（轻量级）
                structural_connections = self._discover_structural_connections(source_node, metadata_nodes)
                batch_connections.extend(structural_connections)
            
            all_connections.extend(batch_connections)
            
            # 进度反馈
            progress = ((i + batch_size) / total_nodes) * 100
            if progress % 20 < (batch_size / total_nodes) * 100:  # 每20%输出一次
                logger.info(f"   连接发现进度: {min(100, progress):.0f}%")
        
        # 去重和强度计算（优化版）
        connections = self._deduplicate_and_strengthen_connections_optimized(all_connections)
        
        return connections
    
    def _deduplicate_and_strengthen_connections_optimized(self, connections: List[Connection]) -> List[Connection]:
        """优化版去重和强化连接"""
        if not connections:
            return []
        
        # 使用更高效的去重算法
        connection_map = {}
        
        # 首次遍历：收集所有连接
        for conn in connections:
            key = (conn.source_id, conn.target_id, conn.connection_type.value)
            
            if key not in connection_map:
                connection_map[key] = []
            connection_map[key].append(conn)
        
        # 第二次遍历：合并重复连接
        deduplicated = []
        for key, conn_list in connection_map.items():
            if len(conn_list) == 1:
                deduplicated.append(conn_list[0])
            else:
                # 合并多个连接：取最高强度，增强置信度
                best_conn = max(conn_list, key=lambda c: c.strength)
                best_conn.confidence = min(1.0, best_conn.confidence + 0.1 * (len(conn_list) - 1))
                best_conn.discovery_method += "_merged"
                deduplicated.append(best_conn)
        
        return deduplicated
    
    def _discover_point_to_line_connections(self, metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[Connection]:
        """连点成线：发现基础连接"""
        connections = []
        
        for source_id, source_node in metadata_nodes.items():
            # 1. 基于直接引用发现连接
            ref_connections = self._discover_reference_connections(source_node, metadata_nodes)
            connections.extend(ref_connections)
            
            # 2. 基于语义相似性发现连接
            semantic_connections = self._discover_semantic_connections(source_node, metadata_nodes)
            connections.extend(semantic_connections)
            
            # 3. 基于结构关系发现连接
            structural_connections = self._discover_structural_connections(source_node, metadata_nodes)
            connections.extend(structural_connections)
        
        # 去重和强度计算
        connections = self._deduplicate_and_strengthen_connections(connections)
        
        return connections
    
    def _discover_reference_connections(self, source_node: EnhancedMetadataNode, 
                                      metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[Connection]:
        """发现引用连接"""
        connections = []
        
        # 🔧 修复：获取源节点ID - 处理不同的ID类型
        if hasattr(source_node, 'id'):
            source_id = str(source_node.id)  # 确保ID是字符串类型
        elif hasattr(source_node, 'element_id'):
            source_id = str(source_node.element_id)
        else:
            # 从元素属性中获取ID
            source_id = source_node.element.get('id', f'temp_{hash(str(source_node.element.attrib))}')
        
        # 检查元素的所有属性
        for attr_name, attr_value in source_node.element.attrib.items():
            if self._is_reference_attribute(attr_name, attr_value):
                # 尝试解析引用
                target_id = self._resolve_reference(attr_value, metadata_nodes)
                if target_id and target_id in metadata_nodes:
                    connection = Connection(
                        source_id=source_id,
                        target_id=target_id,
                        connection_type=self._determine_connection_type(attr_name),
                        strength=self._calculate_reference_strength(attr_name, attr_value),
                        confidence=0.9,  # 引用连接具有高置信度
                        direction="forward",
                        discovery_method="reference_analysis",
                        properties={
                            'reference_attribute': attr_name,
                            'reference_value': attr_value
                        }
                    )
                    connections.append(connection)
        
        return connections
    
    def _discover_semantic_connections(self, source_node: EnhancedMetadataNode,
                                     metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[Connection]:
        """发现语义连接 - 优化版本"""
        connections = []
        
        # 🔧 修复：安全获取源节点ID
        if hasattr(source_node, 'id'):
            source_id = str(source_node.id)
        elif hasattr(source_node, 'element_id'):
            source_id = str(source_node.element_id)
        else:
            source_id = source_node.element.get('id', f'temp_{hash(str(source_node.element.attrib))}')
        
        # 性能模式下的优化策略
        if self.performance_mode:
            # 1. 预计算源节点的语义特征
            source_features = self._extract_semantic_features(source_node)
            
            # 2. 构建候选节点索引（基于类型和标签快速筛选）
            candidates = self._get_semantic_candidates(source_node, metadata_nodes)
            
            # 3. 批量计算相似度（限制候选数量）
            max_candidates = min(len(candidates), self.max_connections_per_element)
            limited_candidates = candidates[:max_candidates]
            
            for target_id, target_node in limited_candidates:
                if target_id == source_id:
                    continue
                
                # 使用缓存的相似度计算
                similarity = self._calculate_semantic_similarity_cached(source_node, target_node)
                
                if similarity > self.semantic_threshold:
                    connection = Connection(
                        source_id=source_id,
                        target_id=target_id,
                        connection_type=ConnectionType.SEMANTIC,
                        strength=similarity,
                        confidence=0.7,
                        discovery_method="semantic_analysis_optimized",
                        properties={"similarity_score": similarity}
                    )
                    connections.append(connection)
        else:
            # 标准模式：保持原有逻辑但仍然使用缓存
            for target_id, target_node in metadata_nodes.items():
                if target_id == source_id:
                    continue
                
                similarity = self._calculate_semantic_similarity_cached(source_node, target_node)
                
                if similarity > self.semantic_threshold:
                    connection = Connection(
                        source_id=source_id,
                        target_id=target_id,
                        connection_type=ConnectionType.SEMANTIC,
                        strength=similarity,
                        confidence=0.7,
                        discovery_method="semantic_analysis",
                        properties={"similarity_score": similarity}
                    )
                    connections.append(connection)
        
        return connections
    
    def _extract_semantic_features(self, node: EnhancedMetadataNode) -> Dict[str, Any]:
        """提取语义特征"""
        name = node.element.get('name', node.element.tag)
        
        # 安全获取主要类别
        try:
            if hasattr(node, 'get_primary_category'):
                category = node.get_primary_category()
            else:
                # 如果没有get_primary_category方法，使用简单的分类逻辑
                tag = node.element.tag.lower()
                if 'requirement' in tag or 'req' in tag:
                    category = '需求域'
                elif 'class' in tag or 'component' in tag or 'block' in tag:
                    category = '结构域'
                elif 'activity' in tag or 'action' in tag or 'operation' in tag:
                    category = '行为域'
                elif 'property' in tag or 'attribute' in tag:
                    category = '属性域'
                elif 'test' in tag or 'verify' in tag:
                    category = '验证域'
                else:
                    category = '未知域'
        except Exception:
            category = '未知域'
        
        return {
            'tag': node.element.tag,
            'type': node.element.get('type', 'unknown'),
            'name_words': set(name.lower().split()),
            'category': category
        }
    
    def _get_semantic_candidates(self, source_node: EnhancedMetadataNode, 
                               metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[Tuple[str, EnhancedMetadataNode]]:
        """获取语义候选节点（预筛选）"""
        source_tag = source_node.element.tag
        source_type = source_node.element.get('type', 'unknown')
        
        # 安全获取源节点类别
        try:
            if hasattr(source_node, 'get_primary_category'):
                source_category = source_node.get_primary_category()
            else:
                source_category = self._get_category_from_tag(source_node.element.tag)
        except Exception:
            source_category = '未知域'
        
        candidates = []
        high_priority = []
        medium_priority = []
        
        for target_id, target_node in metadata_nodes.items():
            target_tag = target_node.element.tag
            target_type = target_node.element.get('type', 'unknown')
            
            # 安全获取目标节点类别
            try:
                if hasattr(target_node, 'get_primary_category'):
                    target_category = target_node.get_primary_category()
                else:
                    target_category = self._get_category_from_tag(target_node.element.tag)
            except Exception:
                target_category = '未知域'
            
            # 高优先级：相同标签或类型
            if target_tag == source_tag or target_type == source_type:
                high_priority.append((target_id, target_node))
            # 中优先级：相同类别
            elif target_category == source_category:
                medium_priority.append((target_id, target_node))
            # 低优先级：其他
            else:
                candidates.append((target_id, target_node))
        
        # 按优先级排序，高优先级在前
        return high_priority + medium_priority + candidates
    
    def _get_category_from_tag(self, tag: str) -> str:
        """从标签获取类别的辅助方法"""
        tag_lower = tag.lower()
        if 'requirement' in tag_lower or 'req' in tag_lower:
            return '需求域'
        elif 'class' in tag_lower or 'component' in tag_lower or 'block' in tag_lower:
            return '结构域'
        elif 'activity' in tag_lower or 'action' in tag_lower or 'operation' in tag_lower:
            return '行为域'
        elif 'property' in tag_lower or 'attribute' in tag_lower:
            return '属性域'
        elif 'test' in tag_lower or 'verify' in tag_lower:
            return '验证域'
        else:
            return '未知域'
    
    def _calculate_semantic_similarity_cached(self, node1: EnhancedMetadataNode, node2: EnhancedMetadataNode) -> float:
        """带缓存的语义相似度计算"""
        if not self.enable_semantic_cache:
            return self._calculate_semantic_similarity(node1, node2)
        
        # 🔧 修复：安全获取节点ID
        def get_node_id(node):
            if hasattr(node, 'id'):
                return str(node.id)
            elif hasattr(node, 'element_id'):
                return str(node.element_id)
            else:
                return node.element.get('id', f'temp_{hash(str(node.element.attrib))}')
        
        # 创建缓存键（确保顺序一致性）
        id1, id2 = sorted([get_node_id(node1), get_node_id(node2)])
        cache_key = f"{id1}#{id2}"
        
        if cache_key in self._semantic_cache:
            self.connection_stats['cache_hits'] = self.connection_stats.get('cache_hits', 0) + 1
            return self._semantic_cache[cache_key]
        
        # 计算相似度并缓存
        similarity = self._calculate_semantic_similarity(node1, node2)
        self._semantic_cache[cache_key] = similarity
        self.connection_stats['cache_misses'] = self.connection_stats.get('cache_misses', 0) + 1
        
        return similarity
    
    def _discover_structural_connections(self, source_node: EnhancedMetadataNode,
                                       metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[Connection]:
        """发现结构连接"""
        connections = []
        
        # 🔧 修复：安全获取源节点ID
        if hasattr(source_node, 'id'):
            source_id = str(source_node.id)
        elif hasattr(source_node, 'element_id'):
            source_id = str(source_node.element_id)
        else:
            source_id = source_node.element.get('id', f'temp_{hash(str(source_node.element.attrib))}')
        
        # 简化的结构关系分析，基于XML元素层次结构
        # 获取父元素关系
        parent_elem = source_node.element.getparent() if hasattr(source_node.element, 'getparent') else None
        if parent_elem is not None:
            parent_id = parent_elem.get('id')
            if parent_id and parent_id in metadata_nodes:
                connection = Connection(
                    source_id=parent_id,
                    target_id=source_id,
                    connection_type=ConnectionType.COMPOSITION,
                    strength=0.9,
                    confidence=0.9,
                    discovery_method="structural_hierarchy",
                    properties={"relationship": "parent_child"}
                )
                connections.append(connection)
        
        # 获取子元素关系
        for child_elem in source_node.element:
            child_id = child_elem.get('id')
            if child_id and child_id in metadata_nodes:
                connection = Connection(
                    source_id=source_id,
                    target_id=child_id,
                    connection_type=ConnectionType.COMPOSITION,
                    strength=0.8,
                    confidence=0.9,
                    discovery_method="structural_hierarchy",
                    properties={"relationship": "child_parent"}
                )
                connections.append(connection)
        
        return connections
    
    def _build_line_to_surface_connections(self, connections: List[Connection],
                                         metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[ConnectionLine]:
        """连线成面：构建连接线"""
        connection_lines = []
        
        # 1. 基于路径模式构建连线
        pattern_lines = self._build_pattern_based_lines(connections, metadata_nodes)
        connection_lines.extend(pattern_lines)
        
        # 2. 基于语义聚类构建连线
        semantic_lines = self._build_semantic_cluster_lines(connections, metadata_nodes)
        connection_lines.extend(semantic_lines)
        
        # 3. 基于强度阈值构建连线
        strength_lines = self._build_strength_based_lines(connections, metadata_nodes)
        connection_lines.extend(strength_lines)
        
        return connection_lines
    
    def _build_pattern_based_lines(self, connections: List[Connection],
                                 metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[ConnectionLine]:
        """基于模式构建连线"""
        pattern_lines = []
        
        for pattern_name, pattern_info in self.relationship_patterns.items():
            pattern_sequence = pattern_info["pattern"]
            
            # 查找匹配模式的连接序列
            matching_sequences = self._find_pattern_sequences(connections, pattern_sequence, metadata_nodes)
            
            for i, sequence in enumerate(matching_sequences):
                line_id = f"{pattern_name}_line_{i}"
                total_strength = sum(conn.strength for conn in sequence) / len(sequence)
                
                connection_line = ConnectionLine(
                    line_id=line_id,
                    connections=sequence,
                    line_type=pattern_name,
                    semantic_role=pattern_info["description"],
                    total_strength=total_strength,
                    path_length=len(sequence),
                    business_meaning=pattern_info["description"]
                )
                pattern_lines.append(connection_line)
        
        return pattern_lines
    
    def _build_surface_to_volume_connections(self, connection_lines: List[ConnectionLine],
                                           metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[ConnectionSurface]:
        """连面成体：构建连接面"""
        connection_surfaces = []
        
        # 1. 基于业务域构建连接面
        domain_surfaces = self._build_domain_based_surfaces(connection_lines, metadata_nodes)
        connection_surfaces.extend(domain_surfaces)
        
        # 2. 基于功能聚类构建连接面
        functional_surfaces = self._build_functional_surfaces(connection_lines, metadata_nodes)
        connection_surfaces.extend(functional_surfaces)
        
        # 3. 基于复杂度构建连接面
        complexity_surfaces = self._build_complexity_surfaces(connection_lines, metadata_nodes)
        connection_surfaces.extend(complexity_surfaces)
        
        return connection_surfaces
    
    def _build_complete_volume(self, connection_surfaces: List[ConnectionSurface],
                             metadata_nodes: Dict[str, EnhancedMetadataNode]) -> ConnectionVolume:
        """构建完整的连接体"""
        
        # 构建关系图
        relationship_graph = self._build_relationship_graph(connection_surfaces)
        
        # 构建业务视角
        business_perspectives = self._build_business_perspectives(connection_surfaces, metadata_nodes)
        
        # 构建技术视角
        technical_perspectives = self._build_technical_perspectives(connection_surfaces, metadata_nodes)
        
        # 计算全局指标
        global_metrics = self._calculate_global_metrics(connection_surfaces, relationship_graph)
        
        volume_id = f"volume_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        connection_volume = ConnectionVolume(
            volume_id=volume_id,
            connection_surfaces=connection_surfaces,
            business_perspectives=business_perspectives,
            technical_perspectives=technical_perspectives,
            global_metrics=global_metrics,
            relationship_graph=relationship_graph
        )
        
        return connection_volume
    
    def _build_relationship_graph(self, connection_surfaces: List[ConnectionSurface]) -> nx.DiGraph:
        """构建关系图"""
        graph = nx.DiGraph()
        
        for surface in connection_surfaces:
            for line in surface.connection_lines:
                for connection in line.connections:
                    graph.add_edge(
                        connection.source_id,
                        connection.target_id,
                        weight=connection.strength,
                        type=connection.connection_type.value,
                        confidence=connection.confidence
                    )
        
        return graph
    
    def _build_business_perspectives(self, connection_surfaces: List[ConnectionSurface],
                                   metadata_nodes: Dict[str, EnhancedMetadataNode]) -> Dict[str, Dict[str, Any]]:
        """构建业务视角"""
        perspectives = {}
        
        for role, role_info in self.business_perspective_rules.items():
            focus_categories = role_info["focus_categories"]
            
            # 筛选相关的连接面
            relevant_surfaces = []
            for surface in connection_surfaces:
                if self._surface_matches_role(surface, focus_categories, metadata_nodes):
                    relevant_surfaces.append(surface)
            
            # 构建该角色的视角
            perspective = {
                "role": role,
                "relevant_surfaces": len(relevant_surfaces),
                "total_elements": sum(surface.get_element_count() for surface in relevant_surfaces),
                "total_connections": sum(surface.get_total_connections() for surface in relevant_surfaces),
                "focus_areas": self._identify_focus_areas(relevant_surfaces, metadata_nodes),
                "key_metrics": self._calculate_role_metrics(relevant_surfaces, role_info)
            }
            
            perspectives[role] = perspective
        
        return perspectives
    
    def _build_technical_perspectives(self, connection_surfaces: List[ConnectionSurface],
                                    metadata_nodes: Dict[str, EnhancedMetadataNode]) -> Dict[str, Dict[str, Any]]:
        """构建技术视角"""
        perspectives = {
            "架构复杂度": self._analyze_architectural_complexity(connection_surfaces),
            "依赖管理": self._analyze_dependency_management(connection_surfaces),
            "模块化程度": self._analyze_modularization(connection_surfaces),
            "接口设计": self._analyze_interface_design(connection_surfaces, metadata_nodes)
        }
        
        return perspectives
    
    # 辅助方法
    def _is_reference_attribute(self, attr_name: str, attr_value: str) -> bool:
        """判断是否为引用属性 - 基于实际UML/SysML术语标准的完整支持"""
        
        # 🔥 核心UML/SysML引用属性名称模式
        uml_sysml_reference_attributes = [
            # === 核心UML引用属性 ===
            'type',              # 最常见：type="_9_0_62a020a_1105704885343_144138_7929"
            'href',              # 最常见：href="#_9_0_62a020a_1105704884807_371561_7741"
            'general',           # UML泛化关系
            'specific',          # UML特化关系
            'classifier',        # UML分类器引用
            'association',       # UML关联引用
            'memberEnd',         # UML关联端点
            'owningPackage',     # UML包所有权
            'extension',         # UML扩展
            'client',            # UML客户依赖
            'supplier',          # UML供应商依赖
            
            # === SysML特有引用属性 ===
            'base_Class',        # SysML基础类引用
            'base_Package',      # SysML基础包引用  
            'base_Property',     # SysML基础属性引用
            'base_Port',         # SysML基础端口引用
            'base_UMLDiagram',   # SysML基础图引用
            'base_Element',      # SysML基础元素引用
            'base_Classifier',   # SysML基础分类器引用
            'base_Constraint',   # SysML基础约束引用
            'base_Activity',     # SysML基础活动引用
            'base_Operation',    # SysML基础操作引用
            'base_Parameter',    # SysML基础参数引用
            'base_Connector',    # SysML基础连接器引用
            'base_ConnectorEnd', # SysML基础连接器端点引用
            'base_InformationFlow', # SysML基础信息流引用
            
            # === SysML关系引用属性 ===
            'satisfy',           # SysML满足关系
            'verify',            # SysML验证关系
            'derive',            # SysML派生关系 
            'trace',             # SysML追踪关系
            'refine',            # SysML细化关系
            'allocate',          # SysML分配关系
            'deriveReqt',        # SysML需求派生
            
            # === MagicDraw特有引用 ===
            'stereotypeHREF',    # MagicDraw刻板印象引用
            'elementID',         # MagicDraw元素ID引用
            'rowElementType',    # MagicDraw表格行类型
            'columnElementType', # MagicDraw表格列类型
            'usedProject',       # MagicDraw项目引用
            'mountedPoint',      # MagicDraw挂载点
            'mountedOn',         # MagicDraw挂载目标
            'mountedSharePoint', # MagicDraw共享挂载点
            
            # === XMI标准引用属性 ===
            'xsi:type',          # XMI类型引用
            'xmi:type',          # XMI类型引用（另一种命名空间）
            'xmi:id',            # XMI标识符
            'xmi:idref',         # XMI标识符引用
            'xmi:href',          # XMI超链接引用
            
            # === 通用引用指示器 ===
            'ref',               # 通用引用
            'idref',             # ID引用
            'link',              # 链接引用
            'target',            # 目标引用
            'source',            # 源引用
            'destination',       # 目标引用
            'owner',             # 所有者引用
            'namespace',         # 命名空间引用
            'parent',            # 父引用
            'child',             # 子引用
            'container',         # 容器引用
            'contained',         # 被包含引用
        ]
        
        attr_lower = attr_name.lower()
        
        # 🔥 策略1：精确匹配UML/SysML标准属性名
        if attr_name in uml_sysml_reference_attributes:
            return True
        
        # 🔥 策略2：不区分大小写的匹配
        for ref_attr in uml_sysml_reference_attributes:
            if attr_lower == ref_attr.lower():
                return True
        
        # 🔥 策略3：包含匹配（用于复合属性名）
        uml_sysml_indicators = [
            'base_',             # SysML基础引用前缀
            'stereotype',        # 刻板印象相关
            'element',           # 元素相关
            'type',              # 类型相关
            'href',              # 超链接相关
            'ref',               # 引用相关
            'link',              # 链接相关
            'target',            # 目标相关
            'source',            # 源相关
            'owner',             # 所有者相关
            'classifier',        # 分类器相关
            'association',       # 关联相关
            'member',            # 成员相关
            'extension',         # 扩展相关
        ]
        
        for indicator in uml_sysml_indicators:
            if indicator in attr_lower:
                return True
        
        # 🔥 策略4：基于属性值模式的智能识别
        if isinstance(attr_value, str):
            # UML工具生成的ID模式：_数字_字母数字_时间戳_序号_编号
            if self._matches_uml_id_pattern(attr_value):
                return True
            
            # SysML应用扩展后缀模式
            if attr_value.endswith('_application') or attr_value.endswith('_stereotype'):
                return True
            
            # 长数字ID（MagicDraw常用）
            if len(attr_value) > 10 and attr_value.isdigit():
                return True
            
            # XMI引用模式：以#开头的片段引用
            if attr_value.startswith('#') and len(attr_value) > 5:
                return True
            
            # URI模式引用
            if ('PROJECT-' in attr_value and 'resource=' in attr_value) or attr_value.startswith('local:/'):
                return True
            
            # SysML Profile引用模式
            if '.mdzip#' in attr_value or 'Profile.mdzip#' in attr_value:
                return True
        
        return False
    
    def _matches_uml_id_pattern(self, value: str) -> bool:
        """检查是否匹配UML工具生成的ID模式"""
        # 模式1：_数字_字母数字_时间戳_序号_编号 (如: _9_0_62a020a_1105704885343_144138_7929)
        uml_pattern1 = r'^_\d+_\w+_\w+_\d+_\d+$'
        
        # 模式2：_数字_字母数字_字母数字_时间戳_序号_编号 (如: _11_5EAPbeta_be00301_1147424179914_458922_958)
        uml_pattern2 = r'^_\d+_\w+_\w+_\d+_\d+_\d+$'
        
        # 模式3：纯数字长ID (如: 85876566602889792)
        numeric_pattern = r'^\d{15,}$'  # 15位以上的纯数字
        
        import re
        return (re.match(uml_pattern1, value) is not None or 
                re.match(uml_pattern2, value) is not None or
                re.match(numeric_pattern, value) is not None)
    
    def _resolve_reference(self, reference_value: str, metadata_nodes: Dict[str, EnhancedMetadataNode]) -> Optional[str]:
        """解析引用值 - 基于UML/SysML标准的增强支持"""
        
        if not isinstance(reference_value, str) or not reference_value.strip():
            return None
        
        reference_value = reference_value.strip()
        
        # 🔥 策略1：直接ID匹配（最快）
        if reference_value in metadata_nodes:
            return reference_value
        
        # 🔥 策略2：处理XMI片段引用（以#开头）
        if reference_value.startswith('#'):
            # 移除#前缀进行匹配
            fragment_id = reference_value[1:]
            if fragment_id in metadata_nodes:
                return fragment_id
            
            # 检查节点的XMI ID属性
            for node_id, node in metadata_nodes.items():
                xmi_id = node.element.get('{http://www.omg.org/spec/XMI/20131001}id', '')
                if xmi_id == fragment_id:
                    return node_id
        
        # 🔥 策略3：处理SysML Profile引用（包含.mdzip#）
        if '.mdzip#' in reference_value:
            # 提取#后面的ID部分
            profile_id = reference_value.split('#')[-1]
            if profile_id in metadata_nodes:
                return profile_id
        
        # 🔥 策略4：处理MagicDraw项目引用（PROJECT-xxx格式）
        if 'PROJECT-' in reference_value and '#' in reference_value:
            # 提取#后面的元素ID
            project_element_id = reference_value.split('#')[-1]
            if project_element_id in metadata_nodes:
                return project_element_id
        
        # 🔥 策略5：处理SysML应用扩展ID（移除后缀）
        if reference_value.endswith('_application') or reference_value.endswith('_stereotype'):
            # 提取基础ID：85900884686726592_application -> 85900884686726592
            base_id = reference_value.rsplit('_', 1)[0]
            if base_id in metadata_nodes:
                return base_id
            
            # 检查节点的XMI ID属性
            for node_id, node in metadata_nodes.items():
                xmi_id = node.element.get('{http://www.omg.org/spec/XMI/20131001}id', '')
                if xmi_id == base_id:
                    return node_id
        
        # 🔥 策略6：UML工具生成ID的智能匹配
        if self._matches_uml_id_pattern(reference_value):
            # 直接匹配
            for node_id, node in metadata_nodes.items():
                # 检查节点的各种ID属性
                id_attributes = [
                    node.element.get('xmi:id', ''),
                    node.element.get('{http://www.omg.org/spec/XMI/20131001}id', ''),
                    node.element.get('ID', ''),
                    node.element.get('id', ''),
                    str(node_id)
                ]
                
                if reference_value in id_attributes:
                    return node_id
        
        # 🔥 策略7：长数字ID匹配（MagicDraw常用）
        if reference_value.isdigit() and len(reference_value) > 10:
            for node_id, node in metadata_nodes.items():
                # 检查各种可能的数字ID属性
                id_attributes = [
                    node.element.get('xmi:id', ''),
                    node.element.get('ID', ''), 
                    node.element.get('id', ''),
                    str(node_id)
                ]
                
                if reference_value in id_attributes:
                    return node_id
        
        # 🔥 策略8：属性值全扫描匹配（备用策略）
        for node_id, node in metadata_nodes.items():
            # 检查节点的所有属性值
            for attr_name, attr_value in node.element.attrib.items():
                if str(attr_value) == reference_value:
                    return node_id
        
        # 🔥 策略9：安全的名称匹配（最后手段）
        for node_id, node in metadata_nodes.items():
            # 获取节点名称的多种方式
            node_names = []
            
            if hasattr(node, 'name') and node.name:
                node_names.append(str(node.name))
            
            if hasattr(node, 'element') and node.element is not None:
                element_name = node.element.get('name', '')
                if element_name:
                    node_names.append(element_name)
            
            if hasattr(node, 'properties') and isinstance(node.properties, dict):
                prop_name = node.properties.get('name', '')
                if prop_name:
                    node_names.append(str(prop_name))
            
            # 如果引用值匹配任何名称
            if reference_value in node_names:
                return node_id
        
        # 🔥 策略10：模糊匹配（用于复杂引用格式）
        # 处理形如 "uml:Property" 这样的类型引用
        if ':' in reference_value:
            simple_ref = reference_value.split(':')[-1]  # 取冒号后的部分
            for node_id, node in metadata_nodes.items():
                # 检查元素标签或类型
                if (node.element.tag.endswith(simple_ref) or 
                    node.element.get('type', '').endswith(simple_ref) or
                    node.element.get('name', '') == simple_ref):
                    return node_id
        
        return None
    
    def _determine_connection_type(self, attr_name: str) -> ConnectionType:
        """确定连接类型"""
        attr_lower = attr_name.lower()
        
        if 'href' in attr_lower or 'link' in attr_lower:
            return ConnectionType.DIRECT_REFERENCE
        elif 'depend' in attr_lower:
            return ConnectionType.DEPENDENCY
        elif 'inherit' in attr_lower or 'extend' in attr_lower:
            return ConnectionType.INHERITANCE
        elif 'compose' in attr_lower:
            return ConnectionType.COMPOSITION
        elif 'aggregate' in attr_lower:
            return ConnectionType.AGGREGATION
        else:
            return ConnectionType.ASSOCIATION
    
    def _calculate_reference_strength(self, attr_name: str, attr_value: str) -> float:
        """计算引用强度"""
        base_strength = 0.7
        
        # 基于属性名调整
        if 'href' in attr_name.lower():
            base_strength = 0.9
        elif 'id' in attr_name.lower():
            base_strength = 0.8
        
        # 基于值的完整性调整
        if attr_value and len(attr_value) > 5:
            base_strength += 0.1
        
        return min(1.0, base_strength)
    
    def _calculate_semantic_similarity(self, node1: EnhancedMetadataNode, node2: EnhancedMetadataNode) -> float:
        """计算语义相似度"""
        similarity = 0.0
        
        # 从XML元素中提取名称
        name1 = node1.element.get('name', node1.element.tag)
        name2 = node2.element.get('name', node2.element.tag)
        
        # 名称相似度
        name_sim = self._calculate_name_similarity(name1, name2)
        similarity += name_sim * 0.3
        
        # 标签相似度 (使用XML元素的tag)
        tag_sim = 1.0 if node1.element.tag == node2.element.tag else 0.0
        similarity += tag_sim * 0.4
        
        # 类型相似度 (从type属性获取)
        type1 = node1.element.get('type', 'unknown')
        type2 = node2.element.get('type', 'unknown')
        type_sim = 1.0 if type1 == type2 else 0.0
        similarity += type_sim * 0.3
        
        return similarity
    
    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """计算名称相似度"""
        if not name1 or not name2:
            return 0.0
        
        # 简单的Jaccard相似度
        words1 = set(name1.lower().split())
        words2 = set(name2.lower().split())
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)
    
    def _deduplicate_and_strengthen_connections(self, connections: List[Connection]) -> List[Connection]:
        """去重和强化连接"""
        connection_map = {}
        
        for conn in connections:
            key = (conn.source_id, conn.target_id, conn.connection_type.value)
            
            if key in connection_map:
                # 合并重复连接，取最高强度
                existing = connection_map[key]
                if conn.strength > existing.strength:
                    connection_map[key] = conn
                else:
                    # 增强置信度
                    existing.confidence = min(1.0, existing.confidence + 0.1)
            else:
                connection_map[key] = conn
        
        return list(connection_map.values())
    
    def _find_pattern_sequences(self, connections: List[Connection], pattern: List[str],
                               metadata_nodes: Dict[str, EnhancedMetadataNode]) -> List[List[Connection]]:
        """查找模式序列"""
        sequences = []
        
        # 构建连接图
        conn_graph = {}
        for conn in connections:
            if conn.source_id not in conn_graph:
                conn_graph[conn.source_id] = []
            conn_graph[conn.source_id].append(conn)
        
        # 查找匹配模式的路径
        for start_id, start_connections in conn_graph.items():
            if start_id not in metadata_nodes:
                continue
            
            start_node = metadata_nodes[start_id]
            start_category = start_node.get_primary_category()
            
            if start_category == pattern[0]:
                # 深度优先搜索匹配的序列
                sequence = []
                if self._dfs_pattern_match(start_id, pattern, 0, sequence, conn_graph, metadata_nodes):
                    sequences.append(sequence.copy())
        
        return sequences
    
    def _dfs_pattern_match(self, current_id: str, pattern: List[str], pattern_index: int,
                          sequence: List[Connection], conn_graph: Dict[str, List[Connection]],
                          metadata_nodes: Dict[str, EnhancedMetadataNode]) -> bool:
        """深度优先搜索模式匹配"""
        if pattern_index >= len(pattern) - 1:
            return True
        
        if current_id not in conn_graph:
            return False
        
        next_category = pattern[pattern_index + 1]
        
        for conn in conn_graph[current_id]:
            target_id = conn.target_id
            if target_id in metadata_nodes:
                target_node = metadata_nodes[target_id]
                target_category = target_node.get_primary_category()
                
                if target_category == next_category:
                    sequence.append(conn)
                    if self._dfs_pattern_match(target_id, pattern, pattern_index + 1, sequence, conn_graph, metadata_nodes):
                        return True
                    sequence.pop()
        
        return False
    
    def _update_connection_stats(self, connections: List[Connection], 
                               connection_lines: List[ConnectionLine],
                               connection_surfaces: List[ConnectionSurface]):
        """更新连接统计"""
        self.connection_stats['total_connections_discovered'] = len(connections)
        self.connection_stats['connection_lines_created'] = len(connection_lines)
        self.connection_stats['connection_surfaces_created'] = len(connection_surfaces)
        self.connection_stats['connection_volumes_created'] += 1
        
        # 强度分布统计
        for conn in connections:
            strength_level = conn.get_strength_level()
            if strength_level == ConnectionStrength.STRONG:
                self.connection_stats['strong_connections'] += 1
            elif strength_level == ConnectionStrength.MEDIUM:
                self.connection_stats['medium_connections'] += 1
            else:
                self.connection_stats['weak_connections'] += 1
    
    def get_connection_statistics(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        return self.connection_stats.copy()
    
    def find_element_connections(self, element: ET.Element, root: ET.Element) -> List[Dict[str, Any]]:
        """简化的元素连接查找方法，用于测试"""
        connections = []
        
        try:
            element_name = element.get('name', element.tag)
            element_id = element.get('xmi:id', element.get('id', 'unknown'))
            
            # 1. 查找属性引用
            for attr_name, attr_value in element.attrib.items():
                if self._is_reference_attribute(attr_name, attr_value):
                    # 查找被引用的元素
                    referenced_element = self._find_element_by_reference(attr_value, root)
                    if referenced_element is not None:
                        target_name = referenced_element.get('name', referenced_element.tag)
                        connections.append({
                            'source': element_name,
                            'target': target_name,
                            'type': 'reference',
                            'attribute': attr_name,
                            'strength': self._calculate_reference_strength(attr_name, attr_value)
                        })
            
            # 2. 查找父子关系
            parent = _get_parent_element_safe(element, root)
            if parent is not None:
                parent_name = parent.get('name', parent.tag)
                connections.append({
                    'source': parent_name,
                    'target': element_name,
                    'type': 'containment',
                    'attribute': 'parent-child',
                    'strength': 0.9
                })
            
            # 3. 查找子元素关系
            for child in element:
                child_name = child.get('name', child.tag)
                connections.append({
                    'source': element_name,
                    'target': child_name,
                    'type': 'containment',
                    'attribute': 'parent-child',
                    'strength': 0.9
                })
            
        except Exception as e:
            print(f"查找元素连接时出错: {e}")
        
        return connections
    
    def _find_element_by_reference(self, reference_value: str, root: ET.Element) -> Optional[ET.Element]:
        """根据引用值查找元素"""
        # 简单的ID匹配
        for elem in root.iter():
            if elem.get('xmi:id') == reference_value or elem.get('id') == reference_value:
                return elem
        return None

    def _build_semantic_cluster_lines(self, connections, metadata_nodes):
        return []
    
    def _build_strength_based_lines(self, connections, metadata_nodes):
        return []
    
    def _build_domain_based_surfaces(self, connection_lines, metadata_nodes):
        return []
    
    def _build_functional_surfaces(self, connection_lines, metadata_nodes):
        return []
    
    def _build_complexity_surfaces(self, connection_lines, metadata_nodes):
        return []
    
    def _calculate_global_metrics(self, connection_surfaces, relationship_graph):
        return {}
    
    def _surface_matches_role(self, surface, focus_categories, metadata_nodes):
        return True
    
    def _identify_focus_areas(self, relevant_surfaces, metadata_nodes):
        return []
    
    def _calculate_role_metrics(self, relevant_surfaces, role_info):
        return {}
    
    def _analyze_architectural_complexity(self, connection_surfaces):
        return {}
    
    def _analyze_dependency_management(self, connection_surfaces):
        return {}
    
    def _analyze_modularization(self, connection_surfaces):
        return {}
    
    def _analyze_interface_design(self, connection_surfaces, metadata_nodes):
        return {} 