#!/usr/bin/env python3
"""
连线成片引擎 - Line to Surface Engine

核心功能：
1. 将语义相关的数据线连接成数据片
2. 支持多视角的数据整合和分析
3. 实现跨线关系分析和表面密度计算
4. 提供视角相关性评分和表面质量评估

作者: XML元数据系统开发团队
版本: v1.0.0
"""

import logging
import asyncio
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter
import networkx as nx
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)

class PerspectiveType(Enum):
    """视角类型枚举"""
    REQUIREMENTS_ANALYST = "requirements_analyst"    # 需求分析师视角
    SYSTEM_ARCHITECT = "system_architect"           # 系统架构师视角  
    BEHAVIOR_ANALYST = "behavior_analyst"           # 行为分析师视角
    TEST_ENGINEER = "test_engineer"                 # 测试工程师视角
    BUSINESS_ANALYST = "business_analyst"           # 业务分析师视角
    INTEGRATION_SPECIALIST = "integration_specialist" # 集成专家视角

class SurfaceType(Enum):
    """数据片类型枚举"""
    FUNCTIONAL_SURFACE = "functional_surface"       # 功能表面
    STRUCTURAL_SURFACE = "structural_surface"       # 结构表面
    BEHAVIORAL_SURFACE = "behavioral_surface"       # 行为表面
    QUALITY_SURFACE = "quality_surface"             # 质量表面
    INTEGRATION_SURFACE = "integration_surface"     # 集成表面
    BUSINESS_SURFACE = "business_surface"           # 业务表面

@dataclass
class DataLine:
    """数据线结构"""
    line_id: str
    elements: List[str]                    # 线上的元素ID列表
    semantic_strength: float               # 语义强度 0.0-1.0
    line_type: str                        # 线类型
    start_element: str                    # 起始元素
    end_element: str                      # 结束元素
    intermediate_elements: List[str]       # 中间元素
    perspective_relevance: Dict[str, float] # 视角相关性
    quality_score: float                  # 线质量评分
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass 
class DataSurface:
    """数据片结构"""
    surface_id: str
    connected_lines: List[DataLine]        # 连接的数据线
    surface_type: SurfaceType             # 表面类型
    perspective: PerspectiveType          # 主要视角
    elements: Set[str]                    # 包含的所有元素
    
    # 表面属性
    density: float                        # 表面密度
    coherence: float                      # 连贯性评分
    completeness: float                   # 完整性评分
    quality_score: float                  # 整体质量评分
    
    # 分析结果
    central_elements: List[str]           # 中心元素
    boundary_elements: List[str]          # 边界元素
    clusters: List[List[str]]             # 元素聚类
    
    # 元数据
    creation_time: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SurfaceAnalysisResult:
    """表面分析结果"""
    surfaces: List[DataSurface]
    perspective_analysis: Dict[str, Any]
    cross_surface_connections: List[Tuple[str, str, float]]
    overall_metrics: Dict[str, float]
    recommendations: List[str]
    processing_time: float

class LineToSurfaceEngine:
    """连线成片引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.perspective_analyzer = PerspectiveAnalyzer()
        self.cross_line_connector = CrossLineConnector()
        self.surface_optimizer = SurfaceOptimizer()
        
        # 配置参数
        self.config = {
            'min_lines_per_surface': 2,
            'max_lines_per_surface': 50,
            'density_threshold': 0.3,
            'coherence_threshold': 0.5,
            'quality_threshold': 0.6
        }
        
        self.logger.info("连线成片引擎初始化完成")
    
    async def connect_lines_to_surfaces(self, 
                                      data_lines: List[DataLine], 
                                      target_perspectives: List[PerspectiveType],
                                      context: Optional[Dict] = None) -> SurfaceAnalysisResult:
        """
        将数据线连接成数据片
        
        Args:
            data_lines: 输入的数据线列表
            target_perspectives: 目标视角列表
            context: 分析上下文
            
        Returns:
            SurfaceAnalysisResult: 表面分析结果
        """
        start_time = datetime.now()
        self.logger.info(f"开始连线成片分析，数据线数量: {len(data_lines)}, 目标视角: {len(target_perspectives)}")
        
        try:
            # 1. 视角分析
            perspective_analysis = await self.perspective_analyzer.analyze_perspectives(
                data_lines, target_perspectives)
            
            # 2. 线间关系分析
            line_connections = await self.cross_line_connector.analyze_line_relationships(
                data_lines, perspective_analysis)
            
            # 3. 构建数据片
            surfaces = await self._build_data_surfaces(
                data_lines, line_connections, target_perspectives)
            
            # 4. 表面优化
            optimized_surfaces = await self.surface_optimizer.optimize_surfaces(surfaces)
            
            # 5. 跨表面连接分析
            cross_surface_connections = await self._analyze_cross_surface_connections(
                optimized_surfaces)
            
            # 6. 计算整体指标
            overall_metrics = self._calculate_overall_metrics(
                optimized_surfaces, cross_surface_connections)
            
            # 7. 生成建议
            recommendations = self._generate_recommendations(
                optimized_surfaces, overall_metrics)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = SurfaceAnalysisResult(
                surfaces=optimized_surfaces,
                perspective_analysis=perspective_analysis,
                cross_surface_connections=cross_surface_connections,
                overall_metrics=overall_metrics,
                recommendations=recommendations,
                processing_time=processing_time
            )
            
            self.logger.info(f"连线成片分析完成，生成表面数量: {len(optimized_surfaces)}, 耗时: {processing_time:.3f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"连线成片分析失败: {str(e)}")
            raise
    
    async def _build_data_surfaces(self, 
                                 data_lines: List[DataLine],
                                 line_connections: Dict[str, List[str]],
                                 target_perspectives: List[PerspectiveType]) -> List[DataSurface]:
        """构建数据片"""
        surfaces = []
        used_lines = set()
        
        for perspective in target_perspectives:
            # 为每个视角构建相关的数据片
            perspective_surfaces = await self._build_perspective_surfaces(
                data_lines, line_connections, perspective, used_lines)
            surfaces.extend(perspective_surfaces)
        
        return surfaces
    
    async def _build_perspective_surfaces(self,
                                        data_lines: List[DataLine],
                                        line_connections: Dict[str, List[str]],
                                        perspective: PerspectiveType,
                                        used_lines: Set[str]) -> List[DataSurface]:
        """为特定视角构建数据片"""
        perspective_surfaces = []
        
        # 1. 筛选与视角相关的数据线
        relevant_lines = [line for line in data_lines 
                         if line.line_id not in used_lines 
                         and self._is_line_relevant_to_perspective(line, perspective)]
        
        if not relevant_lines:
            return perspective_surfaces
        
        # 2. 基于连接关系进行聚类
        line_clusters = self._cluster_lines_by_connections(
            relevant_lines, line_connections)
        
        # 3. 为每个聚类构建数据片
        for cluster_idx, line_cluster in enumerate(line_clusters):
            if len(line_cluster) >= self.config['min_lines_per_surface']:
                surface = await self._create_surface_from_lines(
                    line_cluster, perspective, cluster_idx)
                
                if surface.quality_score >= self.config['quality_threshold']:
                    perspective_surfaces.append(surface)
                    # 标记已使用的线
                    for line in line_cluster:
                        used_lines.add(line.line_id)
        
        return perspective_surfaces
    
    def _is_line_relevant_to_perspective(self, line: DataLine, perspective: PerspectiveType) -> bool:
        """判断数据线是否与视角相关"""
        perspective_key = perspective.value
        relevance = line.perspective_relevance.get(perspective_key, 0.0)
        return relevance >= 0.3  # 相关性阈值
    
    def _cluster_lines_by_connections(self, 
                                    lines: List[DataLine],
                                    line_connections: Dict[str, List[str]]) -> List[List[DataLine]]:
        """基于连接关系对数据线进行聚类"""
        # 构建连接图
        G = nx.Graph()
        line_dict = {line.line_id: line for line in lines}
        
        # 添加节点
        for line in lines:
            G.add_node(line.line_id)
        
        # 添加连接边
        for line_id, connected_ids in line_connections.items():
            if line_id in line_dict:
                for connected_id in connected_ids:
                    if connected_id in line_dict:
                        G.add_edge(line_id, connected_id)
        
        # 查找连通分量作为聚类
        clusters = []
        for component in nx.connected_components(G):
            cluster_lines = [line_dict[line_id] for line_id in component]
            clusters.append(cluster_lines)
        
        return clusters
    
    async def _create_surface_from_lines(self,
                                       lines: List[DataLine],
                                       perspective: PerspectiveType,
                                       cluster_idx: int) -> DataSurface:
        """从数据线创建数据片"""
        # 收集所有元素
        all_elements = set()
        for line in lines:
            all_elements.update(line.elements)
        
        # 确定表面类型
        surface_type = self._determine_surface_type(lines, perspective)
        
        # 计算表面属性
        density = self._calculate_surface_density(lines)
        coherence = self._calculate_surface_coherence(lines)
        completeness = self._calculate_surface_completeness(lines, all_elements)
        quality_score = (density + coherence + completeness) / 3.0
        
        # 识别中心和边界元素
        central_elements = self._identify_central_elements(lines, all_elements)
        boundary_elements = self._identify_boundary_elements(lines, all_elements)
        
        # 元素聚类
        clusters = self._cluster_surface_elements(lines, all_elements)
        
        surface_id = f"surface_{perspective.value}_{cluster_idx}_{len(lines)}lines"
        
        return DataSurface(
            surface_id=surface_id,
            connected_lines=lines,
            surface_type=surface_type,
            perspective=perspective,
            elements=all_elements,
            density=density,
            coherence=coherence,
            completeness=completeness,
            quality_score=quality_score,
            central_elements=central_elements,
            boundary_elements=boundary_elements,
            clusters=clusters
        )
    
    def _determine_surface_type(self, lines: List[DataLine], perspective: PerspectiveType) -> SurfaceType:
        """确定表面类型"""
        # 基于视角和线的特征确定表面类型
        perspective_mapping = {
            PerspectiveType.REQUIREMENTS_ANALYST: SurfaceType.FUNCTIONAL_SURFACE,
            PerspectiveType.SYSTEM_ARCHITECT: SurfaceType.STRUCTURAL_SURFACE,
            PerspectiveType.BEHAVIOR_ANALYST: SurfaceType.BEHAVIORAL_SURFACE,
            PerspectiveType.TEST_ENGINEER: SurfaceType.QUALITY_SURFACE,
            PerspectiveType.BUSINESS_ANALYST: SurfaceType.BUSINESS_SURFACE,
            PerspectiveType.INTEGRATION_SPECIALIST: SurfaceType.INTEGRATION_SURFACE
        }
        return perspective_mapping.get(perspective, SurfaceType.FUNCTIONAL_SURFACE)
    
    def _calculate_surface_density(self, lines: List[DataLine]) -> float:
        """计算表面密度"""
        if len(lines) <= 1:
            return 0.0
        
        # 计算线之间的平均连接强度
        total_strength = sum(line.semantic_strength for line in lines)
        avg_strength = total_strength / len(lines)
        
        # 计算元素重叠度
        all_elements = set()
        element_occurrences = Counter()
        for line in lines:
            all_elements.update(line.elements)
            for element in line.elements:
                element_occurrences[element] += 1
        
        overlap_score = sum(1 for count in element_occurrences.values() if count > 1) / len(all_elements)
        
        return (avg_strength + overlap_score) / 2.0
    
    def _calculate_surface_coherence(self, lines: List[DataLine]) -> float:
        """计算表面连贯性"""
        if len(lines) <= 1:
            return 1.0
        
        # 计算线类型的一致性
        line_types = [line.line_type for line in lines]
        type_consistency = len(set(line_types)) / len(line_types)
        
        # 计算质量评分的一致性
        quality_scores = [line.quality_score for line in lines]
        quality_variance = np.var(quality_scores) if len(quality_scores) > 1 else 0
        quality_consistency = 1.0 - min(quality_variance, 1.0)
        
        return (type_consistency + quality_consistency) / 2.0
    
    def _calculate_surface_completeness(self, lines: List[DataLine], all_elements: Set[str]) -> float:
        """计算表面完整性"""
        if not all_elements:
            return 0.0
        
        # 检查元素覆盖程度
        covered_elements = set()
        for line in lines:
            covered_elements.update(line.elements)
        
        coverage_ratio = len(covered_elements) / len(all_elements)
        
        # 检查连接完整性
        connection_completeness = len(lines) / max(len(all_elements) - 1, 1)
        connection_completeness = min(connection_completeness, 1.0)
        
        return (coverage_ratio + connection_completeness) / 2.0
    
    def _identify_central_elements(self, lines: List[DataLine], all_elements: Set[str]) -> List[str]:
        """识别中心元素"""
        element_centrality = Counter()
        
        for line in lines:
            # 给线上的每个元素计算中心性得分
            for element in line.elements:
                # 基于线的质量和语义强度加权
                centrality_score = line.quality_score * line.semantic_strength
                element_centrality[element] += centrality_score
        
        # 选择中心性得分最高的元素
        sorted_elements = element_centrality.most_common()
        threshold = len(sorted_elements) * 0.2  # 前20%作为中心元素
        central_elements = [elem for elem, _ in sorted_elements[:int(max(threshold, 1))]]
        
        return central_elements
    
    def _identify_boundary_elements(self, lines: List[DataLine], all_elements: Set[str]) -> List[str]:
        """识别边界元素"""
        element_connections = Counter()
        
        for line in lines:
            for element in line.elements:
                element_connections[element] += 1
        
        # 连接数少的元素更可能是边界元素
        min_connections = min(element_connections.values()) if element_connections else 0
        boundary_elements = [elem for elem, count in element_connections.items() 
                           if count == min_connections]
        
        return boundary_elements
    
    def _cluster_surface_elements(self, lines: List[DataLine], all_elements: Set[str]) -> List[List[str]]:
        """对表面元素进行聚类"""
        # 构建元素相似性图
        G = nx.Graph()
        G.add_nodes_from(all_elements)
        
        # 基于共现关系添加边
        for line in lines:
            elements = line.elements
            for i, elem1 in enumerate(elements):
                for elem2 in elements[i+1:]:
                    weight = line.semantic_strength
                    if G.has_edge(elem1, elem2):
                        G[elem1][elem2]['weight'] += weight
                    else:
                        G.add_edge(elem1, elem2, weight=weight)
        
        # 基于模块度进行社区检测
        clusters = []
        try:
            communities = nx.community.greedy_modularity_communities(G)
            clusters = [list(community) for community in communities]
        except:
            # 如果社区检测失败，使用连通分量
            clusters = [list(component) for component in nx.connected_components(G)]
        
        return clusters
    
    async def _analyze_cross_surface_connections(self, 
                                               surfaces: List[DataSurface]) -> List[Tuple[str, str, float]]:
        """分析跨表面连接"""
        connections = []
        
        for i, surface1 in enumerate(surfaces):
            for surface2 in surfaces[i+1:]:
                connection_strength = self._calculate_surface_connection_strength(
                    surface1, surface2)
                
                if connection_strength > 0.1:  # 连接强度阈值
                    connections.append((surface1.surface_id, surface2.surface_id, connection_strength))
        
        # 按连接强度排序
        connections.sort(key=lambda x: x[2], reverse=True)
        return connections
    
    def _calculate_surface_connection_strength(self, 
                                             surface1: DataSurface, 
                                             surface2: DataSurface) -> float:
        """计算表面间连接强度"""
        # 元素重叠度
        shared_elements = surface1.elements.intersection(surface2.elements)
        total_elements = surface1.elements.union(surface2.elements)
        element_overlap = len(shared_elements) / len(total_elements) if total_elements else 0
        
        # 视角相关性
        perspective_similarity = 1.0 if surface1.perspective == surface2.perspective else 0.5
        
        # 质量匹配度
        quality_similarity = 1.0 - abs(surface1.quality_score - surface2.quality_score)
        
        return (element_overlap + perspective_similarity + quality_similarity) / 3.0
    
    def _calculate_overall_metrics(self, 
                                 surfaces: List[DataSurface],
                                 connections: List[Tuple[str, str, float]]) -> Dict[str, float]:
        """计算整体指标"""
        if not surfaces:
            return {}
        
        # 表面质量统计
        quality_scores = [surface.quality_score for surface in surfaces]
        avg_quality = sum(quality_scores) / len(quality_scores)
        
        # 密度统计
        densities = [surface.density for surface in surfaces]
        avg_density = sum(densities) / len(densities)
        
        # 连接强度统计
        connection_strengths = [strength for _, _, strength in connections]
        avg_connection_strength = sum(connection_strengths) / len(connection_strengths) if connection_strengths else 0
        
        # 覆盖率
        total_elements = set()
        for surface in surfaces:
            total_elements.update(surface.elements)
        coverage = len(total_elements)
        
        return {
            'surface_count': len(surfaces),
            'average_quality': avg_quality,
            'average_density': avg_density,
            'average_connection_strength': avg_connection_strength,
            'total_element_coverage': coverage,
            'connection_count': len(connections)
        }
    
    def _generate_recommendations(self, 
                                surfaces: List[DataSurface],
                                metrics: Dict[str, float]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 质量建议
        avg_quality = metrics.get('average_quality', 0)
        if avg_quality < 0.7:
            recommendations.append("建议优化表面质量，当前平均质量偏低")
        
        # 密度建议
        avg_density = metrics.get('average_density', 0)
        if avg_density < 0.5:
            recommendations.append("建议增强表面密度，加强元素间连接")
        
        # 连接建议
        connection_count = metrics.get('connection_count', 0)
        surface_count = metrics.get('surface_count', 1)
        if connection_count < surface_count * 0.5:
            recommendations.append("建议增加跨表面连接，提升整体连贯性")
        
        # 视角平衡建议
        perspective_distribution = Counter(surface.perspective for surface in surfaces)
        if len(perspective_distribution) < 3:
            recommendations.append("建议增加更多视角的表面分析，提升分析全面性")
        
        return recommendations


class PerspectiveAnalyzer:
    """视角分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def analyze_perspectives(self, 
                                 data_lines: List[DataLine],
                                 target_perspectives: List[PerspectiveType]) -> Dict[str, Any]:
        """分析视角相关性"""
        analysis = {
            'perspective_coverage': {},
            'line_perspective_mapping': {},
            'perspective_quality': {},
            'recommendations': []
        }
        
        for perspective in target_perspectives:
            # 计算视角覆盖率
            relevant_lines = [line for line in data_lines 
                            if line.perspective_relevance.get(perspective.value, 0) >= 0.3]
            coverage = len(relevant_lines) / len(data_lines) if data_lines else 0
            analysis['perspective_coverage'][perspective.value] = coverage
            
            # 映射线到视角
            analysis['line_perspective_mapping'][perspective.value] = [
                line.line_id for line in relevant_lines]
            
            # 计算视角质量
            if relevant_lines:
                avg_quality = sum(line.quality_score for line in relevant_lines) / len(relevant_lines)
                analysis['perspective_quality'][perspective.value] = avg_quality
            else:
                analysis['perspective_quality'][perspective.value] = 0.0
        
        return analysis


class CrossLineConnector:
    """线间连接器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def analyze_line_relationships(self, 
                                       data_lines: List[DataLine],
                                       perspective_analysis: Dict[str, Any]) -> Dict[str, List[str]]:
        """分析线间关系"""
        line_connections = defaultdict(list)
        
        for i, line1 in enumerate(data_lines):
            for line2 in data_lines[i+1:]:
                connection_strength = self._calculate_line_connection_strength(line1, line2)
                
                if connection_strength >= 0.3:  # 连接阈值
                    line_connections[line1.line_id].append(line2.line_id)
                    line_connections[line2.line_id].append(line1.line_id)
        
        return dict(line_connections)
    
    def _calculate_line_connection_strength(self, line1: DataLine, line2: DataLine) -> float:
        """计算线间连接强度"""
        # 元素重叠
        shared_elements = set(line1.elements).intersection(set(line2.elements))
        total_elements = set(line1.elements).union(set(line2.elements))
        element_overlap = len(shared_elements) / len(total_elements) if total_elements else 0
        
        # 语义相似性
        semantic_similarity = min(line1.semantic_strength, line2.semantic_strength)
        
        # 类型相似性
        type_similarity = 1.0 if line1.line_type == line2.line_type else 0.5
        
        return (element_overlap + semantic_similarity + type_similarity) / 3.0


class SurfaceOptimizer:
    """表面优化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def optimize_surfaces(self, surfaces: List[DataSurface]) -> List[DataSurface]:
        """优化数据片"""
        optimized_surfaces = []
        
        for surface in surfaces:
            # 优化表面
            optimized = await self._optimize_single_surface(surface)
            if optimized.quality_score >= 0.4:  # 质量阈值
                optimized_surfaces.append(optimized)
        
        return optimized_surfaces
    
    async def _optimize_single_surface(self, surface: DataSurface) -> DataSurface:
        """优化单个表面"""
        # 重新计算优化后的指标
        optimized_density = min(surface.density * 1.1, 1.0)  # 轻微提升密度
        optimized_coherence = surface.coherence
        optimized_completeness = surface.completeness
        
        # 更新质量评分
        surface.density = optimized_density
        surface.quality_score = (optimized_density + optimized_coherence + optimized_completeness) / 3.0
        
        return surface


# 导出主要类
__all__ = [
    'LineToSurfaceEngine',
    'DataSurface', 
    'SurfaceAnalysisResult',
    'PerspectiveType',
    'SurfaceType'
] 