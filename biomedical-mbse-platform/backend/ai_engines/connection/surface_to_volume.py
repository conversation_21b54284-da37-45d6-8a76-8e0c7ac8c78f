#!/usr/bin/env python3
"""
连片成面引擎 - Surface to Volume Engine

核心功能：
1. 将数据片连接成立体数据模型
2. 实现跨域关系分析和MBSE成熟度评估
3. 提供业务洞察生成和三维模型构建
4. 支持立体化数据分析和决策支持

作者: XML元数据系统开发团队
版本: v1.0.0
"""

import logging
import asyncio
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)

class VolumeType(Enum):
    """立体数据类型枚举"""
    SYSTEM_VOLUME = "system_volume"           # 系统立体
    BUSINESS_VOLUME = "business_volume"       # 业务立体
    TECHNICAL_VOLUME = "technical_volume"     # 技术立体
    QUALITY_VOLUME = "quality_volume"         # 质量立体
    INTEGRATION_VOLUME = "integration_volume" # 集成立体

class MaturityLevel(Enum):
    """MBSE成熟度级别"""
    INITIAL = 1      # 初始级
    MANAGED = 2      # 管理级  
    DEFINED = 3      # 定义级
    OPTIMIZING = 4   # 优化级
    INNOVATING = 5   # 创新级

@dataclass
class DataVolume:
    """立体数据结构"""
    volume_id: str
    connected_surfaces: List[Any]          # 连接的数据片
    volume_type: VolumeType               # 立体类型
    domains: Set[str]                     # 覆盖的领域
    
    # 立体属性
    complexity: float                     # 复杂度评分
    integration_score: float              # 集成度评分
    maturity_level: MaturityLevel         # MBSE成熟度级别
    business_value: float                 # 业务价值评分
    
    # 三维模型
    dimensional_model: Dict[str, Any]     # 三维模型数据
    cross_domain_connections: List[Tuple[str, str, float]] # 跨域连接
    
    # 洞察分析
    business_insights: List[str]          # 业务洞察
    technical_recommendations: List[str]   # 技术建议
    risk_assessments: List[str]           # 风险评估
    
    # 元数据
    creation_time: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class VolumeAnalysisResult:
    """立体分析结果"""
    volumes: List[DataVolume]
    overall_integration: float
    mbse_maturity_assessment: Dict[str, Any]
    business_impact_analysis: Dict[str, Any]
    cross_volume_relationships: List[Tuple[str, str, float]]
    strategic_recommendations: List[str]
    processing_time: float

class SurfaceToVolumeEngine:
    """连片成面引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.cross_domain_analyzer = CrossDomainAnalyzer()
        self.business_insight_generator = BusinessInsightGenerator()
        self.mbse_assessor = MBSEMaturityAssessor()
        
        # 配置参数
        self.config = {
            'min_surfaces_per_volume': 1,  # 降低最小表面要求
            'complexity_threshold': 0.4,
            'integration_threshold': 0.6,
            'business_value_threshold': 0.3  # 降低业务价值阈值
        }
        
        self.logger.info("连片成面引擎初始化完成")
    
    async def connect_surfaces_to_volumes(self,
                                        data_surfaces: List[Any],
                                        target_domains: List[str],
                                        context: Optional[Dict] = None) -> VolumeAnalysisResult:
        """
        将数据片连接成立体数据模型
        
        Args:
            data_surfaces: 输入的数据片列表
            target_domains: 目标领域列表
            context: 分析上下文
            
        Returns:
            VolumeAnalysisResult: 立体分析结果
        """
        start_time = datetime.now()
        self.logger.info(f"开始连片成面分析，数据片数量: {len(data_surfaces)}, 目标领域: {len(target_domains)}")
        
        try:
            # 1. 跨域关系分析
            cross_domain_analysis = await self.cross_domain_analyzer.analyze_cross_domain_relationships(
                data_surfaces, target_domains)
            
            # 2. 构建立体数据模型
            volumes = await self._build_data_volumes(
                data_surfaces, cross_domain_analysis, target_domains)
            
            # 3. MBSE成熟度评估
            mbse_assessment = await self.mbse_assessor.assess_mbse_maturity(volumes)
            
            # 4. 业务影响分析
            business_impact = await self.business_insight_generator.analyze_business_impact(
                volumes, mbse_assessment)
            
            # 5. 跨立体关系分析
            cross_volume_relationships = await self._analyze_cross_volume_relationships(volumes)
            
            # 6. 整体集成度计算
            overall_integration = self._calculate_overall_integration(volumes, cross_volume_relationships)
            
            # 7. 战略建议生成
            strategic_recommendations = self._generate_strategic_recommendations(
                volumes, mbse_assessment, business_impact)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = VolumeAnalysisResult(
                volumes=volumes,
                overall_integration=overall_integration,
                mbse_maturity_assessment=mbse_assessment,
                business_impact_analysis=business_impact,
                cross_volume_relationships=cross_volume_relationships,
                strategic_recommendations=strategic_recommendations,
                processing_time=processing_time
            )
            
            self.logger.info(f"连片成面分析完成，生成立体数量: {len(volumes)}, 耗时: {processing_time:.3f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"连片成面分析失败: {str(e)}")
            raise
    
    async def _build_data_volumes(self,
                                data_surfaces: List[Any],
                                cross_domain_analysis: Dict[str, Any],
                                target_domains: List[str]) -> List[DataVolume]:
        """构建立体数据模型"""
        volumes = []
        used_surfaces = set()
        
        for domain in target_domains:
            # 为每个领域构建相关的立体模型
            domain_volumes = await self._build_domain_volumes(
                data_surfaces, cross_domain_analysis, domain, used_surfaces)
            volumes.extend(domain_volumes)
        
        return volumes
    
    async def _build_domain_volumes(self,
                                  data_surfaces: List[Any],
                                  cross_domain_analysis: Dict[str, Any],
                                  domain: str,
                                  used_surfaces: Set[str]) -> List[DataVolume]:
        """为特定领域构建立体模型"""
        domain_volumes = []
        
        # 1. 筛选与领域相关的数据片
        relevant_surfaces = [surface for surface in data_surfaces 
                           if surface.surface_id not in used_surfaces
                           and self._is_surface_relevant_to_domain(surface, domain)]
        
        if len(relevant_surfaces) < self.config['min_surfaces_per_volume']:
            return domain_volumes
        
        # 2. 基于跨域连接进行聚类
        surface_clusters = self._cluster_surfaces_by_domain_connections(
            relevant_surfaces, cross_domain_analysis)
        
        # 3. 为每个聚类构建立体模型
        for cluster_idx, surface_cluster in enumerate(surface_clusters):
            if len(surface_cluster) >= self.config['min_surfaces_per_volume']:
                volume = await self._create_volume_from_surfaces(
                    surface_cluster, domain, cluster_idx)
                
                if volume.business_value >= self.config['business_value_threshold']:
                    domain_volumes.append(volume)
                    # 标记已使用的表面
                    for surface in surface_cluster:
                        used_surfaces.add(surface.surface_id)
        
        return domain_volumes
    
    def _is_surface_relevant_to_domain(self, surface, domain: str) -> bool:
        """判断数据片是否与领域相关"""
        # 基于表面类型和领域的匹配规则
        domain_surface_mapping = {
            'functional': ['functional_surface', 'business_surface'],
            'architectural': ['structural_surface', 'integration_surface'],
            'behavioral': ['behavioral_surface', 'workflow_surface'],
            'quality': ['quality_surface', 'verification_surface'],
            'business': ['business_surface', 'value_surface'],
            'integration': ['integration_surface', 'system_surface']
        }
        
        relevant_types = domain_surface_mapping.get(domain, [])
        
        # 首先检查直接匹配
        if hasattr(surface, 'surface_type') and surface.surface_type.value in relevant_types:
            return True
        
        # 如果没有直接匹配，检查表面的domain属性
        if hasattr(surface, 'domain') and surface.domain == domain:
            return True
        
        # 如果还没有匹配，允许一些跨域组合
        cross_domain_allowed = {
            'functional': ['architectural', 'business'],
            'architectural': ['functional', 'behavioral'],
            'behavioral': ['architectural', 'quality'],
            'quality': ['behavioral', 'integration'],
            'business': ['functional', 'integration'],
            'integration': ['quality', 'business']
        }
        
        surface_domain = getattr(surface, 'domain', 'unknown')
        allowed_domains = cross_domain_allowed.get(domain, [])
        
        return surface_domain in allowed_domains
    
    def _cluster_surfaces_by_domain_connections(self, surfaces: List[Any], 
                                              cross_domain_analysis: Dict[str, Any]) -> List[List[Any]]:
        """基于跨域连接对表面进行聚类"""
        clusters = []
        unassigned_surfaces = surfaces.copy()
        
        while unassigned_surfaces:
            # 选择一个种子表面开始新聚类
            seed_surface = unassigned_surfaces.pop(0)
            current_cluster = [seed_surface]
            
            # 查找与种子表面连接的其他表面
            for connection in cross_domain_analysis.get('cross_domain_connections', []):
                if connection['strength'] > 0.1:  # 降低连接强度阈值
                    for surface in unassigned_surfaces[:]:  # 使用切片避免修改迭代中的列表
                        if (surface.surface_id == connection['surface1'] and 
                            seed_surface.surface_id == connection['surface2']) or \
                           (surface.surface_id == connection['surface2'] and 
                            seed_surface.surface_id == connection['surface1']):
                            current_cluster.append(surface)
                            unassigned_surfaces.remove(surface)
            
            # 如果没有找到连接的表面，但聚类为空，至少保留种子表面
            if len(current_cluster) == 1 and not unassigned_surfaces:
                # 这是最后一个表面，保留它
                pass
            
            clusters.append(current_cluster)
        
        return clusters
    
    async def _create_volume_from_surfaces(self, surfaces: List[Any], 
                                         domain: str, cluster_idx: int) -> DataVolume:
        """从表面创建立体模型"""
        volume_id = f"{domain}_volume_{cluster_idx}"
        
        # 计算立体属性
        complexity = self._calculate_volume_complexity(surfaces)
        integration_score = self._calculate_integration_score(surfaces)
        business_value = self._calculate_business_value(surfaces)
        
        # 确定立体类型
        volume_type = self._determine_volume_type(domain, surfaces)
        
        # 构建三维模型
        dimensional_model = await self._build_3d_model(surfaces)
        
        # 分析跨域连接
        cross_domain_connections = self._analyze_cross_domain_connections(surfaces)
        
        # 生成业务洞察
        business_insights = await self._generate_business_insights(surfaces, domain)
        
        # 生成技术建议
        technical_recommendations = self._generate_technical_recommendations(surfaces)
        
        # 风险评估
        risk_assessments = self._assess_risks(surfaces, complexity)
        
        # 确定成熟度级别
        maturity_level = self._determine_maturity_level(complexity, integration_score, business_value)
        
        return DataVolume(
            volume_id=volume_id,
            connected_surfaces=surfaces,
            volume_type=volume_type,
            domains={domain},
            complexity=complexity,
            integration_score=integration_score,
            maturity_level=maturity_level,
            business_value=business_value,
            dimensional_model=dimensional_model,
            cross_domain_connections=cross_domain_connections,
            business_insights=business_insights,
            technical_recommendations=technical_recommendations,
            risk_assessments=risk_assessments
        )
    
    def _calculate_volume_complexity(self, surfaces: List[Any]) -> float:
        """计算立体复杂度"""
        if not surfaces:
            return 0.0
        
        # 基于表面数量、连接数量、元素多样性计算复杂度
        surface_count_factor = min(len(surfaces) / 10.0, 1.0)  # 表面数量因子
        
        # 计算连接复杂度
        total_connections = sum(len(getattr(surface, 'connections', [])) for surface in surfaces)
        connection_factor = min(total_connections / (len(surfaces) * 5), 1.0)
        
        # 计算类型多样性
        surface_types = set(getattr(surface, 'surface_type', 'unknown') for surface in surfaces)
        diversity_factor = min(len(surface_types) / 5.0, 1.0)
        
        return (surface_count_factor + connection_factor + diversity_factor) / 3.0
    
    def _calculate_integration_score(self, surfaces: List[Any]) -> float:
        """计算集成度评分"""
        if len(surfaces) <= 1:
            return 0.0
        
        # 计算表面间的连接密度
        total_possible_connections = len(surfaces) * (len(surfaces) - 1) / 2
        actual_connections = 0
        
        for i, surface1 in enumerate(surfaces):
            for surface2 in surfaces[i+1:]:
                if self._surfaces_are_connected(surface1, surface2):
                    actual_connections += 1
        
        return actual_connections / total_possible_connections if total_possible_connections > 0 else 0.0
    
    def _calculate_business_value(self, surfaces: List[Any]) -> float:
        """计算业务价值"""
        if not surfaces:
            return 0.0
        
        # 基于表面的业务重要性、影响范围等计算价值
        total_value = 0.0
        for surface in surfaces:
            # 获取表面的业务价值属性
            surface_value = getattr(surface, 'business_importance', 0.5)
            impact_scope = getattr(surface, 'impact_scope', 0.5)
            strategic_alignment = getattr(surface, 'strategic_alignment', 0.5)
            
            total_value += (surface_value + impact_scope + strategic_alignment) / 3.0
        
        return total_value / len(surfaces)
    
    def _determine_volume_type(self, domain: str, surfaces: List[Any]) -> VolumeType:
        """确定立体类型"""
        domain_type_mapping = {
            'functional': VolumeType.BUSINESS_VOLUME,
            'architectural': VolumeType.SYSTEM_VOLUME,
            'behavioral': VolumeType.TECHNICAL_VOLUME,
            'quality': VolumeType.QUALITY_VOLUME,
            'business': VolumeType.BUSINESS_VOLUME,
            'integration': VolumeType.INTEGRATION_VOLUME
        }
        return domain_type_mapping.get(domain, VolumeType.SYSTEM_VOLUME)
    
    async def _build_3d_model(self, surfaces: List[Any]) -> Dict[str, Any]:
        """构建三维模型"""
        model = {
            'nodes': [],
            'edges': [],
            'layers': {},
            'spatial_coordinates': {},
            'model_metadata': {
                'surface_count': len(surfaces),
                'model_type': '3d_volume',
                'generation_time': datetime.now().isoformat()
            }
        }
        
        # 为每个表面创建节点
        for i, surface in enumerate(surfaces):
            node = {
                'id': surface.surface_id,
                'type': getattr(surface, 'surface_type', 'unknown'),
                'position': {'x': i * 10, 'y': 0, 'z': 0},  # 简单的空间布局
                'properties': getattr(surface, 'properties', {})
            }
            model['nodes'].append(node)
        
        # 创建表面间的边
        for i, surface1 in enumerate(surfaces):
            for j, surface2 in enumerate(surfaces[i+1:], i+1):
                if self._surfaces_are_connected(surface1, surface2):
                    edge = {
                        'source': surface1.surface_id,
                        'target': surface2.surface_id,
                        'weight': self._calculate_connection_strength(surface1, surface2)
                    }
                    model['edges'].append(edge)
        
        return model
    
    def _analyze_cross_domain_connections(self, surfaces: List[Any]) -> List[Tuple[str, str, float]]:
        """分析跨域连接"""
        connections = []
        
        for i, surface1 in enumerate(surfaces):
            for surface2 in surfaces[i+1:]:
                domain1 = getattr(surface1, 'domain', 'unknown')
                domain2 = getattr(surface2, 'domain', 'unknown')
                
                if domain1 != domain2 and self._surfaces_are_connected(surface1, surface2):
                    strength = self._calculate_connection_strength(surface1, surface2)
                    connections.append((domain1, domain2, strength))
        
        return connections
    
    async def _generate_business_insights(self, surfaces: List[Any], domain: str) -> List[str]:
        """生成业务洞察"""
        insights = []
        
        # 基于表面数量和复杂度生成洞察
        if len(surfaces) > 5:
            insights.append(f"{domain}领域具有高复杂度，建议进行模块化设计")
        
        # 基于连接密度生成洞察
        integration_score = self._calculate_integration_score(surfaces)
        if integration_score > 0.8:
            insights.append(f"{domain}领域集成度很高，具有良好的协同效应")
        elif integration_score < 0.3:
            insights.append(f"{domain}领域集成度较低，存在孤岛化风险")
        
        return insights
    
    def _generate_technical_recommendations(self, surfaces: List[Any]) -> List[str]:
        """生成技术建议"""
        recommendations = []
        
        # 基于表面类型分布生成建议
        surface_types = [getattr(surface, 'surface_type', 'unknown') for surface in surfaces]
        type_counts = Counter(surface_types)
        
        if len(type_counts) == 1:
            recommendations.append("建议增加表面类型多样性以提高系统健壮性")
        
        if len(surfaces) > 10:
            recommendations.append("考虑将大型立体模型分解为更小的子模块")
        
        return recommendations
    
    def _assess_risks(self, surfaces: List[Any], complexity: float) -> List[str]:
        """评估风险"""
        risks = []
        
        if complexity > 0.8:
            risks.append("高复杂度可能导致维护困难")
        
        if len(surfaces) < 2:
            risks.append("表面数量过少，立体模型可能不稳定")
        
        # 检查关键依赖
        critical_surfaces = [s for s in surfaces if getattr(s, 'is_critical', False)]
        if len(critical_surfaces) > len(surfaces) * 0.5:
            risks.append("关键表面比例过高，存在单点故障风险")
        
        return risks
    
    def _determine_maturity_level(self, complexity: float, integration_score: float, 
                                business_value: float) -> MaturityLevel:
        """确定成熟度级别"""
        avg_score = (complexity + integration_score + business_value) / 3.0
        
        if avg_score >= 0.9:
            return MaturityLevel.INNOVATING
        elif avg_score >= 0.7:
            return MaturityLevel.OPTIMIZING
        elif avg_score >= 0.5:
            return MaturityLevel.DEFINED
        elif avg_score >= 0.3:
            return MaturityLevel.MANAGED
        else:
            return MaturityLevel.INITIAL
    
    def _surfaces_are_connected(self, surface1: Any, surface2: Any) -> bool:
        """判断两个表面是否连接"""
        # 检查表面间是否存在连接关系
        surface1_connections = getattr(surface1, 'connections', [])
        return surface2.surface_id in surface1_connections
    
    def _calculate_connection_strength(self, surface1: Any, surface2: Any) -> float:
        """计算连接强度"""
        # 基于共享元素、依赖关系等计算连接强度
        shared_elements = set(getattr(surface1, 'elements', [])) & set(getattr(surface2, 'elements', []))
        max_elements = max(len(getattr(surface1, 'elements', [])), len(getattr(surface2, 'elements', [])))
        
        if max_elements == 0:
            return 0.0
        
        return len(shared_elements) / max_elements
    
    async def _analyze_cross_volume_relationships(self, volumes: List[DataVolume]) -> List[Tuple[str, str, float]]:
        """分析跨立体关系"""
        relationships = []
        
        for i, volume1 in enumerate(volumes):
            for volume2 in volumes[i+1:]:
                # 计算立体间的关系强度
                strength = self._calculate_volume_relationship_strength(volume1, volume2)
                if strength > 0.2:  # 关系强度阈值
                    relationships.append((volume1.volume_id, volume2.volume_id, strength))
        
        return relationships
    
    def _calculate_volume_relationship_strength(self, volume1: DataVolume, volume2: DataVolume) -> float:
        """计算立体间关系强度"""
        # 基于共享领域、跨域连接等计算关系强度
        shared_domains = volume1.domains & volume2.domains
        domain_overlap = len(shared_domains) / len(volume1.domains | volume2.domains) if volume1.domains | volume2.domains else 0
        
        # 检查跨域连接
        cross_connections = 0
        for conn1 in volume1.cross_domain_connections:
            for conn2 in volume2.cross_domain_connections:
                if conn1[0] == conn2[1] or conn1[1] == conn2[0]:  # 存在交叉连接
                    cross_connections += 1
        
        connection_factor = min(cross_connections / 10.0, 1.0)
        
        return (domain_overlap + connection_factor) / 2.0
    
    def _calculate_overall_integration(self, volumes: List[DataVolume], 
                                     cross_volume_relationships: List[Tuple[str, str, float]]) -> float:
        """计算整体集成度"""
        if not volumes:
            return 0.0
        
        # 基于立体内集成度和立体间关系计算整体集成度
        avg_volume_integration = sum(volume.integration_score for volume in volumes) / len(volumes)
        
        # 计算立体间连接密度
        total_possible_relationships = len(volumes) * (len(volumes) - 1) / 2
        actual_relationships = len(cross_volume_relationships)
        inter_volume_integration = actual_relationships / total_possible_relationships if total_possible_relationships > 0 else 0
        
        return (avg_volume_integration + inter_volume_integration) / 2.0
    
    def _generate_strategic_recommendations(self, volumes: List[DataVolume],
                                          mbse_assessment: Dict[str, Any],
                                          business_impact: Dict[str, Any]) -> List[str]:
        """生成战略建议"""
        recommendations = []
        
        # 基于成熟度评估生成建议
        overall_maturity = mbse_assessment.get('overall_maturity_level', MaturityLevel.INITIAL)
        if overall_maturity.value < 3:
            recommendations.append("建议加强MBSE流程标准化，提升整体成熟度")
        
        # 基于业务影响生成建议
        value_potential = business_impact.get('value_creation_potential', 0.0)
        if value_potential > 0.7:
            recommendations.append("具有高价值创造潜力，建议优先投资和发展")
        
        # 基于立体分布生成建议
        volume_types = [volume.volume_type for volume in volumes]
        type_counts = Counter(volume_types)
        
        if len(type_counts) < 3:
            recommendations.append("建议增加立体类型多样性，实现更全面的系统覆盖")
        
        return recommendations


class CrossDomainAnalyzer:
    """跨域分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def analyze_cross_domain_relationships(self,
                                               data_surfaces: List[Any],
                                               target_domains: List[str]) -> Dict[str, Any]:
        """分析跨域关系"""
        analysis = {
            'domain_mapping': {},
            'cross_domain_connections': [],
            'domain_complexity': {},
            'integration_opportunities': []
        }
        
        # 领域映射
        for surface in data_surfaces:
            domain = self._identify_surface_domain(surface)
            if domain not in analysis['domain_mapping']:
                analysis['domain_mapping'][domain] = []
            analysis['domain_mapping'][domain].append(surface.surface_id)
        
        # 跨域连接分析
        for i, surface1 in enumerate(data_surfaces):
            for surface2 in data_surfaces[i+1:]:
                connection_strength = self._calculate_cross_domain_connection_strength(
                    surface1, surface2)
                if connection_strength > 0.1:  # 降低连接强度阈值
                    analysis['cross_domain_connections'].append({
                        'surface1': surface1.surface_id,
                        'surface2': surface2.surface_id,
                        'strength': connection_strength,
                        'domain1': self._identify_surface_domain(surface1),
                        'domain2': self._identify_surface_domain(surface2)
                    })
        
        return analysis
    
    def _identify_surface_domain(self, surface) -> str:
        """识别表面所属领域"""
        # 基于表面类型和内容识别领域
        surface_type_mapping = {
            'functional_surface': 'functional',
            'structural_surface': 'architectural', 
            'behavioral_surface': 'behavioral',
            'quality_surface': 'quality',
            'business_surface': 'business',
            'integration_surface': 'integration'
        }
        return surface_type_mapping.get(surface.surface_type.value, 'unknown')
    
    def _calculate_cross_domain_connection_strength(self, surface1: Any, surface2: Any) -> float:
        """计算跨域连接强度"""
        # 获取表面的领域
        domain1 = self._identify_surface_domain(surface1)
        domain2 = self._identify_surface_domain(surface2)
        
        # 如果是同一领域，连接强度为0（不是跨域连接）
        if domain1 == domain2:
            return 0.0
        
        # 计算基于共享元素的连接强度
        elements1 = set(getattr(surface1, 'elements', []))
        elements2 = set(getattr(surface2, 'elements', []))
        shared_elements = elements1 & elements2
        
        if not elements1 or not elements2:
            return 0.0
        
        # 基于共享元素比例计算基础强度
        base_strength = len(shared_elements) / min(len(elements1), len(elements2))
        
        # 基于表面类型的兼容性调整强度
        compatibility_matrix = {
            ('functional', 'architectural'): 0.9,
            ('functional', 'behavioral'): 0.8,
            ('functional', 'quality'): 0.7,
            ('architectural', 'behavioral'): 0.8,
            ('architectural', 'integration'): 0.9,
            ('behavioral', 'quality'): 0.8,
            ('business', 'functional'): 0.9,
            ('business', 'quality'): 0.7,
            ('integration', 'quality'): 0.8
        }
        
        # 获取兼容性系数（双向查找）
        compatibility = compatibility_matrix.get((domain1, domain2), 
                                                compatibility_matrix.get((domain2, domain1), 0.5))
        
        # 计算最终连接强度
        final_strength = base_strength * compatibility
        
        return min(final_strength, 1.0)  # 确保不超过1.0


class BusinessInsightGenerator:
    """业务洞察生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def analyze_business_impact(self,
                                    volumes: List[DataVolume],
                                    mbse_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """分析业务影响"""
        impact_analysis = {
            'value_creation_potential': 0.0,
            'risk_mitigation_opportunities': [],
            'efficiency_improvements': [],
            'innovation_indicators': [],
            'strategic_alignment': 0.0
        }
        
        # 计算价值创造潜力
        total_business_value = sum(volume.business_value for volume in volumes)
        impact_analysis['value_creation_potential'] = total_business_value / len(volumes) if volumes else 0
        
        # 识别效率改进机会
        for volume in volumes:
            if volume.integration_score > 0.8:
                impact_analysis['efficiency_improvements'].append(
                    f"领域 {volume.volume_id} 具有高集成度，可进一步优化流程效率")
        
        return impact_analysis


class MBSEMaturityAssessor:
    """MBSE成熟度评估器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def assess_mbse_maturity(self, volumes: List[DataVolume]) -> Dict[str, Any]:
        """评估MBSE成熟度"""
        assessment = {
            'overall_maturity_level': MaturityLevel.INITIAL,
            'domain_maturity': {},
            'improvement_areas': [],
            'maturity_score': 0.0
        }
        
        if not volumes:
            return assessment
        
        # 计算整体成熟度
        maturity_scores = []
        for volume in volumes:
            score = self._calculate_volume_maturity_score(volume)
            maturity_scores.append(score)
            
        avg_maturity = sum(maturity_scores) / len(maturity_scores)
        assessment['maturity_score'] = avg_maturity
        assessment['overall_maturity_level'] = self._score_to_maturity_level(avg_maturity)
        
        return assessment
    
    def _calculate_volume_maturity_score(self, volume: DataVolume) -> float:
        """计算立体模型的成熟度评分"""
        # 基于复杂度、集成度等因素计算成熟度
        complexity_factor = min(volume.complexity, 1.0)
        integration_factor = volume.integration_score
        business_value_factor = volume.business_value
        
        return (complexity_factor + integration_factor + business_value_factor) / 3.0
    
    def _score_to_maturity_level(self, score: float) -> MaturityLevel:
        """将评分转换为成熟度级别"""
        if score >= 0.9:
            return MaturityLevel.INNOVATING
        elif score >= 0.7:
            return MaturityLevel.OPTIMIZING
        elif score >= 0.5:
            return MaturityLevel.DEFINED
        elif score >= 0.3:
            return MaturityLevel.MANAGED
        else:
            return MaturityLevel.INITIAL


# 导出主要类
__all__ = [
    'SurfaceToVolumeEngine',
    'DataVolume',
    'VolumeAnalysisResult', 
    'VolumeType',
    'MaturityLevel'
] 