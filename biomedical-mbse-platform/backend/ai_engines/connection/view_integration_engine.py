#!/usr/bin/env python3
"""
视角集成引擎 - View Integration Engine

核心功能：
1. 实现4种标准视角的完整集成算法
2. 支持需求→架构→行为→测试的关联映射
3. 提供跨视角的依赖关系和影响链分析
4. 集成语义分析和术语标准化能力

基于UML/SysML改进经验和语义分析体系
作者: XML元数据系统开发团队
版本: v3.0.0
"""

import logging
import asyncio
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter
import networkx as nx
from datetime import datetime

# 导入已有的连线成片引擎
try:
    from .line_to_surface import LineToSurfaceEngine, DataLine, DataSurface, PerspectiveType, SurfaceType
except ImportError:
    # 如果导入失败，创建简化的类定义
    class LineToSurfaceEngine:
        def __init__(self):
            pass
        
        async def connect_lines_to_surfaces(self, data_lines, target_perspectives, context=None):
            return {'surfaces': [], 'analysis': {}}
    
    @dataclass
    class DataLine:
        line_id: str
        elements: List[str] = field(default_factory=list)
        semantic_strength: float = 0.0
        line_type: str = ""
        start_element: str = ""
        end_element: str = ""
        intermediate_elements: List[str] = field(default_factory=list)
        perspective_relevance: Dict[str, float] = field(default_factory=dict)
        quality_score: float = 0.0
        metadata: Dict[str, Any] = field(default_factory=dict)
    
    @dataclass
    class DataSurface:
        surface_id: str
        elements: Set[str] = field(default_factory=set)
        metadata: Dict[str, Any] = field(default_factory=dict)
    
    class PerspectiveType(Enum):
        REQUIREMENTS_ANALYST = "requirements_analyst"
        SYSTEM_ARCHITECT = "system_architect"
        BEHAVIOR_ANALYST = "behavior_analyst"
        TEST_ENGINEER = "test_engineer"
    
    class SurfaceType(Enum):
        FUNCTIONAL_SURFACE = "functional_surface"

# 设置日志
logger = logging.getLogger(__name__)

class StandardPerspective(Enum):
    """4种标准视角枚举"""
    REQUIREMENTS_ANALYST = "requirements_analyst"    # 需求分析师视角
    SYSTEM_ARCHITECT = "system_architect"           # 系统架构师视角  
    BEHAVIOR_ANALYST = "behavior_analyst"           # 行为分析师视角
    TEST_ENGINEER = "test_engineer"                 # 测试工程师视角

class IntegrationStrategy(Enum):
    """集成策略枚举"""
    REQUIREMENTS_TO_ARCHITECTURE = "req_to_arch"    # 需求→架构集成
    ARCHITECTURE_TO_BEHAVIOR = "arch_to_behavior"   # 架构→行为集成
    BEHAVIOR_TO_TESTING = "behavior_to_test"        # 行为→测试集成
    CROSS_PERSPECTIVE = "cross_perspective"         # 跨视角集成

@dataclass
class PerspectiveMapping:
    """视角映射结构"""
    source_perspective: StandardPerspective
    target_perspective: StandardPerspective
    mapping_elements: Dict[str, List[str]]          # 源元素→目标元素映射
    relationship_strength: float                    # 关系强度
    dependency_chain: List[str]                     # 依赖链
    impact_analysis: Dict[str, float]               # 影响分析
    semantic_similarity: float                      # 语义相似度
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class IntegrationResult:
    """集成结果结构"""
    integrated_perspectives: List[StandardPerspective]
    perspective_mappings: List[PerspectiveMapping]
    cross_perspective_connections: Dict[str, List[str]]
    dependency_graph: nx.DiGraph
    impact_chains: List[List[str]]
    integration_quality: float
    semantic_coherence: float
    completeness_score: float
    processing_time: float
    recommendations: List[str]

class ViewIntegrationEngine:
    """视角集成引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 集成已有的连线成片引擎
        self.line_to_surface_engine = LineToSurfaceEngine()
        
        # 集成策略映射
        self.integration_strategies = {
            IntegrationStrategy.REQUIREMENTS_TO_ARCHITECTURE: self._integrate_req_to_arch,
            IntegrationStrategy.ARCHITECTURE_TO_BEHAVIOR: self._integrate_arch_to_behavior,
            IntegrationStrategy.BEHAVIOR_TO_TESTING: self._integrate_behavior_to_test,
            IntegrationStrategy.CROSS_PERSPECTIVE: self._integrate_cross_perspective
        }
        
        # 语义分析配置
        self.semantic_config = {
            'similarity_threshold': 0.7,
            'dependency_strength_threshold': 0.5,
            'integration_quality_threshold': 0.8
        }
        
        # 4种标准视角的特征定义
        self.perspective_features = {
            StandardPerspective.REQUIREMENTS_ANALYST: {
                'focus_elements': ['requirement', 'constraint', 'specification', 'stakeholder_need'],
                'analysis_depth': 'functional_requirements',
                'semantic_keywords': ['shall', 'must', 'should', 'requirement', 'constraint'],
                'quality_attributes': ['completeness', 'consistency', 'traceability']
            },
            StandardPerspective.SYSTEM_ARCHITECT: {
                'focus_elements': ['component', 'interface', 'module', 'subsystem', 'architecture'],
                'analysis_depth': 'structural_design',
                'semantic_keywords': ['component', 'interface', 'module', 'architecture', 'design'],
                'quality_attributes': ['modularity', 'cohesion', 'coupling', 'scalability']
            },
            StandardPerspective.BEHAVIOR_ANALYST: {
                'focus_elements': ['behavior', 'workflow', 'interaction', 'sequence', 'activity'],
                'analysis_depth': 'behavioral_patterns',
                'semantic_keywords': ['behavior', 'workflow', 'process', 'interaction', 'sequence'],
                'quality_attributes': ['correctness', 'performance', 'reliability']
            },
            StandardPerspective.TEST_ENGINEER: {
                'focus_elements': ['test_case', 'verification', 'validation', 'test_scenario'],
                'analysis_depth': 'quality_assurance',
                'semantic_keywords': ['test', 'verify', 'validate', 'check', 'assert'],
                'quality_attributes': ['coverage', 'effectiveness', 'efficiency']
            }
        }
        
        self.logger.info("视角集成引擎初始化完成 - 支持4种标准视角")
    
    async def integrate_perspectives(self, 
                                   elements: List[Dict[str, Any]], 
                                   target_perspectives: List[StandardPerspective],
                                   integration_config: Optional[Dict] = None) -> IntegrationResult:
        """
        集成多个视角的完整算法
        
        Args:
            elements: 要分析的元素列表
            target_perspectives: 目标视角列表
            integration_config: 集成配置参数
            
        Returns:
            IntegrationResult: 集成结果
        """
        start_time = datetime.now()
        self.logger.info(f"开始视角集成分析，元素数量: {len(elements)}, 目标视角: {len(target_perspectives)}")
        
        try:
            # 1. 元素预处理和语义标准化
            standardized_elements = await self._standardize_elements_semantics(elements)
            
            # 2. 为每个视角生成数据线
            perspective_data_lines = await self._generate_perspective_data_lines(
                standardized_elements, target_perspectives)
            
            # 3. 执行连线成片分析
            surface_results = {}
            for perspective in target_perspectives:
                if perspective in perspective_data_lines:
                    surface_result = await self.line_to_surface_engine.connect_lines_to_surfaces(
                        perspective_data_lines[perspective], [perspective], integration_config)
                    surface_results[perspective] = surface_result
            
            # 4. 构建视角映射
            perspective_mappings = await self._build_perspective_mappings(
                surface_results, target_perspectives)
            
            # 5. 分析跨视角连接
            cross_perspective_connections = await self._analyze_cross_perspective_connections(
                perspective_mappings, surface_results)
            
            # 6. 构建依赖图
            dependency_graph = await self._build_dependency_graph(
                perspective_mappings, cross_perspective_connections)
            
            # 7. 分析影响链
            impact_chains = await self._analyze_impact_chains(dependency_graph)
            
            # 8. 计算集成质量指标
            integration_quality = self._calculate_integration_quality(
                perspective_mappings, cross_perspective_connections)
            
            semantic_coherence = self._calculate_semantic_coherence(
                perspective_mappings, standardized_elements)
            
            completeness_score = self._calculate_completeness_score(
                target_perspectives, perspective_mappings)
            
            # 9. 生成建议
            recommendations = self._generate_integration_recommendations(
                perspective_mappings, integration_quality, semantic_coherence)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = IntegrationResult(
                integrated_perspectives=target_perspectives,
                perspective_mappings=perspective_mappings,
                cross_perspective_connections=cross_perspective_connections,
                dependency_graph=dependency_graph,
                impact_chains=impact_chains,
                integration_quality=integration_quality,
                semantic_coherence=semantic_coherence,
                completeness_score=completeness_score,
                processing_time=processing_time,
                recommendations=recommendations
            )
            
            self.logger.info(f"视角集成分析完成，集成质量: {integration_quality:.3f}, 耗时: {processing_time:.3f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"视角集成分析失败: {str(e)}")
            raise
    
    async def _standardize_elements_semantics(self, elements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """元素语义标准化 - 基于术语体系"""
        standardized = []
        
        for element in elements:
            # 1. 术语标准化
            standardized_element = {
                **element,
                'semantic_features': self._extract_semantic_features(element),
                'perspective_relevance': self._calculate_perspective_relevance(element),
                'quality_indicators': self._extract_quality_indicators(element)
            }
            
            # 2. 语义域分类
            standardized_element['semantic_domain'] = self._classify_semantic_domain(element)
            
            # 3. 标准术语映射
            standardized_element['standard_terminology'] = self._map_to_standard_terminology(element)
            
            standardized.append(standardized_element)
        
        return standardized
    
    def _extract_semantic_features(self, element: Dict[str, Any]) -> Dict[str, Any]:
        """提取语义特征"""
        features = {
            'element_type': element.get('type', ''),
            'content_keywords': self._extract_keywords(element.get('content', '')),
            'structural_position': element.get('position', ''),
            'relationship_count': len(element.get('relationships', [])),
            'complexity_score': self._calculate_element_complexity(element)
        }
        return features
    
    def _extract_quality_indicators(self, element: Dict[str, Any]) -> Dict[str, Any]:
        """提取质量指标"""
        indicators = {
            'completeness': self._assess_element_completeness(element),
            'consistency': self._assess_element_consistency(element),
            'traceability': self._assess_element_traceability(element),
            'clarity': self._assess_element_clarity(element),
            'testability': self._assess_element_testability(element)
        }
        return indicators
    
    def _assess_element_completeness(self, element: Dict[str, Any]) -> float:
        """评估元素完整性"""
        score = 0.0
        
        # 检查必要字段
        required_fields = ['id', 'name', 'type', 'content']
        present_fields = sum(1 for field in required_fields if element.get(field))
        score += (present_fields / len(required_fields)) * 0.4
        
        # 检查内容长度
        content_length = len(element.get('content', ''))
        if content_length > 10:
            score += 0.3
        elif content_length > 0:
            score += 0.1
        
        # 检查属性和关系
        if element.get('attributes'):
            score += 0.15
        if element.get('relationships'):
            score += 0.15
        
        return min(score, 1.0)
    
    def _assess_element_consistency(self, element: Dict[str, Any]) -> float:
        """评估元素一致性"""
        # 简化的一致性评估
        score = 0.7  # 默认中等一致性
        
        # 检查类型和内容的一致性
        element_type = element.get('type', '').lower()
        content = element.get('content', '').lower()
        
        # 类型关键词匹配
        type_keywords = {
            'requirement': ['shall', 'must', 'should', 'requirement'],
            'component': ['component', 'module', 'interface'],
            'test': ['test', 'verify', 'validate', 'check'],
            'behavior': ['behavior', 'process', 'workflow', 'activity']
        }
        
        for type_key, keywords in type_keywords.items():
            if type_key in element_type:
                keyword_matches = sum(1 for keyword in keywords if keyword in content)
                if keyword_matches > 0:
                    score += 0.2
                break
        
        return min(score, 1.0)
    
    def _assess_element_traceability(self, element: Dict[str, Any]) -> float:
        """评估元素可追溯性"""
        score = 0.0
        
        # 检查ID格式
        element_id = element.get('id', '')
        if element_id and '_' in element_id:
            score += 0.3
        
        # 检查关系数量
        relationships = element.get('relationships', [])
        if relationships:
            score += min(len(relationships) * 0.1, 0.4)
        
        # 检查源信息
        if element.get('source'):
            score += 0.3
        elif element.get('attributes'):
            # attributes是列表，检查是否包含source相关信息
            attributes = element.get('attributes', [])
            if isinstance(attributes, list) and 'source' in attributes:
                score += 0.3
        
        return min(score, 1.0)
    
    def _assess_element_clarity(self, element: Dict[str, Any]) -> float:
        """评估元素清晰度"""
        score = 0.0
        
        # 检查名称清晰度
        name = element.get('name', '')
        if name and len(name) > 5:
            score += 0.3
        
        # 检查内容清晰度
        content = element.get('content', '')
        if content:
            # 简单的清晰度指标：长度适中，包含关键词
            if 10 <= len(content) <= 500:
                score += 0.4
            
            # 检查是否包含明确的动词
            action_words = ['shall', 'must', 'should', 'will', 'can', 'verify', 'test', 'implement']
            if any(word in content.lower() for word in action_words):
                score += 0.3
        
        return min(score, 1.0)
    
    def _assess_element_testability(self, element: Dict[str, Any]) -> float:
        """评估元素可测试性"""
        score = 0.0
        
        element_type = element.get('type', '').lower()
        content = element.get('content', '').lower()
        
        # 测试相关元素得分更高
        if 'test' in element_type:
            score += 0.5
        
        # 包含可测试关键词
        testable_keywords = ['verify', 'validate', 'check', 'measure', 'confirm', 'ensure']
        keyword_matches = sum(1 for keyword in testable_keywords if keyword in content)
        score += min(keyword_matches * 0.1, 0.3)
        
        # 包含具体数值或标准
        if any(char.isdigit() for char in content):
            score += 0.2
        
        return min(score, 1.0)
    
    def _calculate_perspective_relevance(self, element: Dict[str, Any]) -> Dict[str, float]:
        """计算元素对各视角的相关性"""
        relevance = {}
        
        for perspective, features in self.perspective_features.items():
            score = 0.0
            
            # 基于元素类型的相关性
            if element.get('type') in features['focus_elements']:
                score += 0.4
            
            # 基于语义关键词的相关性
            content = element.get('content', '').lower()
            keyword_matches = sum(1 for keyword in features['semantic_keywords'] 
                                if keyword in content)
            score += (keyword_matches / len(features['semantic_keywords'])) * 0.3
            
            # 基于质量属性的相关性
            quality_score = self._assess_quality_relevance(element, features['quality_attributes'])
            score += quality_score * 0.3
            
            relevance[perspective.value] = min(score, 1.0)
        
        return relevance
    
    async def _generate_perspective_data_lines(self, 
                                             elements: List[Dict[str, Any]], 
                                             perspectives: List[StandardPerspective]) -> Dict[StandardPerspective, List[DataLine]]:
        """为每个视角生成数据线"""
        perspective_lines = {}
        
        for perspective in perspectives:
            # 筛选与该视角相关的元素
            relevant_elements = [elem for elem in elements 
                               if elem['perspective_relevance'].get(perspective.value, 0) > 0.3]
            
            # 构建数据线
            lines = await self._build_data_lines_for_perspective(relevant_elements, perspective)
            perspective_lines[perspective] = lines
        
        return perspective_lines
    
    async def _build_data_lines_for_perspective(self, 
                                              elements: List[Dict[str, Any]], 
                                              perspective: StandardPerspective) -> List[DataLine]:
        """为特定视角构建数据线"""
        lines = []
        
        # 基于语义相似性和关系构建线
        for i, elem1 in enumerate(elements):
            for j, elem2 in enumerate(elements[i+1:], i+1):
                # 计算元素间的连接强度
                connection_strength = self._calculate_element_connection_strength(
                    elem1, elem2, perspective)
                
                if connection_strength > 0.5:  # 阈值过滤
                    line = DataLine(
                        line_id=f"{perspective.value}_line_{i}_{j}",
                        elements=[elem1['id'], elem2['id']],
                        semantic_strength=connection_strength,
                        line_type=f"{perspective.value}_connection",
                        start_element=elem1['id'],
                        end_element=elem2['id'],
                        intermediate_elements=[],
                        perspective_relevance={perspective.value: connection_strength},
                        quality_score=self._calculate_line_quality(elem1, elem2, perspective)
                    )
                    lines.append(line)
        
        return lines
    
    def _calculate_element_connection_strength(self, 
                                             elem1: Dict[str, Any], 
                                             elem2: Dict[str, Any], 
                                             perspective: StandardPerspective) -> float:
        """计算元素间的连接强度"""
        strength = 0.0
        
        # 1. 语义相似性
        semantic_sim = self._calculate_semantic_similarity(
            elem1['semantic_features'], elem2['semantic_features'])
        strength += semantic_sim * 0.4
        
        # 2. 结构关系
        structural_rel = self._calculate_structural_relationship(elem1, elem2)
        strength += structural_rel * 0.3
        
        # 3. 视角特定关系
        perspective_rel = self._calculate_perspective_specific_relationship(
            elem1, elem2, perspective)
        strength += perspective_rel * 0.3
        
        return min(strength, 1.0)
    
    async def _build_perspective_mappings(self, 
                                        surface_results: Dict[StandardPerspective, Any],
                                        perspectives: List[StandardPerspective]) -> List[PerspectiveMapping]:
        """构建视角映射"""
        mappings = []
        
        # 构建所有视角对的映射
        for i, source_perspective in enumerate(perspectives):
            for target_perspective in perspectives[i+1:]:
                mapping = await self._create_perspective_mapping(
                    source_perspective, target_perspective, surface_results)
                mappings.append(mapping)
        
        return mappings
    
    async def _create_perspective_mapping(self, 
                                        source: StandardPerspective,
                                        target: StandardPerspective,
                                        surface_results: Dict) -> PerspectiveMapping:
        """创建两个视角间的映射"""
        
        # 获取源和目标视角的表面
        source_surfaces = []
        target_surfaces = []
        
        if source in surface_results:
            source_result = surface_results[source]
            if hasattr(source_result, 'surfaces'):
                source_surfaces = source_result.surfaces
            elif isinstance(source_result, dict):
                source_surfaces = source_result.get('surfaces', [])
        
        if target in surface_results:
            target_result = surface_results[target]
            if hasattr(target_result, 'surfaces'):
                target_surfaces = target_result.surfaces
            elif isinstance(target_result, dict):
                target_surfaces = target_result.get('surfaces', [])
        
        # 构建元素映射
        mapping_elements = {}
        total_strength = 0.0
        mapping_count = 0
        
        for source_surface in source_surfaces:
            for target_surface in target_surfaces:
                # 计算表面间的映射
                surface_mapping = self._calculate_surface_mapping(source_surface, target_surface)
                if surface_mapping['strength'] > 0.3:
                    mapping_elements.update(surface_mapping['mappings'])
                    total_strength += surface_mapping['strength']
                    mapping_count += 1
        
        # 计算平均关系强度
        relationship_strength = total_strength / mapping_count if mapping_count > 0 else 0.0
        
        # 构建依赖链
        dependency_chain = self._build_dependency_chain(source, target, mapping_elements)
        
        # 影响分析
        impact_analysis = self._analyze_mapping_impact(mapping_elements, source, target)
        
        # 语义相似度
        semantic_similarity = self._calculate_perspective_semantic_similarity(source, target)
        
        return PerspectiveMapping(
            source_perspective=source,
            target_perspective=target,
            mapping_elements=mapping_elements,
            relationship_strength=relationship_strength,
            dependency_chain=dependency_chain,
            impact_analysis=impact_analysis,
            semantic_similarity=semantic_similarity
        )
    
    # 集成策略实现
    async def _integrate_req_to_arch(self, req_elements: List[Dict], arch_elements: List[Dict]) -> Dict:
        """需求→架构集成策略"""
        integration_result = {
            'strategy': 'requirements_to_architecture',
            'mappings': [],
            'traceability_matrix': {},
            'coverage_analysis': {}
        }
        
        # 需求到架构的追溯映射
        for req in req_elements:
            related_arch = []
            for arch in arch_elements:
                if self._is_requirement_satisfied_by_architecture(req, arch):
                    related_arch.append(arch['id'])
            
            if related_arch:
                integration_result['mappings'].append({
                    'requirement': req['id'],
                    'architecture_components': related_arch,
                    'satisfaction_level': self._calculate_satisfaction_level(req, related_arch)
                })
        
        return integration_result
    
    async def _integrate_arch_to_behavior(self, arch_elements: List[Dict], behavior_elements: List[Dict]) -> Dict:
        """架构→行为集成策略"""
        integration_result = {
            'strategy': 'architecture_to_behavior',
            'behavioral_mappings': [],
            'interaction_patterns': {},
            'performance_implications': {}
        }
        
        # 架构组件到行为的映射
        for arch in arch_elements:
            related_behaviors = []
            for behavior in behavior_elements:
                if self._does_architecture_enable_behavior(arch, behavior):
                    related_behaviors.append(behavior['id'])
            
            if related_behaviors:
                integration_result['behavioral_mappings'].append({
                    'architecture_component': arch['id'],
                    'enabled_behaviors': related_behaviors,
                    'interaction_complexity': self._calculate_interaction_complexity(arch, related_behaviors)
                })
        
        return integration_result
    
    async def _integrate_behavior_to_test(self, behavior_elements: List[Dict], test_elements: List[Dict]) -> Dict:
        """行为→测试集成策略"""
        integration_result = {
            'strategy': 'behavior_to_testing',
            'test_mappings': [],
            'coverage_matrix': {},
            'verification_strategy': {}
        }
        
        # 行为到测试的验证映射
        for behavior in behavior_elements:
            related_tests = []
            for test in test_elements:
                if self._does_test_verify_behavior(test, behavior):
                    related_tests.append(test['id'])
            
            if related_tests:
                integration_result['test_mappings'].append({
                    'behavior': behavior['id'],
                    'verification_tests': related_tests,
                    'coverage_level': self._calculate_test_coverage_level(behavior, related_tests)
                })
        
        return integration_result
    
    async def _integrate_cross_perspective(self, all_perspectives: Dict) -> Dict:
        """跨视角集成策略"""
        integration_result = {
            'strategy': 'cross_perspective',
            'global_connections': [],
            'consistency_analysis': {},
            'gap_analysis': {}
        }
        
        # 全局一致性分析
        consistency_issues = self._analyze_cross_perspective_consistency(all_perspectives)
        integration_result['consistency_analysis'] = consistency_issues
        
        # 缺口分析
        gaps = self._identify_integration_gaps(all_perspectives)
        integration_result['gap_analysis'] = gaps
        
        return integration_result
    
    # 辅助方法
    def _extract_keywords(self, content: str) -> List[str]:
        """提取内容关键词"""
        # 简化的关键词提取
        words = content.lower().split()
        return [word for word in words if len(word) > 3]
    
    def _calculate_element_complexity(self, element: Dict[str, Any]) -> float:
        """计算元素复杂度"""
        complexity = 0.0
        complexity += len(element.get('relationships', [])) * 0.1
        complexity += len(element.get('attributes', [])) * 0.05
        complexity += len(element.get('content', '')) / 100.0
        return min(complexity, 1.0)
    
    def _assess_quality_relevance(self, element: Dict[str, Any], quality_attributes: List[str]) -> float:
        """评估质量相关性"""
        # 简化的质量评估
        return 0.5  # 默认中等相关性
    
    def _classify_semantic_domain(self, element: Dict[str, Any]) -> str:
        """分类语义域"""
        element_type = element.get('type', '').lower()
        
        if any(keyword in element_type for keyword in ['requirement', 'need', 'constraint']):
            return 'requirements'
        elif any(keyword in element_type for keyword in ['component', 'module', 'interface']):
            return 'architecture'
        elif any(keyword in element_type for keyword in ['behavior', 'activity', 'workflow']):
            return 'behavior'
        elif any(keyword in element_type for keyword in ['test', 'verification', 'validation']):
            return 'testing'
        else:
            return 'general'
    
    def _map_to_standard_terminology(self, element: Dict[str, Any]) -> Dict[str, str]:
        """映射到标准术语"""
        # 简化的术语映射
        return {
            'standard_type': element.get('type', ''),
            'standard_name': element.get('name', ''),
            'domain': self._classify_semantic_domain(element)
        }
    
    def _calculate_semantic_similarity(self, features1: Dict, features2: Dict) -> float:
        """计算语义相似性"""
        # 简化的相似性计算
        type_sim = 1.0 if features1.get('element_type') == features2.get('element_type') else 0.0
        keyword_sim = len(set(features1.get('content_keywords', [])) & 
                         set(features2.get('content_keywords', []))) / \
                     max(len(features1.get('content_keywords', [])), 
                         len(features2.get('content_keywords', [])), 1)
        
        return (type_sim * 0.6 + keyword_sim * 0.4)
    
    def _calculate_structural_relationship(self, elem1: Dict, elem2: Dict) -> float:
        """计算结构关系"""
        # 检查是否有直接关系
        elem1_relations = set(elem1.get('relationships', []))
        elem2_relations = set(elem2.get('relationships', []))
        
        if elem2['id'] in elem1_relations or elem1['id'] in elem2_relations:
            return 1.0
        
        # 检查共同关系
        common_relations = elem1_relations & elem2_relations
        if common_relations:
            return len(common_relations) / max(len(elem1_relations), len(elem2_relations), 1)
        
        return 0.0
    
    def _calculate_perspective_specific_relationship(self, 
                                                   elem1: Dict, 
                                                   elem2: Dict, 
                                                   perspective: StandardPerspective) -> float:
        """计算视角特定关系"""
        perspective_features = self.perspective_features[perspective]
        
        # 检查两个元素是否都与该视角相关
        elem1_relevance = elem1['perspective_relevance'].get(perspective.value, 0)
        elem2_relevance = elem2['perspective_relevance'].get(perspective.value, 0)
        
        return (elem1_relevance + elem2_relevance) / 2.0
    
    def _calculate_line_quality(self, elem1: Dict, elem2: Dict, perspective: StandardPerspective) -> float:
        """计算线质量"""
        # 基于元素质量和连接强度计算线质量
        elem1_quality = elem1.get('quality_score', 0.5)
        elem2_quality = elem2.get('quality_score', 0.5)
        connection_strength = self._calculate_element_connection_strength(elem1, elem2, perspective)
        
        return (elem1_quality + elem2_quality + connection_strength) / 3.0
    
    def _calculate_surface_mapping(self, source_surface: DataSurface, target_surface: DataSurface) -> Dict:
        """计算表面映射"""
        # 计算两个表面间的元素映射
        source_elements = source_surface.elements
        target_elements = target_surface.elements
        
        mappings = {}
        total_strength = 0.0
        
        for source_elem in source_elements:
            best_target = None
            best_strength = 0.0
            
            for target_elem in target_elements:
                # 这里需要实际的元素相似性计算
                strength = 0.5  # 简化计算
                if strength > best_strength:
                    best_strength = strength
                    best_target = target_elem
            
            if best_target and best_strength > 0.3:
                mappings[source_elem] = [best_target]
                total_strength += best_strength
        
        avg_strength = total_strength / len(mappings) if mappings else 0.0
        
        return {
            'mappings': mappings,
            'strength': avg_strength
        }
    
    def _build_dependency_chain(self, source: StandardPerspective, target: StandardPerspective, mappings: Dict) -> List[str]:
        """构建依赖链"""
        # 简化的依赖链构建
        chain = [source.value]
        
        # 根据标准视角顺序构建链
        perspective_order = [
            StandardPerspective.REQUIREMENTS_ANALYST,
            StandardPerspective.SYSTEM_ARCHITECT,
            StandardPerspective.BEHAVIOR_ANALYST,
            StandardPerspective.TEST_ENGINEER
        ]
        
        source_idx = perspective_order.index(source) if source in perspective_order else 0
        target_idx = perspective_order.index(target) if target in perspective_order else len(perspective_order) - 1
        
        for i in range(source_idx + 1, target_idx + 1):
            chain.append(perspective_order[i].value)
        
        return chain
    
    def _analyze_mapping_impact(self, mappings: Dict, source: StandardPerspective, target: StandardPerspective) -> Dict[str, float]:
        """分析映射影响"""
        impact = {
            'coverage_impact': len(mappings) / 10.0,  # 简化计算
            'complexity_impact': min(len(mappings) * 0.1, 1.0),
            'quality_impact': 0.7  # 默认值
        }
        return impact
    
    def _calculate_perspective_semantic_similarity(self, source: StandardPerspective, target: StandardPerspective) -> float:
        """计算视角语义相似性"""
        source_features = self.perspective_features[source]
        target_features = self.perspective_features[target]
        
        # 计算关键词重叠
        source_keywords = set(source_features['semantic_keywords'])
        target_keywords = set(target_features['semantic_keywords'])
        
        overlap = len(source_keywords & target_keywords)
        total = len(source_keywords | target_keywords)
        
        return overlap / total if total > 0 else 0.0
    
    async def _analyze_cross_perspective_connections(self, mappings: List[PerspectiveMapping], surface_results: Dict) -> Dict[str, List[str]]:
        """分析跨视角连接"""
        connections = defaultdict(list)
        
        for mapping in mappings:
            source_key = mapping.source_perspective.value
            target_key = mapping.target_perspective.value
            
            # 添加双向连接
            connections[source_key].append(target_key)
            connections[target_key].append(source_key)
        
        return dict(connections)
    
    async def _build_dependency_graph(self, mappings: List[PerspectiveMapping], connections: Dict) -> nx.DiGraph:
        """构建依赖图"""
        graph = nx.DiGraph()
        
        # 添加节点
        for mapping in mappings:
            graph.add_node(mapping.source_perspective.value)
            graph.add_node(mapping.target_perspective.value)
        
        # 添加边
        for mapping in mappings:
            graph.add_edge(
                mapping.source_perspective.value,
                mapping.target_perspective.value,
                weight=mapping.relationship_strength
            )
        
        return graph
    
    async def _analyze_impact_chains(self, dependency_graph: nx.DiGraph) -> List[List[str]]:
        """分析影响链"""
        chains = []
        
        # 找到所有简单路径
        for source in dependency_graph.nodes():
            for target in dependency_graph.nodes():
                if source != target:
                    try:
                        paths = list(nx.all_simple_paths(dependency_graph, source, target, cutoff=4))
                        chains.extend(paths)
                    except nx.NetworkXNoPath:
                        continue
        
        return chains
    
    def _calculate_integration_quality(self, mappings: List[PerspectiveMapping], connections: Dict) -> float:
        """计算集成质量"""
        if not mappings:
            return 0.0
        
        # 基于映射强度和连接数量计算质量
        avg_strength = sum(m.relationship_strength for m in mappings) / len(mappings)
        connection_density = sum(len(conns) for conns in connections.values()) / len(connections) if connections else 0
        
        return (avg_strength * 0.7 + min(connection_density / 4.0, 1.0) * 0.3)
    
    def _calculate_semantic_coherence(self, mappings: List[PerspectiveMapping], elements: List[Dict]) -> float:
        """计算语义连贯性"""
        if not mappings:
            return 0.0
        
        coherence_scores = [m.semantic_similarity for m in mappings]
        return sum(coherence_scores) / len(coherence_scores)
    
    def _calculate_completeness_score(self, perspectives: List[StandardPerspective], mappings: List[PerspectiveMapping]) -> float:
        """计算完整性评分"""
        expected_mappings = len(perspectives) * (len(perspectives) - 1) // 2  # 组合数
        actual_mappings = len(mappings)
        
        return min(actual_mappings / expected_mappings, 1.0) if expected_mappings > 0 else 0.0
    
    def _generate_integration_recommendations(self, mappings: List[PerspectiveMapping], quality: float, coherence: float) -> List[str]:
        """生成集成建议"""
        recommendations = []
        
        if quality < 0.7:
            recommendations.append("建议加强视角间的映射关系，提高集成质量")
        
        if coherence < 0.6:
            recommendations.append("建议改进语义一致性，统一术语使用")
        
        if len(mappings) < 3:
            recommendations.append("建议增加更多视角映射，提高系统完整性")
        
        # 检查特定的集成模式
        perspective_names = [m.source_perspective.value for m in mappings] + [m.target_perspective.value for m in mappings]
        if 'requirements_analyst' in perspective_names and 'test_engineer' not in perspective_names:
            recommendations.append("建议建立需求到测试的追溯关系")
        
        if not recommendations:
            recommendations.append("视角集成状态良好，建议继续保持当前质量水平")
        
        return recommendations
    
    # 集成策略辅助方法
    def _is_requirement_satisfied_by_architecture(self, requirement: Dict, architecture: Dict) -> bool:
        """判断需求是否被架构满足"""
        # 简化的判断逻辑
        req_keywords = set(self._extract_keywords(requirement.get('content', '')))
        arch_keywords = set(self._extract_keywords(architecture.get('content', '')))
        
        overlap = len(req_keywords & arch_keywords)
        return overlap > 0
    
    def _calculate_satisfaction_level(self, requirement: Dict, architecture_components: List[str]) -> float:
        """计算满足程度"""
        # 基于组件数量的简化计算
        return min(len(architecture_components) / 3.0, 1.0)
    
    def _does_architecture_enable_behavior(self, architecture: Dict, behavior: Dict) -> bool:
        """判断架构是否支持行为"""
        # 简化的判断逻辑
        return architecture.get('type', '') in ['component', 'interface'] and \
               behavior.get('type', '') in ['behavior', 'activity']
    
    def _calculate_interaction_complexity(self, architecture: Dict, behaviors: List[str]) -> float:
        """计算交互复杂度"""
        return min(len(behaviors) * 0.2, 1.0)
    
    def _does_test_verify_behavior(self, test: Dict, behavior: Dict) -> bool:
        """判断测试是否验证行为"""
        # 简化的判断逻辑
        test_content = test.get('content', '').lower()
        behavior_name = behavior.get('name', '').lower()
        
        return behavior_name in test_content or 'verify' in test_content
    
    def _calculate_test_coverage_level(self, behavior: Dict, tests: List[str]) -> float:
        """计算测试覆盖度"""
        return min(len(tests) / 2.0, 1.0)
    
    def _analyze_cross_perspective_consistency(self, perspectives: Dict) -> Dict:
        """分析跨视角一致性"""
        return {
            'consistency_score': 0.8,  # 简化计算
            'inconsistencies': [],
            'recommendations': ['保持当前一致性水平']
        }
    
    def _identify_integration_gaps(self, perspectives: Dict) -> Dict:
        """识别集成缺口"""
        return {
            'missing_connections': [],
            'weak_mappings': [],
            'improvement_areas': ['加强语义映射']
        } 