#!/usr/bin/env python3
"""
智能推荐引擎 - Intelligent Recommendation Engine

核心功能：
1. 个性化内容推荐
2. 业务模式识别和洞察生成
3. 异常检测和预警
4. 智能决策支持

基于任务5智能预加载系统的访问模式分析能力
作者: XML元数据系统开发团队
版本: v1.0.0
"""

import logging
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter
from datetime import datetime, timedelta
import json
from pathlib import Path

# 导入任务5的访问模式分析能力
try:
    from engines.loading.intelligent_preloader import (
        AccessPatternAnalyzer, AccessRecord, AccessType, 
        PredictionConfidence
    )
except ImportError:
    # 如果导入失败，提供基础类定义
    class AccessPatternAnalyzer:
        pass
    class AccessRecord:
        pass
    class AccessType:
        pass
    class PredictionConfidence:
        pass

# 在文件开头的导入部分添加语义分析组件
try:
    from intelligence.semantic.semantic_engine import IntelligentSemanticEngine
    from intelligence.knowledge.terminology_db import TerminologyDB
    from intelligence.semantic.domain_classifier import DomainClassifier
except ImportError:
    # 如果导入失败，提供基础类定义
    class IntelligentSemanticEngine:
        pass
    class TerminologyDB:
        pass
    class DomainClassifier:
        pass

# 设置日志
logger = logging.getLogger(__name__)

class RecommendationType(Enum):
    """推荐类型枚举"""
    CONTENT = "content"
    WORKFLOW = "workflow"
    OPTIMIZATION = "optimization"
    INSIGHT = "insight"
    ALERT = "alert"

class InsightType(Enum):
    """洞察类型枚举"""
    PATTERN = "pattern"
    TREND = "trend"
    ANOMALY = "anomaly"
    OPPORTUNITY = "opportunity"
    RISK = "risk"

class RecommendationStrategy(Enum):
    """推荐策略枚举"""
    PERFORMANCE_BASED = "performance_based"
    ACCURACY_BASED = "accuracy_based"
    HYBRID = "hybrid"
    DOMAIN_SPECIFIC = "domain_specific"

class AnomalyType(Enum):
    """异常类型枚举"""
    PERFORMANCE_DEGRADATION = "performance_degradation"
    UNUSUAL_ACCESS_PATTERN = "unusual_access_pattern"
    RESOURCE_OVERLOAD = "resource_overload"
    ERROR_SPIKE = "error_spike"
    SECURITY_ANOMALY = "security_anomaly"

@dataclass
class Recommendation:
    """推荐项"""
    recommendation_id: str
    type: RecommendationType
    title: str
    description: str
    confidence: float
    priority: int  # 1-10, 10最高
    target_resources: List[str]
    reasoning: List[str]
    expected_benefit: str
    implementation_effort: str  # low/medium/high
    created_at: datetime
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class BusinessInsight:
    """业务洞察"""
    insight_id: str
    type: InsightType
    title: str
    description: str
    confidence: float
    impact_level: str  # low/medium/high/critical
    affected_areas: List[str]
    data_sources: List[str]
    evidence: Dict[str, Any]
    recommendations: List[str]
    created_at: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class UserProfile:
    """用户画像"""
    user_id: str
    preferences: Dict[str, float]
    behavior_patterns: Dict[str, Any]
    expertise_level: str  # beginner/intermediate/advanced/expert
    active_domains: List[str]
    interaction_history: List[Dict[str, Any]]
    last_updated: datetime

@dataclass
class AnomalyAlert:
    """异常告警"""
    alert_id: str
    type: AnomalyType
    severity: str  # low/medium/high/critical
    title: str
    description: str
    detected_at: datetime
    confidence: float
    affected_resources: List[str]
    suggested_actions: List[str]
    metadata: Dict[str, Any]

@dataclass
class RecommendationConfig:
    """推荐配置"""
    strategy: RecommendationStrategy = RecommendationStrategy.HYBRID
    max_recommendations: int = 10
    confidence_threshold: float = 0.5
    enable_anomaly_detection: bool = True
    cache_ttl: int = 3600  # 缓存时间（秒）
    performance_weight: float = 0.6
    accuracy_weight: float = 0.4
    metadata: Dict[str, Any] = field(default_factory=dict)

class PersonalizedRecommender:
    """个性化推荐器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.user_profiles = {}
        self.content_features = {}
        self.recommendation_history = defaultdict(list)
        
        # 🆕 集成语义分析与术语体系
        try:
            self.semantic_engine = IntelligentSemanticEngine()
            self.terminology_db = TerminologyDB()
            self.domain_classifier = DomainClassifier()
            self.semantic_enabled = True
            self.logger.info("语义分析与术语体系集成成功")
        except Exception as e:
            self.logger.warning(f"语义分析集成失败，使用基础模式: {e}")
            self.semantic_engine = None
            self.terminology_db = None
            self.domain_classifier = None
            self.semantic_enabled = False
        
    def generate_content_recommendations(self, 
                                       user_id: str,
                                       current_context: Dict[str, Any],
                                       limit: int = 10) -> List[Recommendation]:
        """生成个性化内容推荐"""
        recommendations = []
        
        # 1. 获取或创建用户画像
        user_profile = self._get_or_create_user_profile(user_id, current_context)
        
        # 2. 基于协同过滤推荐
        collaborative_recs = self._collaborative_filtering_recommendations(
            user_profile, current_context
        )
        recommendations.extend(collaborative_recs)
        
        # 3. 基于内容的推荐
        content_based_recs = self._content_based_recommendations(
            user_profile, current_context
        )
        recommendations.extend(content_based_recs)
        
        # 4. 基于知识的推荐
        knowledge_based_recs = self._knowledge_based_recommendations(
            user_profile, current_context
        )
        recommendations.extend(knowledge_based_recs)
        
        # 5. 混合推荐和排序
        final_recommendations = self._hybrid_recommendation_ranking(
            recommendations, user_profile
        )
        
        return final_recommendations[:limit]
    
    def _get_or_create_user_profile(self, 
                                  user_id: str,
                                  context: Dict[str, Any]) -> UserProfile:
        """获取或创建用户画像"""
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = UserProfile(
                user_id=user_id,
                preferences={},
                behavior_patterns={},
                expertise_level="intermediate",
                active_domains=[],
                interaction_history=[],
                last_updated=datetime.now()
            )
        
        # 更新用户画像
        profile = self.user_profiles[user_id]
        self._update_user_profile(profile, context)
        
        return profile
    
    def _update_user_profile(self, profile: UserProfile, context: Dict[str, Any]):
        """更新用户画像"""
        # 更新交互历史
        interaction = {
            'timestamp': datetime.now(),
            'context': context,
            'action': context.get('action', 'view')
        }
        profile.interaction_history.append(interaction)
        
        # 保持历史记录在合理范围内
        if len(profile.interaction_history) > 1000:
            profile.interaction_history = profile.interaction_history[-1000:]
        
        # 更新偏好
        resource_type = context.get('resource_type')
        if resource_type:
            if resource_type not in profile.preferences:
                profile.preferences[resource_type] = 0.0
            profile.preferences[resource_type] += 0.1
        
        # 更新活跃领域
        domain = context.get('domain')
        if domain and domain not in profile.active_domains:
            profile.active_domains.append(domain)
        
        profile.last_updated = datetime.now()
    
    def _collaborative_filtering_recommendations(self,
                                               user_profile: UserProfile,
                                               context: Dict[str, Any]) -> List[Recommendation]:
        """协同过滤推荐"""
        recommendations = []
        
        # 找到相似用户
        similar_users = self._find_similar_users(user_profile)
        
        # 基于相似用户的偏好生成推荐
        for similar_user_id, similarity_score in similar_users[:5]:
            similar_profile = self.user_profiles.get(similar_user_id)
            if not similar_profile:
                continue
            
            # 推荐相似用户喜欢但当前用户未接触的内容
            for resource_type, preference_score in similar_profile.preferences.items():
                if resource_type not in user_profile.preferences:
                    confidence = similarity_score * preference_score
                    
                    recommendations.append(Recommendation(
                        recommendation_id=f"collab_{user_profile.user_id}_{resource_type}_{datetime.now().timestamp()}",
                        type=RecommendationType.CONTENT,
                        title=f"推荐{resource_type}类型内容",
                        description=f"基于与您相似的用户偏好，推荐{resource_type}相关内容",
                        confidence=confidence,
                        priority=int(confidence * 10),
                        target_resources=[resource_type],
                        reasoning=[f"相似用户偏好分析，相似度{similarity_score:.2f}"],
                        expected_benefit="发现新的感兴趣内容",
                        implementation_effort="low",
                        created_at=datetime.now()
                    ))
        
        return recommendations
    
    def _content_based_recommendations(self,
                                     user_profile: UserProfile,
                                     context: Dict[str, Any]) -> List[Recommendation]:
        """基于内容的推荐"""
        recommendations = []
        
        # 🆕 语义驱动的内容推荐
        if self.semantic_enabled and context.get('current_elements'):
            semantic_recommendations = self._semantic_content_recommendations(
                user_profile, context
            )
            recommendations.extend(semantic_recommendations)
        
        # 基于用户历史偏好推荐相似内容
        for resource_type, preference_score in user_profile.preferences.items():
            if preference_score > 0.5:  # 只推荐用户明显喜欢的类型
                # 查找相似内容
                similar_content = self._find_similar_content(resource_type, context)
                
                for content_id, similarity_score in similar_content[:3]:
                    confidence = preference_score * similarity_score
                    
                    recommendations.append(Recommendation(
                        recommendation_id=f"content_{user_profile.user_id}_{content_id}_{datetime.now().timestamp()}",
                        type=RecommendationType.CONTENT,
                        title=f"相关{resource_type}内容",
                        description=f"基于您对{resource_type}的兴趣，推荐相关内容",
                        confidence=confidence,
                        priority=int(confidence * 8),
                        target_resources=[content_id],
                        reasoning=[f"内容相似性分析，相似度{similarity_score:.2f}"],
                        expected_benefit="深入了解感兴趣的领域",
                        implementation_effort="low",
                        created_at=datetime.now()
                    ))
        
        return recommendations
    
    def _semantic_content_recommendations(self,
                                        user_profile: UserProfile,
                                        context: Dict[str, Any]) -> List[Recommendation]:
        """基于语义分析的内容推荐"""
        recommendations = []
        
        try:
            current_elements = context.get('current_elements', [])
            if not current_elements:
                return recommendations
            
            # 对当前元素进行语义分析
            semantic_results = []
            for element in current_elements[:5]:  # 限制分析数量
                if hasattr(element, 'tag') or isinstance(element, dict):
                    # 转换为ET.Element格式进行分析
                    if isinstance(element, dict):
                        # 模拟元素分析
                        semantic_result = {
                            'primary_category': element.get('type', 'unknown'),
                            'confidence': 0.8,
                            'business_domain': element.get('domain', 'general')
                        }
                    else:
                        semantic_result = self.semantic_engine.analyze_element_semantics(element)
                    
                    semantic_results.append(semantic_result)
            
            # 基于语义分析结果生成推荐
            for result in semantic_results:
                if hasattr(result, 'primary_category'):
                    category = result.primary_category
                    confidence = result.confidence
                    domain = getattr(result, 'business_domain', 'general')
                else:
                    category = result.get('primary_category', 'unknown')
                    confidence = result.get('confidence', 0.5)
                    domain = result.get('business_domain', 'general')
                
                if confidence > 0.6:  # 高置信度的语义分析结果
                    recommendations.append(Recommendation(
                        recommendation_id=f"semantic_{user_profile.user_id}_{category}_{datetime.now().timestamp()}",
                        type=RecommendationType.CONTENT,
                        title=f"语义相关的{category}内容",
                        description=f"基于语义分析，发现您正在处理{category}类型的内容，推荐相关资源",
                        confidence=confidence * 0.9,  # 语义推荐置信度调整
                        priority=int(confidence * 9),
                        target_resources=[f"{category}_resources", f"{domain}_examples"],
                        reasoning=[f"语义分析识别为{category}类型，置信度{confidence:.2f}", f"业务领域：{domain}"],
                        expected_benefit="发现语义相关的专业内容",
                        implementation_effort="low",
                        created_at=datetime.now(),
                        metadata={
                            'semantic_category': category,
                            'business_domain': domain,
                            'analysis_confidence': confidence
                        }
                    ))
            
            # 术语标准化建议
            if self.terminology_db and current_elements:
                terminology_recommendations = self._terminology_recommendations(
                    user_profile, current_elements
                )
                recommendations.extend(terminology_recommendations)
                
        except Exception as e:
            self.logger.error(f"语义内容推荐生成失败: {e}")
        
        return recommendations
    
    def _terminology_recommendations(self,
                                   user_profile: UserProfile,
                                   elements: List[Any]) -> List[Recommendation]:
        """基于术语标准化的推荐"""
        recommendations = []
        
        try:
            # 提取元素中的术语
            extracted_terms = []
            for element in elements[:3]:  # 限制分析数量
                if isinstance(element, dict):
                    # 从字典中提取术语
                    for key, value in element.items():
                        if isinstance(value, str) and len(value) > 2:
                            extracted_terms.append(value)
                elif hasattr(element, 'tag'):
                    # 从XML元素中提取术语
                    if element.text and len(element.text.strip()) > 2:
                        extracted_terms.append(element.text.strip())
                    for attr_value in element.attrib.values():
                        if len(attr_value) > 2:
                            extracted_terms.append(attr_value)
            
            # 检查术语标准化机会
            if extracted_terms:
                # 模拟术语标准化检查
                non_standard_terms = [term for term in extracted_terms if '_' in term or term.islower()]
                
                if non_standard_terms:
                    recommendations.append(Recommendation(
                        recommendation_id=f"terminology_{user_profile.user_id}_{datetime.now().timestamp()}",
                        type=RecommendationType.OPTIMIZATION,
                        title="术语标准化建议",
                        description=f"发现{len(non_standard_terms)}个可能需要标准化的术语，建议使用标准术语库进行规范化",
                        confidence=0.7,
                        priority=6,
                        target_resources=["terminology_standards", "naming_conventions"],
                        reasoning=[f"检测到{len(non_standard_terms)}个非标准术语", "术语标准化可提升模型质量"],
                        expected_benefit="提升模型术语一致性和可读性",
                        implementation_effort="medium",
                        created_at=datetime.now(),
                        metadata={
                            'non_standard_terms': non_standard_terms[:5],  # 最多显示5个
                            'total_terms_checked': len(extracted_terms)
                        }
                    ))
                    
        except Exception as e:
            self.logger.error(f"术语推荐生成失败: {e}")
        
        return recommendations
    
    def _knowledge_based_recommendations(self,
                                       user_profile: UserProfile,
                                       context: Dict[str, Any]) -> List[Recommendation]:
        """基于知识的推荐"""
        recommendations = []
        
        # 基于专业水平推荐
        expertise_level = user_profile.expertise_level
        
        if expertise_level == "beginner":
            recommendations.append(Recommendation(
                recommendation_id=f"knowledge_beginner_{user_profile.user_id}_{datetime.now().timestamp()}",
                type=RecommendationType.WORKFLOW,
                title="新手入门指南",
                description="为初学者准备的系统入门教程和最佳实践",
                confidence=0.9,
                priority=8,
                target_resources=["tutorial", "best_practices", "getting_started"],
                reasoning=["用户专业水平为初级"],
                expected_benefit="快速掌握系统基础功能",
                implementation_effort="medium",
                created_at=datetime.now()
            ))
        elif expertise_level == "intermediate":
            recommendations.append(Recommendation(
                recommendation_id=f"knowledge_intermediate_{user_profile.user_id}_{datetime.now().timestamp()}",
                type=RecommendationType.CONTENT,
                title="进阶功能探索",
                description="基于您的中级水平，推荐进阶功能和实用技巧",
                confidence=0.8,
                priority=7,
                target_resources=["advanced_tutorials", "tips_tricks", "workflow_optimization"],
                reasoning=["用户专业水平为中级"],
                expected_benefit="提升系统使用效率",
                implementation_effort="medium",
                created_at=datetime.now()
            ))
        elif expertise_level == "expert":
            recommendations.append(Recommendation(
                recommendation_id=f"knowledge_expert_{user_profile.user_id}_{datetime.now().timestamp()}",
                type=RecommendationType.OPTIMIZATION,
                title="高级优化建议",
                description="基于您的专业水平，推荐系统高级优化和定制功能",
                confidence=0.85,
                priority=7,
                target_resources=["advanced_features", "optimization", "customization"],
                reasoning=["用户专业水平为专家级"],
                expected_benefit="充分发挥系统高级功能",
                implementation_effort="high",
                created_at=datetime.now()
            ))
        
        # 基于当前上下文的通用推荐
        current_resource_type = context.get('resource_type')
        if current_resource_type:
            recommendations.append(Recommendation(
                recommendation_id=f"context_{user_profile.user_id}_{current_resource_type}_{datetime.now().timestamp()}",
                type=RecommendationType.CONTENT,
                title=f"相关{current_resource_type}资源",
                description=f"基于您当前正在使用的{current_resource_type}，推荐相关资源",
                confidence=0.7,
                priority=6,
                target_resources=[f"{current_resource_type}_related", f"{current_resource_type}_examples"],
                reasoning=[f"基于当前使用的{current_resource_type}类型"],
                expected_benefit="发现相关有用资源",
                implementation_effort="low",
                created_at=datetime.now()
            ))
        
        # 基于活跃领域的推荐
        if user_profile.active_domains:
            for domain in user_profile.active_domains[:2]:  # 最多推荐2个领域
                recommendations.append(Recommendation(
                    recommendation_id=f"domain_{user_profile.user_id}_{domain}_{datetime.now().timestamp()}",
                    type=RecommendationType.INSIGHT,
                    title=f"{domain}领域洞察",
                    description=f"基于您在{domain}领域的活动，推荐相关洞察和分析",
                    confidence=0.6,
                    priority=5,
                    target_resources=[f"{domain}_insights", f"{domain}_analysis"],
                    reasoning=[f"用户在{domain}领域活跃"],
                    expected_benefit="深入了解专业领域",
                    implementation_effort="low",
                    created_at=datetime.now()
                ))
        
        return recommendations
    
    def _find_similar_users(self, user_profile: UserProfile) -> List[Tuple[str, float]]:
        """查找相似用户"""
        similarities = []
        
        for other_user_id, other_profile in self.user_profiles.items():
            if other_user_id == user_profile.user_id:
                continue
            
            # 计算偏好相似度
            similarity = self._calculate_preference_similarity(
                user_profile.preferences, other_profile.preferences
            )
            
            if similarity > 0.3:  # 相似度阈值
                similarities.append((other_user_id, similarity))
        
        return sorted(similarities, key=lambda x: x[1], reverse=True)
    
    def _calculate_preference_similarity(self, 
                                       prefs1: Dict[str, float],
                                       prefs2: Dict[str, float]) -> float:
        """计算偏好相似度"""
        if not prefs1 or not prefs2:
            return 0.0
        
        # 使用余弦相似度
        common_items = set(prefs1.keys()) & set(prefs2.keys())
        if not common_items:
            return 0.0
        
        sum_xx = sum(prefs1[item] ** 2 for item in common_items)
        sum_yy = sum(prefs2[item] ** 2 for item in common_items)
        sum_xy = sum(prefs1[item] * prefs2[item] for item in common_items)
        
        if sum_xx == 0 or sum_yy == 0:
            return 0.0
        
        return sum_xy / (sum_xx ** 0.5 * sum_yy ** 0.5)
    
    def _find_similar_content(self, 
                            resource_type: str,
                            context: Dict[str, Any]) -> List[Tuple[str, float]]:
        """查找相似内容"""
        # 模拟内容相似度计算
        similar_content = [
            (f"{resource_type}_related_1", 0.8),
            (f"{resource_type}_related_2", 0.7),
            (f"{resource_type}_related_3", 0.6)
        ]
        return similar_content
    
    def _hybrid_recommendation_ranking(self,
                                     recommendations: List[Recommendation],
                                     user_profile: UserProfile) -> List[Recommendation]:
        """混合推荐排序"""
        # 去重
        unique_recs = {}
        for rec in recommendations:
            key = f"{rec.type.value}_{rec.title}"
            if key not in unique_recs or rec.confidence > unique_recs[key].confidence:
                unique_recs[key] = rec
        
        # 排序：优先级 * 置信度
        sorted_recs = sorted(
            unique_recs.values(),
            key=lambda x: x.priority * x.confidence,
            reverse=True
        )
        
        return sorted_recs

class BusinessInsightGenerator:
    """业务洞察生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.pattern_analyzer = None  # 将在初始化时设置
        self.insight_history = []
        self.anomaly_thresholds = {
            'access_frequency': 2.0,  # 标准差倍数
            'response_time': 1.5,
            'error_rate': 0.1
        }
    
    def set_pattern_analyzer(self, analyzer: AccessPatternAnalyzer):
        """设置访问模式分析器"""
        self.pattern_analyzer = analyzer
    
    def generate_business_insights(self, 
                                 data_context: Dict[str, Any],
                                 time_window_days: int = 7) -> List[BusinessInsight]:
        """生成业务洞察"""
        insights = []
        
        # 1. 模式识别洞察
        pattern_insights = self._identify_business_patterns(data_context, time_window_days)
        insights.extend(pattern_insights)
        
        # 2. 趋势分析洞察
        trend_insights = self._analyze_business_trends(data_context, time_window_days)
        insights.extend(trend_insights)
        
        # 3. 异常检测洞察
        anomaly_insights = self._detect_business_anomalies(data_context, time_window_days)
        insights.extend(anomaly_insights)
        
        # 4. 机会识别洞察
        opportunity_insights = self._identify_opportunities(data_context, time_window_days)
        insights.extend(opportunity_insights)
        
        # 5. 风险评估洞察
        risk_insights = self._assess_risks(data_context, time_window_days)
        insights.extend(risk_insights)
        
        return sorted(insights, key=lambda x: x.confidence, reverse=True)
    
    def _identify_business_patterns(self, 
                                  data_context: Dict[str, Any],
                                  time_window_days: int) -> List[BusinessInsight]:
        """识别业务模式"""
        insights = []
        
        # 模拟业务模式识别
        if self.pattern_analyzer and hasattr(self.pattern_analyzer, 'access_history'):
            recent_accesses = [
                record for record in self.pattern_analyzer.access_history
                if (datetime.now() - record.timestamp).days <= time_window_days
            ]
            
            if recent_accesses:
                # 分析访问模式
                user_counts = Counter(record.user_id for record in recent_accesses)
                resource_counts = Counter(record.resource_id for record in recent_accesses)
                
                # 识别高频用户模式
                if user_counts:
                    top_user, top_count = user_counts.most_common(1)[0]
                    if top_count > len(recent_accesses) * 0.3:  # 超过30%的访问
                        insights.append(BusinessInsight(
                            insight_id=f"pattern_high_user_{datetime.now().timestamp()}",
                            type=InsightType.PATTERN,
                            title="发现高频用户模式",
                            description=f"用户{top_user}在过去{time_window_days}天内产生了{top_count}次访问，占总访问量的{top_count/len(recent_accesses)*100:.1f}%",
                            confidence=0.85,
                            impact_level="medium",
                            affected_areas=["user_behavior", "system_load"],
                            data_sources=["access_logs"],
                            evidence={"user_id": top_user, "access_count": top_count, "percentage": top_count/len(recent_accesses)},
                            recommendations=["考虑为高频用户提供专门的优化", "分析用户需求模式"],
                            created_at=datetime.now()
                        ))
                
                # 识别热门资源模式
                if resource_counts:
                    top_resource, top_count = resource_counts.most_common(1)[0]
                    if top_count > len(recent_accesses) * 0.2:  # 超过20%的访问
                        insights.append(BusinessInsight(
                            insight_id=f"pattern_hot_resource_{datetime.now().timestamp()}",
                            type=InsightType.PATTERN,
                            title="发现热门资源模式",
                            description=f"资源{top_resource}在过去{time_window_days}天内被访问{top_count}次，是最受欢迎的资源",
                            confidence=0.9,
                            impact_level="high",
                            affected_areas=["resource_optimization", "caching_strategy"],
                            data_sources=["access_logs"],
                            evidence={"resource_id": top_resource, "access_count": top_count},
                            recommendations=["优化热门资源的缓存策略", "考虑资源预加载"],
                            created_at=datetime.now()
                        ))
        
        return insights
    
    def _analyze_business_trends(self,
                               data_context: Dict[str, Any],
                               time_window_days: int) -> List[BusinessInsight]:
        """分析业务趋势"""
        insights = []
        
        # 模拟趋势分析
        if self.pattern_analyzer and hasattr(self.pattern_analyzer, 'access_history'):
            recent_accesses = [
                record for record in self.pattern_analyzer.access_history
                if (datetime.now() - record.timestamp).days <= time_window_days
            ]
            
            if len(recent_accesses) > 10:
                # 分析访问量趋势
                daily_counts = defaultdict(int)
                for record in recent_accesses:
                    day_key = record.timestamp.strftime('%Y-%m-%d')
                    daily_counts[day_key] += 1
                
                if len(daily_counts) >= 3:
                    counts = list(daily_counts.values())
                    # 简单的趋势检测
                    if len(counts) >= 2:
                        recent_avg = np.mean(counts[-3:]) if len(counts) >= 3 else counts[-1]
                        earlier_avg = np.mean(counts[:-3]) if len(counts) >= 6 else counts[0]
                        
                        if recent_avg > earlier_avg * 1.2:  # 增长超过20%
                            insights.append(BusinessInsight(
                                insight_id=f"trend_increasing_{datetime.now().timestamp()}",
                                type=InsightType.TREND,
                                title="系统使用量呈上升趋势",
                                description=f"过去{time_window_days}天内，系统访问量呈现明显上升趋势，近期日均访问量比早期增长{(recent_avg/earlier_avg-1)*100:.1f}%",
                                confidence=0.8,
                                impact_level="high",
                                affected_areas=["system_capacity", "performance"],
                                data_sources=["access_logs"],
                                evidence={"recent_avg": recent_avg, "earlier_avg": earlier_avg, "growth_rate": recent_avg/earlier_avg-1},
                                recommendations=["考虑扩展系统容量", "优化性能瓶颈"],
                                created_at=datetime.now()
                            ))
        
        return insights
    
    def _detect_business_anomalies(self,
                                 data_context: Dict[str, Any],
                                 time_window_days: int) -> List[BusinessInsight]:
        """检测业务异常"""
        insights = []
        
        # 模拟异常检测
        if self.pattern_analyzer and hasattr(self.pattern_analyzer, 'access_history'):
            recent_accesses = [
                record for record in self.pattern_analyzer.access_history
                if (datetime.now() - record.timestamp).days <= time_window_days
            ]
            
            if recent_accesses:
                # 检测错误率异常
                error_count = sum(1 for record in recent_accesses if not record.success)
                error_rate = error_count / len(recent_accesses)
                
                if error_rate > self.anomaly_thresholds['error_rate']:
                    insights.append(BusinessInsight(
                        insight_id=f"anomaly_error_rate_{datetime.now().timestamp()}",
                        type=InsightType.ANOMALY,
                        title="检测到异常错误率",
                        description=f"过去{time_window_days}天内错误率为{error_rate*100:.1f}%，超过正常阈值{self.anomaly_thresholds['error_rate']*100:.1f}%",
                        confidence=0.95,
                        impact_level="critical",
                        affected_areas=["system_reliability", "user_experience"],
                        data_sources=["access_logs", "error_logs"],
                        evidence={"error_rate": error_rate, "error_count": error_count, "total_accesses": len(recent_accesses)},
                        recommendations=["立即调查错误原因", "检查系统健康状态", "通知运维团队"],
                        created_at=datetime.now()
                    ))
        
        return insights
    
    def _identify_opportunities(self,
                              data_context: Dict[str, Any],
                              time_window_days: int) -> List[BusinessInsight]:
        """识别机会"""
        insights = []
        
        # 模拟机会识别
        insights.append(BusinessInsight(
            insight_id=f"opportunity_optimization_{datetime.now().timestamp()}",
            type=InsightType.OPPORTUNITY,
            title="系统优化机会",
            description="基于访问模式分析，发现了多个系统优化机会",
            confidence=0.7,
            impact_level="medium",
            affected_areas=["performance", "user_experience"],
            data_sources=["access_patterns", "performance_metrics"],
            evidence={"optimization_potential": "high"},
            recommendations=["实施智能缓存策略", "优化热门资源加载", "改进用户界面"],
            created_at=datetime.now()
        ))
        
        return insights
    
    def _assess_risks(self,
                    data_context: Dict[str, Any],
                    time_window_days: int) -> List[BusinessInsight]:
        """评估风险"""
        insights = []
        
        # 模拟风险评估
        if self.pattern_analyzer and hasattr(self.pattern_analyzer, 'user_profiles'):
            if len(self.pattern_analyzer.user_profiles) < 5:
                insights.append(BusinessInsight(
                    insight_id=f"risk_low_adoption_{datetime.now().timestamp()}",
                    type=InsightType.RISK,
                    title="用户采用率风险",
                    description=f"当前活跃用户数量较少（{len(self.pattern_analyzer.user_profiles)}个），可能存在用户采用率风险",
                    confidence=0.6,
                    impact_level="medium",
                    affected_areas=["user_adoption", "business_growth"],
                    data_sources=["user_profiles"],
                    evidence={"active_users": len(self.pattern_analyzer.user_profiles)},
                    recommendations=["加强用户推广", "改进用户体验", "提供更多培训资源"],
                    created_at=datetime.now()
                ))
        
        return insights

class IntelligentRecommendationEngine:
    """智能推荐引擎主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or {}
        
        # 初始化组件
        self.personalizer = PersonalizedRecommender()
        self.insight_generator = BusinessInsightGenerator()
        
        # 尝试集成任务5的访问模式分析器
        try:
            self.pattern_analyzer = AccessPatternAnalyzer()
            self.insight_generator.set_pattern_analyzer(self.pattern_analyzer)
        except Exception as e:
            self.logger.warning(f"无法初始化访问模式分析器: {e}")
            self.pattern_analyzer = None
        
        # 推荐历史和统计
        self.recommendation_history = []
        self.performance_stats = {
            'total_recommendations': 0,
            'accepted_recommendations': 0,
            'insights_generated': 0,
            'anomalies_detected': 0
        }
        
        self.recommendations_cache = {}
        self.performance_metrics = {}
        self.anomaly_alerts = []
        self.strategy = RecommendationStrategy.HYBRID
        
        self.logger.info("智能推荐引擎初始化完成")
    
    def get_personalized_recommendations(self,
                                       user_id: str,
                                       context: Dict[str, Any],
                                       limit: int = 10) -> List[Recommendation]:
        """获取个性化推荐"""
        try:
            recommendations = self.personalizer.generate_content_recommendations(
                user_id, context, limit
            )
            
            # 记录推荐历史
            self.recommendation_history.extend(recommendations)
            self.performance_stats['total_recommendations'] += len(recommendations)
            
            self.logger.info(f"为用户{user_id}生成了{len(recommendations)}个推荐")
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成个性化推荐失败: {e}")
            return []
    
    def get_business_insights(self,
                            context: Dict[str, Any],
                            time_window_days: int = 7) -> List[BusinessInsight]:
        """获取业务洞察"""
        try:
            insights = self.insight_generator.generate_business_insights(
                context, time_window_days
            )
            
            self.performance_stats['insights_generated'] += len(insights)
            
            # 统计异常数量
            anomalies = [i for i in insights if i.type == InsightType.ANOMALY]
            self.performance_stats['anomalies_detected'] += len(anomalies)
            
            self.logger.info(f"生成了{len(insights)}个业务洞察，包含{len(anomalies)}个异常")
            return insights
            
        except Exception as e:
            self.logger.error(f"生成业务洞察失败: {e}")
            return []
    
    def record_recommendation_feedback(self,
                                     recommendation_id: str,
                                     feedback: str,
                                     user_id: str):
        """记录推荐反馈"""
        if feedback in ['accepted', 'implemented']:
            self.performance_stats['accepted_recommendations'] += 1
        
        # 更新用户画像
        if user_id in self.personalizer.user_profiles:
            profile = self.personalizer.user_profiles[user_id]
            feedback_entry = {
                'recommendation_id': recommendation_id,
                'feedback': feedback,
                'timestamp': datetime.now()
            }
            profile.interaction_history.append(feedback_entry)
        
        self.logger.info(f"记录推荐反馈: {recommendation_id} -> {feedback}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        total_recs = self.performance_stats['total_recommendations']
        accepted_recs = self.performance_stats['accepted_recommendations']
        
        report = {
            'recommendation_stats': {
                'total_recommendations': total_recs,
                'accepted_recommendations': accepted_recs,
                'acceptance_rate': accepted_recs / max(total_recs, 1),
                'active_users': len(self.personalizer.user_profiles)
            },
            'insight_stats': {
                'insights_generated': self.performance_stats['insights_generated'],
                'anomalies_detected': self.performance_stats['anomalies_detected']
            },
            'system_health': {
                'pattern_analyzer_active': self.pattern_analyzer is not None,
                'recommendation_engine_status': 'active'
            }
        }
        
        return report
    
    def set_strategy(self, strategy: RecommendationStrategy):
        """设置推荐策略"""
        self.strategy = strategy
        self.logger.info(f"推荐策略设置为: {strategy.value}")
    
    def recommend(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成推荐"""
        context_type = context.get('type', 'general')
        
        if self.strategy == RecommendationStrategy.PERFORMANCE_BASED:
            return self._performance_based_recommendation(context)
        elif self.strategy == RecommendationStrategy.ACCURACY_BASED:
            return self._accuracy_based_recommendation(context)
        elif self.strategy == RecommendationStrategy.DOMAIN_SPECIFIC:
            return self._domain_specific_recommendation(context)
        else:  # HYBRID
            return self._hybrid_recommendation(context)
    
    def detect_anomalies(self, metrics: Dict[str, Any]) -> List[AnomalyAlert]:
        """检测异常"""
        alerts = []
        
        # 性能异常检测
        if 'response_time' in metrics:
            response_time = metrics['response_time']
            if response_time > 5.0:  # 响应时间超过5秒
                alerts.append(AnomalyAlert(
                    alert_id=f"perf_anomaly_{datetime.now().timestamp()}",
                    type=AnomalyType.PERFORMANCE_DEGRADATION,
                    severity="high",
                    title="性能异常检测",
                    description=f"响应时间异常：{response_time:.2f}秒，超过正常阈值",
                    detected_at=datetime.now(),
                    confidence=0.9,
                    affected_resources=["system_performance"],
                    suggested_actions=["检查系统负载", "优化查询性能", "扩展系统资源"],
                    metadata={"response_time": response_time, "threshold": 5.0}
                ))
        
        # 错误率异常检测
        if 'error_rate' in metrics:
            error_rate = metrics['error_rate']
            if error_rate > 0.1:  # 错误率超过10%
                alerts.append(AnomalyAlert(
                    alert_id=f"error_anomaly_{datetime.now().timestamp()}",
                    type=AnomalyType.ERROR_SPIKE,
                    severity="critical",
                    title="错误率异常",
                    description=f"错误率异常：{error_rate*100:.1f}%，超过正常阈值",
                    detected_at=datetime.now(),
                    confidence=0.95,
                    affected_resources=["system_reliability"],
                    suggested_actions=["立即检查错误日志", "排查故障原因", "通知运维团队"],
                    metadata={"error_rate": error_rate, "threshold": 0.1}
                ))
        
        # 资源过载检测
        if 'cpu_usage' in metrics:
            cpu_usage = metrics['cpu_usage']
            if cpu_usage > 0.9:  # CPU使用率超过90%
                alerts.append(AnomalyAlert(
                    alert_id=f"resource_anomaly_{datetime.now().timestamp()}",
                    type=AnomalyType.RESOURCE_OVERLOAD,
                    severity="high",
                    title="资源过载异常",
                    description=f"CPU使用率异常：{cpu_usage*100:.1f}%，系统负载过高",
                    detected_at=datetime.now(),
                    confidence=0.85,
                    affected_resources=["compute_resources"],
                    suggested_actions=["监控系统负载", "考虑扩容", "优化资源分配"],
                    metadata={"cpu_usage": cpu_usage, "threshold": 0.9}
                ))
        
        self.anomaly_alerts.extend(alerts)
        return alerts
    
    def _performance_based_recommendation(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于性能的推荐"""
        return [
            {"item": "high_performance_tool", "confidence": 0.9, "type": "performance"},
            {"item": "optimized_workflow", "confidence": 0.8, "type": "performance"}
        ]
    
    def _accuracy_based_recommendation(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于精度的推荐"""
        return [
            {"item": "high_accuracy_method", "confidence": 0.95, "type": "accuracy"},
            {"item": "validated_algorithm", "confidence": 0.85, "type": "accuracy"}
        ]
    
    def _domain_specific_recommendation(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """领域特定推荐"""
        domain = context.get('domain', 'general')
        if domain == 'biomedical':
            return [
                {"item": "biomedical_specific_tool", "confidence": 0.9, "type": "biomedical"},
                {"item": "medical_workflow", "confidence": 0.8, "type": "biomedical"}
            ]
        return self._hybrid_recommendation(context)
    
    def _hybrid_recommendation(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """混合推荐策略"""
        perf_recs = self._performance_based_recommendation(context)
        acc_recs = self._accuracy_based_recommendation(context)
        
        # 合并并重新评分
        hybrid_recs = []
        for rec in perf_recs + acc_recs:
            rec['confidence'] *= 0.9  # 混合策略的置信度调整
            hybrid_recs.append(rec)
        
        return sorted(hybrid_recs, key=lambda x: x['confidence'], reverse=True)
    
    def update_performance_metrics(self, item: str, performance: float):
        """更新性能指标"""
        self.performance_metrics[item] = performance
        self.logger.info(f"更新性能指标: {item} = {performance}")
    
    def get_recommendation_stats(self) -> Dict[str, Any]:
        """获取推荐统计信息"""
        return {
            "current_strategy": self.strategy.value,
            "cache_size": len(self.recommendations_cache),
            "performance_metrics_count": len(self.performance_metrics),
            "anomaly_alerts_count": len(self.anomaly_alerts),
            "available_strategies": [s.value for s in RecommendationStrategy]
        }
    
    def get_recent_anomalies(self, hours: int = 24) -> List[AnomalyAlert]:
        """获取最近的异常告警"""
        cutoff_time = datetime.now() - datetime.timedelta(hours=hours)
        return [
            alert for alert in self.anomaly_alerts
            if alert.detected_at >= cutoff_time
        ]
    
    def clear_old_anomalies(self, days: int = 7):
        """清理旧的异常告警"""
        cutoff_time = datetime.now() - datetime.timedelta(days=days)
        self.anomaly_alerts = [
            alert for alert in self.anomaly_alerts
            if alert.detected_at >= cutoff_time
        ]

# 导出主要类
__all__ = [
    'IntelligentRecommendationEngine',
    'PersonalizedRecommender', 
    'BusinessInsightGenerator',
    'Recommendation',
    'BusinessInsight',
    'UserProfile',
    'RecommendationType',
    'InsightType',
    'AnomalyAlert'
] 