#!/usr/bin/env python3
"""
智能预加载系统 - Intelligent Preloader

核心功能：
1. 访问模式分析和学习
2. 预测性数据加载
3. 智能缓存策略优化
4. 与snapshots系统集成

作者: XML元数据系统开发团队
版本: v1.0.0
"""

import logging
import asyncio
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter, deque
from datetime import datetime, timedelta
import json
import pickle
from pathlib import Path

# 设置日志
logger = logging.getLogger(__name__)

class AccessType(Enum):
    """访问类型枚举"""
    READ = "read"
    WRITE = "write"
    ANALYZE = "analyze"
    EXPORT = "export"
    SEARCH = "search"

class PredictionConfidence(Enum):
    """预测置信度级别"""
    LOW = 0.3
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.9

@dataclass
class AccessRecord:
    """访问记录"""
    timestamp: datetime
    user_id: str
    resource_id: str
    access_type: AccessType
    context: Dict[str, Any]
    session_id: str
    duration: float = 0.0
    success: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AccessPattern:
    """访问模式"""
    pattern_id: str
    user_patterns: Dict[str, List[str]]  # 用户访问序列模式
    temporal_patterns: Dict[str, float]  # 时间模式
    contextual_patterns: Dict[str, Any]  # 上下文模式
    frequency: int
    confidence: float
    last_updated: datetime

@dataclass
class PreloadPrediction:
    """预加载预测"""
    resource_id: str
    probability: float
    confidence: PredictionConfidence
    predicted_access_time: datetime
    context_factors: Dict[str, float]
    reasoning: List[str]

@dataclass
class CacheStrategy:
    """缓存策略"""
    strategy_id: str
    resource_priorities: Dict[str, float]
    eviction_policy: str
    cache_size_limit: int
    ttl_settings: Dict[str, int]
    performance_metrics: Dict[str, float]

class AccessPatternAnalyzer:
    """访问模式分析器"""
    
    def __init__(self, history_window_days: int = 30):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.history_window_days = history_window_days
        self.access_history = deque(maxlen=10000)  # 保持最近10000条记录
        self.patterns = {}
        self.user_profiles = {}
        
    def record_access(self, access_record: AccessRecord):
        """记录访问行为"""
        self.access_history.append(access_record)
        self._update_user_profile(access_record)
        
    def analyze_user_access_patterns(self, user_id: str = None) -> Dict[str, AccessPattern]:
        """分析用户访问模式"""
        if user_id:
            user_records = [r for r in self.access_history if r.user_id == user_id]
        else:
            user_records = list(self.access_history)
            
        patterns = {}
        
        # 1. 序列模式分析
        sequence_patterns = self._analyze_sequence_patterns(user_records)
        
        # 2. 时间模式分析
        temporal_patterns = self._analyze_temporal_patterns(user_records)
        
        # 3. 上下文模式分析
        contextual_patterns = self._analyze_contextual_patterns(user_records)
        
        # 4. 组合模式生成
        for pattern_type in ['sequence', 'temporal', 'contextual']:
            pattern_id = f"{user_id or 'global'}_{pattern_type}"
            patterns[pattern_id] = AccessPattern(
                pattern_id=pattern_id,
                user_patterns=sequence_patterns,
                temporal_patterns=temporal_patterns,
                contextual_patterns=contextual_patterns,
                frequency=len(user_records),
                confidence=self._calculate_pattern_confidence(user_records),
                last_updated=datetime.now()
            )
            
        return patterns
    
    def _analyze_sequence_patterns(self, records: List[AccessRecord]) -> Dict[str, List[str]]:
        """分析访问序列模式"""
        sequences = defaultdict(list)
        
        # 按会话分组
        session_groups = defaultdict(list)
        for record in records:
            session_groups[record.session_id].append(record)
        
        # 分析每个会话的访问序列
        for session_id, session_records in session_groups.items():
            # 按时间排序
            session_records.sort(key=lambda x: x.timestamp)
            
            # 提取资源访问序列
            resource_sequence = [r.resource_id for r in session_records]
            
            # 生成n-gram模式
            for n in range(2, min(6, len(resource_sequence) + 1)):
                for i in range(len(resource_sequence) - n + 1):
                    ngram = tuple(resource_sequence[i:i+n])
                    sequences[f"{n}gram"].append(list(ngram))
        
        return dict(sequences)
    
    def _analyze_temporal_patterns(self, records: List[AccessRecord]) -> Dict[str, float]:
        """分析时间模式"""
        temporal_patterns = {}
        
        if not records:
            return temporal_patterns
        
        # 按小时统计访问频率
        hour_counts = Counter(r.timestamp.hour for r in records)
        total_records = len(records)
        
        for hour, count in hour_counts.items():
            temporal_patterns[f"hour_{hour}"] = count / total_records
        
        # 按星期几统计
        weekday_counts = Counter(r.timestamp.weekday() for r in records)
        for weekday, count in weekday_counts.items():
            temporal_patterns[f"weekday_{weekday}"] = count / total_records
        
        # 计算访问间隔模式
        if len(records) > 1:
            intervals = []
            sorted_records = sorted(records, key=lambda x: x.timestamp)
            for i in range(1, len(sorted_records)):
                interval = (sorted_records[i].timestamp - sorted_records[i-1].timestamp).total_seconds()
                intervals.append(interval)
            
            if intervals:
                temporal_patterns['avg_interval'] = float(np.mean(intervals))
                temporal_patterns['std_interval'] = float(np.std(intervals))
        
        return temporal_patterns
    
    def _analyze_contextual_patterns(self, records: List[AccessRecord]) -> Dict[str, Any]:
        """分析上下文模式"""
        contextual_patterns = {}
        
        # 访问类型分布
        access_type_counts = Counter(r.access_type for r in records)
        total_records = len(records)
        
        for access_type, count in access_type_counts.items():
            contextual_patterns[f"access_type_{access_type.value}"] = count / total_records
        
        # 成功率统计
        success_rate = sum(1 for r in records if r.success) / total_records if records else 0
        contextual_patterns['success_rate'] = success_rate
        
        # 平均持续时间
        durations = [r.duration for r in records if r.duration > 0]
        if durations:
            contextual_patterns['avg_duration'] = np.mean(durations)
            contextual_patterns['std_duration'] = np.std(durations)
        
        return contextual_patterns
    
    def _calculate_pattern_confidence(self, records: List[AccessRecord]) -> float:
        """计算模式置信度"""
        if len(records) < 5:
            return 0.3  # 数据太少，置信度低
        
        # 基于数据量和一致性计算置信度
        data_volume_factor = min(len(records) / 100, 1.0)
        
        # 计算访问一致性（相同资源的重复访问）
        resource_counts = Counter(r.resource_id for r in records)
        consistency_factor = len([c for c in resource_counts.values() if c > 1]) / len(resource_counts)
        
        return (data_volume_factor + consistency_factor) / 2
    
    def _update_user_profile(self, access_record: AccessRecord):
        """更新用户画像"""
        user_id = access_record.user_id
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = {
                'total_accesses': 0,
                'favorite_resources': Counter(),
                'preferred_access_types': Counter(),
                'active_hours': Counter(),
                'last_access': None
            }
        
        profile = self.user_profiles[user_id]
        profile['total_accesses'] += 1
        profile['favorite_resources'][access_record.resource_id] += 1
        profile['preferred_access_types'][access_record.access_type] += 1
        profile['active_hours'][access_record.timestamp.hour] += 1
        profile['last_access'] = access_record.timestamp

class PredictiveLoader:
    """预测性加载器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.pattern_analyzer = AccessPatternAnalyzer()
        self.ml_models = {}
        self.prediction_cache = {}
        
    def predict_next_access(self, 
                          current_context: Dict[str, Any],
                          user_profile: Dict[str, Any]) -> List[PreloadPrediction]:
        """预测下一步访问"""
        predictions = []
        
        # 1. 基于序列模式预测
        sequence_predictions = self._predict_from_sequences(current_context, user_profile)
        predictions.extend(sequence_predictions)
        
        # 2. 基于时间模式预测
        temporal_predictions = self._predict_from_temporal_patterns(current_context, user_profile)
        predictions.extend(temporal_predictions)
        
        # 3. 基于用户偏好预测
        preference_predictions = self._predict_from_user_preferences(current_context, user_profile)
        predictions.extend(preference_predictions)
        
        # 4. 去重和排序
        predictions = self._deduplicate_and_rank_predictions(predictions)
        
        return predictions[:10]  # 返回前10个预测
    
    def _predict_from_sequences(self, 
                              current_context: Dict[str, Any],
                              user_profile: Dict[str, Any]) -> List[PreloadPrediction]:
        """基于序列模式预测"""
        predictions = []
        
        current_resource = current_context.get('current_resource')
        if not current_resource:
            return predictions
        
        # 获取用户的访问模式
        user_id = current_context.get('user_id')
        patterns = self.pattern_analyzer.analyze_user_access_patterns(user_id)
        
        for pattern in patterns.values():
            # 查找包含当前资源的序列
            for ngram_type, sequences in pattern.user_patterns.items():
                for sequence in sequences:
                    if current_resource in sequence:
                        # 找到当前资源在序列中的位置
                        for i, resource in enumerate(sequence):
                            if resource == current_resource and i < len(sequence) - 1:
                                next_resource = sequence[i + 1]
                                probability = pattern.confidence * 0.8  # 序列预测权重
                                
                                predictions.append(PreloadPrediction(
                                    resource_id=next_resource,
                                    probability=probability,
                                    confidence=self._probability_to_confidence(probability),
                                    predicted_access_time=datetime.now() + timedelta(minutes=5),
                                    context_factors={'sequence_match': 1.0},
                                    reasoning=[f"基于{ngram_type}序列模式预测"]
                                ))
        
        return predictions
    
    def _predict_from_temporal_patterns(self,
                                      current_context: Dict[str, Any],
                                      user_profile: Dict[str, Any]) -> List[PreloadPrediction]:
        """基于时间模式预测"""
        predictions = []
        
        current_time = datetime.now()
        current_hour = current_time.hour
        current_weekday = current_time.weekday()
        
        # 获取用户在当前时间段的访问偏好
        user_id = current_context.get('user_id')
        if user_id in self.pattern_analyzer.user_profiles:
            profile = self.pattern_analyzer.user_profiles[user_id]
            
            # 基于活跃时间预测
            if current_hour in profile['active_hours']:
                hour_activity = profile['active_hours'][current_hour]
                total_activity = sum(profile['active_hours'].values())
                hour_probability = hour_activity / total_activity if total_activity > 0 else 0
                
                # 预测用户喜欢的资源
                for resource, count in profile['favorite_resources'].most_common(5):
                    probability = hour_probability * (count / profile['total_accesses'])
                    
                    predictions.append(PreloadPrediction(
                        resource_id=resource,
                        probability=probability,
                        confidence=self._probability_to_confidence(probability),
                        predicted_access_time=current_time + timedelta(minutes=10),
                        context_factors={'temporal_match': hour_probability},
                        reasoning=[f"基于{current_hour}点时间模式预测"]
                    ))
        
        return predictions
    
    def _predict_from_user_preferences(self,
                                     current_context: Dict[str, Any],
                                     user_profile: Dict[str, Any]) -> List[PreloadPrediction]:
        """基于用户偏好预测"""
        predictions = []
        
        user_id = current_context.get('user_id')
        if user_id not in self.pattern_analyzer.user_profiles:
            return predictions
        
        profile = self.pattern_analyzer.user_profiles[user_id]
        
        # 基于最常访问的资源预测
        favorite_resources = profile['favorite_resources']
        if hasattr(favorite_resources, 'most_common'):
            # 如果是Counter对象
            top_resources = favorite_resources.most_common(3)
        else:
            # 如果是普通字典，手动排序
            sorted_items = sorted(favorite_resources.items(), key=lambda x: x[1], reverse=True)
            top_resources = sorted_items[:3]
        
        for resource, count in top_resources:
            probability = count / profile['total_accesses']
            
            # 如果最近没有访问过，提高预测概率
            recent_threshold = datetime.now() - timedelta(hours=2)
            recent_accesses = [r for r in self.pattern_analyzer.access_history 
                             if r.user_id == user_id and r.resource_id == resource 
                             and r.timestamp > recent_threshold]
            
            if not recent_accesses:
                probability *= 1.5  # 提高未最近访问资源的预测概率
            
            predictions.append(PreloadPrediction(
                resource_id=resource,
                probability=min(probability, 1.0),
                confidence=self._probability_to_confidence(probability),
                predicted_access_time=datetime.now() + timedelta(minutes=15),
                context_factors={'preference_score': probability},
                reasoning=[f"基于用户偏好预测，访问频率{count}次"]
            ))
        
        return predictions
    
    def _deduplicate_and_rank_predictions(self, 
                                        predictions: List[PreloadPrediction]) -> List[PreloadPrediction]:
        """去重和排序预测结果"""
        # 按资源ID去重，保留概率最高的
        resource_predictions = {}
        for pred in predictions:
            if pred.resource_id not in resource_predictions or \
               pred.probability > resource_predictions[pred.resource_id].probability:
                resource_predictions[pred.resource_id] = pred
        
        # 按概率排序
        sorted_predictions = sorted(resource_predictions.values(), 
                                  key=lambda x: x.probability, reverse=True)
        
        return sorted_predictions
    
    def _probability_to_confidence(self, probability: float) -> PredictionConfidence:
        """将概率转换为置信度级别"""
        if probability >= 0.9:
            return PredictionConfidence.VERY_HIGH
        elif probability >= 0.7:
            return PredictionConfidence.HIGH
        elif probability >= 0.5:
            return PredictionConfidence.MEDIUM
        else:
            return PredictionConfidence.LOW

class IntelligentCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, max_cache_size: int = 1000):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.max_cache_size = max_cache_size
        self.cache = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'preload_hits': 0
        }
        self.access_frequencies = Counter()
        self.last_access_times = {}
        
    def optimize_cache_strategy(self, 
                              access_patterns: Dict[str, AccessPattern],
                              predictions: List[PreloadPrediction],
                              system_resources: Dict[str, Any]) -> CacheStrategy:
        """优化缓存策略"""
        
        # 1. 计算资源优先级
        resource_priorities = self._calculate_resource_priorities(access_patterns, predictions)
        
        # 2. 确定驱逐策略
        eviction_policy = self._determine_eviction_policy(system_resources)
        
        # 3. 设置TTL
        ttl_settings = self._calculate_ttl_settings(access_patterns)
        
        # 4. 调整缓存大小
        optimal_cache_size = self._calculate_optimal_cache_size(system_resources)
        
        strategy = CacheStrategy(
            strategy_id=f"strategy_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            resource_priorities=resource_priorities,
            eviction_policy=eviction_policy,
            cache_size_limit=optimal_cache_size,
            ttl_settings=ttl_settings,
            performance_metrics=self._calculate_performance_metrics()
        )
        
        return strategy
    
    def preload_resources(self, predictions: List[PreloadPrediction]) -> Dict[str, bool]:
        """预加载资源"""
        preload_results = {}
        
        for prediction in predictions:
            if prediction.confidence.value >= PredictionConfidence.MEDIUM.value:
                success = self._preload_resource(prediction.resource_id, prediction)
                preload_results[prediction.resource_id] = success
                
                if success:
                    self.logger.info(f"预加载成功: {prediction.resource_id}, "
                                   f"概率: {prediction.probability:.3f}")
        
        return preload_results
    
    def get_cached_resource(self, resource_id: str) -> Optional[Any]:
        """获取缓存资源"""
        if resource_id in self.cache:
            self.cache_stats['hits'] += 1
            self.access_frequencies[resource_id] += 1
            self.last_access_times[resource_id] = datetime.now()
            
            # 检查是否为预加载命中
            if hasattr(self.cache[resource_id], '_preloaded'):
                self.cache_stats['preload_hits'] += 1
            
            return self.cache[resource_id]
        else:
            self.cache_stats['misses'] += 1
            return None
    
    def put_cached_resource(self, resource_id: str, resource_data: Any, is_preloaded: bool = False):
        """放入缓存资源"""
        # 检查缓存空间
        if len(self.cache) >= self.max_cache_size:
            self._evict_resources()
        
        # 标记预加载资源
        if is_preloaded:
            resource_data._preloaded = True
        
        self.cache[resource_id] = resource_data
        self.access_frequencies[resource_id] += 1
        self.last_access_times[resource_id] = datetime.now()
    
    def _calculate_resource_priorities(self, 
                                     access_patterns: Dict[str, AccessPattern],
                                     predictions: List[PreloadPrediction]) -> Dict[str, float]:
        """计算资源优先级"""
        priorities = {}
        
        # 基于访问模式计算基础优先级
        for pattern in access_patterns.values():
            for sequences in pattern.user_patterns.values():
                for sequence in sequences:
                    for resource in sequence:
                        if resource not in priorities:
                            priorities[resource] = 0.0
                        priorities[resource] += pattern.confidence * 0.3
        
        # 基于预测结果调整优先级
        for prediction in predictions:
            if prediction.resource_id not in priorities:
                priorities[prediction.resource_id] = 0.0
            priorities[prediction.resource_id] += prediction.probability * 0.7
        
        # 基于历史访问频率调整
        total_accesses = sum(self.access_frequencies.values())
        if total_accesses > 0:
            for resource, count in self.access_frequencies.items():
                if resource not in priorities:
                    priorities[resource] = 0.0
                priorities[resource] += (count / total_accesses) * 0.4
        
        return priorities
    
    def _determine_eviction_policy(self, system_resources: Dict[str, Any]) -> str:
        """确定驱逐策略"""
        memory_usage = system_resources.get('memory_usage_percent', 50)
        
        if memory_usage > 80:
            return "LRU"  # 内存紧张时使用LRU
        elif memory_usage > 60:
            return "LFU"  # 中等内存使用时使用LFU
        else:
            return "FIFO"  # 内存充足时使用FIFO
    
    def _calculate_ttl_settings(self, access_patterns: Dict[str, AccessPattern]) -> Dict[str, int]:
        """计算TTL设置"""
        ttl_settings = {}
        
        for pattern in access_patterns.values():
            avg_interval = pattern.temporal_patterns.get('avg_interval', 3600)  # 默认1小时
            
            # 基于访问间隔设置TTL
            if avg_interval < 300:  # 5分钟内
                ttl = 1800  # 30分钟TTL
            elif avg_interval < 3600:  # 1小时内
                ttl = 7200  # 2小时TTL
            else:
                ttl = int(avg_interval * 2)  # 2倍访问间隔
            
            ttl_settings[pattern.pattern_id] = ttl
        
        return ttl_settings
    
    def _calculate_optimal_cache_size(self, system_resources: Dict[str, Any]) -> int:
        """计算最优缓存大小"""
        available_memory = system_resources.get('available_memory_mb', 1024)
        
        # 基于可用内存动态调整缓存大小
        if available_memory > 2048:
            return min(self.max_cache_size * 2, 2000)
        elif available_memory > 1024:
            return self.max_cache_size
        else:
            return max(self.max_cache_size // 2, 100)
    
    def _calculate_performance_metrics(self) -> Dict[str, float]:
        """计算性能指标"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        
        if total_requests == 0:
            return {'hit_rate': 0.0, 'preload_hit_rate': 0.0}
        
        hit_rate = self.cache_stats['hits'] / total_requests
        preload_hit_rate = self.cache_stats['preload_hits'] / self.cache_stats['hits'] if self.cache_stats['hits'] > 0 else 0
        
        return {
            'hit_rate': hit_rate,
            'preload_hit_rate': preload_hit_rate,
            'total_requests': total_requests,
            'eviction_rate': self.cache_stats['evictions'] / total_requests
        }
    
    def _preload_resource(self, resource_id: str, prediction: PreloadPrediction) -> bool:
        """预加载单个资源"""
        try:
            # 模拟资源加载（实际实现中会调用真实的资源加载逻辑）
            # 创建一个可以设置属性的对象而不是字符串
            class PreloadedResource:
                def __init__(self, data):
                    self.data = data
                    self._preloaded = False
                
                def __str__(self):
                    return str(self.data)
                
                def __repr__(self):
                    return f"PreloadedResource({self.data})"
            
            resource_data = PreloadedResource(f"preloaded_data_for_{resource_id}")
            self.put_cached_resource(resource_id, resource_data, is_preloaded=True)
            return True
        except Exception as e:
            self.logger.error(f"预加载资源失败 {resource_id}: {str(e)}")
            return False
    
    def _evict_resources(self):
        """驱逐缓存资源"""
        if not self.cache:
            return
        
        # 使用LRU策略驱逐
        oldest_resource = min(self.last_access_times.items(), key=lambda x: x[1])
        resource_to_evict = oldest_resource[0]
        
        del self.cache[resource_to_evict]
        del self.last_access_times[resource_to_evict]
        self.cache_stats['evictions'] += 1
        
        self.logger.debug(f"驱逐缓存资源: {resource_to_evict}")

class IntelligentPreloader:
    """智能预加载系统主引擎"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or {}
        
        # 初始化组件
        self.pattern_analyzer = AccessPatternAnalyzer(
            history_window_days=self.config.get('history_window_days', 30)
        )
        self.predictive_loader = PredictiveLoader()
        self.cache_manager = IntelligentCacheManager(
            max_cache_size=self.config.get('max_cache_size', 1000)
        )
        
        # 性能统计
        self.performance_stats = {
            'total_predictions': 0,
            'successful_preloads': 0,
            'cache_hit_improvements': 0.0,
            'response_time_improvements': 0.0
        }
        
        self.logger.info("智能预加载系统初始化完成")
    
    async def process_access_request(self, 
                                   resource_id: str,
                                   user_context: Dict[str, Any]) -> Tuple[Any, Dict[str, Any]]:
        """处理访问请求"""
        start_time = datetime.now()
        
        # 1. 记录访问
        access_record = AccessRecord(
            timestamp=start_time,
            user_id=user_context.get('user_id', 'anonymous'),
            resource_id=resource_id,
            access_type=AccessType(user_context.get('access_type', 'read')),
            context=user_context,
            session_id=user_context.get('session_id', 'default')
        )
        self.pattern_analyzer.record_access(access_record)
        
        # 2. 尝试从缓存获取
        cached_resource = self.cache_manager.get_cached_resource(resource_id)
        if cached_resource:
            response_time = (datetime.now() - start_time).total_seconds()
            return cached_resource, {'cache_hit': True, 'response_time': response_time}
        
        # 3. 加载资源（模拟）
        resource_data = await self._load_resource(resource_id)
        
        # 4. 放入缓存
        self.cache_manager.put_cached_resource(resource_id, resource_data)
        
        # 5. 触发预测和预加载
        await self._trigger_predictive_preload(user_context)
        
        response_time = (datetime.now() - start_time).total_seconds()
        return resource_data, {'cache_hit': False, 'response_time': response_time}
    
    async def _trigger_predictive_preload(self, user_context: Dict[str, Any]):
        """触发预测性预加载"""
        try:
            # 1. 获取用户画像
            user_id = user_context.get('user_id')
            user_profile = self.pattern_analyzer.user_profiles.get(user_id, {})
            
            # 2. 生成预测
            predictions = self.predictive_loader.predict_next_access(user_context, user_profile)
            self.performance_stats['total_predictions'] += len(predictions)
            
            # 3. 执行预加载
            preload_results = self.cache_manager.preload_resources(predictions)
            successful_preloads = sum(1 for success in preload_results.values() if success)
            self.performance_stats['successful_preloads'] += successful_preloads
            
            self.logger.debug(f"预测性预加载完成: {successful_preloads}/{len(predictions)} 成功")
            
        except Exception as e:
            self.logger.error(f"预测性预加载失败: {str(e)}")
    
    async def _load_resource(self, resource_id: str) -> Any:
        """加载资源（模拟实现）"""
        # 模拟资源加载延迟
        await asyncio.sleep(0.1)
        return f"resource_data_for_{resource_id}"
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        cache_metrics = self.cache_manager._calculate_performance_metrics()
        
        report = {
            'prediction_stats': {
                'total_predictions': self.performance_stats['total_predictions'],
                'successful_preloads': self.performance_stats['successful_preloads'],
                'preload_success_rate': (
                    self.performance_stats['successful_preloads'] / 
                    max(self.performance_stats['total_predictions'], 1)
                )
            },
            'cache_stats': cache_metrics,
            'system_stats': {
                'active_users': len(self.pattern_analyzer.user_profiles),
                'total_access_records': len(self.pattern_analyzer.access_history),
                'cache_size': len(self.cache_manager.cache)
            }
        }
        
        return report
    
    def optimize_system(self) -> Dict[str, Any]:
        """优化系统性能"""
        # 1. 分析访问模式
        patterns = self.pattern_analyzer.analyze_user_access_patterns()
        
        # 2. 生成预测
        sample_context = {'user_id': 'system_optimizer'}
        predictions = self.predictive_loader.predict_next_access(sample_context, {})
        
        # 3. 优化缓存策略
        system_resources = {'available_memory_mb': 2048, 'memory_usage_percent': 60}
        cache_strategy = self.cache_manager.optimize_cache_strategy(
            patterns, predictions, system_resources
        )
        
        optimization_report = {
            'patterns_analyzed': len(patterns),
            'predictions_generated': len(predictions),
            'cache_strategy': {
                'strategy_id': cache_strategy.strategy_id,
                'cache_size_limit': cache_strategy.cache_size_limit,
                'eviction_policy': cache_strategy.eviction_policy,
                'performance_metrics': cache_strategy.performance_metrics
            }
        }
        
        return optimization_report

# 导出主要类
__all__ = [
    'IntelligentPreloader',
    'AccessPatternAnalyzer',
    'PredictiveLoader',
    'IntelligentCacheManager',
    'AccessRecord',
    'AccessPattern',
    'PreloadPrediction',
    'CacheStrategy',
    'AccessType',
    'PredictionConfidence'
] 