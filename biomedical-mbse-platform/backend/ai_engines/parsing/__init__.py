"""
XML元数据系统 - 分层解析引擎 v3.0

🔍 实现分层解析功能：
- 分层快照解析器：三层渐进式解析策略
- 优化分层解析器：14.7倍性能提升版本
- 快照引擎：智能快照管理和存储
- 层处理器集合：Layer1/2/3专门处理器
- 快照管理器：渐进式数据构建
- 渐进式构建器：按需补全机制

📊 解析能力:
- Layer1: 基础结构解析 (DOM树)
- Layer2: 语义标签识别 (术语分析)
- Layer3: 关系网络构建 (深度连接)
- 性能监控：实时性能追踪
- 智能缓存：61.9%命中率
- 并行处理：多线程优化
"""

__version__ = "3.0.0"
__author__ = "XML元数据系统开发团队"

# 核心分层解析器
from .layered_snapshot_parser import (
    LayeredSnapshotParser,
    ElementSnapshot,
    LayeredParseResult
)

# 优化版分层解析器 (14.7倍性能提升)
from .optimized_layered_parser import (
    OptimizedLayeredParser
)

# 快照引擎
from .snapshot_engine import (
    SnapshotEngine
)

# 导出所有公共接口
__all__ = [
    # 核心解析器
    'LayeredSnapshotParser',
    'OptimizedLayeredParser',
    
    # 数据结构
    'ElementSnapshot',
    'LayeredParseResult',
    
    # 引擎组件
    'SnapshotEngine'
] 