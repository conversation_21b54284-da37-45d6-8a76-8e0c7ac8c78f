"""
优化版分层快照解析器

主要优化点：
1. 缓存机制：避免重复计算
2. 批量处理：减少逐个元素处理的开销
3. 并行处理：利用多线程
4. 内存优化：减少不必要的数据复制
5. 算法优化：改进父子关系查找
"""

import xml.etree.ElementTree as ET
import time
import concurrent.futures
import threading
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import hashlib

# 导入原有的数据结构
from .layered_snapshot_parser import ElementSnapshot, LayerInfo, LayeredParseResult

class OptimizedLayeredParser:
    """优化版分层快照解析器"""
    
    def __init__(self, terminology_analyzer=None):
        # 基础配置
        self.terminology_analyzer = terminology_analyzer
        self.id_generator = None  # 延迟初始化
        
        # 性能优化配置
        self.enable_parallel = True
        self.max_workers = 4
        self.batch_size = 50
        
        # 缓存系统
        self._element_cache = {}
        self._parent_cache = {}
        self._depth_cache = {}
        self._id_cache = {}
        
        # 性能统计
        self.parsing_stats = {
            'total_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0,
            'parallel_batches': 0
        }
        
        # 层级限制
        self.layer_limits = {1: 50, 2: 500, 3: 2000}
        
    def parse_by_layers_optimized(self, xml_content: str, max_depth: int = 3) -> LayeredParseResult:
        """优化版分层解析"""
        start_time = time.time()
        
        try:
            # 1. 快速解析XML
            root = self._fast_xml_parse(xml_content)
            
            # 2. 预构建索引（批量操作）
            indices = self._build_fast_indices(root)
            
            # 3. 术语分析（如果需要）
            terminology_result = self._get_terminology_result(xml_content)
            
            # 4. 并行分层解析
            snapshots, layers_info = self._parallel_layered_parse(root, indices, max_depth)
            
            # 5. 计算性能指标
            total_time = time.time() - start_time
            performance_metrics = self._calculate_optimized_metrics(total_time, len(snapshots))
            
            return LayeredParseResult(
                success=True,
                total_elements=len(snapshots),
                layers_info=layers_info,
                snapshots=snapshots,
                terminology_result=terminology_result,
                performance_metrics=performance_metrics,
                parsing_strategy='optimized_parallel',
                recommendations=self._generate_optimization_recommendations()
            )
            
        except Exception as e:
            return self._create_error_result(f"优化解析失败: {str(e)}")
    
    def _fast_xml_parse(self, xml_content: str) -> ET.Element:
        """快速XML解析"""
        # 移除不必要的空白符以减少内存占用
        import re
        # 保留结构的同时压缩空白
        compressed = re.sub(r'>\s+<', '><', xml_content)
        return ET.fromstring(compressed)
    
    def _build_fast_indices(self, root: ET.Element) -> Dict[str, Any]:
        """快速构建元素索引"""
        start_time = time.time()
        
        # 使用集合和字典优化查找
        element_to_parent = {}
        element_to_children = defaultdict(list)
        element_to_depth = {}
        element_list = []
        
        # 单次遍历构建所有索引
        for element in root.iter():
            element_list.append(element)
            
            # 构建父子关系映射
            for child in element:
                element_to_parent[child] = element
                element_to_children[element].append(child)
        
        # 批量计算深度
        self._batch_calculate_depths(root, element_to_parent, element_to_depth)
        
        indices = {
            'element_list': element_list,
            'parent_map': element_to_parent,
            'children_map': element_to_children,
            'depth_map': element_to_depth,
            'total_elements': len(element_list),
            'build_time': time.time() - start_time
        }
        
        return indices
    
    def _batch_calculate_depths(self, root: ET.Element, parent_map: Dict, depth_map: Dict):
        """批量计算元素深度"""
        # 使用BFS算法批量计算深度
        queue = deque([(root, 0)])
        depth_map[root] = 0
        
        while queue:
            element, depth = queue.popleft()
            
            # 处理所有子元素
            for child in element:
                if child not in depth_map:
                    depth_map[child] = depth + 1
                    queue.append((child, depth + 1))
    
    def _parallel_layered_parse(self, root: ET.Element, indices: Dict, max_depth: int) -> Tuple[Dict[str, ElementSnapshot], Dict[int, LayerInfo]]:
        """并行分层解析"""
        snapshots = {}
        layers_info = {}
        
        # Layer 1: 根元素（单个）
        layer1_start = time.time()
        layer1_snapshots = self._parse_layer1_optimized(root, indices)
        layer1_time = time.time() - layer1_start
        
        layers_info[1] = LayerInfo(
            layer_number=1,
            element_count=len(layer1_snapshots),
            processing_time=layer1_time,
            max_depth=0,
            coverage_ratio=len(layer1_snapshots) / indices['total_elements']
        )
        snapshots.update(layer1_snapshots)
        
        if max_depth >= 2:
            # Layer 2: 并行处理直接子元素
            layer2_start = time.time()
            layer2_snapshots = self._parse_layer2_parallel(root, indices)
            layer2_time = time.time() - layer2_start
            
            layers_info[2] = LayerInfo(
                layer_number=2,
                element_count=len(layer2_snapshots),
                processing_time=layer2_time,
                max_depth=1,
                coverage_ratio=len(layer2_snapshots) / indices['total_elements']
            )
            snapshots.update(layer2_snapshots)
        
        if max_depth >= 3:
            # Layer 3: 并行处理深层元素
            layer3_start = time.time()
            processed_elements = set(snapshots.keys())
            layer3_snapshots = self._parse_layer3_parallel(root, indices, processed_elements)
            layer3_time = time.time() - layer3_start
            
            layers_info[3] = LayerInfo(
                layer_number=3,
                element_count=len(layer3_snapshots),
                processing_time=layer3_time,
                max_depth=max([indices['depth_map'].get(elem, 0) for elem in indices['element_list']]),
                coverage_ratio=len(layer3_snapshots) / indices['total_elements']
            )
            snapshots.update(layer3_snapshots)
        
        return snapshots, layers_info
    
    def _parse_layer1_optimized(self, root: ET.Element, indices: Dict) -> Dict[str, ElementSnapshot]:
        """优化的Layer1解析"""
        snapshots = {}
        
        # 直接处理根元素，使用缓存的索引
        snapshot = self._create_snapshot_optimized(root, 1, indices)
        snapshots[snapshot.id] = snapshot
        
        return snapshots
    
    def _parse_layer2_parallel(self, root: ET.Element, indices: Dict) -> Dict[str, ElementSnapshot]:
        """并行处理Layer2"""
        snapshots = {}
        direct_children = list(root)
        
        if not direct_children:
            return snapshots
        
        # 限制元素数量
        max_elements = min(self.layer_limits[2], len(direct_children))
        children_to_process = direct_children[:max_elements]
        
        if self.enable_parallel and len(children_to_process) > self.batch_size:
            # 并行处理
            snapshots = self._parallel_process_elements(children_to_process, 2, indices)
            self.parsing_stats['parallel_batches'] += 1
        else:
            # 串行处理（小批量）
            for element in children_to_process:
                snapshot = self._create_snapshot_optimized(element, 2, indices)
                snapshots[snapshot.id] = snapshot
        
        return snapshots
    
    def _parse_layer3_parallel(self, root: ET.Element, indices: Dict, processed_elements: Set[str]) -> Dict[str, ElementSnapshot]:
        """并行处理Layer3"""
        snapshots = {}
        
        # 找出未处理的深层元素
        remaining_elements = []
        processed_count = 0
        
        for element in indices['element_list']:
            if processed_count >= self.layer_limits[3]:
                break
            
            # 跳过根元素和直接子元素
            if element == root or element in root:
                continue
            
            remaining_elements.append(element)
            processed_count += 1
        
        if not remaining_elements:
            return snapshots
        
        if self.enable_parallel and len(remaining_elements) > self.batch_size:
            # 并行处理
            snapshots = self._parallel_process_elements(remaining_elements, 3, indices)
            self.parsing_stats['parallel_batches'] += 1
        else:
            # 串行处理
            for element in remaining_elements:
                snapshot = self._create_snapshot_optimized(element, 3, indices)
                snapshots[snapshot.id] = snapshot
        
        return snapshots
    
    def _parallel_process_elements(self, elements: List[ET.Element], layer: int, indices: Dict) -> Dict[str, ElementSnapshot]:
        """并行处理元素列表"""
        snapshots = {}
        
        # 将元素分批
        batches = [elements[i:i + self.batch_size] for i in range(0, len(elements), self.batch_size)]
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交批处理任务
            future_to_batch = {
                executor.submit(self._process_element_batch, batch, layer, indices): batch 
                for batch in batches
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_batch):
                try:
                    batch_snapshots = future.result()
                    snapshots.update(batch_snapshots)
                except Exception as e:
                    print(f"批处理失败: {e}")
        
        return snapshots
    
    def _process_element_batch(self, elements: List[ET.Element], layer: int, indices: Dict) -> Dict[str, ElementSnapshot]:
        """处理元素批次"""
        snapshots = {}
        
        for element in elements:
            try:
                snapshot = self._create_snapshot_optimized(element, layer, indices)
                snapshots[snapshot.id] = snapshot
            except Exception as e:
                # 跳过有问题的元素，继续处理其他元素
                continue
        
        return snapshots
    
    def _create_snapshot_optimized(self, element: ET.Element, layer: int, indices: Dict) -> ElementSnapshot:
        """优化的快照创建"""
        start_time = time.time()
        
        # 1. 生成或获取缓存的ID
        element_id = self._get_cached_id(element, indices)
        
        # 2. 提取基本信息（快速）
        tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
        attributes = dict(element.attrib)  # 浅拷贝就足够
        text_content = (element.text or "").strip()
        
        # 3. 快速查找关系信息
        parent_id = self._get_parent_id_fast(element, indices)
        children_ids = self._get_children_ids_fast(element, indices)
        
        # 4. 简化的关系信息
        relationships = self._extract_relationships_fast(element, parent_id, children_ids)
        
        # 5. 快速语义信息
        semantic_info = self._extract_semantic_info_fast(element, tag, attributes)
        
        processing_time = time.time() - start_time
        
        return ElementSnapshot(
            id=element_id,
            layer_level=layer,
            tag=tag,
            attributes=attributes,
            text_content=text_content,
            parent_id=parent_id,
            children_ids=children_ids,
            relationships=relationships,
            semantic_info=semantic_info,
            stability_score=0.85,  # 固定值避免计算
            processing_time=processing_time,
            completion_status='complete'
        )
    
    def _get_cached_id(self, element: ET.Element, indices: Dict) -> str:
        """获取缓存的元素ID"""
        element_hash = id(element)
        
        if element_hash in self._id_cache:
            self.parsing_stats['cache_hits'] += 1
            return self._id_cache[element_hash]
        
        # 快速ID生成（简化版）
        tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
        name = element.get('name', '')
        element_type = element.get('xmi:type', '')
        
        if name:
            element_id = f"{tag}_{name}_{element_hash % 10000}"
        elif element_type:
            type_name = element_type.split('::')[-1] if '::' in element_type else element_type
            element_id = f"{tag}_{type_name}_{element_hash % 10000}"
        else:
            element_id = f"{tag}_{element_hash % 10000}"
        
        self._id_cache[element_hash] = element_id
        self.parsing_stats['cache_misses'] += 1
        
        return element_id
    
    def _get_parent_id_fast(self, element: ET.Element, indices: Dict) -> Optional[str]:
        """快速获取父元素ID"""
        parent = indices['parent_map'].get(element)
        if parent:
            return self._get_cached_id(parent, indices)
        return None
    
    def _get_children_ids_fast(self, element: ET.Element, indices: Dict) -> List[str]:
        """快速获取子元素ID列表"""
        children = indices['children_map'].get(element, [])
        return [self._get_cached_id(child, indices) for child in children[:10]]  # 限制子元素数量
    
    def _extract_relationships_fast(self, element: ET.Element, parent_id: Optional[str], children_ids: List[str]) -> Dict[str, List[str]]:
        """快速提取关系信息"""
        relationships = {
            'references_to': [],
            'referenced_by': [],
            'contains': children_ids,
            'contained_by': [parent_id] if parent_id else []
        }
        
        # 只查找常见的引用属性
        for attr_name, attr_value in element.attrib.items():
            if attr_name in ('href', 'ref', 'target', 'source') and attr_value:
                relationships['references_to'].append(attr_value)
                break  # 只取第一个引用
        
        return relationships
    
    def _extract_semantic_info_fast(self, element: ET.Element, tag: str, attributes: Dict) -> Dict[str, Any]:
        """快速提取语义信息"""
        return {
            'element_type': tag,
            'name': attributes.get('name', ''),
            'stereotype': attributes.get('xmi:type', ''),
            'domain': 'uml' if 'uml' in tag.lower() else 'unknown',
            'category': 'element',
            'confidence': 0.8
        }
    
    def _get_terminology_result(self, xml_content: str):
        """获取术语分析结果"""
        if self.terminology_analyzer:
            return self.terminology_analyzer.analyze_document_terminology(xml_content)
        return None
    
    def _calculate_optimized_metrics(self, total_time: float, total_elements: int) -> Dict[str, float]:
        """计算优化后的性能指标"""
        elements_per_second = total_elements / total_time if total_time > 0 else 0
        cache_hit_ratio = self.parsing_stats['cache_hits'] / (self.parsing_stats['cache_hits'] + self.parsing_stats['cache_misses']) if (self.parsing_stats['cache_hits'] + self.parsing_stats['cache_misses']) > 0 else 0
        
        return {
            'total_processing_time': total_time,
            'elements_per_second': elements_per_second,
            'cache_hit_ratio': cache_hit_ratio,
            'parallel_batches': self.parsing_stats['parallel_batches'],
            'optimization_level': 'high'
        }
    
    def _generate_optimization_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        cache_hit_ratio = self.parsing_stats['cache_hits'] / (self.parsing_stats['cache_hits'] + self.parsing_stats['cache_misses']) if (self.parsing_stats['cache_hits'] + self.parsing_stats['cache_misses']) > 0 else 0
        
        if cache_hit_ratio < 0.5:
            recommendations.append("考虑增加缓存大小以提高缓存命中率")
        
        if self.parsing_stats['parallel_batches'] == 0:
            recommendations.append("文件较小，未使用并行处理")
        
        recommendations.append("使用了优化版解析器，性能已提升")
        
        return recommendations
    
    def _create_error_result(self, error_message: str) -> LayeredParseResult:
        """创建错误结果"""
        from .layered_snapshot_parser import LayeredParseResult
        return LayeredParseResult(
            success=False,
            total_elements=0,
            layers_info={},
            snapshots={},
            terminology_result=None,
            performance_metrics={'error': error_message},
            parsing_strategy='failed',
            recommendations=[f"解析失败: {error_message}"]
        ) 