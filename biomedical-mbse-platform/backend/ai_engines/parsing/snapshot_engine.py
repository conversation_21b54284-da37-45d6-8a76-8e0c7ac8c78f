"""
XML元数据系统 - 快照解析引擎

基于分层快照解析实现方案，提供：
- 四阶段渐进式解析
- 文档术语识别
- 快照式元数据管理
- ≤3层分层策略
"""

import time
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import xml.etree.ElementTree as ET

# 设置日志 - 需要在导入前定义
logger = logging.getLogger(__name__)

# 从connection_engine导入EnhancedMetadataNode
from ..connection.connection_engine import EnhancedMetadataNode
# 修复导入：使用core模块替代重复实现
try:
    from core.parsing.terminology import TerminologyAnalyzer as DocumentTerminologyAnalyzer
    from core.services.id_manager import StableIDManager as StableIDGenerator
    from core.services.cache_manager import CacheManager
    _core_available = True
    logger.info("✅ 使用core模块组件")
except ImportError:
    # 保留本地实现作为fallback
    _core_available = False
    logger.warning("⚠️ core模块不可用，使用本地实现")

@dataclass
class SnapshotLayer:
    """快照层数据结构"""
    layer_id: int
    name: str
    priority: int
    elements: Dict[str, Any] = field(default_factory=dict)
    metadata_count: int = 0
    processing_time: float = 0.0
    confidence_score: float = 0.0

@dataclass
class DocumentSignature:
    """文档签名"""
    content_hash: str
    structure_hash: str
    namespace_signature: str
    element_count: int
    complexity_score: float
    creation_time: datetime = field(default_factory=datetime.now)

@dataclass
class LayerMetadata:
    """层元数据"""
    layer_id: int
    name: str
    description: str
    element_count: int
    metadata_count: int
    processing_time: float
    confidence_score: float
    elements: Dict[str, EnhancedMetadataNode] = field(default_factory=dict)

@dataclass
class DocumentSnapshot:
    """文档快照"""
    document_signature: DocumentSignature
    layers: List[LayerMetadata]
    total_elements: int
    snapshot_time: datetime
    processing_statistics: Dict[str, Any] = field(default_factory=dict)

class DocumentTerminologyAnalyzer:
    """文档术语体系识别器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化术语分析器"""
        self.namespace_patterns = {}
        self.terminology_cache = {}
        self.identification_schemes = {}
        
    def analyze_document_terminology(self, root: ET.Element) -> Dict[str, Any]:
        """
        分析文档术语体系
        
        Args:
            root: XML根元素
            
        Returns:
            术语分析结果
        """
        # 1. 识别命名空间体系
        namespace_info = self._extract_namespace_system(root)
        
        # 2. 构建术语识别机制
        terminology = self._build_terminology_system(root, namespace_info)
        
        # 3. 建立标识机制
        identification = self._establish_identification_scheme(terminology)
        
        # 4. 确定解析策略
        parsing_strategy = self._determine_parsing_strategy(root)
        
        return {
            'namespace_system': namespace_info,
            'terminology_system': terminology,
            'identification_scheme': identification,
            'parsing_strategy': parsing_strategy,
            'document_type': self._identify_document_type(root),
            'complexity_indicators': self._analyze_complexity(root)
        }
    
    def _extract_namespace_system(self, root: ET.Element) -> Dict[str, Any]:
        """提取命名空间体系"""
        namespaces = {}
        prefixes = {}
        
        # 从根元素提取命名空间
        for key, value in root.attrib.items():
            if key.startswith('xmlns:'):
                prefix = key[6:]  # 去掉 'xmlns:' 前缀
                namespaces[prefix] = value
                prefixes[value] = prefix
            elif key == 'xmlns':
                namespaces['default'] = value
                prefixes[value] = 'default'
        
        # 分析命名空间模式
        namespace_patterns = self._analyze_namespace_patterns(namespaces)
        
        return {
            'namespaces': namespaces,
            'prefixes': prefixes,
            'patterns': namespace_patterns,
            'primary_schema': self._identify_primary_schema(namespaces)
        }
    
    def _build_terminology_system(self, root: ET.Element, namespace_info: Dict[str, Any]) -> Dict[str, Any]:
        """构建术语识别机制"""
        # 收集所有元素标签
        all_tags = set()
        tag_frequencies = {}
        attribute_patterns = {}
        
        for element in root.iter():
            tag = element.tag
            all_tags.add(tag)
            tag_frequencies[tag] = tag_frequencies.get(tag, 0) + 1
            
            # 分析属性模式
            for attr_name in element.attrib:
                if attr_name not in attribute_patterns:
                    attribute_patterns[attr_name] = set()
                attribute_patterns[attr_name].add(tag)
        
        # 识别关键术语
        key_terms = self._identify_key_terms(tag_frequencies, attribute_patterns)
        
        # 构建层次结构
        hierarchy = self._build_element_hierarchy(root)
        
        return {
            'all_tags': list(all_tags),
            'tag_frequencies': tag_frequencies,
            'attribute_patterns': {k: list(v) for k, v in attribute_patterns.items()},
            'key_terms': key_terms,
            'hierarchy': hierarchy,
            'vocabulary_size': len(all_tags)
        }
    
    def _establish_identification_scheme(self, terminology: Dict[str, Any]) -> Dict[str, Any]:
        """建立标识机制"""
        # 基于术语复杂度确定ID策略
        vocabulary_size = terminology['vocabulary_size']
        
        if vocabulary_size < 50:
            strategy = "simple_hash"
        elif vocabulary_size < 200:
            strategy = "semantic_hash"
        else:
            strategy = "hierarchical_hash"
        
        return {
            'strategy': strategy,
            'vocabulary_complexity': vocabulary_size,
            'id_stability_target': 0.95,
            'hash_method': 'sha256_truncated'
        }
    
    def _determine_parsing_strategy(self, root: ET.Element) -> Dict[str, Any]:
        """确定解析策略"""
        element_count = len(list(root.iter()))
        max_depth = self._calculate_max_depth(root)
        
        # 根据复杂度确定分层策略
        if element_count < 100:
            layers = 1
            strategy = "single_layer"
        elif element_count < 1000:
            layers = 2
            strategy = "dual_layer"
        else:
            layers = 3
            strategy = "multi_layer"
        
        return {
            'strategy': strategy,
            'recommended_layers': layers,
            'element_count': element_count,
            'max_depth': max_depth,
            'processing_mode': 'incremental' if element_count > 500 else 'batch'
        }
    
    def _analyze_namespace_patterns(self, namespaces: Dict[str, str]) -> Dict[str, Any]:
        """分析命名空间模式"""
        patterns = {
            'uml_related': False,
            'sysml_related': False,
            'xmi_related': False,
            'custom_schema': False
        }
        
        for prefix, uri in namespaces.items():
            uri_lower = uri.lower()
            if 'uml' in uri_lower:
                patterns['uml_related'] = True
            if 'sysml' in uri_lower:
                patterns['sysml_related'] = True
            if 'xmi' in uri_lower:
                patterns['xmi_related'] = True
            if not any([
                'uml' in uri_lower, 'sysml' in uri_lower, 
                'xmi' in uri_lower, 'w3.org' in uri_lower
            ]):
                patterns['custom_schema'] = True
        
        return patterns
    
    def _identify_primary_schema(self, namespaces: Dict[str, str]) -> str:
        """识别主要模式"""
        for prefix, uri in namespaces.items():
            if 'uml' in uri.lower():
                return 'UML'
            elif 'sysml' in uri.lower():
                return 'SysML'
        return 'Custom'
    
    def _identify_key_terms(self, tag_frequencies: Dict[str, int], 
                          attribute_patterns: Dict[str, set]) -> List[str]:
        """识别关键术语"""
        # 基于频率识别重要标签
        sorted_tags = sorted(tag_frequencies.items(), key=lambda x: x[1], reverse=True)
        key_terms = [tag for tag, freq in sorted_tags[:20]]  # 取前20个最频繁的标签
        
        return key_terms
    
    def _build_element_hierarchy(self, root: ET.Element) -> Dict[str, Any]:
        """构建元素层次结构"""
        hierarchy = {}
        
        def build_tree(element, path=""):
            tag = element.tag
            current_path = f"{path}/{tag}" if path else tag
            
            if tag not in hierarchy:
                hierarchy[tag] = {
                    'paths': set(),
                    'children': set(),
                    'parents': set()
                }
            
            hierarchy[tag]['paths'].add(current_path)
            
            for child in element:
                child_tag = child.tag
                hierarchy[tag]['children'].add(child_tag)
                
                if child_tag not in hierarchy:
                    hierarchy[child_tag] = {
                        'paths': set(),
                        'children': set(),
                        'parents': set()
                    }
                hierarchy[child_tag]['parents'].add(tag)
                
                build_tree(child, current_path)
        
        build_tree(root)
        
        # 转换set为list以便序列化
        for tag_info in hierarchy.values():
            tag_info['paths'] = list(tag_info['paths'])
            tag_info['children'] = list(tag_info['children'])
            tag_info['parents'] = list(tag_info['parents'])
        
        return hierarchy
    
    def _identify_document_type(self, root: ET.Element) -> str:
        """识别文档类型"""
        root_tag = root.tag.lower()
        
        if 'model' in root_tag:
            return 'UML_Model'
        elif 'package' in root_tag:
            return 'Package'
        elif 'diagram' in root_tag:
            return 'Diagram'
        else:
            return 'Unknown'
    
    def _analyze_complexity(self, root: ET.Element) -> Dict[str, float]:
        """分析复杂度指标"""
        element_count = len(list(root.iter()))
        max_depth = self._calculate_max_depth(root)
        unique_tags = len(set(element.tag for element in root.iter()))
        
        # 计算复杂度分数
        size_complexity = min(1.0, element_count / 1000.0)
        depth_complexity = min(1.0, max_depth / 10.0)
        diversity_complexity = min(1.0, unique_tags / 50.0)
        
        overall_complexity = (size_complexity + depth_complexity + diversity_complexity) / 3
        
        return {
            'size_complexity': size_complexity,
            'depth_complexity': depth_complexity,
            'diversity_complexity': diversity_complexity,
            'overall_complexity': overall_complexity,
            'element_count': element_count,
            'max_depth': max_depth,
            'unique_tags': unique_tags
        }
    
    def _calculate_max_depth(self, root: ET.Element) -> int:
        """计算最大深度"""
        def get_depth(element, current_depth=0):
            if not list(element):
                return current_depth
            return max(get_depth(child, current_depth + 1) for child in element)
        
        return get_depth(root)

# =============================================================================
# 🔄 条件化本地实现 - 只有在core模块不可用时才使用
# =============================================================================

if not _core_available:
    logger.warning("🔄 正在使用本地fallback实现，建议启用core模块获得更好性能")
    
    # 只有在core不可用时才定义这些类
    class CacheManager:
        """简单的缓存管理器 - fallback实现"""
        def __init__(self):
            self.cache = {}
            self.cache_stats = {'hits': 0, 'misses': 0}
        
        def get_snapshot(self, signature):
            """获取缓存的快照"""
            if str(signature) in self.cache:
                self.cache_stats['hits'] += 1
                return self.cache[str(signature)]
            self.cache_stats['misses'] += 1
            return None
        
        def store_snapshot(self, signature, snapshot):
            """存储快照到缓存"""
            self.cache[str(signature)] = snapshot
        
        def get_cache_statistics(self):
            """获取缓存统计"""
            return self.cache_stats

    class StableIDGenerator:
        """简单的稳定ID生成器 - fallback实现"""
        def __init__(self):
            self.id_counter = 0
        
        def generate_stable_id(self, element, context=None):
            """生成稳定ID"""
            # 尝试使用元素的xmi:id或name属性
            xmi_id = element.get('{http://www.omg.org/XMI}id') or element.get('xmi:id')
            if xmi_id:
                return xmi_id
            
            name = element.get('name')
            if name:
                return f"{element.tag}_{name}"
            
            # 回退到基于内容的哈希
            content = f"{element.tag}_{len(list(element))}"
            hash_id = hashlib.md5(content.encode()).hexdigest()[:12]
            return f"stable_{hash_id}"
else:
    logger.info("✅ 使用core模块的高性能组件，跳过本地实现")

class SnapshotEngine:
    """分层快照解析引擎"""
    
    def __init__(self):
        # 优先使用core模块组件
        if _core_available:
            self.cache_manager = CacheManager(config={})  # 传递空配置字典
            self.terminology_analyzer = DocumentTerminologyAnalyzer(config={})  # 传递空配置字典
            logger.info("✅ 使用core模块的高性能组件")
        else:
            # fallback到本地实现
            self.cache_manager = CacheManager()  # 使用本地简化版
            self.terminology_analyzer = DocumentTerminologyAnalyzer()  # 使用本地简化版
            logger.warning("⚠️ 使用本地fallback实现")
            
        self.layer_definitions = self._initialize_layer_definitions()
        self.processing_stats = {
            'total_documents': 0,
            'successful_snapshots': 0,
            'failed_snapshots': 0,
            'average_processing_time': 0.0
        }
        
    def _initialize_layer_definitions(self) -> Dict[int, Dict[str, Any]]:
        """初始化分层定义"""
        return {
            1: {
                'name': '文档结构层',
                'priority': 1,
                'target_elements': ['packagedElement', 'model', 'package'],
                'max_elements': 1000,
                'timeout_seconds': 0.1
            },
            2: {
                'name': '语义关键层', 
                'priority': 2,
                'target_elements': ['class', 'association', 'component'],
                'max_elements': 2000,
                'timeout_seconds': 0.2
            },
            3: {
                'name': '详细信息层',
                'priority': 3, 
                'target_elements': ['property', 'operation', 'parameter'],
                'max_elements': 5000,
                'timeout_seconds': 0.2
            }
        }
    
    def create_document_snapshot(self, xml_content: str, options: Dict[str, Any] = None) -> DocumentSnapshot:
        """创建文档快照"""
        start_time = time.time()
        options = options or {}
        
        try:
            # 1. 文档预处理和签名生成
            document_signature = self._generate_document_signature(xml_content)
            
            # 2. 检查缓存
            cached_snapshot = self.cache_manager.get_snapshot(document_signature)
            if cached_snapshot and not options.get('force_refresh', False):
                logger.info(f"使用缓存快照: {document_signature}")
                return cached_snapshot
            
            # 3. 解析XML文档
            root = ET.fromstring(xml_content)
            
            # 4. 执行分层快照解析
            layers = self._execute_layered_parsing(root, options)
            
            # 5. 创建快照对象
            snapshot = DocumentSnapshot(
                document_signature=document_signature,
                layers=layers,
                total_elements=sum(layer.metadata_count for layer in layers),
                snapshot_time=datetime.now(),
                processing_statistics={
                    'processing_time': time.time() - start_time,
                    'layers_created': len(layers),
                    'terminology_analysis': self.terminology_analyzer.analyze_document_terminology(root),
                    'performance_metrics': self._calculate_performance_metrics(layers)
                }
            )
            
            # 6. 缓存快照
            self.cache_manager.store_snapshot(document_signature, snapshot)
            
            # 7. 更新统计
            self._update_processing_stats(snapshot)
            
            logger.info(f"快照创建成功: {len(layers)}层, 共{snapshot.total_elements}个元素, 耗时{snapshot.processing_statistics['processing_time']:.3f}秒")
            
            return snapshot
            
        except Exception as e:
            logger.error(f"快照创建失败: {str(e)}")
            self.processing_stats['failed_snapshots'] += 1
            raise
    
    def _execute_layered_parsing(self, root: ET.Element, options: Dict[str, Any]) -> List[LayerMetadata]:
        """执行分层解析"""
        layers = []
        max_layers = options.get('max_layers', 3)
        
        for layer_id in range(1, max_layers + 1):
            if layer_id not in self.layer_definitions:
                continue
                
            layer_def = self.layer_definitions[layer_id]
            layer_start_time = time.time()
            
            # 执行单层解析
            layer = self._parse_single_layer(root, layer_id, layer_def, options)
            layer.processing_time = time.time() - layer_start_time
            
            layers.append(layer)
            
            # 检查是否满足快速返回条件
            if self._should_stop_layered_parsing(layers, options):
                logger.info(f"快速返回条件满足，停止在第{layer_id}层")
                break
        
        return layers
    
    def _parse_single_layer(self, root: ET.Element, layer_id: int, layer_def: Dict[str, Any], options: Dict[str, Any]) -> LayerMetadata:
        """解析单个层级"""
        layer = LayerMetadata(
            layer_id=layer_id,
            name=layer_def['name'],
            description=layer_def['name'],
            element_count=layer_def['max_elements'],
            metadata_count=0,
            processing_time=0.0,
            confidence_score=0.0
        )
        
        target_elements = layer_def['target_elements']
        max_elements = layer_def['max_elements']
        timeout = layer_def['timeout_seconds']
        
        start_time = time.time()
        processed_count = 0
        
        # 使用XPath查找目标元素
        for element_type in target_elements:
            if time.time() - start_time > timeout:
                logger.warning(f"第{layer_id}层解析超时")
                break
                
            elements = root.findall(f".//{element_type}")
            
            for element in elements:
                if processed_count >= max_elements:
                    break
                    
                # 生成稳定ID
                element_id = self._generate_stable_element_id(element, layer_id)
                
                # 创建增强元数据节点
                enhanced_node = self._create_enhanced_metadata_node(element, element_id, layer_id)
                
                layer.elements[element_id] = enhanced_node
                processed_count += 1
        
        layer.metadata_count = processed_count
        layer.confidence_score = self._calculate_layer_confidence(layer, processed_count, max_elements)
        
        return layer
    
    def _create_enhanced_metadata_node(self, element: ET.Element, element_id: str, layer_id: int) -> EnhancedMetadataNode:
        """创建增强的元数据节点"""
        # 基础信息提取
        name = element.get('name', '')
        tag = element.tag
        
        # 语义分析 (简化版本，实际应调用语义引擎)
        semantic_result = self._simple_semantic_analysis(element, name, tag)
        
        # 关系信息提取
        relationships = self._extract_relationships(element)
        
        # 创建增强节点
        enhanced_node = EnhancedMetadataNode(
            id=element_id,
            name=name,
            tag=tag,
            layer_id=layer_id,
            semantics=semantic_result,
            relationships=relationships,
            attributes=dict(element.attrib),
            processing_metadata={
                'extraction_time': datetime.now(),
                'layer_id': layer_id,
                'confidence': semantic_result.get('confidence', 0.8)
            }
        )
        
        return enhanced_node
    
    def _simple_semantic_analysis(self, element: ET.Element, name: str, tag: str) -> Dict[str, Any]:
        """简化的语义分析"""
        # 基于标签名称的基础分类
        category_mapping = {
            'packagedElement': '结构域',
            'class': '结构域', 
            'component': '结构域',
            'association': '关系域',
            'dependency': '关系域',
            'property': '属性域',
            'operation': '行为域',
            'parameter': '参数域'
        }
        
        category = category_mapping.get(tag, '未知域')
        
        # 计算优先级
        priority = self._calculate_element_priority(element, name, tag)
        
        return {
            'category': category,
            'priority': priority,
            'confidence': 0.8,
            'analysis_method': 'rule_based',
            'keywords': self._extract_keywords(name),
            'stereotype': element.get('{http://www.eclipse.org/uml2/3.0.0/UML}type', '')
        }
    
    def _extract_relationships(self, element: ET.Element) -> Dict[str, Any]:
        """提取关系信息"""
        relationships = {
            'references_to': [],
            'referenced_by': [],
            'parent_id': None,
            'children_ids': []
        }
        
        # 提取引用关系
        for attr_name, attr_value in element.attrib.items():
            if attr_name.endswith('_ref') or 'href' in attr_name:
                relationships['references_to'].append(attr_value)
        
        # 提取子元素关系
        for child in element:
            child_id = child.get('xmi:id') or self._generate_simple_id(child)
            relationships['children_ids'].append(child_id)
        
        return relationships
    
    def _generate_stable_element_id(self, element: ET.Element, layer_id: int) -> str:
        """生成稳定的元素ID"""
        # 优先使用xmi:id
        xmi_id = element.get('{http://www.omg.org/XMI}id') or element.get('xmi:id')
        if xmi_id:
            return f"L{layer_id}_{xmi_id}"
        
        # 基于内容生成稳定哈希
        content_signature = f"{element.tag}_{element.get('name', '')}_{layer_id}"
        hash_id = hashlib.md5(content_signature.encode()).hexdigest()[:12]
        
        return f"L{layer_id}_{hash_id}"
    
    def _generate_simple_id(self, element: ET.Element) -> str:
        """生成简单ID"""
        xmi_id = element.get('{http://www.omg.org/XMI}id') or element.get('xmi:id')
        if xmi_id:
            return xmi_id
            
        name = element.get('name', '')
        if name:
            return f"{element.tag}_{name}"
            
        return f"{element.tag}_{id(element)}"
    
    def _calculate_element_priority(self, element: ET.Element, name: str, tag: str) -> int:
        """计算元素优先级"""
        priority = 5  # 默认优先级
        
        # 基于标签的优先级调整
        priority_mapping = {
            'model': 10,
            'package': 9,
            'class': 8,
            'component': 8,
            'association': 7,
            'property': 6,
            'operation': 6,
            'parameter': 5
        }
        
        priority = priority_mapping.get(tag, priority)
        
        # 基于名称的优先级调整
        if name:
            if any(keyword in name.lower() for keyword in ['main', 'root', 'system']):
                priority += 2
            elif any(keyword in name.lower() for keyword in ['test', 'temp', 'debug']):
                priority -= 1
        
        return max(1, min(10, priority))
    
    def _extract_keywords(self, name: str) -> List[str]:
        """提取关键词"""
        if not name:
            return []
        
        # 简单的关键词提取
        keywords = []
        
        # 分割驼峰命名
        import re
        words = re.findall(r'[A-Z][a-z]*|[a-z]+', name)
        keywords.extend([word.lower() for word in words if len(word) > 2])
        
        return keywords[:5]  # 限制关键词数量
    
    def _calculate_layer_confidence(self, layer: LayerMetadata, processed_count: int, max_elements: int) -> float:
        """计算层级置信度"""
        if max_elements == 0:
            return 1.0
            
        # 基于处理完成度的置信度
        completion_ratio = min(1.0, processed_count / max_elements)
        
        # 基于处理时间的置信度调整
        time_factor = 1.0
        if layer.processing_time > 0.5:  # 超过500ms认为可能不完整
            time_factor = 0.8
        
        confidence = completion_ratio * time_factor
        return round(confidence, 3)
    
    def _should_stop_layered_parsing(self, layers: List[LayerMetadata], options: Dict[str, Any]) -> bool:
        """判断是否应该停止分层解析"""
        # 快速返回选项
        if options.get('quick_return', False):
            return len(layers) >= 1
        
        # 基于元素数量的停止条件
        total_elements = sum(layer.metadata_count for layer in layers)
        if total_elements >= options.get('max_total_elements', 10000):
            return True
        
        # 基于时间的停止条件
        total_time = sum(layer.processing_time for layer in layers)
        if total_time >= options.get('max_total_time', 1.0):
            return True
        
        return False
    
    def _generate_document_signature(self, xml_content: str) -> DocumentSignature:
        """生成文档签名"""
        # 计算内容哈希
        content_hash = hashlib.sha256(xml_content.encode()).hexdigest()[:16]
        
        # 添加时间戳以确保唯一性
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        return DocumentSignature(
            content_hash=content_hash,
            structure_hash=content_hash,
            namespace_signature=content_hash[:12],
            element_count=len(list(ET.fromstring(xml_content).iter())),
            complexity_score=min(10.0, len(list(ET.fromstring(xml_content).iter())) / 100.0)
        )
    
    def _update_processing_stats(self, snapshot: DocumentSnapshot):
        """更新处理统计"""
        self.processing_stats['total_documents'] += 1
        self.processing_stats['successful_snapshots'] += 1
        
        # 更新平均处理时间
        current_avg = self.processing_stats['average_processing_time']
        new_time = snapshot.processing_statistics['processing_time']
        total_docs = self.processing_stats['total_documents']
        
        self.processing_stats['average_processing_time'] = (
            (current_avg * (total_docs - 1) + new_time) / total_docs
        )
    
    def get_snapshot_layer(self, snapshot: DocumentSnapshot, layer_id: int) -> Optional[LayerMetadata]:
        """获取指定层级的快照数据"""
        for layer in snapshot.layers:
            if layer.layer_id == layer_id:
                return layer
        return None
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            **self.processing_stats,
            'cache_stats': self.cache_manager.get_cache_statistics(),
            'layer_definitions': self.layer_definitions
        }

    def _calculate_performance_metrics(self, layers: List[LayerMetadata]) -> Dict[str, float]:
        """计算性能指标"""
        total_processing_time = sum(layer.processing_time for layer in layers)
        total_elements = sum(layer.metadata_count for layer in layers)
        
        return {
            'total_processing_time': total_processing_time,
            'total_elements_processed': total_elements,
            'elements_per_second': total_elements / max(0.001, total_processing_time),
            'average_layer_time': total_processing_time / max(1, len(layers)),
            'average_confidence': sum(layer.confidence_score for layer in layers) / max(1, len(layers))
        } 