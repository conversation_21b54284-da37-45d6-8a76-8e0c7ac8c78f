"""
XML元数据系统 - 视角管理引擎 v3.0

🎭 实现视角管理功能：
- 视角分析器：多角色视角生成和分析
- 需求分析师视角：需求追踪和管理视图
- 系统架构师视角：架构设计和依赖视图
- 行为分析师视角：行为建模和状态视图
- 测试工程师视角：测试覆盖和验证视图
- 业务分析师视角：业务流程和价值视图
- 集成专家视角：接口和集成视图

📊 视角能力:
- 角色定制化视图生成
- 跨视角洞察分析
- 协作路径优化
- 视角对比分析
- 多维度数据过滤
- 智能推荐系统
"""

__version__ = "3.0.0"
__author__ = "XML元数据系统开发团队"

# 核心视角分析器
from .perspective_analyzer import (
    PerspectiveAnalyzer,
    PerspectiveView,
    PerspectiveConfig,
    ProfessionalRole,
    ViewType,
    AnalysisDepth,
    PerspectiveAnalysisResult
)

# 导出所有公共接口
__all__ = [
    # 核心分析器
    'PerspectiveAnalyzer',
    
    # 数据结构
    'PerspectiveView',
    'PerspectiveConfig',
    'PerspectiveAnalysisResult',
    
    # 枚举类型
    'ProfessionalRole',
    'ViewType',
    'AnalysisDepth'
] 