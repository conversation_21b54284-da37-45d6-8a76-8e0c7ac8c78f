"""
生物医学MBSE建模平台 - FastAPI主应用

基于FastAPI的REST API服务，集成UML/SysML标准建模系统与生物医学扩展
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from typing import Dict, Any
import logging
import sys
import os
import time

# 添加backend目录到Python路径
backend_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if backend_path not in sys.path:
    sys.path.append(backend_path)

# 导入路由
from api.routers.modeling import router as modeling_router

# 导入现有组件（保持兼容性）
from biomedical_extensions.biomedical_recommendation_engine import BiomedicalRecommendationEngine
from biomedical_extensions.biomedical_preloader import BiomedicalPreloader
from biomedical_extensions.biomedical_cache_manager import BiomedicalCacheManager
from biomedical_extensions.tool_chain_manager import ToolChainManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="生物医学MBSE建模平台",
    description="基于UML/SysML标准的生物医学建模和分析平台，集成177个专业工具",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境需要限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(modeling_router, prefix="/api/v1/modeling", tags=["modeling"])

# 全局组件实例
biomedical_recommender = None
biomedical_preloader = None
cache_manager = None
tool_chain_manager = None

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global biomedical_recommender, biomedical_preloader, cache_manager, tool_chain_manager
    logger.info("🚀 启动生物医学MBSE平台...")
    
    try:
        # 初始化现有组件（保持兼容性）
        biomedical_recommender = BiomedicalRecommendationEngine()
        biomedical_preloader = BiomedicalPreloader()
        cache_manager = BiomedicalCacheManager()
        tool_chain_manager = ToolChainManager()
        
        logger.info("✅ 生物医学推荐引擎初始化完成")
        logger.info("✅ 生物医学预加载器初始化完成")
        logger.info("✅ 缓存管理器初始化完成")
        logger.info("✅ 工具链管理器初始化完成")
        logger.info("✅ UML/SysML建模系统集成完成")
        logger.info("✅ 平台启动完成")
        
    except Exception as e:
        logger.error(f"❌ 平台启动失败: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("关闭生物医学MBSE平台...")

# 基础API端点
@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "欢迎使用生物医学MBSE建模平台",
        "platform": "生物医学MBSE建模平台",
        "version": "2.0.0",
        "features": [
            "UML 2.5标准建模",
            "SysML 1.6扩展",
            "177个生物医学工具集成",
            "知识管理系统",
            "AI辅助建模"
        ],
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    global biomedical_recommender, biomedical_preloader, cache_manager, tool_chain_manager
    
    connection_status = {
        "connected": all([
            biomedical_recommender,
            biomedical_preloader,
            cache_manager,
            tool_chain_manager
        ])
    }
    
    return {
        "status": "healthy",
        "platform": "生物医学MBSE建模平台",
        "version": "2.0.0",
        "components": {
            "uml_engine": "active",
            "sysml_engine": "active",
            "biomedical_extensions": "active",
            "tool_integration": "active",
            "knowledge_management": "active"
        },
        "ai_components": connection_status,
        "timestamp": "2025-06-30T15:00:00Z"
    }

# 系统能力展示端点
@app.get("/api/v1/capabilities")
async def get_system_capabilities():
    """获取系统能力"""
    return {
        "modeling_standards": {
            "uml": {
                "version": "2.5",
                "supported_diagrams": [
                    "ClassDiagram", "ComponentDiagram", "PackageDiagram", 
                    "UseCaseDiagram", "SequenceDiagram", "ActivityDiagram",
                    "StateDiagram", "DeploymentDiagram"
                ]
            },
            "sysml": {
                "version": "1.6",
                "supported_diagrams": [
                    "BlockDefinitionDiagram", "InternalBlockDiagram",
                    "RequirementsDiagram", "ParametricDiagram"
                ]
            }
        },
        "biomedical_domains": [
            "molecular_biology", "systems_biology", "cell_biology",
            "bioinformatics", "pharmacology", "genetics"
        ],
        "integrated_tools": {
            "count": 177,
            "categories": [
                "molecular_modeling", "sequence_analysis", "statistical_analysis",
                "systems_biology", "visualization", "bioinformatics"
            ]
        },
        "knowledge_management": {
            "document_formats": ["PDF", "DOC", "TXT", "XML"],
            "standards_supported": ["SBML", "FASTA", "PDB", "DICOM", "FHIR"],
            "semantic_search": True,
            "auto_annotation": True
        }
    }

# XML元数据系统兼容性端点
@app.get("/api/v1/xml-system/status")
async def get_xml_system_status():
    """获取XML元数据系统状态"""
    global biomedical_recommender, biomedical_preloader, cache_manager, tool_chain_manager
    
    if not all([biomedical_recommender, biomedical_preloader, cache_manager, tool_chain_manager]):
        raise HTTPException(status_code=500, detail="AI组件未初始化")
    
    status = {
        "biomedical_recommender": biomedical_recommender is not None,
        "biomedical_preloader": biomedical_preloader is not None,
        "cache_manager": cache_manager is not None,
        "tool_chain_manager": tool_chain_manager is not None
    }
    return {
        "xml_system_status": status,
        "ai_components": status,
        "migration_status": "completed",
        "uml_sysml_integration": "active"
    }

@app.post("/api/v1/xml-system/test-recommendation")
async def test_recommendation_engine(request: Dict[str, Any]):
    """测试推荐引擎"""
    global biomedical_recommender, biomedical_preloader, cache_manager, tool_chain_manager
    
    if not all([biomedical_recommender, biomedical_preloader, cache_manager, tool_chain_manager]):
        raise HTTPException(status_code=500, detail="AI组件未初始化")
    
    try:
        recommender = biomedical_recommender
        
        # 测试生物医学工具推荐
        tools = recommender.recommend_biomedical_tools(request)
        
        # 测试建模策略推荐
        strategy = recommender.recommend_modeling_strategy(
            request.get('model_type', 'general'),
            request
        )
        
        return {
            "status": "success",
            "recommended_tools": tools,
            "recommended_strategy": strategy,
            "integration_note": "推荐引擎已集成UML/SysML建模能力",
            "request": request
        }
        
    except Exception as e:
        logger.error(f"推荐引擎测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"推荐引擎测试失败: {str(e)}")

# 保持现有端点的兼容性...
@app.get("/api/v1/biomedical/tools")
async def list_biomedical_tools():
    """列出可用的生物医学工具"""
    global tool_chain_manager
    
    if not tool_chain_manager:
        raise HTTPException(status_code=500, detail="工具链管理器未初始化")
    
    try:
        tools = tool_chain_manager.get_tools_by_category(None)
        return {
            "status": "success",
            "tools": tools,
            "total_count": len(tools),
            "integration_status": "UML/SysML建模系统已集成"
        }
        
    except Exception as e:
        logger.error(f"获取工具列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取工具列表失败: {str(e)}")

# 快速上手示例端点
@app.get("/api/v1/quickstart/examples")
async def get_quickstart_examples():
    """获取快速上手示例"""
    return {
        "examples": {
            "create_protein_model": {
                "description": "创建蛋白质分子模型",
                "steps": [
                    "POST /api/v1/modeling/projects - 创建项目",
                    "POST /api/v1/modeling/projects/{id}/diagrams/sysml - 创建SysML图",
                    "POST /api/v1/modeling/projects/{id}/diagrams/{id}/blocks - 创建蛋白质块"
                ]
            },
            "pathway_analysis": {
                "description": "生物通路分析建模",
                "steps": [
                    "创建项目 (domain: systems_biology)",
                    "创建块定义图",
                    "添加通路块和相互作用",
                    "执行工作流分析"
                ]
            },
            "integrate_tools": {
                "description": "集成生物医学工具",
                "steps": [
                    "GET /api/v1/modeling/tools - 查看可用工具",
                    "POST /api/v1/modeling/projects/{id}/workflows - 执行分析工作流",
                    "GET /api/v1/modeling/workflows/{id}/status - 查看结果"
                ]
            }
        }
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "detail": "内部服务器错误",
            "error": str(exc),
            "platform": "生物医学MBSE建模平台 v2.0"
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 