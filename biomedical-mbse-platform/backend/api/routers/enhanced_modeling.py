"""
增强版建模API路由
===============

集成XML解析、生物医学专业化和增强UML引擎的完整API接口
"""

from fastapi import APIRouter, HTTPException, UploadFile, File, BackgroundTasks
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import sys
import os
import tempfile
import uuid

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from mbse_core.enhanced_uml_engine import EnhancedUMLEngine

router = APIRouter()

# 全局增强引擎实例
enhanced_uml_engine = EnhancedUMLEngine()

# Pydantic模型定义
class EnhancedProjectCreateRequest(BaseModel):
    name: str
    description: Optional[str] = ""
    project_type: str = "biomedical_mbse"
    domain: str = "molecular_biology"
    data_type: Optional[str] = "general"
    size_hint: Optional[str] = "medium"
    enable_xml_integration: bool = True
    biomedical_profile: Optional[str] = "molecular_biology"

class XMLImportRequest(BaseModel):
    import_options: Dict[str, Any] = {
        'apply_biomedical_enhancement': True,
        'standardize_naming': True,
        'validate_model': True,
        'generate_suggestions': True
    }

class XMLExportRequest(BaseModel):
    export_format: str = "xmi"
    export_options: Dict[str, Any] = {
        'include_biomedical_extensions': True,
        'include_validation_info': True,
        'include_suggestions': False
    }

class ModelTransformationRequest(BaseModel):
    transformation_type: str
    transformation_params: Dict[str, Any] = {}

class ModelValidationRequest(BaseModel):
    validate_consistency: bool = True
    validate_biomedical_constraints: bool = True
    generate_report: bool = True

# 增强项目管理API
@router.post("/enhanced/projects", response_model=Dict[str, Any])
async def create_enhanced_project(request: EnhancedProjectCreateRequest):
    """创建增强版生物医学MBSE项目"""
    try:
        project_config = {
            'name': request.name,
            'description': request.description,
            'project_type': request.project_type,
            'domain': request.domain,
            'data_type': request.data_type,
            'size_hint': request.size_hint,
            'enable_xml_integration': request.enable_xml_integration,
            'biomedical_profile': request.biomedical_profile
        }
        
        project_id = await enhanced_uml_engine.create_biomedical_project(project_config)
        
        return {
            'success': True,
            'project_id': project_id,
            'message': f'增强版项目 {request.name} 创建成功',
            'features': {
                'xml_integration': request.enable_xml_integration,
                'biomedical_profile': request.biomedical_profile,
                'enhanced_validation': True,
                'model_transformation': True
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/enhanced/projects/{project_id}/status", response_model=Dict[str, Any])
async def get_enhanced_project_status(project_id: str):
    """获取增强版项目状态"""
    try:
        status = enhanced_uml_engine.get_project_status(project_id)
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# XML导入导出API
@router.post("/enhanced/projects/{project_id}/import/xml", response_model=Dict[str, Any])
async def import_xml_model(project_id: str, file: UploadFile = File(...), 
                          import_options: XMLImportRequest = XMLImportRequest()):
    """导入XML模型文件（XMI、EA项目文件等）"""
    try:
        # 验证项目存在
        project_status = enhanced_uml_engine.get_project_status(project_id)
        if 'error' in project_status:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 验证文件格式
        if not file.filename.lower().endswith(('.xmi', '.xml', '.uml', '.eap')):
            raise HTTPException(status_code=400, detail="不支持的文件格式")
        
        # 保存上传的文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xml') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # 执行导入
            import_result = await enhanced_uml_engine.import_model_from_xml(
                project_id, 
                temp_file_path, 
                import_options.import_options
            )
            
            return {
                'success': import_result['success'],
                'file_name': file.filename,
                'file_size': len(content),
                'import_result': import_result,
                'project_id': project_id
            }
            
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"XML导入失败: {str(e)}")

@router.post("/enhanced/projects/{project_id}/export/xml", response_model=Dict[str, Any])
async def export_xml_model(project_id: str, export_request: XMLExportRequest):
    """导出项目模型为XML格式"""
    try:
        # 验证项目存在
        project_status = enhanced_uml_engine.get_project_status(project_id)
        if 'error' in project_status:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 执行导出
        xml_content = await enhanced_uml_engine.export_model_to_xml(
            project_id,
            export_request.export_format,
            export_request.export_options
        )
        
        # 生成文件名
        project_name = project_status.get('config', {}).get('name', 'project')
        filename = f"{project_name}_{project_id}.{export_request.export_format}"
        
        return {
            'success': True,
            'xml_content': xml_content,
            'filename': filename,
            'export_format': export_request.export_format,
            'content_length': len(xml_content),
            'project_info': {
                'project_id': project_id,
                'diagrams_count': project_status.get('diagrams_count', 0),
                'elements_count': project_status.get('total_elements', 0)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"XML导出失败: {str(e)}")

# 模型验证API
@router.post("/enhanced/projects/{project_id}/validate", response_model=Dict[str, Any])
async def validate_enhanced_model(project_id: str, validation_request: ModelValidationRequest):
    """验证增强版模型"""
    try:
        # 验证项目存在
        project_status = enhanced_uml_engine.get_project_status(project_id)
        if 'error' in project_status:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 执行模型验证
        validation_result = await enhanced_uml_engine.validate_project_model(project_id)
        
        # 生成验证报告
        validation_report = {
            'project_id': project_id,
            'validation_result': validation_result,
            'project_summary': {
                'diagrams_count': project_status.get('diagrams_count', 0),
                'total_elements': project_status.get('total_elements', 0),
                'biomedical_elements': project_status.get('biomedical_elements', 0),
                'biomedical_coverage': 0.0
            }
        }
        
        # 计算生物医学覆盖率
        total_elements = project_status.get('total_elements', 0)
        bio_elements = project_status.get('biomedical_elements', 0)
        if total_elements > 0:
            validation_report['project_summary']['biomedical_coverage'] = bio_elements / total_elements
        
        return validation_report
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模型验证失败: {str(e)}")

# 模型变换API
@router.post("/enhanced/projects/{project_id}/transform", response_model=Dict[str, Any])
async def transform_model(project_id: str, transformation_request: ModelTransformationRequest,
                         background_tasks: BackgroundTasks):
    """执行模型变换"""
    try:
        # 验证项目存在
        project_status = enhanced_uml_engine.get_project_status(project_id)
        if 'error' in project_status:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 执行模型变换
        transformation_result = await enhanced_uml_engine.transform_model(
            project_id,
            transformation_request.transformation_type,
            transformation_request.transformation_params
        )
        
        return {
            'success': transformation_result.get('success', True),
            'transformation_type': transformation_request.transformation_type,
            'transformation_result': transformation_result,
            'project_id': project_id
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"模型变换失败: {str(e)}")

# 生物医学专业化API
@router.post("/enhanced/projects/{project_id}/biomedical/enhance", response_model=Dict[str, Any])
async def enhance_biomedical_features(project_id: str, enhancement_options: Dict[str, Any] = {}):
    """增强生物医学特性"""
    try:
        # 验证项目存在
        project_status = enhanced_uml_engine.get_project_status(project_id)
        if 'error' in project_status:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 获取项目
        project = enhanced_uml_engine.projects.get(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目对象不存在")
        
        # 增强生物医学特性
        await enhanced_uml_engine._enhance_with_biomedical_context(project, project.diagrams)
        
        # 重新获取状态
        updated_status = enhanced_uml_engine.get_project_status(project_id)
        
        return {
            'success': True,
            'project_id': project_id,
            'enhancement_applied': True,
            'before': {
                'biomedical_elements': project_status.get('biomedical_elements', 0),
                'total_elements': project_status.get('total_elements', 0)
            },
            'after': {
                'biomedical_elements': updated_status.get('biomedical_elements', 0),
                'total_elements': updated_status.get('total_elements', 0)
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生物医学增强失败: {str(e)}")

# 建模建议API
@router.get("/enhanced/projects/{project_id}/suggestions", response_model=List[Dict[str, Any]])
async def get_modeling_suggestions(project_id: str):
    """获取AI建模建议"""
    try:
        # 验证项目存在
        project_status = enhanced_uml_engine.get_project_status(project_id)
        if 'error' in project_status:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 获取项目
        project = enhanced_uml_engine.projects.get(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目对象不存在")
        
        # 生成建议
        suggestions = await enhanced_uml_engine._generate_modeling_suggestions(project)
        
        return suggestions
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取建议失败: {str(e)}")

# 变换历史API
@router.get("/enhanced/projects/{project_id}/history", response_model=List[Dict[str, Any]])
async def get_transformation_history(project_id: str):
    """获取项目变换历史"""
    try:
        # 过滤项目相关的历史记录
        project_history = [
            record for record in enhanced_uml_engine.transformation_history
            if record.get('project_id') == project_id
        ]
        
        return project_history
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")

# 系统能力API
@router.get("/enhanced/capabilities", response_model=Dict[str, Any])
async def get_enhanced_capabilities():
    """获取增强版系统能力"""
    return {
        "xml_integration": {
            "supported_formats": ["XMI", "UML", "EA Project Files", "ArchiMate"],
            "import_features": [
                "语义分析", "自动元素分类", "关系推断", "生物医学检测"
            ],
            "export_features": [
                "标准XMI导出", "生物医学扩展", "验证信息", "建议包含"
            ]
        },
        "biomedical_specialization": {
            "supported_domains": ["molecular_biology", "systems_biology", "cell_biology"],
            "auto_detection": True,
            "constraint_solving": True,
            "standard_attributes": True
        },
        "model_validation": {
            "uml_compliance": True,
            "sysml_compliance": True,
            "biomedical_constraints": True,
            "consistency_checking": True
        },
        "model_transformation": {
            "supported_transformations": [
                "extract_interfaces", "merge_diagrams", "split_diagram",
                "apply_pattern", "refactor_naming"
            ],
            "ai_suggestions": True,
            "history_tracking": True
        },
        "ai_integration": {
            "xml_metadata_bridge": enhanced_uml_engine.xml_metadata_bridge.is_connected,
            "recommendation_engine": True,
            "intelligent_preprocessing": True,
            "context_analysis": True
        }
    }

# 批量操作API
@router.post("/enhanced/projects/batch/create", response_model=List[Dict[str, Any]])
async def create_projects_batch(projects: List[EnhancedProjectCreateRequest]):
    """批量创建项目"""
    results = []
    
    for project_request in projects:
        try:
            project_config = {
                'name': project_request.name,
                'description': project_request.description,
                'project_type': project_request.project_type,
                'domain': project_request.domain,
                'data_type': project_request.data_type,
                'size_hint': project_request.size_hint,
                'enable_xml_integration': project_request.enable_xml_integration,
                'biomedical_profile': project_request.biomedical_profile
            }
            
            project_id = await enhanced_uml_engine.create_biomedical_project(project_config)
            
            results.append({
                'success': True,
                'project_id': project_id,
                'project_name': project_request.name
            })
            
        except Exception as e:
            results.append({
                'success': False,
                'project_name': project_request.name,
                'error': str(e)
            })
    
    return results

# 高级查询API
@router.post("/enhanced/projects/search", response_model=Dict[str, Any])
async def search_projects(search_criteria: Dict[str, Any]):
    """高级项目搜索"""
    try:
        matching_projects = []
        
        for project_id, project in enhanced_uml_engine.projects.items():
            # 简化的搜索逻辑
            if _matches_criteria(project, search_criteria):
                matching_projects.append({
                    'project_id': project_id,
                    'status': project.get_status()
                })
        
        return {
            'success': True,
            'total_results': len(matching_projects),
            'projects': matching_projects,
            'search_criteria': search_criteria
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

def _matches_criteria(project, criteria: Dict[str, Any]) -> bool:
    """检查项目是否匹配搜索条件"""
    # 简化的匹配逻辑
    if 'domain' in criteria:
        if project.config.get('domain') != criteria['domain']:
            return False
    
    if 'project_type' in criteria:
        if project.config.get('project_type') != criteria['project_type']:
            return False
    
    if 'biomedical_profile' in criteria:
        if project.biomedical_profile.get('name') != criteria['biomedical_profile']:
            return False
    
    return True

# 性能监控API
@router.get("/enhanced/performance", response_model=Dict[str, Any])
async def get_performance_metrics():
    """获取性能指标"""
    return {
        'total_projects': len(enhanced_uml_engine.projects),
        'total_transformations': len(enhanced_uml_engine.transformation_history),
        'xml_bridge_status': enhanced_uml_engine.xml_metadata_bridge.is_connected,
        'memory_usage': {
            'projects_in_memory': len(enhanced_uml_engine.projects),
            'cache_size': len(enhanced_uml_engine.model_cache)
        },
        'recent_activity': enhanced_uml_engine.transformation_history[-5:] if enhanced_uml_engine.transformation_history else []
    } 