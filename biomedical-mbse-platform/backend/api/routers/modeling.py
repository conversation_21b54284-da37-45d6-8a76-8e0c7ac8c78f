"""
建模相关API路由
=============

提供UML/SysML建模相关的RESTful API接口
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from mbse_core.engine import IntegratedBiomedicalMBSEEngine

router = APIRouter()

# 全局引擎实例
mbse_engine = IntegratedBiomedicalMBSEEngine()

# Pydantic模型定义
class ProjectCreateRequest(BaseModel):
    name: str
    description: Optional[str] = ""
    project_type: str = "general_biomedical"
    domain: str = "molecular_biology"
    data_type: Optional[str] = "general"
    size_hint: Optional[str] = "medium"

class DiagramCreateRequest(BaseModel):
    name: str
    diagram_type: str
    domain: str = "biomedical"

class BlockCreateRequest(BaseModel):
    name: str
    biological_type: Optional[str] = None
    auto_configure: bool = True
    add_standard_properties: bool = True
    add_experimental_data: bool = False
    auto_annotate: bool = True

class WorkflowExecuteRequest(BaseModel):
    analysis_type: str
    data_format: str = "unknown"
    input_data: Any = None
    target_diagram_id: Optional[str] = None
    update_models: bool = True
    tools: Optional[List[str]] = None

class KnowledgeIntegrationRequest(BaseModel):
    document_ids: List[str]

# 项目管理API
@router.post("/projects", response_model=Dict[str, Any])
async def create_project(request: ProjectCreateRequest):
    """创建新的生物医学MBSE项目"""
    try:
        project_config = {
            'name': request.name,
            'description': request.description,
            'project_type': request.project_type,
            'domain': request.domain,
            'data_type': request.data_type,
            'size_hint': request.size_hint
        }
        
        project_id = await mbse_engine.create_biomedical_project(project_config)
        
        return {
            'success': True,
            'project_id': project_id,
            'message': f'项目 {request.name} 创建成功'
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects/{project_id}/status", response_model=Dict[str, Any])
async def get_project_status(project_id: str):
    """获取项目状态"""
    try:
        status = await mbse_engine.get_project_status(project_id)
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects/{project_id}/diagrams", response_model=List[Dict[str, Any]])
async def list_project_diagrams(project_id: str):
    """列出项目中的所有图形"""
    try:
        project = mbse_engine.active_projects.get(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        diagrams = project.list_diagrams()
        return diagrams
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# UML图形管理API
@router.post("/projects/{project_id}/diagrams/uml", response_model=Dict[str, Any])
async def create_uml_diagram(project_id: str, request: DiagramCreateRequest):
    """创建UML图形"""
    try:
        result = await mbse_engine.create_uml_diagram(
            project_id, request.diagram_type, request.name, request.domain
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# SysML图形管理API
@router.post("/projects/{project_id}/diagrams/sysml", response_model=Dict[str, Any])
async def create_sysml_diagram(project_id: str, request: DiagramCreateRequest):
    """创建SysML图形"""
    try:
        result = await mbse_engine.create_sysml_diagram(
            project_id, request.diagram_type, request.name, request.domain
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects/{project_id}/diagrams/{diagram_id}", response_model=Dict[str, Any])
async def get_diagram(project_id: str, diagram_id: str):
    """获取图形详情"""
    try:
        project = mbse_engine.active_projects.get(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        diagram = project.get_diagram(diagram_id)
        if not diagram:
            raise HTTPException(status_code=404, detail="图形不存在")
        
        return {
            'diagram': diagram.to_dict(),
            'project_id': project_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# SysML块管理API
@router.post("/projects/{project_id}/diagrams/{diagram_id}/blocks", response_model=Dict[str, Any])
async def create_biomedical_block(project_id: str, diagram_id: str, request: BlockCreateRequest):
    """创建生物医学SysML块"""
    try:
        block_config = {
            'name': request.name,
            'biological_type': request.biological_type,
            'auto_configure': request.auto_configure,
            'add_standard_properties': request.add_standard_properties,
            'add_experimental_data': request.add_experimental_data,
            'auto_annotate': request.auto_annotate
        }
        
        result = await mbse_engine.create_biomedical_block(
            project_id, diagram_id, block_config
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 工作流管理API
@router.post("/projects/{project_id}/workflows", response_model=Dict[str, Any])
async def execute_workflow(project_id: str, request: WorkflowExecuteRequest, 
                          background_tasks: BackgroundTasks):
    """执行生物医学分析工作流"""
    try:
        workflow_config = {
            'analysis_type': request.analysis_type,
            'data_format': request.data_format,
            'input_data': request.input_data,
            'target_diagram_id': request.target_diagram_id,
            'update_models': request.update_models
        }
        
        if request.tools:
            workflow_config['tools'] = request.tools
        
        workflow_id = await mbse_engine.execute_integrated_workflow(
            project_id, workflow_config
        )
        
        return {
            'success': True,
            'workflow_id': workflow_id,
            'message': '工作流已启动'
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/workflows/{workflow_id}/status", response_model=Dict[str, Any])
async def get_workflow_status(workflow_id: str):
    """获取工作流状态"""
    try:
        workflow = mbse_engine.running_workflows.get(workflow_id)
        if not workflow:
            raise HTTPException(status_code=404, detail="工作流不存在")
        
        return {
            'workflow_id': workflow_id,
            'status': workflow['status'],
            'project_id': workflow['project_id'],
            'timestamp': workflow['timestamp'].isoformat(),
            'result': workflow.get('result'),
            'error': workflow.get('error')
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 知识集成API
@router.post("/projects/{project_id}/diagrams/{diagram_id}/knowledge", response_model=Dict[str, Any])
async def integrate_knowledge(project_id: str, diagram_id: str, 
                            request: KnowledgeIntegrationRequest):
    """集成知识上下文到图形"""
    try:
        result = await mbse_engine.integrate_knowledge_context(
            project_id, diagram_id, request.document_ids
        )
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 工具和推荐API
@router.get("/tools", response_model=List[Dict[str, Any]])
async def list_available_tools():
    """列出可用的生物医学工具"""
    try:
        tools = await mbse_engine.list_available_tools()
        return tools
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/recommendations", response_model=List[Dict[str, Any]])
async def get_recommendations(context: Dict[str, Any]):
    """获取AI推荐"""
    try:
        recommendations = await mbse_engine.get_recommendations(context)
        return recommendations
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 系统状态API
@router.get("/engine/status", response_model=Dict[str, Any])
async def get_engine_status():
    """获取引擎状态"""
    try:
        status = mbse_engine.get_engine_status()
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 图形验证API
@router.post("/diagrams/{diagram_id}/validate", response_model=Dict[str, Any])
async def validate_diagram(diagram_id: str, project_id: str):
    """验证图形"""
    try:
        project = mbse_engine.active_projects.get(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        diagram = project.get_diagram(diagram_id)
        if not diagram:
            raise HTTPException(status_code=404, detail="图形不存在")
        
        validation_result = mbse_engine.uml_engine.validate_diagram(diagram)
        
        return {
            'diagram_id': diagram_id,
            'validation_result': validation_result,
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 约束求解API
@router.post("/projects/{project_id}/diagrams/{diagram_id}/blocks/{block_id}/solve", 
            response_model=Dict[str, Any])
async def solve_block_constraints(project_id: str, diagram_id: str, block_id: str,
                                constraint_values: Dict[str, Any]):
    """求解SysML块约束"""
    try:
        project = mbse_engine.active_projects.get(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        diagram = project.get_diagram(diagram_id)
        if not diagram:
            raise HTTPException(status_code=404, detail="图形不存在")
        
        # 查找块
        block = None
        for element in diagram.elements:
            if element.id == block_id and hasattr(element, 'constraint_properties'):
                block = element
                break
        
        if not block:
            raise HTTPException(status_code=404, detail="块不存在")
        
        # 求解约束
        solution = mbse_engine.sysml_engine.solve_constraints(block, constraint_values)
        
        return {
            'block_id': block_id,
            'solution': solution,
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 