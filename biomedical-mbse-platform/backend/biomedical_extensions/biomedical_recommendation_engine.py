"""
生物医学推荐引擎
================

基于XML元数据系统推荐引擎，扩展支持177个生物医学工具的智能推荐。
"""

from typing import Dict, List, Any
import sys
import os

# 添加AI引擎路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'ai_engines'))

try:
    from intelligence.insights.recommendation_engine import RecommendationEngine
except ImportError:
    # 如果无法导入，使用Mock实现
    class RecommendationEngine:
        def recommend_biomedical_tools(self, config):
            return []

class BiomedicalRecommendationEngine:
    """生物医学推荐引擎"""
    
    def __init__(self):
        self.base_engine = RecommendationEngine()
        self.biomedical_tools = self._load_biomedical_tools()
    
    def _load_biomedical_tools(self) -> Dict[str, Any]:
        """加载177个生物医学工具配置"""
        return {
            "molecular_visualization": ["PyMOL", "ChimeraX", "VMD"],
            "statistical_analysis": ["SPSS", "R", "GraphPad", "Origin"],
            "sequence_analysis": ["BLAST", "ClustalW", "MEGA"],
            "molecular_dynamics": ["GROMACS", "NAMD", "AMBER"],
            "drug_discovery": ["AutoDock", "Schrödinger", "MOE"],
            # ... 更多工具
        }
    
    def recommend_tools_for_analysis(self, analysis_type: str, data_format: str) -> List[Dict]:
        """为特定分析类型推荐工具"""
        recommendations = []
        
        if analysis_type == "protein_structure_analysis":
            recommendations = [
                {"name": "PyMOL", "confidence": 0.95, "reason": "分子可视化标准工具"},
                {"name": "ChimeraX", "confidence": 0.90, "reason": "高级结构分析功能"}
            ]
        elif analysis_type == "statistical_analysis":
            recommendations = [
                {"name": "SPSS", "confidence": 0.92, "reason": "生物统计分析专业软件"},
                {"name": "GraphPad", "confidence": 0.88, "reason": "生物医学图表制作"}
            ]
        
        return recommendations
    
    def recommend_workflow(self, project_type: str) -> Dict[str, Any]:
        """推荐完整的工作流"""
        workflows = {
            "drug_discovery": {
                "steps": [
                    {"tool": "PyMOL", "action": "蛋白质结构分析"},
                    {"tool": "AutoDock", "action": "分子对接分析"},
                    {"tool": "SPSS", "action": "结果统计分析"}
                ],
                "estimated_time": "2-4小时",
                "complexity": "中等"
            }
        }
        
        return workflows.get(project_type, {})
