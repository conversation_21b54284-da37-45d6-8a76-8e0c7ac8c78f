"""
生物医学工具链管理器
==================

管理177个生物医学工具的集成、调度和执行。
支持工具链编排、性能监控和故障恢复。
"""

from typing import Dict, List, Any, Optional
import asyncio
import logging
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class ToolStatus(Enum):
    """工具状态枚举"""
    AVAILABLE = "available"
    BUSY = "busy"
    ERROR = "error"
    OFFLINE = "offline"

class ToolCategory(Enum):
    """工具分类枚举"""
    MOLECULAR_MODELING = "molecular_modeling"
    DATA_ANALYSIS = "data_analysis"
    VISUALIZATION = "visualization"
    SIMULATION = "simulation"
    DATABASE = "database"
    BIOINFORMATICS = "bioinformatics"

class ToolChainManager:
    """生物医学工具链管理器"""
    
    def __init__(self):
        self.available_tools = {}
        self.tool_chains = {}
        self.execution_history = []
        self.performance_metrics = {}
        self._setup_integrations()
        logger.info("工具链管理器初始化完成")
    
    def _setup_integrations(self):
        """设置工具集成配置"""
        # 预定义工具配置
        self._register_default_tools()
        
        # 设置工具间依赖关系
        self._setup_tool_dependencies()
        
        # 初始化性能监控
        self._initialize_performance_monitoring()
        
        logger.info("工具集成设置完成")
    
    def _register_default_tools(self):
        """注册默认工具"""
        default_tools = {
            # 分子建模工具
            "PyMOL": {
                "category": ToolCategory.MOLECULAR_MODELING,
                "status": ToolStatus.AVAILABLE,
                "description": "蛋白质结构可视化",
                "supported_formats": ["pdb", "mol2", "sdf"],
                "performance_score": 0.9
            },
            "ChimeraX": {
                "category": ToolCategory.MOLECULAR_MODELING,
                "status": ToolStatus.AVAILABLE,
                "description": "分子可视化和分析",
                "supported_formats": ["pdb", "mmcif"],
                "performance_score": 0.85
            },
            
            # 数据分析工具
            "SPSS": {
                "category": ToolCategory.DATA_ANALYSIS,
                "status": ToolStatus.AVAILABLE,
                "description": "统计分析软件",
                "supported_formats": ["csv", "xlsx", "sav"],
                "performance_score": 0.8
            },
            "R": {
                "category": ToolCategory.DATA_ANALYSIS,
                "status": ToolStatus.AVAILABLE,
                "description": "统计计算和图形",
                "supported_formats": ["csv", "rdata"],
                "performance_score": 0.9
            },
            
            # 生物信息学工具
            "BLAST": {
                "category": ToolCategory.BIOINFORMATICS,
                "status": ToolStatus.AVAILABLE,
                "description": "序列相似性搜索",
                "supported_formats": ["fasta", "genbank"],
                "performance_score": 0.95
            },
            
            # 可视化工具
            "Matplotlib": {
                "category": ToolCategory.VISUALIZATION,
                "status": ToolStatus.AVAILABLE,
                "description": "Python绘图库",
                "supported_formats": ["csv", "json"],
                "performance_score": 0.85
            }
        }
        
        for tool_name, config in default_tools.items():
            self.available_tools[tool_name] = {
                **config,
                "last_used": None,
                "usage_count": 0,
                "average_execution_time": 0.0
            }
    
    def _setup_tool_dependencies(self):
        """设置工具依赖关系"""
        self.tool_dependencies = {
            "PyMOL": ["python", "numpy"],
            "ChimeraX": [],
            "SPSS": [],
            "R": ["Rcpp"],
            "BLAST": ["ncbi-blast+"],
            "Matplotlib": ["python", "numpy"]
        }
    
    def _initialize_performance_monitoring(self):
        """初始化性能监控"""
        self.performance_metrics = {
            "total_executions": 0,
            "successful_executions": 0,
            "average_execution_time": 0.0,
            "last_execution": None,
            "tool_usage_stats": {}
        }
    
    def get_tools_by_category(self, category: Optional[ToolCategory]) -> List[Dict[str, Any]]:
        """根据分类获取工具列表"""
        if category is None:
            # 返回所有工具
            return [
                {
                    "name": name,
                    "category": tool["category"].value if hasattr(tool["category"], 'value') else str(tool["category"]),
                    "status": tool["status"].value if hasattr(tool["status"], 'value') else str(tool["status"]),
                    "description": tool["description"],
                    "performance_score": tool["performance_score"]
                }
                for name, tool in self.available_tools.items()
            ]
        else:
            # 按分类筛选
            filtered_tools = [
                {
                    "name": name,
                    "category": tool["category"].value if hasattr(tool["category"], 'value') else str(tool["category"]),
                    "status": tool["status"].value if hasattr(tool["status"], 'value') else str(tool["status"]),
                    "description": tool["description"],
                    "performance_score": tool["performance_score"]
                }
                for name, tool in self.available_tools.items()
                if tool["category"] == category
            ]
            return filtered_tools
    
    def execute_tool_chain(self, tool_names: List[str], input_data: Any) -> Dict[str, Any]:
        """执行工具链"""
        execution_id = f"exec_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        
        results = {}
        current_data = input_data
        
        try:
            for tool_name in tool_names:
                if tool_name not in self.available_tools:
                    raise ValueError(f"工具不存在: {tool_name}")
                
                tool = self.available_tools[tool_name]
                if tool["status"] != ToolStatus.AVAILABLE:
                    raise RuntimeError(f"工具不可用: {tool_name}, 状态: {tool['status']}")
                
                # 模拟工具执行
                tool_result = self._execute_single_tool(tool_name, current_data)
                results[tool_name] = tool_result
                
                # 更新输入数据为上一步的输出
                current_data = tool_result.get("output", current_data)
                
                # 更新工具使用统计
                self._update_tool_usage(tool_name)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 记录执行历史
            execution_record = {
                "execution_id": execution_id,
                "tools": tool_names,
                "start_time": start_time.isoformat(),
                "execution_time": execution_time,
                "status": "success",
                "results": results
            }
            
            self.execution_history.append(execution_record)
            self._update_performance_metrics(execution_time, True)
            
            return {
                "execution_id": execution_id,
                "status": "success",
                "execution_time": execution_time,
                "results": results,
                "final_output": current_data
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            
            error_record = {
                "execution_id": execution_id,
                "tools": tool_names,
                "start_time": start_time.isoformat(),
                "execution_time": execution_time,
                "status": "error",
                "error": str(e)
            }
            
            self.execution_history.append(error_record)
            self._update_performance_metrics(execution_time, False)
            
            return {
                "execution_id": execution_id,
                "status": "error",
                "error": str(e),
                "execution_time": execution_time
            }
    
    def _execute_single_tool(self, tool_name: str, input_data: Any) -> Dict[str, Any]:
        """执行单个工具（模拟）"""
        tool = self.available_tools[tool_name]
        
        # 模拟工具执行时间
        import time
        execution_time = 0.1 + (tool["performance_score"] * 0.1)  # 模拟执行时间
        time.sleep(execution_time)
        
        # 模拟工具输出
        mock_output = {
            "tool": tool_name,
            "input_processed": True,
            "output": f"processed_by_{tool_name.lower()}",
            "metadata": {
                "execution_time": execution_time,
                "tool_version": "1.0.0",
                "parameters_used": {}
            }
        }
        
        return mock_output
    
    def _update_tool_usage(self, tool_name: str):
        """更新工具使用统计"""
        if tool_name in self.available_tools:
            self.available_tools[tool_name]["usage_count"] += 1
            self.available_tools[tool_name]["last_used"] = datetime.now().isoformat()
    
    def _update_performance_metrics(self, execution_time: float, success: bool):
        """更新性能指标"""
        self.performance_metrics["total_executions"] += 1
        if success:
            self.performance_metrics["successful_executions"] += 1
        
        # 更新平均执行时间
        total = self.performance_metrics["total_executions"]
        prev_avg = self.performance_metrics["average_execution_time"]
        self.performance_metrics["average_execution_time"] = (prev_avg * (total - 1) + execution_time) / total
        
        self.performance_metrics["last_execution"] = datetime.now().isoformat()
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        success_rate = 0.0
        if self.performance_metrics["total_executions"] > 0:
            success_rate = self.performance_metrics["successful_executions"] / self.performance_metrics["total_executions"]
        
        return {
            **self.performance_metrics,
            "success_rate": success_rate,
            "available_tools_count": len(self.available_tools),
            "most_used_tools": self._get_most_used_tools()
        }
    
    def _get_most_used_tools(self) -> List[Dict[str, Any]]:
        """获取最常用的工具"""
        tools_usage = [
            {
                "name": name,
                "usage_count": tool["usage_count"],
                "last_used": tool["last_used"]
            }
            for name, tool in self.available_tools.items()
            if tool["usage_count"] > 0
        ]
        
        return sorted(tools_usage, key=lambda x: x["usage_count"], reverse=True)[:5]
    
    def register_tool(self, name: str, config: Dict[str, Any]) -> bool:
        """注册新工具"""
        try:
            self.available_tools[name] = {
                "category": config.get("category", ToolCategory.DATA_ANALYSIS),
                "status": config.get("status", ToolStatus.AVAILABLE),
                "description": config.get("description", ""),
                "supported_formats": config.get("supported_formats", []),
                "performance_score": config.get("performance_score", 0.5),
                "last_used": None,
                "usage_count": 0,
                "average_execution_time": 0.0
            }
            logger.info(f"工具注册成功: {name}")
            return True
        except Exception as e:
            logger.error(f"工具注册失败: {name}, 错误: {e}")
            return False
