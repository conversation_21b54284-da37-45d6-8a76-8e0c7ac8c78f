"""
适配器模块 - 外部格式接口适配

提供与外部系统和格式的交互接口：
- JSON适配器 - JSON数据格式转换
- XML适配器 - XML数据格式转换  
- CSV适配器 - CSV数据格式转换
- 数据库适配器 - 数据库系统集成
- API适配器 - RESTful API接口
"""

from typing import Dict, List, Any, Optional, Union, Type
import logging

# 适配器基类
from .base_adapter import BaseAdapter, AdapterResult, AdapterConfig

# 具体适配器
from .json_adapter import JSONAdapter
from .xml_adapter import XMLAdapter  
from .csv_adapter import CSVAdapter
from .database_adapter import DatabaseAdapter
from .api_adapter import APIAdapter

# 版本信息
__version__ = "3.1.0"
__author__ = "XML元数据系统团队"

# 模块日志
logger = logging.getLogger(__name__)

# 适配器注册表
ADAPTER_REGISTRY: Dict[str, Type[BaseAdapter]] = {
    'json': JSONAdapter,
    'xml': XMLAdapter,
    'csv': CSVAdapter,
    'database': DatabaseAdapter,
    'api': APIAdapter
}

def create_adapter(adapter_type: str, config: Optional[AdapterConfig] = None) -> Optional[BaseAdapter]:
    """
    适配器工厂函数
    
    Args:
        adapter_type: 适配器类型
        config: 适配器配置
        
    Returns:
        创建的适配器实例
    """
    try:
        if adapter_type.lower() in ADAPTER_REGISTRY:
            adapter_class = ADAPTER_REGISTRY[adapter_type.lower()]
            return adapter_class(config or AdapterConfig())
        else:
            logger.error(f"未知的适配器类型: {adapter_type}")
            return None
    except Exception as e:
        logger.error(f"创建适配器 {adapter_type} 时发生错误: {e}")
        return None

def get_supported_formats() -> List[str]:
    """获取支持的格式列表"""
    return list(ADAPTER_REGISTRY.keys())

def register_adapter(name: str, adapter_class: Type[BaseAdapter]) -> bool:
    """
    注册新的适配器类型
    
    Args:
        name: 适配器名称
        adapter_class: 适配器类
        
    Returns:
        是否注册成功
    """
    try:
        if not issubclass(adapter_class, BaseAdapter):
            logger.error(f"适配器类 {adapter_class} 必须继承自BaseAdapter")
            return False
        
        ADAPTER_REGISTRY[name.lower()] = adapter_class
        logger.info(f"成功注册适配器类型: {name}")
        return True
        
    except Exception as e:
        logger.error(f"注册适配器类型 {name} 时发生错误: {e}")
        return False

def get_adapter_info(adapter_type: str) -> Optional[Dict[str, Any]]:
    """
    获取适配器信息
    
    Args:
        adapter_type: 适配器类型
        
    Returns:
        适配器信息字典
    """
    if adapter_type.lower() not in ADAPTER_REGISTRY:
        return None
    
    adapter_class = ADAPTER_REGISTRY[adapter_type.lower()]
    
    return {
        'name': adapter_type,
        'class_name': adapter_class.__name__,
        'module': adapter_class.__module__,
        'docstring': adapter_class.__doc__,
        'supported_operations': getattr(adapter_class, 'SUPPORTED_OPERATIONS', [])
    }

# 便捷适配器函数
def unified_adapter(config: Optional[AdapterConfig] = None) -> Optional[BaseAdapter]:
    """
    统一适配器 - 通用数据格式转换
    优先使用JSON适配器作为统一格式
    """
    return create_adapter('json', config)

def legacy_adapter(config: Optional[AdapterConfig] = None) -> Optional[BaseAdapter]:
    """
    遗留系统适配器 - 兼容旧格式
    使用XML适配器处理遗留XML格式
    """
    return create_adapter('xml', config)

# 导出所有公共接口
__all__ = [
    # 基础类
    'BaseAdapter',
    'AdapterResult', 
    'AdapterConfig',
    
    # 具体适配器
    'JSONAdapter',
    'XMLAdapter',
    'CSVAdapter',
    'DatabaseAdapter',
    'APIAdapter',
    
    # 便捷适配器
    'unified_adapter',
    'legacy_adapter',
    
    # 工厂和工具函数
    'create_adapter',
    'get_supported_formats',
    'register_adapter',
    'get_adapter_info'
]

# 模块初始化日志
logger.info(f"适配器模块已加载 v{__version__}")
logger.info(f"已注册 {len(ADAPTER_REGISTRY)} 个适配器类型") 