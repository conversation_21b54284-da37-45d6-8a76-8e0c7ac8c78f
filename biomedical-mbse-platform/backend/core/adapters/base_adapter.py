"""
基础适配器 - 所有适配器的抽象基类

定义适配器的通用接口和功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging

class AdapterOperation(Enum):
    """适配器操作类型"""
    READ = "read"           # 读取数据
    WRITE = "write"         # 写入数据
    UPDATE = "update"       # 更新数据
    DELETE = "delete"       # 删除数据
    SYNC = "sync"          # 同步数据
    VALIDATE = "validate"   # 验证数据
    TRANSFORM = "transform" # 数据转换

class AdapterStatus(Enum):
    """适配器状态"""
    DISCONNECTED = "disconnected"   # 未连接
    CONNECTING = "connecting"       # 连接中
    CONNECTED = "connected"         # 已连接
    ERROR = "error"                # 错误状态
    BUSY = "busy"                  # 忙碌中

@dataclass
class AdapterConfig:
    """适配器配置"""
    # 基础配置
    name: str = ""
    description: str = ""
    timeout: int = 30  # 超时时间（秒）
    max_retries: int = 3  # 最大重试次数
    
    # 连接配置
    connection_string: str = ""
    username: str = ""
    password: str = ""
    host: str = ""
    port: int = 0
    
    # 数据配置
    encoding: str = "utf-8"
    format_options: Dict[str, Any] = field(default_factory=dict)
    
    # 高级配置
    batch_size: int = 1000
    enable_cache: bool = True
    cache_ttl: int = 300  # 缓存TTL（秒）
    
    # 扩展配置
    custom_options: Dict[str, Any] = field(default_factory=dict)

@dataclass 
class AdapterResult:
    """适配器操作结果"""
    success: bool = True
    operation: Optional[AdapterOperation] = None
    data: Any = None
    message: str = ""
    error: Optional[Exception] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 性能指标
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: float = 0.0  # 执行时间（秒）
    
    # 数据统计
    records_processed: int = 0
    records_success: int = 0
    records_failed: int = 0

class BaseAdapter(ABC):
    """
    基础适配器抽象类
    
    所有适配器的共同基类，定义：
    - 连接管理
    - 数据操作接口
    - 错误处理
    - 性能监控
    """
    
    # 支持的操作类型（子类应重写）
    SUPPORTED_OPERATIONS: List[AdapterOperation] = []
    
    def __init__(self, config: AdapterConfig):
        self.config = config
        self.status = AdapterStatus.DISCONNECTED
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        
        # 连接信息
        self._connection = None
        self._last_connected: Optional[datetime] = None
        self._connection_errors: List[str] = []
        
        # 性能监控
        self._operation_count = 0
        self._total_processing_time = 0.0
        self._last_operation_time: Optional[datetime] = None
        
        # 缓存
        self._cache: Dict[str, Any] = {} if config.enable_cache else None
        self._cache_timestamps: Dict[str, datetime] = {}
        
        # 初始化
        self._initialize()
    
    def _initialize(self):
        """初始化适配器（子类可重写）"""
        self.logger.info(f"初始化适配器: {self.__class__.__name__}")
    
    @abstractmethod
    def connect(self) -> AdapterResult:
        """
        建立连接
        
        Returns:
            连接结果
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> AdapterResult:
        """
        断开连接
        
        Returns:
            断开结果
        """
        pass
    
    @abstractmethod
    def read(self, source: str, **kwargs) -> AdapterResult:
        """
        读取数据
        
        Args:
            source: 数据源标识
            **kwargs: 额外参数
            
        Returns:
            读取结果
        """
        pass
    
    @abstractmethod
    def write(self, destination: str, data: Any, **kwargs) -> AdapterResult:
        """
        写入数据
        
        Args:
            destination: 目标标识
            data: 要写入的数据
            **kwargs: 额外参数
            
        Returns:
            写入结果
        """
        pass
    
    def update(self, target: str, data: Any, **kwargs) -> AdapterResult:
        """
        更新数据（默认实现，子类可重写）
        
        Args:
            target: 目标标识
            data: 更新数据
            **kwargs: 额外参数
            
        Returns:
            更新结果
        """
        if AdapterOperation.UPDATE not in self.SUPPORTED_OPERATIONS:
            return AdapterResult(
                success=False,
                operation=AdapterOperation.UPDATE,
                message="此适配器不支持更新操作"
            )
        
        # 默认实现：先读取，再写入
        read_result = self.read(target, **kwargs)
        if not read_result.success:
            return read_result
        
        return self.write(target, data, **kwargs)
    
    def delete(self, target: str, **kwargs) -> AdapterResult:
        """
        删除数据（默认实现，子类可重写）
        
        Args:
            target: 目标标识
            **kwargs: 额外参数
            
        Returns:
            删除结果
        """
        return AdapterResult(
            success=False,
            operation=AdapterOperation.DELETE,
            message="此适配器不支持删除操作"
        )
    
    def validate(self, data: Any, **kwargs) -> AdapterResult:
        """
        验证数据（默认实现，子类可重写）
        
        Args:
            data: 要验证的数据
            **kwargs: 额外参数
            
        Returns:
            验证结果
        """
        return AdapterResult(
            success=True,
            operation=AdapterOperation.VALIDATE,
            data=True,
            message="数据验证通过（使用默认验证）"
        )
    
    def transform(self, data: Any, target_format: str, **kwargs) -> AdapterResult:
        """
        数据转换（默认实现，子类可重写）
        
        Args:
            data: 要转换的数据
            target_format: 目标格式
            **kwargs: 额外参数
            
        Returns:
            转换结果
        """
        return AdapterResult(
            success=False,
            operation=AdapterOperation.TRANSFORM,
            message="此适配器不支持数据转换操作"
        )
    
    def test_connection(self) -> AdapterResult:
        """
        测试连接
        
        Returns:
            测试结果
        """
        try:
            if self.status == AdapterStatus.CONNECTED:
                return AdapterResult(
                    success=True,
                    message="连接正常"
                )
            
            # 尝试连接
            connect_result = self.connect()
            if connect_result.success:
                # 连接成功后断开
                self.disconnect()
                return AdapterResult(
                    success=True,
                    message="连接测试成功"
                )
            else:
                return connect_result
                
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return AdapterResult(
                success=False,
                error=e,
                message=f"连接测试失败: {str(e)}"
            )
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取适配器状态信息
        
        Returns:
            状态信息字典
        """
        return {
            'adapter_type': self.__class__.__name__,
            'status': self.status.value,
            'config_name': self.config.name,
            'last_connected': self._last_connected.isoformat() if self._last_connected else None,
            'operation_count': self._operation_count,
            'total_processing_time': self._total_processing_time,
            'average_processing_time': (
                self._total_processing_time / self._operation_count 
                if self._operation_count > 0 else 0.0
            ),
            'last_operation_time': self._last_operation_time.isoformat() if self._last_operation_time else None,
            'connection_errors': self._connection_errors[-5:],  # 最近5个错误
            'cache_enabled': self._cache is not None,
            'cache_size': len(self._cache) if self._cache else 0,
            'supported_operations': [op.value for op in self.SUPPORTED_OPERATIONS]
        }
    
    def clear_cache(self):
        """清空缓存"""
        if self._cache is not None:
            self._cache.clear()
            self._cache_timestamps.clear()
            self.logger.info("缓存已清空")
    
    def _get_from_cache(self, key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if self._cache is None:
            return None
        
        if key not in self._cache:
            return None
        
        # 检查TTL
        if key in self._cache_timestamps:
            cache_time = self._cache_timestamps[key]
            if (datetime.now() - cache_time).total_seconds() > self.config.cache_ttl:
                # 缓存过期
                del self._cache[key]
                del self._cache_timestamps[key]
                return None
        
        return self._cache[key]
    
    def _put_to_cache(self, key: str, value: Any):
        """将数据放入缓存"""
        if self._cache is not None:
            self._cache[key] = value
            self._cache_timestamps[key] = datetime.now()
    
    def _record_operation(self, operation: AdapterOperation, duration: float):
        """记录操作统计"""
        self._operation_count += 1
        self._total_processing_time += duration
        self._last_operation_time = datetime.now()
        
        self.logger.debug(f"操作 {operation.value} 完成，耗时: {duration:.3f}秒")
    
    def _create_result(self, operation: AdapterOperation, success: bool = True, 
                      data: Any = None, message: str = "", error: Optional[Exception] = None,
                      start_time: Optional[datetime] = None, 
                      end_time: Optional[datetime] = None) -> AdapterResult:
        """
        创建操作结果
        
        Args:
            operation: 操作类型
            success: 是否成功
            data: 结果数据
            message: 消息
            error: 错误对象
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            操作结果
        """
        result = AdapterResult(
            success=success,
            operation=operation,
            data=data,
            message=message,
            error=error
        )
        
        # 设置时间信息
        if start_time:
            result.start_time = start_time
        if end_time:
            result.end_time = end_time
        if start_time and end_time:
            result.duration = (end_time - start_time).total_seconds()
            self._record_operation(operation, result.duration)
        
        return result
    
    def _handle_error(self, operation: AdapterOperation, error: Exception, 
                     message: str = "") -> AdapterResult:
        """
        处理错误
        
        Args:
            operation: 操作类型
            error: 错误对象
            message: 额外消息
            
        Returns:
            错误结果
        """
        error_msg = f"{message}: {str(error)}" if message else str(error)
        self.logger.error(f"操作 {operation.value} 失败: {error_msg}")
        
        # 记录连接错误
        if "连接" in error_msg or "connection" in error_msg.lower():
            self._connection_errors.append(f"{datetime.now().isoformat()}: {error_msg}")
            # 只保留最近10个错误
            if len(self._connection_errors) > 10:
                self._connection_errors = self._connection_errors[-10:]
        
        return AdapterResult(
            success=False,
            operation=operation,
            error=error,
            message=error_msg
        )
    
    def _ensure_connected(self) -> AdapterResult:
        """
        确保已连接
        
        Returns:
            连接结果
        """
        if self.status != AdapterStatus.CONNECTED:
            return self.connect()
        
        return AdapterResult(success=True, message="已连接")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(name='{self.config.name}', status={self.status.value})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}("
                f"name='{self.config.name}', "
                f"status={self.status.value}, "
                f"operations={self._operation_count})")

class AdapterManager:
    """适配器管理器"""
    
    def __init__(self):
        self._adapters: Dict[str, BaseAdapter] = {}
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
    
    def register_adapter(self, name: str, adapter: BaseAdapter):
        """注册适配器"""
        self._adapters[name] = adapter
        self.logger.info(f"注册适配器: {name}")
    
    def unregister_adapter(self, name: str) -> bool:
        """注销适配器"""
        if name in self._adapters:
            adapter = self._adapters[name]
            if adapter.status == AdapterStatus.CONNECTED:
                adapter.disconnect()
            del self._adapters[name]
            self.logger.info(f"注销适配器: {name}")
            return True
        return False
    
    def get_adapter(self, name: str) -> Optional[BaseAdapter]:
        """获取适配器"""
        return self._adapters.get(name)
    
    def list_adapters(self) -> List[str]:
        """列出所有适配器名称"""
        return list(self._adapters.keys())
    
    def get_adapters_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有适配器状态"""
        return {name: adapter.get_status() for name, adapter in self._adapters.items()}
    
    def connect_all(self) -> Dict[str, AdapterResult]:
        """连接所有适配器"""
        results = {}
        for name, adapter in self._adapters.items():
            try:
                results[name] = adapter.connect()
            except Exception as e:
                self.logger.error(f"连接适配器 {name} 失败: {e}")
                results[name] = AdapterResult(
                    success=False,
                    error=e,
                    message=f"连接失败: {str(e)}"
                )
        return results
    
    def disconnect_all(self) -> Dict[str, AdapterResult]:
        """断开所有适配器连接"""
        results = {}
        for name, adapter in self._adapters.items():
            try:
                results[name] = adapter.disconnect()
            except Exception as e:
                self.logger.error(f"断开适配器 {name} 失败: {e}")
                results[name] = AdapterResult(
                    success=False,
                    error=e,
                    message=f"断开失败: {str(e)}"
                )
        return results 