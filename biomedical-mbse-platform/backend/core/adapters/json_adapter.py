"""
JSON适配器 - JSON格式数据读写

实现与JSON文件和JSON数据的交互功能
"""

import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

from .base_adapter import BaseAdapter, AdapterResult, AdapterConfig, AdapterOperation, AdapterStatus

class JSONAdapter(BaseAdapter):
    """
    JSON适配器
    
    支持：
    - JSON文件读写
    - JSON数据验证
    - JSON格式转换
    - 批量处理
    """
    
    SUPPORTED_OPERATIONS = [
        AdapterOperation.READ,
        AdapterOperation.WRITE,
        AdapterOperation.UPDATE,
        AdapterOperation.VALIDATE,
        AdapterOperation.TRANSFORM
    ]
    
    def __init__(self, config: AdapterConfig):
        super().__init__(config)
        
        # JSON特定配置
        self.ensure_ascii = self.config.format_options.get('ensure_ascii', False)
        self.indent = self.config.format_options.get('indent', 2)
        self.sort_keys = self.config.format_options.get('sort_keys', True)
        
        # 工作目录
        self.work_dir = Path(self.config.connection_string or ".")
        
    def connect(self) -> AdapterResult:
        """建立连接（确保工作目录存在）"""
        start_time = datetime.now()
        
        try:
            self.status = AdapterStatus.CONNECTING
            
            # 确保工作目录存在
            if not self.work_dir.exists():
                self.work_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"创建工作目录: {self.work_dir}")
            
            # 检查目录权限
            if not os.access(self.work_dir, os.R_OK | os.W_OK):
                raise PermissionError(f"工作目录无读写权限: {self.work_dir}")
            
            self.status = AdapterStatus.CONNECTED
            self._last_connected = datetime.now()
            
            return self._create_result(
                operation=AdapterOperation.READ,  # 连接操作使用READ标识
                success=True,
                message=f"JSON适配器连接成功，工作目录: {self.work_dir}",
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except Exception as e:
            self.status = AdapterStatus.ERROR
            return self._handle_error(AdapterOperation.READ, e, "连接失败")
    
    def disconnect(self) -> AdapterResult:
        """断开连接"""
        start_time = datetime.now()
        
        try:
            # 清空缓存
            self.clear_cache()
            
            self.status = AdapterStatus.DISCONNECTED
            
            return self._create_result(
                operation=AdapterOperation.READ,  # 断开操作使用READ标识
                success=True,
                message="JSON适配器断开连接成功",
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except Exception as e:
            return self._handle_error(AdapterOperation.READ, e, "断开连接失败")
    
    def read(self, source: str, **kwargs) -> AdapterResult:
        """读取JSON数据"""
        start_time = datetime.now()
        
        try:
            # 确保已连接
            connect_result = self._ensure_connected()
            if not connect_result.success:
                return connect_result
            
            # 检查缓存
            cache_key = f"read_{source}"
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return self._create_result(
                    operation=AdapterOperation.READ,
                    success=True,
                    data=cached_data,
                    message=f"从缓存读取JSON数据: {source}",
                    start_time=start_time,
                    end_time=datetime.now()
                )
            
            # 构建文件路径
            file_path = self._resolve_path(source)
            
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 读取JSON文件
            with open(file_path, 'r', encoding=self.config.encoding) as f:
                data = json.load(f)
            
            # 缓存数据
            self._put_to_cache(cache_key, data)
            
            return self._create_result(
                operation=AdapterOperation.READ,
                success=True,
                data=data,
                message=f"成功读取JSON文件: {file_path}",
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except json.JSONDecodeError as e:
            return self._handle_error(AdapterOperation.READ, e, f"JSON格式错误: {source}")
        except Exception as e:
            return self._handle_error(AdapterOperation.READ, e, f"读取失败: {source}")
    
    def write(self, destination: str, data: Any, **kwargs) -> AdapterResult:
        """写入JSON数据"""
        start_time = datetime.now()
        
        try:
            # 确保已连接
            connect_result = self._ensure_connected()
            if not connect_result.success:
                return connect_result
            
            # 构建文件路径
            file_path = self._resolve_path(destination)
            
            # 确保目标目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 备份选项
            backup = kwargs.get('backup', False)
            if backup and file_path.exists():
                backup_path = file_path.with_suffix(f'.bak.{int(datetime.now().timestamp())}')
                file_path.rename(backup_path)
                self.logger.info(f"创建备份文件: {backup_path}")
            
            # 写入JSON文件
            with open(file_path, 'w', encoding=self.config.encoding) as f:
                json.dump(
                    data,
                    f,
                    ensure_ascii=self.ensure_ascii,
                    indent=self.indent,
                    sort_keys=self.sort_keys
                )
            
            # 更新缓存
            cache_key = f"read_{destination}"
            self._put_to_cache(cache_key, data)
            
            return self._create_result(
                operation=AdapterOperation.WRITE,
                success=True,
                data={'file_path': str(file_path), 'size_bytes': file_path.stat().st_size},
                message=f"成功写入JSON文件: {file_path}",
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except Exception as e:
            return self._handle_error(AdapterOperation.WRITE, e, f"写入失败: {destination}")
    
    def validate(self, data: Any, **kwargs) -> AdapterResult:
        """验证JSON数据"""
        start_time = datetime.now()
        
        try:
            # 如果是字符串，尝试解析
            if isinstance(data, str):
                json.loads(data)
            # 如果是其他对象，尝试序列化
            else:
                json.dumps(data)
            
            return self._create_result(
                operation=AdapterOperation.VALIDATE,
                success=True,
                data=True,
                message="JSON数据验证通过",
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except (json.JSONDecodeError, TypeError) as e:
            return self._create_result(
                operation=AdapterOperation.VALIDATE,
                success=False,
                data=False,
                message=f"JSON数据验证失败: {str(e)}",
                error=e,
                start_time=start_time,
                end_time=datetime.now()
            )
    
    def transform(self, data: Any, target_format: str, **kwargs) -> AdapterResult:
        """转换数据格式"""
        start_time = datetime.now()
        
        try:
            if target_format.lower() == 'string':
                # 转换为JSON字符串
                result = json.dumps(
                    data,
                    ensure_ascii=self.ensure_ascii,
                    indent=self.indent,
                    sort_keys=self.sort_keys
                )
            elif target_format.lower() == 'compact':
                # 转换为压缩JSON字符串
                result = json.dumps(data, separators=(',', ':'))
            elif target_format.lower() == 'pretty':
                # 转换为格式化JSON字符串
                result = json.dumps(data, indent=4, sort_keys=True, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的目标格式: {target_format}")
            
            return self._create_result(
                operation=AdapterOperation.TRANSFORM,
                success=True,
                data=result,
                message=f"成功转换为 {target_format} 格式",
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except Exception as e:
            return self._handle_error(AdapterOperation.TRANSFORM, e, f"格式转换失败: {target_format}")
    
    def batch_read(self, sources: List[str], **kwargs) -> AdapterResult:
        """批量读取JSON文件"""
        start_time = datetime.now()
        
        try:
            results = {}
            errors = []
            
            for source in sources:
                read_result = self.read(source, **kwargs)
                if read_result.success:
                    results[source] = read_result.data
                else:
                    errors.append(f"{source}: {read_result.message}")
            
            success = len(errors) == 0
            message = f"批量读取完成，成功: {len(results)}, 失败: {len(errors)}"
            
            result_data = {
                'results': results,
                'errors': errors,
                'success_count': len(results),
                'error_count': len(errors)
            }
            
            return self._create_result(
                operation=AdapterOperation.READ,
                success=success,
                data=result_data,
                message=message,
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except Exception as e:
            return self._handle_error(AdapterOperation.READ, e, "批量读取失败")
    
    def batch_write(self, data_map: Dict[str, Any], **kwargs) -> AdapterResult:
        """批量写入JSON文件"""
        start_time = datetime.now()
        
        try:
            results = {}
            errors = []
            
            for destination, data in data_map.items():
                write_result = self.write(destination, data, **kwargs)
                if write_result.success:
                    results[destination] = write_result.data
                else:
                    errors.append(f"{destination}: {write_result.message}")
            
            success = len(errors) == 0
            message = f"批量写入完成，成功: {len(results)}, 失败: {len(errors)}"
            
            result_data = {
                'results': results,
                'errors': errors,
                'success_count': len(results),
                'error_count': len(errors)
            }
            
            return self._create_result(
                operation=AdapterOperation.WRITE,
                success=success,
                data=result_data,
                message=message,
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except Exception as e:
            return self._handle_error(AdapterOperation.WRITE, e, "批量写入失败")
    
    def _resolve_path(self, path_str: str) -> Path:
        """解析文件路径"""
        path = Path(path_str)
        
        # 如果是相对路径，基于工作目录
        if not path.is_absolute():
            path = self.work_dir / path
        
        return path.resolve()
    
    def list_files(self, pattern: str = "*.json") -> AdapterResult:
        """列出JSON文件"""
        start_time = datetime.now()
        
        try:
            # 确保已连接
            connect_result = self._ensure_connected()
            if not connect_result.success:
                return connect_result
            
            # 查找匹配的文件
            files = list(self.work_dir.glob(pattern))
            file_info = []
            
            for file_path in files:
                if file_path.is_file():
                    stat = file_path.stat()
                    file_info.append({
                        'path': str(file_path.relative_to(self.work_dir)),
                        'absolute_path': str(file_path),
                        'size_bytes': stat.st_size,
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
            
            return self._create_result(
                operation=AdapterOperation.READ,
                success=True,
                data=file_info,
                message=f"找到 {len(file_info)} 个匹配文件",
                start_time=start_time,
                end_time=datetime.now()
            )
            
        except Exception as e:
            return self._handle_error(AdapterOperation.READ, e, "列出文件失败") 