"""
迁移指南和助手 - 自动迁移工具

提供从旧架构到新架构的完整迁移指南：
- 组件映射表
- 迁移步骤
- 自动化工具
- 兼容性检查
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import re

class MigrationPriority(Enum):
    """迁移优先级"""
    CRITICAL = "critical"   # 必须立即迁移
    HIGH = "high"          # 高优先级迁移
    MEDIUM = "medium"      # 中等优先级
    LOW = "low"           # 低优先级

@dataclass
class MigrationMapping:
    """迁移映射"""
    old_component: str
    new_component: str
    version_deprecated: str
    version_removed: str
    priority: MigrationPriority
    migration_steps: List[str]
    breaking_changes: List[str] = None
    compatibility_notes: str = ""

class MigrationGuide:
    """迁移指南管理器"""
    
    def __init__(self):
        self.migration_mappings = self._initialize_migration_mappings()
        self.code_patterns = self._initialize_code_patterns()
    
    def _initialize_migration_mappings(self) -> Dict[str, MigrationMapping]:
        """初始化迁移映射表"""
        mappings = {}
        
        # 解析器迁移映射
        parser_mappings = [
            ("XMLParser", "core.parsing.create_parser('xml')", ["将XMLParser替换为新的工厂函数", "更新配置参数格式", "使用异步接口"]),
            ("EnhancedXMLParser", "core.parsing.create_parser('xml')", ["增强功能已内置", "移除旧配置选项", "采用新的性能监控"]),
            ("OptimizedXMLParser", "core.parsing.create_parser('xml')", ["优化已成为默认特性", "使用统一配置", "享受自动缓存"]),
            ("FinalXMLParser", "core.parsing.create_parser('xml')", ["所有功能已合并", "简化API调用", "统一错误处理"]),
            ("ImprovedXMLParser", "core.parsing.create_parser('xml')", ["改进功能已标准化", "使用新的批处理", "利用并行处理"]),
            ("TraceParser", "core.parsing.create_parser('xml')", ["跟踪功能已内置", "启用调试模式", "使用性能监控"]),
            ("HierarchicalParser", "core.parsing.create_parser('layered')", ["升级到分层快照解析", "配置分层参数", "使用渐进加载"]),
            ("SuperQuickParser", "core.parsing.create_parser('xml')", ["快速模式已默认", "移除冗余配置", "享受自动优化"]),
            ("DocumentTerminologyAnalyzer", "core.parsing.create_parser('terminology')", ["功能已重构增强", "更新术语配置", "使用新的分类器"]),
            ("TerminologyClassifier", "core.parsing.create_parser('terminology')", ["已集成到术语分析器", "更新分类规则", "使用统一接口"]),
        ]
        
        for old_name, new_name, steps in parser_mappings:
            mappings[old_name] = MigrationMapping(
                old_component=old_name,
                new_component=new_name,
                version_deprecated="3.0",
                version_removed="4.0",
                priority=MigrationPriority.HIGH,
                migration_steps=steps,
                compatibility_notes="向后兼容包装可用，但建议尽快迁移"
            )
        
        # 适配器迁移映射
        adapter_mappings = [
            ("UnifiedParserAdapter", "core.adapters.create_adapter('json')", ["使用标准JSON适配器", "更新配置格式", "利用新的错误处理"]),
            ("QuickUnifiedAdapter", "core.adapters.create_adapter('json')", ["快速处理已标准化", "简化配置", "使用性能监控"]),
            ("LegacyXMLAdapter", "core.adapters.create_adapter('xml')", ["升级到新XML适配器", "配置命名空间", "享受增强功能"]),
            ("CSVExportAdapter", "core.adapters.create_adapter('csv')", ["使用标准CSV适配器", "更新导出配置", "支持更多格式"]),
            ("DatabaseConnector", "core.adapters.create_adapter('database')", ["升级到标准适配器", "配置连接池", "使用事务支持"]),
            ("APIConnector", "core.adapters.create_adapter('api')", ["使用标准API适配器", "配置认证", "享受重试机制"]),
            ("CompatibilityAdapter", "core.adapters.create_adapter('json')", ["兼容性已内置", "移除冗余配置", "使用统一接口"]),
            ("LegacyDataBridge", "core.adapters.create_adapter('json')", ["桥接功能已内置", "简化数据转换", "使用标准适配器"]),
        ]
        
        for old_name, new_name, steps in adapter_mappings:
            mappings[old_name] = MigrationMapping(
                old_component=old_name,
                new_component=new_name,
                version_deprecated="3.0",
                version_removed="4.0",
                priority=MigrationPriority.MEDIUM,
                migration_steps=steps,
                compatibility_notes="适配器接口基本兼容"
            )
        
        # 服务迁移映射
        service_mappings = [
            ("StableIDGenerator", "core.services.get_id_manager()", ["使用服务层ID管理器", "更新配置", "享受缓存优化"]),
            ("CacheManager", "core.services.get_cache_manager()", ["升级到服务层缓存", "配置缓存策略", "使用分布式缓存"]),
        ]
        
        for old_name, new_name, steps in service_mappings:
            mappings[old_name] = MigrationMapping(
                old_component=old_name,
                new_component=new_name,
                version_deprecated="3.0",
                version_removed="4.0",
                priority=MigrationPriority.CRITICAL,
                migration_steps=steps,
                compatibility_notes="服务层提供更好的性能和稳定性"
            )
        
        return mappings
    
    def _initialize_code_patterns(self) -> Dict[str, str]:
        """初始化代码模式替换规则"""
        return {
            # 导入语句替换
            r'from core\.parsers import (\w+)': r'from core.parsing import create_parser',
            r'from core\.id_management import StableIDGenerator': r'from core.services import get_id_manager',
            r'from core\.performance import CacheManager': r'from core.services import get_cache_manager',
            
            # 实例化替换
            r'(\w+) = XMLParser\((.*?)\)': r'\1 = create_parser("xml", \2)',
            r'(\w+) = OptimizedXMLParser\((.*?)\)': r'\1 = create_parser("xml", \2)',
            r'(\w+) = EnhancedXMLParser\((.*?)\)': r'\1 = create_parser("xml", \2)',
            r'(\w+) = HierarchicalParser\((.*?)\)': r'\1 = create_parser("layered", \2)',
            r'(\w+) = DocumentTerminologyAnalyzer\((.*?)\)': r'\1 = create_parser("terminology", \2)',
            
            # 适配器替换
            r'(\w+) = UnifiedParserAdapter\((.*?)\)': r'\1 = create_adapter("json", \2)',
            r'(\w+) = DatabaseConnector\((.*?)\)': r'\1 = create_adapter("database", \2)',
            
            # 服务替换
            r'(\w+) = StableIDGenerator\((.*?)\)': r'\1 = get_id_manager()',
            r'(\w+) = CacheManager\((.*?)\)': r'\1 = get_cache_manager()',
        }
    
    def get_component_migration_plan(self, component_name: str) -> Dict[str, Any]:
        """获取特定组件的迁移计划"""
        if component_name not in self.migration_mappings:
            return {
                'found': False,
                'message': f"未找到组件 '{component_name}' 的迁移信息"
            }
        
        mapping = self.migration_mappings[component_name]
        
        return {
            'found': True,
            'component': component_name,
            'replacement': mapping.new_component,
            'priority': mapping.priority.value,
            'version_deprecated': mapping.version_deprecated,
            'version_removed': mapping.version_removed,
            'migration_steps': mapping.migration_steps,
            'breaking_changes': mapping.breaking_changes or [],
            'compatibility_notes': mapping.compatibility_notes,
            'estimated_effort': self._estimate_migration_effort(mapping)
        }
    
    def list_all_deprecated_components(self) -> List[Dict[str, Any]]:
        """列出所有废弃组件"""
        components = []
        
        for component_name, mapping in self.migration_mappings.items():
            components.append({
                'name': component_name,
                'replacement': mapping.new_component,
                'priority': mapping.priority.value,
                'version_removed': mapping.version_removed,
                'category': self._get_component_category(component_name)
            })
        
        # 按优先级排序
        priority_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
        components.sort(key=lambda x: priority_order.get(x['priority'], 3))
        
        return components
    
    def generate_migration_script(self, file_content: str) -> Tuple[str, List[str]]:
        """生成迁移脚本"""
        modified_content = file_content
        changes = []
        
        for pattern, replacement in self.code_patterns.items():
            matches = re.findall(pattern, modified_content)
            if matches:
                modified_content = re.sub(pattern, replacement, modified_content)
                changes.append(f"替换模式: {pattern} -> {replacement}")
        
        return modified_content, changes
    
    def analyze_codebase_dependencies(self, file_paths: List[str]) -> Dict[str, Any]:
        """分析代码库的依赖关系"""
        analysis = {
            'files_analyzed': len(file_paths),
            'deprecated_usage': {},
            'migration_priority': {},
            'estimated_effort': 'low'
        }
        
        # 这里可以扩展为实际的文件分析
        # 目前返回模拟数据
        return analysis
    
    def get_migration_recommendations(self, component_name: str = None) -> Dict[str, Any]:
        """获取迁移建议"""
        if component_name:
            return self.get_component_migration_plan(component_name)
        
        # 返回总体迁移建议
        all_components = self.list_all_deprecated_components()
        critical_components = [c for c in all_components if c['priority'] == 'critical']
        high_components = [c for c in all_components if c['priority'] == 'high']
        
        return {
            'total_deprecated': len(all_components),
            'critical_migrations': len(critical_components),
            'high_priority_migrations': len(high_components),
            'recommended_order': critical_components + high_components[:3],  # 优先处理前3个高优先级
            'migration_timeline': {
                'week_1': critical_components,
                'week_2': high_components[:5],
                'month_1': [c for c in all_components if c['priority'] == 'medium']
            }
        }
    
    def _estimate_migration_effort(self, mapping: MigrationMapping) -> str:
        """估算迁移工作量"""
        steps_count = len(mapping.migration_steps)
        breaking_changes = len(mapping.breaking_changes or [])
        
        if mapping.priority == MigrationPriority.CRITICAL:
            return "medium" if breaking_changes > 0 else "low"
        elif steps_count > 3 or breaking_changes > 1:
            return "medium"
        else:
            return "low"
    
    def _get_component_category(self, component_name: str) -> str:
        """获取组件类别"""
        if any(keyword in component_name for keyword in ['Parser', 'Analyzer']):
            return 'parser'
        elif any(keyword in component_name for keyword in ['Adapter', 'Connector', 'Bridge']):
            return 'adapter'
        elif any(keyword in component_name for keyword in ['Generator', 'Manager']):
            return 'service'
        else:
            return 'other'

# 便捷函数
def get_migration_recommendations(component_name: str = None) -> Dict[str, Any]:
    """获取迁移建议的便捷函数"""
    guide = MigrationGuide()
    return guide.get_migration_recommendations(component_name)

def generate_migration_plan() -> Dict[str, Any]:
    """生成完整的迁移计划"""
    guide = MigrationGuide()
    recommendations = guide.get_migration_recommendations()
    
    plan = {
        'overview': {
            'total_components': recommendations['total_deprecated'],
            'critical_items': recommendations['critical_migrations'],
            'estimated_timeline': '2-4 周',
            'complexity': 'medium'
        },
        'phase_1_critical': {
            'components': recommendations['migration_timeline']['week_1'],
            'timeline': '第1周',
            'focus': '核心服务和高频使用组件'
        },
        'phase_2_high_priority': {
            'components': recommendations['migration_timeline']['week_2'],
            'timeline': '第2周',
            'focus': '解析器和主要功能组件'
        },
        'phase_3_standard': {
            'components': recommendations['migration_timeline']['month_1'],
            'timeline': '第3-4周',
            'focus': '适配器和辅助功能'
        },
        'success_criteria': [
            '所有critical组件迁移完成',
            '核心功能测试通过',
            '性能指标保持或提升',
            '向后兼容性验证通过'
        ]
    }
    
    return plan

def create_migration_checklist() -> List[Dict[str, Any]]:
    """创建迁移检查清单"""
    return [
        {
            'phase': 'preparation',
            'title': '迁移准备',
            'items': [
                '备份现有代码',
                '创建测试分支',
                '确保测试用例完整',
                '评估依赖关系'
            ]
        },
        {
            'phase': 'critical_migration',
            'title': '关键组件迁移',
            'items': [
                '迁移StableIDGenerator到服务层',
                '迁移CacheManager到服务层',
                '验证服务层功能',
                '运行核心功能测试'
            ]
        },
        {
            'phase': 'parser_migration',
            'title': '解析器迁移',
            'items': [
                '迁移XML解析器到统一接口',
                '迁移术语分析器',
                '迁移分层解析器',
                '验证解析功能'
            ]
        },
        {
            'phase': 'adapter_migration',
            'title': '适配器迁移',
            'items': [
                '迁移适配器到标准接口',
                '配置新的适配器参数',
                '测试数据转换功能',
                '验证兼容性'
            ]
        },
        {
            'phase': 'validation',
            'title': '迁移验证',
            'items': [
                '运行完整测试套件',
                '性能基准测试',
                '向后兼容性测试',
                '文档更新'
            ]
        }
    ]

# 导出接口
__all__ = [
    'MigrationPriority',
    'MigrationMapping',
    'MigrationGuide',
    'get_migration_recommendations',
    'generate_migration_plan',
    'create_migration_checklist'
] 