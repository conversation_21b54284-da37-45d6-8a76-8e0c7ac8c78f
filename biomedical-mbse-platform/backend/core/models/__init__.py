"""
数据模型模块 - XML元数据系统的核心数据结构

提供完整的数据模型体系：
- 基础模型抽象类和接口
- 元素数据模型
- 关系数据模型  
- 快照数据模型
- 术语数据模型
- 模型集合和工厂函数
"""

from typing import Dict, List, Any, Optional, Union, Type
import logging

# 基础模型
from .base import (
    BaseModel,
    ModelCollection,
    ValidationResult,
    ChangeRecord
)

# 元素模型
from .element import (
    Element,
    ElementType,
    ElementStatus,
    ElementCollection,
    Namespace,
    Attribute,
    Reference
)

# 关系模型
from .relationships import (
    Relationship,
    RelationshipNetwork,
    RelationType,
    RelationshipDirection,
    RelationshipConstraint
)

# 快照模型
from .snapshots import (
    ElementSnapshot,
    LayerSnapshot,
    SnapshotType,
    CompletionStatus,
    LayerLevel,
    ProcessingMetrics
)

# 术语模型
from .terminology import (
    Term,
    TerminologyDatabase,
    TerminologyDomain,
    TerminologyCategory,
    TerminologyStatus,
    TerminologyDefinition,
    TerminologyTranslation
)

# 版本信息
__version__ = "3.1.0"
__author__ = "XML元数据系统团队"

# 模块日志
logger = logging.getLogger(__name__)

# 模型注册表
MODEL_REGISTRY: Dict[str, Type[BaseModel]] = {
    'Element': Element,
    'ElementSnapshot': ElementSnapshot,
    'LayerSnapshot': LayerSnapshot,
    'Relationship': Relationship,
    'RelationshipNetwork': RelationshipNetwork,
    'Term': Term,
    'TerminologyDatabase': TerminologyDatabase,
    'ElementCollection': ElementCollection
}

# 集合类注册表
COLLECTION_REGISTRY: Dict[str, Type[ModelCollection]] = {
    'ElementCollection': ElementCollection,
    'RelationshipNetwork': RelationshipNetwork,
    'TerminologyDatabase': TerminologyDatabase
}

def create_model(model_type: str, *args, **kwargs) -> Optional[BaseModel]:
    """
    模型工厂函数
    
    Args:
        model_type: 模型类型名称
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        创建的模型实例，如果类型不存在则返回None
    """
    try:
        if model_type in MODEL_REGISTRY:
            model_class = MODEL_REGISTRY[model_type]
            return model_class(*args, **kwargs)
        else:
            logger.error(f"未知的模型类型: {model_type}")
            return None
    except Exception as e:
        logger.error(f"创建模型 {model_type} 时发生错误: {e}")
        return None

def create_collection(collection_type: str, *args, **kwargs) -> Optional[ModelCollection]:
    """
    集合工厂函数
    
    Args:
        collection_type: 集合类型名称
        *args: 位置参数
        **kwargs: 关键字参数
        
    Returns:
        创建的集合实例，如果类型不存在则返回None
    """
    try:
        if collection_type in COLLECTION_REGISTRY:
            collection_class = COLLECTION_REGISTRY[collection_type]
            return collection_class(*args, **kwargs)
        else:
            logger.error(f"未知的集合类型: {collection_type}")
            return None
    except Exception as e:
        logger.error(f"创建集合 {collection_type} 时发生错误: {e}")
        return None

def create_element(tag: str, element_type: ElementType = ElementType.LEAF, 
                  element_id: Optional[str] = None) -> Element:
    """
    创建元素的便捷函数
    
    Args:
        tag: 元素标签
        element_type: 元素类型
        element_id: 元素ID（可选）
        
    Returns:
        Element实例
    """
    return Element(tag=tag, element_type=element_type, element_id=element_id)

def create_relationship(source_id: str, target_id: str, relation_type: RelationType,
                       relationship_id: Optional[str] = None,
                       direction: RelationshipDirection = RelationshipDirection.UNIDIRECTIONAL) -> Relationship:
    """
    创建关系的便捷函数
    
    Args:
        source_id: 源元素ID
        target_id: 目标元素ID  
        relation_type: 关系类型
        relationship_id: 关系ID（可选）
        direction: 关系方向
        
    Returns:
        Relationship实例
    """
    return Relationship(
        source_id=source_id,
        target_id=target_id,
        relation_type=relation_type,
        relationship_id=relationship_id,
        direction=direction
    )

def create_element_snapshot(element_id: str, tag: str, layer_level: LayerLevel,
                           snapshot_id: Optional[str] = None) -> ElementSnapshot:
    """
    创建元素快照的便捷函数
    
    Args:
        element_id: 元素ID
        tag: 元素标签
        layer_level: 层级级别
        snapshot_id: 快照ID（可选）
        
    Returns:
        ElementSnapshot实例
    """
    return ElementSnapshot(
        element_id=element_id,
        tag=tag,
        layer_level=layer_level,
        snapshot_id=snapshot_id
    )

def create_term(term_text: str, domain: TerminologyDomain, category: TerminologyCategory,
               term_id: Optional[str] = None) -> Term:
    """
    创建术语的便捷函数
    
    Args:
        term_text: 术语文本
        domain: 术语领域
        category: 术语分类
        term_id: 术语ID（可选）
        
    Returns:
        Term实例
    """
    return Term(
        term_text=term_text,
        domain=domain,
        category=category,
        term_id=term_id
    )

def validate_model_batch(models: List[BaseModel], stop_on_error: bool = False) -> Dict[str, ValidationResult]:
    """
    批量验证模型
    
    Args:
        models: 要验证的模型列表
        stop_on_error: 是否在第一个错误时停止
        
    Returns:
        验证结果字典，键为模型ID，值为ValidationResult
    """
    results = {}
    
    for model in models:
        try:
            result = model.validate()
            results[model.id] = result
            
            if stop_on_error and not result.is_valid:
                logger.warning(f"模型 {model.id} 验证失败，停止批量验证")
                break
                
        except Exception as e:
            logger.error(f"验证模型 {model.id} 时发生错误: {e}")
            # 创建错误结果
            error_result = ValidationResult()
            error_result.is_valid = False
            error_result.errors.append(f"验证过程异常: {str(e)}")
            error_result.score = 0.0
            results[model.id] = error_result
            
            if stop_on_error:
                break
    
    return results

def get_model_statistics(models: List[BaseModel]) -> Dict[str, Any]:
    """
    获取模型统计信息
    
    Args:
        models: 模型列表
        
    Returns:
        统计信息字典
    """
    if not models:
        return {
            'total_count': 0,
            'type_distribution': {},
            'validation_summary': {
                'valid_count': 0,
                'invalid_count': 0,
                'average_score': 0.0
            },
            'version_distribution': {},
            'creation_time_range': None
        }
    
    # 类型分布
    type_distribution = {}
    for model in models:
        model_type = model.__class__.__name__
        type_distribution[model_type] = type_distribution.get(model_type, 0) + 1
    
    # 验证统计
    validation_results = validate_model_batch(models)
    valid_count = sum(1 for result in validation_results.values() if result.is_valid)
    invalid_count = len(validation_results) - valid_count
    average_score = sum(result.score for result in validation_results.values()) / len(validation_results)
    
    # 版本分布
    version_distribution = {}
    for model in models:
        version = model.version
        version_distribution[version] = version_distribution.get(version, 0) + 1
    
    # 创建时间范围
    creation_times = [model.created_at for model in models]
    creation_time_range = {
        'earliest': min(creation_times),
        'latest': max(creation_times)
    } if creation_times else None
    
    return {
        'total_count': len(models),
        'type_distribution': type_distribution,
        'validation_summary': {
            'valid_count': valid_count,
            'invalid_count': invalid_count,
            'average_score': average_score
        },
        'version_distribution': version_distribution,
        'creation_time_range': creation_time_range
    }

def convert_models_to_dict_batch(models: List[BaseModel]) -> Dict[str, Dict[str, Any]]:
    """
    批量转换模型为字典
    
    Args:
        models: 模型列表
        
    Returns:
        字典，键为模型ID，值为模型字典表示
    """
    result = {}
    
    for model in models:
        try:
            result[model.id] = model.to_dict()
        except Exception as e:
            logger.error(f"转换模型 {model.id} 为字典时发生错误: {e}")
            # 添加错误信息
            result[model.id] = {
                'id': model.id,
                'type': model.__class__.__name__,
                'error': f"转换失败: {str(e)}"
            }
    
    return result

def find_models_by_type(models: List[BaseModel], model_type: Type[BaseModel]) -> List[BaseModel]:
    """
    按类型查找模型
    
    Args:
        models: 模型列表
        model_type: 模型类型
        
    Returns:
        匹配类型的模型列表
    """
    return [model for model in models if isinstance(model, model_type)]

def find_models_by_version(models: List[BaseModel], version: str) -> List[BaseModel]:
    """
    按版本查找模型
    
    Args:
        models: 模型列表
        version: 版本号
        
    Returns:
        匹配版本的模型列表
    """
    return [model for model in models if model.version == version]

def get_registered_model_types() -> List[str]:
    """
    获取已注册的模型类型列表
    
    Returns:
        模型类型名称列表
    """
    return list(MODEL_REGISTRY.keys())

def get_registered_collection_types() -> List[str]:
    """
    获取已注册的集合类型列表
    
    Returns:
        集合类型名称列表
    """
    return list(COLLECTION_REGISTRY.keys())

def register_model_type(name: str, model_class: Type[BaseModel]) -> bool:
    """
    注册新的模型类型
    
    Args:
        name: 模型类型名称
        model_class: 模型类
        
    Returns:
        是否注册成功
    """
    try:
        if not issubclass(model_class, BaseModel):
            logger.error(f"模型类 {model_class} 必须继承自BaseModel")
            return False
        
        MODEL_REGISTRY[name] = model_class
        logger.info(f"成功注册模型类型: {name}")
        return True
        
    except Exception as e:
        logger.error(f"注册模型类型 {name} 时发生错误: {e}")
        return False

def register_collection_type(name: str, collection_class: Type[ModelCollection]) -> bool:
    """
    注册新的集合类型
    
    Args:
        name: 集合类型名称
        collection_class: 集合类
        
    Returns:
        是否注册成功
    """
    try:
        if not issubclass(collection_class, ModelCollection):
            logger.error(f"集合类 {collection_class} 必须继承自ModelCollection")
            return False
        
        COLLECTION_REGISTRY[name] = collection_class
        logger.info(f"成功注册集合类型: {name}")
        return True
        
    except Exception as e:
        logger.error(f"注册集合类型 {name} 时发生错误: {e}")
        return False

# 导出所有公共接口
__all__ = [
    # 基础类
    'BaseModel',
    'ModelCollection', 
    'ValidationResult',
    'ChangeRecord',
    
    # 元素模型
    'Element',
    'ElementType',
    'ElementStatus',
    'ElementCollection',
    'Namespace',
    'Attribute',
    'Reference',
    
    # 关系模型
    'Relationship',
    'RelationshipNetwork',
    'RelationType',
    'RelationshipDirection',
    'RelationshipConstraint',
    
    # 快照模型
    'ElementSnapshot',
    'LayerSnapshot',
    'SnapshotType',
    'CompletionStatus',
    'LayerLevel',
    'ProcessingMetrics',
    
    # 术语模型
    'Term',
    'TerminologyDatabase',
    'TerminologyDomain',
    'TerminologyCategory',
    'TerminologyStatus',
    'TerminologyDefinition',
    'TerminologyTranslation',
    
    # 工厂函数
    'create_model',
    'create_collection',
    'create_element',
    'create_relationship',
    'create_element_snapshot',
    'create_term',
    
    # 工具函数
    'validate_model_batch',
    'get_model_statistics',
    'convert_models_to_dict_batch',
    'find_models_by_type',
    'find_models_by_version',
    'get_registered_model_types',
    'get_registered_collection_types',
    'register_model_type',
    'register_collection_type'
]

# 模块初始化日志
logger.info(f"数据模型模块已加载 v{__version__}")
logger.info(f"已注册 {len(MODEL_REGISTRY)} 个模型类型")
logger.info(f"已注册 {len(COLLECTION_REGISTRY)} 个集合类型") 