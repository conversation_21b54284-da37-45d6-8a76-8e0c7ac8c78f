"""
基础数据模型 - 所有模型类的抽象基类

提供通用的数据模型功能：
- 唯一ID管理
- 时间戳记录
- 版本控制
- 验证接口
- 序列化/反序列化
- 变更追踪
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import json
import hashlib

@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool = True
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    score: float = 100.0  # 质量评分 (0-100)
    validated_at: Optional[datetime] = None

@dataclass
class ChangeRecord:
    """变更记录"""
    timestamp: datetime
    change_type: str
    change_data: Dict[str, Any]
    user: str = "system"

class BaseModel(ABC):
    """
    基础数据模型抽象类
    
    所有数据模型的共同基类，提供：
    - 唯一ID管理
    - 创建和更新时间
    - 版本控制
    - 验证接口
    - 序列化支持
    - 变更追踪
    """
    
    def __init__(self, model_id: Optional[str] = None):
        self.id = model_id or self._generate_id()
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.version = "1.0"
        
        # 内部状态
        self._metadata: Dict[str, Any] = {}
        self._change_history: List[ChangeRecord] = []
        self._hash_cache: Optional[str] = None
        self._validation_cache: Optional[ValidationResult] = None
    
    def _generate_id(self) -> str:
        """生成唯一ID"""
        timestamp = int(datetime.now().timestamp() * 1000000)
        random_part = uuid.uuid4().hex[:8]
        return f"{self.__class__.__name__.lower()}_{timestamp}_{random_part}"
    
    def _record_change(self, change_type: str, change_data: Dict[str, Any], user: str = "system"):
        """记录变更"""
        change_record = ChangeRecord(
            timestamp=datetime.now(),
            change_type=change_type,
            change_data=change_data,
            user=user
        )
        self._change_history.append(change_record)
        self.updated_at = datetime.now()
        
        # 清除缓存
        self._hash_cache = None
        self._validation_cache = None
    
    def get_change_history(self) -> List[ChangeRecord]:
        """获取变更历史"""
        return self._change_history.copy()
    
    def get_latest_changes(self, limit: int = 10) -> List[ChangeRecord]:
        """获取最近的变更"""
        return self._change_history[-limit:] if self._change_history else []
    
    def set_metadata(self, key: str, value: Any) -> 'BaseModel':
        """设置元数据"""
        old_value = self._metadata.get(key)
        self._metadata[key] = value
        self._record_change('metadata_changed', {
            'key': key,
            'old_value': old_value,
            'new_value': value
        })
        return self
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """获取元数据"""
        return self._metadata.get(key, default)
    
    def calculate_hash(self) -> str:
        """计算模型的哈希值"""
        if self._hash_cache is not None:
            return self._hash_cache
        
        # 获取模型的字典表示（排除时间戳和哈希）
        data = self.to_dict()
        
        # 移除会变化的字段
        exclude_fields = {'created_at', 'updated_at', '_hash_cache', '_validation_cache'}
        filtered_data = {k: v for k, v in data.items() if k not in exclude_fields}
        
        # 计算哈希
        json_str = json.dumps(filtered_data, sort_keys=True, default=str)
        hash_value = hashlib.sha256(json_str.encode('utf-8')).hexdigest()
        
        self._hash_cache = hash_value
        return hash_value
    
    def increment_version(self, version_increment: str = "minor") -> 'BaseModel':
        """递增版本号"""
        try:
            parts = self.version.split('.')
            major, minor = int(parts[0]), int(parts[1]) if len(parts) > 1 else 0
            patch = int(parts[2]) if len(parts) > 2 else 0
            
            if version_increment == "major":
                major += 1
                minor = 0
                patch = 0
            elif version_increment == "minor":
                minor += 1
                patch = 0
            else:  # patch
                patch += 1
            
            old_version = self.version
            self.version = f"{major}.{minor}.{patch}"
            
            self._record_change('version_incremented', {
                'old_version': old_version,
                'new_version': self.version,
                'increment_type': version_increment
            })
            
        except (ValueError, IndexError):
            # 如果版本格式不标准，重置为1.0.1
            self.version = "1.0.1"
            self._record_change('version_reset', {'new_version': self.version})
        
        return self
    
    @abstractmethod
    def validate(self) -> ValidationResult:
        """
        验证模型的有效性
        
        子类必须实现此方法，返回验证结果
        """
        pass
    
    def validate_cached(self, force_refresh: bool = False) -> ValidationResult:
        """
        带缓存的验证
        
        Args:
            force_refresh: 是否强制刷新缓存
            
        Returns:
            验证结果
        """
        if self._validation_cache is None or force_refresh:
            self._validation_cache = self.validate()
            self._validation_cache.validated_at = datetime.now()
        
        return self._validation_cache
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        子类必须实现此方法
        """
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseModel':
        """
        从字典创建实例
        
        子类必须实现此方法
        """
        pass
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), default=str, indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'BaseModel':
        """从JSON字符串创建实例"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def clone(self) -> 'BaseModel':
        """克隆模型实例"""
        # 通过序列化和反序列化来实现深拷贝
        data = self.to_dict()
        
        # 生成新的ID
        data['id'] = self._generate_id()
        
        # 重置时间戳
        data['created_at'] = datetime.now().isoformat()
        data['updated_at'] = datetime.now().isoformat()
        
        # 清空变更历史
        if '_change_history' in data:
            data['_change_history'] = []
        
        return self.__class__.from_dict(data)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取模型摘要信息"""
        validation_result = self.validate_cached()
        
        return {
            'id': self.id,
            'type': self.__class__.__name__,
            'version': self.version,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'is_valid': validation_result.is_valid,
            'validation_score': validation_result.score,
            'change_count': len(self._change_history),
            'hash': self.calculate_hash()
        }
    
    def is_newer_than(self, other: 'BaseModel') -> bool:
        """判断是否比另一个模型更新"""
        return self.updated_at > other.updated_at
    
    def is_same_version(self, other: 'BaseModel') -> bool:
        """判断是否与另一个模型版本相同"""
        return self.version == other.version
    
    def has_changed_since(self, timestamp: datetime) -> bool:
        """判断自指定时间以来是否有变更"""
        return self.updated_at > timestamp
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.__class__.__name__}(id={self.id}, version={self.version})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"{self.__class__.__name__}(id='{self.id}', version='{self.version}', created_at='{self.created_at}', updated_at='{self.updated_at}')"
    
    def __eq__(self, other) -> bool:
        """相等性比较"""
        if not isinstance(other, BaseModel):
            return False
        return self.id == other.id and self.calculate_hash() == other.calculate_hash()
    
    def __hash__(self) -> int:
        """哈希值"""
        return hash((self.id, self.calculate_hash()))

class ModelCollection(BaseModel):
    """
    模型集合基类
    
    用于管理多个相关模型的集合
    """
    
    def __init__(self, collection_id: Optional[str] = None):
        super().__init__(collection_id)
        self._items: Dict[str, BaseModel] = {}
        
        # 集合属性
        self.name: str = ""
        self.description: str = ""
        self.tags: Set[str] = set()
    
    def add_item(self, item: BaseModel) -> 'ModelCollection':
        """添加项目到集合"""
        self._items[item.id] = item
        self._record_change('item_added', {
            'item_id': item.id,
            'item_type': item.__class__.__name__
        })
        return self
    
    def remove_item(self, item_id: str) -> bool:
        """从集合中移除项目"""
        if item_id in self._items:
            item = self._items.pop(item_id)
            self._record_change('item_removed', {
                'item_id': item_id,
                'item_type': item.__class__.__name__
            })
            return True
        return False
    
    def get_item(self, item_id: str) -> Optional[BaseModel]:
        """获取指定项目"""
        return self._items.get(item_id)
    
    def get_all_items(self) -> Dict[str, BaseModel]:
        """获取所有项目"""
        return self._items.copy()
    
    def get_items_by_type(self, model_type: type) -> List[BaseModel]:
        """按类型获取项目"""
        return [item for item in self._items.values() if isinstance(item, model_type)]
    
    def count(self) -> int:
        """获取项目数量"""
        return len(self._items)
    
    def is_empty(self) -> bool:
        """判断集合是否为空"""
        return len(self._items) == 0
    
    def clear(self) -> 'ModelCollection':
        """清空集合"""
        item_count = len(self._items)
        self._items.clear()
        self._record_change('collection_cleared', {'removed_count': item_count})
        return self
    
    def validate(self) -> ValidationResult:
        """验证集合中的所有项目"""
        result = ValidationResult()
        
        invalid_items = []
        total_score = 0
        
        for item_id, item in self._items.items():
            item_result = item.validate_cached()
            if not item_result.is_valid:
                invalid_items.append(item_id)
                result.errors.extend([f"项目{item_id}: {error}" for error in item_result.errors])
            total_score += item_result.score
        
        # 检查集合完整性
        if invalid_items:
            result.warnings.append(f"集合中有{len(invalid_items)}个无效项目")
        
        # 计算平均评分
        if self._items:
            result.score = total_score / len(self._items)
        else:
            result.score = 100.0
        
        result.is_valid = len(result.errors) == 0
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'tags': list(self.tags),
            'items': {item_id: item.to_dict() for item_id, item in self._items.items()},
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version,
            'metadata': self._metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelCollection':
        """从字典创建集合实例"""
        collection = cls(data.get('id'))
        collection.name = data.get('name', '')
        collection.description = data.get('description', '')
        collection.tags = set(data.get('tags', []))
        
        # 恢复时间戳和版本
        if 'created_at' in data:
            collection.created_at = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            collection.updated_at = datetime.fromisoformat(data['updated_at'])
        collection.version = data.get('version', '1.0')
        collection._metadata = data.get('metadata', {})
        
        # 注意：具体的项目恢复需要在子类中实现
        # 因为需要知道具体的模型类型
        
        return collection 