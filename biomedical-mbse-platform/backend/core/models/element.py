"""
元素数据模型 - XML元素的标准化结构

定义统一的元素数据模型，支持多种XML格式的标准化表示
"""

from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import json

class ElementType(Enum):
    """元素类型枚举"""
    ROOT = "root"
    CONTAINER = "container"
    LEAF = "leaf"
    REFERENCE = "reference"
    ATTRIBUTE = "attribute"

class ElementStatus(Enum):
    """元素状态枚举"""
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    DELETED = "deleted"
    PENDING = "pending"

@dataclass
class Namespace:
    """命名空间信息"""
    prefix: str = ""
    uri: str = ""
    local_name: str = ""

@dataclass
class Attribute:
    """属性对象"""
    name: str
    value: Any
    namespace: Optional[Namespace] = None
    data_type: str = "string"
    is_required: bool = False

@dataclass
class Reference:
    """引用对象"""
    ref_id: str
    ref_type: str
    ref_uri: Optional[str] = None
    resolved: bool = False
    target_element: Optional['Element'] = None

@dataclass
class Validation:
    """验证信息"""
    is_valid: bool = True
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    score: float = 100.0
    validated_at: Optional[datetime] = None

@dataclass
class Metadata:
    """元数据信息"""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    author: str = ""
    version: str = "1.0"
    description: str = ""
    tags: Set[str] = field(default_factory=set)
    custom_properties: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SemanticInfo:
    """语义信息"""
    domain_category: str = "unknown"
    domain_code: str = "UNK"
    confidence_score: float = 0.0
    semantic_tags: List[str] = field(default_factory=list)
    classification: Dict[str, Any] = field(default_factory=dict)
    ontology_mapping: Dict[str, str] = field(default_factory=dict)

@dataclass
class Position:
    """位置信息"""
    line_number: int = 0
    column_number: int = 0
    byte_offset: int = 0
    depth_level: int = 0
    xpath: str = ""

class Element:
    """
    统一元素模型 - XML元素的标准化表示
    
    功能特性:
    - 完整的元素信息封装
    - 多格式XML支持
    - 语义信息集成
    - 验证状态管理
    - 关系网络支持
    """
    
    def __init__(self, 
                 element_id: str,
                 tag: str,
                 element_type: ElementType = ElementType.LEAF):
        self.id = element_id
        self.tag = tag
        self.element_type = element_type
        
        # 基本信息
        self.namespace = Namespace()
        self.local_name = tag.split('}')[-1] if '}' in tag else tag
        self.text_content = ""
        self.status = ElementStatus.ACTIVE
        
        # 结构信息
        self.parent_id: Optional[str] = None
        self.children_ids: List[str] = []
        self.attributes: Dict[str, Attribute] = {}
        self.references: List[Reference] = []
        
        # 元数据
        self.metadata = Metadata(created_at=datetime.now())
        self.semantic_info = SemanticInfo()
        self.validation = Validation()
        self.position = Position()
        
        # 扩展信息
        self.custom_data: Dict[str, Any] = {}
        self.processing_hints: Dict[str, Any] = {}
    
    def add_attribute(self, name: str, value: Any, 
                     namespace: Optional[Namespace] = None,
                     data_type: str = "string",
                     is_required: bool = False) -> 'Element':
        """添加属性"""
        attr = Attribute(
            name=name,
            value=value,
            namespace=namespace,
            data_type=data_type,
            is_required=is_required
        )
        self.attributes[name] = attr
        return self
    
    def add_child(self, child_id: str) -> 'Element':
        """添加子元素ID"""
        if child_id not in self.children_ids:
            self.children_ids.append(child_id)
        return self
    
    def remove_child(self, child_id: str) -> 'Element':
        """移除子元素ID"""
        if child_id in self.children_ids:
            self.children_ids.remove(child_id)
        return self
    
    def add_reference(self, ref_id: str, ref_type: str, 
                     ref_uri: Optional[str] = None) -> 'Element':
        """添加引用"""
        ref = Reference(ref_id=ref_id, ref_type=ref_type, ref_uri=ref_uri)
        self.references.append(ref)
        return self
    
    def set_namespace(self, prefix: str = "", uri: str = "") -> 'Element':
        """设置命名空间"""
        self.namespace = Namespace(prefix=prefix, uri=uri, local_name=self.local_name)
        return self
    
    def set_text_content(self, content: str) -> 'Element':
        """设置文本内容"""
        self.text_content = content.strip() if content else ""
        return self
    
    def set_semantic_info(self, domain_category: str, 
                         domain_code: str = "",
                         confidence_score: float = 0.0) -> 'Element':
        """设置语义信息"""
        self.semantic_info.domain_category = domain_category
        self.semantic_info.domain_code = domain_code or domain_category.upper()[:3]
        self.semantic_info.confidence_score = confidence_score
        return self
    
    def set_position(self, line: int = 0, column: int = 0, 
                    depth: int = 0, xpath: str = "") -> 'Element':
        """设置位置信息"""
        self.position.line_number = line
        self.position.column_number = column  
        self.position.depth_level = depth
        self.position.xpath = xpath
        return self
    
    def add_tag(self, tag: str) -> 'Element':
        """添加语义标签"""
        self.semantic_info.semantic_tags.append(tag)
        return self
    
    def add_metadata_tag(self, tag: str) -> 'Element':
        """添加元数据标签"""
        self.metadata.tags.add(tag)
        return self
    
    def set_custom_property(self, key: str, value: Any) -> 'Element':
        """设置自定义属性"""
        self.custom_data[key] = value
        return self
    
    def validate(self) -> Validation:
        """验证元素"""
        errors = []
        warnings = []
        
        # 基本验证
        if not self.id:
            errors.append("元素ID不能为空")
        
        if not self.tag:
            errors.append("元素标签不能为空")
        
        # 属性验证
        for attr_name, attr in self.attributes.items():
            if attr.is_required and not attr.value:
                errors.append(f"必需属性 {attr_name} 为空")
        
        # 引用验证
        unresolved_refs = [ref for ref in self.references if not ref.resolved]
        if unresolved_refs:
            warnings.append(f"存在 {len(unresolved_refs)} 个未解析的引用")
        
        # 计算验证分数
        total_checks = 10  # 假设总共10项检查
        failed_checks = len(errors)
        warning_penalty = len(warnings) * 0.1
        
        score = max(0, ((total_checks - failed_checks) / total_checks - warning_penalty) * 100)
        
        # 更新验证信息
        self.validation = Validation(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            score=score,
            validated_at=datetime.now()
        )
        
        return self.validation
    
    def get_attribute_value(self, name: str, default: Any = None) -> Any:
        """获取属性值"""
        attr = self.attributes.get(name)
        return attr.value if attr else default
    
    def has_attribute(self, name: str) -> bool:
        """检查是否有指定属性"""
        return name in self.attributes
    
    def get_child_count(self) -> int:
        """获取子元素数量"""
        return len(self.children_ids)
    
    def is_leaf(self) -> bool:
        """判断是否为叶子节点"""
        return len(self.children_ids) == 0
    
    def is_root(self) -> bool:
        """判断是否为根节点"""
        return self.parent_id is None
    
    def get_full_path(self) -> str:
        """获取完整路径（需要配合元素树使用）"""
        return self.position.xpath or f"/{self.tag}"
    
    def get_qualified_name(self) -> str:
        """获取限定名称"""
        if self.namespace.prefix:
            return f"{self.namespace.prefix}:{self.local_name}"
        return self.local_name
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'tag': self.tag,
            'local_name': self.local_name,
            'element_type': self.element_type.value,
            'status': self.status.value,
            'namespace': {
                'prefix': self.namespace.prefix,
                'uri': self.namespace.uri,
                'local_name': self.namespace.local_name
            },
            'text_content': self.text_content,
            'parent_id': self.parent_id,
            'children_ids': self.children_ids.copy(),
            'attributes': {
                name: {
                    'name': attr.name,
                    'value': attr.value,
                    'data_type': attr.data_type,
                    'is_required': attr.is_required
                }
                for name, attr in self.attributes.items()
            },
            'references': [
                {
                    'ref_id': ref.ref_id,
                    'ref_type': ref.ref_type,
                    'ref_uri': ref.ref_uri,
                    'resolved': ref.resolved
                }
                for ref in self.references
            ],
            'metadata': {
                'created_at': self.metadata.created_at.isoformat() if self.metadata.created_at else None,
                'updated_at': self.metadata.updated_at.isoformat() if self.metadata.updated_at else None,
                'author': self.metadata.author,
                'version': self.metadata.version,
                'description': self.metadata.description,
                'tags': list(self.metadata.tags),
                'custom_properties': self.metadata.custom_properties.copy()
            },
            'semantic_info': {
                'domain_category': self.semantic_info.domain_category,
                'domain_code': self.semantic_info.domain_code,
                'confidence_score': self.semantic_info.confidence_score,
                'semantic_tags': self.semantic_info.semantic_tags.copy(),
                'classification': self.semantic_info.classification.copy(),
                'ontology_mapping': self.semantic_info.ontology_mapping.copy()
            },
            'validation': {
                'is_valid': self.validation.is_valid,
                'errors': self.validation.errors.copy(),
                'warnings': self.validation.warnings.copy(),
                'score': self.validation.score,
                'validated_at': self.validation.validated_at.isoformat() if self.validation.validated_at else None
            },
            'position': {
                'line_number': self.position.line_number,
                'column_number': self.position.column_number,
                'depth_level': self.position.depth_level,
                'xpath': self.position.xpath
            },
            'custom_data': self.custom_data.copy()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Element':
        """从字典创建元素"""
        element = cls(
            element_id=data['id'],
            tag=data['tag'],
            element_type=ElementType(data.get('element_type', 'leaf'))
        )
        
        # 设置基本信息
        element.local_name = data.get('local_name', element.local_name)
        element.status = ElementStatus(data.get('status', 'active'))
        element.text_content = data.get('text_content', '')
        element.parent_id = data.get('parent_id')
        element.children_ids = data.get('children_ids', []).copy()
        
        # 设置命名空间
        if 'namespace' in data:
            ns_data = data['namespace']
            element.namespace = Namespace(
                prefix=ns_data.get('prefix', ''),
                uri=ns_data.get('uri', ''),
                local_name=ns_data.get('local_name', element.local_name)
            )
        
        # 设置属性
        if 'attributes' in data:
            for attr_name, attr_data in data['attributes'].items():
                element.add_attribute(
                    name=attr_data['name'],
                    value=attr_data['value'],
                    data_type=attr_data.get('data_type', 'string'),
                    is_required=attr_data.get('is_required', False)
                )
        
        # 设置引用
        if 'references' in data:
            for ref_data in data['references']:
                element.add_reference(
                    ref_id=ref_data['ref_id'],
                    ref_type=ref_data['ref_type'],
                    ref_uri=ref_data.get('ref_uri')
                )
        
        # 设置自定义数据
        element.custom_data = data.get('custom_data', {}).copy()
        
        return element
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), indent=2, ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Element':
        """从JSON字符串创建元素"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def clone(self) -> 'Element':
        """克隆元素"""
        return self.from_dict(self.to_dict())
    
    def __str__(self) -> str:
        return f"Element(id='{self.id}', tag='{self.tag}', type='{self.element_type.value}')"
    
    def __repr__(self) -> str:
        return self.__str__()
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, Element):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        return hash(self.id)

class ElementCollection:
    """元素集合类 - 管理多个元素"""
    
    def __init__(self):
        self.elements: Dict[str, Element] = {}
        self.metadata = {
            'created_at': datetime.now(),
            'total_count': 0,
            'version': '1.0'
        }
    
    def add_element(self, element: Element) -> 'ElementCollection':
        """添加元素"""
        self.elements[element.id] = element
        self.metadata['total_count'] = len(self.elements)
        return self
    
    def get_element(self, element_id: str) -> Optional[Element]:
        """获取元素"""
        return self.elements.get(element_id)
    
    def remove_element(self, element_id: str) -> bool:
        """移除元素"""
        if element_id in self.elements:
            del self.elements[element_id]
            self.metadata['total_count'] = len(self.elements)
            return True
        return False
    
    def get_all_elements(self) -> List[Element]:
        """获取所有元素"""
        return list(self.elements.values())
    
    def filter_by_type(self, element_type: ElementType) -> List[Element]:
        """按类型过滤元素"""
        return [elem for elem in self.elements.values() if elem.element_type == element_type]
    
    def __len__(self) -> int:
        return len(self.elements)
    
    def __iter__(self):
        return iter(self.elements.values())
    
    def __contains__(self, element_id: str) -> bool:
        return element_id in self.elements 