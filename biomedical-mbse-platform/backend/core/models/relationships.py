"""
关系数据模型 - 定义元素间的关系结构

支持多种关系类型、关系网络分析、关系验证等功能
"""

from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

from .base import BaseModel, ValidationResult

class RelationType(Enum):
    """关系类型枚举"""
    # UML基础关系
    DEPENDENCY = "dependency"          # 依赖关系
    ASSOCIATION = "association"        # 关联关系
    AGGREGATION = "aggregation"        # 聚合关系
    COMPOSITION = "composition"        # 组合关系
    GENERALIZATION = "generalization"  # 泛化关系
    REALIZATION = "realization"        # 实现关系
    
    # MBSE扩展关系
    ALLOCATION = "allocation"          # 分配关系
    SATISFY = "satisfy"               # 满足关系
    VERIFY = "verify"                 # 验证关系
    REFINE = "refine"                 # 细化关系
    DERIVE = "derive"                 # 导出关系
    TRACE = "trace"                   # 追踪关系
    
    # 结构关系
    CONTAINMENT = "containment"        # 包含关系
    REFERENCE = "reference"            # 引用关系
    INHERITANCE = "inheritance"        # 继承关系
    IMPLEMENTATION = "implementation"   # 实现关系

class RelationshipDirection(Enum):
    """关系方向"""
    UNIDIRECTIONAL = "unidirectional"  # 单向
    BIDIRECTIONAL = "bidirectional"    # 双向
    NONE = "none"                       # 无方向

@dataclass
class RelationshipConstraint:
    """关系约束"""
    min_cardinality: int = 0
    max_cardinality: Optional[int] = None  # None表示无上限
    required: bool = False
    unique: bool = False
    
class Relationship(BaseModel):
    """
    关系模型
    
    表示两个元素之间的关系，支持：
    - 多种关系类型
    - 关系属性和约束
    - 双向关系管理
    - 关系验证
    """
    
    def __init__(self, 
                 source_id: str,
                 target_id: str,
                 relation_type: RelationType,
                 relationship_id: Optional[str] = None,
                 direction: RelationshipDirection = RelationshipDirection.UNIDIRECTIONAL):
        super().__init__(relationship_id)
        
        self.source_id = source_id
        self.target_id = target_id
        self.relation_type = relation_type
        self.direction = direction
        
        # 关系属性
        self.attributes: Dict[str, Any] = {}
        self.weight: float = 1.0  # 关系权重
        self.confidence: float = 1.0  # 置信度
        self.strength: float = 1.0  # 关系强度
        
        # 约束和规则
        self.constraints: List[RelationshipConstraint] = []
        self.tags: Set[str] = set()
        
        # 发现和验证信息
        self.discovery_method: str = "manual"  # manual, automatic, inferred
        self.validation_status: str = "unvalidated"  # validated, unvalidated, invalid
        self.last_verified: Optional[datetime] = None
        
        # 上下文信息
        self.context: Dict[str, Any] = {}
        self.description: str = ""
    
    def add_attribute(self, name: str, value: Any) -> 'Relationship':
        """添加关系属性"""
        self.attributes[name] = value
        self._record_change('attribute_added', {'name': name, 'value': value})
        return self
    
    def add_constraint(self, constraint: RelationshipConstraint) -> 'Relationship':
        """添加关系约束"""
        self.constraints.append(constraint)
        self._record_change('constraint_added', {'constraint': constraint.__dict__})
        return self
    
    def add_tag(self, tag: str) -> 'Relationship':
        """添加标签"""
        self.tags.add(tag)
        self._record_change('tag_added', {'tag': tag})
        return self
    
    def set_weight(self, weight: float) -> 'Relationship':
        """设置关系权重"""
        old_weight = self.weight
        self.weight = max(0.0, min(1.0, weight))  # 限制在0-1之间
        self._record_change('weight_changed', {
            'old_weight': old_weight,
            'new_weight': self.weight
        })
        return self
    
    def set_confidence(self, confidence: float) -> 'Relationship':
        """设置置信度"""
        old_confidence = self.confidence
        self.confidence = max(0.0, min(1.0, confidence))
        self._record_change('confidence_changed', {
            'old_confidence': old_confidence,
            'new_confidence': self.confidence
        })
        return self
    
    def validate(self) -> ValidationResult:
        """验证关系的有效性"""
        result = ValidationResult()
        
        # 基础验证
        if not self.source_id:
            result.errors.append("源元素ID不能为空")
            result.is_valid = False
        
        if not self.target_id:
            result.errors.append("目标元素ID不能为空")
            result.is_valid = False
        
        if self.source_id == self.target_id:
            result.warnings.append("自引用关系可能不合理")
            result.score -= 10
        
        # 权重和置信度验证
        if not (0.0 <= self.weight <= 1.0):
            result.errors.append(f"关系权重必须在0-1之间: {self.weight}")
            result.is_valid = False
        
        if not (0.0 <= self.confidence <= 1.0):
            result.errors.append(f"置信度必须在0-1之间: {self.confidence}")
            result.is_valid = False
        
        # 约束验证
        for i, constraint in enumerate(self.constraints):
            if constraint.min_cardinality < 0:
                result.errors.append(f"约束{i}: 最小基数不能为负数")
                result.is_valid = False
            
            if (constraint.max_cardinality is not None and 
                constraint.max_cardinality < constraint.min_cardinality):
                result.errors.append(f"约束{i}: 最大基数不能小于最小基数")
                result.is_valid = False
        
        # 计算质量评分
        if result.is_valid:
            score = 100.0
            
            # 根据置信度调整评分
            score *= self.confidence
            
            # 根据是否有描述调整评分
            if not self.description:
                score -= 5
            
            # 根据验证状态调整评分
            if self.validation_status == "unvalidated":
                score -= 10
            elif self.validation_status == "invalid":
                score -= 30
            
            result.score = max(0, score)
        
        return result
    
    def get_reverse_relationship(self) -> Optional['Relationship']:
        """获取反向关系（如果是双向关系）"""
        if self.direction != RelationshipDirection.BIDIRECTIONAL:
            return None
        
        # 创建反向关系
        reverse_rel = Relationship(
            source_id=self.target_id,
            target_id=self.source_id,
            relation_type=self.relation_type,
            direction=RelationshipDirection.BIDIRECTIONAL
        )
        
        # 复制相关属性
        reverse_rel.weight = self.weight
        reverse_rel.confidence = self.confidence
        reverse_rel.attributes = self.attributes.copy()
        reverse_rel.tags = self.tags.copy()
        
        return reverse_rel
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'source_id': self.source_id,
            'target_id': self.target_id,
            'relation_type': self.relation_type.value,
            'direction': self.direction.value,
            'weight': self.weight,
            'confidence': self.confidence,
            'strength': self.strength,
            'attributes': self.attributes,
            'constraints': [constraint.__dict__ for constraint in self.constraints],
            'tags': list(self.tags),
            'discovery_method': self.discovery_method,
            'validation_status': self.validation_status,
            'last_verified': self.last_verified.isoformat() if self.last_verified else None,
            'context': self.context,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version,
            'metadata': self._metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Relationship':
        """从字典创建关系实例"""
        # 创建基本实例
        relationship = cls(
            source_id=data['source_id'],
            target_id=data['target_id'],
            relation_type=RelationType(data['relation_type']),
            relationship_id=data.get('id'),
            direction=RelationshipDirection(data.get('direction', 'unidirectional'))
        )
        
        # 设置其他属性
        relationship.weight = data.get('weight', 1.0)
        relationship.confidence = data.get('confidence', 1.0)
        relationship.strength = data.get('strength', 1.0)
        relationship.attributes = data.get('attributes', {})
        relationship.tags = set(data.get('tags', []))
        relationship.discovery_method = data.get('discovery_method', 'manual')
        relationship.validation_status = data.get('validation_status', 'unvalidated')
        relationship.context = data.get('context', {})
        relationship.description = data.get('description', '')
        
        # 设置约束
        constraints_data = data.get('constraints', [])
        relationship.constraints = [
            RelationshipConstraint(**constraint_data)
            for constraint_data in constraints_data
        ]
        
        # 设置时间戳
        if 'created_at' in data:
            relationship.created_at = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            relationship.updated_at = datetime.fromisoformat(data['updated_at'])
        if data.get('last_verified'):
            relationship.last_verified = datetime.fromisoformat(data['last_verified'])
        
        # 设置版本和元数据
        relationship.version = data.get('version', '1.0')
        relationship._metadata = data.get('metadata', {})
        
        return relationship

class RelationshipNetwork(BaseModel):
    """
    关系网络
    
    管理多个关系的集合，提供：
    - 关系网络构建
    - 网络分析功能
    - 路径查找
    - 连通性分析
    """
    
    def __init__(self, network_id: Optional[str] = None):
        super().__init__(network_id)
        
        self.relationships: Dict[str, Relationship] = {}
        self.nodes: Set[str] = set()  # 所有节点ID
        
        # 邻接表表示（用于快速查找）
        self.adjacency_list: Dict[str, Set[str]] = {}
        self.reverse_adjacency: Dict[str, Set[str]] = {}  # 反向邻接表
        
        # 网络统计
        self.total_nodes = 0
        self.total_relationships = 0
        self.relationship_types_count: Dict[RelationType, int] = {}
        
        # 网络属性
        self.name: str = ""
        self.description: str = ""
        self.domain: str = ""
        self.tags: Set[str] = set()
    
    def add_relationship(self, relationship: Relationship) -> 'RelationshipNetwork':
        """添加关系到网络"""
        self.relationships[relationship.id] = relationship
        
        # 更新节点集合
        self.nodes.add(relationship.source_id)
        self.nodes.add(relationship.target_id)
        
        # 更新邻接表
        if relationship.source_id not in self.adjacency_list:
            self.adjacency_list[relationship.source_id] = set()
        if relationship.target_id not in self.reverse_adjacency:
            self.reverse_adjacency[relationship.target_id] = set()
        
        self.adjacency_list[relationship.source_id].add(relationship.target_id)
        self.reverse_adjacency[relationship.target_id].add(relationship.source_id)
        
        # 如果是双向关系，也要添加反向
        if relationship.direction == RelationshipDirection.BIDIRECTIONAL:
            if relationship.target_id not in self.adjacency_list:
                self.adjacency_list[relationship.target_id] = set()
            if relationship.source_id not in self.reverse_adjacency:
                self.reverse_adjacency[relationship.source_id] = set()
            
            self.adjacency_list[relationship.target_id].add(relationship.source_id)
            self.reverse_adjacency[relationship.source_id].add(relationship.target_id)
        
        # 更新统计
        self._update_statistics()
        
        # 记录变更
        self._record_change('relationship_added', {
            'relationship_id': relationship.id,
            'source_id': relationship.source_id,
            'target_id': relationship.target_id,
            'relation_type': relationship.relation_type.value
        })
        
        return self
    
    def remove_relationship(self, relationship_id: str) -> bool:
        """从网络中移除关系"""
        if relationship_id not in self.relationships:
            return False
        
        relationship = self.relationships[relationship_id]
        
        # 从邻接表中移除
        if relationship.source_id in self.adjacency_list:
            self.adjacency_list[relationship.source_id].discard(relationship.target_id)
        if relationship.target_id in self.reverse_adjacency:
            self.reverse_adjacency[relationship.target_id].discard(relationship.source_id)
        
        # 如果是双向关系，也要移除反向
        if relationship.direction == RelationshipDirection.BIDIRECTIONAL:
            if relationship.target_id in self.adjacency_list:
                self.adjacency_list[relationship.target_id].discard(relationship.source_id)
            if relationship.source_id in self.reverse_adjacency:
                self.reverse_adjacency[relationship.source_id].discard(relationship.target_id)
        
        # 移除关系
        del self.relationships[relationship_id]
        
        # 检查并移除孤立节点
        self._remove_orphaned_nodes()
        
        # 更新统计
        self._update_statistics()
        
        # 记录变更
        self._record_change('relationship_removed', {
            'relationship_id': relationship_id
        })
        
        return True
    
    def find_path(self, source_id: str, target_id: str, max_depth: int = 10) -> Optional[List[str]]:
        """查找两个节点之间的路径"""
        if source_id not in self.nodes or target_id not in self.nodes:
            return None
        
        if source_id == target_id:
            return [source_id]
        
        # 使用BFS查找最短路径
        from collections import deque
        
        queue = deque([(source_id, [source_id])])
        visited = {source_id}
        
        while queue:
            current_node, path = queue.popleft()
            
            if len(path) > max_depth:
                continue
            
            # 查看所有邻居
            neighbors = self.adjacency_list.get(current_node, set())
            
            for neighbor in neighbors:
                if neighbor == target_id:
                    return path + [neighbor]
                
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [neighbor]))
        
        return None
    
    def get_connected_components(self) -> List[Set[str]]:
        """获取所有连通分量"""
        visited = set()
        components = []
        
        for node in self.nodes:
            if node not in visited:
                component = set()
                self._dfs_component(node, visited, component)
                components.append(component)
        
        return components
    
    def get_relationship_by_nodes(self, source_id: str, target_id: str) -> List[Relationship]:
        """获取两个节点间的所有关系"""
        relationships = []
        
        for relationship in self.relationships.values():
            if (relationship.source_id == source_id and relationship.target_id == target_id) or \
               (relationship.direction == RelationshipDirection.BIDIRECTIONAL and
                relationship.source_id == target_id and relationship.target_id == source_id):
                relationships.append(relationship)
        
        return relationships
    
    def validate(self) -> ValidationResult:
        """验证关系网络"""
        result = ValidationResult()
        
        # 验证所有关系
        invalid_relationships = []
        total_score = 0
        
        for rel_id, relationship in self.relationships.items():
            rel_result = relationship.validate()
            if not rel_result.is_valid:
                invalid_relationships.append(rel_id)
                result.errors.extend([f"关系{rel_id}: {error}" for error in rel_result.errors])
            total_score += rel_result.score
        
        # 检查网络完整性
        if len(invalid_relationships) > 0:
            result.warnings.append(f"发现{len(invalid_relationships)}个无效关系")
        
        # 检查孤立节点
        isolated_nodes = self._find_isolated_nodes()
        if isolated_nodes:
            result.warnings.append(f"发现{len(isolated_nodes)}个孤立节点")
        
        # 计算整体评分
        if self.relationships:
            result.score = total_score / len(self.relationships)
        else:
            result.score = 100.0
        
        result.is_valid = len(result.errors) == 0
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'domain': self.domain,
            'tags': list(self.tags),
            'relationships': {rel_id: rel.to_dict() for rel_id, rel in self.relationships.items()},
            'nodes': list(self.nodes),
            'statistics': {
                'total_nodes': self.total_nodes,
                'total_relationships': self.total_relationships,
                'relationship_types_count': {rt.value: count for rt, count in self.relationship_types_count.items()}
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version,
            'metadata': self._metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RelationshipNetwork':
        """从字典创建关系网络实例"""
        network = cls(data.get('id'))
        
        # 设置基本属性
        network.name = data.get('name', '')
        network.description = data.get('description', '')
        network.domain = data.get('domain', '')
        network.tags = set(data.get('tags', []))
        
        # 添加关系
        relationships_data = data.get('relationships', {})
        for rel_data in relationships_data.values():
            relationship = Relationship.from_dict(rel_data)
            network.add_relationship(relationship)
        
        # 设置时间戳和版本
        if 'created_at' in data:
            network.created_at = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            network.updated_at = datetime.fromisoformat(data['updated_at'])
        network.version = data.get('version', '1.0')
        network._metadata = data.get('metadata', {})
        
        return network
    
    def _update_statistics(self):
        """更新网络统计信息"""
        self.total_nodes = len(self.nodes)
        self.total_relationships = len(self.relationships)
        
        # 统计关系类型
        self.relationship_types_count.clear()
        for relationship in self.relationships.values():
            rel_type = relationship.relation_type
            self.relationship_types_count[rel_type] = self.relationship_types_count.get(rel_type, 0) + 1
    
    def _remove_orphaned_nodes(self):
        """移除孤立节点"""
        connected_nodes = set()
        
        for relationship in self.relationships.values():
            connected_nodes.add(relationship.source_id)
            connected_nodes.add(relationship.target_id)
        
        self.nodes = connected_nodes
        
        # 清理邻接表
        for node_id in list(self.adjacency_list.keys()):
            if node_id not in connected_nodes:
                del self.adjacency_list[node_id]
        
        for node_id in list(self.reverse_adjacency.keys()):
            if node_id not in connected_nodes:
                del self.reverse_adjacency[node_id]
    
    def _find_isolated_nodes(self) -> Set[str]:
        """查找孤立节点"""
        isolated = set()
        
        for node in self.nodes:
            has_outgoing = node in self.adjacency_list and len(self.adjacency_list[node]) > 0
            has_incoming = node in self.reverse_adjacency and len(self.reverse_adjacency[node]) > 0
            
            if not has_outgoing and not has_incoming:
                isolated.add(node)
        
        return isolated
    
    def _dfs_component(self, node: str, visited: Set[str], component: Set[str]):
        """DFS遍历连通分量"""
        visited.add(node)
        component.add(node)
        
        # 遍历所有邻居（包括出边和入边）
        neighbors = set()
        if node in self.adjacency_list:
            neighbors.update(self.adjacency_list[node])
        if node in self.reverse_adjacency:
            neighbors.update(self.reverse_adjacency[node])
        
        for neighbor in neighbors:
            if neighbor not in visited:
                self._dfs_component(neighbor, visited, component) 