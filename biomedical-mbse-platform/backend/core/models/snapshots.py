"""
快照数据模型 - 分层解析快照结构

支持ElementSnapshot、LayerSnapshot等快照类型，
用于分层解析器的渐进式数据管理
"""

from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

from .base import BaseModel, ValidationResult

class SnapshotType(Enum):
    """快照类型"""
    ELEMENT = "element"         # 元素快照
    LAYER = "layer"            # 层级快照
    BATCH = "batch"            # 批次快照
    INCREMENTAL = "incremental" # 增量快照
    FULL = "full"              # 完整快照

class CompletionStatus(Enum):
    """完成状态"""
    COMPLETE = "complete"       # 完成
    PARTIAL = "partial"         # 部分完成
    PLACEHOLDER = "placeholder" # 占位符
    PENDING = "pending"         # 待处理
    ERROR = "error"            # 错误

class LayerLevel(Enum):
    """层级级别"""
    LAYER1 = 1  # 根层
    LAYER2 = 2  # 中间层
    LAYER3 = 3  # 叶子层

@dataclass
class ProcessingMetrics:
    """处理指标"""
    processing_time: float = 0.0
    memory_usage: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0
    elements_processed: int = 0

class ElementSnapshot(BaseModel):
    """
    元素快照 - 12维完整元素结构
    
    用于分层解析器的核心数据结构，包含：
    - 基本元素信息
    - 语义分析结果
    - 关系信息
    - 位置和层级信息
    - 处理状态和指标
    """
    
    def __init__(self, 
                 element_id: str,
                 tag: str,
                 layer_level: LayerLevel,
                 snapshot_id: Optional[str] = None):
        super().__init__(snapshot_id)
        
        # 基本元素信息 (维度1-4)
        self.element_id = element_id
        self.tag = tag
        self.local_name = tag.split('}')[-1] if '}' in tag else tag
        self.layer_level = layer_level
        
        # 属性和内容 (维度5-6)
        self.attributes: Dict[str, str] = {}
        self.text_content: str = ""
        
        # 关系信息 (维度7-8)
        self.parent_id: Optional[str] = None
        self.children_ids: List[str] = []
        self.relationships: Dict[str, List[str]] = {
            'dependencies': [],
            'associations': [],
            'references': []
        }
        
        # 语义信息 (维度9)
        self.semantic_info: Dict[str, Any] = {
            'domain_category': 'unknown',
            'domain_code': 'UNK',
            'confidence_score': 0.0,
            'semantic_tags': [],
            'classification': {}
        }
        
        # 命名空间信息 (维度10)
        self.namespace_info: Dict[str, str] = {
            'namespace': '',
            'prefix': '',
            'uri': '',
            'local_name': self.local_name
        }
        
        # 位置和深度信息 (维度11)
        self.position_info: Dict[str, Any] = {
            'depth_level': 0,
            'xpath': '',
            'line_number': 0,
            'element_index': 0
        }
        
        # 稳定性和处理信息 (维度12)
        self.stability_score: float = 1.0
        self.completion_status: CompletionStatus = CompletionStatus.COMPLETE
        self.processing_metrics: ProcessingMetrics = ProcessingMetrics()
        
        # 快照特有属性
        self.snapshot_timestamp: datetime = datetime.now()
        self.source_parser: str = "layered_snapshot"
        self.quality_indicators: Dict[str, float] = {}
    
    def set_attributes(self, attributes: Dict[str, str]) -> 'ElementSnapshot':
        """设置属性"""
        self.attributes = attributes.copy()
        self._record_change('attributes_set', {'count': len(attributes)})
        return self
    
    def set_text_content(self, content: str) -> 'ElementSnapshot':
        """设置文本内容"""
        old_content = self.text_content
        self.text_content = content.strip()
        self._record_change('text_content_changed', {
            'old_length': len(old_content),
            'new_length': len(self.text_content)
        })
        return self
    
    def add_child(self, child_id: str) -> 'ElementSnapshot':
        """添加子元素"""
        if child_id not in self.children_ids:
            self.children_ids.append(child_id)
            self._record_change('child_added', {'child_id': child_id})
        return self
    
    def add_relationship(self, rel_type: str, target_id: str) -> 'ElementSnapshot':
        """添加关系"""
        if rel_type not in self.relationships:
            self.relationships[rel_type] = []
        
        if target_id not in self.relationships[rel_type]:
            self.relationships[rel_type].append(target_id)
            self._record_change('relationship_added', {
                'type': rel_type,
                'target_id': target_id
            })
        return self
    
    def set_semantic_info(self, domain_category: str, domain_code: str = "", 
                         confidence_score: float = 0.0) -> 'ElementSnapshot':
        """设置语义信息"""
        old_category = self.semantic_info.get('domain_category')
        
        self.semantic_info.update({
            'domain_category': domain_category,
            'domain_code': domain_code or domain_category.upper()[:3],
            'confidence_score': confidence_score
        })
        
        self._record_change('semantic_info_updated', {
            'old_category': old_category,
            'new_category': domain_category,
            'confidence': confidence_score
        })
        return self
    
    def set_position(self, depth: int = 0, xpath: str = "", 
                    line: int = 0, index: int = 0) -> 'ElementSnapshot':
        """设置位置信息"""
        self.position_info.update({
            'depth_level': depth,
            'xpath': xpath,
            'line_number': line,
            'element_index': index
        })
        self._record_change('position_updated', self.position_info.copy())
        return self
    
    def update_processing_metrics(self, **metrics) -> 'ElementSnapshot':
        """更新处理指标"""
        for key, value in metrics.items():
            if hasattr(self.processing_metrics, key):
                setattr(self.processing_metrics, key, value)
        
        self._record_change('metrics_updated', metrics)
        return self
    
    def calculate_quality_score(self) -> float:
        """计算质量评分"""
        score = 100.0
        
        # 完整性评分
        if not self.attributes:
            score -= 10
        if not self.text_content:
            score -= 5
        if not self.semantic_info.get('domain_category') or self.semantic_info['domain_category'] == 'unknown':
            score -= 15
        
        # 语义置信度评分
        semantic_confidence = self.semantic_info.get('confidence_score', 0.0)
        score *= (0.7 + 0.3 * semantic_confidence)  # 基础70%，语义贡献30%
        
        # 稳定性评分
        score *= self.stability_score
        
        # 完成状态评分
        if self.completion_status == CompletionStatus.PARTIAL:
            score *= 0.8
        elif self.completion_status == CompletionStatus.PLACEHOLDER:
            score *= 0.5
        elif self.completion_status == CompletionStatus.ERROR:
            score *= 0.1
        
        return max(0, min(100, score))
    
    def validate(self) -> ValidationResult:
        """验证快照有效性"""
        result = ValidationResult()
        
        # 基础验证
        if not self.element_id:
            result.errors.append("元素ID不能为空")
            result.is_valid = False
        
        if not self.tag:
            result.errors.append("标签不能为空")
            result.is_valid = False
        
        # 语义信息验证
        confidence = self.semantic_info.get('confidence_score', 0.0)
        if not (0.0 <= confidence <= 1.0):
            result.errors.append(f"语义置信度必须在0-1之间: {confidence}")
            result.is_valid = False
        
        # 稳定性验证
        if not (0.0 <= self.stability_score <= 1.0):
            result.errors.append(f"稳定性评分必须在0-1之间: {self.stability_score}")
            result.is_valid = False
        
        # 层级验证
        if self.layer_level.value < 1 or self.layer_level.value > 3:
            result.warnings.append(f"非标准层级: {self.layer_level.value}")
        
        # 关系完整性验证
        for rel_type, targets in self.relationships.items():
            if not isinstance(targets, list):
                result.errors.append(f"关系{rel_type}必须是列表类型")
                result.is_valid = False
        
        # 计算质量评分
        result.score = self.calculate_quality_score()
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'element_id': self.element_id,
            'tag': self.tag,
            'local_name': self.local_name,
            'layer_level': self.layer_level.value,
            'attributes': self.attributes,
            'text_content': self.text_content,
            'parent_id': self.parent_id,
            'children_ids': self.children_ids,
            'relationships': self.relationships,
            'semantic_info': self.semantic_info,
            'namespace_info': self.namespace_info,
            'position_info': self.position_info,
            'stability_score': self.stability_score,
            'completion_status': self.completion_status.value,
            'processing_metrics': {
                'processing_time': self.processing_metrics.processing_time,
                'memory_usage': self.processing_metrics.memory_usage,
                'cache_hits': self.processing_metrics.cache_hits,
                'cache_misses': self.processing_metrics.cache_misses,
                'elements_processed': self.processing_metrics.elements_processed
            },
            'snapshot_timestamp': self.snapshot_timestamp.isoformat(),
            'source_parser': self.source_parser,
            'quality_indicators': self.quality_indicators,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version,
            'metadata': self._metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ElementSnapshot':
        """从字典创建快照实例"""
        snapshot = cls(
            element_id=data['element_id'],
            tag=data['tag'],
            layer_level=LayerLevel(data['layer_level']),
            snapshot_id=data.get('id')
        )
        
        # 设置属性
        snapshot.local_name = data.get('local_name', snapshot.local_name)
        snapshot.attributes = data.get('attributes', {})
        snapshot.text_content = data.get('text_content', '')
        snapshot.parent_id = data.get('parent_id')
        snapshot.children_ids = data.get('children_ids', [])
        snapshot.relationships = data.get('relationships', {})
        snapshot.semantic_info = data.get('semantic_info', {})
        snapshot.namespace_info = data.get('namespace_info', {})
        snapshot.position_info = data.get('position_info', {})
        snapshot.stability_score = data.get('stability_score', 1.0)
        snapshot.completion_status = CompletionStatus(data.get('completion_status', 'complete'))
        snapshot.source_parser = data.get('source_parser', 'layered_snapshot')
        snapshot.quality_indicators = data.get('quality_indicators', {})
        
        # 设置处理指标
        metrics_data = data.get('processing_metrics', {})
        snapshot.processing_metrics = ProcessingMetrics(
            processing_time=metrics_data.get('processing_time', 0.0),
            memory_usage=metrics_data.get('memory_usage', 0.0),
            cache_hits=metrics_data.get('cache_hits', 0),
            cache_misses=metrics_data.get('cache_misses', 0),
            elements_processed=metrics_data.get('elements_processed', 0)
        )
        
        # 设置时间戳
        if 'snapshot_timestamp' in data:
            snapshot.snapshot_timestamp = datetime.fromisoformat(data['snapshot_timestamp'])
        if 'created_at' in data:
            snapshot.created_at = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            snapshot.updated_at = datetime.fromisoformat(data['updated_at'])
        
        # 设置版本和元数据
        snapshot.version = data.get('version', '1.0')
        snapshot._metadata = data.get('metadata', {})
        
        return snapshot

class LayerSnapshot(BaseModel):
    """
    层级快照
    
    管理特定层级的所有元素快照，提供：
    - 层级统计信息
    - 批次处理指标
    - 层级质量评估
    """
    
    def __init__(self, layer_level: LayerLevel, snapshot_id: Optional[str] = None):
        super().__init__(snapshot_id)
        
        self.layer_level = layer_level
        self.element_snapshots: Dict[str, ElementSnapshot] = {}
        
        # 层级统计
        self.total_elements = 0
        self.processed_elements = 0
        self.completed_elements = 0
        self.failed_elements = 0
        
        # 处理指标
        self.layer_processing_time = 0.0
        self.average_element_time = 0.0
        self.cache_hit_rate = 0.0
        
        # 质量指标
        self.average_quality_score = 0.0
        self.semantic_coverage = 0.0  # 有语义分析的元素比例
        self.completion_rate = 0.0
        
        # 层级属性
        self.max_depth = 0
        self.coverage_ratio = 0.0
    
    def add_element_snapshot(self, snapshot: ElementSnapshot) -> 'LayerSnapshot':
        """添加元素快照"""
        self.element_snapshots[snapshot.element_id] = snapshot
        self._update_statistics()
        self._record_change('element_added', {
            'element_id': snapshot.element_id,
            'completion_status': snapshot.completion_status.value
        })
        return self
    
    def remove_element_snapshot(self, element_id: str) -> bool:
        """移除元素快照"""
        if element_id in self.element_snapshots:
            snapshot = self.element_snapshots.pop(element_id)
            self._update_statistics()
            self._record_change('element_removed', {
                'element_id': element_id,
                'completion_status': snapshot.completion_status.value
            })
            return True
        return False
    
    def get_snapshots_by_status(self, status: CompletionStatus) -> List[ElementSnapshot]:
        """按状态获取快照"""
        return [snapshot for snapshot in self.element_snapshots.values() 
                if snapshot.completion_status == status]
    
    def get_semantic_distribution(self) -> Dict[str, int]:
        """获取语义分布统计"""
        distribution = {}
        for snapshot in self.element_snapshots.values():
            category = snapshot.semantic_info.get('domain_category', 'unknown')
            distribution[category] = distribution.get(category, 0) + 1
        return distribution
    
    def _update_statistics(self):
        """更新统计信息"""
        snapshots = list(self.element_snapshots.values())
        self.total_elements = len(snapshots)
        
        if not snapshots:
            return
        
        # 状态统计
        self.completed_elements = len([s for s in snapshots if s.completion_status == CompletionStatus.COMPLETE])
        self.failed_elements = len([s for s in snapshots if s.completion_status == CompletionStatus.ERROR])
        self.processed_elements = self.total_elements - len([s for s in snapshots if s.completion_status == CompletionStatus.PENDING])
        
        # 处理时间统计
        processing_times = [s.processing_metrics.processing_time for s in snapshots]
        self.layer_processing_time = sum(processing_times)
        self.average_element_time = self.layer_processing_time / len(snapshots) if snapshots else 0
        
        # 缓存统计
        total_hits = sum(s.processing_metrics.cache_hits for s in snapshots)
        total_misses = sum(s.processing_metrics.cache_misses for s in snapshots)
        total_accesses = total_hits + total_misses
        self.cache_hit_rate = (total_hits / total_accesses * 100) if total_accesses > 0 else 0
        
        # 质量统计
        quality_scores = [s.calculate_quality_score() for s in snapshots]
        self.average_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        
        # 语义覆盖率
        semantic_elements = len([s for s in snapshots 
                               if s.semantic_info.get('domain_category', 'unknown') != 'unknown'])
        self.semantic_coverage = (semantic_elements / len(snapshots) * 100) if snapshots else 0
        
        # 完成率
        self.completion_rate = (self.completed_elements / len(snapshots) * 100) if snapshots else 0
        
        # 深度信息
        depths = [s.position_info.get('depth_level', 0) for s in snapshots]
        self.max_depth = max(depths) if depths else 0
    
    def validate(self) -> ValidationResult:
        """验证层级快照"""
        result = ValidationResult()
        
        # 验证所有元素快照
        invalid_snapshots = []
        total_score = 0
        
        for snapshot in self.element_snapshots.values():
            snapshot_result = snapshot.validate()
            if not snapshot_result.is_valid:
                invalid_snapshots.append(snapshot.element_id)
                result.errors.extend([f"快照{snapshot.element_id}: {error}" 
                                    for error in snapshot_result.errors])
            total_score += snapshot_result.score
        
        # 层级完整性检查
        if invalid_snapshots:
            result.warnings.append(f"层级{self.layer_level.value}中有{len(invalid_snapshots)}个无效快照")
        
        # 性能检查
        if self.completion_rate < 80:
            result.warnings.append(f"完成率较低: {self.completion_rate:.1f}%")
        
        if self.semantic_coverage < 60:
            result.warnings.append(f"语义覆盖率较低: {self.semantic_coverage:.1f}%")
        
        # 计算平均评分
        if self.element_snapshots:
            result.score = total_score / len(self.element_snapshots)
        else:
            result.score = 100.0
        
        result.is_valid = len(result.errors) == 0
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'layer_level': self.layer_level.value,
            'element_snapshots': {eid: snapshot.to_dict() 
                                 for eid, snapshot in self.element_snapshots.items()},
            'statistics': {
                'total_elements': self.total_elements,
                'processed_elements': self.processed_elements,
                'completed_elements': self.completed_elements,
                'failed_elements': self.failed_elements,
                'layer_processing_time': self.layer_processing_time,
                'average_element_time': self.average_element_time,
                'cache_hit_rate': self.cache_hit_rate,
                'average_quality_score': self.average_quality_score,
                'semantic_coverage': self.semantic_coverage,
                'completion_rate': self.completion_rate,
                'max_depth': self.max_depth,
                'coverage_ratio': self.coverage_ratio
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version,
            'metadata': self._metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LayerSnapshot':
        """从字典创建层级快照实例"""
        layer_snapshot = cls(
            layer_level=LayerLevel(data['layer_level']),
            snapshot_id=data.get('id')
        )
        
        # 恢复元素快照
        snapshots_data = data.get('element_snapshots', {})
        for snapshot_data in snapshots_data.values():
            element_snapshot = ElementSnapshot.from_dict(snapshot_data)
            layer_snapshot.add_element_snapshot(element_snapshot)
        
        # 恢复统计信息（会被_update_statistics覆盖，但保持一致性）
        stats = data.get('statistics', {})
        layer_snapshot.coverage_ratio = stats.get('coverage_ratio', 0.0)
        
        # 设置时间戳和版本
        if 'created_at' in data:
            layer_snapshot.created_at = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            layer_snapshot.updated_at = datetime.fromisoformat(data['updated_at'])
        layer_snapshot.version = data.get('version', '1.0')
        layer_snapshot._metadata = data.get('metadata', {})
        
        return layer_snapshot 