"""
术语数据模型 - 术语分析和术语库管理

支持术语定义、分类、关系管理、术语库构建等功能
"""

from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import re

from .base import BaseModel, ValidationResult, ModelCollection

class TerminologyDomain(Enum):
    """术语领域"""
    MBSE = "mbse"                    # 基于模型的系统工程
    UML = "uml"                      # 统一建模语言
    SYSML = "sysml"                  # 系统建模语言
    REQUIREMENTS = "requirements"     # 需求工程
    ARCHITECTURE = "architecture"    # 架构设计
    VERIFICATION = "verification"    # 验证与确认
    GENERAL = "general"              # 通用术语

class TerminologyCategory(Enum):
    """术语分类"""
    CONCEPT = "concept"              # 概念术语
    ELEMENT = "element"              # 元素术语
    RELATIONSHIP = "relationship"    # 关系术语
    ATTRIBUTE = "attribute"          # 属性术语
    STEREOTYPE = "stereotype"        # 构造型术语
    OPERATION = "operation"          # 操作术语
    STATE = "state"                  # 状态术语
    EVENT = "event"                  # 事件术语

class TerminologyStatus(Enum):
    """术语状态"""
    ACTIVE = "active"                # 活跃
    DEPRECATED = "deprecated"        # 废弃
    CANDIDATE = "candidate"          # 候选
    REJECTED = "rejected"            # 拒绝
    REVIEW = "review"                # 审核中

@dataclass
class TerminologyDefinition:
    """术语定义"""
    primary_definition: str
    context: str = ""
    source: str = ""
    examples: List[str] = field(default_factory=list)
    notes: str = ""

@dataclass
class TerminologyTranslation:
    """术语翻译"""
    language: str
    term: str
    definition: str = ""
    source: str = ""

class Term(BaseModel):
    """
    术语模型
    
    表示单个术语，包含：
    - 术语基本信息
    - 多语言支持
    - 分类和标签
    - 关系管理
    - 使用统计
    """
    
    def __init__(self, 
                 term_text: str,
                 domain: TerminologyDomain,
                 category: TerminologyCategory,
                 term_id: Optional[str] = None):
        super().__init__(term_id)
        
        # 基本信息
        self.term_text = term_text.strip()
        self.normalized_text = self._normalize_term(term_text)
        self.domain = domain
        self.category = category
        self.status = TerminologyStatus.ACTIVE
        
        # 定义和描述
        self.primary_definition = TerminologyDefinition("")
        self.alternative_definitions: List[TerminologyDefinition] = []
        
        # 多语言支持
        self.translations: Dict[str, TerminologyTranslation] = {}
        
        # 分类和标签
        self.tags: Set[str] = set()
        self.synonyms: Set[str] = set()
        self.abbreviations: Set[str] = set()
        self.alternative_spellings: Set[str] = set()
        
        # 关系信息
        self.broader_terms: Set[str] = set()    # 上位术语
        self.narrower_terms: Set[str] = set()   # 下位术语
        self.related_terms: Set[str] = set()    # 相关术语
        self.opposite_terms: Set[str] = set()   # 对立术语
        
        # 使用信息
        self.usage_frequency: int = 0
        self.context_usage: Dict[str, int] = {}  # 不同上下文中的使用次数
        self.first_seen: Optional[datetime] = None
        self.last_used: Optional[datetime] = None
        
        # 质量指标
        self.confidence_score: float = 1.0
        self.completeness_score: float = 0.0
        self.validation_score: float = 0.0
    
    def _normalize_term(self, text: str) -> str:
        """标准化术语文本"""
        # 转小写、去空格、移除特殊字符
        normalized = text.lower().strip()
        normalized = re.sub(r'[^\w\s-]', '', normalized)
        normalized = re.sub(r'\s+', ' ', normalized)
        return normalized
    
    def set_primary_definition(self, definition: str, context: str = "", 
                              source: str = "") -> 'Term':
        """设置主要定义"""
        old_definition = self.primary_definition.primary_definition
        self.primary_definition = TerminologyDefinition(
            primary_definition=definition,
            context=context,
            source=source
        )
        
        self._record_change('primary_definition_changed', {
            'old_definition': old_definition,
            'new_definition': definition
        })
        
        self._update_completeness_score()
        return self
    
    def add_alternative_definition(self, definition: TerminologyDefinition) -> 'Term':
        """添加备选定义"""
        self.alternative_definitions.append(definition)
        self._record_change('alternative_definition_added', {
            'definition': definition.primary_definition
        })
        return self
    
    def add_translation(self, language: str, term: str, definition: str = "", 
                       source: str = "") -> 'Term':
        """添加翻译"""
        translation = TerminologyTranslation(
            language=language,
            term=term,
            definition=definition,
            source=source
        )
        
        self.translations[language] = translation
        self._record_change('translation_added', {
            'language': language,
            'term': term
        })
        return self
    
    def add_synonym(self, synonym: str) -> 'Term':
        """添加同义词"""
        normalized_synonym = self._normalize_term(synonym)
        if normalized_synonym != self.normalized_text:
            self.synonyms.add(synonym)
            self._record_change('synonym_added', {'synonym': synonym})
        return self
    
    def add_abbreviation(self, abbreviation: str) -> 'Term':
        """添加缩写"""
        self.abbreviations.add(abbreviation)
        self._record_change('abbreviation_added', {'abbreviation': abbreviation})
        return self
    
    def add_broader_term(self, term_id: str) -> 'Term':
        """添加上位术语"""
        self.broader_terms.add(term_id)
        self._record_change('broader_term_added', {'term_id': term_id})
        return self
    
    def add_narrower_term(self, term_id: str) -> 'Term':
        """添加下位术语"""
        self.narrower_terms.add(term_id)
        self._record_change('narrower_term_added', {'term_id': term_id})
        return self
    
    def add_related_term(self, term_id: str) -> 'Term':
        """添加相关术语"""
        self.related_terms.add(term_id)
        self._record_change('related_term_added', {'term_id': term_id})
        return self
    
    def record_usage(self, context: str = "general") -> 'Term':
        """记录术语使用"""
        self.usage_frequency += 1
        self.context_usage[context] = self.context_usage.get(context, 0) + 1
        self.last_used = datetime.now()
        
        if self.first_seen is None:
            self.first_seen = datetime.now()
        
        self._record_change('usage_recorded', {
            'context': context,
            'total_frequency': self.usage_frequency
        })
        return self
    
    def _update_completeness_score(self):
        """更新完整性评分"""
        score = 0.0
        
        # 基本信息 (40%)
        if self.primary_definition.primary_definition:
            score += 25
        if self.primary_definition.context:
            score += 10
        if self.primary_definition.examples:
            score += 5
        
        # 分类信息 (20%)
        if self.tags:
            score += 10
        if self.synonyms:
            score += 5
        if self.abbreviations:
            score += 5
        
        # 关系信息 (20%)
        total_relations = len(self.broader_terms) + len(self.narrower_terms) + len(self.related_terms)
        if total_relations > 0:
            score += min(20, total_relations * 5)
        
        # 多语言支持 (10%)
        if self.translations:
            score += min(10, len(self.translations) * 3)
        
        # 使用信息 (10%)
        if self.usage_frequency > 0:
            score += 5
        if self.context_usage:
            score += 5
        
        self.completeness_score = min(100, score)
    
    def calculate_similarity(self, other: 'Term') -> float:
        """计算与另一个术语的相似度"""
        if not isinstance(other, Term):
            return 0.0
        
        similarity = 0.0
        
        # 文本相似度 (30%)
        if self.normalized_text == other.normalized_text:
            similarity += 30
        elif self._text_similarity(self.normalized_text, other.normalized_text) > 0.8:
            similarity += 20
        
        # 同义词匹配 (20%)
        if (self.term_text in other.synonyms or 
            other.term_text in self.synonyms or
            bool(self.synonyms & other.synonyms)):
            similarity += 20
        
        # 领域匹配 (20%)
        if self.domain == other.domain:
            similarity += 20
        
        # 分类匹配 (15%)
        if self.category == other.category:
            similarity += 15
        
        # 标签匹配 (10%)
        if self.tags & other.tags:
            tag_similarity = len(self.tags & other.tags) / max(len(self.tags | other.tags), 1)
            similarity += 10 * tag_similarity
        
        # 关系匹配 (5%)
        all_relations_self = self.broader_terms | self.narrower_terms | self.related_terms
        all_relations_other = other.broader_terms | other.narrower_terms | other.related_terms
        if all_relations_self & all_relations_other:
            relation_similarity = len(all_relations_self & all_relations_other) / max(len(all_relations_self | all_relations_other), 1)
            similarity += 5 * relation_similarity
        
        return min(100, similarity)
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简化版Jaccard相似度）"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def validate(self) -> ValidationResult:
        """验证术语有效性"""
        result = ValidationResult()
        
        # 基础验证
        if not self.term_text:
            result.errors.append("术语文本不能为空")
            result.is_valid = False
        
        if len(self.term_text) < 2:
            result.warnings.append("术语文本过短，可能不是有效术语")
        
        # 定义验证
        if not self.primary_definition.primary_definition:
            result.warnings.append("缺少主要定义")
            result.score -= 20
        
        # 置信度验证
        if not (0.0 <= self.confidence_score <= 1.0):
            result.errors.append(f"置信度必须在0-1之间: {self.confidence_score}")
            result.is_valid = False
        
        # 完整性检查
        self._update_completeness_score()
        if self.completeness_score < 30:
            result.warnings.append(f"术语信息不完整: {self.completeness_score:.1f}%")
        
        # 关系一致性检查
        if self.broader_terms & self.narrower_terms:
            result.warnings.append("上位术语和下位术语存在重叠")
        
        # 计算最终评分
        result.score = (self.completeness_score * 0.6 + 
                       self.confidence_score * 100 * 0.3 + 
                       (100 - len(result.warnings) * 5) * 0.1)
        
        result.score = max(0, min(100, result.score))
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'term_text': self.term_text,
            'normalized_text': self.normalized_text,
            'domain': self.domain.value,
            'category': self.category.value,
            'status': self.status.value,
            'primary_definition': {
                'primary_definition': self.primary_definition.primary_definition,
                'context': self.primary_definition.context,
                'source': self.primary_definition.source,
                'examples': self.primary_definition.examples,
                'notes': self.primary_definition.notes
            },
            'alternative_definitions': [
                {
                    'primary_definition': defn.primary_definition,
                    'context': defn.context,
                    'source': defn.source,
                    'examples': defn.examples,
                    'notes': defn.notes
                } for defn in self.alternative_definitions
            ],
            'translations': {
                lang: {
                    'language': trans.language,
                    'term': trans.term,
                    'definition': trans.definition,
                    'source': trans.source
                } for lang, trans in self.translations.items()
            },
            'tags': list(self.tags),
            'synonyms': list(self.synonyms),
            'abbreviations': list(self.abbreviations),
            'alternative_spellings': list(self.alternative_spellings),
            'broader_terms': list(self.broader_terms),
            'narrower_terms': list(self.narrower_terms),
            'related_terms': list(self.related_terms),
            'opposite_terms': list(self.opposite_terms),
            'usage_frequency': self.usage_frequency,
            'context_usage': self.context_usage,
            'first_seen': self.first_seen.isoformat() if self.first_seen else None,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'confidence_score': self.confidence_score,
            'completeness_score': self.completeness_score,
            'validation_score': self.validation_score,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version,
            'metadata': self._metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Term':
        """从字典创建术语实例"""
        term = cls(
            term_text=data['term_text'],
            domain=TerminologyDomain(data['domain']),
            category=TerminologyCategory(data['category']),
            term_id=data.get('id')
        )
        
        # 设置基本属性
        term.normalized_text = data.get('normalized_text', term.normalized_text)
        term.status = TerminologyStatus(data.get('status', 'active'))
        
        # 设置主要定义
        primary_def_data = data.get('primary_definition', {})
        term.primary_definition = TerminologyDefinition(
            primary_definition=primary_def_data.get('primary_definition', ''),
            context=primary_def_data.get('context', ''),
            source=primary_def_data.get('source', ''),
            examples=primary_def_data.get('examples', []),
            notes=primary_def_data.get('notes', '')
        )
        
        # 设置备选定义
        alt_defs_data = data.get('alternative_definitions', [])
        term.alternative_definitions = [
            TerminologyDefinition(
                primary_definition=defn_data.get('primary_definition', ''),
                context=defn_data.get('context', ''),
                source=defn_data.get('source', ''),
                examples=defn_data.get('examples', []),
                notes=defn_data.get('notes', '')
            ) for defn_data in alt_defs_data
        ]
        
        # 设置翻译
        translations_data = data.get('translations', {})
        for lang, trans_data in translations_data.items():
            term.translations[lang] = TerminologyTranslation(
                language=trans_data['language'],
                term=trans_data['term'],
                definition=trans_data.get('definition', ''),
                source=trans_data.get('source', '')
            )
        
        # 设置集合属性
        term.tags = set(data.get('tags', []))
        term.synonyms = set(data.get('synonyms', []))
        term.abbreviations = set(data.get('abbreviations', []))
        term.alternative_spellings = set(data.get('alternative_spellings', []))
        term.broader_terms = set(data.get('broader_terms', []))
        term.narrower_terms = set(data.get('narrower_terms', []))
        term.related_terms = set(data.get('related_terms', []))
        term.opposite_terms = set(data.get('opposite_terms', []))
        
        # 设置使用信息
        term.usage_frequency = data.get('usage_frequency', 0)
        term.context_usage = data.get('context_usage', {})
        
        if data.get('first_seen'):
            term.first_seen = datetime.fromisoformat(data['first_seen'])
        if data.get('last_used'):
            term.last_used = datetime.fromisoformat(data['last_used'])
        
        # 设置评分
        term.confidence_score = data.get('confidence_score', 1.0)
        term.completeness_score = data.get('completeness_score', 0.0)
        term.validation_score = data.get('validation_score', 0.0)
        
        # 设置时间戳和版本
        if 'created_at' in data:
            term.created_at = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            term.updated_at = datetime.fromisoformat(data['updated_at'])
        term.version = data.get('version', '1.0')
        term._metadata = data.get('metadata', {})
        
        return term

class TerminologyDatabase(ModelCollection):
    """
    术语库
    
    管理术语集合，提供：
    - 术语检索和查询
    - 术语关系分析
    - 术语统计
    - 导入导出功能
    """
    
    def __init__(self, database_id: Optional[str] = None):
        super().__init__(database_id)
        
        # 术语库属性
        self.database_name: str = ""
        self.language: str = "zh-CN"
        self.domain: TerminologyDomain = TerminologyDomain.GENERAL
        
        # 索引
        self._text_index: Dict[str, Set[str]] = {}     # 文本 -> 术语ID
        self._domain_index: Dict[str, Set[str]] = {}   # 领域 -> 术语ID
        self._category_index: Dict[str, Set[str]] = {} # 分类 -> 术语ID
        
        # 统计信息
        self.total_terms = 0
        self.domain_distribution: Dict[str, int] = {}
        self.category_distribution: Dict[str, int] = {}
        self.average_completeness: float = 0.0
    
    def add_term(self, term: Term) -> 'TerminologyDatabase':
        """添加术语"""
        self.add_item(term)
        self._update_indexes(term)
        self._update_statistics()
        return self
    
    def remove_term(self, term_id: str) -> bool:
        """移除术语"""
        if term_id in self._items:
            term = self._items[term_id]
            if self.remove_item(term_id):
                self._remove_from_indexes(term)
                self._update_statistics()
                return True
        return False
    
    def find_term_by_text(self, text: str, exact_match: bool = True) -> List[Term]:
        """根据文本查找术语"""
        normalized_text = self._normalize_text(text)
        
        if exact_match:
            term_ids = self._text_index.get(normalized_text, set())
            return [self._items[tid] for tid in term_ids if tid in self._items]
        else:
            # 模糊匹配
            matching_terms = []
            for term in self._items.values():
                if isinstance(term, Term):
                    similarity = self._calculate_text_similarity(normalized_text, term.normalized_text)
                    if similarity > 0.7:  # 相似度阈值
                        matching_terms.append(term)
            
            return sorted(matching_terms, 
                         key=lambda t: self._calculate_text_similarity(normalized_text, t.normalized_text),
                         reverse=True)
    
    def find_terms_by_domain(self, domain: TerminologyDomain) -> List[Term]:
        """根据领域查找术语"""
        term_ids = self._domain_index.get(domain.value, set())
        return [self._items[tid] for tid in term_ids if tid in self._items]
    
    def find_terms_by_category(self, category: TerminologyCategory) -> List[Term]:
        """根据分类查找术语"""
        term_ids = self._category_index.get(category.value, set())
        return [self._items[tid] for tid in term_ids if tid in self._items]
    
    def find_similar_terms(self, term: Term, threshold: float = 70.0) -> List[tuple]:
        """查找相似术语"""
        similar_terms = []
        
        for other_term in self._items.values():
            if isinstance(other_term, Term) and other_term.id != term.id:
                similarity = term.calculate_similarity(other_term)
                if similarity >= threshold:
                    similar_terms.append((other_term, similarity))
        
        return sorted(similar_terms, key=lambda x: x[1], reverse=True)
    
    def get_term_network(self, term_id: str, depth: int = 2) -> Dict[str, Any]:
        """获取术语关系网络"""
        if term_id not in self._items:
            return {}
        
        term = self._items[term_id]
        if not isinstance(term, Term):
            return {}
        
        network = {
            'center': term.to_dict(),
            'nodes': {},
            'edges': []
        }
        
        visited = set()
        queue = [(term_id, 0)]
        
        while queue:
            current_id, current_depth = queue.pop(0)
            
            if current_id in visited or current_depth >= depth:
                continue
            
            visited.add(current_id)
            current_term = self._items.get(current_id)
            
            if not isinstance(current_term, Term):
                continue
            
            network['nodes'][current_id] = current_term.to_dict()
            
            # 添加关系边
            for related_id in (current_term.broader_terms | 
                              current_term.narrower_terms | 
                              current_term.related_terms):
                
                if related_id in self._items:
                    # 确定关系类型
                    if related_id in current_term.broader_terms:
                        relation_type = "broader"
                    elif related_id in current_term.narrower_terms:
                        relation_type = "narrower"
                    else:
                        relation_type = "related"
                    
                    network['edges'].append({
                        'source': current_id,
                        'target': related_id,
                        'type': relation_type
                    })
                    
                    queue.append((related_id, current_depth + 1))
        
        return network
    
    def _normalize_text(self, text: str) -> str:
        """标准化文本"""
        normalized = text.lower().strip()
        normalized = re.sub(r'[^\w\s-]', '', normalized)
        normalized = re.sub(r'\s+', ' ', normalized)
        return normalized
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def _update_indexes(self, term: Term):
        """更新索引"""
        # 文本索引
        if term.normalized_text not in self._text_index:
            self._text_index[term.normalized_text] = set()
        self._text_index[term.normalized_text].add(term.id)
        
        # 同义词索引
        for synonym in term.synonyms:
            normalized_synonym = self._normalize_text(synonym)
            if normalized_synonym not in self._text_index:
                self._text_index[normalized_synonym] = set()
            self._text_index[normalized_synonym].add(term.id)
        
        # 领域索引
        domain_key = term.domain.value
        if domain_key not in self._domain_index:
            self._domain_index[domain_key] = set()
        self._domain_index[domain_key].add(term.id)
        
        # 分类索引
        category_key = term.category.value
        if category_key not in self._category_index:
            self._category_index[category_key] = set()
        self._category_index[category_key].add(term.id)
    
    def _remove_from_indexes(self, term: Term):
        """从索引中移除"""
        # 从文本索引移除
        if term.normalized_text in self._text_index:
            self._text_index[term.normalized_text].discard(term.id)
            if not self._text_index[term.normalized_text]:
                del self._text_index[term.normalized_text]
        
        # 从同义词索引移除
        for synonym in term.synonyms:
            normalized_synonym = self._normalize_text(synonym)
            if normalized_synonym in self._text_index:
                self._text_index[normalized_synonym].discard(term.id)
                if not self._text_index[normalized_synonym]:
                    del self._text_index[normalized_synonym]
        
        # 从领域索引移除
        domain_key = term.domain.value
        if domain_key in self._domain_index:
            self._domain_index[domain_key].discard(term.id)
            if not self._domain_index[domain_key]:
                del self._domain_index[domain_key]
        
        # 从分类索引移除
        category_key = term.category.value
        if category_key in self._category_index:
            self._category_index[category_key].discard(term.id)
            if not self._category_index[category_key]:
                del self._category_index[category_key]
    
    def _update_statistics(self):
        """更新统计信息"""
        terms = [item for item in self._items.values() if isinstance(item, Term)]
        self.total_terms = len(terms)
        
        # 领域分布
        self.domain_distribution.clear()
        for term in terms:
            domain = term.domain.value
            self.domain_distribution[domain] = self.domain_distribution.get(domain, 0) + 1
        
        # 分类分布
        self.category_distribution.clear()
        for term in terms:
            category = term.category.value
            self.category_distribution[category] = self.category_distribution.get(category, 0) + 1
        
        # 平均完整性
        if terms:
            total_completeness = sum(term.completeness_score for term in terms)
            self.average_completeness = total_completeness / len(terms)
        else:
            self.average_completeness = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        base_dict = super().to_dict()
        base_dict.update({
            'database_name': self.database_name,
            'language': self.language,
            'domain': self.domain.value,
            'statistics': {
                'total_terms': self.total_terms,
                'domain_distribution': self.domain_distribution,
                'category_distribution': self.category_distribution,
                'average_completeness': self.average_completeness
            }
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TerminologyDatabase':
        """从字典创建术语库实例"""
        database = cls(data.get('id'))
        database.name = data.get('name', '')
        database.description = data.get('description', '')
        database.tags = set(data.get('tags', []))
        database.database_name = data.get('database_name', '')
        database.language = data.get('language', 'zh-CN')
        database.domain = TerminologyDomain(data.get('domain', 'general'))
        
        # 恢复术语
        items_data = data.get('items', {})
        for item_data in items_data.values():
            # 假设所有项目都是Term类型
            term = Term.from_dict(item_data)
            database.add_term(term)
        
        # 设置时间戳和版本
        if 'created_at' in data:
            database.created_at = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            database.updated_at = datetime.fromisoformat(data['updated_at'])
        database.version = data.get('version', '1.0')
        database._metadata = data.get('metadata', {})
        
        return database 