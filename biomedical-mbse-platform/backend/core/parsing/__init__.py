"""
解析核心功能模块 - 统一入口

提供简化的工厂函数，无需复杂的工厂类设计
"""
from typing import Dict, Any, Optional, Union
import warnings

# 版本信息
__version__ = "3.2.0"
__author__ = "XML元数据系统团队"

# 新版本解析器导入
from .xml_parser import UnifiedXMLParser
from .terminology import TerminologyAnalyzer  
from .validation import ValidationParser
from .layered import LayeredSnapshotParser

# 解析器注册表
PARSERS = {
    'xml': UnifiedXMLParser,
    'terminology': TerminologyAnalyzer,
    'validation': ValidationParser,
    'layered': LayeredSnapshotParser,
    # 兼容性别名
    'xml_parser': UnifiedXMLParser,
    'enhanced_xml': UnifiedXMLParser,
    'optimized_xml': UnifiedXMLParser,
    'final_xml': UnifiedXMLParser,
    'improved_xml': UnifiedXMLParser,
}

def create_parser(parser_type: str, config: Optional[Dict[str, Any]] = None):
    """
    创建解析器实例 - 简化的工厂函数
    
    Args:
        parser_type: 解析器类型 ('xml', 'terminology', 'validation', 'layered')
        config: 可选配置参数
    
    Returns:
        解析器实例
    
    Examples:
        >>> parser = create_parser('xml')
        >>> result = await parser.parse(xml_content)
        
        >>> terminology = create_parser('terminology')
        >>> analysis = await terminology.analyze(document)
    """
    if parser_type not in PARSERS:
        available = ', '.join(PARSERS.keys())
        raise ValueError(f"不支持的解析器类型: {parser_type}. 可用类型: {available}")
    
    parser_class = PARSERS[parser_type]
    return parser_class(config or {})

# 便捷函数
def xml_parser(config: Optional[Dict[str, Any]] = None):
    """创建XML解析器 - 整合所有优化特性"""
    return create_parser('xml', config)

def terminology_analyzer(config: Optional[Dict[str, Any]] = None):
    """创建术语分析器"""
    return create_parser('terminology', config)

def validation_parser(config: Optional[Dict[str, Any]] = None):
    """创建验证解析器"""
    return create_parser('validation', config)

def layered_parser(config: Optional[Dict[str, Any]] = None):
    """创建分层快照解析器"""
    return create_parser('layered', config)

# 导出主要接口
__all__ = [
    'create_parser',
    'xml_parser', 
    'terminology_analyzer',
    'validation_parser',
    'layered_parser',
    'UnifiedXMLParser',
    'TerminologyAnalyzer',
    'ValidationParser', 
    'LayeredSnapshotParser'
] 