"""
基础解析器抽象类 - 统一接口设计

所有解析器都应继承此基类，确保接口一致性
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
import time
import asyncio

class BaseParser(ABC):
    """解析器基础抽象类 - 简化版接口"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.performance_metrics = {
            'total_parsing_time': 0.0,
            'elements_processed': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'processing_speed': 0.0  # elements/second
        }
        self._start_time = None
    
    @abstractmethod
    async def parse(self, content: str, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        解析内容 - 核心接口
        
        Args:
            content: 待解析的内容
            config: 可选的解析配置参数
            
        Returns:
            解析结果字典
        """
        pass
    
    def validate(self, content: str) -> bool:
        """
        验证内容格式 - 可选重写
        
        Args:
            content: 待验证的内容
            
        Returns:
            验证是否通过
        """
        return True
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.performance_metrics.copy()
    
    def reset_metrics(self):
        """重置性能指标"""
        self.performance_metrics = {
            'total_parsing_time': 0.0,
            'elements_processed': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'processing_speed': 0.0
        }
    
    def _start_timing(self):
        """开始计时"""
        self._start_time = time.time()
    
    def _end_timing(self, elements_count: int = 0):
        """结束计时并更新指标"""
        if self._start_time is not None:
            elapsed_time = time.time() - self._start_time
            self.performance_metrics['total_parsing_time'] += elapsed_time
            self.performance_metrics['elements_processed'] += elements_count
            
            if elapsed_time > 0:
                self.performance_metrics['processing_speed'] = elements_count / elapsed_time
            
            self._start_time = None
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的格式列表 - 可选重写
        
        Returns:
            支持的格式列表
        """
        return ['xml']
    
    def get_parser_info(self) -> Dict[str, Any]:
        """
        获取解析器基本信息
        
        Returns:
            解析器信息字典
        """
        return {
            'parser_name': self.__class__.__name__,
            'supported_formats': self.get_supported_formats(),
            'config': self.config,
            'metrics': self.get_metrics()
        }

class AsyncParserMixin:
    """异步解析器混入类 - 提供异步处理能力"""
    
    async def parse_batch(self, contents: List[str], config: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        批量解析内容
        
        Args:
            contents: 待解析的内容列表
            config: 可选的解析配置参数
            
        Returns:
            解析结果列表
        """
        tasks = [self.parse(content, config) for content in contents]
        return await asyncio.gather(*tasks)
    
    async def parse_with_timeout(self, content: str, timeout: float = 30.0, 
                                config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        带超时的解析
        
        Args:
            content: 待解析的内容
            timeout: 超时时间（秒）
            config: 可选的解析配置参数
            
        Returns:
            解析结果字典
            
        Raises:
            asyncio.TimeoutError: 解析超时
        """
        return await asyncio.wait_for(self.parse(content, config), timeout=timeout) 