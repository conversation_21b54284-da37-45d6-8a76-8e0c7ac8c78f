"""
文档术语体系识别器 - 重构版

基于新的解析器架构，继承BaseParser接口，保持原有功能完整性
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import re
from pathlib import Path
import logging

from .base import BaseParser, AsyncParserMixin

@dataclass
class NamespaceInfo:
    """命名空间信息"""
    uri: str
    prefix: str
    usage_count: int
    element_types: Set[str] = field(default_factory=set)
    is_primary: bool = False

@dataclass
class TerminologyPattern:
    """术语模式"""
    pattern: str
    category: str
    confidence: float
    examples: List[str] = field(default_factory=list)
    usage_frequency: int = 0

@dataclass
class IdentificationScheme:
    """标识方案"""
    primary_id_attribute: str
    fallback_attributes: List[str]
    naming_patterns: Dict[str, str]
    stability_indicators: List[str]

@dataclass
class DocumentTerminologyResult:
    """文档术语分析结果"""
    namespace_system: Dict[str, NamespaceInfo]
    terminology_system: Dict[str, TerminologyPattern]
    identification_scheme: IdentificationScheme
    parsing_strategy: str
    confidence_score: float
    recommendations: List[str]

class TerminologyAnalyzer(BaseParser, AsyncParserMixin):
    """
    文档术语体系识别器 - 重构版
    
    继承BaseParser接口，提供术语分析功能
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config or {})
        self.logger = logging.getLogger(__name__)
        
        # MBSE标准术语模式
        self.standard_patterns = {
            'sysml': {
                'requirement': r'(?i)(requirement|req\d+|需求)',
                'block': r'(?i)(block|系统|模块|组件)',
                'activity': r'(?i)(activity|action|活动|操作)',
                'constraint': r'(?i)(constraint|约束|限制)',
                'interface': r'(?i)(interface|接口|连接)',
                'stakeholder': r'(?i)(stakeholder|利益相关者|涉众)'
            },
            'uml': {
                'class': r'(?i)(class|类|对象)',
                'component': r'(?i)(component|组件)',
                'package': r'(?i)(package|包|模型)',
                'association': r'(?i)(association|关联|关系)',
                'diagram': r'(?i)(diagram|图|视图)',
                'stereotype': r'(?i)(stereotype|构造型|原型)'
            },
            'business': {
                'process': r'(?i)(process|流程|过程)',
                'actor': r'(?i)(actor|参与者|执行者)',
                'use_case': r'(?i)(usecase|用例|场景)',
                'goal': r'(?i)(goal|目标|目的)',
                'capability': r'(?i)(capability|能力|功能)',
                'service': r'(?i)(service|服务|接口)'
            }
        }
        
        # 标准命名空间映射
        self.standard_namespaces = {
            'http://www.eclipse.org/uml2/5.0.0/UML': 'uml',
            'http://www.omg.org/XMI': 'xmi',
            'http://www.omg.org/uml': 'uml',
            'http://www.eclipse.org/papyrus/sysml/1.6/SysML': 'sysml',
            'http://www.omg.org/SysML': 'sysml'
        }
    
    async def parse(self, content: str, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        解析文档术语体系 - 实现BaseParser接口
        
        Args:
            content: XML内容
            config: 可选配置参数
            
        Returns:
            术语分析结果
        """
        self._start_timing()
        
        try:
            # 执行术语分析
            result = self.analyze_document_terminology(content)
            
            # 更新指标
            elements_count = len(result.terminology_system) + len(result.namespace_system)
            self._end_timing(elements_count)
            
            # 转换为标准解析器结果格式
            return {
                'parser_type': 'terminology',
                'success': True,
                'data': {
                    'namespace_system': {k: vars(v) for k, v in result.namespace_system.items()},
                    'terminology_system': {k: vars(v) for k, v in result.terminology_system.items()},
                    'identification_scheme': vars(result.identification_scheme),
                    'parsing_strategy': result.parsing_strategy,
                    'confidence_score': result.confidence_score,
                    'recommendations': result.recommendations
                },
                'metrics': self.get_metrics()
            }
            
        except Exception as e:
            self.logger.error(f"术语分析失败: {e}")
            return {
                'parser_type': 'terminology',
                'success': False,
                'error': str(e),
                'metrics': self.get_metrics()
            }
    
    def validate(self, content: str) -> bool:
        """验证内容是否为有效的XML"""
        try:
            ET.fromstring(content)
            return True
        except ET.ParseError:
            return False
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式"""
        return ['xml', 'uml', 'sysml', 'xmi']
    
    def analyze_document_terminology(self, xml_content: str) -> DocumentTerminologyResult:
        """分析文档术语体系 - 保持原有接口"""
        try:
            root = ET.fromstring(xml_content)
            
            # 1. 识别命名空间体系
            namespace_info = self._extract_namespace_system(root)
            
            # 2. 构建术语识别机制
            terminology = self._build_terminology_system(root, namespace_info)
            
            # 3. 建立标识机制
            identification = self._establish_identification_scheme(root, terminology)
            
            # 4. 确定解析策略
            strategy = self._determine_parsing_strategy(root, namespace_info, terminology)
            
            # 5. 计算置信度和生成建议
            confidence = self._calculate_confidence(namespace_info, terminology, identification)
            recommendations = self._generate_recommendations(namespace_info, terminology, strategy)
            
            return DocumentTerminologyResult(
                namespace_system=namespace_info,
                terminology_system=terminology,
                identification_scheme=identification,
                parsing_strategy=strategy,
                confidence_score=confidence,
                recommendations=recommendations
            )
            
        except Exception as e:
            self.logger.error(f"文档术语分析失败: {e}")
            return self._create_fallback_result()
    
    def _create_fallback_result(self) -> DocumentTerminologyResult:
        """创建默认的回退结果"""
        self.logger.warning("创建回退术语分析结果")
        
        # 创建基本的命名空间信息
        fallback_namespace = NamespaceInfo(
            uri="unknown",
            prefix="unknown",
            usage_count=0,
            element_types=set(),
            is_primary=True
        )
        
        # 创建基本的术语模式
        fallback_terminology = TerminologyPattern(
            pattern="unknown",
            category="unknown",
            confidence=0.5,
            examples=[],
            usage_frequency=0
        )
        
        # 创建基本的标识方案
        fallback_identification = IdentificationScheme(
            primary_id_attribute="id",
            fallback_attributes=["name", "xmi:id"],
            naming_patterns={"default": "element_{counter}"},
            stability_indicators=["id", "name"]
        )
        
        return DocumentTerminologyResult(
            namespace_system={"default": fallback_namespace},
            terminology_system={"default": fallback_terminology},
            identification_scheme=fallback_identification,
            parsing_strategy="fallback",
            confidence_score=0.3,
            recommendations=["需要更详细的XML结构分析", "建议检查XML格式有效性"]
        )
    
    def _extract_namespace_system(self, root: ET.Element) -> Dict[str, NamespaceInfo]:
        """提取命名空间体系"""
        namespaces = {}
        
        # 从根元素获取命名空间
        for prefix, uri in root.attrib.items():
            if prefix.startswith('xmlns'):
                namespace_prefix = prefix.split(':')[-1] if ':' in prefix else 'default'
                
                # 获取标准前缀
                standard_prefix = self.standard_namespaces.get(uri, namespace_prefix)
                
                # 统计元素类型
                element_types = set()
                usage_count = 0
                
                # 遍历整个文档统计使用情况
                for elem in root.iter():
                    if elem.tag.startswith(f'{{{uri}}}') or (namespace_prefix == 'default' and ':' not in elem.tag):
                        element_types.add(elem.tag.split('}')[-1])
                        usage_count += 1
                
                namespaces[standard_prefix] = NamespaceInfo(
                    uri=uri,
                    prefix=standard_prefix,
                    usage_count=usage_count,
                    element_types=element_types,
                    is_primary=(usage_count > 10)  # 使用次数超过10次的视为主要命名空间
                )
        
        # 如果没有找到命名空间，创建默认的
        if not namespaces:
            default_types = {elem.tag for elem in root.iter()}
            namespaces['default'] = NamespaceInfo(
                uri="default",
                prefix="default",
                usage_count=len(list(root.iter())),
                element_types=default_types,
                is_primary=True
            )
        
        return namespaces
    
    def _build_terminology_system(self, root: ET.Element, namespace_info: Dict[str, NamespaceInfo]) -> Dict[str, TerminologyPattern]:
        """构建术语识别机制"""
        terminology = {}
        text_content = ET.tostring(root, encoding='unicode', method='text')
        
        # 应用标准术语模式
        for domain, patterns in self.standard_patterns.items():
            for term_type, pattern in patterns.items():
                matches = re.findall(pattern, text_content)
                if matches:
                    terminology[f"{domain}_{term_type}"] = TerminologyPattern(
                        pattern=pattern,
                        category=domain,
                        confidence=0.8,
                        examples=matches[:3],  # 取前3个例子
                        usage_frequency=len(matches)
                    )
        
        # 分析元素名称模式
        element_names = [elem.tag.split('}')[-1] for elem in root.iter()]
        name_patterns = self._analyze_naming_patterns(element_names)
        
        for pattern_name, pattern_info in name_patterns.items():
            terminology[f"naming_{pattern_name}"] = TerminologyPattern(
                pattern=pattern_info['pattern'],
                category="naming",
                confidence=pattern_info['confidence'],
                examples=pattern_info['examples'],
                usage_frequency=pattern_info['frequency']
            )
        
        return terminology
    
    def _analyze_naming_patterns(self, names: List[str]) -> Dict[str, Dict[str, Any]]:
        """分析命名模式"""
        patterns = {}
        
        # 分析驼峰命名
        camel_case = [name for name in names if re.match(r'^[a-z][a-zA-Z0-9]*$', name)]
        if camel_case:
            patterns['camelCase'] = {
                'pattern': r'^[a-z][a-zA-Z0-9]*$',
                'confidence': 0.9,
                'examples': camel_case[:3],
                'frequency': len(camel_case)
            }
        
        # 分析帕斯卡命名
        pascal_case = [name for name in names if re.match(r'^[A-Z][a-zA-Z0-9]*$', name)]
        if pascal_case:
            patterns['PascalCase'] = {
                'pattern': r'^[A-Z][a-zA-Z0-9]*$',
                'confidence': 0.9,
                'examples': pascal_case[:3],
                'frequency': len(pascal_case)
            }
        
        # 分析下划线命名
        snake_case = [name for name in names if re.match(r'^[a-z]+(_[a-z]+)*$', name)]
        if snake_case:
            patterns['snake_case'] = {
                'pattern': r'^[a-z]+(_[a-z]+)*$',
                'confidence': 0.8,
                'examples': snake_case[:3],
                'frequency': len(snake_case)
            }
        
        return patterns
    
    def _establish_identification_scheme(self, root: ET.Element, terminology: Dict[str, TerminologyPattern]) -> IdentificationScheme:
        """建立标识机制"""
        # 分析常用的ID属性
        id_attributes = set()
        for elem in root.iter():
            for attr in elem.attrib:
                if 'id' in attr.lower():
                    id_attributes.add(attr)
        
        # 确定主要ID属性
        primary_id = 'id'
        if 'xmi:id' in id_attributes:
            primary_id = 'xmi:id'
        elif id_attributes:
            primary_id = list(id_attributes)[0]
        
        # 设置后备属性
        fallback_attrs = ['name', 'xmi:id', 'id', 'uuid']
        fallback_attrs = [attr for attr in fallback_attrs if attr != primary_id]
        
        # 分析命名模式
        naming_patterns = {}
        for pattern_name, pattern_info in terminology.items():
            if pattern_name.startswith('naming_'):
                naming_patterns[pattern_name] = pattern_info.pattern
        
        if not naming_patterns:
            naming_patterns = {"default": "element_{counter}"}
        
        return IdentificationScheme(
            primary_id_attribute=primary_id,
            fallback_attributes=fallback_attrs,
            naming_patterns=naming_patterns,
            stability_indicators=[primary_id, 'name']
        )
    
    def _determine_parsing_strategy(self, root: ET.Element, namespace_info: Dict[str, NamespaceInfo], terminology: Dict[str, TerminologyPattern]) -> str:
        """确定解析策略"""
        # 根据命名空间和术语确定策略
        if any('uml' in ns.uri.lower() for ns in namespace_info.values()):
            return "uml_standard"
        elif any('sysml' in ns.uri.lower() for ns in namespace_info.values()):
            return "sysml_standard"
        elif any(t.category == 'business' for t in terminology.values()):
            return "business_process"
        else:
            return "generic_xml"
    
    def _calculate_confidence(self, namespace_info: Dict[str, NamespaceInfo], terminology: Dict[str, TerminologyPattern], identification: IdentificationScheme) -> float:
        """计算置信度"""
        confidence_factors = []
        
        # 命名空间置信度
        ns_confidence = sum(1 for ns in namespace_info.values() if ns.uri in self.standard_namespaces) / max(1, len(namespace_info))
        confidence_factors.append(ns_confidence * 0.4)
        
        # 术语识别置信度
        term_confidence = sum(t.confidence for t in terminology.values()) / max(1, len(terminology))
        confidence_factors.append(term_confidence * 0.4)
        
        # 标识机制置信度
        id_confidence = 0.8 if identification.primary_id_attribute in ['id', 'xmi:id'] else 0.5
        confidence_factors.append(id_confidence * 0.2)
        
        return sum(confidence_factors)
    
    def _generate_recommendations(self, namespace_info: Dict[str, NamespaceInfo], terminology: Dict[str, TerminologyPattern], strategy: str) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 基于命名空间的建议
        if not namespace_info or all(ns.uri == "default" for ns in namespace_info.values()):
            recommendations.append("建议明确定义XML命名空间以提高解析准确性")
        
        # 基于术语的建议
        if len(terminology) < 3:
            recommendations.append("检测到的术语模式较少，建议使用更标准的MBSE术语")
        
        # 基于策略的建议
        if strategy == "generic_xml":
            recommendations.append("当前使用通用XML解析策略，建议采用标准的UML/SysML格式")
        elif strategy in ["uml_standard", "sysml_standard"]:
            recommendations.append(f"检测到{strategy}格式，建议启用对应的专用解析器")
        
        return recommendations 