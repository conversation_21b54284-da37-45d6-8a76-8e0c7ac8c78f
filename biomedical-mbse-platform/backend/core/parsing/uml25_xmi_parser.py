#!/usr/bin/env python3
"""
UML2.5 XMI解析器 - 基于现有解析架构的扩展版

复用现有解析器基础架构：
- 继承UnifiedXMLParser的高性能XML解析能力 (14.7倍性能提升)
- 集成LayeredSnapshotParser的分层渐进解析机制
- 利用TerminologyAnalyzer的术语体系识别能力
- 专注于UML2.5特定的元类定义和关系解析

功能特性:
- 解析UML2.5.xmi标准文件
- 提取元类定义和继承关系  
- 生成基础Schema映射
- 支持增量扩展机制
- 元类关系分析和验证

作者: XML元数据系统开发团队
版本: 2.0.0 (基于解析器架构重构)
日期: 2025年1月1日
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path

# 导入基础解析器架构
from .base import BaseParser, AsyncParserMixin
from .xml_parser import UnifiedXMLParser
from .layered import LayeredSnapshotParser
from .terminology import TerminologyAnalyzer

logger = logging.getLogger(__name__)

class UMLElementType(Enum):
    """UML元素类型枚举"""
    CLASS = "uml:Class"
    INTERFACE = "uml:Interface" 
    ENUMERATION = "uml:Enumeration"
    PRIMITIVE_TYPE = "uml:PrimitiveType"
    ASSOCIATION = "uml:Association"
    PACKAGE = "uml:Package"
    PROPERTY = "uml:Property"
    OPERATION = "uml:Operation"
    PARAMETER = "uml:Parameter"
    GENERALIZATION = "uml:Generalization"
    CONSTRAINT = "uml:Constraint"

@dataclass
class UMLAttribute:
    """UML属性定义"""
    id: str
    name: str
    type_ref: Optional[str] = None
    multiplicity: str = "1"
    is_composite: bool = False
    is_derived: bool = False
    visibility: str = "public"
    default_value: Optional[str] = None
    documentation: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'type_ref': self.type_ref,
            'multiplicity': self.multiplicity,
            'is_composite': self.is_composite,
            'is_derived': self.is_derived,
            'visibility': self.visibility,
            'default_value': self.default_value,
            'documentation': self.documentation
        }

@dataclass
class UMLOperation:
    """UML操作定义"""
    id: str
    name: str
    return_type: Optional[str] = None
    parameters: List[Dict[str, Any]] = field(default_factory=list)
    visibility: str = "public"
    is_abstract: bool = False
    is_static: bool = False
    documentation: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'return_type': self.return_type,
            'parameters': self.parameters,
            'visibility': self.visibility,
            'is_abstract': self.is_abstract,
            'is_static': self.is_static,
            'documentation': self.documentation
        }

@dataclass
class UMLConstraint:
    """UML约束定义"""
    id: str
    name: str
    specification: str
    language: str = "OCL"
    documentation: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'specification': self.specification,
            'language': self.language,
            'documentation': self.documentation
        }

@dataclass 
class UMLMetaclass:
    """UML元类定义"""
    id: str
    name: str
    qualified_name: str
    element_type: UMLElementType
    is_abstract: bool = False
    super_classes: List[str] = field(default_factory=list)
    sub_classes: List[str] = field(default_factory=list)
    attributes: List[UMLAttribute] = field(default_factory=list)
    operations: List[UMLOperation] = field(default_factory=list)
    constraints: List[UMLConstraint] = field(default_factory=list)
    package_path: str = ""
    documentation: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'qualified_name': self.qualified_name,
            'element_type': self.element_type.value,
            'is_abstract': self.is_abstract,
            'super_classes': self.super_classes,
            'sub_classes': self.sub_classes,
            'attributes': [attr.to_dict() for attr in self.attributes],
            'operations': [op.to_dict() for op in self.operations],
            'constraints': [const.to_dict() for const in self.constraints],
            'package_path': self.package_path,
            'documentation': self.documentation
        }

@dataclass
class UMLAssociation:
    """UML关联定义"""
    id: str
    name: str
    member_ends: List[Dict[str, Any]] = field(default_factory=list)
    owned_ends: List[Dict[str, Any]] = field(default_factory=list)
    is_derived: bool = False
    documentation: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'member_ends': self.member_ends,
            'owned_ends': self.owned_ends,
            'is_derived': self.is_derived,
            'documentation': self.documentation
        }

class UML25XMIParser(BaseParser, AsyncParserMixin):
    """
    UML2.5 XMI解析器 - 基于现有解析架构的扩展版
    
    继承和组合现有解析器：
    - UnifiedXMLParser: 高性能XML解析 (14.7倍性能提升)
    - LayeredSnapshotParser: 分层渐进解析
    - TerminologyAnalyzer: 术语体系识别
    """
    
    def __init__(self, xmi_file_path: str, config: Dict[str, Any] = None):
        """
        初始化解析器
        
        Args:
            xmi_file_path: UML2.5.xmi文件路径
            config: 可选配置参数
        """
        # 确保config不是None
        merged_config = config or {}
        super().__init__(merged_config)
        self.xmi_file_path = Path(xmi_file_path)
        
        # 组合现有解析器 - 传递非None的config
        self.unified_parser = UnifiedXMLParser(merged_config)
        self.layered_parser = LayeredSnapshotParser(merged_config)
        self.terminology_analyzer = TerminologyAnalyzer(merged_config)
        
        # UML2.5特定数据结构
        self.metaclasses: Dict[str, UMLMetaclass] = {}
        self.associations: Dict[str, UMLAssociation] = {}
        self.packages: Dict[str, str] = {}  # id -> package_path
        
        # 解析结果缓存
        self._unified_result: Optional[Dict[str, Any]] = None
        self._layered_result: Optional[Dict[str, Any]] = None
        self._terminology_result: Optional[Dict[str, Any]] = None
        
        # 统计信息
        self.parse_stats = {
            'total_elements': 0,
            'metaclasses_parsed': 0,
            'associations_parsed': 0,
            'packages_parsed': 0,
            'attributes_parsed': 0,
            'operations_parsed': 0,
            'constraints_parsed': 0,
            'parse_time': 0.0,
            'errors': []
        }
    
    async def parse(self, content: str = None, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        解析UML2.5 XMI文件 - 实现BaseParser接口
        
        Args:
            content: 可选，如果不提供则使用xmi_file_path
            config: 可选配置参数
            
        Returns:
            解析结果字典，包含所有元类和关联定义
        """
        self._start_timing()
        
        try:
            logger.info(f"开始解析UML2.5 XMI文件: {self.xmi_file_path}")
            
            # 1. 获取XML内容
            xml_content = content or self._load_xml_content()
            
            # 2. 并行执行基础解析器
            await self._execute_base_parsers_async(xml_content, config)
            
            # 3. 基于基础解析结果提取UML2.5特定信息
            await self._extract_uml25_specific_info_async()
            
            # 4. 构建继承关系
            await self._build_inheritance_relationships_async()
            
            # 5. 验证元模型一致性
            await self._validate_metamodel_async()
            
            elements_count = len(self.metaclasses) + len(self.associations)
            self._end_timing(elements_count)
            
            logger.info(f"UML2.5 XMI解析完成:")
            logger.info(f"  - 元类数量: {self.parse_stats['metaclasses_parsed']}")
            logger.info(f"  - 关联数量: {self.parse_stats['associations_parsed']}")
            logger.info(f"  - 包数量: {self.parse_stats['packages_parsed']}")
            logger.info(f"  - 解析时间: {self.parse_stats['parse_time']:.2f}秒")
            
            return await self._build_result_async()
            
        except Exception as e:
            self.parse_stats['errors'].append(str(e))
            logger.error(f"UML2.5 XMI解析失败: {e}")
            return {
                'parser_type': 'uml25_xmi',
                'success': False,
                'error': str(e),
                'metrics': self.get_metrics()
            }
    
    def _load_xml_content(self) -> str:
        """加载XML文件内容"""
        if not self.xmi_file_path.exists():
            raise FileNotFoundError(f"XMI文件不存在: {self.xmi_file_path}")
        
        with open(self.xmi_file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    async def _execute_base_parsers_async(self, xml_content: str, config: Optional[Dict[str, Any]]):
        """并行执行基础解析器"""
        
        # 并行执行所有基础解析器
        tasks = [
            self.unified_parser.parse(xml_content, config),
            self.layered_parser.parse(xml_content, config),
            self.terminology_analyzer.parse(xml_content, config)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 保存结果
        self._unified_result = results[0] if not isinstance(results[0], Exception) else None
        self._layered_result = results[1] if not isinstance(results[1], Exception) else None
        self._terminology_result = results[2] if not isinstance(results[2], Exception) else None
        
        # 记录任何错误
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                parser_names = ['unified', 'layered', 'terminology']
                logger.warning(f"{parser_names[i]}解析器执行失败: {result}")
                self.parse_stats['errors'].append(f"{parser_names[i]}_parser_error: {result}")
    
    async def _extract_uml25_specific_info_async(self):
        """基于基础解析结果提取UML2.5特定信息 - 专注于xmi:type定义"""
        
        # 从unified_parser结果中提取元数据
        if self._unified_result and self._unified_result.get('success'):
            unified_data = self._unified_result['data']
            metadata_store = unified_data.get('metadata_store', {})
            
            # 收集所有xmi:type定义
            xmi_types_found = set()
            type_definitions = {}
            
            # 分析每个元素的xmi:type
            for element_id, metadata in metadata_store.items():
                namespace_info = metadata.get('namespace_info', {})
                element_type = namespace_info.get('element_type', '')
                
                if element_type and element_type not in ['default', 'unknown']:
                    xmi_types_found.add(element_type)
                    
                    # 收集该类型的属性定义
                    if element_type not in type_definitions:
                        type_definitions[element_type] = {
                            'type_name': element_type,
                            'sample_attributes': set(),
                            'sample_properties': set(),
                            'occurrence_count': 0,
                            'inheritance_info': {},
                            'documentation': ''
                        }
                    
                    # 更新类型定义信息
                    type_def = type_definitions[element_type]
                    type_def['occurrence_count'] += 1
                    
                    # 收集属性样本
                    attributes = metadata.get('attributes', {})
                    for attr_name, attr_value in attributes.items():
                        type_def['sample_attributes'].add((attr_name, type(attr_value).__name__))
                    
                    # 收集子元素作为属性
                    children_ids = metadata.get('children_ids', [])
                    for child_id in children_ids:
                        if child_id in metadata_store:
                            child_meta = metadata_store[child_id]
                            child_type = child_meta.get('namespace_info', {}).get('element_type', '')
                            if child_type:
                                type_def['sample_properties'].add(child_type)
            
            # 为每个xmi:type创建元类定义
            await self._create_metaclass_definitions_from_types(type_definitions)
            
        else:
            logger.warning("统一解析器结果不可用，使用回退解析方法")
            await self._fallback_parse_async()
    
    async def _create_metaclass_definitions_from_types(self, type_definitions: Dict[str, Any]):
        """从xmi:type定义创建元类定义"""
        
        for type_name, type_info in type_definitions.items():
            # 创建元类定义
            metaclass = UMLMetaclass(
                id=f"metaclass_{type_name.replace(':', '_')}",
                name=type_name.split(':')[-1] if ':' in type_name else type_name,
                qualified_name=type_name,
                element_type=self._map_to_uml_element_type(type_name),
                is_abstract=self._is_abstract_type(type_name),
                package_path=self._extract_package_from_type(type_name),
                documentation=f"元类定义: {type_name} (出现{type_info['occurrence_count']}次)"
            )
            
            # 根据UML元模型为该类型添加标准属性
            await self._add_standard_attributes_for_type(metaclass, type_name, type_info)
            
            # 添加样本中发现的属性
            await self._add_discovered_attributes(metaclass, type_info)
            
            self.metaclasses[metaclass.id] = metaclass
            self.parse_stats['metaclasses_parsed'] += 1
        
        logger.info(f"从{len(type_definitions)}个xmi:type创建了元类定义")
    
    async def _add_standard_attributes_for_type(self, metaclass: UMLMetaclass, type_name: str, type_info: Dict[str, Any]):
        """为特定类型添加标准UML属性"""
        
        # 所有元素的基本属性
        base_attributes = [
            UMLAttribute('id', 'id', 'String', '1', False, False, 'public', None, 'XMI标识符'),
            UMLAttribute('name', 'name', 'String', '0..1', False, False, 'public', None, '元素名称'),
        ]
        metaclass.attributes.extend(base_attributes)
        
        # 根据具体类型添加特定属性
        if type_name in ['uml:Class', 'Class']:
            metaclass.attributes.extend([
                UMLAttribute('attr_isAbstract', 'isAbstract', 'Boolean', '1', False, False, 'public', 'false', '是否抽象类'),
                UMLAttribute('attr_isFinal', 'isFinal', 'Boolean', '1', False, False, 'public', 'false', '是否最终类'),
                UMLAttribute('attr_isActive', 'isActive', 'Boolean', '1', False, False, 'public', 'false', '是否活动类'),
            ])
            
        elif type_name in ['uml:Property', 'Property']:
            metaclass.attributes.extend([
                UMLAttribute('attr_type', 'type', 'Type', '0..1', False, False, 'public', None, '属性类型'),
                UMLAttribute('attr_multiplicity', 'multiplicity', 'String', '1', False, False, 'public', '1', '多重性'),
                UMLAttribute('attr_isComposite', 'isComposite', 'Boolean', '1', False, False, 'public', 'false', '是否组合'),
                UMLAttribute('attr_isDerived', 'isDerived', 'Boolean', '1', False, False, 'public', 'false', '是否派生'),
                UMLAttribute('attr_aggregation', 'aggregation', 'AggregationKind', '1', False, False, 'public', 'none', '聚合类型'),
            ])
            
        elif type_name in ['uml:Operation', 'Operation']:
            metaclass.attributes.extend([
                UMLAttribute('attr_isStatic', 'isStatic', 'Boolean', '1', False, False, 'public', 'false', '是否静态'),
                UMLAttribute('attr_isAbstract', 'isAbstract', 'Boolean', '1', False, False, 'public', 'false', '是否抽象'),
                UMLAttribute('attr_isQuery', 'isQuery', 'Boolean', '1', False, False, 'public', 'false', '是否查询操作'),
            ])
            
        elif type_name in ['uml:Association', 'Association']:
            metaclass.attributes.extend([
                UMLAttribute('attr_isDerived', 'isDerived', 'Boolean', '1', False, False, 'public', 'false', '是否派生关联'),
                UMLAttribute('attr_memberEnd', 'memberEnd', 'Property', '2..*', False, False, 'public', None, '成员端'),
            ])
            
        elif type_name in ['uml:Package', 'Package']:
            metaclass.attributes.extend([
                UMLAttribute('attr_packagedElement', 'packagedElement', 'PackageableElement', '0..*', True, False, 'public', None, '包含元素'),
                UMLAttribute('attr_nestingPackage', 'nestingPackage', 'Package', '0..1', False, False, 'public', None, '嵌套包'),
            ])
            
        elif type_name in ['uml:Enumeration', 'Enumeration']:
            metaclass.attributes.extend([
                UMLAttribute('attr_ownedLiteral', 'ownedLiteral', 'EnumerationLiteral', '0..*', True, False, 'public', None, '枚举字面量'),
            ])
        
        # 为所有分类器添加通用属性
        classifier_types = ['uml:Class', 'uml:Interface', 'uml:Enumeration', 'uml:PrimitiveType', 
                          'Class', 'Interface', 'Enumeration', 'PrimitiveType']
        if type_name in classifier_types:
            metaclass.attributes.extend([
                UMLAttribute('attr_visibility', 'visibility', 'VisibilityKind', '1', False, False, 'public', 'public', '可见性'),
                UMLAttribute('attr_feature', 'feature', 'Feature', '0..*', False, True, 'public', None, '特征列表'),
                UMLAttribute('attr_general', 'general', 'Classifier', '0..*', False, False, 'public', None, '一般化'),
            ])
        
        self.parse_stats['attributes_parsed'] += len(metaclass.attributes)
    
    async def _add_discovered_attributes(self, metaclass: UMLMetaclass, type_info: Dict[str, Any]):
        """添加从样本中发现的属性"""
        
        # 添加发现的XML属性
        for attr_name, attr_type in type_info['sample_attributes']:
            if not any(attr.name == f"xml_{attr_name}" for attr in metaclass.attributes):
                discovered_attr = UMLAttribute(
                    id=f"disc_{attr_name}",
                    name=f"xml_{attr_name}",
                    type_ref=self._map_python_type_to_uml(attr_type),
                    multiplicity='0..1',
                    is_composite=False,
                    is_derived=False,
                    visibility='public',
                    documentation=f"从XML属性发现: {attr_name}"
                )
                metaclass.attributes.append(discovered_attr)
                self.parse_stats['attributes_parsed'] += 1
    
    def _is_abstract_type(self, type_name: str) -> bool:
        """判断类型是否为抽象类型"""
        abstract_types = [
            'uml:Element', 'uml:NamedElement', 'uml:Classifier', 
            'uml:Feature', 'uml:PackageableElement', 'uml:TypedElement'
        ]
        return type_name in abstract_types
    
    def _extract_package_from_type(self, type_name: str) -> str:
        """从类型名称提取包路径"""
        if ':' in type_name:
            namespace = type_name.split(':')[0]
            
            # UML包映射
            package_mapping = {
                'uml': 'UML::Core',
                'xmi': 'XMI',
                'sysml': 'SysML',
                'xsi': 'XMLSchema'
            }
            
            return package_mapping.get(namespace, namespace)
        
        return 'Unknown'
    
    def _map_python_type_to_uml(self, python_type: str) -> str:
        """将Python类型映射到UML类型"""
        mapping = {
            'str': 'String',
            'int': 'Integer', 
            'float': 'Real',
            'bool': 'Boolean',
            'list': 'Collection',
            'dict': 'Map',
            'NoneType': 'Void'
        }
        return mapping.get(python_type, 'String')
    
    async def _fallback_parse_async(self):
        """回退解析方法"""
        logger.info("使用回退解析方法")
        
        # 如果基础解析器都失败了，直接解析XML
        try:
            xml_content = self._load_xml_content()
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_content)
            
            # 简单的回退解析
            for element in root.iter():
                tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
                
                if tag in ['Class', 'Interface', 'Enumeration']:
                    element_id = element.get('id') or element.get('xmi:id') or f"fallback_{len(self.metaclasses)}"
                    name = element.get('name', tag)
                    
                    metaclass = UMLMetaclass(
                        id=element_id,
                        name=name,
                        qualified_name=name,
                        element_type=self._map_to_uml_element_type(tag),
                        documentation="Fallback parsed"
                    )
                    
                    self.metaclasses[element_id] = metaclass
                    self.parse_stats['metaclasses_parsed'] += 1
                    
        except Exception as e:
            logger.error(f"回退解析也失败了: {e}")
            self.parse_stats['errors'].append(f"fallback_parse_error: {e}")
    
    async def _build_inheritance_relationships_async(self):
        """构建继承关系"""
        # 使用unified_parser的引用关系构建继承
        if self._unified_result:
            references_to_map = self._unified_result['data'].get('references_to_map', {})
            
            for child_id, parent_ids in references_to_map.items():
                if child_id in self.metaclasses:
                    child_metaclass = self.metaclasses[child_id]
                    
                    for parent_id in parent_ids:
                        if parent_id in self.metaclasses:
                            child_metaclass.super_classes.append(parent_id)
                            self.metaclasses[parent_id].sub_classes.append(child_id)
        
        logger.info("构建继承关系完成")
    
    async def _validate_metamodel_async(self):
        """验证元模型一致性"""
        validation_errors = []
        circular_inheritance_found = []
        
        # 检查循环继承 - 仅针对真正的类继承，跳过包含关系
        for metaclass in self.metaclasses.values():
            # 跳过包元素，它们的包含关系是正常的
            if metaclass.name in ['packagedElement', 'Package'] or 'package' in metaclass.name.lower():
                continue
                
            # 检查类继承循环
            if await self._has_circular_inheritance_async(metaclass.id, set()):
                circular_inheritance_found.append(metaclass.qualified_name)
        
        # 只在发现实际问题时报告，避免包结构误报
        if circular_inheritance_found:
            unique_errors = list(set(circular_inheritance_found))[:10]  # 限制显示数量
            for error_name in unique_errors:
                validation_errors.append(f"检测到循环继承: {error_name}")
        
        # 检查类型引用
        unresolved_types = []
        for metaclass in self.metaclasses.values():
            for attr in metaclass.attributes:
                if attr.type_ref and attr.type_ref not in self.metaclasses:
                    # 检查是否是基本类型
                    if not self._is_primitive_type(attr.type_ref):
                        unresolved_types.append(
                            f"未找到属性类型引用: {attr.type_ref} in {metaclass.qualified_name}.{attr.name}"
                        )
        
        # 限制类型引用错误的数量显示
        if unresolved_types:
            validation_errors.extend(unresolved_types[:10])
            if len(unresolved_types) > 10:
                validation_errors.append(f"... 还有 {len(unresolved_types) - 10} 个类型引用问题")
        
        if validation_errors:
            logger.warning("元模型验证发现问题:")
            for error in validation_errors[:5]:  # 只显示前5个错误
                logger.warning(f"  - {error}")
            if len(validation_errors) > 5:
                logger.warning(f"  ... 还有 {len(validation_errors) - 5} 个验证问题")
            self.parse_stats['errors'].extend(validation_errors)
        else:
            logger.info("元模型验证通过")
    
    async def _has_circular_inheritance_async(self, metaclass_id: str, visited: Set[str]) -> bool:
        """检查循环继承"""
        if metaclass_id in visited:
            return True
        
        if metaclass_id not in self.metaclasses:
            return False
        
        visited.add(metaclass_id)
        metaclass = self.metaclasses[metaclass_id]
        
        for super_id in metaclass.super_classes:
            if await self._has_circular_inheritance_async(super_id, visited.copy()):
                return True
        
        return False
    
    def _is_primitive_type(self, type_ref: str) -> bool:
        """检查是否是基本类型"""
        primitive_types = {
            'Boolean', 'Integer', 'UnlimitedNatural', 'String', 
            'Real', 'DateTime', 'URI', 'Any'
        }
        return type_ref in primitive_types or type_ref.startswith('http://')
    
    async def _build_result_async(self) -> Dict[str, Any]:
        """构建最终结果"""
        import time
        
        # 计算总解析时间
        total_time = 0.0
        if self._unified_result:
            total_time += self._unified_result.get('metrics', {}).get('total_parsing_time', 0)
        if self._layered_result:
            total_time += self._layered_result.get('metrics', {}).get('total_parsing_time', 0)
        if self._terminology_result:
            total_time += self._terminology_result.get('metrics', {}).get('total_parsing_time', 0)
        
        self.parse_stats['parse_time'] = total_time
        
        return {
            'parser_type': 'uml25_xmi_extended',
            'success': True,
            'data': {
                'metadata': {
                    'source_file': str(self.xmi_file_path),
                    'parser_version': '2.0.0',
                    'parse_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'uml_version': '2.5',
                    'standard': 'OMG UML 2.5',
                    'base_parsers_used': {
                        'unified_parser': self._unified_result is not None,
                        'layered_parser': self._layered_result is not None,
                        'terminology_analyzer': self._terminology_result is not None
                    }
                },
                'statistics': self.parse_stats,
                'metaclasses': {
                    metaclass_id: metaclass.to_dict() 
                    for metaclass_id, metaclass in self.metaclasses.items()
                },
                'associations': {
                    assoc_id: association.to_dict()
                    for assoc_id, association in self.associations.items()
                },
                'packages': self.packages,
                'inheritance_hierarchy': await self._build_inheritance_hierarchy_async(),
                'core_metaclasses': await self._identify_core_metaclasses_async(),
                'base_parser_results': {
                    'unified_result': self._unified_result,
                    'layered_result': self._layered_result,
                    'terminology_result': self._terminology_result
                }
            },
            'metrics': self.get_metrics()
        }
    
    async def _build_inheritance_hierarchy_async(self) -> Dict[str, Any]:
        """构建继承层次结构"""
        hierarchy = {}
        
        # 找到根类（没有父类的类）
        root_classes = [
            metaclass.id for metaclass in self.metaclasses.values()
            if not metaclass.super_classes
        ]
        
        # 为每个根类构建子树
        tasks = []
        for root_id in root_classes:
            if root_id in self.metaclasses:
                task = self._build_class_subtree_async(root_id)
                tasks.append(task)
        
        if tasks:
            subtrees = await asyncio.gather(*tasks)
            for i, root_id in enumerate(root_classes):
                hierarchy[root_id] = subtrees[i]
        
        return hierarchy
    
    async def _build_class_subtree_async(self, metaclass_id: str) -> Dict[str, Any]:
        """构建类的子树"""
        if metaclass_id not in self.metaclasses:
            return {}
        
        metaclass = self.metaclasses[metaclass_id]
        subtree = {
            'id': metaclass_id,
            'name': metaclass.name,
            'qualified_name': metaclass.qualified_name,
            'is_abstract': metaclass.is_abstract,
            'children': []
        }
        
        # 递归构建子类
        child_tasks = []
        for child_id in metaclass.sub_classes:
            task = self._build_class_subtree_async(child_id)
            child_tasks.append(task)
        
        if child_tasks:
            child_subtrees = await asyncio.gather(*child_tasks)
            subtree['children'] = [subtree for subtree in child_subtrees if subtree]
        
        return subtree
    
    async def _identify_core_metaclasses_async(self) -> List[str]:
        """识别核心元类"""
        core_metaclasses = []
        
        # 定义核心包路径
        core_packages = [
            'CommonStructure', 'CommonBehavior', 'Classification',
            'SimpleClassifiers', 'StructuredClassifiers', 'Values'
        ]
        
        for metaclass in self.metaclasses.values():
            if any(package in metaclass.package_path for package in core_packages):
                core_metaclasses.append(metaclass.id)
        
        return core_metaclasses
    
    def validate(self, content: str) -> bool:
        """验证XML内容格式"""
        try:
            import xml.etree.ElementTree as ET
            ET.fromstring(content)
            return True
        except ET.ParseError:
            return False
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式"""
        return ['xmi', 'uml', 'xml']

    def _map_to_uml_element_type(self, element_type_str: str) -> UMLElementType:
        """映射到UML元素类型枚举"""
        type_mapping = {
            'Class': UMLElementType.CLASS,
            'uml:Class': UMLElementType.CLASS,
            'Interface': UMLElementType.INTERFACE,
            'uml:Interface': UMLElementType.INTERFACE,
            'Enumeration': UMLElementType.ENUMERATION,
            'uml:Enumeration': UMLElementType.ENUMERATION,
            'PrimitiveType': UMLElementType.PRIMITIVE_TYPE,
            'uml:PrimitiveType': UMLElementType.PRIMITIVE_TYPE,
            'Association': UMLElementType.ASSOCIATION,
            'uml:Association': UMLElementType.ASSOCIATION,
            'Package': UMLElementType.PACKAGE,
            'uml:Package': UMLElementType.PACKAGE,
            'Property': UMLElementType.PROPERTY,
            'uml:Property': UMLElementType.PROPERTY,
            'Operation': UMLElementType.OPERATION,
            'uml:Operation': UMLElementType.OPERATION,
            'Parameter': UMLElementType.PARAMETER,
            'uml:Parameter': UMLElementType.PARAMETER,
            'Generalization': UMLElementType.GENERALIZATION,
            'uml:Generalization': UMLElementType.GENERALIZATION,
            'Constraint': UMLElementType.CONSTRAINT,
            'uml:Constraint': UMLElementType.CONSTRAINT
        }
        
        return type_mapping.get(element_type_str, UMLElementType.CLASS)

# 便捷函数
async def parse_uml25_xmi(xmi_file_path: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    便捷函数：解析UML2.5 XMI文件
    
    Args:
        xmi_file_path: XMI文件路径
        config: 可选配置参数
        
    Returns:
        解析结果
    """
    parser = UML25XMIParser(xmi_file_path, config)
    return await parser.parse()

def create_uml25_parser(xmi_file_path: str, config: Dict[str, Any] = None) -> UML25XMIParser:
    """
    创建UML2.5解析器实例
    
    Args:
        xmi_file_path: XMI文件路径
        config: 可选配置参数
        
    Returns:
        解析器实例
    """
    return UML25XMIParser(xmi_file_path, config)

# 导出接口
__all__ = [
    'UML25XMIParser',
    'UMLMetaclass', 
    'UMLAttribute',
    'UMLOperation',
    'UMLConstraint',
    'UMLAssociation',
    'UMLElementType',
    'parse_uml25_xmi',
    'create_uml25_parser'
] 