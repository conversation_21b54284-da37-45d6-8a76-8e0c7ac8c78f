"""
验证解析器 - XML验证和质量检查

继承BaseParser接口，提供统一的验证功能：
- XML格式验证
- Schema验证 (XSD)
- 内容完整性检查
- 数据质量分析
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
import re
from pathlib import Path
import logging

from .base import BaseParser, AsyncParserMixin

# 尝试导入lxml以支持XSD验证
try:
    from lxml import etree
    LXML_AVAILABLE = True
except ImportError:
    LXML_AVAILABLE = False

@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    validation_type: str
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    score: float = 0.0  # 质量评分 (0-100)

@dataclass
class QualityMetrics:
    """质量指标"""
    completeness_score: float = 0.0  # 完整性评分
    consistency_score: float = 0.0   # 一致性评分
    accuracy_score: float = 0.0      # 准确性评分
    accessibility_score: float = 0.0  # 可访问性评分
    overall_score: float = 0.0       # 总体评分

class ValidationParser(BaseParser, AsyncParserMixin):
    """
    验证解析器 - 重构版
    
    功能特性:
    - XML格式验证
    - Schema验证 (如果有lxml)
    - 数据质量检查
    - MBSE标准合规性检查
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config or {})
        self.logger = logging.getLogger(__name__)
        
        # 验证配置
        self.validation_config = {
            'strict_mode': config.get('strict_mode', False),
            'check_references': config.get('check_references', True),
            'validate_namespaces': config.get('validate_namespaces', True),
            'quality_threshold': config.get('quality_threshold', 80.0),
            'schema_validation': config.get('schema_validation', LXML_AVAILABLE)
        }
        
        # 加载Schema (如果可用)
        self.schemas = {}
        if LXML_AVAILABLE and self.validation_config['schema_validation']:
            self._load_schemas()
        
        # MBSE标准验证规则
        self.mbse_rules = {
            'required_namespaces': [
                'http://www.omg.org/XMI',
                'http://www.eclipse.org/uml2',
                'http://www.omg.org/uml'
            ],
            'standard_attributes': [
                'xmi:id', 'id', 'name', 'type', 'xmi:type'
            ],
            'naming_conventions': {
                'class_pattern': r'^[A-Z][a-zA-Z0-9_]*$',
                'attribute_pattern': r'^[a-z][a-zA-Z0-9_]*$',
                'method_pattern': r'^[a-z][a-zA-Z0-9_]*$'
            }
        }
    
    async def parse(self, content: str, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        执行XML验证 - 实现BaseParser接口
        
        Args:
            content: XML内容
            config: 可选配置参数
            
        Returns:
            验证结果
        """
        self._start_timing()
        merge_config = {**self.validation_config, **(config or {})}
        
        try:
            # 执行多层验证
            validation_results = await self._perform_comprehensive_validation(content, merge_config)
            
            # 计算质量指标
            quality_metrics = await self._calculate_quality_metrics(validation_results)
            
            # 生成建议
            suggestions = await self._generate_improvement_suggestions(validation_results, quality_metrics)
            
            # 更新指标
            elements_count = len(validation_results.get('structural_validation', {}).get('elements', []))
            self._end_timing(elements_count)
            
            return {
                'parser_type': 'validation',
                'success': True,
                'data': {
                    'validation_results': validation_results,
                    'quality_metrics': quality_metrics.__dict__,
                    'suggestions': suggestions,
                    'overall_valid': self._is_overall_valid(validation_results),
                    'validation_summary': self._generate_validation_summary(validation_results)
                },
                'metrics': self.get_metrics()
            }
            
        except Exception as e:
            self.logger.error(f"验证失败: {e}")
            return {
                'parser_type': 'validation',
                'success': False,
                'error': str(e),
                'metrics': self.get_metrics()
            }
    
    async def _perform_comprehensive_validation(self, content: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行全面验证"""
        results = {}
        
        # 1. XML格式验证
        results['format_validation'] = await self._validate_xml_format(content)
        
        # 2. Schema验证 (如果可用)
        if LXML_AVAILABLE and config.get('schema_validation', False):
            results['schema_validation'] = await self._validate_against_schemas(content)
        
        # 3. 结构完整性验证
        if results['format_validation'].is_valid:
            root = ET.fromstring(content)
            results['structural_validation'] = await self._validate_structure(root)
            
            # 4. 引用完整性验证
            if config.get('check_references', True):
                results['reference_validation'] = await self._validate_references(root)
            
            # 5. MBSE标准合规性验证
            results['compliance_validation'] = await self._validate_mbse_compliance(root)
            
            # 6. 命名空间验证
            if config.get('validate_namespaces', True):
                results['namespace_validation'] = await self._validate_namespaces(root)
        
        return results
    
    async def _validate_xml_format(self, content: str) -> ValidationResult:
        """验证XML格式"""
        try:
            ET.fromstring(content)
            return ValidationResult(
                is_valid=True,
                validation_type='xml_format',
                score=100.0
            )
        except ET.ParseError as e:
            return ValidationResult(
                is_valid=False,
                validation_type='xml_format',
                errors=[f"XML格式错误: {e}"],
                suggestions=["检查XML语法", "确保标签正确闭合", "检查特殊字符转义"],
                score=0.0
            )
    
    async def _validate_against_schemas(self, content: str) -> Dict[str, ValidationResult]:
        """针对Schema验证"""
        if not LXML_AVAILABLE:
            return {'info': ValidationResult(
                is_valid=True, 
                validation_type='schema_not_available',
                warnings=["lxml不可用，跳过Schema验证"]
            )}
        
        results = {}
        
        try:
            xml_doc = etree.fromstring(content.encode('utf-8'))
            
            for schema_name, schema in self.schemas.items():
                try:
                    is_valid = schema.validate(xml_doc)
                    errors = [str(error) for error in schema.error_log] if not is_valid else []
                    
                    results[schema_name] = ValidationResult(
                        is_valid=is_valid,
                        validation_type=f'schema_{schema_name}',
                        errors=errors,
                        score=100.0 if is_valid else max(0, 100 - len(errors) * 10)
                    )
                except Exception as e:
                    results[schema_name] = ValidationResult(
                        is_valid=False,
                        validation_type=f'schema_{schema_name}',
                        errors=[f"Schema验证错误: {e}"],
                        score=0.0
                    )
        except Exception as e:
            results['general'] = ValidationResult(
                is_valid=False,
                validation_type='schema_general',
                errors=[f"Schema验证失败: {e}"],
                score=0.0
            )
        
        return results
    
    async def _validate_structure(self, root: ET.Element) -> ValidationResult:
        """验证结构完整性"""
        errors = []
        warnings = []
        suggestions = []
        
        # 统计信息
        total_elements = len(list(root.iter()))
        elements_with_id = 0
        elements_with_name = 0
        
        # 验证每个元素
        for element in root.iter():
            # 检查ID属性
            if any(attr in element.attrib for attr in ['id', 'xmi:id']):
                elements_with_id += 1
            else:
                warnings.append(f"元素 {element.tag} 缺少ID属性")
            
            # 检查name属性
            if 'name' in element.attrib:
                elements_with_name += 1
            
            # 检查空元素
            if not element.attrib and not element.text and len(element) == 0:
                warnings.append(f"发现空元素: {element.tag}")
        
        # 计算评分
        id_coverage = (elements_with_id / total_elements * 100) if total_elements > 0 else 0
        name_coverage = (elements_with_name / total_elements * 100) if total_elements > 0 else 0
        
        score = (id_coverage * 0.6 + name_coverage * 0.4)  # ID权重更高
        
        # 生成建议
        if id_coverage < 80:
            suggestions.append("建议为更多元素添加唯一ID")
        if name_coverage < 60:
            suggestions.append("建议为更多元素添加描述性名称")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            validation_type='structural',
            errors=errors,
            warnings=warnings,
            suggestions=suggestions,
            score=score
        )
    
    async def _validate_references(self, root: ET.Element) -> ValidationResult:
        """验证引用完整性"""
        errors = []
        warnings = []
        
        # 收集所有ID
        all_ids = set()
        for element in root.iter():
            for attr in ['id', 'xmi:id']:
                if attr in element.attrib:
                    all_ids.add(element.attrib[attr])
        
        # 检查引用
        reference_attrs = ['href', 'type', 'base_Class', 'client', 'supplier']
        broken_refs = 0
        total_refs = 0
        
        for element in root.iter():
            for attr, value in element.attrib.items():
                # 简单的引用检测
                if attr in reference_attrs or self._looks_like_reference(value):
                    total_refs += 1
                    referenced_id = self._extract_referenced_id(value)
                    if referenced_id and referenced_id not in all_ids:
                        broken_refs += 1
                        errors.append(f"断开的引用: {attr}='{value}' 在元素 {element.tag}")
        
        # 计算评分
        if total_refs > 0:
            ref_integrity = ((total_refs - broken_refs) / total_refs * 100)
        else:
            ref_integrity = 100.0
        
        return ValidationResult(
            is_valid=broken_refs == 0,
            validation_type='references',
            errors=errors,
            warnings=warnings,
            score=ref_integrity
        )
    
    async def _validate_mbse_compliance(self, root: ET.Element) -> ValidationResult:
        """验证MBSE标准合规性"""
        errors = []
        warnings = []
        suggestions = []
        
        # 检查命名空间
        root_namespaces = self._extract_root_namespaces(root)
        required_ns_found = 0
        
        for required_ns in self.mbse_rules['required_namespaces']:
            if any(required_ns in ns for ns in root_namespaces):
                required_ns_found += 1
            else:
                warnings.append(f"缺少推荐的命名空间: {required_ns}")
        
        # 检查命名约定
        naming_violations = 0
        total_named_elements = 0
        
        for element in root.iter():
            if 'name' in element.attrib:
                total_named_elements += 1
                name = element.attrib['name']
                
                # 简化的命名检查
                if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', name):
                    naming_violations += 1
                    warnings.append(f"命名约定违规: '{name}' 在 {element.tag}")
        
        # 计算合规性评分
        ns_score = (required_ns_found / len(self.mbse_rules['required_namespaces']) * 100)
        naming_score = ((total_named_elements - naming_violations) / total_named_elements * 100) if total_named_elements > 0 else 100
        
        compliance_score = (ns_score * 0.4 + naming_score * 0.6)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            validation_type='mbse_compliance',
            errors=errors,
            warnings=warnings,
            suggestions=suggestions,
            score=compliance_score
        )
    
    async def _validate_namespaces(self, root: ET.Element) -> ValidationResult:
        """验证命名空间"""
        errors = []
        warnings = []
        
        # 检查命名空间声明
        namespaces = self._extract_root_namespaces(root)
        
        if not namespaces:
            warnings.append("未找到命名空间声明")
        
        # 检查未使用的命名空间
        used_prefixes = set()
        for element in root.iter():
            if ':' in element.tag:
                prefix = element.tag.split(':')[0]
                used_prefixes.add(prefix)
        
        declared_prefixes = set(ns.split(':')[0] for ns in namespaces if ':' in ns)
        unused_prefixes = declared_prefixes - used_prefixes
        
        for prefix in unused_prefixes:
            warnings.append(f"未使用的命名空间前缀: {prefix}")
        
        score = max(0, 100 - len(warnings) * 5)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            validation_type='namespaces',
            errors=errors,
            warnings=warnings,
            score=score
        )
    
    async def _calculate_quality_metrics(self, validation_results: Dict[str, Any]) -> QualityMetrics:
        """计算质量指标"""
        scores = []
        
        # 收集所有验证结果的评分
        for key, result in validation_results.items():
            if isinstance(result, ValidationResult):
                scores.append(result.score)
            elif isinstance(result, dict):
                # 处理嵌套结果 (如schema验证)
                for sub_result in result.values():
                    if isinstance(sub_result, ValidationResult):
                        scores.append(sub_result.score)
        
        # 计算平均分
        if scores:
            overall_score = sum(scores) / len(scores)
        else:
            overall_score = 0.0
        
        return QualityMetrics(
            completeness_score=overall_score,
            consistency_score=overall_score,
            accuracy_score=overall_score,
            accessibility_score=overall_score,
            overall_score=overall_score
        )
    
    async def _generate_improvement_suggestions(self, validation_results: Dict[str, Any], quality_metrics: QualityMetrics) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 收集所有建议
        for result in validation_results.values():
            if isinstance(result, ValidationResult):
                suggestions.extend(result.suggestions)
            elif isinstance(result, dict):
                for sub_result in result.values():
                    if isinstance(sub_result, ValidationResult):
                        suggestions.extend(sub_result.suggestions)
        
        # 基于质量指标添加建议
        if quality_metrics.overall_score < 60:
            suggestions.append("考虑重新设计XML结构以提高质量")
        
        if quality_metrics.completeness_score < 70:
            suggestions.append("添加更多必要的属性和元数据")
        
        return list(set(suggestions))  # 去重
    
    def _is_overall_valid(self, validation_results: Dict[str, Any]) -> bool:
        """判断整体是否有效"""
        # 格式验证必须通过
        if 'format_validation' in validation_results:
            if not validation_results['format_validation'].is_valid:
                return False
        
        # 其他验证可以有警告但不能有错误
        for result in validation_results.values():
            if isinstance(result, ValidationResult):
                if result.errors:
                    return False
            elif isinstance(result, dict):
                for sub_result in result.values():
                    if isinstance(sub_result, ValidationResult) and sub_result.errors:
                        return False
        
        return True
    
    def _generate_validation_summary(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成验证摘要"""
        total_errors = 0
        total_warnings = 0
        
        for result in validation_results.values():
            if isinstance(result, ValidationResult):
                total_errors += len(result.errors)
                total_warnings += len(result.warnings)
            elif isinstance(result, dict):
                for sub_result in result.values():
                    if isinstance(sub_result, ValidationResult):
                        total_errors += len(sub_result.errors)
                        total_warnings += len(sub_result.warnings)
        
        return {
            'total_validations': len(validation_results),
            'total_errors': total_errors,
            'total_warnings': total_warnings,
            'validation_types': list(validation_results.keys())
        }
    
    def _load_schemas(self):
        """加载XSD文件"""
        # 这里可以根据需要加载特定的XSD文件
        # 暂时留空，可以后续扩展
        pass
    
    def _extract_root_namespaces(self, root: ET.Element) -> List[str]:
        """提取根元素的命名空间"""
        namespaces = []
        
        # 检查标签中的命名空间
        if '}' in root.tag:
            ns = root.tag.split('}')[0][1:]
            namespaces.append(ns)
        
        # 检查属性中的命名空间声明
        for attr, value in root.attrib.items():
            if attr.startswith('xmlns'):
                namespaces.append(value)
        
        return namespaces
    
    def _looks_like_reference(self, value: str) -> bool:
        """判断值是否像引用"""
        # 简单的引用检测
        if not isinstance(value, str):
            return False
        
        # 常见的引用模式
        ref_patterns = [
            r'^[a-zA-Z0-9_-]+$',  # 简单ID引用
            r'^#.+',              # 内部引用
            r'.*\.xmi#.*',        # XMI引用
            r'platform:/.*'       # 平台引用
        ]
        
        return any(re.match(pattern, value) for pattern in ref_patterns)
    
    def _extract_referenced_id(self, value: str) -> Optional[str]:
        """提取引用的ID"""
        if '#' in value:
            return value.split('#')[-1]
        return value if self._looks_like_reference(value) else None
    
    def validate(self, content: str) -> bool:
        """简单的XML格式验证"""
        try:
            ET.fromstring(content)
            return True
        except ET.ParseError:
            return False
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式"""
        return ['xml', 'xmi', 'uml', 'sysml'] 