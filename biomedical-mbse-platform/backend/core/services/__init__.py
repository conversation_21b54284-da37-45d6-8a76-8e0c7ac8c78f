"""
核心服务模块 - 统一服务入口

提供所有核心服务的统一访问接口
"""

from .id_manager import StableIDManager
from .cache_manager import CacheManager
from .relation_manager import RelationManager  
from .version_manager import VersionManager

# 版本信息
__version__ = "3.0.0"

# 服务实例缓存
_service_instances = {}

def get_id_manager(config=None):
    """获取ID管理服务实例"""
    if 'id_manager' not in _service_instances:
        _service_instances['id_manager'] = StableIDManager(config or {})
    return _service_instances['id_manager']

def get_cache_manager(config=None):
    """获取缓存管理服务实例"""
    if 'cache_manager' not in _service_instances:
        _service_instances['cache_manager'] = CacheManager(config or {})
    return _service_instances['cache_manager']

def get_relation_manager(config=None):
    """获取关系管理服务实例"""
    if 'relation_manager' not in _service_instances:
        _service_instances['relation_manager'] = RelationManager(config or {})
    return _service_instances['relation_manager']

def get_version_manager(config=None):
    """获取版本管理服务实例"""
    if 'version_manager' not in _service_instances:
        _service_instances['version_manager'] = VersionManager(config or {})
    return _service_instances['version_manager']

def create_service_registry(config=None):
    """
    创建完整的服务注册表
    
    Args:
        config: 全局配置字典
        
    Returns:
        服务注册表字典
    """
    config = config or {}
    
    return {
        'id_manager': StableIDManager(config.get('id_manager', {})),
        'cache_manager': CacheManager(config.get('cache_manager', {})),
        'relation_manager': RelationManager(config.get('relation_manager', {})),
        'version_manager': VersionManager(config.get('version_manager', {}))
    }

def create_service(service_type: str, config=None):
    """
    创建指定类型的服务实例 - 工厂函数
    
    Args:
        service_type: 服务类型 ('id_manager', 'cache_manager', 'relation_manager', 'version_manager')
        config: 配置参数
        
    Returns:
        服务实例
    """
    config = config or {}
    
    service_classes = {
        'id_manager': StableIDManager,
        'cache_manager': CacheManager,
        'relation_manager': RelationManager,
        'version_manager': VersionManager
    }
    
    if service_type not in service_classes:
        raise ValueError(f"不支持的服务类型: {service_type}。支持的类型: {list(service_classes.keys())}")
    
    service_class = service_classes[service_type]
    return service_class(config)

def clear_service_cache():
    """清空服务实例缓存"""
    global _service_instances
    _service_instances.clear()

# 便捷访问
id_manager = get_id_manager
cache_manager = get_cache_manager
relation_manager = get_relation_manager
version_manager = get_version_manager

# 导出所有公开接口
__all__ = [
    'StableIDManager',
    'CacheManager', 
    'RelationManager',
    'VersionManager',
    'get_id_manager',
    'get_cache_manager',
    'get_relation_manager',
    'get_version_manager',
    'create_service_registry',
    'create_service',
    'clear_service_cache',
    'id_manager',
    'cache_manager',
    'relation_manager',
    'version_manager'
] 