"""
缓存管理服务 - 统一缓存机制

提供多级缓存、智能清理、性能监控等功能
"""

import time
import threading
from typing import Dict, Any, Optional, List, Tuple, Set
from dataclasses import dataclass, field
from collections import defaultdict, OrderedDict
import weakref
import logging
import json
import hashlib

@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_time: float
    last_accessed: float
    access_count: int = 0
    size_bytes: int = 0
    ttl: Optional[float] = None  # Time to live in seconds
    tags: Set[str] = field(default_factory=set)

@dataclass
class CacheStats:
    """缓存统计"""
    total_entries: int = 0
    total_size_bytes: int = 0
    hit_count: int = 0
    miss_count: int = 0
    eviction_count: int = 0
    hit_rate: float = 0.0
    average_access_time: float = 0.0

class CacheManager:
    """
    缓存管理服务
    
    功能特性:
    - 多级缓存 (L1: 内存, L2: 持久化)
    - LRU/LFU 淘汰策略
    - TTL 过期管理
    - 标签分组管理
    - 性能监控
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 缓存配置
        self.max_size = config.get('max_size', 1000)
        self.max_memory_mb = config.get('max_memory_mb', 100)
        self.default_ttl = config.get('default_ttl', 3600)  # 1 hour
        self.eviction_policy = config.get('eviction_policy', 'lru')  # lru, lfu, ttl
        self.enable_stats = config.get('enable_stats', True)
        
        # 多级缓存存储
        self._l1_cache: OrderedDict[str, CacheEntry] = OrderedDict()  # 内存缓存
        self._l2_cache: Dict[str, str] = {}  # 持久化缓存路径映射
        
        # 索引和统计
        self._tag_index: Dict[str, Set[str]] = defaultdict(set)
        self._stats = CacheStats()
        self._lock = threading.RLock()
        
        # 性能监控
        self._access_times: List[float] = []
        self._cleanup_thread = None
        self._start_cleanup_thread()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            缓存值或默认值
        """
        start_time = time.time()
        
        with self._lock:
            # 1. 尝试从L1缓存获取
            if key in self._l1_cache:
                entry = self._l1_cache[key]
                
                # 检查TTL
                if self._is_expired(entry):
                    self._remove_entry(key)
                    self._stats.miss_count += 1
                    return default
                
                # 更新访问信息
                entry.last_accessed = time.time()
                entry.access_count += 1
                
                # LRU: 移到末尾
                self._l1_cache.move_to_end(key)
                
                # 更新统计
                self._stats.hit_count += 1
                access_time = time.time() - start_time
                self._update_access_time(access_time)
                
                return entry.value
            
            # 2. 尝试从L2缓存获取
            if key in self._l2_cache:
                try:
                    value = self._load_from_l2(key)
                    if value is not None:
                        # 提升到L1缓存
                        self.set(key, value, promote_to_l1=True)
                        self._stats.hit_count += 1
                        return value
                except Exception as e:
                    self.logger.warning(f"L2缓存读取失败 {key}: {e}")
            
            # 3. 缓存未命中
            self._stats.miss_count += 1
            return default
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None, 
            tags: Optional[Set[str]] = None, promote_to_l1: bool = True) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            tags: 标签集合
            promote_to_l1: 是否提升到L1缓存
            
        Returns:
            设置是否成功
        """
        with self._lock:
            try:
                # 计算大小
                size_bytes = self._calculate_size(value)
                current_time = time.time()
                
                # 创建缓存条目
                entry = CacheEntry(
                    key=key,
                    value=value,
                    created_time=current_time,
                    last_accessed=current_time,
                    size_bytes=size_bytes,
                    ttl=ttl or self.default_ttl,
                    tags=tags or set()
                )
                
                if promote_to_l1:
                    # 检查是否需要淘汰
                    self._ensure_capacity_for_entry(entry)
                    
                    # 添加到L1缓存
                    self._l1_cache[key] = entry
                    
                    # 更新标签索引
                    for tag in entry.tags:
                        self._tag_index[tag].add(key)
                    
                    # 更新统计
                    self._stats.total_entries = len(self._l1_cache)
                    self._stats.total_size_bytes += size_bytes
                else:
                    # 保存到L2缓存
                    self._save_to_l2(key, value)
                
                return True
                
            except Exception as e:
                self.logger.error(f"设置缓存失败 {key}: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        with self._lock:
            removed = False
            
            # 从L1缓存删除
            if key in self._l1_cache:
                entry = self._l1_cache[key]
                self._remove_entry(key)
                removed = True
            
            # 从L2缓存删除
            if key in self._l2_cache:
                try:
                    self._delete_from_l2(key)
                    removed = True
                except Exception as e:
                    self.logger.warning(f"L2缓存删除失败 {key}: {e}")
            
            return removed
    
    def delete_by_tags(self, tags: Set[str]) -> int:
        """根据标签删除缓存项"""
        with self._lock:
            keys_to_delete = set()
            
            for tag in tags:
                if tag in self._tag_index:
                    keys_to_delete.update(self._tag_index[tag])
            
            deleted_count = 0
            for key in keys_to_delete:
                if self.delete(key):
                    deleted_count += 1
            
            return deleted_count
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._l1_cache.clear()
            self._l2_cache.clear()
            self._tag_index.clear()
            self._stats = CacheStats()
            self.logger.info("所有缓存已清空")
    
    def exists(self, key: str) -> bool:
        """检查键是否存在"""
        with self._lock:
            if key in self._l1_cache:
                entry = self._l1_cache[key]
                if not self._is_expired(entry):
                    return True
                else:
                    self._remove_entry(key)
            
            return key in self._l2_cache
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        with self._lock:
            # 更新实时统计
            self._update_stats()
            return self._stats
    
    def get_keys_by_tag(self, tag: str) -> List[str]:
        """根据标签获取键列表"""
        with self._lock:
            return list(self._tag_index.get(tag, set()))
    
    def get_all_keys(self) -> List[str]:
        """获取所有键"""
        with self._lock:
            return list(self._l1_cache.keys()) + list(self._l2_cache.keys())
    
    def optimize(self) -> Dict[str, Any]:
        """优化缓存性能"""
        with self._lock:
            optimization_result = {
                'before_stats': self.get_stats(),
                'actions_taken': []
            }
            
            # 1. 清理过期项
            expired_count = self._cleanup_expired()
            if expired_count > 0:
                optimization_result['actions_taken'].append(f"清理过期项: {expired_count}")
            
            # 2. 整理L1缓存
            if self.eviction_policy == 'lru':
                self._reorder_lru()
                optimization_result['actions_taken'].append("重新排序LRU缓存")
            
            # 3. 压缩L2缓存
            l2_compressed = self._compress_l2_cache()
            if l2_compressed:
                optimization_result['actions_taken'].append("压缩L2缓存")
            
            optimization_result['after_stats'] = self.get_stats()
            return optimization_result
    
    def _ensure_capacity_for_entry(self, entry: CacheEntry) -> None:
        """确保有足够容量存储新条目"""
        # 检查数量限制
        while len(self._l1_cache) >= self.max_size:
            self._evict_one_entry()
        
        # 检查内存限制
        max_bytes = self.max_memory_mb * 1024 * 1024
        while (self._stats.total_size_bytes + entry.size_bytes) > max_bytes:
            self._evict_one_entry()
    
    def _evict_one_entry(self) -> None:
        """淘汰一个缓存条目"""
        if not self._l1_cache:
            return
        
        if self.eviction_policy == 'lru':
            # LRU: 淘汰最久未使用的
            key, entry = self._l1_cache.popitem(last=False)
        elif self.eviction_policy == 'lfu':
            # LFU: 淘汰使用频率最低的
            min_key = min(self._l1_cache.keys(), 
                         key=lambda k: self._l1_cache[k].access_count)
            entry = self._l1_cache.pop(min_key)
            key = min_key
        elif self.eviction_policy == 'ttl':
            # TTL: 淘汰最先过期的
            min_key = min(self._l1_cache.keys(),
                         key=lambda k: self._l1_cache[k].created_time + (self._l1_cache[k].ttl or 0))
            entry = self._l1_cache.pop(min_key)
            key = min_key
        else:
            # 默认FIFO
            key, entry = self._l1_cache.popitem(last=False)
        
        # 更新统计和索引
        self._update_stats_after_eviction(entry)
        self._remove_from_tag_index(key, entry.tags)
        
        self.logger.debug(f"淘汰缓存条目: {key}")
    
    def _remove_entry(self, key: str) -> None:
        """移除缓存条目"""
        if key in self._l1_cache:
            entry = self._l1_cache.pop(key)
            self._stats.total_size_bytes -= entry.size_bytes
            self._remove_from_tag_index(key, entry.tags)
    
    def _remove_from_tag_index(self, key: str, tags: Set[str]) -> None:
        """从标签索引中移除"""
        for tag in tags:
            if tag in self._tag_index:
                self._tag_index[tag].discard(key)
                if not self._tag_index[tag]:
                    del self._tag_index[tag]
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查条目是否过期"""
        if entry.ttl is None:
            return False
        return time.time() > (entry.created_time + entry.ttl)
    
    def _cleanup_expired(self) -> int:
        """清理过期条目"""
        expired_keys = []
        current_time = time.time()
        
        for key, entry in self._l1_cache.items():
            if self._is_expired(entry):
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_entry(key)
        
        return len(expired_keys)
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小（字节）"""
        try:
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (dict, list)):
                return len(json.dumps(value, default=str).encode('utf-8'))
            else:
                return len(str(value).encode('utf-8'))
        except:
            return 100  # 默认大小
    
    def _update_access_time(self, access_time: float) -> None:
        """更新访问时间统计"""
        self._access_times.append(access_time)
        # 保持最近1000次访问记录
        if len(self._access_times) > 1000:
            self._access_times = self._access_times[-1000:]
    
    def _update_stats(self) -> None:
        """更新统计信息"""
        total_requests = self._stats.hit_count + self._stats.miss_count
        if total_requests > 0:
            self._stats.hit_rate = (self._stats.hit_count / total_requests) * 100
        
        if self._access_times:
            self._stats.average_access_time = sum(self._access_times) / len(self._access_times)
        
        self._stats.total_entries = len(self._l1_cache)
        self._stats.total_size_bytes = sum(entry.size_bytes for entry in self._l1_cache.values())
    
    def _update_stats_after_eviction(self, entry: CacheEntry) -> None:
        """淘汰后更新统计"""
        self._stats.eviction_count += 1
        self._stats.total_size_bytes -= entry.size_bytes
    
    def _reorder_lru(self) -> None:
        """重新排序LRU缓存"""
        # OrderedDict已经维护了LRU顺序，这里可以做额外优化
        pass
    
    def _load_from_l2(self, key: str) -> Any:
        """从L2缓存加载"""
        # 简化实现：实际应该从持久化存储加载
        return None
    
    def _save_to_l2(self, key: str, value: Any) -> None:
        """保存到L2缓存"""
        # 简化实现：实际应该保存到持久化存储
        self._l2_cache[key] = f"l2_path_{key}"
    
    def _delete_from_l2(self, key: str) -> None:
        """从L2缓存删除"""
        if key in self._l2_cache:
            del self._l2_cache[key]
    
    def _compress_l2_cache(self) -> bool:
        """压缩L2缓存"""
        # 简化实现：实际应该压缩持久化文件
        return False
    
    def _start_cleanup_thread(self) -> None:
        """启动清理线程"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(300)  # 每5分钟清理一次
                    with self._lock:
                        self._cleanup_expired()
                except Exception as e:
                    self.logger.error(f"清理线程错误: {e}")
        
        self._cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self._cleanup_thread.start()
    
    def __del__(self):
        """析构函数"""
        try:
            if self._cleanup_thread and self._cleanup_thread.is_alive():
                # 清理线程是守护线程，会自动结束
                pass
        except:
            pass 