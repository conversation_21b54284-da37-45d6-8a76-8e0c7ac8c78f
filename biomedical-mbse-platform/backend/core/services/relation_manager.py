"""
关系管理服务 - 统一关系分析和管理

处理XML元素间的各种关系：依赖、关联、继承、实现等
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import re
import logging
from enum import Enum

class RelationType(Enum):
    """关系类型枚举"""
    DEPENDENCY = "dependency"
    ASSOCIATION = "association"
    AGGREGATION = "aggregation"
    COMPOSITION = "composition"
    GENERALIZATION = "generalization"
    REALIZATION = "realization"
    USAGE = "usage"
    EXTEND = "extend"
    INCLUDE = "include"
    TRACE = "trace"

@dataclass
class Relationship:
    """关系对象"""
    id: str
    source_id: str
    target_id: str
    relation_type: RelationType
    attributes: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 1.0
    discovery_method: str = "direct"
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RelationshipNetwork:
    """关系网络"""
    nodes: Set[str] = field(default_factory=set)
    relationships: Dict[str, Relationship] = field(default_factory=dict)
    adjacency_list: Dict[str, Set[str]] = field(default_factory=lambda: defaultdict(set))
    reverse_adjacency: Dict[str, Set[str]] = field(default_factory=lambda: defaultdict(set))

@dataclass
class RelationshipAnalysis:
    """关系分析结果"""
    total_relationships: int = 0
    relationship_types: Dict[str, int] = field(default_factory=dict)
    strongly_connected_components: List[Set[str]] = field(default_factory=list)
    circular_dependencies: List[List[str]] = field(default_factory=list)
    orphaned_nodes: Set[str] = field(default_factory=set)
    hub_nodes: List[Tuple[str, int]] = field(default_factory=list)  # (node_id, degree)

class RelationManager:
    """
    关系管理服务
    
    功能特性:
    - 多种关系类型识别
    - 关系网络构建
    - 循环依赖检测
    - 强连通分量分析
    - 关系质量评估
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 关系识别配置
        self.relation_patterns = {
            RelationType.DEPENDENCY: {
                'attributes': ['href', 'type', 'supplier', 'client'],
                'patterns': [r'.*\.xmi#.*', r'platform:/.*', r'#.*'],
                'keywords': ['depends', 'uses', 'requires']
            },
            RelationType.ASSOCIATION: {
                'attributes': ['memberEnd', 'ownedEnd', 'association'],
                'patterns': [r'.*association.*', r'.*link.*'],
                'keywords': ['associated', 'linked', 'connected']
            },
            RelationType.GENERALIZATION: {
                'attributes': ['general', 'specific', 'generalization'],
                'patterns': [r'.*inherit.*', r'.*extend.*'],
                'keywords': ['extends', 'inherits', 'specializes']
            },
            RelationType.REALIZATION: {
                'attributes': ['contract', 'implementingClassifier'],
                'patterns': [r'.*implement.*', r'.*realize.*'],
                'keywords': ['implements', 'realizes']
            }
        }
        
        # 关系网络
        self.network = RelationshipNetwork()
        
        # 分析缓存
        self._analysis_cache = {}
        self._path_cache = {}
    
    def extract_relationships(self, xml_content: str, element_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Relationship]:
        """
        从XML内容中提取关系
        
        Args:
            xml_content: XML内容
            element_metadata: 元素元数据（可选）
            
        Returns:
            关系字典
        """
        try:
            root = ET.fromstring(xml_content)
            relationships = {}
            
            # 1. 直接关系提取
            direct_relations = self._extract_direct_relationships(root)
            relationships.update(direct_relations)
            
            # 2. 属性引用关系
            reference_relations = self._extract_reference_relationships(root)
            relationships.update(reference_relations)
            
            # 3. 结构化关系（父子、包含等）
            structural_relations = self._extract_structural_relationships(root)
            relationships.update(structural_relations)
            
            # 4. 语义关系（基于命名和类型）
            semantic_relations = self._extract_semantic_relationships(root, element_metadata)
            relationships.update(semantic_relations)
            
            # 更新关系网络
            self._update_network(relationships)
            
            return relationships
            
        except Exception as e:
            self.logger.error(f"关系提取失败: {e}")
            return {}
    
    def _extract_direct_relationships(self, root: ET.Element) -> Dict[str, Relationship]:
        """提取直接定义的关系"""
        relationships = {}
        
        for element in root.iter():
            source_id = self._get_element_id(element)
            if not source_id:
                continue
            
            # 检查每种关系类型
            for rel_type, config in self.relation_patterns.items():
                for attr_name in config['attributes']:
                    if attr_name in element.attrib:
                        target_ref = element.attrib[attr_name]
                        target_id = self._resolve_reference(target_ref, root)
                        
                        if target_id and target_id != source_id:
                            rel_id = f"{source_id}_to_{target_id}_{rel_type.value}"
                            relationships[rel_id] = Relationship(
                                id=rel_id,
                                source_id=source_id,
                                target_id=target_id,
                                relation_type=rel_type,
                                attributes={attr_name: target_ref},
                                confidence=0.9,
                                discovery_method="direct_attribute"
                            )
        
        return relationships
    
    def _extract_reference_relationships(self, root: ET.Element) -> Dict[str, Relationship]:
        """提取引用关系"""
        relationships = {}
        
        # 收集所有元素ID
        all_ids = set()
        id_to_element = {}
        
        for element in root.iter():
            element_id = self._get_element_id(element)
            if element_id:
                all_ids.add(element_id)
                id_to_element[element_id] = element
        
        # 查找引用
        for element in root.iter():
            source_id = self._get_element_id(element)
            if not source_id:
                continue
            
            for attr_name, attr_value in element.attrib.items():
                # 检查是否包含对其他元素的引用
                referenced_ids = self._extract_ids_from_value(attr_value, all_ids)
                
                for target_id in referenced_ids:
                    if target_id != source_id:
                        rel_id = f"{source_id}_ref_{target_id}"
                        relationships[rel_id] = Relationship(
                            id=rel_id,
                            source_id=source_id,
                            target_id=target_id,
                            relation_type=RelationType.DEPENDENCY,
                            attributes={attr_name: attr_value},
                            confidence=0.7,
                            discovery_method="reference_analysis"
                        )
        
        return relationships
    
    def _extract_structural_relationships(self, root: ET.Element) -> Dict[str, Relationship]:
        """提取结构关系（父子、包含等）"""
        relationships = {}
        
        for element in root.iter():
            parent_id = self._get_element_id(element)
            if not parent_id:
                continue
            
            # 父子关系
            for child in element:
                child_id = self._get_element_id(child)
                if child_id:
                    rel_id = f"{parent_id}_contains_{child_id}"
                    relationships[rel_id] = Relationship(
                        id=rel_id,
                        source_id=parent_id,
                        target_id=child_id,
                        relation_type=RelationType.COMPOSITION,
                        confidence=1.0,
                        discovery_method="structural_hierarchy"
                    )
        
        return relationships
    
    def _extract_semantic_relationships(self, root: ET.Element, element_metadata: Optional[Dict[str, Any]]) -> Dict[str, Relationship]:
        """提取语义关系"""
        relationships = {}
        
        if not element_metadata:
            return relationships
        
        # 基于名称相似性的关系
        elements = [(self._get_element_id(elem), elem) for elem in root.iter() if self._get_element_id(elem)]
        
        for i, (id1, elem1) in enumerate(elements):
            for id2, elem2 in elements[i+1:]:
                similarity = self._calculate_semantic_similarity(elem1, elem2, element_metadata)
                
                if similarity > 0.7:  # 高相似性阈值
                    rel_id = f"{id1}_similar_{id2}"
                    relationships[rel_id] = Relationship(
                        id=rel_id,
                        source_id=id1,
                        target_id=id2,
                        relation_type=RelationType.ASSOCIATION,
                        confidence=similarity,
                        discovery_method="semantic_similarity",
                        metadata={'similarity_score': similarity}
                    )
        
        return relationships
    
    def analyze_relationships(self, relationships: Optional[Dict[str, Relationship]] = None) -> RelationshipAnalysis:
        """
        分析关系网络
        
        Args:
            relationships: 关系字典（可选，使用当前网络中的关系）
            
        Returns:
            关系分析结果
        """
        if relationships:
            self._update_network(relationships)
        
        analysis = RelationshipAnalysis()
        
        # 基本统计
        analysis.total_relationships = len(self.network.relationships)
        analysis.relationship_types = self._count_relationship_types()
        
        # 图分析
        analysis.strongly_connected_components = self._find_strongly_connected_components()
        analysis.circular_dependencies = self._detect_circular_dependencies()
        analysis.orphaned_nodes = self._find_orphaned_nodes()
        analysis.hub_nodes = self._find_hub_nodes()
        
        return analysis
    
    def find_path(self, source_id: str, target_id: str, max_depth: int = 10) -> Optional[List[str]]:
        """
        查找两个节点间的路径
        
        Args:
            source_id: 源节点ID
            target_id: 目标节点ID
            max_depth: 最大搜索深度
            
        Returns:
            路径列表或None
        """
        cache_key = f"{source_id}_to_{target_id}_{max_depth}"
        if cache_key in self._path_cache:
            return self._path_cache[cache_key]
        
        path = self._bfs_path(source_id, target_id, max_depth)
        self._path_cache[cache_key] = path
        return path
    
    def get_related_elements(self, element_id: str, relation_types: Optional[List[RelationType]] = None, 
                           direction: str = "both") -> Dict[str, List[str]]:
        """
        获取与指定元素相关的其他元素
        
        Args:
            element_id: 元素ID
            relation_types: 关系类型列表（可选）
            direction: 方向 ("incoming", "outgoing", "both")
            
        Returns:
            相关元素字典 {relation_type: [element_ids]}
        """
        related = defaultdict(list)
        
        for relationship in self.network.relationships.values():
            include_relation = (
                relation_types is None or 
                relationship.relation_type in relation_types
            )
            
            if not include_relation:
                continue
            
            if direction in ("outgoing", "both") and relationship.source_id == element_id:
                related[relationship.relation_type.value].append(relationship.target_id)
            
            if direction in ("incoming", "both") and relationship.target_id == element_id:
                related[relationship.relation_type.value].append(relationship.source_id)
        
        return dict(related)
    
    def validate_relationships(self) -> Dict[str, Any]:
        """验证关系的有效性"""
        validation_result = {
            'valid_relationships': 0,
            'invalid_relationships': 0,
            'warnings': [],
            'errors': []
        }
        
        for rel_id, relationship in self.network.relationships.items():
            # 检查节点存在性
            if relationship.source_id not in self.network.nodes:
                validation_result['errors'].append(f"关系 {rel_id} 的源节点 {relationship.source_id} 不存在")
                validation_result['invalid_relationships'] += 1
                continue
            
            if relationship.target_id not in self.network.nodes:
                validation_result['errors'].append(f"关系 {rel_id} 的目标节点 {relationship.target_id} 不存在")
                validation_result['invalid_relationships'] += 1
                continue
            
            # 检查自引用
            if relationship.source_id == relationship.target_id:
                validation_result['warnings'].append(f"关系 {rel_id} 存在自引用")
            
            # 检查置信度
            if relationship.confidence < 0.5:
                validation_result['warnings'].append(f"关系 {rel_id} 置信度较低: {relationship.confidence}")
            
            validation_result['valid_relationships'] += 1
        
        return validation_result
    
    def export_graph(self, format: str = "dot") -> str:
        """
        导出关系图
        
        Args:
            format: 导出格式 ("dot", "json", "csv")
            
        Returns:
            导出的图数据
        """
        if format == "dot":
            return self._export_to_dot()
        elif format == "json":
            return self._export_to_json()
        elif format == "csv":
            return self._export_to_csv()
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def _update_network(self, relationships: Dict[str, Relationship]):
        """更新关系网络"""
        for relationship in relationships.values():
            # 添加关系
            self.network.relationships[relationship.id] = relationship
            
            # 更新节点集合
            self.network.nodes.add(relationship.source_id)
            self.network.nodes.add(relationship.target_id)
            
            # 更新邻接表
            self.network.adjacency_list[relationship.source_id].add(relationship.target_id)
            self.network.reverse_adjacency[relationship.target_id].add(relationship.source_id)
    
    def _get_element_id(self, element: ET.Element) -> Optional[str]:
        """获取元素ID"""
        for attr in ['id', 'xmi:id', '{http://www.omg.org/XMI}id']:
            if attr in element.attrib:
                return element.attrib[attr]
        return None
    
    def _resolve_reference(self, reference: str, root: ET.Element) -> Optional[str]:
        """解析引用到实际ID"""
        # 处理不同的引用格式
        if reference.startswith('#'):
            return reference[1:]
        elif '.xmi#' in reference:
            return reference.split('#')[-1]
        elif reference.startswith('platform:/'):
            # 平台引用，需要更复杂的解析
            return None
        else:
            # 直接ID引用
            return reference
    
    def _extract_ids_from_value(self, value: str, all_ids: Set[str]) -> List[str]:
        """从属性值中提取ID引用"""
        extracted_ids = []
        
        # 简单的ID模式匹配
        for id_candidate in all_ids:
            if id_candidate in value:
                extracted_ids.append(id_candidate)
        
        return extracted_ids
    
    def _calculate_semantic_similarity(self, elem1: ET.Element, elem2: ET.Element, 
                                     metadata: Dict[str, Any]) -> float:
        """计算语义相似性"""
        # 简化的相似性计算
        similarity = 0.0
        
        # 标签名相似性
        tag1 = elem1.tag.split('}')[-1] if '}' in elem1.tag else elem1.tag
        tag2 = elem2.tag.split('}')[-1] if '}' in elem2.tag else elem2.tag
        
        if tag1 == tag2:
            similarity += 0.5
        
        # 名称相似性
        name1 = elem1.get('name', '')
        name2 = elem2.get('name', '')
        
        if name1 and name2:
            name_similarity = len(set(name1.lower().split()) & set(name2.lower().split())) / max(len(name1.split()), len(name2.split()))
            similarity += name_similarity * 0.3
        
        # 类型相似性
        type1 = elem1.get('xmi:type', elem1.get('type', ''))
        type2 = elem2.get('xmi:type', elem2.get('type', ''))
        
        if type1 == type2 and type1:
            similarity += 0.2
        
        return min(similarity, 1.0)
    
    def _count_relationship_types(self) -> Dict[str, int]:
        """统计关系类型"""
        type_counts = defaultdict(int)
        
        for relationship in self.network.relationships.values():
            type_counts[relationship.relation_type.value] += 1
        
        return dict(type_counts)
    
    def _find_strongly_connected_components(self) -> List[Set[str]]:
        """查找强连通分量（Tarjan算法简化版）"""
        # 简化实现，使用DFS查找强连通分量
        visited = set()
        components = []
        
        def dfs(node, component):
            if node in visited:
                return
            visited.add(node)
            component.add(node)
            
            for neighbor in self.network.adjacency_list.get(node, set()):
                dfs(neighbor, component)
        
        for node in self.network.nodes:
            if node not in visited:
                component = set()
                dfs(node, component)
                if len(component) > 1:
                    components.append(component)
        
        return components
    
    def _detect_circular_dependencies(self) -> List[List[str]]:
        """检测循环依赖"""
        cycles = []
        visited = set()
        path = []
        
        def dfs(node):
            if node in path:
                # 发现循环
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return
            
            if node in visited:
                return
            
            visited.add(node)
            path.append(node)
            
            for neighbor in self.network.adjacency_list.get(node, set()):
                dfs(neighbor)
            
            path.pop()
        
        for node in self.network.nodes:
            if node not in visited:
                dfs(node)
        
        return cycles
    
    def _find_orphaned_nodes(self) -> Set[str]:
        """查找孤立节点"""
        orphaned = set()
        
        for node in self.network.nodes:
            incoming = len(self.network.reverse_adjacency.get(node, set()))
            outgoing = len(self.network.adjacency_list.get(node, set()))
            
            if incoming == 0 and outgoing == 0:
                orphaned.add(node)
        
        return orphaned
    
    def _find_hub_nodes(self, top_n: int = 10) -> List[Tuple[str, int]]:
        """查找枢纽节点（度数最高的节点）"""
        node_degrees = []
        
        for node in self.network.nodes:
            incoming = len(self.network.reverse_adjacency.get(node, set()))
            outgoing = len(self.network.adjacency_list.get(node, set()))
            total_degree = incoming + outgoing
            
            node_degrees.append((node, total_degree))
        
        # 按度数排序，返回前top_n个
        node_degrees.sort(key=lambda x: x[1], reverse=True)
        return node_degrees[:top_n]
    
    def _bfs_path(self, source: str, target: str, max_depth: int) -> Optional[List[str]]:
        """BFS查找路径"""
        if source == target:
            return [source]
        
        queue = deque([(source, [source])])
        visited = {source}
        
        while queue:
            node, path = queue.popleft()
            
            if len(path) > max_depth:
                continue
            
            for neighbor in self.network.adjacency_list.get(node, set()):
                if neighbor == target:
                    return path + [neighbor]
                
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [neighbor]))
        
        return None
    
    def _export_to_dot(self) -> str:
        """导出为DOT格式"""
        lines = ["digraph relationships {"]
        
        # 节点定义
        for node in self.network.nodes:
            lines.append(f'  "{node}";')
        
        # 边定义
        for relationship in self.network.relationships.values():
            label = relationship.relation_type.value
            lines.append(f'  "{relationship.source_id}" -> "{relationship.target_id}" [label="{label}"];')
        
        lines.append("}")
        return "\n".join(lines)
    
    def _export_to_json(self) -> str:
        """导出为JSON格式"""
        import json
        
        data = {
            'nodes': list(self.network.nodes),
            'relationships': [
                {
                    'id': rel.id,
                    'source': rel.source_id,
                    'target': rel.target_id,
                    'type': rel.relation_type.value,
                    'confidence': rel.confidence
                }
                for rel in self.network.relationships.values()
            ]
        }
        
        return json.dumps(data, indent=2)
    
    def _export_to_csv(self) -> str:
        """导出为CSV格式"""
        lines = ["source,target,type,confidence"]
        
        for relationship in self.network.relationships.values():
            lines.append(f"{relationship.source_id},{relationship.target_id},{relationship.relation_type.value},{relationship.confidence}")
        
        return "\n".join(lines) 