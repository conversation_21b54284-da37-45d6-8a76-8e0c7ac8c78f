"""
版本管理服务 - 统一版本控制和变更追踪

处理XML元数据的版本化管理、变更检测、历史追踪等
"""

import time
import hashlib
import json
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import logging
from enum import Enum

class ChangeType(Enum):
    """变更类型枚举"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    MOVE = "move"
    RENAME = "rename"

@dataclass
class Change:
    """变更记录"""
    id: str
    change_type: ChangeType
    element_id: str
    timestamp: float
    old_value: Any = None
    new_value: Any = None
    attribute_path: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Version:
    """版本对象"""
    version_id: str
    timestamp: float
    author: str
    message: str
    parent_version: Optional[str] = None
    changes: List[Change] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    content_hash: str = ""

@dataclass
class Snapshot:
    """快照对象"""
    snapshot_id: str
    version_id: str
    timestamp: float
    data: Dict[str, Any]
    compressed: bool = False
    size_bytes: int = 0

class VersionManager:
    """
    版本管理服务
    
    功能特性:
    - 增量版本控制
    - 变更检测和追踪
    - 快照管理
    - 分支和合并
    - 回滚机制
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 版本控制配置
        self.max_versions = config.get('max_versions', 100)
        self.enable_compression = config.get('enable_compression', True)
        self.auto_snapshot = config.get('auto_snapshot', True)
        self.snapshot_interval = config.get('snapshot_interval', 10)  # 每10个版本一个快照
        
        # 存储结构
        self.versions: Dict[str, Version] = {}
        self.snapshots: Dict[str, Snapshot] = {}
        self.branches: Dict[str, str] = {"main": ""}  # branch_name -> latest_version_id
        self.current_branch = "main"
        
        # 变更检测
        self._previous_data: Optional[Dict[str, Any]] = None
        self._change_id_counter = 0
        
        # 索引
        self._element_versions: Dict[str, List[str]] = {}  # element_id -> [version_ids]
        self._version_timeline: List[str] = []  # 时间顺序的版本列表
    
    def create_version(self, data: Dict[str, Any], message: str = "", author: str = "system") -> str:
        """
        创建新版本
        
        Args:
            data: 版本数据
            message: 版本说明
            author: 作者
            
        Returns:
            版本ID
        """
        timestamp = time.time()
        version_id = self._generate_version_id(timestamp)
        
        # 计算变更
        changes = []
        if self._previous_data is not None:
            changes = self._detect_changes(self._previous_data, data)
        
        # 计算内容哈希
        content_hash = self._calculate_content_hash(data)
        
        # 获取父版本
        parent_version = self.branches.get(self.current_branch)
        
        # 创建版本对象
        version = Version(
            version_id=version_id,
            timestamp=timestamp,
            author=author,
            message=message,
            parent_version=parent_version,
            changes=changes,
            content_hash=content_hash
        )
        
        # 存储版本
        self.versions[version_id] = version
        self.branches[self.current_branch] = version_id
        self._version_timeline.append(version_id)
        
        # 更新元素版本索引
        self._update_element_index(data, version_id)
        
        # 创建快照（如果需要）
        if self._should_create_snapshot(version_id):
            self._create_snapshot(version_id, data)
        
        # 更新前一次数据
        self._previous_data = data.copy()
        
        # 清理旧版本（如果超过限制）
        self._cleanup_old_versions()
        
        self.logger.info(f"创建版本 {version_id}: {message}")
        return version_id
    
    def get_version(self, version_id: str) -> Optional[Version]:
        """获取版本信息"""
        return self.versions.get(version_id)
    
    def get_version_data(self, version_id: str) -> Optional[Dict[str, Any]]:
        """
        获取版本数据
        
        Args:
            version_id: 版本ID
            
        Returns:
            版本数据或None
        """
        if version_id not in self.versions:
            return None
        
        # 首先尝试从快照获取
        snapshot = self._find_snapshot_for_version(version_id)
        if snapshot:
            return self._reconstruct_from_snapshot(snapshot, version_id)
        
        # 从变更历史重建
        return self._reconstruct_from_changes(version_id)
    
    def get_latest_version(self, branch: str = None) -> Optional[str]:
        """获取最新版本ID"""
        branch = branch or self.current_branch
        return self.branches.get(branch)
    
    def get_version_history(self, element_id: Optional[str] = None, limit: int = 50) -> List[Version]:
        """
        获取版本历史
        
        Args:
            element_id: 元素ID（可选，获取特定元素的历史）
            limit: 限制数量
            
        Returns:
            版本历史列表
        """
        if element_id:
            # 获取特定元素的版本历史
            element_versions = self._element_versions.get(element_id, [])
            versions = [self.versions[vid] for vid in element_versions if vid in self.versions]
        else:
            # 获取全部版本历史
            versions = list(self.versions.values())
        
        # 按时间排序
        versions.sort(key=lambda v: v.timestamp, reverse=True)
        return versions[:limit]
    
    def compare_versions(self, version1_id: str, version2_id: str) -> Dict[str, Any]:
        """
        比较两个版本
        
        Args:
            version1_id: 版本1 ID
            version2_id: 版本2 ID
            
        Returns:
            比较结果
        """
        version1 = self.get_version(version1_id)
        version2 = self.get_version(version2_id)
        
        if not version1 or not version2:
            return {"error": "版本不存在"}
        
        data1 = self.get_version_data(version1_id)
        data2 = self.get_version_data(version2_id)
        
        if data1 is None or data2 is None:
            return {"error": "无法获取版本数据"}
        
        # 检测差异
        differences = self._detect_changes(data1, data2)
        
        return {
            "version1": {
                "id": version1_id,
                "timestamp": version1.timestamp,
                "author": version1.author,
                "message": version1.message
            },
            "version2": {
                "id": version2_id,
                "timestamp": version2.timestamp,
                "author": version2.author,
                "message": version2.message
            },
            "changes": [vars(change) for change in differences],
            "total_changes": len(differences)
        }
    
    def rollback_to_version(self, version_id: str) -> bool:
        """
        回滚到指定版本
        
        Args:
            version_id: 目标版本ID
            
        Returns:
            是否成功
        """
        try:
            data = self.get_version_data(version_id)
            if data is None:
                return False
            
            # 创建回滚版本
            rollback_message = f"回滚到版本 {version_id}"
            new_version_id = self.create_version(data, rollback_message)
            
            self.logger.info(f"成功回滚到版本 {version_id}，新版本ID: {new_version_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"回滚失败: {e}")
            return False
    
    def create_branch(self, branch_name: str, from_version: Optional[str] = None) -> bool:
        """
        创建分支
        
        Args:
            branch_name: 分支名称
            from_version: 基于的版本ID（可选）
            
        Returns:
            是否成功
        """
        if branch_name in self.branches:
            return False
        
        base_version = from_version or self.get_latest_version()
        if not base_version:
            return False
        
        self.branches[branch_name] = base_version
        self.logger.info(f"创建分支 {branch_name} 基于版本 {base_version}")
        return True
    
    def switch_branch(self, branch_name: str) -> bool:
        """切换分支"""
        if branch_name not in self.branches:
            return False
        
        self.current_branch = branch_name
        
        # 更新当前数据
        latest_version = self.branches[branch_name]
        if latest_version:
            self._previous_data = self.get_version_data(latest_version)
        
        self.logger.info(f"切换到分支 {branch_name}")
        return True
    
    def merge_branch(self, source_branch: str, target_branch: str = None, 
                    strategy: str = "auto") -> Dict[str, Any]:
        """
        合并分支
        
        Args:
            source_branch: 源分支
            target_branch: 目标分支（默认当前分支）
            strategy: 合并策略 ("auto", "manual")
            
        Returns:
            合并结果
        """
        target_branch = target_branch or self.current_branch
        
        if source_branch not in self.branches or target_branch not in self.branches:
            return {"success": False, "error": "分支不存在"}
        
        source_version = self.branches[source_branch]
        target_version = self.branches[target_branch]
        
        if not source_version or not target_version:
            return {"success": False, "error": "分支为空"}
        
        # 获取版本数据
        source_data = self.get_version_data(source_version)
        target_data = self.get_version_data(target_version)
        
        if source_data is None or target_data is None:
            return {"success": False, "error": "无法获取版本数据"}
        
        # 执行合并
        merged_data, conflicts = self._merge_data(source_data, target_data, strategy)
        
        if conflicts and strategy == "auto":
            return {
                "success": False,
                "error": "存在冲突，需要手动解决",
                "conflicts": conflicts
            }
        
        # 创建合并版本
        merge_message = f"合并分支 {source_branch} 到 {target_branch}"
        merged_version = self.create_version(merged_data, merge_message)
        
        return {
            "success": True,
            "merged_version": merged_version,
            "conflicts": conflicts
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取版本统计信息"""
        return {
            "total_versions": len(self.versions),
            "total_snapshots": len(self.snapshots),
            "total_branches": len(self.branches),
            "current_branch": self.current_branch,
            "storage_size": self._calculate_storage_size(),
            "oldest_version": min(self.versions.values(), key=lambda v: v.timestamp) if self.versions else None,
            "newest_version": max(self.versions.values(), key=lambda v: v.timestamp) if self.versions else None
        }
    
    def _detect_changes(self, old_data: Dict[str, Any], new_data: Dict[str, Any]) -> List[Change]:
        """检测数据变更"""
        changes = []
        
        # 获取所有键的并集
        all_keys = set(old_data.keys()) | set(new_data.keys())
        
        for key in all_keys:
            old_value = old_data.get(key)
            new_value = new_data.get(key)
            
            if key not in old_data:
                # 新增
                change = Change(
                    id=self._generate_change_id(),
                    change_type=ChangeType.CREATE,
                    element_id=key,
                    timestamp=time.time(),
                    new_value=new_value
                )
                changes.append(change)
            elif key not in new_data:
                # 删除
                change = Change(
                    id=self._generate_change_id(),
                    change_type=ChangeType.DELETE,
                    element_id=key,
                    timestamp=time.time(),
                    old_value=old_value
                )
                changes.append(change)
            elif old_value != new_value:
                # 修改
                change = Change(
                    id=self._generate_change_id(),
                    change_type=ChangeType.UPDATE,
                    element_id=key,
                    timestamp=time.time(),
                    old_value=old_value,
                    new_value=new_value
                )
                changes.append(change)
        
        return changes
    
    def _generate_version_id(self, timestamp: float) -> str:
        """生成版本ID"""
        return f"v_{int(timestamp)}_{len(self.versions):04d}"
    
    def _generate_change_id(self) -> str:
        """生成变更ID"""
        self._change_id_counter += 1
        return f"change_{self._change_id_counter:06d}"
    
    def _calculate_content_hash(self, data: Dict[str, Any]) -> str:
        """计算内容哈希"""
        content_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(content_str.encode()).hexdigest()[:16]
    
    def _update_element_index(self, data: Dict[str, Any], version_id: str):
        """更新元素版本索引"""
        for element_id in data.keys():
            if element_id not in self._element_versions:
                self._element_versions[element_id] = []
            self._element_versions[element_id].append(version_id)
    
    def _should_create_snapshot(self, version_id: str) -> bool:
        """判断是否应该创建快照"""
        if not self.auto_snapshot:
            return False
        
        version_count = len(self._version_timeline)
        return version_count % self.snapshot_interval == 0
    
    def _create_snapshot(self, version_id: str, data: Dict[str, Any]) -> str:
        """创建快照"""
        timestamp = time.time()
        snapshot_id = f"snap_{int(timestamp)}_{version_id}"
        
        # 计算大小
        data_str = json.dumps(data, default=str)
        size_bytes = len(data_str.encode('utf-8'))
        
        snapshot = Snapshot(
            snapshot_id=snapshot_id,
            version_id=version_id,
            timestamp=timestamp,
            data=data,
            size_bytes=size_bytes
        )
        
        self.snapshots[snapshot_id] = snapshot
        self.logger.debug(f"创建快照 {snapshot_id} for版本 {version_id}")
        return snapshot_id
    
    def _find_snapshot_for_version(self, version_id: str) -> Optional[Snapshot]:
        """查找版本对应的快照"""
        # 寻找最近的快照
        target_version = self.versions.get(version_id)
        if not target_version:
            return None
        
        best_snapshot = None
        min_distance = float('inf')
        
        for snapshot in self.snapshots.values():
            snapshot_version = self.versions.get(snapshot.version_id)
            if snapshot_version and snapshot_version.timestamp <= target_version.timestamp:
                distance = target_version.timestamp - snapshot_version.timestamp
                if distance < min_distance:
                    min_distance = distance
                    best_snapshot = snapshot
        
        return best_snapshot
    
    def _reconstruct_from_snapshot(self, snapshot: Snapshot, target_version_id: str) -> Dict[str, Any]:
        """从快照重建版本数据"""
        data = snapshot.data.copy()
        
        # 如果目标版本就是快照版本，直接返回
        if snapshot.version_id == target_version_id:
            return data
        
        # 应用从快照到目标版本的所有变更
        version_path = self._get_version_path(snapshot.version_id, target_version_id)
        
        for version_id in version_path[1:]:  # 跳过快照版本本身
            version = self.versions[version_id]
            data = self._apply_changes(data, version.changes)
        
        return data
    
    def _reconstruct_from_changes(self, version_id: str) -> Dict[str, Any]:
        """从变更历史重建数据"""
        # 构建版本路径
        version_path = self._build_version_path(version_id)
        
        # 从空数据开始应用所有变更
        data = {}
        for vid in version_path:
            version = self.versions[vid]
            data = self._apply_changes(data, version.changes)
        
        return data
    
    def _apply_changes(self, data: Dict[str, Any], changes: List[Change]) -> Dict[str, Any]:
        """应用变更到数据"""
        result = data.copy()
        
        for change in changes:
            if change.change_type == ChangeType.CREATE:
                result[change.element_id] = change.new_value
            elif change.change_type == ChangeType.UPDATE:
                result[change.element_id] = change.new_value
            elif change.change_type == ChangeType.DELETE:
                result.pop(change.element_id, None)
        
        return result
    
    def _build_version_path(self, version_id: str) -> List[str]:
        """构建版本路径"""
        path = []
        current = version_id
        
        while current:
            path.append(current)
            version = self.versions.get(current)
            current = version.parent_version if version else None
        
        return list(reversed(path))
    
    def _get_version_path(self, from_version: str, to_version: str) -> List[str]:
        """获取两个版本之间的路径"""
        from_path = self._build_version_path(from_version)
        to_path = self._build_version_path(to_version)
        
        # 找到共同祖先
        common_ancestor_idx = 0
        for i, (v1, v2) in enumerate(zip(from_path, to_path)):
            if v1 == v2:
                common_ancestor_idx = i
            else:
                break
        
        # 从共同祖先到目标版本的路径
        return to_path[common_ancestor_idx:]
    
    def _merge_data(self, source_data: Dict[str, Any], target_data: Dict[str, Any], 
                   strategy: str) -> Tuple[Dict[str, Any], List[str]]:
        """合并数据"""
        merged = target_data.copy()
        conflicts = []
        
        for key, source_value in source_data.items():
            if key not in target_data:
                # 源分支新增的数据
                merged[key] = source_value
            elif target_data[key] != source_value:
                # 冲突
                conflict_msg = f"键 {key} 存在冲突: target={target_data[key]}, source={source_value}"
                conflicts.append(conflict_msg)
                
                if strategy == "auto":
                    # 自动解决：选择源分支的值
                    merged[key] = source_value
        
        return merged, conflicts
    
    def _cleanup_old_versions(self):
        """清理旧版本"""
        if len(self.versions) <= self.max_versions:
            return
        
        # 按时间排序，保留最新的版本
        sorted_versions = sorted(self.versions.items(), key=lambda x: x[1].timestamp)
        versions_to_remove = sorted_versions[:-self.max_versions]
        
        for version_id, version in versions_to_remove:
            # 不删除分支指向的版本
            if version_id not in self.branches.values():
                del self.versions[version_id]
                self._version_timeline.remove(version_id)
                
                # 清理快照
                snapshots_to_remove = [sid for sid, snap in self.snapshots.items() 
                                     if snap.version_id == version_id]
                for sid in snapshots_to_remove:
                    del self.snapshots[sid]
    
    def _calculate_storage_size(self) -> int:
        """计算存储大小"""
        total_size = 0
        
        # 版本大小
        for version in self.versions.values():
            version_str = json.dumps(vars(version), default=str)
            total_size += len(version_str.encode('utf-8'))
        
        # 快照大小
        for snapshot in self.snapshots.values():
            total_size += snapshot.size_bytes
        
        return total_size 