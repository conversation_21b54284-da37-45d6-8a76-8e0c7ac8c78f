"""
工具模块 - 通用工具函数集合

提供XML元数据系统所需的各种工具：
- 配置管理工具
- 日志工具  
- 文件管理工具
- 性能监控工具
- 验证工具
"""

from typing import Dict, List, Any, Optional
import logging

# 工具模块
from .config_utils import ConfigManager, load_config, save_config
from .logging_utils import setup_logging, get_logger, LogLevel
from .file_utils import FileManager, path_utils, backup_file
from .performance_utils import PerformanceMonitor, Timer, profiler
from .validation_utils import (
    ValidationType, ValidationSeverity, ValidationIssue, ValidationResult,
    XMLValidator, JSONValidator, DataValidator, ValidationUtils,
    validate_xml_string, validate_xml_file, validate_json_string, 
    validate_json_file, validate_data_with_rules
)

# 版本信息
__version__ = "3.1.0"
__author__ = "XML元数据系统团队"

# 模块日志
logger = logging.getLogger(__name__)

# 全局工具实例
_config_manager = None
_file_manager = None
_performance_monitor = None

def get_config_manager(config_path: str = None) -> ConfigManager:
    """获取配置管理器单例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_path)
    return _config_manager

def get_file_manager(base_path: str = ".") -> FileManager:
    """获取文件管理器单例"""
    global _file_manager
    if _file_manager is None:
        _file_manager = FileManager(base_path)
    return _file_manager

def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器单例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

# 便捷函数
def setup_system_logging(level: LogLevel = LogLevel.INFO, log_file: str = None):
    """设置系统日志"""
    setup_logging(level, log_file)

def load_system_config(config_file: str = "config.json"):
    """加载系统配置"""
    return get_config_manager().load()

def save_system_config(config_file: str = "config.json"):
    """保存系统配置"""
    return get_config_manager().save()

def measure_performance(func_name: str = "operation"):
    """性能测量装饰器工厂"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            monitor = get_performance_monitor()
            timer = Timer()
            timer.start()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = timer.stop()
                monitor.record_metric(f"{func_name}_duration", duration)
        return wrapper
    return decorator

# 导出的公共接口
__all__ = [
    # 配置管理
    'ConfigManager',
    'load_config', 
    'save_config',
    'get_config_manager',
    'load_system_config',
    'save_system_config',
    
    # 日志工具
    'LogLevel',
    'setup_logging',
    'get_logger',
    'setup_system_logging',
    
    # 文件管理
    'FileManager',
    'path_utils',
    'backup_file',
    'get_file_manager',
    
    # 性能监控
    'PerformanceMonitor',
    'Timer',
    'profiler',
    'get_performance_monitor',
    'measure_performance',
    
    # 验证工具
    'ValidationType',
    'ValidationSeverity', 
    'ValidationIssue',
    'ValidationResult',
    'XMLValidator',
    'JSONValidator',
    'DataValidator',
    'ValidationUtils',
    'validate_xml_string',
    'validate_xml_file',
    'validate_json_string',
    'validate_json_file',
    'validate_data_with_rules',
    'is_valid_xml',
    'is_valid_json'
]

# 模块初始化日志
logger.info(f"工具模块已加载 v{__version__}") 