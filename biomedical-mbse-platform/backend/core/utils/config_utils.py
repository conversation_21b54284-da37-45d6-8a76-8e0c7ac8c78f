"""配置管理工具"""

import json
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = Path(config_path or "config.json")
        self._config: Dict[str, Any] = {}
    
    def load(self) -> Dict[str, Any]:
        """加载配置"""
        if self.config_path.exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = json.load(f)
        return self._config
    
    def save(self) -> bool:
        """保存配置"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
            return True
        except Exception:
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        self._config[key] = value

def load_config(path: str) -> Dict[str, Any]:
    """加载配置文件"""
    manager = ConfigManager(path)
    return manager.load()

def save_config(config: Dict[str, Any], path: str) -> bool:
    """保存配置文件"""
    manager = ConfigManager(path)
    manager._config = config
    return manager.save() 