"""文件工具"""
from pathlib import Path
from typing import Optional
import shutil

class FileManager:
    """文件管理器"""
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
    
    def create_directory(self, path: str) -> bool:
        try:
            (self.base_path / path).mkdir(parents=True, exist_ok=True)
            return True
        except:
            return False

def path_utils(path: str) -> Path:
    """路径工具函数"""
    return Path(path).resolve()

def backup_file(source: str, backup_dir: Optional[str] = None) -> bool:
    """备份文件"""
    try:
        source_path = Path(source)
        if backup_dir:
            backup_path = Path(backup_dir) / f"{source_path.name}.bak"
        else:
            backup_path = source_path.with_suffix(f"{source_path.suffix}.bak")
        shutil.copy2(source_path, backup_path)
        return True
    except:
        return False 