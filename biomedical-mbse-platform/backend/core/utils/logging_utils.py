"""日志工具"""

import logging
from enum import Enum
from typing import Optional

class LogLevel(Enum):
    DEBUG = logging.DEBUG
    INFO = logging.INFO  
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL

def setup_logging(level: LogLevel = LogLevel.INFO, 
                 log_file: Optional[str] = None,
                 format_string: Optional[str] = None) -> None:
    """设置日志系统"""
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=level.value,
        format=format_string,
        filename=log_file
    )

def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    return logging.getLogger(name) 