"""性能工具"""
import time
from contextlib import contextmanager
from typing import Dict, Any

class PerformanceMonitor:
    """性能监控器"""
    def __init__(self):
        self.metrics: Dict[str, Any] = {}
    
    def record_metric(self, name: str, value: float):
        self.metrics[name] = value
    
    def get_metrics(self) -> Dict[str, Any]:
        return self.metrics.copy()

class Timer:
    """计时器"""
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        self.start_time = time.time()
    
    def stop(self) -> float:
        self.end_time = time.time()
        return self.end_time - (self.start_time or 0)

@contextmanager
def profiler(name: str = "operation"):
    """性能分析上下文管理器"""
    start_time = time.time()
    try:
        yield
    finally:
        end_time = time.time()
        print(f"{name} 耗时: {end_time - start_time:.3f}秒") 