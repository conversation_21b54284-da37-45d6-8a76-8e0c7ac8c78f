"""
验证工具 - 数据验证和格式检查

提供各种数据验证功能：
- XML格式验证
- JSON格式验证
- 数据完整性验证
- 模式验证
- 自定义验证规则
"""

import json
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import re
import hashlib
from datetime import datetime

class ValidationType(Enum):
    """验证类型"""
    XML_FORMAT = "xml_format"           # XML格式验证
    JSON_FORMAT = "json_format"         # JSON格式验证
    SCHEMA = "schema"                   # 模式验证
    DATA_TYPE = "data_type"             # 数据类型验证
    RANGE = "range"                     # 范围验证
    PATTERN = "pattern"                 # 模式匹配验证
    CUSTOM = "custom"                   # 自定义验证
    COMPLETENESS = "completeness"       # 完整性验证

class ValidationSeverity(Enum):
    """验证严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class ValidationIssue:
    """验证问题"""
    type: ValidationType
    severity: ValidationSeverity
    message: str
    path: str = ""
    line: int = 0
    column: int = 0
    expected: Any = None
    actual: Any = None
    suggestion: str = ""

@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool = True
    issues: List[ValidationIssue] = field(default_factory=list)
    summary: Dict[str, int] = field(default_factory=dict)
    validation_time: float = 0.0
    validated_at: Optional[datetime] = None
    
    def add_issue(self, issue: ValidationIssue):
        """添加验证问题"""
        self.issues.append(issue)
        if not self.is_valid and issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL]:
            self.is_valid = False
        
        # 更新摘要
        severity_key = issue.severity.value
        self.summary[severity_key] = self.summary.get(severity_key, 0) + 1
    
    def get_errors(self) -> List[ValidationIssue]:
        """获取错误问题"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.ERROR]
    
    def get_warnings(self) -> List[ValidationIssue]:
        """获取警告问题"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.WARNING]
    
    def get_criticals(self) -> List[ValidationIssue]:
        """获取严重问题"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.CRITICAL]
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.get_errors()) > 0 or len(self.get_criticals()) > 0

class XMLValidator:
    """XML格式验证器"""
    
    def __init__(self):
        self.encoding_patterns = [
            'utf-8', 'utf-16', 'iso-8859-1', 'ascii'
        ]
    
    def validate_xml_string(self, xml_content: str) -> ValidationResult:
        """验证XML字符串"""
        result = ValidationResult()
        start_time = datetime.now()
        
        try:
            # 检查空内容
            if not xml_content or not xml_content.strip():
                result.add_issue(ValidationIssue(
                    type=ValidationType.XML_FORMAT,
                    severity=ValidationSeverity.ERROR,
                    message="XML内容为空",
                    suggestion="提供有效的XML内容"
                ))
                result.is_valid = False
                return result
            
            # 尝试解析XML
            try:
                root = ET.fromstring(xml_content)
                result.add_issue(ValidationIssue(
                    type=ValidationType.XML_FORMAT,
                    severity=ValidationSeverity.INFO,
                    message=f"XML格式有效，根元素: {root.tag}"
                ))
            except ET.ParseError as e:
                result.add_issue(ValidationIssue(
                    type=ValidationType.XML_FORMAT,
                    severity=ValidationSeverity.ERROR,
                    message=f"XML解析错误: {str(e)}",
                    line=getattr(e, 'lineno', 0),
                    column=getattr(e, 'offset', 0),
                    suggestion="检查XML标签配对和语法"
                ))
                result.is_valid = False
            
            # 检查编码声明
            if '<?xml' in xml_content:
                encoding_match = re.search(r'encoding=[\'"](.*?)[\'"]', xml_content)
                if encoding_match:
                    encoding = encoding_match.group(1).lower()
                    if encoding not in self.encoding_patterns:
                        result.add_issue(ValidationIssue(
                            type=ValidationType.XML_FORMAT,
                            severity=ValidationSeverity.WARNING,
                            message=f"不常见的编码: {encoding}",
                            suggestion="考虑使用UTF-8编码"
                        ))
            
            # 检查命名空间
            self._validate_namespaces(xml_content, result)
            
            # 检查结构完整性
            self._validate_structure(xml_content, result)
            
        except Exception as e:
            result.add_issue(ValidationIssue(
                type=ValidationType.XML_FORMAT,
                severity=ValidationSeverity.CRITICAL,
                message=f"验证过程异常: {str(e)}"
            ))
            result.is_valid = False
        
        finally:
            end_time = datetime.now()
            result.validation_time = (end_time - start_time).total_seconds()
            result.validated_at = end_time
        
        return result
    
    def validate_xml_file(self, file_path: str) -> ValidationResult:
        """验证XML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            return self.validate_xml_string(content)
        except FileNotFoundError:
            result = ValidationResult()
            result.add_issue(ValidationIssue(
                type=ValidationType.XML_FORMAT,
                severity=ValidationSeverity.ERROR,
                message=f"文件不存在: {file_path}"
            ))
            result.is_valid = False
            return result
        except UnicodeDecodeError as e:
            result = ValidationResult()
            result.add_issue(ValidationIssue(
                type=ValidationType.XML_FORMAT,
                severity=ValidationSeverity.ERROR,
                message=f"文件编码错误: {str(e)}",
                suggestion="检查文件编码格式"
            ))
            result.is_valid = False
            return result
    
    def _validate_namespaces(self, xml_content: str, result: ValidationResult):
        """验证命名空间"""
        # 查找命名空间声明
        namespace_declarations = re.findall(r'xmlns(?::(\w+))?=[\'"](.*?)[\'"]', xml_content)
        
        for prefix, uri in namespace_declarations:
            if not uri:
                result.add_issue(ValidationIssue(
                    type=ValidationType.XML_FORMAT,
                    severity=ValidationSeverity.WARNING,
                    message=f"空的命名空间URI",
                    suggestion="提供有效的命名空间URI"
                ))
            elif not uri.startswith(('http://', 'https://', 'urn:')):
                result.add_issue(ValidationIssue(
                    type=ValidationType.XML_FORMAT,
                    severity=ValidationSeverity.WARNING,
                    message=f"非标准命名空间URI: {uri}",
                    suggestion="使用标准的URI格式"
                ))
    
    def _validate_structure(self, xml_content: str, result: ValidationResult):
        """验证结构完整性"""
        # 检查是否有多个根元素
        root_elements = re.findall(r'<(\w+)(?:\s+[^>]*)?(?:\s*/\s*>|>[^<]*</\1>)', xml_content)
        
        # 计算缩进一致性
        lines = xml_content.split('\n')
        indent_pattern = None
        inconsistent_indents = 0
        
        for i, line in enumerate(lines, 1):
            stripped = line.lstrip()
            if stripped and stripped.startswith('<') and not stripped.startswith('<?'):
                indent = len(line) - len(stripped)
                if indent > 0:
                    current_indent = line[:indent]
                    if indent_pattern is None:
                        # 确定缩进模式
                        if '\t' in current_indent:
                            indent_pattern = 'tab'
                        elif '  ' in current_indent:
                            indent_pattern = 'space'
                    else:
                        # 检查缩进一致性
                        if (indent_pattern == 'tab' and ' ' in current_indent) or \
                           (indent_pattern == 'space' and '\t' in current_indent):
                            inconsistent_indents += 1
        
        if inconsistent_indents > 0:
            result.add_issue(ValidationIssue(
                type=ValidationType.XML_FORMAT,
                severity=ValidationSeverity.WARNING,
                message=f"发现{inconsistent_indents}处缩进不一致",
                suggestion="使用统一的缩进风格（制表符或空格）"
            ))

class JSONValidator:
    """JSON格式验证器"""
    
    def validate_json_string(self, json_content: str) -> ValidationResult:
        """验证JSON字符串"""
        result = ValidationResult()
        start_time = datetime.now()
        
        try:
            # 检查空内容
            if not json_content or not json_content.strip():
                result.add_issue(ValidationIssue(
                    type=ValidationType.JSON_FORMAT,
                    severity=ValidationSeverity.ERROR,
                    message="JSON内容为空",
                    suggestion="提供有效的JSON内容"
                ))
                result.is_valid = False
                return result
            
            # 尝试解析JSON
            try:
                data = json.loads(json_content)
                result.add_issue(ValidationIssue(
                    type=ValidationType.JSON_FORMAT,
                    severity=ValidationSeverity.INFO,
                    message=f"JSON格式有效，类型: {type(data).__name__}"
                ))
                
                # 验证JSON结构
                self._validate_json_structure(data, result)
                
            except json.JSONDecodeError as e:
                result.add_issue(ValidationIssue(
                    type=ValidationType.JSON_FORMAT,
                    severity=ValidationSeverity.ERROR,
                    message=f"JSON解析错误: {str(e)}",
                    line=getattr(e, 'lineno', 0),
                    column=getattr(e, 'colno', 0),
                    suggestion="检查JSON语法和格式"
                ))
                result.is_valid = False
            
            # 检查常见问题
            self._check_json_common_issues(json_content, result)
            
        except Exception as e:
            result.add_issue(ValidationIssue(
                type=ValidationType.JSON_FORMAT,
                severity=ValidationSeverity.CRITICAL,
                message=f"验证过程异常: {str(e)}"
            ))
            result.is_valid = False
        
        finally:
            end_time = datetime.now()
            result.validation_time = (end_time - start_time).total_seconds()
            result.validated_at = end_time
        
        return result
    
    def validate_json_file(self, file_path: str) -> ValidationResult:
        """验证JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            return self.validate_json_string(content)
        except FileNotFoundError:
            result = ValidationResult()
            result.add_issue(ValidationIssue(
                type=ValidationType.JSON_FORMAT,
                severity=ValidationSeverity.ERROR,
                message=f"文件不存在: {file_path}"
            ))
            result.is_valid = False
            return result
        except UnicodeDecodeError as e:
            result = ValidationResult()
            result.add_issue(ValidationIssue(
                type=ValidationType.JSON_FORMAT,
                severity=ValidationSeverity.ERROR,
                message=f"文件编码错误: {str(e)}",
                suggestion="检查文件编码格式"
            ))
            result.is_valid = False
            return result
    
    def _validate_json_structure(self, data: Any, result: ValidationResult, path: str = ""):
        """验证JSON结构"""
        if isinstance(data, dict):
            # 检查空对象
            if not data:
                result.add_issue(ValidationIssue(
                    type=ValidationType.JSON_FORMAT,
                    severity=ValidationSeverity.INFO,
                    message=f"空对象: {path}",
                    path=path
                ))
            
            # 检查键的命名规范
            for key in data.keys():
                if not isinstance(key, str):
                    result.add_issue(ValidationIssue(
                        type=ValidationType.JSON_FORMAT,
                        severity=ValidationSeverity.WARNING,
                        message=f"非字符串键: {key}",
                        path=f"{path}.{key}"
                    ))
                elif not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', key):
                    result.add_issue(ValidationIssue(
                        type=ValidationType.JSON_FORMAT,
                        severity=ValidationSeverity.WARNING,
                        message=f"键名不符合标准命名规范: {key}",
                        path=f"{path}.{key}",
                        suggestion="使用字母、数字和下划线，以字母或下划线开头"
                    ))
                
                # 递归验证值
                self._validate_json_structure(data[key], result, f"{path}.{key}" if path else key)
        
        elif isinstance(data, list):
            # 检查空数组
            if not data:
                result.add_issue(ValidationIssue(
                    type=ValidationType.JSON_FORMAT,
                    severity=ValidationSeverity.INFO,
                    message=f"空数组: {path}",
                    path=path
                ))
            
            # 递归验证数组元素
            for i, item in enumerate(data):
                self._validate_json_structure(item, result, f"{path}[{i}]")
    
    def _check_json_common_issues(self, json_content: str, result: ValidationResult):
        """检查JSON常见问题"""
        # 检查尾随逗号
        if re.search(r',\s*[}\]]', json_content):
            result.add_issue(ValidationIssue(
                type=ValidationType.JSON_FORMAT,
                severity=ValidationSeverity.WARNING,
                message="发现尾随逗号",
                suggestion="移除对象或数组末尾的多余逗号"
            ))
        
        # 检查单引号（JSON标准要求双引号）
        if "'" in json_content:
            result.add_issue(ValidationIssue(
                type=ValidationType.JSON_FORMAT,
                severity=ValidationSeverity.WARNING,
                message="可能使用了单引号",
                suggestion="JSON标准要求使用双引号"
            ))

class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.validators: Dict[str, Callable] = {}
    
    def register_validator(self, name: str, validator_func: Callable):
        """注册自定义验证器"""
        self.validators[name] = validator_func
    
    def validate_data_type(self, data: Any, expected_type: type, path: str = "") -> ValidationResult:
        """验证数据类型"""
        result = ValidationResult()
        
        if not isinstance(data, expected_type):
            result.add_issue(ValidationIssue(
                type=ValidationType.DATA_TYPE,
                severity=ValidationSeverity.ERROR,
                message=f"数据类型不匹配",
                path=path,
                expected=expected_type.__name__,
                actual=type(data).__name__
            ))
            result.is_valid = False
        
        return result
    
    def validate_range(self, value: Union[int, float], min_val: Optional[Union[int, float]] = None, 
                      max_val: Optional[Union[int, float]] = None, path: str = "") -> ValidationResult:
        """验证数值范围"""
        result = ValidationResult()
        
        if not isinstance(value, (int, float)):
            result.add_issue(ValidationIssue(
                type=ValidationType.RANGE,
                severity=ValidationSeverity.ERROR,
                message="值不是数值类型",
                path=path,
                actual=type(value).__name__
            ))
            result.is_valid = False
            return result
        
        if min_val is not None and value < min_val:
            result.add_issue(ValidationIssue(
                type=ValidationType.RANGE,
                severity=ValidationSeverity.ERROR,
                message=f"值小于最小值",
                path=path,
                expected=f">= {min_val}",
                actual=value
            ))
            result.is_valid = False
        
        if max_val is not None and value > max_val:
            result.add_issue(ValidationIssue(
                type=ValidationType.RANGE,
                severity=ValidationSeverity.ERROR,
                message=f"值大于最大值",
                path=path,
                expected=f"<= {max_val}",
                actual=value
            ))
            result.is_valid = False
        
        return result
    
    def validate_pattern(self, text: str, pattern: str, path: str = "") -> ValidationResult:
        """验证模式匹配"""
        result = ValidationResult()
        
        if not isinstance(text, str):
            result.add_issue(ValidationIssue(
                type=ValidationType.PATTERN,
                severity=ValidationSeverity.ERROR,
                message="值不是字符串类型",
                path=path,
                actual=type(text).__name__
            ))
            result.is_valid = False
            return result
        
        try:
            if not re.match(pattern, text):
                result.add_issue(ValidationIssue(
                    type=ValidationType.PATTERN,
                    severity=ValidationSeverity.ERROR,
                    message="值不匹配指定模式",
                    path=path,
                    expected=pattern,
                    actual=text
                ))
                result.is_valid = False
        except re.error as e:
            result.add_issue(ValidationIssue(
                type=ValidationType.PATTERN,
                severity=ValidationSeverity.ERROR,
                message=f"正则表达式错误: {str(e)}",
                path=path
            ))
            result.is_valid = False
        
        return result
    
    def validate_completeness(self, data: Dict[str, Any], required_fields: List[str], 
                            path: str = "") -> ValidationResult:
        """验证数据完整性"""
        result = ValidationResult()
        
        if not isinstance(data, dict):
            result.add_issue(ValidationIssue(
                type=ValidationType.COMPLETENESS,
                severity=ValidationSeverity.ERROR,
                message="数据不是字典类型",
                path=path,
                actual=type(data).__name__
            ))
            result.is_valid = False
            return result
        
        missing_fields = []
        for field in required_fields:
            if field not in data:
                missing_fields.append(field)
            elif data[field] is None or data[field] == "":
                result.add_issue(ValidationIssue(
                    type=ValidationType.COMPLETENESS,
                    severity=ValidationSeverity.WARNING,
                    message=f"必需字段为空: {field}",
                    path=f"{path}.{field}" if path else field
                ))
        
        if missing_fields:
            result.add_issue(ValidationIssue(
                type=ValidationType.COMPLETENESS,
                severity=ValidationSeverity.ERROR,
                message=f"缺少必需字段: {', '.join(missing_fields)}",
                path=path
            ))
            result.is_valid = False
        
        return result
    
    def validate_custom(self, data: Any, validator_name: str, path: str = "", **kwargs) -> ValidationResult:
        """执行自定义验证"""
        result = ValidationResult()
        
        if validator_name not in self.validators:
            result.add_issue(ValidationIssue(
                type=ValidationType.CUSTOM,
                severity=ValidationSeverity.ERROR,
                message=f"未找到验证器: {validator_name}",
                path=path
            ))
            result.is_valid = False
            return result
        
        try:
            validator_func = self.validators[validator_name]
            custom_result = validator_func(data, path=path, **kwargs)
            
            if isinstance(custom_result, ValidationResult):
                return custom_result
            elif isinstance(custom_result, bool):
                if not custom_result:
                    result.add_issue(ValidationIssue(
                        type=ValidationType.CUSTOM,
                        severity=ValidationSeverity.ERROR,
                        message=f"自定义验证失败: {validator_name}",
                        path=path
                    ))
                    result.is_valid = False
            else:
                result.add_issue(ValidationIssue(
                    type=ValidationType.CUSTOM,
                    severity=ValidationSeverity.WARNING,
                    message=f"自定义验证器返回类型不明确: {type(custom_result)}",
                    path=path
                ))
        
        except Exception as e:
            result.add_issue(ValidationIssue(
                type=ValidationType.CUSTOM,
                severity=ValidationSeverity.ERROR,
                message=f"自定义验证器执行错误: {str(e)}",
                path=path
            ))
            result.is_valid = False
        
        return result

class ValidationUtils:
    """验证工具类 - 统一入口"""
    
    def __init__(self):
        self.xml_validator = XMLValidator()
        self.json_validator = JSONValidator()
        self.data_validator = DataValidator()
    
    def validate_xml(self, content: Union[str, Path], is_file: bool = False) -> ValidationResult:
        """验证XML"""
        if is_file or isinstance(content, Path):
            return self.xml_validator.validate_xml_file(str(content))
        else:
            return self.xml_validator.validate_xml_string(content)
    
    def validate_json(self, content: Union[str, Path], is_file: bool = False) -> ValidationResult:
        """验证JSON"""
        if is_file or isinstance(content, Path):
            return self.json_validator.validate_json_file(str(content))
        else:
            return self.json_validator.validate_json_string(content)
    
    def validate_data(self, data: Any, rules: Dict[str, Any], path: str = "") -> ValidationResult:
        """根据规则验证数据"""
        combined_result = ValidationResult()
        
        for rule_name, rule_config in rules.items():
            if rule_name == "type":
                result = self.data_validator.validate_data_type(data, rule_config, path)
            elif rule_name == "range":
                min_val = rule_config.get("min")
                max_val = rule_config.get("max")
                result = self.data_validator.validate_range(data, min_val, max_val, path)
            elif rule_name == "pattern":
                result = self.data_validator.validate_pattern(data, rule_config, path)
            elif rule_name == "required_fields":
                result = self.data_validator.validate_completeness(data, rule_config, path)
            elif rule_name == "custom":
                validator_name = rule_config.get("validator")
                kwargs = rule_config.get("kwargs", {})
                result = self.data_validator.validate_custom(data, validator_name, path, **kwargs)
            else:
                continue
            
            # 合并结果
            combined_result.issues.extend(result.issues)
            if not result.is_valid:
                combined_result.is_valid = False
        
        # 更新摘要
        for issue in combined_result.issues:
            severity_key = issue.severity.value
            combined_result.summary[severity_key] = combined_result.summary.get(severity_key, 0) + 1
        
        return combined_result
    
    def batch_validate(self, items: List[Tuple[Any, Dict[str, Any]]], 
                      stop_on_error: bool = False) -> Dict[int, ValidationResult]:
        """批量验证"""
        results = {}
        
        for i, (data, rules) in enumerate(items):
            result = self.validate_data(data, rules, f"item[{i}]")
            results[i] = result
            
            if stop_on_error and not result.is_valid:
                break
        
        return results
    
    def register_custom_validator(self, name: str, validator_func: Callable):
        """注册自定义验证器"""
        self.data_validator.register_validator(name, validator_func)

# 全局验证工具实例
validation_utils = ValidationUtils()

# 便捷函数
def validate_xml_string(xml_content: str) -> ValidationResult:
    """验证XML字符串"""
    return validation_utils.validate_xml(xml_content)

def validate_xml_file(file_path: str) -> ValidationResult:
    """验证XML文件"""
    return validation_utils.validate_xml(file_path, is_file=True)

def validate_json_string(json_content: str) -> ValidationResult:
    """验证JSON字符串"""
    return validation_utils.validate_json(json_content)

def validate_json_file(file_path: str) -> ValidationResult:
    """验证JSON文件"""
    return validation_utils.validate_json(file_path, is_file=True)

def validate_data_with_rules(data: Any, rules: Dict[str, Any]) -> ValidationResult:
    """根据规则验证数据"""
    return validation_utils.validate_data(data, rules)

def is_valid_xml(content: str) -> bool:
    """检查是否为有效XML"""
    result = validate_xml_string(content)
    return result.is_valid

def is_valid_json(content: str) -> bool:
    """检查是否为有效JSON"""
    result = validate_json_string(content)
    return result.is_valid 