"""
XML兼容性工具模块 - 解决ElementTree兼容性问题

主要解决的问题：
1. getparent() 方法在xml.etree.ElementTree中不存在，但在lxml中存在
2. 不同ElementTree实现之间的API差异
3. 提供统一的兼容性接口

使用方式：
    from core.utils.xml_compatibility import XMLCompatibility
    
    compat = XMLCompatibility()
    parent = compat.get_parent(element, root)
    children = compat.get_children(element)
"""

import xml.etree.ElementTree as ET
from typing import Optional, List, Dict, Any, Union
import logging

# 尝试导入lxml，如果可用则优先使用
try:
    import lxml.etree as lxml_ET
    LXML_AVAILABLE = True
except ImportError:
    lxml_ET = None
    LXML_AVAILABLE = False

logger = logging.getLogger(__name__)

class XMLCompatibility:
    """
    XML兼容性工具类
    
    提供统一的XML操作接口，自动处理不同ElementTree实现之间的差异
    """
    
    def __init__(self, prefer_lxml: bool = True):
        """
        初始化兼容性工具
        
        Args:
            prefer_lxml: 是否优先使用lxml（如果可用）
        """
        self.prefer_lxml = prefer_lxml and LXML_AVAILABLE
        self.using_lxml = False
        
        # 父子关系缓存，用于xml.etree.ElementTree
        self._parent_map_cache: Dict[int, Dict[ET.Element, Optional[ET.Element]]] = {}
        
        logger.info(f"XML兼容性工具初始化: lxml可用={LXML_AVAILABLE}, 优先使用lxml={self.prefer_lxml}")
    
    def parse_string(self, xml_content: str) -> ET.Element:
        """
        解析XML字符串，自动选择最佳解析器
        
        Args:
            xml_content: XML内容字符串
            
        Returns:
            根元素
        """
        if self.prefer_lxml and LXML_AVAILABLE:
            try:
                root = lxml_ET.fromstring(xml_content)
                self.using_lxml = True
                logger.debug("使用lxml解析XML")
                return root
            except Exception as e:
                logger.warning(f"lxml解析失败，回退到ElementTree: {e}")
        
        # 使用标准库ElementTree
        root = ET.fromstring(xml_content)
        self.using_lxml = False
        logger.debug("使用ElementTree解析XML")
        return root
    
    def parse_file(self, xml_file: str) -> ET.Element:
        """
        解析XML文件，自动选择最佳解析器
        
        Args:
            xml_file: XML文件路径
            
        Returns:
            根元素
        """
        if self.prefer_lxml and LXML_AVAILABLE:
            try:
                tree = lxml_ET.parse(xml_file)
                root = tree.getroot()
                self.using_lxml = True
                logger.debug(f"使用lxml解析文件: {xml_file}")
                return root
            except Exception as e:
                logger.warning(f"lxml解析文件失败，回退到ElementTree: {e}")
        
        # 使用标准库ElementTree
        tree = ET.parse(xml_file)
        root = tree.getroot()
        self.using_lxml = False
        logger.debug(f"使用ElementTree解析文件: {xml_file}")
        return root
    
    def get_parent(self, element: ET.Element, root: Optional[ET.Element] = None) -> Optional[ET.Element]:
        """
        获取元素的父节点 - 兼容性方法
        
        Args:
            element: 目标元素
            root: 根元素（当使用ElementTree时需要）
            
        Returns:
            父元素，如果是根元素则返回None
        """
        # 如果元素有getparent方法（lxml），直接使用
        if hasattr(element, 'getparent'):
            return element.getparent()
        
        # 如果没有根元素，无法查找父节点
        if root is None:
            logger.warning("使用ElementTree时需要提供root参数来查找父节点")
            return None
        
        # 使用缓存的父子映射
        root_id = id(root)
        if root_id not in self._parent_map_cache:
            self._parent_map_cache[root_id] = self._build_parent_map(root)
        
        return self._parent_map_cache[root_id].get(element)
    
    def get_children(self, element: ET.Element) -> List[ET.Element]:
        """
        获取元素的所有子元素
        
        Args:
            element: 父元素
            
        Returns:
            子元素列表
        """
        return list(element)
    
    def get_siblings(self, element: ET.Element, root: Optional[ET.Element] = None) -> List[ET.Element]:
        """
        获取元素的兄弟节点
        
        Args:
            element: 目标元素
            root: 根元素（当使用ElementTree时需要）
            
        Returns:
            兄弟元素列表（不包括自己）
        """
        parent = self.get_parent(element, root)
        if parent is None:
            return []
        
        siblings = []
        for child in parent:
            if child != element:
                siblings.append(child)
        
        return siblings
    
    def get_ancestors(self, element: ET.Element, root: Optional[ET.Element] = None) -> List[ET.Element]:
        """
        获取元素的所有祖先节点
        
        Args:
            element: 目标元素
            root: 根元素
            
        Returns:
            祖先元素列表，从直接父节点到根节点
        """
        ancestors = []
        current = element
        
        while True:
            parent = self.get_parent(current, root)
            if parent is None:
                break
            ancestors.append(parent)
            current = parent
        
        return ancestors
    
    def get_descendants(self, element: ET.Element) -> List[ET.Element]:
        """
        获取元素的所有后代节点
        
        Args:
            element: 父元素
            
        Returns:
            后代元素列表
        """
        descendants = []
        
        def collect_descendants(elem):
            for child in elem:
                descendants.append(child)
                collect_descendants(child)
        
        collect_descendants(element)
        return descendants
    
    def find_element_path(self, element: ET.Element, root: ET.Element) -> str:
        """
        获取元素的XPath路径
        
        Args:
            element: 目标元素
            root: 根元素
            
        Returns:
            XPath路径字符串
        """
        if element == root:
            return f"/{element.tag}"
        
        path_parts = []
        current = element
        
        while current != root:
            parent = self.get_parent(current, root)
            if parent is None:
                break
            
            # 计算在同名兄弟中的位置
            siblings_with_same_tag = [child for child in parent if child.tag == current.tag]
            if len(siblings_with_same_tag) > 1:
                index = siblings_with_same_tag.index(current) + 1
                path_parts.append(f"{current.tag}[{index}]")
            else:
                path_parts.append(current.tag)
            
            current = parent
        
        path_parts.append(root.tag)
        path_parts.reverse()
        
        return "/" + "/".join(path_parts)
    
    def is_ancestor(self, ancestor: ET.Element, descendant: ET.Element, root: Optional[ET.Element] = None) -> bool:
        """
        检查一个元素是否是另一个元素的祖先
        
        Args:
            ancestor: 可能的祖先元素
            descendant: 可能的后代元素
            root: 根元素
            
        Returns:
            如果ancestor是descendant的祖先则返回True
        """
        current = descendant
        
        while True:
            parent = self.get_parent(current, root)
            if parent is None:
                return False
            if parent == ancestor:
                return True
            current = parent
    
    def get_element_depth(self, element: ET.Element, root: ET.Element) -> int:
        """
        获取元素在树中的深度
        
        Args:
            element: 目标元素
            root: 根元素
            
        Returns:
            深度值（根元素深度为0）
        """
        if element == root:
            return 0
        
        depth = 0
        current = element
        
        while current != root:
            parent = self.get_parent(current, root)
            if parent is None:
                return -1  # 元素不在树中
            depth += 1
            current = parent
        
        return depth
    
    def _build_parent_map(self, root: ET.Element) -> Dict[ET.Element, Optional[ET.Element]]:
        """
        构建父子关系映射表（用于ElementTree）
        
        Args:
            root: 根元素
            
        Returns:
            元素到父元素的映射字典
        """
        parent_map = {root: None}
        
        def build_map_recursive(element):
            for child in element:
                parent_map[child] = element
                build_map_recursive(child)
        
        build_map_recursive(root)
        return parent_map
    
    def clear_cache(self):
        """清空父子关系缓存"""
        self._parent_map_cache.clear()
        logger.debug("XML兼容性工具缓存已清空")
    
    def get_parser_info(self) -> Dict[str, Any]:
        """
        获取当前解析器信息
        
        Returns:
            解析器信息字典
        """
        return {
            'lxml_available': LXML_AVAILABLE,
            'prefer_lxml': self.prefer_lxml,
            'using_lxml': self.using_lxml,
            'current_parser': 'lxml' if self.using_lxml else 'ElementTree',
            'features': {
                'getparent_native': self.using_lxml,
                'xpath_support': self.using_lxml,
                'namespace_support': True,
                'validation_support': self.using_lxml
            }
        }

# 全局兼容性实例
_global_compat = XMLCompatibility()

# 便捷函数
def get_parent(element: ET.Element, root: Optional[ET.Element] = None) -> Optional[ET.Element]:
    """获取元素父节点的便捷函数"""
    return _global_compat.get_parent(element, root)

def get_children(element: ET.Element) -> List[ET.Element]:
    """获取子元素的便捷函数"""
    return _global_compat.get_children(element)

def get_siblings(element: ET.Element, root: Optional[ET.Element] = None) -> List[ET.Element]:
    """获取兄弟节点的便捷函数"""
    return _global_compat.get_siblings(element, root)

def parse_xml(content: Union[str, bytes], is_file: bool = False) -> ET.Element:
    """解析XML的便捷函数"""
    if is_file:
        return _global_compat.parse_file(content)
    else:
        return _global_compat.parse_string(content)

def get_parser_info() -> Dict[str, Any]:
    """获取解析器信息的便捷函数"""
    return _global_compat.get_parser_info()

# 兼容性检查函数
def check_compatibility() -> Dict[str, Any]:
    """
    检查XML兼容性状态
    
    Returns:
        兼容性检查结果
    """
    result = {
        'status': 'ok',
        'issues': [],
        'recommendations': [],
        'parser_info': get_parser_info()
    }
    
    # 检查lxml可用性
    if not LXML_AVAILABLE:
        result['issues'].append('lxml不可用，某些高级功能可能受限')
        result['recommendations'].append('安装lxml以获得更好的性能和功能: pip install lxml')
    
    # 检查getparent支持
    test_element = ET.Element('test')
    if not hasattr(test_element, 'getparent'):
        result['issues'].append('当前ElementTree不支持getparent方法')
        result['recommendations'].append('使用XMLCompatibility.get_parent()方法替代')
    
    if result['issues']:
        result['status'] = 'warning'
    
    return result

if __name__ == "__main__":
    # 测试兼容性
    compat_result = check_compatibility()
    print("XML兼容性检查结果:")
    print(f"状态: {compat_result['status']}")
    if compat_result['issues']:
        print("问题:")
        for issue in compat_result['issues']:
            print(f"  - {issue}")
    if compat_result['recommendations']:
        print("建议:")
        for rec in compat_result['recommendations']:
            print(f"  - {rec}")
    print(f"解析器信息: {compat_result['parser_info']}") 