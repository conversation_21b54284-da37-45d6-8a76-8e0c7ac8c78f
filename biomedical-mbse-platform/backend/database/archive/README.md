# Archive - 备份内容

本目录包含已备份的非核心功能模块，这些功能虽然完整且有价值，但不属于UML 2.5核心映射的范围。

## 📁 备份内容

### 🔧 PostgreSQL特定扩展
**`postgresql_extensions/`**
- postgresql_components_demo.py - PostgreSQL高级组件演示  
- run_postgresql_demo.py - PostgreSQL功能综合演示
- 基于PostgreSQL特性如表继承、物化视图、分区表等的高级功能

### 🗂️ XMI文件处理
**`xmi_processing/`**
- xmi_dynamic_generator.py - XMI动态生成器
- xmi_demo_example.py - XMI处理演示
- XMI文件解析、模型版本管理、动态领域生成功能

### 🎭 演示程序
**`demos/`**
- uml_metaclass_demo.py - UML元类演示
- uml25_base_schema_demo.py - UML2.5 Schema演示
- 完整的功能演示和使用示例

### 🏗️ 特定领域实现
**`domain_implementations/`**
- uml_sysml_domain_implementation.py - UML/SysML领域实现
- 跨领域关系管理和特定建模语言支持

### 📄 核心工具文件
**独立工具模块**:
- **`uml_metaclass_generator.py`** - UML元类生成器 (38KB, 883行)
- **`uml_instance_parser.py`** - UML实例解析器 (32KB, 772行)  
- **`usage_example.py`** - 完整使用示例 (17KB, 469行)
- **`example_usage.py`** - 基础使用示例 (6.4KB, 176行)

## 📦 已移出的文件

以下文件已经从archive移出到主目录，成为核心功能的一部分：

### ⭐ 移动到主目录的核心模块

#### 1. **`performance_optimization.py`** → `../performance_optimization.py`
- **移动原因**: 性能优化是核心功能的重要组成部分
- **功能**: 智能索引策略、物化视图管理、性能基准测试
- **大小**: 33KB, 839行
- **移动时间**: 2025年1月

#### 2. **`postgresql_enhanced_metaclass_manager.py`** → `../postgresql_enhanced_metaclass_manager.py`  
- **移动原因**: PostgreSQL增强功能对系统性能至关重要
- **功能**: 表继承支持、系统目录集成、存储过程管理
- **大小**: 32KB, 747行
- **移动时间**: 2025年1月
- **原位置**: `postgresql_extensions/postgresql_enhanced_metaclass_manager.py`

### 🔄 最近迁移的内容

**已迁移到 `schemas/dynamic/`**:
- `schema_version_manager.py` → `../schemas/dynamic/schema_version_manager.py`
- `uml25_schema_generator.py` → `../schemas/dynamic/uml25_schema_generator.py`  
- `dynamic_schema_generator.py` → `../schemas/dynamic/dynamic_schema_generator.py`
- `dynamic_domain_example.py` → `../schemas/dynamic/dynamic_domain_example.py`

**独立测试和验证**:
- `tests/` 目录独立到 `../tests/`
- 用于代码结构验证和自动化测试生成

## 🎯 为什么备份这些内容

这些模块都是完整且功能强大的实现，但它们：

1. **超出UML核心范围** - 如PostgreSQL特定功能、XMI处理等
2. **属于特定技术实现** - 不是UML标准本身的一部分  
3. **是增值功能** - 提供额外价值但非UML映射必需
4. **特定场景使用** - 针对特殊需求的工具和实用程序

## 🔄 如何使用备份内容

如果需要使用这些功能：

1. **恢复到主目录**:
   ```bash
   # 恢复PostgreSQL扩展
   mv archive/postgresql_extensions ./
   
   # 恢复XMI处理
   mv archive/xmi_processing ./
   
   # 恢复特定工具
   mv archive/performance_optimization.py ./
   ```

2. **独立使用**:
   ```python
   # 从archive导入
   import sys
   sys.path.append('archive')
   from postgresql_extensions.postgresql_enhanced_metaclass_manager import create_postgresql_enhanced_manager
   from performance_optimization import PerformanceOptimizer
   from uml_metaclass_generator import UMLMetaclassGenerator
   ```

3. **参考现有迁移示例**:
   - 动态Schema文件的迁移方式可作为其他模块迁移的参考
   - 查看 `schemas/dynamic/` 目录了解集成方式

## 📚 相关文档

这些备份模块的详细文档仍在 `docs/` 目录中：
- PostgreSQL_Advanced_Features_Guide.md
- PostgreSQL_Advanced_Components_Guide.md  
- XMI_Dynamic_Generation_Guide.md
- UML_Metaclass_System_Guide.md

## 🧪 测试和验证

测试相关功能现在在独立的 `tests/` 目录中：
- **`tests/verify_structure.py`** - 项目结构验证
- **`tests/README.md`** - 测试文档和使用指南

## 📈 未来发展

### 潜在迁移候选
根据使用频率，以下模块可能未来会迁移到主项目：
- `performance_optimization.py` - 如果性能优化成为核心需求
- `uml_metaclass_generator.py` - 如果需要运行时元类生成

### 保留为工具集
以下模块适合保持为独立工具：
- PostgreSQL特定扩展
- XMI专用处理
- 特定领域实现

---

**这些内容虽被备份，但仍是高质量的企业级实现，可根据需要随时恢复使用。随着项目发展，部分核心功能可能会像动态Schema一样迁移到主项目结构中。** 