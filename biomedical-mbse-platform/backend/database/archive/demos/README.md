# Demos

本目录包含各种功能演示和示例程序。

## 📁 文件说明

### 演示程序
- **`uml_metaclass_demo.py`** - UML元类系统演示
  - 展示UML元类生成和管理
  - 演示Profile和Stereotype处理
  - 包含完整的XMI解析和实例创建流程

- **`uml25_base_schema_demo.py`** - UML2.5基础Schema演示
  - 演示UML2.5基础Schema的生成和使用
  - 展示继承映射和数据库表创建
  - 包含生物医学扩展示例

## 🎯 演示内容

### UML元类演示功能
- **元模型处理** - XMI解析和元类定义生成
- **Profile支持** - Stereotype应用和扩展机制
- **实例管理** - 模型实例的创建和查询
- **数据验证** - Schema验证和数据完整性检查

### Schema演示功能
- **继承映射** - UML继承关系到数据库的映射
- **动态生成** - 基于XMI的Schema自动生成
- **性能优化** - 索引和查询优化演示
- **扩展支持** - 生物医学等领域特定扩展

## 🚀 运行演示

```bash
# 运行UML元类演示
python uml_metaclass_demo.py

# 运行UML2.5 Schema演示
python uml25_base_schema_demo.py
```

## 📋 演示流程

### UML元类演示
1. **初始化** - 设置数据库连接和基础表结构
2. **元模型处理** - 解析示例UML元模型XMI
3. **Profile处理** - 处理UML Profile和Stereotype
4. **实例解析** - 解析模型实例数据
5. **数据查询** - 演示各种查询操作
6. **验证检查** - 验证生成的Schema和数据

### Schema演示
1. **XMI解析** - 解析UML2.5 XMI文件
2. **Schema生成** - 生成基础数据库Schema
3. **扩展演示** - 创建生物医学扩展
4. **数据测试** - 插入测试数据并验证
5. **查询演示** - 演示各种复杂查询
6. **性能测试** - 性能基准测试

## 📚 相关文档

这些演示程序是以下文档的实际应用：
- UML_Metaclass_System_Guide.md
- README_UML25_Base_Schema.md
- UML25_Inheritance_Design_Evaluation.md 