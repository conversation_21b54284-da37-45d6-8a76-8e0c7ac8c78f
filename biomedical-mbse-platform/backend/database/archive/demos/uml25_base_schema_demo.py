#!/usr/bin/env python3
"""
UML2.5基础Schema演示

完整演示从UML2.5.xmi解析到基础Schema生成，以及domain扩展的全流程。
这是@core解析 + @database生成的完整集成示例。

功能演示:
1. 解析UML2.5.xmi标准文件
2. 生成PostgreSQL基础Schema
3. 创建domain扩展机制
4. 演示生物医学domain扩展
5. 验证扩展能力

使用方法:
    python uml25_base_schema_demo.py [--setup] [--demo] [--extend]

作者: XML元数据系统开发团队
版本: 1.0.0
日期: 2025年1月1日
"""

import asyncio
import argparse
import logging
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import asyncpg
from datetime import datetime

# 导入解析器和生成器
sys.path.append(str(Path(__file__).parent.parent))

try:
    from core.parsing.uml25_xmi_parser import UML25XMIParser, parse_uml25_xmi
    from database.uml25_schema_generator import UML25SchemaGenerator, generate_uml25_schema
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保core和database模块在Python路径中")
    sys.exit(1)

logger = logging.getLogger(__name__)

class UML25BaseSchemaDemo:
    """UML2.5基础Schema演示"""
    
    def __init__(self):
        """初始化演示环境"""
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'biomedical_mbse_platform',
            'user': 'mbse_user',
            'password': 'mbse_pass_2024'
        }
        self.db_pool = None
        
        # 文件路径
        self.project_root = Path(__file__).parent.parent.parent
        self.xmi_file_path = self.project_root / "resources" / "domainXmi" / "UML2.5.xmi"
        
        # 演示状态
        self.xmi_result = None
        self.schema_result = None
        
        # 统计信息
        self.demo_stats = {
            'start_time': None,
            'end_time': None,
            'total_duration': 0,
            'phases': {},
            'errors': []
        }
    
    async def initialize(self):
        """初始化演示环境"""
        print("🚀 初始化UML2.5基础Schema演示环境...")
        self.demo_stats['start_time'] = datetime.now()
        
        # 检查文件存在
        if not self.xmi_file_path.exists():
            raise FileNotFoundError(f"UML2.5.xmi文件不存在: {self.xmi_file_path}")
        
        # 连接数据库
        database_url = f"postgresql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
        self.db_pool = await asyncpg.create_pool(database_url, min_size=2, max_size=10)
        
        print("✅ 演示环境初始化完成!")
        print(f"   📁 XMI文件: {self.xmi_file_path}")
        print(f"   🗄️ 数据库: {self.db_config['database']}")
    
    async def cleanup(self):
        """清理资源"""
        if self.db_pool:
            await self.db_pool.close()
        
        self.demo_stats['end_time'] = datetime.now()
        if self.demo_stats['start_time']:
            self.demo_stats['total_duration'] = (
                self.demo_stats['end_time'] - self.demo_stats['start_time']
            ).total_seconds()
    
    async def demo_complete_workflow(self):
        """演示完整工作流程"""
        print("\n🎯 UML2.5基础Schema完整工作流程演示")
        print("=" * 80)
        
        try:
            await self.initialize()
            
            # 阶段1: 解析UML2.5.xmi
            await self.phase1_parse_uml25_xmi()
            
            # 阶段2: 生成基础Schema
            await self.phase2_generate_base_schema()
            
            # 阶段3: 演示扩展机制
            await self.phase3_demonstrate_extensions()
            
            # 阶段4: 创建生物医学domain扩展
            await self.phase4_create_biomedical_extensions()
            
            # 阶段5: 验证和测试
            await self.phase5_validate_and_test()
            
            # 显示总结
            await self.show_demo_summary()
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            self.demo_stats['errors'].append(str(e))
            logger.exception("演示异常")
        
        finally:
            await self.cleanup()
    
    async def phase1_parse_uml25_xmi(self):
        """阶段1: 解析UML2.5.xmi文件"""
        print("\n📖 阶段1: 解析UML2.5.xmi标准文件")
        print("-" * 50)
        
        import time
        start_time = time.time()
        
        try:
            print(f"正在解析文件: {self.xmi_file_path}")
            print("⏳ 分析xmi:type定义和元类结构...")
            
            # 使用@core解析器解析XMI
            self.xmi_result = await parse_uml25_xmi(str(self.xmi_file_path))
            
            parse_time = time.time() - start_time
            self.demo_stats['phases']['parse'] = parse_time
            
            # 显示解析结果统计
            stats = self.xmi_result.get('data', {}).get('statistics', {})
            print(f"✅ UML2.5.xmi解析完成!")
            print(f"   📊 xmi:type分析统计:")
            print(f"     - 发现的元类类型: {stats.get('metaclasses_parsed', 0)}")
            print(f"     - 关联定义: {stats.get('associations_parsed', 0)}")
            print(f"     - 包结构: {stats.get('packages_parsed', 0)}")
            print(f"     - 属性定义: {stats.get('attributes_parsed', 0)}")
            print(f"     - 操作定义: {stats.get('operations_parsed', 0)}")
            print(f"     - 约束定义: {stats.get('constraints_parsed', 0)}")
            print(f"     - 解析时间: {parse_time:.2f}秒")
            
            # 显示发现的xmi:type示例
            metaclasses = self.xmi_result.get('data', {}).get('metaclasses', {})
            print(f"   🎯 发现的xmi:type类型 (前10个):")
            for i, (metaclass_id, mc) in enumerate(list(metaclasses.items())[:10]):
                qualified_name = mc.get('qualified_name', mc.get('name', 'Unknown'))
                occurrence_count = mc.get('documentation', '').split('出现')[-1].split('次')[0] if '出现' in mc.get('documentation', '') else '?'
                print(f"     {i+1}. {qualified_name} (出现{occurrence_count}次)")
            
        except Exception as e:
            print(f"❌ XMI解析失败: {e}")
            raise
    
    async def phase2_generate_base_schema(self):
        """阶段2: 生成基础Schema"""
        print("\n🗄️ 阶段2: 基于xmi:type生成PostgreSQL表结构")
        print("-" * 50)
        
        import time
        start_time = time.time()
        
        try:
            print("正在生成基于元类定义的表结构...")
            print("⏳ 为每个xmi:type创建对应的表...")
            
            # 使用@database生成器生成Schema
            self.schema_result = await generate_uml25_schema(self.db_pool, self.xmi_result)
            
            generation_time = time.time() - start_time
            self.demo_stats['phases']['generation'] = generation_time
            
            # 显示生成结果统计
            stats = self.schema_result.get('statistics', {})
            print(f"✅ 元类表结构生成完成!")
            print(f"   📊 生成统计:")
            print(f"     - 元类表数量: {stats.get('tables_generated', 0)}")
            print(f"     - 列定义数量: {stats.get('columns_generated', 0)}")
            print(f"     - 约束数量: {stats.get('constraints_generated', 0)}")
            print(f"     - 索引数量: {stats.get('indexes_generated', 0)}")
            print(f"     - 生成时间: {generation_time:.2f}秒")
            
            # 显示生成的元类表
            tables = self.schema_result.get('generated_tables', {})
            print(f"   🏗️ 生成的元类表 (前10个):")
            for i, (table_name, table_info) in enumerate(list(tables.items())[:10]):
                inheritance = table_info.get('inheritance', '无继承')
                inheritance_info = f" <- {inheritance.split('.')[-1]}" if inheritance else ""
                print(f"     {i+1}. {table_name} ({table_info['column_count']}列){inheritance_info}")
            
        except Exception as e:
            print(f"❌ 表结构生成失败: {e}")
            raise
    
    async def phase3_demonstrate_extensions(self):
        """阶段3: 演示扩展机制"""
        print("\n🔧 阶段3: 演示扩展机制")
        print("-" * 50)
        
        async with self.db_pool.acquire() as conn:
            try:
                print("演示基础扩展机制...")
                
                # 查询扩展支持表
                tables_result = await conn.fetch("""
                    SELECT t.table_name, 
                           COALESCE(pgd.description, '扩展支持表') as table_comment
                    FROM information_schema.tables t
                    LEFT JOIN pg_class pgc ON pgc.relname = t.table_name
                    LEFT JOIN pg_description pgd ON pgd.objoid = pgc.oid AND pgd.objsubid = 0
                    WHERE t.table_schema = 'uml25_base'
                    AND t.table_name LIKE '%extension%'
                    ORDER BY t.table_name
                """)
                
                print(f"✅ 扩展支持表:")
                for table in tables_result:
                    print(f"   - {table['table_name']}: {table['table_comment']}")
                
                # 查询扩展函数
                functions_result = await conn.fetch("""
                    SELECT routine_name, routine_definition
                    FROM information_schema.routines
                    WHERE routine_schema = 'uml25_base'
                    AND routine_name LIKE '%extension%'
                    ORDER BY routine_name
                """)
                
                print(f"✅ 扩展管理函数:")
                for func in functions_result:
                    print(f"   - {func['routine_name']}")
                
                # 测试注册函数
                print("\n🧪 测试扩展注册...")
                test_domain_id = await conn.fetchval("""
                    SELECT uml25_base.register_domain_extension(
                        'test_domain', 
                        'test_schema', 
                        'Element'
                    )
                """)
                print(f"   ✅ 注册测试域成功，ID: {test_domain_id}")
                
                # 清理测试数据
                await conn.execute("""
                    DELETE FROM uml25_base.domain_extensions 
                    WHERE domain_name = 'test_domain'
                """)
                print("   🧹 清理测试数据")
                
            except Exception as e:
                print(f"❌ 扩展机制演示失败: {e}")
                raise
    
    async def phase4_create_biomedical_extensions(self):
        """阶段4: 创建生物医学domain扩展"""
        print("\n🧬 阶段4: 创建生物医学Domain扩展")
        print("-" * 50)
        
        async with self.db_pool.acquire() as conn:
            try:
                print("正在创建生物医学domain扩展...")
                
                # 先检查是否已存在biomedical domain，如果存在则清理
                existing_id = await conn.fetchval("""
                    SELECT id FROM uml25_base.domain_extensions 
                    WHERE domain_name = 'biomedical'
                """)
                
                if existing_id:
                    print(f"   🧹 清理现有biomedical domain: {existing_id}")
                    await conn.execute("""
                        DELETE FROM uml25_base.extended_metaclasses 
                        WHERE domain_id = $1
                    """, existing_id)
                    await conn.execute("""
                        DELETE FROM uml25_base.domain_extensions 
                        WHERE id = $1
                    """, existing_id)
                
                # 注册生物医学domain（简化版）
                biomedical_domain_id = await conn.fetchval("""
                    INSERT INTO uml25_base.domain_extensions 
                    (domain_name, extension_schema)
                    VALUES ('biomedical', 'biomedical_domain')
                    RETURNING id
                """)
                print(f"   ✅ 注册生物医学domain，ID: {biomedical_domain_id}")
                
                # 2. 直接创建扩展表（不使用可能失败的函数）
                print("\n🏗️ 创建扩展表结构...")
                await self._create_biomedical_tables(conn)
                
                # 3. 插入扩展元类记录（简化版）
                biomedical_extensions = [
                    'BiologicalEntity',
                    'ClinicalEntity', 
                    'PharmaceuticalEntity'
                ]
                
                # 获取一个基础元类ID作为引用（如果需要的话）
                base_element_id = await conn.fetchval("""
                    INSERT INTO uml25_base.uml_element (name, element_type)
                    VALUES ('BaseClass', 'Class')
                    RETURNING id
                """)
                
                created_extensions = []
                for ext_name in biomedical_extensions:
                    ext_id = await conn.fetchval("""
                        INSERT INTO uml25_base.extended_metaclasses 
                        (domain_id, base_metaclass_id, extended_name, extension_properties, table_name)
                        VALUES ($1, $2, $3, '{}', $4)
                        RETURNING id
                    """, biomedical_domain_id, base_element_id, ext_name, f"biomedical_{ext_name.lower()}")
                    
                    created_extensions.append({
                        'id': ext_id,
                        'name': ext_name
                    })
                    print(f"   ✅ 创建扩展元类: {ext_name} (ID: {ext_id})")
                
                # 4. 验证扩展
                print("\n🔍 验证扩展创建...")
                extensions_count = await conn.fetchval("""
                    SELECT COUNT(*) FROM uml25_base.extended_metaclasses
                    WHERE domain_id = $1
                """, biomedical_domain_id)
                print(f"   ✅ 生物医学扩展元类数量: {extensions_count}")
                
            except Exception as e:
                print(f"❌ 生物医学扩展创建失败: {e}")
                raise
    
    async def _create_biomedical_tables(self, conn):
        """创建生物医学扩展表"""
        
        # 创建生物医学扩展schema
        await conn.execute("CREATE SCHEMA IF NOT EXISTS biomedical_domain")
        
        # 生物实体表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS biomedical_domain.biological_entities (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                name TEXT NOT NULL,
                biological_type TEXT,
                taxonomy_id TEXT,
                gene_ontology JSONB DEFAULT '{}',
                biological_properties JSONB DEFAULT '{}',
                base_element_id UUID REFERENCES uml25_base.uml_element(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 临床实体表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS biomedical_domain.clinical_entities (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                name TEXT NOT NULL,
                clinical_type TEXT,
                icd_code TEXT,
                clinical_significance JSONB DEFAULT '{}',
                patient_data JSONB DEFAULT '{}',
                base_element_id UUID REFERENCES uml25_base.uml_element(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 药物实体表
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS biomedical_domain.pharmaceutical_entities (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                name TEXT NOT NULL,
                drug_type TEXT,
                compound_id TEXT,
                molecular_formula TEXT,
                drug_properties JSONB DEFAULT '{}',
                base_element_id UUID REFERENCES uml25_base.uml_element(id),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("   ✅ 创建 biomedical_domain.biological_entities")
        print("   ✅ 创建 biomedical_domain.clinical_entities") 
        print("   ✅ 创建 biomedical_domain.pharmaceutical_entities")
    
    async def phase5_validate_and_test(self):
        """阶段5: 验证和测试"""
        print("\n✅ 阶段5: 验证和测试")
        print("-" * 50)
        
        async with self.db_pool.acquire() as conn:
            try:
                # 1. 验证基础Schema
                print("🔍 验证基础Schema...")
                base_tables = await conn.fetch("""
                    SELECT table_name, 
                           (SELECT COUNT(*) FROM information_schema.columns 
                            WHERE table_schema = 'uml25_base' AND table_name = t.table_name) as column_count
                    FROM information_schema.tables t
                    WHERE table_schema = 'uml25_base'
                    ORDER BY table_name
                """)
                
                print(f"   ✅ 基础表数量: {len(base_tables)}")
                for table in base_tables[:5]:  # 显示前5个
                    print(f"     - {table['table_name']} ({table['column_count']}列)")
                
                # 2. 验证扩展Schema
                print("\n🔍 验证扩展Schema...")
                extension_tables = await conn.fetch("""
                    SELECT table_name, 
                           (SELECT COUNT(*) FROM information_schema.columns 
                            WHERE table_schema = 'biomedical_domain' AND table_name = t.table_name) as column_count
                    FROM information_schema.tables t
                    WHERE table_schema = 'biomedical_domain'
                    ORDER BY table_name
                """)
                
                print(f"   ✅ 扩展表数量: {len(extension_tables)}")
                for table in extension_tables:
                    print(f"     - {table['table_name']} ({table['column_count']}列)")
                
                # 3. 插入测试数据
                print("\n🧪 插入测试数据...")
                await self._insert_test_data(conn)
                
                # 4. 执行查询测试
                print("\n🔎 执行查询测试...")
                await self._execute_query_tests(conn)
                
            except Exception as e:
                print(f"❌ 验证测试失败: {e}")
                raise
    
    async def _insert_test_data(self, conn: asyncpg.Connection):
        """插入测试数据"""
        
        # 清理可能存在的测试数据 - 按外键依赖顺序清理
        await conn.execute("""
            DELETE FROM biomedical_domain.biological_entities 
            WHERE base_element_id IN (
                SELECT id FROM uml25_base.uml_element WHERE xmi_id LIKE 'test_%'
            )
        """)
        
        await conn.execute("""
            DELETE FROM biomedical_domain.clinical_entities 
            WHERE base_element_id IN (
                SELECT id FROM uml25_base.uml_element WHERE xmi_id LIKE 'test_%'
            )
        """)
        
        await conn.execute("""
            DELETE FROM uml25_base.uml_element 
            WHERE xmi_id LIKE 'test_%'
        """)
        
        # 插入基础UML元素
        element_id = await conn.fetchval("""
            INSERT INTO uml25_base.uml_element 
            (xmi_id, name, qualified_name, element_type, package_path, documentation)
            VALUES ('test_gene_001', 'BRCA1', 'Gene::BRCA1', 'uml:Class', 'Genomics', 'BRCA1肿瘤抑制基因')
            RETURNING id
        """)
        
        # 插入生物实体测试数据
        await conn.execute("""
            INSERT INTO biomedical_domain.biological_entities 
            (name, base_element_id, biological_type, taxonomy_id, gene_ontology)
            VALUES ('BRCA1_Gene', $1, 'Gene', 'ENTREZ:672', 
                    '{"molecular_function": "DNA binding", "biological_process": "DNA repair"}'::jsonb)
        """, element_id)
        
        print("   ✅ 插入测试数据: BRCA1基因")
        
        # 插入临床实体
        clinical_element_id = await conn.fetchval("""
            INSERT INTO uml25_base.uml_element 
            (xmi_id, name, qualified_name, element_type, package_path, documentation)
            VALUES ('test_disease_001', 'BreastCancer', 'Disease::BreastCancer', 'uml:Class', 'Clinical', '乳腺癌疾病模型')
            RETURNING id
        """)
        
        await conn.execute("""
            INSERT INTO biomedical_domain.clinical_entities 
            (name, base_element_id, clinical_type, icd_code, clinical_significance)
            VALUES ('Breast_Cancer', $1, 'Disease', 'C50', 
                    '{"severity": "severe", "prevalence": "high", "treatment_options": ["surgery", "chemotherapy"]}'::jsonb)
        """, clinical_element_id)
        
        print("   ✅ 插入测试数据: 乳腺癌疾病模型")
        
        # 插入药物实体
        pharma_element_id = await conn.fetchval("""
            INSERT INTO uml25_base.uml_element 
            (xmi_id, name, qualified_name, element_type, package_path, documentation)
            VALUES ('test_drug_001', 'Tamoxifen', 'Drug::Tamoxifen', 'uml:Class', 'Pharmaceutical', 'Tamoxifen')
            RETURNING id
        """)
        
        await conn.execute("""
            INSERT INTO biomedical_domain.pharmaceutical_entities 
            (name, base_element_id, drug_type, compound_id, molecular_formula)
            VALUES ('Tamoxifen', $1, 'Therapeutic', 'CID_2733526', 'C26H29NO')
        """, pharma_element_id)
    
    async def _execute_query_tests(self, conn: asyncpg.Connection):
        """执行查询测试"""
        
        # 测试1: 基础元素查询
        elements = await conn.fetch("""
            SELECT name, qualified_name, element_type, package_path
            FROM uml25_base.uml_element
            WHERE xmi_id LIKE 'test_%'
            ORDER BY name
        """)
        
        print(f"   ✅ 基础元素查询: {len(elements)}个结果")
        for elem in elements:
            print(f"     - {elem['name']} ({elem['element_type']})")
        
        # 测试2: 生物实体扩展查询
        bio_entities = await conn.fetch("""
            SELECT e.name, b.biological_type, b.taxonomy_id, b.gene_ontology
            FROM uml25_base.uml_element e
            JOIN biomedical_domain.biological_entities b ON e.id = b.base_element_id
        """)
        
        print(f"   ✅ 生物实体查询: {len(bio_entities)}个结果")
        for bio in bio_entities:
            print(f"     - {bio['name']}: {bio['biological_type']} (Taxonomy: {bio['taxonomy_id']})")
        
        # 测试3: 临床实体扩展查询
        clinical_entities = await conn.fetch("""
            SELECT e.name, c.clinical_type, c.icd_code, c.clinical_significance
            FROM uml25_base.uml_element e
            JOIN biomedical_domain.clinical_entities c ON e.id = c.base_element_id
        """)
        
        print(f"   ✅ 临床实体查询: {len(clinical_entities)}个结果")
        for clinical in clinical_entities:
            print(f"     - {clinical['name']}: {clinical['clinical_type']} (ICD: {clinical['icd_code']})")
        
        # 测试4: 联合查询（基础+扩展）
        combined_query = await conn.fetch("""
            SELECT 
                e.name,
                e.element_type,
                e.package_path,
                CASE 
                    WHEN b.id IS NOT NULL THEN 'Biological'
                    WHEN c.id IS NOT NULL THEN 'Clinical'
                    ELSE 'Basic'
                END as extension_type
            FROM uml25_base.uml_element e
            LEFT JOIN biomedical_domain.biological_entities b ON e.id = b.base_element_id
            LEFT JOIN biomedical_domain.clinical_entities c ON e.id = c.base_element_id
            WHERE e.xmi_id LIKE 'test_%'
            ORDER BY e.name
        """)
        
        print(f"   ✅ 联合查询测试: {len(combined_query)}个结果")
        for result in combined_query:
            print(f"     - {result['name']}: {result['extension_type']}扩展")
    
    async def show_demo_summary(self):
        """显示演示总结"""
        print("\n🎉 UML2.5基础Schema演示总结")
        print("=" * 80)
        
        # 显示时间统计
        phases = self.demo_stats.get('phases', {})
        total_time = self.demo_stats.get('total_duration', 0)
        
        print(f"⏱️ 时间统计:")
        print(f"   - XMI解析时间: {phases.get('parse', 0):.2f}秒")
        print(f"   - Schema生成时间: {phases.get('generation', 0):.2f}秒")
        print(f"   - 总演示时间: {total_time:.2f}秒")
        
        # 显示功能成果
        if self.xmi_result and self.schema_result:
            xmi_stats = self.xmi_result.get('data', {}).get('statistics', {})
            schema_stats = self.schema_result.get('statistics', {})
            
            print(f"\n📊 功能成果:")
            print(f"   📖 XMI解析:")
            print(f"     - 解析元类: {xmi_stats.get('metaclasses_parsed', 0)}个")
            print(f"     - 解析关联: {xmi_stats.get('associations_parsed', 0)}个")
            print(f"     - 解析属性: {xmi_stats.get('attributes_parsed', 0)}个")
            
            print(f"   🗄️ Schema生成:")
            print(f"     - 生成表: {schema_stats.get('tables_generated', 0)}个")
            print(f"     - 生成列: {schema_stats.get('columns_generated', 0)}个")
            print(f"     - 生成约束: {schema_stats.get('constraints_generated', 0)}个")
            print(f"     - 生成索引: {schema_stats.get('indexes_generated', 0)}个")
        
        # 显示架构价值
        print(f"\n🏗️ 架构价值:")
        print(f"   ✅ 标准化基础: 基于OMG UML2.5标准建立统一基础")
        print(f"   ✅ 扩展机制: 支持domain-specific扩展")
        print(f"   ✅ 类型安全: PostgreSQL强类型约束保证")
        print(f"   ✅ 性能优化: 完整索引和查询优化")
        print(f"   ✅ 可维护性: 清晰的继承和扩展结构")
        
        # 显示使用指南
        print(f"\n📋 使用指南:")
        print(f"   1. 基础Schema: uml25_base.*")
        print(f"   2. 注册domain: SELECT uml25_base.register_domain_extension('domain', 'schema')")
        print(f"   3. 扩展元类: SELECT uml25_base.create_extended_metaclass('domain', 'base', 'extended')")
        print(f"   4. 生物医学扩展: biomedical_domain.*")
        
        # 显示错误（如果有）
        errors = self.demo_stats.get('errors', [])
        if errors:
            print(f"\n⚠️ 演示过程中的警告/错误:")
            for error in errors:
                print(f"   - {error}")
        
        print(f"\n💡 这个系统为生物医学MBSE平台提供了强大的元类管理基础!")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='UML2.5基础Schema演示')
    parser.add_argument('--setup', action='store_true', help='仅执行环境设置')
    parser.add_argument('--demo', action='store_true', help='执行完整演示')
    parser.add_argument('--extend', action='store_true', help='仅演示扩展功能')
    parser.add_argument('--debug', action='store_true', help='启用调试日志')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    demo = UML25BaseSchemaDemo()
    
    async def run_demo():
        try:
            if args.setup:
                await demo.initialize()
                print("✅ 环境设置完成")
            elif args.extend:
                await demo.initialize()
                await demo.phase3_demonstrate_extensions()
                await demo.phase4_create_biomedical_extensions()
                print("✅ 扩展演示完成")
            else:
                # 默认执行完整演示
                await demo.demo_complete_workflow()
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断演示")
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            if args.debug:
                logger.exception("详细错误信息")
            sys.exit(1)
    
    # 运行演示
    asyncio.run(run_demo())

if __name__ == "__main__":
    main() 