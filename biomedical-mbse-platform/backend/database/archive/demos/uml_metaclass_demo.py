"""
UML元类动态生成完整演示

展示基于核心模块的UML元类动态生成和实例解析完整工作流程：
1. 解析UML元模型定义，动态生成Schema
2. 解析UML Profile定义，生成扩展Schema
3. 解析工程XMI文件，生成模型实例数据
4. 演示查询和关系分析功能

基于core模块的解析实现，展现从元模型到实例的完整数据流
"""

import asyncio
import json
import logging
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Any
import uuid
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncpg

from uml_metaclass_generator import UMLMetaclassGenerator, create_uml_metaclass_generator
from uml_instance_parser import UMLInstanceParser, create_uml_instance_parser

logger = logging.getLogger(__name__)

class UMLMetaclassDemo:
    """UML元类完整演示类"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'biomedical_mbse_platform',
            'user': 'mbse_user',
            'password': 'mbse_pass_2024'
        }
        self.metaclass_generator = None
        self.instance_parser = None
        self.db_pool = None
    
    async def initialize(self):
        """初始化演示环境"""
        print("🚀 初始化UML元类动态生成演示环境...")
        
        # 创建数据库连接池
        database_url = f"postgresql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
        self.db_pool = await asyncpg.create_pool(database_url)
        
        # 初始化生成器和解析器
        self.metaclass_generator = await create_uml_metaclass_generator(self.db_pool)
        self.instance_parser = await create_uml_instance_parser(self.db_pool)
        
        print("✅ UML元类动态生成环境初始化完成！")
    
    def generate_sample_metamodel_xmi(self) -> str:
        """生成示例UML元模型XMI"""
        print("\n📄 生成示例UML元模型定义...")
        
        # 创建根元素 - 这是一个元模型定义文件
        root = ET.Element("xmi:XMI")
        root.set("xmi:version", "2.5")
        root.set("xmlns:xmi", "http://www.omg.org/XMI")
        root.set("xmlns:ecore", "http://www.eclipse.org/emf/2002/Ecore")
        root.set("xmlns:uml", "http://www.eclipse.org/uml2/5.0.0/UML")
        
        # 添加元模型包
        metamodel = ET.SubElement(root, "ecore:EPackage")
        metamodel.set("xmi:id", "BiomedicineMetamodel")
        metamodel.set("name", "BiomedicineMetamodel")
        metamodel.set("nsURI", "http://biomedical.org/metamodel")
        metamodel.set("nsPrefix", "biomed")
        
        # 定义生物医学专用的元类
        
        # 1. 患者元类 (扩展UML Class)
        patient_metaclass = ET.SubElement(metamodel, "eClassifiers")
        patient_metaclass.set("xmi:type", "ecore:EClass")
        patient_metaclass.set("xmi:id", "PatientMetaclass")
        patient_metaclass.set("name", "Patient")
        patient_metaclass.set("instanceClassName", "biomedical.Patient")
        
        # 患者元类属性
        patient_id_attr = ET.SubElement(patient_metaclass, "eStructuralFeatures")
        patient_id_attr.set("xmi:type", "ecore:EAttribute")
        patient_id_attr.set("xmi:id", "PatientID")
        patient_id_attr.set("name", "patientId")
        patient_id_attr.set("eType", "ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EString")
        patient_id_attr.set("lowerBound", "1")
        
        age_attr = ET.SubElement(patient_metaclass, "eStructuralFeatures")
        age_attr.set("xmi:type", "ecore:EAttribute")
        age_attr.set("xmi:id", "Age")
        age_attr.set("name", "age")
        age_attr.set("eType", "ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EInt")
        
        # 2. 治疗元类
        treatment_metaclass = ET.SubElement(metamodel, "eClassifiers")
        treatment_metaclass.set("xmi:type", "ecore:EClass")
        treatment_metaclass.set("xmi:id", "TreatmentMetaclass")
        treatment_metaclass.set("name", "Treatment")
        treatment_metaclass.set("instanceClassName", "biomedical.Treatment")
        
        # 治疗元类属性
        treatment_id_attr = ET.SubElement(treatment_metaclass, "eStructuralFeatures")
        treatment_id_attr.set("xmi:type", "ecore:EAttribute")
        treatment_id_attr.set("xmi:id", "TreatmentID")
        treatment_id_attr.set("name", "treatmentId")
        treatment_id_attr.set("eType", "ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EString")
        
        protocol_attr = ET.SubElement(treatment_metaclass, "eStructuralFeatures")
        protocol_attr.set("xmi:type", "ecore:EAttribute")
        protocol_attr.set("xmi:id", "Protocol")
        protocol_attr.set("name", "protocol")
        protocol_attr.set("eType", "ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EString")
        
        # 3. 药物元类
        drug_metaclass = ET.SubElement(metamodel, "eClassifiers")
        drug_metaclass.set("xmi:type", "ecore:EClass")
        drug_metaclass.set("xmi:id", "DrugMetaclass")
        drug_metaclass.set("name", "Drug")
        drug_metaclass.set("instanceClassName", "biomedical.Drug")
        
        # 药物元类属性
        drug_name_attr = ET.SubElement(drug_metaclass, "eStructuralFeatures")
        drug_name_attr.set("xmi:type", "ecore:EAttribute")
        drug_name_attr.set("xmi:id", "DrugName")
        drug_name_attr.set("name", "drugName")
        drug_name_attr.set("eType", "ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EString")
        
        dosage_attr = ET.SubElement(drug_metaclass, "eStructuralFeatures")
        dosage_attr.set("xmi:type", "ecore:EAttribute")
        dosage_attr.set("xmi:id", "Dosage")
        dosage_attr.set("name", "dosage")
        dosage_attr.set("eType", "ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EString")
        
        # 4. 关联元类：患者-治疗关系
        patient_treatment_ref = ET.SubElement(treatment_metaclass, "eStructuralFeatures")
        patient_treatment_ref.set("xmi:type", "ecore:EReference")
        patient_treatment_ref.set("xmi:id", "TreatmentToPatient")
        patient_treatment_ref.set("name", "patient")
        patient_treatment_ref.set("eType", "#//PatientMetaclass")
        patient_treatment_ref.set("lowerBound", "1")
        
        # 格式化XML
        ET.indent(root, space="  ")
        xml_content = ET.tostring(root, encoding='unicode', xml_declaration=True)
        
        print("✅ UML元模型XMI生成完成")
        return xml_content
    
    def generate_sample_profile_xmi(self) -> str:
        """生成示例UML Profile XMI"""
        print("\n📄 生成示例生物医学Profile定义...")
        
        # 创建根元素
        root = ET.Element("xmi:XMI")
        root.set("xmi:version", "2.5")
        root.set("xmlns:xmi", "http://www.omg.org/XMI")
        root.set("xmlns:uml", "http://www.eclipse.org/uml2/5.0.0/UML")
        root.set("xmlns:biomed", "http://biomedical.org/profile")
        
        # 创建Profile
        profile = ET.SubElement(root, "uml:Profile")
        profile.set("xmi:id", "BiomedicineProfile")
        profile.set("name", "BiomedicineProfile")
        profile.set("URI", "http://biomedical.org/profile")
        profile.set("metamodelReference", "http://www.eclipse.org/uml2/5.0.0/UML")
        
        # 导入UML元模型
        import_elem = ET.SubElement(profile, "elementImport")
        import_elem.set("xmi:id", "UMLImport")
        import_elem.set("importedElement", "http://www.eclipse.org/uml2/5.0.0/UML#Class")
        
        # 1. 临床实体Stereotype
        clinical_entity_stereotype = ET.SubElement(profile, "ownedStereotype")
        clinical_entity_stereotype.set("xmi:type", "uml:Stereotype")
        clinical_entity_stereotype.set("xmi:id", "ClinicalEntity")
        clinical_entity_stereotype.set("name", "ClinicalEntity")
        clinical_entity_stereotype.set("isAbstract", "true")
        
        # ClinicalEntity扩展Class
        clinical_extension = ET.SubElement(clinical_entity_stereotype, "ownedEnd")
        clinical_extension.set("xmi:type", "uml:ExtensionEnd")
        clinical_extension.set("xmi:id", "ClinicalEntityExtension")
        clinical_extension.set("name", "extension_ClinicalEntity")
        clinical_extension.set("type", "#ClinicalEntityExtension")
        clinical_extension.set("aggregation", "composite")
        
        # 2. 患者Stereotype (继承ClinicalEntity)
        patient_stereotype = ET.SubElement(profile, "ownedStereotype")
        patient_stereotype.set("xmi:type", "uml:Stereotype")
        patient_stereotype.set("xmi:id", "Patient")
        patient_stereotype.set("name", "Patient")
        patient_stereotype.set("generalization", "#ClinicalEntity")
        
        # Patient的Tagged Values
        patient_id_tag = ET.SubElement(patient_stereotype, "ownedAttribute")
        patient_id_tag.set("xmi:id", "PatientIdTag")
        patient_id_tag.set("name", "patientId")
        patient_id_tag.set("type", "String")
        patient_id_tag.set("multiplicity", "1")
        
        medical_record_tag = ET.SubElement(patient_stereotype, "ownedAttribute")
        medical_record_tag.set("xmi:id", "MedicalRecordTag")
        medical_record_tag.set("name", "medicalRecordNumber")
        medical_record_tag.set("type", "String")
        
        birth_date_tag = ET.SubElement(patient_stereotype, "ownedAttribute")
        birth_date_tag.set("xmi:id", "BirthDateTag")
        birth_date_tag.set("name", "birthDate")
        birth_date_tag.set("type", "Date")
        
        # Patient扩展Class
        patient_extension = ET.SubElement(patient_stereotype, "ownedEnd")
        patient_extension.set("xmi:type", "uml:ExtensionEnd")
        patient_extension.set("xmi:id", "PatientExtension")
        patient_extension.set("name", "extension_Patient")
        patient_extension.set("type", "#PatientExtension")
        patient_extension.set("aggregation", "composite")
        
        # 3. 治疗方案Stereotype
        treatment_stereotype = ET.SubElement(profile, "ownedStereotype")
        treatment_stereotype.set("xmi:type", "uml:Stereotype")
        treatment_stereotype.set("xmi:id", "TreatmentPlan")
        treatment_stereotype.set("name", "TreatmentPlan")
        
        # TreatmentPlan的Tagged Values
        protocol_tag = ET.SubElement(treatment_stereotype, "ownedAttribute")
        protocol_tag.set("xmi:id", "ProtocolTag")
        protocol_tag.set("name", "protocol")
        protocol_tag.set("type", "String")
        protocol_tag.set("multiplicity", "1")
        
        duration_tag = ET.SubElement(treatment_stereotype, "ownedAttribute")
        duration_tag.set("xmi:id", "DurationTag")
        duration_tag.set("name", "duration")
        duration_tag.set("type", "Integer")
        duration_tag.set("defaultValue", "7")
        
        efficacy_tag = ET.SubElement(treatment_stereotype, "ownedAttribute")
        efficacy_tag.set("xmi:id", "EfficacyTag")
        efficacy_tag.set("name", "efficacyRate")
        efficacy_tag.set("type", "Real")
        
        # TreatmentPlan扩展Activity
        treatment_extension = ET.SubElement(treatment_stereotype, "ownedEnd")
        treatment_extension.set("xmi:type", "uml:ExtensionEnd")
        treatment_extension.set("xmi:id", "TreatmentExtension")
        treatment_extension.set("name", "extension_TreatmentPlan")
        treatment_extension.set("type", "#TreatmentExtension")
        treatment_extension.set("aggregation", "composite")
        
        # 4. 药物Stereotype
        drug_stereotype = ET.SubElement(profile, "ownedStereotype")
        drug_stereotype.set("xmi:type", "uml:Stereotype")
        drug_stereotype.set("xmi:id", "Drug")
        drug_stereotype.set("name", "Drug")
        
        # Drug的Tagged Values
        active_ingredient_tag = ET.SubElement(drug_stereotype, "ownedAttribute")
        active_ingredient_tag.set("xmi:id", "ActiveIngredientTag")
        active_ingredient_tag.set("name", "activeIngredient")
        active_ingredient_tag.set("type", "String")
        active_ingredient_tag.set("multiplicity", "1")
        
        dosage_tag = ET.SubElement(drug_stereotype, "ownedAttribute")
        dosage_tag.set("xmi:id", "DosageTag")
        dosage_tag.set("name", "dosage")
        dosage_tag.set("type", "String")
        
        side_effects_tag = ET.SubElement(drug_stereotype, "ownedAttribute")
        side_effects_tag.set("xmi:id", "SideEffectsTag")
        side_effects_tag.set("name", "sideEffects")
        side_effects_tag.set("type", "String")
        side_effects_tag.set("multiplicity", "0..*")
        
        # Drug扩展Class
        drug_extension = ET.SubElement(drug_stereotype, "ownedEnd")
        drug_extension.set("xmi:type", "uml:ExtensionEnd")
        drug_extension.set("xmi:id", "DrugExtension")
        drug_extension.set("name", "extension_Drug")
        drug_extension.set("type", "#DrugExtension")
        drug_extension.set("aggregation", "composite")
        
        # 格式化XML
        ET.indent(root, space="  ")
        xml_content = ET.tostring(root, encoding='unicode', xml_declaration=True)
        
        print("✅ 生物医学Profile XMI生成完成")
        return xml_content
    
    def generate_sample_model_xmi(self) -> str:
        """生成示例模型实例XMI"""
        print("\n📄 生成示例模型实例XMI...")
        
        # 创建根元素
        root = ET.Element("xmi:XMI")
        root.set("xmi:version", "2.5")
        root.set("xmlns:xmi", "http://www.omg.org/XMI")
        root.set("xmlns:uml", "http://www.eclipse.org/uml2/5.0.0/UML")
        root.set("xmlns:biomed", "http://biomedical.org/profile")
        
        # 创建模型
        model = ET.SubElement(root, "uml:Model")
        model.set("xmi:id", "BiomedicineModel")
        model.set("name", "生物医学治疗模型")
        
        # 应用Profile
        profile_application = ET.SubElement(model, "profileApplication")
        profile_application.set("xmi:id", "ProfileApp")
        applied_profile = ET.SubElement(profile_application, "appliedProfile")
        applied_profile.set("href", "BiomedicineProfile.uml#BiomedicineProfile")
        
        # 创建包结构
        patients_package = ET.SubElement(model, "packagedElement")
        patients_package.set("xmi:type", "uml:Package")
        patients_package.set("xmi:id", "PatientsPackage")
        patients_package.set("name", "Patients")
        
        treatments_package = ET.SubElement(model, "packagedElement")
        treatments_package.set("xmi:type", "uml:Package")
        treatments_package.set("xmi:id", "TreatmentsPackage")
        treatments_package.set("name", "Treatments")
        
        drugs_package = ET.SubElement(model, "packagedElement")
        drugs_package.set("xmi:type", "uml:Package")
        drugs_package.set("xmi:id", "DrugsPackage")
        drugs_package.set("name", "Drugs")
        
        # 创建患者实例
        patient1_class = ET.SubElement(patients_package, "packagedElement")
        patient1_class.set("xmi:type", "uml:Class")
        patient1_class.set("xmi:id", "Patient001")
        patient1_class.set("name", "Zhang_Wei")
        patient1_class.set("visibility", "public")
        
        patient2_class = ET.SubElement(patients_package, "packagedElement")
        patient2_class.set("xmi:type", "uml:Class")
        patient2_class.set("xmi:id", "Patient002")
        patient2_class.set("name", "Li_Ming")
        patient2_class.set("visibility", "public")
        
        # 创建治疗方案实例
        treatment1_activity = ET.SubElement(treatments_package, "packagedElement")
        treatment1_activity.set("xmi:type", "uml:Activity")
        treatment1_activity.set("xmi:id", "Treatment001")
        treatment1_activity.set("name", "ChemotherapyProtocol_A")
        
        treatment2_activity = ET.SubElement(treatments_package, "packagedElement")
        treatment2_activity.set("xmi:type", "uml:Activity")
        treatment2_activity.set("xmi:id", "Treatment002")
        treatment2_activity.set("name", "RadiationTherapy_B")
        
        # 创建药物实例
        drug1_class = ET.SubElement(drugs_package, "packagedElement")
        drug1_class.set("xmi:type", "uml:Class")
        drug1_class.set("xmi:id", "Drug001")
        drug1_class.set("name", "Doxorubicin")
        drug1_class.set("visibility", "public")
        
        drug2_class = ET.SubElement(drugs_package, "packagedElement")
        drug2_class.set("xmi:type", "uml:Class")
        drug2_class.set("xmi:id", "Drug002")
        drug2_class.set("name", "Cisplatin")
        drug2_class.set("visibility", "public")
        
        # 添加Stereotype应用
        
        # Patient Stereotype应用
        patient1_stereotype = ET.SubElement(root, "biomed:Patient")
        patient1_stereotype.set("xmi:id", "Patient001_Stereotype")
        patient1_stereotype.set("base_Class", "#Patient001")
        patient1_stereotype.set("patientId", "P001")
        patient1_stereotype.set("medicalRecordNumber", "MR2024001")
        patient1_stereotype.set("birthDate", "1985-03-15")
        
        patient2_stereotype = ET.SubElement(root, "biomed:Patient")
        patient2_stereotype.set("xmi:id", "Patient002_Stereotype")
        patient2_stereotype.set("base_Class", "#Patient002")
        patient2_stereotype.set("patientId", "P002")
        patient2_stereotype.set("medicalRecordNumber", "MR2024002")
        patient2_stereotype.set("birthDate", "1978-07-22")
        
        # TreatmentPlan Stereotype应用
        treatment1_stereotype = ET.SubElement(root, "biomed:TreatmentPlan")
        treatment1_stereotype.set("xmi:id", "Treatment001_Stereotype")
        treatment1_stereotype.set("base_Activity", "#Treatment001")
        treatment1_stereotype.set("protocol", "CHOP")
        treatment1_stereotype.set("duration", "21")
        treatment1_stereotype.set("efficacyRate", "0.75")
        
        treatment2_stereotype = ET.SubElement(root, "biomed:TreatmentPlan")
        treatment2_stereotype.set("xmi:id", "Treatment002_Stereotype")
        treatment2_stereotype.set("base_Activity", "#Treatment002")
        treatment2_stereotype.set("protocol", "IMRT")
        treatment2_stereotype.set("duration", "35")
        treatment2_stereotype.set("efficacyRate", "0.82")
        
        # Drug Stereotype应用
        drug1_stereotype = ET.SubElement(root, "biomed:Drug")
        drug1_stereotype.set("xmi:id", "Drug001_Stereotype")
        drug1_stereotype.set("base_Class", "#Drug001")
        drug1_stereotype.set("activeIngredient", "Doxorubicin HCl")
        drug1_stereotype.set("dosage", "50mg/m²")
        drug1_stereotype.set("sideEffects", "Cardiotoxicity,Nausea,Hair loss")
        
        drug2_stereotype = ET.SubElement(root, "biomed:Drug")
        drug2_stereotype.set("xmi:id", "Drug002_Stereotype")
        drug2_stereotype.set("base_Class", "#Drug002")
        drug2_stereotype.set("activeIngredient", "Cisplatin")
        drug2_stereotype.set("dosage", "75mg/m²")
        drug2_stereotype.set("sideEffects", "Nephrotoxicity,Ototoxicity,Neuropathy")
        
        # 格式化XML
        ET.indent(root, space="  ")
        xml_content = ET.tostring(root, encoding='unicode', xml_declaration=True)
        
        print("✅ 模型实例XMI生成完成")
        return xml_content
    
    async def demo_metaclass_processing(self):
        """演示元模型处理流程"""
        print("\n🎯 演示UML元模型处理流程")
        print("=" * 50)
        
        # 生成示例元模型XMI
        metamodel_xmi = self.generate_sample_metamodel_xmi()
        
        # 处理元模型定义
        success, message, result = await self.metaclass_generator.process_metamodel_xmi(
            metamodel_xmi, 'BiomedicineMetamodel', is_profile=False
        )
        
        if success:
            print(f"✅ 元模型处理成功！")
            print(f"   📝 模型名称: BiomedicineMetamodel")
            print(f"   🔢 生成Schema数: {result['schemas_generated']}")
            print(f"   📋 Schema列表: {', '.join(result['schema_details'])}")
            return True
        else:
            print(f"❌ 元模型处理失败: {message}")
            return False
    
    async def demo_profile_processing(self):
        """演示Profile处理流程"""
        print("\n🎯 演示UML Profile处理流程")
        print("=" * 50)
        
        # 生成示例Profile XMI
        profile_xmi = self.generate_sample_profile_xmi()
        
        # 处理Profile定义
        success, message, result = await self.metaclass_generator.process_metamodel_xmi(
            profile_xmi, 'BiomedicineProfile', is_profile=True
        )
        
        if success:
            print(f"✅ Profile处理成功！")
            print(f"   📝 Profile名称: BiomedicineProfile")
            print(f"   🔢 生成Schema数: {result['schemas_generated']}")
            print(f"   📋 Schema列表: {', '.join(result['schema_details'])}")
            return True
        else:
            print(f"❌ Profile处理失败: {message}")
            return False
    
    async def demo_instance_parsing(self):
        """演示模型实例解析流程"""
        print("\n🎯 演示模型实例解析流程")
        print("=" * 50)
        
        # 生成示例模型实例XMI
        model_xmi = self.generate_sample_model_xmi()
        
        # 解析模型实例
        result = await self.instance_parser.parse_model_xmi(
            model_xmi, 'BiomedicineModel', 'demo_model.xmi', '1.0'
        )
        
        if result.success:
            print(f"✅ 模型实例解析成功！")
            print(f"   📝 模型名称: {result.model_name}")
            print(f"   🔢 创建实例数: {result.instances_created}")
            print(f"   🏷️ Stereotype应用数: {result.stereotypes_applied}")
            print(f"   🔗 建立关系数: {result.relationships_established}")
            print(f"   📊 实例摘要:")
            for instance_type, count in result.instance_summary.items():
                print(f"      - {instance_type}: {count}个")
            return True
        else:
            print(f"❌ 模型实例解析失败:")
            for error in result.errors:
                print(f"      - {error}")
            return False
    
    async def demo_data_query(self):
        """演示数据查询功能"""
        print("\n🎯 演示数据查询功能")
        print("=" * 50)
        
        # 查询元类定义
        print("📋 查询已注册的元类定义:")
        metaclasses = await self.metaclass_generator.get_metaclass_definitions()
        for metaclass in metaclasses:
            print(f"   - {metaclass['name']} ({metaclass['metaclass_type']})")
        
        # 查询Profile定义
        print(f"\n🏷️ 查询已注册的Profile定义:")
        profiles = await self.metaclass_generator.get_profile_definitions()
        for profile in profiles:
            print(f"   - {profile['name']} (URI: {profile['uri']}, Stereotypes: {profile['stereotype_count']})")
        
        # 查询生成的Schema
        print(f"\n🏗️ 查询生成的Schema:")
        schemas = await self.metaclass_generator.get_generated_schemas()
        for schema in schemas:
            schema_type = "Profile" if schema['stereotype_name'] else "Metaclass"
            print(f"   - {schema['type_name']} ({schema_type}, Table: {schema['table_name']})")
        
        # 查询模型实例
        print(f"\n🎭 查询模型实例:")
        instances = await self.instance_parser.get_model_instances('BiomedicineModel')
        for instance in instances[:5]:  # 只显示前5个
            custom_attrs = json.loads(instance['custom_attributes']) if instance['custom_attributes'] else {}
            stereotype = custom_attrs.get('stereotype_name', 'None')
            print(f"   - {instance['name']} ({instance['element_type']}, Stereotype: {stereotype})")
        
        if len(instances) > 5:
            print(f"   ... 和其他 {len(instances) - 5} 个实例")
        
        # 查询Stereotype应用
        print(f"\n🏷️ 查询Stereotype应用:")
        applications = await self.instance_parser.get_stereotype_applications()
        for app in applications[:5]:  # 只显示前5个
            tagged_values = json.loads(app['tagged_values']) if app['tagged_values'] else {}
            print(f"   - {app['stereotype_name']} 应用到 {app['base_element_id']}")
            for tag, value in tagged_values.items():
                print(f"     * {tag}: {value}")
        
        if len(applications) > 5:
            print(f"   ... 和其他 {len(applications) - 5} 个应用")
    
    async def demo_schema_verification(self):
        """演示Schema验证"""
        print("\n🎯 演示Schema验证")
        print("=" * 50)
        
        async with self.db_pool.acquire() as conn:
            # 查看生成的数据库Schema
            schemas = await conn.fetch("""
                SELECT schema_name FROM information_schema.schemata 
                WHERE schema_name LIKE '%biomed%' OR schema_name LIKE '%profile%'
                ORDER BY schema_name
            """)
            
            print("🗃️ 生成的数据库Schema:")
            for schema in schemas:
                print(f"   - {schema['schema_name']}")
                
                # 查看Schema中的表
                tables = await conn.fetch("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = $1
                    ORDER BY table_name
                """, schema['schema_name'])
                
                for table in tables:
                    print(f"     └── {table['table_name']}")
            
            # 检查核心管理表
            print("\n📊 核心管理表状态:")
            
            # 元类定义表
            metaclass_count = await conn.fetchval(
                "SELECT COUNT(*) FROM core_schema.uml_metaclass_definitions"
            )
            print(f"   - UML元类定义: {metaclass_count} 个")
            
            # Profile定义表
            profile_count = await conn.fetchval(
                "SELECT COUNT(*) FROM core_schema.uml_profile_definitions"
            )
            print(f"   - Profile定义: {profile_count} 个")
            
            # Stereotype定义表
            stereotype_count = await conn.fetchval(
                "SELECT COUNT(*) FROM core_schema.uml_stereotype_definitions"
            )
            print(f"   - Stereotype定义: {stereotype_count} 个")
            
            # 生成的Schema记录表
            generated_schema_count = await conn.fetchval(
                "SELECT COUNT(*) FROM core_schema.generated_element_schemas"
            )
            print(f"   - 生成的Schema记录: {generated_schema_count} 个")
            
            # Element元数据表
            element_count = await conn.fetchval(
                "SELECT COUNT(*) FROM core_schema.element_metadata"
            )
            print(f"   - Element实例: {element_count} 个")
            
            # Stereotype应用表
            application_count = await conn.fetchval(
                "SELECT COUNT(*) FROM core_schema.stereotype_applications"
            )
            print(f"   - Stereotype应用: {application_count} 个")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        print("🎉 UML元类动态生成完整演示")
        print("=" * 60)
        
        try:
            # 初始化
            await self.initialize()
            
            # 1. 处理元模型定义
            metaclass_success = await self.demo_metaclass_processing()
            
            # 2. 处理Profile定义
            profile_success = await self.demo_profile_processing()
            
            # 3. 解析模型实例（只有在前面成功时才继续）
            if metaclass_success and profile_success:
                instance_success = await self.demo_instance_parsing()
                
                # 4. 数据查询演示
                if instance_success:
                    await self.demo_data_query()
                    
                    # 5. Schema验证
                    await self.demo_schema_verification()
            
            print("\n🎉 完整演示完成！")
            print("✅ 展现了从UML元模型定义到模型实例的完整数据流")
            print("✅ 基于core模块的解析实现，实现了动态Schema生成")
            print("✅ 支持Profile扩展和Stereotype应用")
            print("✅ 完整的数据查询和关系分析功能")
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            logger.exception("演示异常")
        
        finally:
            if self.db_pool:
                await self.db_pool.close()

async def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行演示
    demo = UMLMetaclassDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main()) 