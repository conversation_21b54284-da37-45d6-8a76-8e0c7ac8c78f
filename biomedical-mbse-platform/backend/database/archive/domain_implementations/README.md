# Domain Implementations

本目录包含特定领域的实现代码。

## 📁 文件说明

### 领域实现
- **`uml_sysml_domain_implementation.py`** - UML/SysML领域实现
  - 创建UML建模领域
  - 创建SysML系统建模领域
  - 建立UML和SysML之间的跨域关系
  - 支持XML/XMI格式的模型导入

## 🎯 主要功能

### UML领域支持
- **核心UML元素** - Package, Class, Association, UseCase
- **结构图元素** - 类图、包图、组件图
- **行为图元素** - 用例图、序列图、活动图
- **扩展机制** - Profile, Stereotype支持

### SysML领域支持
- **系统元素** - Block, Requirement, Activity
- **结构建模** - 系统结构和组成关系
- **行为建模** - 系统行为和交互
- **需求建模** - 需求层次和追溯关系

### 跨域关系
- **UML到SysML映射** - Class → Block, UseCase → Requirement
- **双向同步** - 保持UML和SysML模型一致性
- **关系追踪** - 完整的跨域关系记录

## 🚀 使用方法

```bash
# 运行UML/SysML领域演示
python uml_sysml_domain_implementation.py
```

### 编程使用
```python
from domain_implementations.uml_sysml_domain_implementation import UMLSysMLDomainBuilder

# 创建领域构建器
builder = UMLSysMLDomainBuilder(db_pool)
await builder.initialize()

# 创建UML领域
await builder.create_uml_domain()

# 创建SysML领域
await builder.create_sysml_domain()

# 解析和导入UML模型
await builder.parse_uml_xml_and_create_elements(uml_xml, "model.uml")

# 解析和导入SysML模型
await builder.parse_sysml_xml_and_create_elements(sysml_xml, "model.sysml")

# 建立跨域关系
await builder.establish_uml_sysml_relationships()
```

## 📋 领域结构

### UML领域 (uml_domain)
```
uml_packages      - UML包
uml_classes       - UML类
uml_associations  - UML关联
uml_usecases      - UML用例
```

### SysML领域 (sysml_domain)
```
sysml_blocks        - SysML块
sysml_requirements  - SysML需求
sysml_activities    - SysML活动
sysml_constraints   - SysML约束
```

## 📚 扩展指南

### 添加新的UML元素类型
1. 在`_create_uml_domain()`中添加Element类型定义
2. 在XML解析函数中添加对应的解析逻辑
3. 在跨域关系中建立与SysML的映射

### 添加新的SysML元素类型
1. 在`_create_sysml_domain()`中添加Element类型定义
2. 在XML解析函数中添加对应的解析逻辑
3. 建立与UML元素的跨域关系

## 📚 相关文档

参考 `docs/` 目录中的相关文档：
- UML_SysML_domain_guide.md 