"""
UML/SysML 领域库实现示例

基于动态领域架构创建UML和SysML建模领域
支持从XML解析后的建模元素自动映射到Element架构
"""

import asyncio
import json
import logging
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'domain_managers'))
import asyncpg
from domain_factory import (
    DomainFactory, DomainCreationRequest, DomainType
)
from core_domain_manager import CoreDomainManager, ElementMetadata

# 修复schemas.dynamic引用路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'schemas', 'dynamic'))
from dynamic_schema_generator import DynamicSchemaGenerator

# 引用上级domain_managers目录
from cross_domain_indexer import CrossDomainIndexer

logger = logging.getLogger(__name__)

@dataclass
class UMLElement:
    """UML建模元素"""
    element_id: str
    uml_type: str  # Class, Package, Association, etc.
    name: str
    stereotype: Optional[str] = None
    visibility: str = "public"
    properties: Dict[str, Any] = None
    relationships: List[str] = None

@dataclass
class SysMLElement:
    """SysML建模元素"""
    element_id: str
    sysml_type: str  # Block, Requirement, Activity, etc.
    name: str
    stereotype: Optional[str] = None
    properties: Dict[str, Any] = None
    requirements_refs: List[str] = None
    allocations: List[str] = None

class UMLSysMLDomainBuilder:
    """UML/SysML领域构建器"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.core_manager = CoreDomainManager(db_pool)
        self.domain_factory = DomainFactory(db_pool, self.core_manager)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def initialize(self):
        """初始化构建器"""
        await self.core_manager
        self.logger.info("UML/SysML领域构建器初始化完成")
    
    async def create_uml_domain(self) -> bool:
        """创建UML建模领域"""
        self.logger.info("--- 创建UML建模领域 ---")
        
        # UML核心元素类型定义
        uml_element_types = [
            {
                'type_id': 'uml_class_element',
                'type_name': 'UML类Element',
                'table_name': 'uml_class_elements',
                'field_definitions': {
                    'class_name': {'type': 'string', 'required': True, 'indexed': True},
                    'qualified_name': {'type': 'string', 'unique': True, 'indexed': True},
                    'package_path': {'type': 'string', 'indexed': True},
                    'stereotype': {'type': 'string', 'indexed': True},
                    'visibility': {'type': 'string', 'default': 'public'},
                    'is_abstract': {'type': 'boolean', 'default': False},
                    'is_final': {'type': 'boolean', 'default': False},
                    'attributes': {'type': 'jsonb', 'default': '[]'},
                    'operations': {'type': 'jsonb', 'default': '[]'},
                    'constraints': {'type': 'jsonb', 'default': '[]'},
                    'documentation': {'type': 'text'},
                    'source_file': {'type': 'string'},
                    'xml_source': {'type': 'text'}  # 原始XML内容
                },
                'index_definitions': [
                    {'fields': ['class_name', 'package_path'], 'type': 'btree'},
                    {'fields': ['stereotype'], 'type': 'btree'},
                    {'fields': ['attributes'], 'type': 'gin'},
                    {'fields': ['operations'], 'type': 'gin'}
                ],
                'cross_domain_refs': ['sysml_refs', 'security_refs', 'traceability_refs']
            },
            {
                'type_id': 'uml_package_element',
                'type_name': 'UML包Element',
                'table_name': 'uml_package_elements',
                'field_definitions': {
                    'package_name': {'type': 'string', 'required': True, 'indexed': True},
                    'qualified_name': {'type': 'string', 'unique': True, 'indexed': True},
                    'parent_package': {'type': 'string', 'indexed': True},
                    'visibility': {'type': 'string', 'default': 'public'},
                    'stereotype': {'type': 'string', 'indexed': True},
                    'owned_elements': {'type': 'jsonb', 'default': '[]'},
                    'imported_packages': {'type': 'jsonb', 'default': '[]'},
                    'documentation': {'type': 'text'},
                    'xml_source': {'type': 'text'}
                },
                'relationship_definitions': {
                    'parent': {'type': 'many_to_one', 'target': 'uml_package_element'},
                    'owned_classes': {'type': 'one_to_many', 'target': 'uml_class_element'}
                },
                'cross_domain_refs': ['sysml_refs', 'architectural_refs']
            },
            {
                'type_id': 'uml_association_element',
                'type_name': 'UML关联Element',
                'table_name': 'uml_association_elements',
                'field_definitions': {
                    'association_name': {'type': 'string', 'indexed': True},
                    'association_type': {'type': 'string', 'required': True, 'indexed': True},  # Association, Aggregation, Composition
                    'source_class_id': {'type': 'string', 'required': True, 'indexed': True},
                    'target_class_id': {'type': 'string', 'required': True, 'indexed': True},
                    'source_multiplicity': {'type': 'string', 'default': '1'},
                    'target_multiplicity': {'type': 'string', 'default': '1'},
                    'source_role': {'type': 'string'},
                    'target_role': {'type': 'string'},
                    'is_navigable': {'type': 'boolean', 'default': True},
                    'constraints': {'type': 'jsonb', 'default': '[]'},
                    'xml_source': {'type': 'text'}
                },
                'relationship_definitions': {
                    'source_class': {'type': 'many_to_one', 'target': 'uml_class_element'},
                    'target_class': {'type': 'many_to_one', 'target': 'uml_class_element'}
                },
                'cross_domain_refs': ['sysml_refs', 'dependency_refs']
            },
            {
                'type_id': 'uml_usecase_element',
                'type_name': 'UML用例Element',
                'table_name': 'uml_usecase_elements',
                'field_definitions': {
                    'usecase_name': {'type': 'string', 'required': True, 'indexed': True},
                    'qualified_name': {'type': 'string', 'unique': True, 'indexed': True},
                    'actor_ids': {'type': 'jsonb', 'default': '[]'},
                    'preconditions': {'type': 'text'},
                    'postconditions': {'type': 'text'},
                    'main_scenario': {'type': 'text'},
                    'alternative_scenarios': {'type': 'jsonb', 'default': '[]'},
                    'extends_usecases': {'type': 'jsonb', 'default': '[]'},
                    'includes_usecases': {'type': 'jsonb', 'default': '[]'},
                    'priority': {'type': 'string', 'default': 'medium'},
                    'complexity': {'type': 'string', 'default': 'medium'},
                    'xml_source': {'type': 'text'}
                },
                'cross_domain_refs': ['sysml_requirements_refs', 'testing_refs']
            }
        ]
        
        # 创建UML领域请求
        uml_request = DomainCreationRequest(
            domain_name='uml',
            display_name='UML建模领域',
            description='统一建模语言(UML)建模元素管理领域，支持类图、用例图、时序图等',
            domain_type=DomainType.DOMAIN_SPECIFIC,
            custom_element_types=uml_element_types,
            cross_domain_connections=['sysml', 'security', 'traceability'],
            auto_optimize=True,
            namespace_prefix='UML'
        )
        
        # 创建领域
        result = await self.domain_factory.create_domain(uml_request)
        
        if result.success:
            self.logger.info(f"✅ UML建模领域创建成功！")
            self.logger.info(f"   - Schema: {result.schema_name}")
            self.logger.info(f"   - 创建的表: {result.created_tables}")
            self.logger.info(f"   - 创建的索引: {len(result.created_indexes)} 个")
        else:
            self.logger.error(f"❌ UML建模领域创建失败: {result.errors}")
            
        return result.success
    
    async def create_sysml_domain(self) -> bool:
        """创建SysML建模领域"""
        self.logger.info("--- 创建SysML建模领域 ---")
        
        # SysML核心元素类型定义
        sysml_element_types = [
            {
                'type_id': 'sysml_block_element',
                'type_name': 'SysML块Element',
                'table_name': 'sysml_block_elements',
                'field_definitions': {
                    'block_name': {'type': 'string', 'required': True, 'indexed': True},
                    'qualified_name': {'type': 'string', 'unique': True, 'indexed': True},
                    'stereotype': {'type': 'string', 'indexed': True},
                    'visibility': {'type': 'string', 'default': 'public'},
                    'is_abstract': {'type': 'boolean', 'default': False},
                    'value_properties': {'type': 'jsonb', 'default': '[]'},
                    'part_properties': {'type': 'jsonb', 'default': '[]'},
                    'reference_properties': {'type': 'jsonb', 'default': '[]'},
                    'flow_properties': {'type': 'jsonb', 'default': '[]'},
                    'operations': {'type': 'jsonb', 'default': '[]'},
                    'constraints': {'type': 'jsonb', 'default': '[]'},
                    'parametric_constraints': {'type': 'jsonb', 'default': '[]'},
                    'allocated_functions': {'type': 'jsonb', 'default': '[]'},
                    'requirements_refs': {'type': 'jsonb', 'default': '[]'},
                    'xml_source': {'type': 'text'}
                },
                'index_definitions': [
                    {'fields': ['block_name'], 'type': 'btree'},
                    {'fields': ['stereotype'], 'type': 'btree'},
                    {'fields': ['value_properties'], 'type': 'gin'},
                    {'fields': ['requirements_refs'], 'type': 'gin'}
                ],
                'cross_domain_refs': ['uml_refs', 'requirements_refs', 'verification_refs']
            },
            {
                'type_id': 'sysml_requirement_element',
                'type_name': 'SysML需求Element',
                'table_name': 'sysml_requirement_elements',
                'field_definitions': {
                    'requirement_id': {'type': 'string', 'required': True, 'unique': True, 'indexed': True},
                    'requirement_name': {'type': 'string', 'required': True, 'indexed': True},
                    'text': {'type': 'text', 'required': True},
                    'priority': {'type': 'string', 'default': 'medium', 'indexed': True},
                    'risk': {'type': 'string', 'default': 'medium'},
                    'verification_method': {'type': 'string', 'indexed': True},  # Test, Analysis, Inspection, Demonstration
                    'category': {'type': 'string', 'indexed': True},  # Functional, Performance, Interface, etc.
                    'source': {'type': 'string'},
                    'rationale': {'type': 'text'},
                    'parent_requirement_id': {'type': 'string', 'indexed': True},
                    'derived_requirements': {'type': 'jsonb', 'default': '[]'},
                    'satisfied_by': {'type': 'jsonb', 'default': '[]'},  # 满足该需求的Element引用
                    'verified_by': {'type': 'jsonb', 'default': '[]'},   # 验证该需求的Element引用
                    'status': {'type': 'string', 'default': 'draft', 'indexed': True},
                    'version': {'type': 'string', 'default': '1.0'},
                    'xml_source': {'type': 'text'}
                },
                'relationship_definitions': {
                    'parent': {'type': 'many_to_one', 'target': 'sysml_requirement_element'},
                    'children': {'type': 'one_to_many', 'target': 'sysml_requirement_element'}
                },
                'cross_domain_refs': ['uml_refs', 'testing_refs', 'verification_refs']
            },
            {
                'type_id': 'sysml_activity_element',
                'type_name': 'SysML活动Element',
                'table_name': 'sysml_activity_elements',
                'field_definitions': {
                    'activity_name': {'type': 'string', 'required': True, 'indexed': True},
                    'qualified_name': {'type': 'string', 'unique': True, 'indexed': True},
                    'activity_type': {'type': 'string', 'indexed': True},  # Action, Activity, CallBehavior
                    'preconditions': {'type': 'jsonb', 'default': '[]'},
                    'postconditions': {'type': 'jsonb', 'default': '[]'},
                    'input_parameters': {'type': 'jsonb', 'default': '[]'},
                    'output_parameters': {'type': 'jsonb', 'default': '[]'},
                    'control_flow': {'type': 'jsonb', 'default': '[]'},
                    'object_flow': {'type': 'jsonb', 'default': '[]'},
                    'allocated_to': {'type': 'jsonb', 'default': '[]'},  # 分配到的Block或Actor
                    'duration_estimate': {'type': 'decimal'},
                    'resource_requirements': {'type': 'jsonb', 'default': '[]'},
                    'xml_source': {'type': 'text'}
                },
                'cross_domain_refs': ['uml_refs', 'process_refs', 'resource_refs']
            },
            {
                'type_id': 'sysml_constraint_element',
                'type_name': 'SysML约束Element',
                'table_name': 'sysml_constraint_elements',
                'field_definitions': {
                    'constraint_name': {'type': 'string', 'required': True, 'indexed': True},
                    'constraint_type': {'type': 'string', 'required': True, 'indexed': True},  # Parametric, Logic, Temporal
                    'expression': {'type': 'text', 'required': True},
                    'language': {'type': 'string', 'default': 'OCL'},  # OCL, Math, Natural Language
                    'parameters': {'type': 'jsonb', 'default': '[]'},
                    'constrained_elements': {'type': 'jsonb', 'default': '[]'},
                    'context': {'type': 'string'},
                    'validation_status': {'type': 'string', 'default': 'pending'},
                    'xml_source': {'type': 'text'}
                },
                'cross_domain_refs': ['uml_refs', 'verification_refs', 'analysis_refs']
            }
        ]
        
        # 创建SysML领域请求
        sysml_request = DomainCreationRequest(
            domain_name='sysml',
            display_name='SysML系统建模领域',
            description='系统建模语言(SysML)建模元素管理领域，支持需求、行为、结构和参数化建模',
            domain_type=DomainType.DOMAIN_SPECIFIC,
            custom_element_types=sysml_element_types,
            cross_domain_connections=['uml', 'requirements', 'verification', 'testing'],
            auto_optimize=True,
            namespace_prefix='SYSML'
        )
        
        # 创建领域
        result = await self.domain_factory.create_domain(sysml_request)
        
        if result.success:
            self.logger.info(f"✅ SysML建模领域创建成功！")
            self.logger.info(f"   - Schema: {result.schema_name}")
            self.logger.info(f"   - 创建的表: {result.created_tables}")
            self.logger.info(f"   - 创建的索引: {len(result.created_indexes)} 个")
        else:
            self.logger.error(f"❌ SysML建模领域创建失败: {result.errors}")
            
        return result.success
    
    async def parse_uml_xml_and_create_elements(self, xml_content: str, source_file: str) -> Tuple[bool, str]:
        """
        解析UML XML文件并创建对应的Element
        
        Args:
            xml_content: XML文件内容
            source_file: 源文件路径
            
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息)
        """
        try:
            self.logger.info(f"解析UML XML文件: {source_file}")
            
            # 解析XML
            root = ET.fromstring(xml_content)
            
            # 提取UML元素
            created_elements = []
            
            # 解析包结构
            packages = await self._parse_uml_packages(root, source_file)
            created_elements.extend(packages)
            
            # 解析类
            classes = await self._parse_uml_classes(root, source_file)
            created_elements.extend(classes)
            
            # 解析关联
            associations = await self._parse_uml_associations(root, source_file)
            created_elements.extend(associations)
            
            # 解析用例
            usecases = await self._parse_uml_usecases(root, source_file)
            created_elements.extend(usecases)
            
            self.logger.info(f"✅ UML XML解析完成，创建了 {len(created_elements)} 个Element")
            return True, f"成功创建 {len(created_elements)} 个UML Element"
            
        except ET.ParseError as e:
            self.logger.error(f"XML解析错误: {e}")
            return False, f"XML解析错误: {e}"
        except Exception as e:
            self.logger.error(f"UML XML解析失败: {e}")
            return False, str(e)
    
    async def parse_sysml_xml_and_create_elements(self, xml_content: str, source_file: str) -> Tuple[bool, str]:
        """
        解析SysML XML文件并创建对应的Element
        
        Args:
            xml_content: XML文件内容
            source_file: 源文件路径
            
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息)
        """
        try:
            self.logger.info(f"解析SysML XML文件: {source_file}")
            
            # 解析XML
            root = ET.fromstring(xml_content)
            
            # 提取SysML元素
            created_elements = []
            
            # 解析块
            blocks = await self._parse_sysml_blocks(root, source_file)
            created_elements.extend(blocks)
            
            # 解析需求
            requirements = await self._parse_sysml_requirements(root, source_file)
            created_elements.extend(requirements)
            
            # 解析活动
            activities = await self._parse_sysml_activities(root, source_file)
            created_elements.extend(activities)
            
            # 解析约束
            constraints = await self._parse_sysml_constraints(root, source_file)
            created_elements.extend(constraints)
            
            self.logger.info(f"✅ SysML XML解析完成，创建了 {len(created_elements)} 个Element")
            return True, f"成功创建 {len(created_elements)} 个SysML Element"
            
        except ET.ParseError as e:
            self.logger.error(f"XML解析错误: {e}")
            return False, f"XML解析错误: {e}"
        except Exception as e:
            self.logger.error(f"SysML XML解析失败: {e}")
            return False, str(e)
    
    async def establish_uml_sysml_relationships(self) -> bool:
        """建立UML和SysML之间的跨域关系"""
        self.logger.info("--- 建立UML-SysML跨域关系 ---")
        
        try:
            # 1. UML类到SysML块的映射关系
            await self._create_uml_class_to_sysml_block_relationships()
            
            # 2. UML用例到SysML需求的跟踪关系
            await self._create_uml_usecase_to_sysml_requirement_relationships()
            
            # 3. UML序列图到SysML活动的映射关系
            await self._create_uml_sequence_to_sysml_activity_relationships()
            
            self.logger.info("✅ UML-SysML跨域关系建立成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ UML-SysML跨域关系建立失败: {e}")
            return False
    
    # 以下是辅助方法的示例实现（实际解析逻辑需要根据具体的XML Schema调整）
    
    async def _parse_uml_packages(self, root: ET.Element, source_file: str) -> List[str]:
        """解析UML包元素"""
        return []  # 占位符实现
    
    async def _parse_uml_classes(self, root: ET.Element, source_file: str) -> List[str]:
        """解析UML类元素"""
        created_elements = []
        
        # 查找所有类元素 (具体XPath需要根据实际UML XML格式调整)
        namespaces = {'uml': 'http://www.eclipse.org/uml2/5.0.0/UML'}
        for class_elem in root.findall(".//uml:Class", namespaces):
            class_data = {
                'class_name': class_elem.get('name', 'UnnamedClass'),
                'qualified_name': self._get_qualified_name(class_elem),
                'visibility': class_elem.get('visibility', 'public'),
                'is_abstract': class_elem.get('isAbstract', 'false').lower() == 'true',
                'stereotype': self._extract_stereotype(class_elem),
                'attributes': self._extract_attributes(class_elem),
                'operations': self._extract_operations(class_elem),
                'source_file': source_file,
                'xml_source': ET.tostring(class_elem, encoding='unicode')
            }
            
            # 创建Element
            element_id = await self._create_uml_class_element(class_data)
            if element_id:
                created_elements.append(element_id)
        
        return created_elements
    
    async def _parse_uml_associations(self, root: ET.Element, source_file: str) -> List[str]:
        """解析UML关联元素"""
        return []  # 占位符实现
    
    async def _parse_uml_usecases(self, root: ET.Element, source_file: str) -> List[str]:
        """解析UML用例元素"""
        return []  # 占位符实现
    
    async def _parse_sysml_blocks(self, root: ET.Element, source_file: str) -> List[str]:
        """解析SysML块元素"""
        return []  # 占位符实现
    
    async def _parse_sysml_requirements(self, root: ET.Element, source_file: str) -> List[str]:
        """解析SysML需求元素"""
        created_elements = []
        
        # 查找所有需求元素
        namespaces = {'sysml': 'http://www.eclipse.org/sysml/2.0.0'}
        for req_elem in root.findall(".//sysml:Requirement", namespaces):
            req_data = {
                'requirement_id': req_elem.get('id', f"REQ_{len(created_elements)+1}"),
                'requirement_name': req_elem.get('name', 'UnnamedRequirement'),
                'text': req_elem.find('.//text').text if req_elem.find('.//text') is not None else '',
                'priority': req_elem.get('priority', 'medium'),
                'category': req_elem.get('category', 'functional'),
                'verification_method': req_elem.get('verificationMethod', 'test'),
                'source_file': source_file,
                'xml_source': ET.tostring(req_elem, encoding='unicode')
            }
            
            # 创建Element
            element_id = await self._create_sysml_requirement_element(req_data)
            if element_id:
                created_elements.append(element_id)
        
        return created_elements
    
    async def _parse_sysml_activities(self, root: ET.Element, source_file: str) -> List[str]:
        """解析SysML活动元素"""
        return []  # 占位符实现
    
    async def _parse_sysml_constraints(self, root: ET.Element, source_file: str) -> List[str]:
        """解析SysML约束元素"""
        return []  # 占位符实现
    
    # 跨域关系建立方法
    async def _create_uml_class_to_sysml_block_relationships(self):
        """创建UML类到SysML块的关系"""
        pass  # 占位符实现
    
    async def _create_uml_usecase_to_sysml_requirement_relationships(self):
        """创建UML用例到SysML需求的关系"""
        pass  # 占位符实现
    
    async def _create_uml_sequence_to_sysml_activity_relationships(self):
        """创建UML序列图到SysML活动的关系"""
        pass  # 占位符实现
    
    # 辅助方法
    def _get_qualified_name(self, element: ET.Element) -> str:
        """获取元素的完全限定名"""
        return element.get('name', 'Unnamed')  # 简化实现
    
    def _extract_stereotype(self, element: ET.Element) -> Optional[str]:
        """提取元素的构造型"""
        return None  # 占位符实现
    
    def _extract_attributes(self, element: ET.Element) -> List[Dict[str, Any]]:
        """提取类的属性"""
        return []  # 占位符实现
    
    def _extract_operations(self, element: ET.Element) -> List[Dict[str, Any]]:
        """提取类的操作"""
        return []  # 占位符实现
    
    # 更多辅助方法...
    async def _create_uml_class_element(self, class_data: Dict[str, Any]) -> Optional[str]:
        """创建UML类Element"""
        # 实现具体的Element创建逻辑
        return None  # 占位符实现
    
    async def _create_sysml_requirement_element(self, req_data: Dict[str, Any]) -> Optional[str]:
        """创建SysML需求Element"""
        # 实现具体的Element创建逻辑
        return None  # 占位符实现

async def main():
    """主函数 - 演示UML/SysML领域库的创建和使用"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'biomedical_mbse_platform',
        'user': 'mbse_user',
        'password': 'mbse_pass_2024'
    }
    
    # 创建数据库连接池
    database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    db_pool = await asyncpg.create_pool(database_url)
    
    try:
        # 初始化构建器
        builder = UMLSysMLDomainBuilder(db_pool)
        await builder.initialize()
        
        # 创建UML领域
        uml_success = await builder.create_uml_domain()
        
        # 创建SysML领域
        sysml_success = await builder.create_sysml_domain()
        
        if uml_success and sysml_success:
            # 建立跨域关系
            await builder.establish_uml_sysml_relationships()
            
            # 示例：解析XML文件（需要提供实际的XML内容）
            # with open('sample_uml_model.xml', 'r', encoding='utf-8') as f:
            #     uml_xml = f.read()
            #     await builder.parse_uml_xml_and_create_elements(uml_xml, 'sample_uml_model.xml')
            
            print("🎉 UML/SysML建模领域库创建完成！")
            print("现在可以通过XML解析自动创建建模元素了。")
        
    finally:
        await db_pool.close()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main()) 