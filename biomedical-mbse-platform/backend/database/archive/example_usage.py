"""
领域驱动Element架构使用示例

展示如何使用基于Schema分离的Element管理系统
"""

import asyncio
import asyncpg

# 修复引用路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'domain_managers'))
from core_domain_manager import CoreDomainManager, ElementMetadata, CrossDomainRelationship
from security_domain_manager import SecurityDomainManager

class ElementPlatformManager:
    """Element平台管理器 - 统一管理所有领域"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.db_pool = None
        self.core_manager = None
        self.security_manager = None
    
    async def initialize(self):
        """初始化平台"""
        # 创建数据库连接池
        self.db_pool = await asyncpg.create_pool(
            self.database_url,
            min_size=5,
            max_size=20,
            command_timeout=60
        )
        
        # 初始化领域管理器
        self.core_manager = CoreDomainManager(self.db_pool)
        self.security_manager = SecurityDomainManager(self.db_pool, self.core_manager)
        
        print("✅ Element平台初始化完成")
    
    async def demo_user_management(self):
        """演示用户管理功能"""
        print("\n🔐 演示安全领域Element管理")
        
        # 1. 创建用户Element
        user_data = {
            'username': 'zhangsan',
            'email': '<EMAIL>',
            'password': 'password123',
            'profile': {
                'display_name': '张三',
                'department': '生物医学工程部',
                'position': '研究员'
            }
        }
        
        success, user_id = await self.security_manager.create_user_element(user_data, 'system')
        if success:
            print(f"✅ 创建用户Element成功: {user_id}")
        else:
            print(f"❌ 创建用户Element失败: {user_id}")
            return
        
        # 2. 用户认证
        user = await self.security_manager.authenticate_user('zhangsan', 'password123')
        if user:
            print(f"✅ 用户认证成功: {user['username']}")
        else:
            print("❌ 用户认证失败")
        
        # 3. 获取用户权限
        permissions = await self.security_manager.get_user_permissions(user_id)
        print(f"📋 用户权限: {permissions}")
        
        # 4. 创建自定义角色
        role_data = {
            'role_name': 'researcher',
            'display_name': '研究员',
            'description': '具有研究项目访问权限的角色'
        }
        
        success, role_id = await self.security_manager.create_role_element(role_data, 'admin')
        if success:
            print(f"✅ 创建角色Element成功: {role_id}")
            
            # 5. 分配角色给用户
            success = await self.security_manager.assign_role_to_user(user_id, role_id, 'admin')
            if success:
                print(f"✅ 角色分配成功")
        
    async def demo_cross_domain_relationships(self):
        """演示跨领域关系管理"""
        print("\n🔗 演示跨领域关系管理")
        
        # 假设我们有一个项目Element (在modeling领域)
        project_id = "project_biomodel_001"
        user_id = "user_zhangsan_001"
        
        # 创建跨领域关系：用户参与项目
        relationship = CrossDomainRelationship(
            relationship_id="rel_user_project_001",
            source_element_id=user_id,
            target_element_id=project_id,
            source_domain="security",
            target_domain="modeling",
            relationship_type="participates_in",
            relationship_data={
                "role_in_project": "lead_researcher",
                "start_date": "2024-12-09",
                "permissions": ["read", "write", "execute"]
            },
            strength=1.0,
            semantic_meaning="用户作为主要研究员参与生物建模项目"
        )
        
        success = await self.core_manager.create_cross_domain_relationship(relationship)
        if success:
            print(f"✅ 创建跨领域关系成功: {relationship.relationship_id}")
            
            # 查询用户的跨领域关系
            relationships = await self.core_manager.get_cross_domain_relationships(user_id)
            print(f"📊 用户跨领域关系数量: {len(relationships)}")
        
    async def demo_element_metadata_query(self):
        """演示Element元数据查询"""
        print("\n📊 演示Element元数据查询")
        
        # 查询所有安全领域的Elements
        security_elements = await self.core_manager.query_elements({
            'domain_schema': 'security_schema'
        })
        print(f"🔐 安全领域Element数量: {len(security_elements)}")
        
        # 按语义标签查询
        user_elements = await self.core_manager.query_elements({
            'domain_schema': 'security_schema',
            'semantic_tags': ['security:user']
        })
        print(f"👥 用户Element数量: {len(user_elements)}")
        
        # 查询领域注册表
        domains = await self.core_manager.get_domain_registry()
        print(f"🏗️ 已注册领域数量: {len(domains)}")
        for domain in domains:
            print(f"   - {domain['domain_name']}: {domain['display_name']}")
    
    async def close(self):
        """关闭连接"""
        if self.db_pool:
            await self.db_pool.close()
        print("✅ 数据库连接已关闭")

async def main():
    """主函数"""
    # 数据库连接URL (需要根据实际情况修改)
    database_url = "postgresql://user:password@localhost/biomedical_mbse_platform"
    
    platform = ElementPlatformManager(database_url)
    
    try:
        # 初始化平台
        await platform.initialize()
        
        # 演示各种功能
        await platform.demo_user_management()
        await platform.demo_cross_domain_relationships()
        await platform.demo_element_metadata_query()
        
        print("\n🎉 所有演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await platform.close()

if __name__ == "__main__":
    asyncio.run(main()) 