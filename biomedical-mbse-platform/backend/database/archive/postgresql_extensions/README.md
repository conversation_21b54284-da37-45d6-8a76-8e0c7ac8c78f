# PostgreSQL Extensions

本目录包含基于PostgreSQL高级特性的扩展功能实现。

## 📁 文件说明

### 核心文件
- **`postgresql_enhanced_metaclass_manager.py`** - PostgreSQL增强型元类管理器
  - 利用表继承、物化视图、分区表等PostgreSQL特性
  - 提供高性能的UML元类管理功能

- **`postgresql_components_demo.py`** - PostgreSQL高级组件演示
  - 展示Aggregates、Collations、Domains、FTS系统的使用
  - 包含完整的生物医学领域应用示例

- **`run_postgresql_demo.py`** - PostgreSQL功能综合演示
  - 演示表继承、物化视图、分区表、全文搜索等功能
  - 提供性能对比和最佳实践示例

## 🎯 主要功能

### PostgreSQL高级特性
- **表继承 (Table Inheritance)** - 直接映射UML继承关系
- **物化视图 (Materialized Views)** - 高性能复杂查询缓存
- **分区表 (Partitioning)** - 大规模数据管理
- **全文搜索 (FTS)** - 智能语义检索
- **自定义类型 (Custom Types)** - 业务领域特定类型
- **存储过程 (Stored Procedures)** - 服务器端业务逻辑

### 生物医学特性
- 生物医学术语同义词支持
- 基因、蛋白质、药物标识符处理
- 多语言排序规则
- 专业领域数据约束

## 🚀 使用方法

```bash
# 运行PostgreSQL组件演示
python postgresql_components_demo.py

# 运行完整功能演示
python run_postgresql_demo.py

# 在其他模块中使用增强管理器
from postgresql_extensions.postgresql_enhanced_metaclass_manager import create_postgresql_enhanced_manager
```

## 📚 相关文档

参考 `docs/` 目录中的详细技术文档：
- PostgreSQL_Advanced_Features_Guide.md
- PostgreSQL_Advanced_Components_Guide.md
- README_PostgreSQL_Enhanced.md 