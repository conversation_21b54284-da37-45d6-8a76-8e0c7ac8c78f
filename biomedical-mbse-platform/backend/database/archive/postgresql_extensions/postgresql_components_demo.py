#!/usr/bin/env python3
"""
PostgreSQL高级组件演示脚本

演示以下功能：
1. Aggregates (聚合函数) - 自定义元类复杂度聚合
2. Collations (排序规则) - 多语言和自然排序
3. Domains (域类型) - 业务规则约束
4. FTS系统 (全文搜索) - 智能语义搜索

使用方法:
    python postgresql_components_demo.py [--component <component_name>] [--setup] [--test]
"""

import asyncio
import argparse
import logging
import json
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
import asyncpg

logger = logging.getLogger(__name__)

class PostgreSQLComponentsDemo:
    """PostgreSQL高级组件演示"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'biomedical_mbse_platform',
            'user': 'mbse_user',
            'password': 'mbse_pass_2024'
        }
        self.db_pool = None
    
    async def initialize(self):
        """初始化数据库连接"""
        print("🚀 初始化PostgreSQL高级组件演示环境...")
        
        database_url = f"postgresql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
        self.db_pool = await asyncpg.create_pool(database_url, min_size=2, max_size=10)
        
        print("✅ 数据库连接初始化完成!")
    
    async def cleanup(self):
        """清理资源"""
        if self.db_pool:
            await self.db_pool.close()
    
    # ========== 1. Aggregates 演示 ==========
    
    async def demo_aggregates(self):
        """演示自定义聚合函数"""
        print("\n📊 演示 Aggregates (聚合函数)")
        print("=" * 50)
        
        async with self.db_pool.acquire() as conn:
            # 1. 创建元类复杂度聚合函数
            print("1️⃣ 创建自定义聚合函数...")
            
            # 状态转换函数
            await conn.execute("""
                CREATE OR REPLACE FUNCTION metaclass_complexity_sfunc(
                    state INTEGER,
                    props JSONB
                ) RETURNS INTEGER AS $$
                BEGIN
                    RETURN state + 
                           COALESCE((props->>'attribute_count')::INTEGER, 0) +
                           COALESCE((props->>'relationship_count')::INTEGER, 0) * 2 +
                           COALESCE((props->>'constraint_count')::INTEGER, 0) * 3;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # 最终函数
            await conn.execute("""
                CREATE OR REPLACE FUNCTION complexity_final_func(state INTEGER)
                RETURNS NUMERIC AS $$
                BEGIN
                    RETURN CASE WHEN state > 0 THEN state::NUMERIC / 10.0 ELSE 0 END;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # 创建聚合函数
            await conn.execute("""
                DROP AGGREGATE IF EXISTS metaclass_complexity_avg(JSONB);
                CREATE AGGREGATE metaclass_complexity_avg(JSONB) (
                    SFUNC = metaclass_complexity_sfunc,
                    STYPE = INTEGER,
                    INITCOND = '0',
                    FINALFUNC = complexity_final_func
                );
            """)
            
            print("   ✅ 创建复杂度聚合函数成功")
            
            # 2. 插入测试数据
            test_data = [
                ('PatientClass', 'biomedicine', '{"attribute_count": 15, "relationship_count": 8, "constraint_count": 5}'),
                ('TreatmentClass', 'biomedicine', '{"attribute_count": 12, "relationship_count": 6, "constraint_count": 3}'),
                ('DrugClass', 'biomedicine', '{"attribute_count": 20, "relationship_count": 10, "constraint_count": 8}'),
                ('GeneClass', 'genomics', '{"attribute_count": 8, "relationship_count": 4, "constraint_count": 2}'),
                ('ProteinClass', 'genomics', '{"attribute_count": 25, "relationship_count": 15, "constraint_count": 12}')
            ]
            
            await conn.executemany("""
                INSERT INTO core_schema.base_metaclass (name, qualified_name, properties)
                VALUES ($1, 'demo::' || $1, $3)
                ON CONFLICT (qualified_name) DO UPDATE SET properties = EXCLUDED.properties
            """, test_data)
            
            print(f"   ✅ 插入测试数据 {len(test_data)} 条")
            
            # 3. 使用聚合函数
            results = await conn.fetch("""
                SELECT 
                    CASE 
                        WHEN properties->>'domain' IS NOT NULL THEN properties->>'domain'
                        ELSE 'unknown'
                    END as domain,
                    COUNT(*) as metaclass_count,
                    metaclass_complexity_avg(properties) as avg_complexity
                FROM core_schema.base_metaclass
                WHERE name LIKE '%Class'
                GROUP BY properties->>'domain'
                ORDER BY avg_complexity DESC
            """)
            
            print("   📈 复杂度聚合结果:")
            for row in results:
                print(f"     - {row['domain']}: {row['metaclass_count']}个元类, 平均复杂度: {float(row['avg_complexity']):.2f}")
            
            # 4. JSONB聚合演示
            await conn.execute("""
                CREATE OR REPLACE FUNCTION jsonb_merge_agg_sfunc(
                    state JSONB,
                    value JSONB
                ) RETURNS JSONB AS $$
                BEGIN
                    RETURN state || value;
                END;
                $$ LANGUAGE plpgsql;
                
                DROP AGGREGATE IF EXISTS jsonb_merge_agg(JSONB);
                CREATE AGGREGATE jsonb_merge_agg(JSONB) (
                    SFUNC = jsonb_merge_agg_sfunc,
                    STYPE = JSONB,
                    INITCOND = '{}'
                );
            """)
            
            merged_properties = await conn.fetchval("""
                SELECT jsonb_merge_agg(properties) 
                FROM core_schema.base_metaclass
                WHERE name LIKE '%Class'
            """)
            
            print(f"   🔗 合并的属性: {len(merged_properties.keys())}个键")
    
    # ========== 2. Collations 演示 ==========
    
    async def demo_collations(self):
        """演示排序规则"""
        print("\n🌍 演示 Collations (排序规则)")
        print("=" * 50)
        
        async with self.db_pool.acquire() as conn:
            # 1. 创建自定义排序规则
            print("1️⃣ 创建自定义排序规则...")
            
            try:
                # 数字敏感排序（自然排序）
                await conn.execute("""
                    CREATE COLLATION IF NOT EXISTS metaclass_natural_sort (
                        provider = icu,
                        locale = 'en-US-u-kn-true'
                    );
                """)
                print("   ✅ 创建自然排序规则成功")
            except Exception as e:
                print(f"   ⚠️ 排序规则可能已存在: {e}")
            
            # 2. 创建测试表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS demo_sorting_test (
                    id SERIAL PRIMARY KEY,
                    name_default VARCHAR(100),
                    name_natural VARCHAR(100) COLLATE metaclass_natural_sort,
                    version VARCHAR(20),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                TRUNCATE demo_sorting_test;
            """)
            
            # 3. 插入测试数据
            sort_test_data = [
                ('Class10', 'Class10', '2.1.0'),
                ('Class2', 'Class2', '2.10.0'),
                ('Class1', 'Class1', '1.0.0'),
                ('Class20', 'Class20', '10.0.0'),
                ('Class3', 'Class3', '1.2.0'),
                ('ClassA10', 'ClassA10', '1.11.0'),
                ('ClassA2', 'ClassA2', '1.2.0')
            ]
            
            await conn.executemany("""
                INSERT INTO demo_sorting_test (name_default, name_natural, version)
                VALUES ($1, $2, $3)
            """, sort_test_data)
            
            print(f"   ✅ 插入排序测试数据 {len(sort_test_data)} 条")
            
            # 4. 对比排序结果
            print("   📊 排序对比:")
            
            # 默认排序
            default_sort = await conn.fetch("""
                SELECT name_default FROM demo_sorting_test 
                ORDER BY name_default
            """)
            print("     默认排序:", [row['name_default'] for row in default_sort])
            
            # 自然排序
            natural_sort = await conn.fetch("""
                SELECT name_natural FROM demo_sorting_test 
                ORDER BY name_natural
            """)
            print("     自然排序:", [row['name_natural'] for row in natural_sort])
            
            # 5. 版本号排序演示
            await conn.execute("""
                CREATE OR REPLACE FUNCTION semantic_version_sort_key(version_string TEXT)
                RETURNS TEXT AS $$
                DECLARE
                    parts TEXT[];
                    result TEXT := '';
                    part TEXT;
                BEGIN
                    parts := string_to_array(version_string, '.');
                    FOREACH part IN ARRAY parts
                    LOOP
                        result := result || lpad(part, 4, '0');
                    END LOOP;
                    RETURN result;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            version_sort = await conn.fetch("""
                SELECT version
                FROM demo_sorting_test
                ORDER BY semantic_version_sort_key(version)
            """)
            print("     版本排序:", [row['version'] for row in version_sort])
    
    # ========== 3. Domains 演示 ==========
    
    async def demo_domains(self):
        """演示域类型"""
        print("\n🛡️ 演示 Domains (域类型)")
        print("=" * 50)
        
        async with self.db_pool.acquire() as conn:
            # 1. 创建域类型
            print("1️⃣ 创建域类型...")
            
            domain_definitions = [
                # 元类标识符域
                """
                DROP DOMAIN IF EXISTS core_schema.metaclass_identifier CASCADE;
                CREATE DOMAIN core_schema.metaclass_identifier AS VARCHAR(255)
                    CHECK (VALUE ~ '^[A-Z][a-zA-Z0-9_]*$')
                    NOT NULL;
                """,
                
                # 限定名称域
                """
                DROP DOMAIN IF EXISTS core_schema.qualified_name_strict CASCADE;
                CREATE DOMAIN core_schema.qualified_name_strict AS VARCHAR(500)
                    CHECK (VALUE ~ '^[a-zA-Z][a-zA-Z0-9_.]*::[a-zA-Z][a-zA-Z0-9_]*$')
                    NOT NULL;
                """,
                
                # 版本号域
                """
                DROP DOMAIN IF EXISTS core_schema.semantic_version CASCADE;
                CREATE DOMAIN core_schema.semantic_version AS VARCHAR(50)
                    CHECK (VALUE ~ '^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9.-]+)?(\\+[a-zA-Z0-9.-]+)?$')
                    DEFAULT '1.0.0';
                """,
                
                # 百分比域
                """
                DROP DOMAIN IF EXISTS core_schema.percentage CASCADE;
                CREATE DOMAIN core_schema.percentage AS NUMERIC(5,2)
                    CHECK (VALUE >= 0.0 AND VALUE <= 100.0);
                """,
                
                # 优先级域
                """
                DROP DOMAIN IF EXISTS core_schema.priority_level CASCADE;
                CREATE DOMAIN core_schema.priority_level AS INTEGER
                    CHECK (VALUE >= 1 AND VALUE <= 10)
                    DEFAULT 5;
                """
            ]
            
            for domain_sql in domain_definitions:
                await conn.execute(domain_sql)
            
            print("   ✅ 创建域类型成功")
            
            # 2. 创建使用域类型的表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS demo_domain_test (
                    id SERIAL PRIMARY KEY,
                    name core_schema.metaclass_identifier,
                    qualified_name core_schema.qualified_name_strict,
                    version core_schema.semantic_version,
                    completion core_schema.percentage,
                    priority core_schema.priority_level,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                TRUNCATE demo_domain_test;
            """)
            
            print("   ✅ 创建域约束表成功")
            
            # 3. 测试有效数据
            print("2️⃣ 测试域约束...")
            
            valid_data = [
                ('PatientClass', 'domain::PatientClass', '1.0.0', 85.5, 3),
                ('TreatmentPlan', 'medical::TreatmentPlan', '2.1.0', 92.0, 1),
                ('DrugInteraction', 'pharma::DrugInteraction', '1.5.2', 78.3, 7)
            ]
            
            await conn.executemany("""
                INSERT INTO demo_domain_test 
                (name, qualified_name, version, completion, priority)
                VALUES ($1, $2, $3, $4, $5)
            """, valid_data)
            
            print(f"   ✅ 插入有效数据 {len(valid_data)} 条")
            
            # 4. 测试无效数据（会失败）
            print("3️⃣ 测试域约束违规...")
            
            invalid_tests = [
                # 无效的元类名称（小写开头）
                ("invalidName", "domain::InvalidName", "1.0.0", 50.0, 5, "元类名称必须大写开头"),
                
                # 无效的限定名称（缺少::）
                ("ValidName", "invalid_qualified_name", "1.0.0", 50.0, 5, "限定名称格式错误"),
                
                # 无效的版本号
                ("ValidName", "domain::ValidName", "invalid_version", 50.0, 5, "版本号格式错误"),
                
                # 无效的百分比（超出范围）
                ("ValidName", "domain::ValidName", "1.0.0", 150.0, 5, "百分比超出范围"),
                
                # 无效的优先级（超出范围）
                ("ValidName", "domain::ValidName", "1.0.0", 50.0, 15, "优先级超出范围")
            ]
            
            for name, qname, version, completion, priority, error_desc in invalid_tests:
                try:
                    await conn.execute("""
                        INSERT INTO demo_domain_test 
                        (name, qualified_name, version, completion, priority)
                        VALUES ($1, $2, $3, $4, $5)
                    """, name, qname, version, completion, priority)
                    print(f"   ❌ 预期失败但成功了: {error_desc}")
                except Exception as e:
                    print(f"   ✅ 域约束生效: {error_desc}")
            
            # 5. 查询域信息
            domain_info = await conn.fetch("""
                SELECT 
                    t.typname as domain_name,
                    format_type(t.typbasetype, t.typtypmod) as base_type,
                    t.typdefault as default_value
                FROM pg_type t
                WHERE t.typtype = 'd'
                AND t.typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'core_schema')
                ORDER BY t.typname
            """)
            
            print("   📋 已创建的域类型:")
            for domain in domain_info:
                print(f"     - {domain['domain_name']}: {domain['base_type']} (默认: {domain['default_value'] or 'NULL'})")
    
    # ========== 4. FTS 演示 ==========
    
    async def demo_full_text_search(self):
        """演示全文搜索系统"""
        print("\n🔍 演示 FTS (全文搜索)")
        print("=" * 50)
        
        async with self.db_pool.acquire() as conn:
            # 1. 创建生物医学搜索配置
            print("1️⃣ 创建生物医学搜索配置...")
            
            try:
                await conn.execute("""
                    CREATE TEXT SEARCH CONFIGURATION IF NOT EXISTS biomedical (
                        PARSER = default
                    );
                    
                    ALTER TEXT SEARCH CONFIGURATION biomedical
                        ALTER MAPPING FOR asciiword, word, numword
                        WITH english_stem, simple;
                """)
                print("   ✅ 创建生物医学搜索配置成功")
            except Exception as e:
                print(f"   ⚠️ 搜索配置可能已存在: {e}")
            
            # 2. 创建智能搜索表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS demo_fts_test (
                    id SERIAL PRIMARY KEY,
                    title VARCHAR(255),
                    content TEXT,
                    category VARCHAR(100),
                    tags TEXT[],
                    
                    -- 搜索向量
                    search_vector_en tsvector GENERATED ALWAYS AS (
                        to_tsvector('english', coalesce(title, '') || ' ' || coalesce(content, ''))
                    ) STORED,
                    
                    search_vector_bio tsvector GENERATED ALWAYS AS (
                        to_tsvector('biomedical', coalesce(title, '') || ' ' || coalesce(content, ''))
                    ) STORED,
                    
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                -- 创建搜索索引
                CREATE INDEX IF NOT EXISTS idx_demo_fts_en 
                ON demo_fts_test USING GIN(search_vector_en);
                
                CREATE INDEX IF NOT EXISTS idx_demo_fts_bio 
                ON demo_fts_test USING GIN(search_vector_bio);
                
                TRUNCATE demo_fts_test;
            """)
            
            # 3. 插入测试文档
            fts_test_data = [
                (
                    'BRCA1 Gene Expression Analysis',
                    'Comprehensive analysis of BRCA1 gene expression in breast cancer patients. '
                    'The study investigates the correlation between BRCA1 mutations and treatment response. '
                    'Results show significant differences in protein expression levels.',
                    'genomics',
                    ['gene', 'BRCA1', 'cancer', 'expression', 'protein']
                ),
                (
                    'Protein-Protein Interaction Networks',
                    'Systematic analysis of protein-protein interactions in cellular pathways. '
                    'The research focuses on drug target identification and pathway analysis. '
                    'Machine learning approaches are used for prediction modeling.',
                    'proteomics',
                    ['protein', 'interaction', 'pathway', 'drug', 'target']
                ),
                (
                    'Cancer Drug Treatment Protocols',
                    'Clinical protocols for cancer drug administration and patient monitoring. '
                    'The guidelines include dosage calculations, side effect management, '
                    'and treatment efficacy evaluation methods.',
                    'clinical',
                    ['cancer', 'drug', 'treatment', 'clinical', 'patient']
                ),
                (
                    'Gene Therapy Approaches',
                    'Novel gene therapy techniques for treating inherited diseases. '
                    'The methodology involves CRISPR gene editing and viral vector delivery systems. '
                    'Clinical trials show promising therapeutic outcomes.',
                    'therapy',
                    ['gene', 'therapy', 'CRISPR', 'treatment', 'clinical']
                ),
                (
                    'Biomarker Discovery Platform',
                    'High-throughput platform for discovering biomarkers in cancer research. '
                    'The system integrates genomics, proteomics, and metabolomics data. '
                    'Statistical analysis reveals novel prognostic indicators.',
                    'biomarker',
                    ['biomarker', 'cancer', 'genomics', 'proteomics', 'analysis']
                )
            ]
            
            await conn.executemany("""
                INSERT INTO demo_fts_test (title, content, category, tags)
                VALUES ($1, $2, $3, $4)
            """, fts_test_data)
            
            print(f"   ✅ 插入搜索测试文档 {len(fts_test_data)} 条")
            
            # 4. 执行搜索测试
            print("2️⃣ 执行搜索测试...")
            
            search_queries = [
                "gene expression cancer",
                "protein interaction drug",
                "BRCA1 mutation analysis",
                "clinical treatment patient"
            ]
            
            for query in search_queries:
                print(f"\n   🔍 搜索: '{query}'")
                
                # 英文搜索
                en_results = await conn.fetch("""
                    SELECT 
                        title,
                        ts_rank(search_vector_en, plainto_tsquery('english', $1)) as rank_en,
                        ts_headline('english', content, plainto_tsquery('english', $1)) as snippet
                    FROM demo_fts_test
                    WHERE search_vector_en @@ plainto_tsquery('english', $1)
                    ORDER BY rank_en DESC
                    LIMIT 3
                """, query)
                
                # 生物医学搜索
                bio_results = await conn.fetch("""
                    SELECT 
                        title,
                        ts_rank(search_vector_bio, plainto_tsquery('biomedical', $1)) as rank_bio,
                        ts_headline('biomedical', content, plainto_tsquery('biomedical', $1)) as snippet
                    FROM demo_fts_test
                    WHERE search_vector_bio @@ plainto_tsquery('biomedical', $1)
                    ORDER BY rank_bio DESC
                    LIMIT 3
                """, query)
                
                print("     📊 英文搜索结果:")
                for result in en_results:
                    print(f"       - {result['title']} (相关性: {float(result['rank_en']):.3f})")
                
                print("     📊 生物医学搜索结果:")
                for result in bio_results:
                    print(f"       - {result['title']} (相关性: {float(result['rank_bio']):.3f})")
            
            # 5. 高级搜索功能
            print("3️⃣ 高级搜索功能...")
            
            # 创建高级搜索函数
            await conn.execute("""
                CREATE OR REPLACE FUNCTION advanced_search(
                    search_query TEXT,
                    category_filter TEXT DEFAULT NULL,
                    min_rank REAL DEFAULT 0.1
                ) RETURNS TABLE(
                    id INTEGER,
                    title VARCHAR(255),
                    category VARCHAR(100),
                    rank REAL,
                    snippet TEXT,
                    matched_tags TEXT[]
                ) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        t.id,
                        t.title,
                        t.category,
                        ts_rank_cd(t.search_vector_bio, query) as rank,
                        ts_headline('biomedical', t.content, query) as snippet,
                        array_agg(tag) FILTER (WHERE tag ILIKE '%' || search_query || '%') as matched_tags
                    FROM 
                        demo_fts_test t,
                        plainto_tsquery('biomedical', search_query) query,
                        unnest(t.tags) as tag
                    WHERE 
                        t.search_vector_bio @@ query
                        AND ts_rank_cd(t.search_vector_bio, query) >= min_rank
                        AND (category_filter IS NULL OR t.category = category_filter)
                    GROUP BY t.id, t.title, t.category, t.search_vector_bio, query, t.content
                    ORDER BY rank DESC;
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # 使用高级搜索
            advanced_results = await conn.fetch("""
                SELECT * FROM advanced_search('gene cancer', NULL, 0.01)
            """)
            
            print("   🎯 高级搜索结果:")
            for result in advanced_results:
                tags = result['matched_tags'] if result['matched_tags'] else []
                print(f"     - {result['title']} ({result['category']})")
                print(f"       相关性: {float(result['rank']):.3f}, 匹配标签: {tags}")
    
    # ========== 综合演示 ==========
    
    async def demo_comprehensive(self):
        """综合演示所有组件"""
        print("\n🎉 PostgreSQL高级组件综合演示")
        print("=" * 60)
        
        try:
            await self.initialize()
            
            print("开始演示PostgreSQL的4大高级组件:")
            print("1. Aggregates - 自定义聚合函数")
            print("2. Collations - 智能排序规则")
            print("3. Domains - 业务约束类型")
            print("4. FTS系统 - 全文搜索引擎")
            
            await self.demo_aggregates()
            await asyncio.sleep(1)
            
            await self.demo_collations()
            await asyncio.sleep(1)
            
            await self.demo_domains()
            await asyncio.sleep(1)
            
            await self.demo_full_text_search()
            
            print("\n🎉 PostgreSQL高级组件演示完成！")
            print("\n📊 功能总结:")
            print("   ✅ Aggregates: 自定义数据聚合，深度分析能力")
            print("   ✅ Collations: 智能排序，国际化支持")
            print("   ✅ Domains: 数据约束，业务规则保证")
            print("   ✅ FTS系统: 语义搜索，智能检索")
            print("\n💡 这些功能为UML元类管理系统提供了强大的数据处理和检索能力！")
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            logger.exception("演示异常")
        
        finally:
            await self.cleanup()
    
    async def run_single_component(self, component: str):
        """运行单个组件演示"""
        component_map = {
            'aggregates': self.demo_aggregates,
            'collations': self.demo_collations,
            'domains': self.demo_domains,
            'fts': self.demo_full_text_search
        }
        
        if component in component_map:
            await self.initialize()
            try:
                await component_map[component]()
            finally:
                await self.cleanup()
        else:
            print(f"❌ 未知组件: {component}")
            print(f"可用组件: {', '.join(component_map.keys())}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PostgreSQL高级组件演示')
    parser.add_argument('--component', type=str,
                       choices=['aggregates', 'collations', 'domains', 'fts'],
                       help='运行特定组件演示')
    parser.add_argument('--debug', action='store_true', help='启用调试日志')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    demo = PostgreSQLComponentsDemo()
    
    async def run_demo():
        try:
            if args.component:
                await demo.run_single_component(args.component)
            else:
                await demo.demo_comprehensive()
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断演示")
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            if args.debug:
                logger.exception("详细错误信息")
            sys.exit(1)
    
    # 运行演示
    asyncio.run(run_demo())

if __name__ == "__main__":
    main() 