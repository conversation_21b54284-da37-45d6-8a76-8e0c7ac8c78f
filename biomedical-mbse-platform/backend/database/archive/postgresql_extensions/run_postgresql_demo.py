#!/usr/bin/env python3
"""
PostgreSQL增强功能综合演示

演示如何使用PostgreSQL高级特性来实现UML元类管理：
1. 表继承（Table Inheritance）
2. 物化视图（Materialized Views）
3. 分区表（Partitioning）
4. 全文搜索（Full Text Search）
5. 自定义类型（Custom Types）
6. 存储过程（Stored Procedures）
7. 触发器（Triggers）
8. JSONB操作（JSONB Operations）

使用方法:
    python run_postgresql_demo.py [--feature <feature_name>] [--setup-only] [--cleanup]
"""

import asyncio
import argparse
import logging
import json
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
from datetime import datetime, timedelta
from typing import Dict, List, Any

import asyncpg

from postgresql_enhanced_metaclass_manager import (
    PostgreSQLEnhancedMetaclassManager,
    create_postgresql_enhanced_manager,
    PostgreSQLFeature,
    MetaclassInheritanceInfo
)

logger = logging.getLogger(__name__)

class PostgreSQLDemoRunner:
    """PostgreSQL功能演示运行器"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'biomedical_mbse_platform',
            'user': 'mbse_user',
            'password': 'mbse_pass_2024'
        }
        self.db_pool = None
        self.manager = None
        
        # 演示数据
        self.demo_metaclasses = [
            {
                'name': 'BiomedicineEntity',
                'type': 'base',
                'properties': {
                    'category': 'clinical',
                    'domain': 'healthcare',
                    'version': '1.0'
                },
                'documentation': 'Base class for all biomedical entities including patients, treatments, and drugs'
            },
            {
                'name': 'Patient',
                'type': 'class',
                'parent': 'BiomedicineEntity',
                'properties': {
                    'category': 'patient',
                    'identifier_required': True
                },
                'documentation': 'Patient entity representing individuals receiving medical care'
            },
            {
                'name': 'Treatment',
                'type': 'activity',
                'parent': 'BiomedicineEntity',
                'properties': {
                    'category': 'treatment',
                    'duration_tracked': True
                },
                'documentation': 'Treatment protocols and therapeutic interventions'
            },
            {
                'name': 'Drug',
                'type': 'class',
                'parent': 'BiomedicineEntity',
                'properties': {
                    'category': 'pharmaceutical',
                    'fda_approved': False
                },
                'documentation': 'Pharmaceutical drugs and medications used in treatments'
            },
            {
                'name': 'GeneExpression',
                'type': 'class',
                'parent': 'BiomedicineEntity',
                'properties': {
                    'category': 'genomics',
                    'expression_unit': 'FPKM'
                },
                'documentation': 'Gene expression data from RNA sequencing experiments'
            }
        ]
    
    async def initialize(self):
        """初始化演示环境"""
        print("🚀 初始化PostgreSQL增强功能演示环境...")
        
        # 创建数据库连接池
        database_url = f"postgresql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
        self.db_pool = await asyncpg.create_pool(database_url, min_size=2, max_size=10)
        
        # 初始化增强管理器
        self.manager = await create_postgresql_enhanced_manager(self.db_pool)
        
        print("✅ PostgreSQL增强功能演示环境初始化完成！")
    
    async def cleanup(self):
        """清理演示环境"""
        print("\n🧹 清理演示环境...")
        
        if self.db_pool:
            async with self.db_pool.acquire() as conn:
                # 清理演示数据
                await conn.execute("DELETE FROM core_schema.base_metaclass WHERE name LIKE 'Demo%'")
                await conn.execute("DELETE FROM core_schema.metaclass_tags WHERE metaclass_id IN (SELECT metaclass_id FROM core_schema.base_metaclass WHERE name LIKE 'Demo%')")
                
                # 删除演示表（如果存在）
                demo_tables = ['demo_patient_entity', 'demo_treatment_entity', 'demo_gene_expression']
                for table in demo_tables:
                    try:
                        await conn.execute(f"DROP TABLE IF EXISTS core_schema.{table} CASCADE")
                    except Exception as e:
                        logger.warning(f"清理表 {table} 失败: {e}")
                
                print("✅ 演示环境清理完成")
            
            await self.db_pool.close()
    
    async def demo_table_inheritance(self):
        """演示表继承功能"""
        print("\n🧬 演示表继承功能")
        print("=" * 50)
        
        # 1. 创建基础继承元类表
        success = await self.manager.create_inherited_metaclass(
            'class', 'biomedicine_entity',
            {
                'entity_category': {'type': 'VARCHAR(100)', 'required': True},
                'identifier': {'type': 'VARCHAR(255)', 'required': True},
                'metadata_json': {'type': 'JSONB', 'default': '{}'}
            }
        )
        print(f"   ✅ 创建基础继承元类表: {'成功' if success else '失败'}")
        
        # 2. 创建患者专用继承表
        success = await self.manager.create_inherited_metaclass(
            'biomedicine_entity', 'patient_entity',
            {
                'medical_record_number': {'type': 'VARCHAR(50)', 'required': True},
                'birth_date': {'type': 'DATE'},
                'gender': {'type': 'VARCHAR(10)'},
                'primary_diagnosis': {'type': 'TEXT'}
            }
        )
        print(f"   ✅ 创建患者专用继承表: {'成功' if success else '失败'}")
        
        # 3. 测试多态查询
        async with self.db_pool.acquire() as conn:
            # 插入测试数据
            await conn.execute("""
                INSERT INTO core_schema.biomedicine_entity_metaclass 
                (name, qualified_name, entity_category, identifier)
                VALUES 
                ('DemoEntity1', 'demo::entity1', 'base', 'BE001'),
                ('DemoEntity2', 'demo::entity2', 'base', 'BE002')
            """)
            
            await conn.execute("""
                INSERT INTO core_schema.patient_entity_metaclass 
                (name, qualified_name, entity_category, identifier, medical_record_number, gender)
                VALUES 
                ('DemoPatient1', 'demo::patient1', 'patient', 'PT001', 'MR2024001', 'F'),
                ('DemoPatient2', 'demo::patient2', 'patient', 'PT002', 'MR2024002', 'M')
            """)
            
            # 多态查询 - 查询所有继承的实体
            rows = await conn.fetch("""
                SELECT name, qualified_name, entity_category, 
                       pg_typeof(tableoid)::text as actual_table
                FROM core_schema.base_metaclass
                WHERE name LIKE 'Demo%'
                ORDER BY name
            """)
            
            print(f"   📊 多态查询结果 ({len(rows)}条记录):")
            for row in rows:
                table_type = row['actual_table'].split('.')[-1] if '.' in row['actual_table'] else row['actual_table']
                print(f"     - {row['name']} ({row['entity_category']}) -> {table_type}")
    
    async def demo_materialized_views(self):
        """演示物化视图功能"""
        print("\n📊 演示物化视图功能")
        print("=" * 50)
        
        async with self.db_pool.acquire() as conn:
            # 1. 检查物化视图是否存在
            views = await conn.fetch("""
                SELECT schemaname, matviewname
                FROM pg_matviews
                WHERE schemaname = 'core_schema'
                AND matviewname LIKE '%metaclass%'
            """)
            
            print(f"   📋 现有物化视图 ({len(views)}个):")
            for view in views:
                print(f"     - {view['schemaname']}.{view['matviewname']}")
            
            # 2. 刷新物化视图
            try:
                await self.manager.refresh_materialized_views()
                print("   🔄 物化视图刷新成功")
            except Exception as e:
                print(f"   ❌ 物化视图刷新失败: {e}")
            
            # 3. 查询继承层次视图
            try:
                hierarchy_rows = await conn.fetch("""
                    SELECT name, depth, inheritance_path
                    FROM core_schema.metaclass_hierarchy_view
                    WHERE name LIKE 'Demo%' OR 'Demo%' = ANY(path)
                    ORDER BY depth, name
                    LIMIT 10
                """)
                
                print(f"   🌳 继承层次视图结果 ({len(hierarchy_rows)}条):")
                for row in hierarchy_rows:
                    indent = "  " * (row['depth'] + 1)
                    print(f"     {indent}- {row['name']} (深度: {row['depth']}, 路径: {row['inheritance_path']})")
                    
            except Exception as e:
                print(f"   ⚠️ 继承层次视图查询跳过 (视图可能不存在): {e}")
            
            # 4. 查询使用统计视图
            try:
                stats_rows = await conn.fetch("""
                    SELECT metaclass_name, instance_count, domain_count
                    FROM core_schema.metaclass_usage_stats
                    WHERE metaclass_name LIKE 'Demo%'
                    ORDER BY instance_count DESC
                    LIMIT 5
                """)
                
                print(f"   📈 使用统计视图结果 ({len(stats_rows)}条):")
                for row in stats_rows:
                    print(f"     - {row['metaclass_name']}: {row['instance_count']}实例, {row['domain_count']}领域")
                    
            except Exception as e:
                print(f"   ⚠️ 使用统计视图查询跳过 (视图可能不存在): {e}")
    
    async def demo_partitioned_tables(self):
        """演示分区表功能"""
        print("\n🗂️ 演示分区表功能")
        print("=" * 50)
        
        async with self.db_pool.acquire() as conn:
            # 1. 检查分区表状态
            partitions = await conn.fetch("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables
                WHERE schemaname = 'core_schema'
                AND tablename LIKE '%partitioned%'
                ORDER BY tablename
            """)
            
            print(f"   📋 现有分区表 ({len(partitions)}个):")
            for partition in partitions:
                print(f"     - {partition['schemaname']}.{partition['tablename']} ({partition['size']})")
            
            # 2. 插入测试数据到分区表
            try:
                test_data = []
                for i in range(10):
                    created_at = datetime.now() - timedelta(days=i*10)
                    test_data.append((
                        f"TestModel_{i}",
                        'Patient',
                        json.dumps({'patient_id': f'P{i:03d}', 'test_data': True}),
                        created_at,
                        'demo_domain',
                        f'demo_file_{i}.xmi'
                    ))
                
                await conn.executemany("""
                    INSERT INTO core_schema.model_instances_partitioned 
                    (model_name, metaclass_type, instance_data, created_at, domain_name, file_source)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, test_data)
                
                print(f"   ✅ 插入测试数据 {len(test_data)} 条")
                
                # 3. 查询分区数据
                recent_count = await conn.fetchval("""
                    SELECT COUNT(*) 
                    FROM core_schema.model_instances_partitioned
                    WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
                    AND model_name LIKE 'TestModel_%'
                """)
                
                print(f"   📊 最近30天数据: {recent_count}条")
                
                # 4. 按元类类型统计
                type_stats = await conn.fetch("""
                    SELECT metaclass_type, COUNT(*) as count
                    FROM core_schema.model_instances_partitioned
                    WHERE model_name LIKE 'TestModel_%'
                    GROUP BY metaclass_type
                    ORDER BY count DESC
                """)
                
                print(f"   📈 元类类型分布:")
                for stat in type_stats:
                    print(f"     - {stat['metaclass_type']}: {stat['count']}个实例")
                
            except Exception as e:
                print(f"   ⚠️ 分区表测试跳过 (表可能不存在): {e}")
    
    async def demo_full_text_search(self):
        """演示全文搜索功能"""
        print("\n🔍 演示全文搜索功能")
        print("=" * 50)
        
        # 1. 插入测试数据
        async with self.db_pool.acquire() as conn:
            test_metaclasses = [
                ('DemoPatientSearch', 'demo::patient_search', False, 
                 '{"category": "patient", "medical_specialty": "cardiology"}',
                 'Patient metaclass for cardiovascular treatments and cardiac monitoring'),
                ('DemoTreatmentSearch', 'demo::treatment_search', False,
                 '{"category": "treatment", "medical_specialty": "oncology"}',
                 'Treatment protocols for cancer therapy including chemotherapy and radiation'),
                ('DemoDrugSearch', 'demo::drug_search', False,
                 '{"category": "drug", "therapeutic_class": "antibiotic"}',
                 'Antibiotic medications for bacterial infections and antimicrobial therapy')
            ]
            
            for name, qualified_name, is_abstract, properties, documentation in test_metaclasses:
                await conn.execute("""
                    INSERT INTO core_schema.base_metaclass 
                    (name, qualified_name, is_abstract, properties, documentation)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (qualified_name) DO UPDATE SET
                        properties = EXCLUDED.properties,
                        documentation = EXCLUDED.documentation
                """, name, qualified_name, is_abstract, properties, documentation)
            
            print(f"   ✅ 插入搜索测试数据 {len(test_metaclasses)} 条")
        
        # 2. 测试语义搜索
        search_queries = [
            "patient cardiac",
            "cancer treatment therapy",
            "antibiotic bacterial infection",
            "medical cardiovascular"
        ]
        
        for query in search_queries:
            try:
                results = await self.manager.search_metaclasses(query, limit=5)
                print(f"   🔍 搜索 '{query}' 结果 ({len(results)}条):")
                
                for result in results:
                    print(f"     - {result['name']} (相关性: {result['relevance_score']:.3f})")
                    if result['snippet']:
                        snippet = result['snippet'][:100] + "..." if len(result['snippet']) > 100 else result['snippet']
                        print(f"       摘要: {snippet}")
                
            except Exception as e:
                print(f"   ⚠️ 搜索 '{query}' 失败: {e}")
    
    async def demo_custom_types(self):
        """演示自定义数据类型功能"""
        print("\n🎨 演示自定义数据类型功能")
        print("=" * 50)
        
        async with self.db_pool.acquire() as conn:
            # 1. 检查自定义类型
            custom_types = await conn.fetch("""
                SELECT typname, typtype
                FROM pg_type t
                JOIN pg_namespace n ON t.typnamespace = n.oid
                WHERE n.nspname = 'core_schema'
                AND typname IN ('metaclass_status', 'uml_visibility', 'metaclass_version')
            """)
            
            print(f"   📋 自定义类型 ({len(custom_types)}个):")
            for custom_type in custom_types:
                type_desc = {
                    'e': '枚举类型',
                    'c': '复合类型',
                    'b': '基础类型'
                }.get(custom_type['typtype'], '其他类型')
                print(f"     - {custom_type['typname']} ({type_desc})")
            
            # 2. 测试枚举类型使用
            try:
                await conn.execute("""
                    UPDATE core_schema.base_metaclass 
                    SET properties = properties || '{"status": "active"}'::jsonb
                    WHERE name LIKE 'Demo%'
                """)
                print("   ✅ 枚举类型测试完成")
            except Exception as e:
                print(f"   ⚠️ 枚举类型测试失败: {e}")
            
            # 3. 测试数组类型 - 标签管理
            try:
                demo_metaclasses = await conn.fetch("""
                    SELECT metaclass_id, name 
                    FROM core_schema.base_metaclass 
                    WHERE name LIKE 'Demo%'
                    LIMIT 3
                """)
                
                for metaclass in demo_metaclasses:
                    tags = ['demo', 'test', f"category_{metaclass['name'].lower()}"]
                    await self.manager.manage_metaclass_tags(str(metaclass['metaclass_id']), tags)
                
                print(f"   ✅ 为 {len(demo_metaclasses)} 个元类添加标签")
                
                # 搜索带特定标签的元类
                tagged_results = await self.manager.search_by_tags(['demo', 'test'])
                print(f"   🏷️ 标签搜索结果: {len(tagged_results)}个元类")
                
            except Exception as e:
                print(f"   ⚠️ 数组类型测试失败: {e}")
    
    async def demo_stored_procedures(self):
        """演示存储过程功能"""
        print("\n⚡ 演示存储过程功能")
        print("=" * 50)
        
        # 1. 测试动态表创建函数
        field_definitions = {
            'gene_symbol': {'type': 'VARCHAR(100)', 'required': True},
            'chromosome': {'type': 'VARCHAR(10)'},
            'start_position': {'type': 'BIGINT'},
            'end_position': {'type': 'BIGINT'},
            'expression_level': {'type': 'DECIMAL(10,4)'},
            'p_value': {'type': 'DECIMAL(20,10)'}
        }
        
        success = await self.manager.create_dynamic_metaclass_table(
            'gene_expression_demo', 'core_schema', field_definitions
        )
        print(f"   ✅ 动态创建表: {'成功' if success else '失败'}")
        
        # 2. 测试继承分析函数
        for metaclass_name in ['DemoPatientSearch', 'Patient', 'BiomedicineEntity']:
            try:
                analysis = await self.manager.get_inheritance_analysis(metaclass_name)
                print(f"   🌳 {metaclass_name} 继承分析:")
                print(f"     父类: {', '.join(analysis['parents']) if analysis['parents'] else '无'}")
                print(f"     子类: {', '.join(analysis['children']) if analysis['children'] else '无'}")
                print(f"     深度映射: {len(analysis['depth_map'])}层")
            except Exception as e:
                print(f"   ⚠️ {metaclass_name} 继承分析失败: {e}")
    
    async def demo_system_catalogs(self):
        """演示系统目录查询功能"""
        print("\n🔬 演示系统目录查询功能")
        print("=" * 50)
        
        # 1. 获取数据库元数据
        try:
            metadata = await self.manager.get_system_metadata()
            
            print(f"   📊 数据库元数据统计:")
            stats = metadata['statistics']
            print(f"     Schema数量: {stats['schema_count']}")
            print(f"     表数量: {stats['table_count']}")
            print(f"     有索引的表: {stats['indexed_tables']}")
            print(f"     有触发器的表: {stats['tables_with_triggers']}")
            
            print(f"   🏗️ 表继承关系 ({len(metadata['inheritance_relationships'])}个):")
            for rel in metadata['inheritance_relationships'][:5]:
                print(f"     {rel['child_table']} <- {rel['parent_table']} ({rel['schema_name']})")
            
        except Exception as e:
            print(f"   ⚠️ 系统元数据查询失败: {e}")
        
        # 2. 分析表结构
        try:
            table_analysis = await self.manager.analyze_table_structure('core_schema', 'base_metaclass')
            
            print(f"   🔍 base_metaclass 表结构分析:")
            print(f"     列数量: {len(table_analysis['columns'])}")
            print(f"     约束数量: {len(table_analysis['constraints'])}")
            
            # 显示主要列
            for col in table_analysis['columns'][:5]:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                print(f"     - {col['column_name']}: {col['data_type']} {nullable}")
                
        except Exception as e:
            print(f"   ⚠️ 表结构分析失败: {e}")
        
        # 3. 发现元类Schema
        try:
            schemas = await self.manager.discover_metaclass_schemas()
            
            print(f"   🔎 元类相关Schema发现 ({len(schemas)}个):")
            for schema_name, tables in schemas.items():
                print(f"     {schema_name}: {len(tables)}个表")
                for table in tables[:3]:  # 只显示前3个
                    print(f"       - {table['table_name']} ({table['table_type']})")
                    
        except Exception as e:
            print(f"   ⚠️ Schema发现失败: {e}")
    
    async def demo_event_system(self):
        """演示事件系统功能"""
        print("\n🔔 演示事件系统功能")
        print("=" * 50)
        
        # 定义事件处理器
        event_count = {'count': 0}
        
        async def demo_change_handler(change_data):
            """演示变更处理器"""
            event_count['count'] += 1
            operation = change_data['operation']
            
            if operation == 'INSERT':
                print(f"     📝 新元类创建: {change_data.get('metaclass_id', 'unknown')}")
            elif operation == 'UPDATE':
                print(f"     ✏️ 元类更新: {change_data.get('metaclass_id', 'unknown')}")
            elif operation == 'DELETE':
                print(f"     🗑️ 元类删除: {change_data.get('metaclass_id', 'unknown')}")
        
        # 1. 启动事件监听（在后台）
        try:
            print("   🔔 启动元类变更监听...")
            
            # 注册事件处理器
            # await self.manager.listen_for_metaclass_changes(demo_change_handler)
            
            # 由于listen_for_metaclass_changes是阻塞的，我们模拟一些变更操作
            async with self.db_pool.acquire() as conn:
                # 插入新元类
                await conn.execute("""
                    INSERT INTO core_schema.base_metaclass 
                    (name, qualified_name, documentation)
                    VALUES ('DemoEventTest', 'demo::event_test', 'Test metaclass for event demonstration')
                """)
                
                # 更新元类
                await conn.execute("""
                    UPDATE core_schema.base_metaclass 
                    SET documentation = 'Updated documentation for event test'
                    WHERE name = 'DemoEventTest'
                """)
                
                # 删除元类
                await conn.execute("""
                    DELETE FROM core_schema.base_metaclass 
                    WHERE name = 'DemoEventTest'
                """)
                
                print("   ✅ 模拟了插入、更新、删除操作")
                print("   💡 在实际应用中，这些操作会触发实时事件通知")
                
        except Exception as e:
            print(f"   ⚠️ 事件系统演示失败: {e}")
    
    async def run_feature_demo(self, feature: str):
        """运行特定功能演示"""
        feature_map = {
            'inheritance': self.demo_table_inheritance,
            'materialized_views': self.demo_materialized_views,
            'partitioning': self.demo_partitioned_tables,
            'full_text_search': self.demo_full_text_search,
            'custom_types': self.demo_custom_types,
            'stored_procedures': self.demo_stored_procedures,
            'system_catalogs': self.demo_system_catalogs,
            'event_system': self.demo_event_system
        }
        
        if feature in feature_map:
            await feature_map[feature]()
        else:
            print(f"❌ 未知功能: {feature}")
            print(f"可用功能: {', '.join(feature_map.keys())}")
    
    async def run_comprehensive_demo(self):
        """运行完整功能演示"""
        print("🎉 PostgreSQL高级功能综合演示")
        print("=" * 60)
        
        try:
            await self.initialize()
            
            # 按顺序运行所有演示
            await self.demo_table_inheritance()
            await asyncio.sleep(1)
            
            await self.demo_materialized_views()
            await asyncio.sleep(1)
            
            await self.demo_partitioned_tables()
            await asyncio.sleep(1)
            
            await self.demo_full_text_search()
            await asyncio.sleep(1)
            
            await self.demo_custom_types()
            await asyncio.sleep(1)
            
            await self.demo_stored_procedures()
            await asyncio.sleep(1)
            
            await self.demo_system_catalogs()
            await asyncio.sleep(1)
            
            await self.demo_event_system()
            
            print("\n🎉 PostgreSQL高级功能演示完成！")
            print("✅ 展现了数据库层面的强大功能")
            print("✅ 元类管理的企业级解决方案")
            print("✅ 高性能、可扩展的架构设计")
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            logger.exception("演示异常")
        
        finally:
            print("\n📊 演示总结:")
            print("   - 表继承: UML元类继承的直接映射")
            print("   - 物化视图: 复杂查询的高性能缓存")
            print("   - 分区表: 大规模数据的有效管理")
            print("   - 全文搜索: 智能的语义检索能力")
            print("   - 自定义类型: 领域特定的数据安全")
            print("   - 存储过程: 服务器端的业务逻辑")
            print("   - 系统目录: 完整的元数据自省")
            print("   - 事件系统: 实时的变更响应机制")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PostgreSQL增强功能演示')
    parser.add_argument('--feature', type=str, 
                       choices=['inheritance', 'materialized_views', 'partitioning', 
                               'full_text_search', 'custom_types', 'stored_procedures', 
                               'system_catalogs', 'event_system'],
                       help='运行特定功能演示')
    parser.add_argument('--setup-only', action='store_true', help='仅初始化环境')
    parser.add_argument('--cleanup', action='store_true', help='清理演示环境')
    parser.add_argument('--debug', action='store_true', help='启用调试日志')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    runner = PostgreSQLDemoRunner()
    
    async def run_demo():
        try:
            if args.cleanup:
                await runner.initialize()
                await runner.cleanup()
                return
            
            if args.setup_only:
                await runner.initialize()
                print("✅ 环境初始化完成")
                return
            
            if args.feature:
                await runner.initialize()
                await runner.run_feature_demo(args.feature)
                await runner.cleanup()
            else:
                await runner.run_comprehensive_demo()
                await runner.cleanup()
                
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断演示")
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            if args.debug:
                logger.exception("详细错误信息")
            sys.exit(1)
    
    # 运行演示
    asyncio.run(run_demo())

if __name__ == "__main__":
    main() 