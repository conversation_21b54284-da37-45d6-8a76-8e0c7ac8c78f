"""
UML模型实例解析器

基于已注册的UML元类和Profile定义，解析工程XMI文件并生成对应的模型实例数据
将XMI中的模型元素映射到动态生成的Element类型表中

核心功能：
1. 解析XMI模型实例（与元模型定义区分）
2. 识别并映射到已注册的元类Schema
3. 处理Stereotype应用和Tagged Values
4. 建立模型实例间的关系
5. 支持增量更新和版本控制
"""

import asyncio
import json
import logging
import xml.etree.ElementTree as ET
import uuid
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timezone
from dataclasses import dataclass, field
from enum import Enum

import asyncpg

# 导入核心模块和元类生成器 - 修复引用路径
# 注意：core目录已不存在，使用标准库替代
# from ..core.parsing.xml_parser import UnifiedXMLParser
# from ..core.models.element import Element, ElementType, Namespace, SemanticInfo

# 使用标准XML解析器
import xml.etree.ElementTree as ET

# 修复引用路径
import sys
import os
# 引用同级archive目录下的文件
from uml_metaclass_generator import UMLMetaclassGenerator, ElementTypeSchema

# 引用上级domain_managers目录
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'domain_managers'))
from core_domain_manager import CoreDomainManager, ElementMetadata

logger = logging.getLogger(__name__)

class InstanceType(Enum):
    """实例类型枚举"""
    MODEL_ELEMENT = "model_element"
    STEREOTYPE_APPLICATION = "stereotype_application"
    TAGGED_VALUE = "tagged_value"
    RELATIONSHIP = "relationship"

@dataclass
class ModelInstance:
    """模型实例数据"""
    instance_id: str
    xmi_id: str
    metaclass_type: str
    stereotype_name: Optional[str] = None
    element_name: str = ""
    qualified_name: str = ""
    properties: Dict[str, Any] = field(default_factory=dict)
    tagged_values: Dict[str, Any] = field(default_factory=dict)
    relationships: List[str] = field(default_factory=list)
    parent_id: Optional[str] = None
    children_ids: List[str] = field(default_factory=list)
    documentation: str = ""
    source_file: str = ""
    model_version: str = "1.0"

@dataclass
class StereotypeApplication:
    """Stereotype应用数据"""
    application_id: str
    base_element_id: str
    stereotype_name: str
    profile_uri: str
    tagged_values: Dict[str, Any] = field(default_factory=dict)
    applied_at: Optional[datetime] = None

@dataclass
class InstanceParsingResult:
    """实例解析结果"""
    success: bool
    model_name: str
    instances_created: int = 0
    stereotypes_applied: int = 0
    relationships_established: int = 0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    instance_summary: Dict[str, int] = field(default_factory=dict)

class UMLInstanceParser:
    """UML模型实例解析器"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.core_manager = CoreDomainManager(db_pool)
        self.metaclass_generator = UMLMetaclassGenerator(db_pool)
        # 移除xml_parser引用，使用简化的解析方法
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 解析状态存储
        self.model_instances: Dict[str, ModelInstance] = {}
        self.stereotype_applications: Dict[str, StereotypeApplication] = {}
        self.registered_schemas: Dict[str, ElementTypeSchema] = {}
        self.xmi_id_mapping: Dict[str, str] = {}  # XMI ID到内部ID的映射
        
        # 解析配置
        self.current_model_name = ""
        self.current_model_version = "1.0"
        self.source_file_path = ""
    
    async def initialize(self):
        """初始化解析器"""
        await self.core_manager.initialize()
        await self.metaclass_generator.initialize()
        
        # 加载已注册的Schema信息
        await self._load_registered_schemas()
        
        self.logger.info("UML实例解析器初始化完成")
    
    async def parse_model_xmi(self, xmi_content: str, model_name: str, 
                            source_file: str = "", model_version: str = "1.0") -> InstanceParsingResult:
        """
        解析模型XMI文件
        
        Args:
            xmi_content: XMI文件内容
            model_name: 模型名称
            source_file: 源文件路径
            model_version: 模型版本
            
        Returns:
            InstanceParsingResult: 解析结果
        """
        try:
            self.logger.info(f"开始解析模型实例: {model_name}")
            
            # 设置解析上下文
            self.current_model_name = model_name
            self.current_model_version = model_version
            self.source_file_path = source_file
            
            # 重置解析状态
            self._reset_parsing_state()
            
            # 1. 解析XML根元素（简化版本）
            root = ET.fromstring(xmi_content)
            
            # 2. 创建简化的解析结果
            parse_result = {
                'success': True,
                'data': {
                    'metadata_store': self._build_simple_metadata_store(root),
                    'parent_child_map': self._build_parent_child_map(root),
                    'references_to_map': self._build_references_map(root)
                }
            }
            
            # 3. 提取模型实例
            instances_count = await self._extract_model_instances(root, parse_result)
            
            # 4. 处理Stereotype应用
            stereotypes_count = await self._process_stereotype_applications(root, parse_result)
            
            # 5. 建立关系
            relationships_count = await self._establish_relationships(root, parse_result)
            
            # 6. 存储到数据库
            storage_success = await self._store_instances_to_database()
            
            if storage_success:
                result = InstanceParsingResult(
                    success=True,
                    model_name=model_name,
                    instances_created=instances_count,
                    stereotypes_applied=stereotypes_count,
                    relationships_established=relationships_count,
                    instance_summary=self._generate_instance_summary()
                )
                self.logger.info(f"✅ 模型实例解析完成: {model_name}")
                return result
            else:
                return InstanceParsingResult(
                    success=False,
                    model_name=model_name,
                    errors=["数据库存储失败"]
                )
                
        except Exception as e:
            self.logger.error(f"模型实例解析失败: {e}")
            return InstanceParsingResult(
                success=False,
                model_name=model_name,
                errors=[str(e)]
            )
    
    async def _load_registered_schemas(self):
        """加载已注册的Schema信息"""
        try:
            # 从数据库加载已生成的Schema
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT type_id, type_name, table_name, base_metaclass, 
                           stereotype_name, field_definitions, relationship_definitions
                    FROM core_schema.generated_element_schemas
                """)
                
                for row in rows:
                    schema = ElementTypeSchema(
                        type_id=row['type_id'],
                        type_name=row['type_name'],
                        table_name=row['table_name'],
                        base_metaclass=row['base_metaclass'],
                        stereotype_name=row['stereotype_name'],
                        field_definitions=json.loads(row['field_definitions']) if row['field_definitions'] else {},
                        relationship_definitions=json.loads(row['relationship_definitions']) if row['relationship_definitions'] else {}
                    )
                    
                    # 以多种键存储，便于查找
                    self.registered_schemas[schema.type_id] = schema
                    self.registered_schemas[schema.base_metaclass] = schema
                    if schema.stereotype_name:
                        self.registered_schemas[schema.stereotype_name] = schema
                
                self.logger.info(f"加载了 {len(rows)} 个已注册的Schema")
                
        except Exception as e:
            self.logger.error(f"加载Schema失败: {e}")
    
    def _reset_parsing_state(self):
        """重置解析状态"""
        self.model_instances.clear()
        self.stereotype_applications.clear()
        self.xmi_id_mapping.clear()
    
    async def _extract_model_instances(self, root: ET.Element, 
                                     parse_result: Dict[str, Any]) -> int:
        """提取模型实例"""
        instances_count = 0
        metadata_store = parse_result['data']['metadata_store']
        
        for element_id, metadata in metadata_store.items():
            try:
                # 查找对应的XML元素
                xml_element = self._find_xml_element_by_id(root, element_id)
                if xml_element is None:
                    continue
                
                # 确定元类类型
                metaclass_type = self._determine_metaclass_type(xml_element, metadata)
                if not metaclass_type:
                    continue
                
                # 检查是否有对应的Schema
                schema = self._find_schema_for_metaclass(metaclass_type)
                if not schema:
                    self.logger.warning(f"未找到元类 {metaclass_type} 对应的Schema")
                    continue
                
                # 创建模型实例
                instance = await self._create_model_instance(
                    element_id, xml_element, metadata, metaclass_type, schema
                )
                
                if instance:
                    self.model_instances[instance.instance_id] = instance
                    self.xmi_id_mapping[instance.xmi_id] = instance.instance_id
                    instances_count += 1
                    
            except Exception as e:
                self.logger.error(f"提取实例失败 {element_id}: {e}")
        
        self.logger.info(f"提取了 {instances_count} 个模型实例")
        return instances_count
    
    async def _process_stereotype_applications(self, root: ET.Element, 
                                             parse_result: Dict[str, Any]) -> int:
        """处理Stereotype应用"""
        stereotypes_count = 0
        
        # 查找Stereotype应用
        for stereotype_elem in root.iter():
            if self._is_stereotype_application(stereotype_elem):
                try:
                    application = await self._create_stereotype_application(stereotype_elem)
                    if application:
                        self.stereotype_applications[application.application_id] = application
                        
                        # 更新对应的模型实例
                        await self._apply_stereotype_to_instance(application)
                        stereotypes_count += 1
                        
                except Exception as e:
                    self.logger.error(f"处理Stereotype应用失败: {e}")
        
        self.logger.info(f"处理了 {stereotypes_count} 个Stereotype应用")
        return stereotypes_count
    
    async def _establish_relationships(self, root: ET.Element, 
                                     parse_result: Dict[str, Any]) -> int:
        """建立关系"""
        relationships_count = 0
        
        # 基于parent_child_map建立父子关系
        parent_child_map = parse_result['data']['parent_child_map']
        for parent_id, children_ids in parent_child_map.items():
            if parent_id in self.xmi_id_mapping:
                parent_instance_id = self.xmi_id_mapping[parent_id]
                parent_instance = self.model_instances.get(parent_instance_id)
                
                if parent_instance:
                    for child_id in children_ids:
                        if child_id in self.xmi_id_mapping:
                            child_instance_id = self.xmi_id_mapping[child_id]
                            child_instance = self.model_instances.get(child_instance_id)
                            
                            if child_instance:
                                # 建立父子关系
                                parent_instance.children_ids.append(child_instance_id)
                                child_instance.parent_id = parent_instance_id
                                relationships_count += 1
        
        # 处理引用关系
        references_map = parse_result['data']['references_to_map']
        for source_id, target_ids in references_map.items():
            if source_id in self.xmi_id_mapping:
                source_instance_id = self.xmi_id_mapping[source_id]
                source_instance = self.model_instances.get(source_instance_id)
                
                if source_instance:
                    for target_id in target_ids:
                        if target_id in self.xmi_id_mapping:
                            target_instance_id = self.xmi_id_mapping[target_id]
                            source_instance.relationships.append(target_instance_id)
                            relationships_count += 1
        
        self.logger.info(f"建立了 {relationships_count} 个关系")
        return relationships_count
    
    async def _create_model_instance(self, element_id: str, xml_element: ET.Element, 
                                   metadata: Dict[str, Any], metaclass_type: str,
                                   schema: ElementTypeSchema) -> Optional[ModelInstance]:
        """创建模型实例"""
        try:
            # 生成实例ID
            instance_id = str(uuid.uuid4())
            xmi_id = xml_element.get('{http://www.omg.org/XMI}id') or xml_element.get('id') or element_id
            
            # 提取基本信息
            element_name = xml_element.get('name', '')
            qualified_name = self._build_qualified_name(xml_element, metadata)
            
            # 提取属性
            properties = {}
            for attr_name, attr_value in xml_element.attrib.items():
                # 跳过命名空间属性
                if attr_name.startswith('{') or attr_name in ['id', 'name']:
                    continue
                properties[attr_name] = attr_value
            
            # 提取文档
            documentation = self._extract_documentation(xml_element)
            
            return ModelInstance(
                instance_id=instance_id,
                xmi_id=xmi_id,
                metaclass_type=metaclass_type,
                element_name=element_name,
                qualified_name=qualified_name,
                properties=properties,
                documentation=documentation,
                source_file=self.source_file_path,
                model_version=self.current_model_version
            )
            
        except Exception as e:
            self.logger.error(f"创建模型实例失败: {e}")
            return None
    
    async def _create_stereotype_application(self, stereotype_elem: ET.Element) -> Optional[StereotypeApplication]:
        """创建Stereotype应用"""
        try:
            # 获取应用信息
            application_id = str(uuid.uuid4())
            base_element_ref = stereotype_elem.get('base_Element') or stereotype_elem.get('base_Class')
            
            if not base_element_ref:
                return None
            
            # 从引用中提取base element ID
            base_element_id = base_element_ref.split('#')[-1] if '#' in base_element_ref else base_element_ref
            
            # 确定Stereotype名称
            stereotype_name = stereotype_elem.tag.split('}')[-1] if '}' in stereotype_elem.tag else stereotype_elem.tag
            
            # 提取Profile URI
            profile_uri = self._extract_profile_uri(stereotype_elem)
            
            # 提取Tagged Values
            tagged_values = {}
            for attr_name, attr_value in stereotype_elem.attrib.items():
                if not attr_name.startswith('base_') and not attr_name.startswith('{'):
                    tagged_values[attr_name] = attr_value
            
            return StereotypeApplication(
                application_id=application_id,
                base_element_id=base_element_id,
                stereotype_name=stereotype_name,
                profile_uri=profile_uri,
                tagged_values=tagged_values,
                applied_at=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            self.logger.error(f"创建Stereotype应用失败: {e}")
            return None
    
    async def _apply_stereotype_to_instance(self, application: StereotypeApplication):
        """将Stereotype应用到实例"""
        # 查找对应的模型实例
        base_instance_id = self.xmi_id_mapping.get(application.base_element_id)
        if base_instance_id and base_instance_id in self.model_instances:
            instance = self.model_instances[base_instance_id]
            instance.stereotype_name = application.stereotype_name
            instance.tagged_values.update(application.tagged_values)
    
    async def _store_instances_to_database(self) -> bool:
        """存储实例到数据库"""
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # 存储模型实例
                    for instance in self.model_instances.values():
                        await self._store_single_instance(conn, instance)
                    
                    # 存储Stereotype应用
                    for application in self.stereotype_applications.values():
                        await self._store_stereotype_application(conn, application)
            
            self.logger.info("所有实例存储完成")
            return True
            
        except Exception as e:
            self.logger.error(f"存储实例失败: {e}")
            return False
    
    async def _store_single_instance(self, conn: asyncpg.Connection, instance: ModelInstance):
        """存储单个实例"""
        try:
            # 查找对应的Schema
            schema = self._find_schema_for_metaclass(instance.metaclass_type)
            if not schema:
                self.logger.warning(f"未找到实例 {instance.instance_id} 的Schema")
                return
            
            # 创建Element元数据
            element_metadata = ElementMetadata(
                element_id=instance.instance_id,
                element_type=instance.metaclass_type,
                name=instance.element_name,
                qualified_name=instance.qualified_name,
                properties=instance.properties,
                parent_id=instance.parent_id,
                children_ids=instance.children_ids,
                documentation=instance.documentation,
                custom_attributes={
                    'xmi_id': instance.xmi_id,
                    'source_file': instance.source_file,
                    'model_version': instance.model_version,
                    'stereotype_name': instance.stereotype_name,
                    'tagged_values': instance.tagged_values
                }
            )
            
            # 使用CoreDomainManager存储
            await self.core_manager.register_element(element_metadata)
            
            # 如果有Stereotype，也存储到对应的Profile表
            if instance.stereotype_name:
                await self._store_to_profile_table(conn, instance, schema)
                
        except Exception as e:
            self.logger.error(f"存储实例失败 {instance.instance_id}: {e}")
    
    async def _store_to_profile_table(self, conn: asyncpg.Connection, 
                                    instance: ModelInstance, base_schema: ElementTypeSchema):
        """存储到Profile表"""
        try:
            # 查找Stereotype Schema
            stereotype_schema = self.registered_schemas.get(instance.stereotype_name)
            if not stereotype_schema:
                return
            
            # 构建插入数据
            insert_data = {
                'element_id': instance.instance_id,
                'stereotype_name': instance.stereotype_name,
                'base_element_id': instance.instance_id,
                'xmi_id': instance.xmi_id,
                'name': instance.element_name,
                'qualified_name': instance.qualified_name
            }
            
            # 添加Tagged Values
            for tag_name, tag_value in instance.tagged_values.items():
                field_name = f"tagged_{tag_name}"
                if field_name in stereotype_schema.field_definitions:
                    insert_data[field_name] = tag_value
            
            # 动态构建SQL
            columns = list(insert_data.keys())
            placeholders = [f"${i+1}" for i in range(len(columns))]
            values = list(insert_data.values())
            
            sql = f"""
                INSERT INTO {stereotype_schema.table_name} ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
                ON CONFLICT (element_id) DO UPDATE SET
                {', '.join([f"{col} = EXCLUDED.{col}" for col in columns[1:]])}
            """
            
            await conn.execute(sql, *values)
            
        except Exception as e:
            self.logger.error(f"存储Profile实例失败: {e}")
    
    async def _store_stereotype_application(self, conn: asyncpg.Connection, 
                                          application: StereotypeApplication):
        """存储Stereotype应用记录"""
        try:
            await conn.execute("""
                INSERT INTO core_schema.stereotype_applications (
                    application_id, base_element_id, stereotype_name, 
                    profile_uri, tagged_values, applied_at
                ) VALUES ($1, $2, $3, $4, $5, $6)
                ON CONFLICT (application_id) DO UPDATE SET
                tagged_values = EXCLUDED.tagged_values,
                applied_at = EXCLUDED.applied_at
            """, 
            application.application_id,
            application.base_element_id,
            application.stereotype_name,
            application.profile_uri,
            json.dumps(application.tagged_values),
            application.applied_at
            )
            
        except Exception as e:
            self.logger.error(f"存储Stereotype应用失败: {e}")
    
    # ========== 工具方法 ==========
    
    def _build_simple_metadata_store(self, root: ET.Element) -> Dict[str, Dict[str, Any]]:
        """构建简化的元数据存储"""
        metadata_store = {}
        
        for elem in root.iter():
            elem_id = elem.get('{http://www.omg.org/XMI}id') or elem.get('id') or str(uuid.uuid4())
            
            metadata_store[elem_id] = {
                'tag': elem.tag,
                'element_type': elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag,
                'attributes': dict(elem.attrib),
                'parent_id': None  # 简化处理
            }
        
        return metadata_store
    
    def _build_parent_child_map(self, root: ET.Element) -> Dict[str, List[str]]:
        """构建父子关系映射"""
        parent_child_map = {}
        
        for elem in root.iter():
            parent_id = elem.get('{http://www.omg.org/XMI}id') or elem.get('id')
            if parent_id:
                children_ids = []
                for child in elem:
                    child_id = child.get('{http://www.omg.org/XMI}id') or child.get('id')
                    if child_id:
                        children_ids.append(child_id)
                if children_ids:
                    parent_child_map[parent_id] = children_ids
        
        return parent_child_map
    
    def _build_references_map(self, root: ET.Element) -> Dict[str, List[str]]:
        """构建引用关系映射"""
        references_map = {}
        
        for elem in root.iter():
            elem_id = elem.get('{http://www.omg.org/XMI}id') or elem.get('id')
            if elem_id:
                refs = []
                for attr_name, attr_value in elem.attrib.items():
                    if attr_name.endswith('Ref') or attr_name.endswith('_ref'):
                        refs.append(attr_value)
                if refs:
                    references_map[elem_id] = refs
        
        return references_map
    
    def _determine_metaclass_type(self, xml_element: ET.Element, metadata: Dict[str, Any]) -> Optional[str]:
        """确定元类类型"""
        # 从XML元素标签推断
        tag = xml_element.tag
        local_tag = tag.split('}')[-1] if '}' in tag else tag
        
        # 检查是否为标准UML元类
        if local_tag in ['Class', 'Package', 'Association', 'Component', 'Interface', 
                        'Property', 'Operation', 'Activity', 'State', 'UseCase', 'Actor']:
            return local_tag
        
        # 从xsi:type属性推断
        xsi_type = xml_element.get('{http://www.w3.org/2001/XMLSchema-instance}type')
        if xsi_type:
            return xsi_type.split(':')[-1] if ':' in xsi_type else xsi_type
        
        # 从语义分析结果推断
        semantic_info = metadata.get('semantic_analysis', {})
        domain_category = semantic_info.get('domain_category', '')
        if domain_category in ['requirement', 'block', 'activity']:
            return domain_category.title()
        
        return None
    
    def _find_schema_for_metaclass(self, metaclass_type: str) -> Optional[ElementTypeSchema]:
        """查找元类对应的Schema"""
        # 尝试不同的键格式
        possible_keys = [
            f"uml_{metaclass_type.lower()}_element",
            metaclass_type,
            metaclass_type.lower()
        ]
        
        for key in possible_keys:
            if key in self.registered_schemas:
                return self.registered_schemas[key]
        
        return None
    
    def _find_xml_element_by_id(self, root: ET.Element, element_id: str) -> Optional[ET.Element]:
        """根据ID查找XML元素"""
        for elem in root.iter():
            if (elem.get('{http://www.omg.org/XMI}id') == element_id or 
                elem.get('id') == element_id):
                return elem
        return None
    
    def _is_stereotype_application(self, element: ET.Element) -> bool:
        """判断是否为Stereotype应用"""
        # 检查是否有base_引用属性
        for attr_name in element.attrib:
            if attr_name.startswith('base_'):
                return True
        
        # 检查元素标签是否像Profile元素
        tag = element.tag.split('}')[-1] if '}' in element.tag else element.tag
        return not tag.startswith('uml:') and not tag in ['xmi:XMI', 'uml:Model', 'uml:Package']
    
    def _build_qualified_name(self, xml_element: ET.Element, metadata: Dict[str, Any]) -> str:
        """构建限定名称"""
        name = xml_element.get('name', '')
        if not name:
            return name
        
        # 尝试从parent构建路径
        parent_id = metadata.get('parent_id')
        if parent_id and parent_id in self.xmi_id_mapping:
            parent_instance_id = self.xmi_id_mapping[parent_id]
            parent_instance = self.model_instances.get(parent_instance_id)
            if parent_instance and parent_instance.qualified_name:
                return f"{parent_instance.qualified_name}::{name}"
        
        return name
    
    def _extract_documentation(self, element: ET.Element) -> str:
        """提取文档注释"""
        # 查找注释元素
        comments = element.findall('.//ownedComment') or element.findall('.//comment')
        if comments:
            return comments[0].get('body', '')
        return ""
    
    def _extract_profile_uri(self, stereotype_elem: ET.Element) -> str:
        """提取Profile URI"""
        # 从命名空间推断
        tag = stereotype_elem.tag
        if '}' in tag:
            namespace_uri = tag.split('}')[0][1:]  # 移除开头的{
            return namespace_uri
        
        return "http://profile/default"
    
    def _generate_instance_summary(self) -> Dict[str, int]:
        """生成实例摘要"""
        summary = {}
        
        # 按元类类型统计
        for instance in self.model_instances.values():
            metaclass_type = instance.metaclass_type
            summary[metaclass_type] = summary.get(metaclass_type, 0) + 1
        
        # 统计Stereotype应用
        stereotype_summary = {}
        for application in self.stereotype_applications.values():
            stereotype_name = application.stereotype_name
            stereotype_summary[f"stereotype_{stereotype_name}"] = stereotype_summary.get(f"stereotype_{stereotype_name}", 0) + 1
        
        summary.update(stereotype_summary)
        return summary
    
    # ========== 数据库表创建 ==========
    
    async def _create_instance_management_tables(self):
        """创建实例管理表"""
        async with self.db_pool.acquire() as conn:
            # Stereotype应用记录表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.stereotype_applications (
                    application_id VARCHAR(50) PRIMARY KEY,
                    base_element_id VARCHAR(50) NOT NULL,
                    stereotype_name VARCHAR(255) NOT NULL,
                    profile_uri VARCHAR(500) NOT NULL,
                    tagged_values JSONB DEFAULT '{}',
                    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    INDEX idx_stereotype_base_element (base_element_id),
                    INDEX idx_stereotype_name (stereotype_name)
                )
            """)
            
            # 模型版本管理表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.model_instances_registry (
                    registry_id VARCHAR(50) PRIMARY KEY,
                    model_name VARCHAR(255) NOT NULL,
                    model_version VARCHAR(50) NOT NULL,
                    source_file VARCHAR(500),
                    instances_count INTEGER DEFAULT 0,
                    stereotypes_count INTEGER DEFAULT 0,
                    parsing_result JSONB DEFAULT '{}',
                    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    UNIQUE(model_name, model_version)
                )
            """)
    
    # ========== 公共API ==========
    
    async def get_model_instances(self, model_name: str, 
                                metaclass_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取模型实例列表"""
        async with self.db_pool.acquire() as conn:
            if metaclass_type:
                rows = await conn.fetch("""
                    SELECT * FROM core_schema.element_metadata
                    WHERE custom_attributes->>'model_name' = $1
                    AND element_type = $2
                    ORDER BY qualified_name
                """, model_name, metaclass_type)
            else:
                rows = await conn.fetch("""
                    SELECT * FROM core_schema.element_metadata
                    WHERE custom_attributes->>'model_name' = $1
                    ORDER BY element_type, qualified_name
                """, model_name)
            
            return [dict(row) for row in rows]
    
    async def get_stereotype_applications(self, base_element_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取Stereotype应用列表"""
        async with self.db_pool.acquire() as conn:
            if base_element_id:
                rows = await conn.fetch("""
                    SELECT * FROM core_schema.stereotype_applications
                    WHERE base_element_id = $1
                    ORDER BY applied_at DESC
                """, base_element_id)
            else:
                rows = await conn.fetch("""
                    SELECT * FROM core_schema.stereotype_applications
                    ORDER BY stereotype_name, applied_at DESC
                """)
            
            return [dict(row) for row in rows]

# 便捷函数
async def create_uml_instance_parser(db_pool: asyncpg.Pool) -> UMLInstanceParser:
    """创建UML实例解析器"""
    parser = UMLInstanceParser(db_pool)
    await parser.initialize()
    return parser

async def main():
    """演示主函数"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'biomedical_mbse_platform',
        'user': 'mbse_user',
        'password': 'mbse_pass_2024'
    }
    
    database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    db_pool = await asyncpg.create_pool(database_url)
    
    try:
        # 创建解析器
        parser = await create_uml_instance_parser(db_pool)
        
        print("🎉 UML实例解析器初始化完成！")
        print("现在可以解析工程XMI文件并生成模型实例数据了。")
        
        # 示例用法
        # with open('my_model.xmi', 'r', encoding='utf-8') as f:
        #     xmi_content = f.read()
        #     result = await parser.parse_model_xmi(
        #         xmi_content, 'BiomedicineModel', 'my_model.xmi', '1.0'
        #     )
        #     print(f"解析结果: 成功={result.success}, 实例数={result.instances_created}")
        
    finally:
        await db_pool.close()

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main()) 