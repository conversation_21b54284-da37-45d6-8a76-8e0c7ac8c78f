"""
UML元类动态生成器

基于标准UML元模型和Profile定义，动态生成领域Schema数据库表结构
支持从XMI解析UML Metaclass和Stereotype定义，自动创建对应的Element类型

核心功能：
1. 解析UML元模型定义（Metaclass）
2. 解析UML Profile扩展（Stereotype）
3. 动态生成数据库Schema
4. 区分元模型定义和模型实例
5. 支持Profile继承和扩展
"""

import asyncio
import json
import logging
import xml.etree.ElementTree as ET
import uuid
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timezone
from dataclasses import dataclass, field
from enum import Enum
import re

import asyncpg

# 导入核心模块 - 修复引用路径
# 注意：core目录已不存在，这些import需要更新或移除
# from ..core.parsing.xml_parser import UnifiedXMLParser
# from ..core.models.element import Element, ElementType, Namespace, SemanticInfo

# 使用标准XML解析器替代
import xml.etree.ElementTree as ET

# 修复domain_managers引用路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'domain_managers'))
from domain_factory import DomainFactory, DomainCreationRequest, DomainType
from core_domain_manager import CoreDomainManager

# 修复schemas.dynamic引用路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'schemas', 'dynamic'))
from dynamic_schema_generator import DynamicSchemaGenerator

logger = logging.getLogger(__name__)

class MetaclassType(Enum):
    """UML元类类型"""
    CLASS = "Class"
    PROPERTY = "Property" 
    OPERATION = "Operation"
    PARAMETER = "Parameter"
    ASSOCIATION = "Association"
    PACKAGE = "Package"
    COMPONENT = "Component"
    INTERFACE = "Interface"
    DATATYPE = "DataType"
    ENUMERATION = "Enumeration"
    CONSTRAINT = "Constraint"
    ACTIVITY = "Activity"
    STATE = "State"
    TRANSITION = "Transition"
    USECASE = "UseCase"
    ACTOR = "Actor"

class ProfileElementType(Enum):
    """Profile元素类型"""
    STEREOTYPE = "Stereotype"
    EXTENSION = "Extension"
    PROFILE = "Profile"
    METACLASS_REFERENCE = "MetaclassReference"

@dataclass
class MetaclassDefinition:
    """UML元类定义"""
    metaclass_id: str
    name: str
    metaclass_type: MetaclassType
    base_metaclass: Optional[str] = None
    properties: Dict[str, Any] = field(default_factory=dict)
    operations: List[Dict[str, Any]] = field(default_factory=list)
    associations: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    documentation: str = ""
    is_abstract: bool = False
    package_uri: str = ""

@dataclass
class StereotypeDefinition:
    """UML Stereotype定义"""
    stereotype_id: str
    name: str
    base_metaclass: str  # 扩展的UML元类
    profile_name: str
    properties: Dict[str, Any] = field(default_factory=dict)  # Tagged Values
    constraints: List[str] = field(default_factory=list)
    icon_uri: Optional[str] = None
    documentation: str = ""
    is_abstract: bool = False

@dataclass
class ProfileDefinition:
    """UML Profile定义"""
    profile_id: str
    name: str
    uri: str
    metamodel_reference: str  # UML版本引用
    stereotypes: Dict[str, StereotypeDefinition] = field(default_factory=dict)
    extensions: List[Dict[str, Any]] = field(default_factory=list)
    imports: List[str] = field(default_factory=list)
    version: str = "1.0"
    documentation: str = ""

@dataclass
class ElementTypeSchema:
    """动态生成的Element类型Schema"""
    type_id: str
    type_name: str
    table_name: str
    base_metaclass: str
    stereotype_name: Optional[str] = None
    field_definitions: Dict[str, Any] = field(default_factory=dict)
    index_definitions: List[Dict[str, Any]] = field(default_factory=list)
    relationship_definitions: Dict[str, Any] = field(default_factory=dict)
    constraint_definitions: List[str] = field(default_factory=list)
    inheritance_chain: List[str] = field(default_factory=list)

class UMLMetaclassGenerator:
    """UML元类动态生成器"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.core_manager = CoreDomainManager(db_pool)
        self.domain_factory = DomainFactory(db_pool, self.core_manager)
        self.schema_generator = DynamicSchemaGenerator(db_pool)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 解析状态存储
        self.metaclass_definitions: Dict[str, MetaclassDefinition] = {}
        self.stereotype_definitions: Dict[str, StereotypeDefinition] = {}
        self.profile_definitions: Dict[str, ProfileDefinition] = {}
        self.generated_schemas: Dict[str, ElementTypeSchema] = {}
        
        # UML元模型命名空间
        self.uml_namespaces = {
            'xmi': 'http://www.omg.org/XMI',
            'uml': 'http://www.eclipse.org/uml2/5.0.0/UML',
            'ecore': 'http://www.eclipse.org/emf/2002/Ecore',
            'xsi': 'http://www.w3.org/2001/XMLSchema-instance'
        }
        
        # 标准UML元类映射
        self.standard_metaclasses = self._load_standard_metaclasses()
    
    async def initialize(self):
        """初始化生成器"""
        await self.core_manager.initialize()
        
        # 创建元类管理表
        await self._create_metaclass_management_tables()
        
        self.logger.info("UML元类生成器初始化完成")
    
    async def process_metamodel_xmi(self, xmi_content: str, model_name: str, 
                                  is_profile: bool = False) -> Tuple[bool, str, Dict[str, Any]]:
        """
        处理元模型XMI文件
        
        Args:
            xmi_content: XMI文件内容
            model_name: 模型名称
            is_profile: 是否为Profile定义
            
        Returns:
            Tuple[bool, str, Dict]: (是否成功, 结果信息, 解析结果)
        """
        try:
            self.logger.info(f"开始处理{'Profile' if is_profile else 'Metamodel'}: {model_name}")
            
            # 1. 解析XML根元素（简化版本）
            root = ET.fromstring(xmi_content)
            
            # 2. 创建简化的解析结果
            parse_result = {
                'success': True,
                'data': {
                    'metadata_store': self._build_simple_metadata_store(root)
                }
            }
            
            # 3. 提取元模型信息
            if is_profile:
                profile_info = await self._extract_profile_definitions(root, parse_result)
                schema_results = await self._generate_profile_schemas(profile_info, model_name)
            else:
                metaclass_info = await self._extract_metaclass_definitions(root, parse_result)
                schema_results = await self._generate_metaclass_schemas(metaclass_info, model_name)
            
            # 4. 创建动态领域
            domain_success = await self._create_metamodel_domain(model_name, schema_results, is_profile)
            
            if domain_success:
                self.logger.info(f"✅ {'Profile' if is_profile else 'Metamodel'}处理完成: {model_name}")
                return True, f"成功处理{'Profile' if is_profile else 'Metamodel'}: {model_name}", {
                    'model_name': model_name,
                    'is_profile': is_profile,
                    'schemas_generated': len(schema_results),
                    'schema_details': [s.type_name for s in schema_results]
                }
            else:
                return False, "动态领域创建失败", {}
                
        except Exception as e:
            self.logger.error(f"{'Profile' if is_profile else 'Metamodel'}处理失败: {e}")
            return False, str(e), {}
    
    async def _extract_metaclass_definitions(self, root: ET.Element, 
                                           parse_result: Dict[str, Any]) -> List[MetaclassDefinition]:
        """从XMI中提取UML元类定义"""
        metaclass_defs = []
        metadata_store = parse_result['data']['metadata_store']
        
        # 遍历解析结果，查找UML元类定义
        for element_id, metadata in metadata_store.items():
            element_tag = metadata.get('tag', '')
            element_type = metadata.get('element_type', '')
            
            # 检查是否为UML元类定义
            if self._is_uml_metaclass(element_tag, element_type):
                metaclass_def = await self._create_metaclass_definition(element_id, metadata, root)
                if metaclass_def:
                    metaclass_defs.append(metaclass_def)
                    self.metaclass_definitions[metaclass_def.metaclass_id] = metaclass_def
        
        self.logger.info(f"提取到 {len(metaclass_defs)} 个UML元类定义")
        return metaclass_defs
    
    async def _extract_profile_definitions(self, root: ET.Element, 
                                         parse_result: Dict[str, Any]) -> List[ProfileDefinition]:
        """从XMI中提取Profile定义"""
        profile_defs = []
        metadata_store = parse_result['data']['metadata_store']
        
        # 查找Profile根元素
        profile_elements = []
        for element_id, metadata in metadata_store.items():
            if self._is_profile_element(metadata.get('tag', '')):
                profile_elements.append((element_id, metadata))
        
        # 为每个Profile创建定义
        for element_id, metadata in profile_elements:
            profile_def = await self._create_profile_definition(element_id, metadata, root, metadata_store)
            if profile_def:
                profile_defs.append(profile_def)
                self.profile_definitions[profile_def.profile_id] = profile_def
        
        self.logger.info(f"提取到 {len(profile_defs)} 个Profile定义")
        return profile_defs
    
    def _is_uml_metaclass(self, tag: str, element_type: str) -> bool:
        """判断是否为UML元类"""
        # 移除命名空间前缀
        local_tag = tag.split('}')[-1] if '}' in tag else tag
        
        # 检查是否为标准UML元类
        return local_tag in [mc.value for mc in MetaclassType]
    
    def _is_profile_element(self, tag: str) -> bool:
        """判断是否为Profile元素"""
        local_tag = tag.split('}')[-1] if '}' in tag else tag
        return local_tag in ['Profile', 'Stereotype', 'Extension']
    
    async def _create_metaclass_definition(self, element_id: str, metadata: Dict[str, Any], 
                                         root: ET.Element) -> Optional[MetaclassDefinition]:
        """创建元类定义"""
        try:
            tag = metadata.get('tag', '')
            local_name = tag.split('}')[-1] if '}' in tag else tag
            
            # 确定元类类型
            try:
                metaclass_type = MetaclassType(local_name)
            except ValueError:
                self.logger.warning(f"未识别的元类类型: {local_name}")
                return None
            
            # 查找对应的XML元素
            xml_element = self._find_xml_element_by_id(root, element_id)
            if xml_element is None:
                return None
            
            # 提取属性
            properties = {}
            for attr_name, attr_value in xml_element.attrib.items():
                if not attr_name.startswith('{'):
                    properties[attr_name] = {
                        'value': attr_value,
                        'type': self._infer_property_type(attr_value)
                    }
            
            # 提取操作（如果有）
            operations = []
            for child in xml_element:
                if child.tag.endswith('ownedOperation') or child.tag.endswith('operation'):
                    op_info = {
                        'name': child.get('name', ''),
                        'visibility': child.get('visibility', 'public'),
                        'parameters': self._extract_operation_parameters(child)
                    }
                    operations.append(op_info)
            
            # 提取基类信息
            base_metaclass = xml_element.get('base') or xml_element.get('superClass')
            
            return MetaclassDefinition(
                metaclass_id=element_id,
                name=xml_element.get('name', local_name),
                metaclass_type=metaclass_type,
                base_metaclass=base_metaclass,
                properties=properties,
                operations=operations,
                is_abstract=xml_element.get('isAbstract', 'false').lower() == 'true',
                documentation=self._extract_documentation(xml_element)
            )
            
        except Exception as e:
            self.logger.error(f"创建元类定义失败 {element_id}: {e}")
            return None
    
    async def _create_profile_definition(self, element_id: str, metadata: Dict[str, Any], 
                                       root: ET.Element, metadata_store: Dict[str, Any]) -> Optional[ProfileDefinition]:
        """创建Profile定义"""
        try:
            xml_element = self._find_xml_element_by_id(root, element_id)
            if xml_element is None:
                return None
            
            profile_name = xml_element.get('name', 'UnnamedProfile')
            profile_uri = xml_element.get('URI', f"http://profile/{profile_name}")
            
            # 创建Profile定义
            profile_def = ProfileDefinition(
                profile_id=element_id,
                name=profile_name,
                uri=profile_uri,
                metamodel_reference=xml_element.get('metamodelReference', 'http://www.eclipse.org/uml2/5.0.0/UML'),
                documentation=self._extract_documentation(xml_element)
            )
            
            # 查找Profile中的Stereotype定义
            for child_id, child_metadata in metadata_store.items():
                if child_metadata.get('parent_id') == element_id:
                    child_tag = child_metadata.get('tag', '')
                    if child_tag.endswith('Stereotype') or 'stereotype' in child_tag.lower():
                        stereotype_def = await self._create_stereotype_definition(
                            child_id, child_metadata, root, profile_name
                        )
                        if stereotype_def:
                            profile_def.stereotypes[stereotype_def.stereotype_id] = stereotype_def
                            self.stereotype_definitions[stereotype_def.stereotype_id] = stereotype_def
            
            return profile_def
            
        except Exception as e:
            self.logger.error(f"创建Profile定义失败 {element_id}: {e}")
            return None
    
    async def _create_stereotype_definition(self, element_id: str, metadata: Dict[str, Any], 
                                          root: ET.Element, profile_name: str) -> Optional[StereotypeDefinition]:
        """创建Stereotype定义"""
        try:
            xml_element = self._find_xml_element_by_id(root, element_id)
            if xml_element is None:
                return None
            
            stereotype_name = xml_element.get('name', 'UnnamedStereotype')
            
            # 查找扩展的元类
            base_metaclass = 'Element'  # 默认值
            for child in xml_element:
                if child.tag.endswith('extension') or 'extension' in child.tag.lower():
                    metaclass_ref = child.get('metaclass') or child.get('extendedMetaclass')
                    if metaclass_ref:
                        base_metaclass = metaclass_ref.split('#')[-1] if '#' in metaclass_ref else metaclass_ref
                    break
            
            # 提取Tagged Values（属性）
            properties = {}
            for child in xml_element:
                if child.tag.endswith('ownedAttribute') or child.tag.endswith('attribute'):
                    attr_name = child.get('name', '')
                    attr_type = child.get('type', 'String')
                    default_value = child.get('defaultValue', '')
                    
                    properties[attr_name] = {
                        'type': attr_type,
                        'default': default_value,
                        'multiplicity': child.get('multiplicity', '1'),
                        'is_required': child.get('lower', '0') != '0'
                    }
            
            return StereotypeDefinition(
                stereotype_id=element_id,
                name=stereotype_name,
                base_metaclass=base_metaclass,
                profile_name=profile_name,
                properties=properties,
                is_abstract=xml_element.get('isAbstract', 'false').lower() == 'true',
                documentation=self._extract_documentation(xml_element)
            )
            
        except Exception as e:
            self.logger.error(f"创建Stereotype定义失败 {element_id}: {e}")
            return None
    
    async def _generate_metaclass_schemas(self, metaclass_defs: List[MetaclassDefinition], 
                                        model_name: str) -> List[ElementTypeSchema]:
        """基于元类定义生成Schema"""
        schemas = []
        
        for metaclass_def in metaclass_defs:
            try:
                schema = await self._create_metaclass_schema(metaclass_def, model_name)
                if schema:
                    schemas.append(schema)
                    self.generated_schemas[schema.type_id] = schema
            except Exception as e:
                self.logger.error(f"生成元类Schema失败 {metaclass_def.name}: {e}")
        
        self.logger.info(f"生成了 {len(schemas)} 个元类Schema")
        return schemas
    
    async def _generate_profile_schemas(self, profile_defs: List[ProfileDefinition], 
                                      model_name: str) -> List[ElementTypeSchema]:
        """基于Profile定义生成Schema"""
        schemas = []
        
        for profile_def in profile_defs.values():
            for stereotype_def in profile_def.stereotypes.values():
                try:
                    schema = await self._create_stereotype_schema(stereotype_def, profile_def, model_name)
                    if schema:
                        schemas.append(schema)
                        self.generated_schemas[schema.type_id] = schema
                except Exception as e:
                    self.logger.error(f"生成Stereotype Schema失败 {stereotype_def.name}: {e}")
        
        self.logger.info(f"生成了 {len(schemas)} 个Profile Schema")
        return schemas
    
    async def _create_metaclass_schema(self, metaclass_def: MetaclassDefinition, 
                                     model_name: str) -> Optional[ElementTypeSchema]:
        """创建元类Schema"""
        try:
            type_id = f"uml_{metaclass_def.name.lower()}_element"
            table_name = f"uml_{metaclass_def.name.lower()}_elements"
            type_name = f"UML {metaclass_def.name} Element"
            
            # 基础字段定义
            field_definitions = {
                'name': {'type': 'string', 'required': True, 'indexed': True},
                'qualified_name': {'type': 'string', 'unique': True, 'indexed': True},
                'visibility': {'type': 'string', 'default': 'public', 'indexed': True},
                'is_abstract': {'type': 'boolean', 'default': False},
                'documentation': {'type': 'text'},
                'xmi_id': {'type': 'string', 'unique': True, 'indexed': True},
                'package_path': {'type': 'string', 'indexed': True},
                'stereotype_applications': {'type': 'jsonb', 'default': '[]'}
            }
            
            # 添加元类特定属性
            for prop_name, prop_info in metaclass_def.properties.items():
                if prop_name not in field_definitions:
                    field_definitions[prop_name] = {
                        'type': self._map_uml_type_to_sql(prop_info.get('type', 'string')),
                        'default': prop_info.get('value'),
                        'indexed': prop_name in ['type', 'id', 'name']
                    }
            
            # 添加元类特定字段
            if metaclass_def.metaclass_type == MetaclassType.CLASS:
                field_definitions.update({
                    'is_active': {'type': 'boolean', 'default': True},
                    'attributes': {'type': 'jsonb', 'default': '[]'},
                    'operations': {'type': 'jsonb', 'default': '[]'},
                    'generalizations': {'type': 'jsonb', 'default': '[]'}
                })
            elif metaclass_def.metaclass_type == MetaclassType.ASSOCIATION:
                field_definitions.update({
                    'source_end': {'type': 'string', 'indexed': True},
                    'target_end': {'type': 'string', 'indexed': True},
                    'association_kind': {'type': 'string', 'default': 'association'}
                })
            elif metaclass_def.metaclass_type == MetaclassType.ACTIVITY:
                field_definitions.update({
                    'pre_conditions': {'type': 'jsonb', 'default': '[]'},
                    'post_conditions': {'type': 'jsonb', 'default': '[]'},
                    'activity_nodes': {'type': 'jsonb', 'default': '[]'}
                })
            
            # 生成索引定义
            index_definitions = [
                {'fields': ['name'], 'type': 'btree'},
                {'fields': ['qualified_name'], 'type': 'btree'},
                {'fields': ['xmi_id'], 'type': 'btree'},
                {'fields': ['visibility'], 'type': 'btree'}
            ]
            
            # 关系定义
            relationship_definitions = {
                'generalizations': {'type': 'many_to_many', 'target': 'self'},
                'associations': {'type': 'many_to_many', 'target': 'uml_association_element'},
                'owned_elements': {'type': 'one_to_many', 'target': 'auto_detect'}
            }
            
            return ElementTypeSchema(
                type_id=type_id,
                type_name=type_name,
                table_name=table_name,
                base_metaclass=metaclass_def.name,
                field_definitions=field_definitions,
                index_definitions=index_definitions,
                relationship_definitions=relationship_definitions,
                inheritance_chain=self._build_inheritance_chain(metaclass_def)
            )
            
        except Exception as e:
            self.logger.error(f"创建元类Schema失败: {e}")
            return None
    
    async def _create_stereotype_schema(self, stereotype_def: StereotypeDefinition, 
                                      profile_def: ProfileDefinition, 
                                      model_name: str) -> Optional[ElementTypeSchema]:
        """创建Stereotype Schema"""
        try:
            type_id = f"profile_{stereotype_def.name.lower()}_element"
            table_name = f"profile_{stereotype_def.name.lower()}_elements"
            type_name = f"Profile {stereotype_def.name} Element"
            
            # 基础字段（继承基元类）
            base_schema = self._get_base_metaclass_fields(stereotype_def.base_metaclass)
            field_definitions = base_schema.copy()
            
            # 添加Profile特定字段
            field_definitions.update({
                'stereotype_name': {'type': 'string', 'default': stereotype_def.name, 'indexed': True},
                'profile_uri': {'type': 'string', 'default': profile_def.uri, 'indexed': True},
                'base_element_id': {'type': 'string', 'indexed': True}  # 引用被扩展的基础元素
            })
            
            # 添加Tagged Values
            for prop_name, prop_info in stereotype_def.properties.items():
                field_name = f"tagged_{prop_name}"
                field_definitions[field_name] = {
                    'type': self._map_uml_type_to_sql(prop_info.get('type', 'String')),
                    'default': prop_info.get('default'),
                    'required': prop_info.get('is_required', False),
                    'indexed': prop_name in ['id', 'type', 'category']
                }
            
            # 生成索引
            index_definitions = [
                {'fields': ['stereotype_name'], 'type': 'btree'},
                {'fields': ['profile_uri'], 'type': 'btree'},
                {'fields': ['base_element_id'], 'type': 'btree'}
            ]
            
            # 关系定义
            relationship_definitions = {
                'base_element': {'type': 'many_to_one', 'target': f"uml_{stereotype_def.base_metaclass.lower()}_element"},
                'stereotype_applications': {'type': 'one_to_many', 'target': 'profile_stereotype_application'}
            }
            
            return ElementTypeSchema(
                type_id=type_id,
                type_name=type_name,
                table_name=table_name,
                base_metaclass=stereotype_def.base_metaclass,
                stereotype_name=stereotype_def.name,
                field_definitions=field_definitions,
                index_definitions=index_definitions,
                relationship_definitions=relationship_definitions,
                inheritance_chain=[stereotype_def.base_metaclass]
            )
            
        except Exception as e:
            self.logger.error(f"创建Stereotype Schema失败: {e}")
            return None
    
    async def _create_metamodel_domain(self, model_name: str, schemas: List[ElementTypeSchema], 
                                     is_profile: bool) -> bool:
        """创建元模型动态领域"""
        try:
            domain_name = f"{model_name}_{'profile' if is_profile else 'metamodel'}"
            
            # 转换为DomainFactory格式
            domain_element_types = []
            for schema in schemas:
                domain_element_types.append({
                    'type_id': schema.type_id,
                    'type_name': schema.type_name,
                    'table_name': schema.table_name,
                    'field_definitions': schema.field_definitions,
                    'index_definitions': schema.index_definitions,
                    'relationship_definitions': schema.relationship_definitions
                })
            
            # 创建领域请求
            request = DomainCreationRequest(
                domain_name=domain_name,
                display_name=f"{model_name} {'Profile' if is_profile else 'Metamodel'} Domain",
                description=f"基于UML {'Profile' if is_profile else 'Metaclass'}定义自动生成的领域",
                domain_type=DomainType.DOMAIN_SPECIFIC,
                custom_element_types=domain_element_types,
                cross_domain_connections=['core', 'security'],
                auto_optimize=True,
                namespace_prefix='UML' if not is_profile else 'PROFILE'
            )
            
            # 创建领域
            result = await self.domain_factory.create_domain(request)
            
            if result.success:
                self.logger.info(f"✅ 元模型领域创建成功: {domain_name}")
                return True
            else:
                self.logger.error(f"❌ 元模型领域创建失败: {result.errors}")
                return False
                
        except Exception as e:
            self.logger.error(f"创建元模型领域失败: {e}")
            return False
    
    # ========== 工具方法 ==========
    
    def _build_simple_metadata_store(self, root: ET.Element) -> Dict[str, Dict[str, Any]]:
        """构建简化的元数据存储，替代UnifiedXMLParser功能"""
        metadata_store = {}
        
        for elem in root.iter():
            elem_id = elem.get('{http://www.omg.org/XMI}id') or elem.get('id') or str(uuid.uuid4())
            
            metadata_store[elem_id] = {
                'tag': elem.tag,
                'element_type': elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag,
                'attributes': dict(elem.attrib),
                'parent_id': None  # 简化处理，不建立父子关系
            }
        
        return metadata_store
    
    def _load_standard_metaclasses(self) -> Dict[str, Dict[str, Any]]:
        """加载标准UML元类定义"""
        return {
            'Class': {
                'properties': ['name', 'visibility', 'isAbstract', 'isFinalSpecialization'],
                'associations': ['generalizations', 'associations', 'ownedAttributes'],
                'base_types': ['Classifier', 'Namespace']
            },
            'Property': {
                'properties': ['name', 'visibility', 'type', 'multiplicity', 'defaultValue'],
                'associations': ['owner', 'type'],
                'base_types': ['StructuralFeature']
            },
            'Association': {
                'properties': ['name', 'visibility', 'isDerived'],
                'associations': ['memberEnds', 'navigableOwnedEnds'],
                'base_types': ['Classifier', 'Relationship']
            }
            # 可以继续添加更多标准元类
        }
    
    def _find_xml_element_by_id(self, root: ET.Element, element_id: str) -> Optional[ET.Element]:
        """根据ID查找XML元素"""
        for elem in root.iter():
            if elem.get('{http://www.omg.org/XMI}id') == element_id or elem.get('id') == element_id:
                return elem
        return None
    
    def _infer_property_type(self, value: str) -> str:
        """推断属性类型"""
        if value.lower() in ('true', 'false'):
            return 'Boolean'
        try:
            int(value)
            return 'Integer'
        except ValueError:
            try:
                float(value)
                return 'Real'
            except ValueError:
                return 'String'
    
    def _extract_operation_parameters(self, operation_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取操作参数"""
        parameters = []
        for param in operation_elem.findall('.//ownedParameter'):
            parameters.append({
                'name': param.get('name', ''),
                'type': param.get('type', 'void'),
                'direction': param.get('direction', 'in')
            })
        return parameters
    
    def _extract_documentation(self, element: ET.Element) -> str:
        """提取文档注释"""
        # 查找注释元素
        comments = element.findall('.//ownedComment') or element.findall('.//comment')
        if comments:
            return comments[0].get('body', '')
        return ""
    
    def _map_uml_type_to_sql(self, uml_type: str) -> str:
        """映射UML类型到SQL类型"""
        type_mapping = {
            'String': 'string',
            'Integer': 'integer',
            'Boolean': 'boolean',
            'Real': 'decimal',
            'Date': 'date',
            'DateTime': 'timestamp',
            'UnlimitedNatural': 'integer',
            'EString': 'string',
            'EBoolean': 'boolean',
            'EInt': 'integer'
        }
        return type_mapping.get(uml_type, 'string')
    
    def _get_base_metaclass_fields(self, metaclass_name: str) -> Dict[str, Any]:
        """获取基元类的标准字段"""
        base_fields = {
            'name': {'type': 'string', 'required': True, 'indexed': True},
            'qualified_name': {'type': 'string', 'unique': True, 'indexed': True},
            'xmi_id': {'type': 'string', 'unique': True, 'indexed': True}
        }
        
        # 根据不同元类添加特定字段
        if metaclass_name in ['Class', 'Component', 'Interface']:
            base_fields.update({
                'visibility': {'type': 'string', 'default': 'public'},
                'is_abstract': {'type': 'boolean', 'default': False}
            })
        elif metaclass_name in ['Property', 'Operation']:
            base_fields.update({
                'visibility': {'type': 'string', 'default': 'public'},
                'type_ref': {'type': 'string', 'indexed': True}
            })
        
        return base_fields
    
    def _build_inheritance_chain(self, metaclass_def: MetaclassDefinition) -> List[str]:
        """构建继承链"""
        chain = [metaclass_def.name]
        if metaclass_def.base_metaclass:
            chain.append(metaclass_def.base_metaclass)
        # 可以递归查找更深的继承关系
        return chain
    
    async def _create_metaclass_management_tables(self):
        """创建元类管理表"""
        async with self.db_pool.acquire() as conn:
            # 元类定义存储表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.uml_metaclass_definitions (
                    metaclass_id VARCHAR(50) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    metaclass_type VARCHAR(50) NOT NULL,
                    base_metaclass VARCHAR(255),
                    properties JSONB DEFAULT '{}',
                    operations JSONB DEFAULT '[]',
                    is_abstract BOOLEAN DEFAULT FALSE,
                    package_uri VARCHAR(500),
                    documentation TEXT,
                    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Profile定义存储表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.uml_profile_definitions (
                    profile_id VARCHAR(50) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    uri VARCHAR(500) NOT NULL UNIQUE,
                    metamodel_reference VARCHAR(500),
                    version VARCHAR(50) DEFAULT '1.0',
                    documentation TEXT,
                    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Stereotype定义存储表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.uml_stereotype_definitions (
                    stereotype_id VARCHAR(50) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    base_metaclass VARCHAR(255) NOT NULL,
                    profile_id VARCHAR(50) NOT NULL,
                    properties JSONB DEFAULT '{}',
                    constraints_def JSONB DEFAULT '[]',
                    is_abstract BOOLEAN DEFAULT FALSE,
                    icon_uri VARCHAR(500),
                    documentation TEXT,
                    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (profile_id) REFERENCES core_schema.uml_profile_definitions(profile_id)
                )
            """)
            
            # 生成的Schema记录表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.generated_element_schemas (
                    schema_id VARCHAR(50) PRIMARY KEY,
                    type_id VARCHAR(255) NOT NULL UNIQUE,
                    type_name VARCHAR(255) NOT NULL,
                    table_name VARCHAR(255) NOT NULL,
                    base_metaclass VARCHAR(255),
                    stereotype_name VARCHAR(255),
                    field_definitions JSONB DEFAULT '{}',
                    relationship_definitions JSONB DEFAULT '{}',
                    inheritance_chain JSONB DEFAULT '[]',
                    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """)

    # ========== 公共API ==========
    
    async def get_metaclass_definitions(self, metaclass_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取元类定义列表"""
        async with self.db_pool.acquire() as conn:
            if metaclass_type:
                rows = await conn.fetch("""
                    SELECT * FROM core_schema.uml_metaclass_definitions
                    WHERE metaclass_type = $1
                    ORDER BY name
                """, metaclass_type)
            else:
                rows = await conn.fetch("""
                    SELECT * FROM core_schema.uml_metaclass_definitions
                    ORDER BY metaclass_type, name
                """)
            
            return [dict(row) for row in rows]
    
    async def get_profile_definitions(self) -> List[Dict[str, Any]]:
        """获取Profile定义列表"""
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT p.*, 
                       COUNT(s.stereotype_id) as stereotype_count
                FROM core_schema.uml_profile_definitions p
                LEFT JOIN core_schema.uml_stereotype_definitions s ON p.profile_id = s.profile_id
                GROUP BY p.profile_id, p.name, p.uri, p.metamodel_reference, p.version, p.documentation, p.created_time
                ORDER BY p.name
            """)
            
            return [dict(row) for row in rows]
    
    async def get_generated_schemas(self) -> List[Dict[str, Any]]:
        """获取生成的Schema列表"""
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT * FROM core_schema.generated_element_schemas
                ORDER BY base_metaclass, type_name
            """)
            
            return [dict(row) for row in rows]

# 便捷函数
async def create_uml_metaclass_generator(db_pool: asyncpg.Pool) -> UMLMetaclassGenerator:
    """创建UML元类生成器实例"""
    generator = UMLMetaclassGenerator(db_pool)
    await generator.initialize()
    return generator

async def main():
    """演示主函数"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'biomedical_mbse_platform',
        'user': 'mbse_user',
        'password': 'mbse_pass_2024'
    }
    
    database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    db_pool = await asyncpg.create_pool(database_url)
    
    try:
        # 创建生成器
        generator = await create_uml_metaclass_generator(db_pool)
        
        print("🎉 UML元类生成器初始化完成！")
        print("现在可以处理UML元模型和Profile定义了。")
        
        # 示例用法
        # with open('my_profile.xmi', 'r', encoding='utf-8') as f:
        #     xmi_content = f.read()
        #     success, message, result = await generator.process_metamodel_xmi(
        #         xmi_content, 'BiomedicineProfile', is_profile=True
        #     )
        #     print(f"处理结果: {message}")
        
    finally:
        await db_pool.close()

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main()) 