#!/usr/bin/env python3
"""
UML 2.5继承关系映射方案完整使用示例

演示如何使用：
- 扩展的继承映射
- Schema生成
- 性能优化
- 版本管理
- 基准测试
"""

import asyncio
import asyncpg
import logging
import json
from typing import Dict, Any
from datetime import datetime

# 导入我们创建的模块 - 修复引用路径
from extended_inheritance_mapping import (
    ExtendedUMLInheritanceMapper, 
    print_extended_inheritance_analysis
)
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from improved_schema_generator import EnhancedUML25SchemaGenerator
from performance_optimization import (
    UML25PerformanceOptimizer, 
    PerformanceBenchmark
)
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'schemas', 'dynamic'))
from schema_version_manager import SchemaVersionManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UML25Demo:
    """UML 2.5方案演示类"""
    
    def __init__(self, db_config: Dict[str, Any]):
        self.db_config = db_config
        self.db_pool = None
        self.schema_name = "uml25_demo"
        
    async def initialize(self):
        """初始化数据库连接"""
        self.db_pool = await asyncpg.create_pool(
            host=self.db_config['host'],
            port=self.db_config['port'],
            user=self.db_config['user'],
            password=self.db_config['password'],
            database=self.db_config['database'],
            min_size=5,
            max_size=20
        )
        logger.info("数据库连接池已创建")
    
    async def demo_inheritance_mapping(self):
        """演示继承关系映射"""
        print("\n=== 1. 继承关系映射演示 ===")
        
        # 创建扩展映射器
        mapper = ExtendedUMLInheritanceMapper(
            schema_name=self.schema_name,
            include_sysml=True
        )
        
        # 打印继承分析
        print_extended_inheritance_analysis()
        
        # 获取统计信息
        stats = mapper.get_extended_statistics()
        print(f"\n继承映射统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 演示继承链查询
        class_chain = mapper.get_inheritance_chain('uml:Class')
        print(f"\numl:Class 的继承链: {' -> '.join(class_chain)}")
        
        # 演示行为模型类型
        behavior_types = mapper.get_behavior_model_types()
        print(f"\n行为模型类型 ({len(behavior_types)}个):")
        for btype in behavior_types[:5]:
            print(f"  - {btype}")
        
        return mapper
    
    async def demo_schema_generation(self, mapper: ExtendedUMLInheritanceMapper):
        """演示Schema生成"""
        print("\n=== 2. Schema生成演示 ===")
        
        # 创建增强版Schema生成器
        generator = EnhancedUML25SchemaGenerator(
            self.db_pool, 
            config={'schema_name': self.schema_name}
        )
        
        # 模拟XMI结果数据
        mock_xmi_result = await self._create_mock_xmi_data()
        
        # 生成Schema
        logger.info("开始生成Schema...")
        result = await generator.generate_schema_from_xmi(mock_xmi_result)
        
        print(f"Schema生成结果:")
        print(f"  - 状态: {result['status']}")
        print(f"  - 生成时间: {result['data']['generation_stats']['generation_time']:.2f}秒")
        print(f"  - 继承表: {result['data']['inheritance_strategy']['inherit_tables']}个")
        print(f"  - 视图表: {result['data']['inheritance_strategy']['view_tables']}个")
        print(f"  - 关系表: {result['data']['inheritance_strategy']['relation_tables']}个")
        
        return result
    
    async def demo_performance_optimization(self):
        """演示性能优化"""
        print("\n=== 3. 性能优化演示 ===")
        
        # 创建性能优化器
        optimizer = UML25PerformanceOptimizer(self.db_pool, self.schema_name)
        
        # 应用所有优化
        logger.info("应用性能优化...")
        optimization_result = await optimizer.apply_all_optimizations()
        
        print(f"性能优化结果:")
        print(f"  - 状态: {optimization_result['status']}")
        print(f"  - 耗时: {optimization_result['duration']:.2f}秒")
        print(f"  - 应用的优化: {len(optimization_result['optimizations_applied'])}项")
        
        for opt in optimization_result['optimizations_applied']:
            print(f"    ✓ {opt}")
        
        return optimization_result
    
    async def demo_performance_benchmark(self):
        """演示性能基准测试"""
        print("\n=== 4. 性能基准测试演示 ===")
        
        # 创建基准测试器
        benchmark = PerformanceBenchmark(self.db_pool, self.schema_name)
        
        # 运行基准测试
        logger.info("运行性能基准测试...")
        benchmark_result = await benchmark.run_full_benchmark()
        
        print(f"基准测试结果 ({benchmark_result['timestamp']}):")
        
        # 显示关键指标
        summary = benchmark_result['summary']
        print(f"\n关键性能指标 (毫秒):")
        for query_name, metrics in summary.items():
            print(f"  {query_name}:")
            print(f"    平均: {metrics['avg_ms']:.2f}ms")
            print(f"    P95: {metrics['p95_ms']:.2f}ms")
        
        return benchmark_result
    
    async def demo_version_management(self):
        """演示版本管理"""
        print("\n=== 5. 版本管理演示 ===")
        
        # 创建版本管理器
        version_manager = SchemaVersionManager(self.db_pool, self.schema_name)
        await version_manager.initialize_version_tracking()
        await version_manager.load_migration_scripts()
        
        # 获取当前版本
        current_version = await version_manager.get_current_version()
        print(f"当前Schema版本: {current_version}")
        
        # 获取Schema信息
        schema_info = await version_manager.get_schema_info()
        print(f"\nSchema信息:")
        for key, value in schema_info.items():
            if key != 'available_migrations':
                print(f"  {key}: {value}")
        
        # 演示干运行迁移
        if version_manager.migration_scripts:
            available_versions = sorted(version_manager.migration_scripts.keys(), 
                                      key=version_manager._version_key)
            if len(available_versions) > 1:
                target_version = available_versions[-1]
                
                print(f"\n演示迁移到版本 {target_version} (干运行):")
                dry_run_result = await version_manager.migrate_to_version(
                    target_version, dry_run=True
                )
                print(f"  {dry_run_result['message']}")
        
        # 验证Schema完整性
        print(f"\n验证Schema完整性:")
        integrity_result = await version_manager.validate_schema_integrity()
        print(f"  总体状态: {integrity_result['overall_status']}")
        
        for check_name, check_result in integrity_result['checks'].items():
            status = check_result.get('status', 'unknown')
            print(f"  {check_name}: {status}")
        
        return version_manager
    
    async def demo_practical_usage(self):
        """演示实际使用场景"""
        print("\n=== 6. 实际使用场景演示 ===")
        
        # 模拟插入一些UML元素
        await self._insert_sample_data()
        
        # 查询演示
        await self._demo_queries()
        
        # 关系查询演示
        await self._demo_relationship_queries()
    
    async def _create_mock_xmi_data(self) -> Dict[str, Any]:
        """创建模拟XMI数据"""
        return {
            'status': 'success',
            'data': {
                'metamodel': {
                    'name': 'UML25Extended',
                    'version': '2.5',
                    'namespace': 'http://www.eclipse.org/uml2/5.0.0/UML'
                },
                'metaclasses': {
                    'Class_1': {
                        'qualified_name': 'uml:Class',
                        'name': 'Class',
                        'is_abstract': False,
                        'attributes': [
                            {'name': 'isActive', 'type_ref': 'Boolean'},
                            {'name': 'ownedAttributes', 'type_ref': 'Property', 'multiplicity': '*'}
                        ]
                    },
                    'Interface_1': {
                        'qualified_name': 'uml:Interface',
                        'name': 'Interface',
                        'is_abstract': False,
                        'attributes': [
                            {'name': 'ownedOperations', 'type_ref': 'Operation', 'multiplicity': '*'}
                        ]
                    }
                }
            }
        }
    
    async def _insert_sample_data(self):
        """插入示例数据"""
        logger.info("插入示例UML元素数据...")
        
        async with self.db_pool.acquire() as conn:
            # 插入一个包
            package_id = await conn.fetchval(f"""
                INSERT INTO {self.schema_name}.uml_element 
                (element_type, documentation, properties)
                VALUES ('uml:Package', '示例包', '{{"version": "1.0"}}')
                RETURNING id
            """)
            
            await conn.execute(f"""
                INSERT INTO {self.schema_name}.uml_named_element 
                (id, name, qualified_name, visibility)
                VALUES ($1, $2, $3, $4)
            """, package_id, 'ExamplePackage', 'com.example.ExamplePackage', 'public')
            
            # 插入一个类
            class_id = await conn.fetchval(f"""
                INSERT INTO {self.schema_name}.uml_element 
                (element_type, documentation)
                VALUES ('uml:Class', '示例类')
                RETURNING id
            """)
            
            await conn.execute(f"""
                INSERT INTO {self.schema_name}.uml_named_element 
                (id, name, qualified_name, visibility)
                VALUES ($1, $2, $3, $4)
            """, class_id, 'ExampleClass', 'com.example.ExampleClass', 'public')
            
            # 如果存在分类器表，插入分类器数据
            try:
                await conn.execute(f"""
                    INSERT INTO {self.schema_name}.uml_classifier 
                    (id, is_abstract, is_leaf)
                    VALUES ($1, $2, $3)
                """, class_id, False, False)
            except:
                pass  # 表可能不存在
        
        logger.info("示例数据插入完成")
    
    async def _demo_queries(self):
        """演示查询功能"""
        print(f"\n查询演示:")
        
        async with self.db_pool.acquire() as conn:
            # 查询所有元素
            elements = await conn.fetch(f"""
                SELECT id, element_type, documentation
                FROM {self.schema_name}.uml_element
                LIMIT 5
            """)
            
            print(f"  发现 {len(elements)} 个UML元素:")
            for element in elements:
                print(f"    - {element['element_type']}: {element['documentation'][:50]}...")
            
            # 查询命名元素
            try:
                named_elements = await conn.fetch(f"""
                    SELECT ne.name, ne.qualified_name, e.element_type
                    FROM {self.schema_name}.uml_named_element ne
                    JOIN {self.schema_name}.uml_element e ON ne.id = e.id
                    LIMIT 5
                """)
                
                print(f"\n  命名元素:")
                for ne in named_elements:
                    print(f"    - {ne['name']} ({ne['element_type']})")
                    
            except Exception as e:
                print(f"    命名元素查询失败: {e}")
    
    async def _demo_relationship_queries(self):
        """演示关系查询"""
        print(f"\n关系查询演示:")
        
        async with self.db_pool.acquire() as conn:
            # 尝试查询关系表
            try:
                relationships = await conn.fetch(f"""
                    SELECT relationship_type, COUNT(*) as count
                    FROM {self.schema_name}.element_relationships
                    GROUP BY relationship_type
                    LIMIT 5
                """)
                
                if relationships:
                    print(f"  关系类型统计:")
                    for rel in relationships:
                        print(f"    - {rel['relationship_type']}: {rel['count']} 个")
                else:
                    print(f"  暂无关系数据")
                    
            except Exception as e:
                print(f"    关系表查询失败: {e}")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        print("开始UML 2.5继承关系映射方案完整演示")
        print("=" * 60)
        
        try:
            # 1. 继承关系映射演示
            mapper = await self.demo_inheritance_mapping()
            
            # 2. Schema生成演示
            schema_result = await self.demo_schema_generation(mapper)
            
            # 3. 性能优化演示
            optimization_result = await self.demo_performance_optimization()
            
            # 4. 性能基准测试演示
            benchmark_result = await self.demo_performance_benchmark()
            
            # 5. 版本管理演示
            version_manager = await self.demo_version_management()
            
            # 6. 实际使用演示
            await self.demo_practical_usage()
            
            print("\n" + "=" * 60)
            print("✅ 完整演示成功完成!")
            
            # 生成演示报告
            report = await self._generate_demo_report(
                schema_result, optimization_result, 
                benchmark_result, version_manager
            )
            
            return report
            
        except Exception as e:
            logger.error(f"演示过程中发生错误: {e}")
            raise
        
        finally:
            if self.db_pool:
                await self.db_pool.close()
    
    async def _generate_demo_report(self, schema_result, optimization_result, 
                                   benchmark_result, version_manager) -> Dict[str, Any]:
        """生成演示报告"""
        
        schema_info = await version_manager.get_schema_info()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'demo_status': 'success',
            'schema_generation': {
                'status': schema_result['status'],
                'generation_time': schema_result['data']['generation_stats']['generation_time'],
                'tables_created': schema_info['tables_count'],
                'views_created': schema_info['views_count']
            },
            'performance_optimization': {
                'status': optimization_result['status'],
                'optimization_time': optimization_result['duration'],
                'optimizations_count': len(optimization_result['optimizations_applied'])
            },
            'benchmark_results': {
                'queries_tested': len(benchmark_result['summary']),
                'average_query_time': sum(
                    metrics['avg_ms'] for metrics in benchmark_result['summary'].values()
                ) / len(benchmark_result['summary']) if benchmark_result['summary'] else 0
            },
            'schema_info': schema_info
        }
        
        # 保存报告到文件
        report_file = f"demo_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📊 演示报告已保存到: {report_file}")
        
        return report


async def main():
    """主函数 - 运行演示"""
    
    # 数据库配置 (请根据实际情况修改)
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'user': 'postgres',
        'password': 'your_password',
        'database': 'biomedical_mbse'
    }
    
    # 创建演示实例
    demo = UML25Demo(db_config)
    
    try:
        # 初始化
        await demo.initialize()
        
        # 运行完整演示
        report = await demo.run_complete_demo()
        
        print(f"\n📈 演示总结:")
        print(f"  - Schema生成时间: {report['schema_generation']['generation_time']:.2f}秒")
        print(f"  - 性能优化时间: {report['performance_optimization']['optimization_time']:.2f}秒")
        print(f"  - 平均查询时间: {report['benchmark_results']['average_query_time']:.2f}ms")
        print(f"  - 创建表数量: {report['schema_generation']['tables_created']}")
        print(f"  - 创建视图数量: {report['schema_generation']['views_created']}")
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    # 运行演示
    success = asyncio.run(main())
    exit(0 if success else 1) 