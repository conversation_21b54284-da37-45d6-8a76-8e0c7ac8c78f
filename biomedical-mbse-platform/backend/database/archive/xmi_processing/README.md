# XMI Processing

本目录包含XMI文件处理和动态代码生成功能。

## 📁 文件说明

### 核心文件
- **`xmi_dynamic_generator.py`** - XMI动态生成器
  - 解析XMI文件并推断Element类型定义
  - 自动创建动态领域和数据库表结构
  - 支持模型版本管理和变更追踪

- **`xmi_demo_example.py`** - XMI处理演示
  - 生成示例UML和SysML的XMI文件
  - 演示XMI解析和处理流程
  - 展示版本管理和回滚功能

## 🎯 主要功能

### XMI文件处理
- **XMI解析** - 支持XMI 2.0-2.5版本
- **元数据提取** - 自动分析模型结构和类型
- **类型推断** - 智能推断Element类型定义
- **动态Schema生成** - 基于XMI结构创建数据库表

### 模型版本管理
- **版本追踪** - 完整的模型版本历史
- **变更检测** - 自动计算模型差异
- **回滚支持** - 支持回滚到历史版本
- **Checksum验证** - 确保模型完整性

### 支持的模型类型
- **UML模型** - 标准UML 2.5模型
- **SysML模型** - 系统建模语言
- **BPMN模型** - 业务流程建模
- **混合模型** - 多种建模语言组合

## 🚀 使用方法

```bash
# 运行XMI处理演示
python xmi_demo_example.py

# 在代码中使用XMI生成器
from xmi_processing.xmi_dynamic_generator import XMIDynamicGenerator

# 创建生成器实例
generator = XMIDynamicGenerator(db_pool)
await generator.initialize()

# 处理XMI文件
success, message, version = await generator.process_xmi_file(
    xmi_content, "MyModel", "author"
)
```

## 📚 相关文档

参考 `docs/` 目录中的详细技术文档：
- XMI_Dynamic_Generation_Guide.md
- UML_Metaclass_System_Guide.md 