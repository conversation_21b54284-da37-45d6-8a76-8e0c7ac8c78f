"""
XMI动态生成器演示脚本

演示如何使用XMI动态生成器处理标准XMI文件
包括生成示例XMI、处理、版本管理等完整流程
"""

import asyncio
import json
import logging
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Any
import uuid

import asyncpg

from xmi_dynamic_generator import XMIDynamicGenerator, ModelType

logger = logging.getLogger(__name__)

class XMIDemo:
    """XMI动态生成器演示类"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'biomedical_mbse_platform',
            'user': 'mbse_user',
            'password': 'mbse_pass_2024'
        }
        self.generator = None
        self.db_pool = None
    
    async def initialize(self):
        """初始化演示环境"""
        print("🚀 初始化XMI动态生成器演示环境...")
        
        # 创建数据库连接池
        database_url = f"postgresql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
        self.db_pool = await asyncpg.create_pool(database_url)
        
        # 初始化XMI生成器
        self.generator = XMIDynamicGenerator(self.db_pool)
        await self.generator.initialize()
        
        print("✅ XMI动态生成器初始化完成！")
    
    def generate_sample_uml_xmi(self) -> str:
        """生成示例UML XMI文件"""
        print("\n📄 生成示例UML XMI文件...")
        
        # 创建根元素
        root = ET.Element("xmi:XMI")
        root.set("xmi:version", "2.5")
        root.set("xmlns:xmi", "http://www.omg.org/XMI")
        root.set("xmlns:uml", "http://www.eclipse.org/uml2/5.0.0/UML")
        
        # 添加模型元素
        model = ET.SubElement(root, "uml:Model")
        model.set("xmi:id", "BiomedicalModel")
        model.set("name", "生物医学MBSE模型")
        
        # 添加包结构
        core_package = ET.SubElement(model, "packagedElement")
        core_package.set("xmi:type", "uml:Package")
        core_package.set("xmi:id", "CorePackage") 
        core_package.set("name", "Core")
        
        # 添加患者类
        patient_class = ET.SubElement(core_package, "packagedElement")
        patient_class.set("xmi:type", "uml:Class")
        patient_class.set("xmi:id", "PatientClass")
        patient_class.set("name", "Patient")
        patient_class.set("visibility", "public")
        
        # 患者属性
        patient_id_attr = ET.SubElement(patient_class, "ownedAttribute")
        patient_id_attr.set("xmi:id", "PatientID")
        patient_id_attr.set("name", "patientId")
        patient_id_attr.set("type", "String")
        patient_id_attr.set("visibility", "private")
        
        age_attr = ET.SubElement(patient_class, "ownedAttribute")
        age_attr.set("xmi:id", "Age")
        age_attr.set("name", "age")
        age_attr.set("type", "Integer")
        age_attr.set("visibility", "private")
        
        # 添加医生类
        doctor_class = ET.SubElement(core_package, "packagedElement")
        doctor_class.set("xmi:type", "uml:Class")
        doctor_class.set("xmi:id", "DoctorClass")
        doctor_class.set("name", "Doctor")
        doctor_class.set("visibility", "public")
        
        # 医生属性
        doctor_id_attr = ET.SubElement(doctor_class, "ownedAttribute")
        doctor_id_attr.set("xmi:id", "DoctorID")
        doctor_id_attr.set("name", "doctorId")
        doctor_id_attr.set("type", "String")
        
        specialty_attr = ET.SubElement(doctor_class, "ownedAttribute")
        specialty_attr.set("xmi:id", "Specialty")
        specialty_attr.set("name", "specialty")
        specialty_attr.set("type", "String")
        
        # 添加治疗类
        treatment_class = ET.SubElement(core_package, "packagedElement")
        treatment_class.set("xmi:type", "uml:Class")
        treatment_class.set("xmi:id", "TreatmentClass")
        treatment_class.set("name", "Treatment")
        treatment_class.set("visibility", "public")
        
        treatment_id_attr = ET.SubElement(treatment_class, "ownedAttribute")
        treatment_id_attr.set("xmi:id", "TreatmentID")
        treatment_id_attr.set("name", "treatmentId")
        treatment_id_attr.set("type", "String")
        
        # 添加关联关系
        patient_doctor_assoc = ET.SubElement(model, "packagedElement")
        patient_doctor_assoc.set("xmi:type", "uml:Association")
        patient_doctor_assoc.set("xmi:id", "PatientDoctorAssociation")
        patient_doctor_assoc.set("name", "PatientDoctorRelation")
        
        # 关联端点
        member_end1 = ET.SubElement(patient_doctor_assoc, "memberEnd")
        member_end1.set("xmi:idref", "PatientEnd")
        
        member_end2 = ET.SubElement(patient_doctor_assoc, "memberEnd")
        member_end2.set("xmi:idref", "DoctorEnd")
        
        # 添加用例包
        usecase_package = ET.SubElement(model, "packagedElement")
        usecase_package.set("xmi:type", "uml:Package")
        usecase_package.set("xmi:id", "UseCasePackage")
        usecase_package.set("name", "UseCases")
        
        # 添加用例
        register_usecase = ET.SubElement(usecase_package, "packagedElement")
        register_usecase.set("xmi:type", "uml:UseCase")
        register_usecase.set("xmi:id", "RegisterPatientUC")
        register_usecase.set("name", "RegisterPatient")
        
        diagnose_usecase = ET.SubElement(usecase_package, "packagedElement")
        diagnose_usecase.set("xmi:type", "uml:UseCase")
        diagnose_usecase.set("xmi:id", "DiagnosePatientUC")
        diagnose_usecase.set("name", "DiagnosePatient")
        
        # 格式化XML
        ET.indent(root, space="  ")
        xml_content = ET.tostring(root, encoding='unicode', xml_declaration=True)
        
        print("✅ UML XMI文件生成完成")
        return xml_content
    
    def generate_sample_sysml_xmi(self) -> str:
        """生成示例SysML XMI文件"""
        print("\n📄 生成示例SysML XMI文件...")
        
        # 创建根元素
        root = ET.Element("xmi:XMI")
        root.set("xmi:version", "2.5")
        root.set("xmlns:xmi", "http://www.omg.org/XMI")
        root.set("xmlns:sysml", "http://www.eclipse.org/papyrus/sysml/1.6/SysML")
        root.set("xmlns:uml", "http://www.eclipse.org/uml2/5.0.0/UML")
        
        # 添加SysML模型
        model = ET.SubElement(root, "uml:Model")
        model.set("xmi:id", "BiomedicalSysMLModel")
        model.set("name", "生物医学系统模型")
        
        # 添加需求包
        req_package = ET.SubElement(model, "packagedElement")
        req_package.set("xmi:type", "uml:Package")
        req_package.set("xmi:id", "RequirementsPackage")
        req_package.set("name", "Requirements")
        
        # 功能需求
        func_req = ET.SubElement(req_package, "packagedElement")
        func_req.set("xmi:type", "sysml:Requirement")
        func_req.set("xmi:id", "REQ001")
        func_req.set("name", "患者数据管理")
        func_req.set("id", "REQ-001")
        func_req.set("text", "系统应能够安全地存储和管理患者的基本信息")
        func_req.set("priority", "high")
        func_req.set("verificationMethod", "test")
        
        # 性能需求
        perf_req = ET.SubElement(req_package, "packagedElement")
        perf_req.set("xmi:type", "sysml:Requirement")
        perf_req.set("xmi:id", "REQ002")
        perf_req.set("name", "响应时间")
        perf_req.set("id", "REQ-002")
        perf_req.set("text", "系统查询响应时间不得超过2秒")
        perf_req.set("priority", "medium")
        perf_req.set("verificationMethod", "analysis")
        
        # 添加块结构包
        structure_package = ET.SubElement(model, "packagedElement")
        structure_package.set("xmi:type", "uml:Package")
        structure_package.set("xmi:id", "StructurePackage")
        structure_package.set("name", "Structure")
        
        # 医疗系统块
        medical_system_block = ET.SubElement(structure_package, "packagedElement")
        medical_system_block.set("xmi:type", "sysml:Block")
        medical_system_block.set("xmi:id", "MedicalSystemBlock")
        medical_system_block.set("name", "MedicalSystem")
        
        # 系统值属性
        cpu_property = ET.SubElement(medical_system_block, "ownedAttribute")
        cpu_property.set("xmi:id", "CPUProperty")
        cpu_property.set("name", "cpuUsage")
        cpu_property.set("type", "Real")
        
        memory_property = ET.SubElement(medical_system_block, "ownedAttribute")
        memory_property.set("xmi:id", "MemoryProperty")
        memory_property.set("name", "memoryUsage")
        memory_property.set("type", "Real")
        
        # 数据库块
        database_block = ET.SubElement(structure_package, "packagedElement")
        database_block.set("xmi:type", "sysml:Block")
        database_block.set("xmi:id", "DatabaseBlock")
        database_block.set("name", "Database")
        
        # 添加活动包
        activity_package = ET.SubElement(model, "packagedElement")
        activity_package.set("xmi:type", "uml:Package")
        activity_package.set("xmi:id", "ActivityPackage")
        activity_package.set("name", "Activities")
        
        # 患者注册活动
        register_activity = ET.SubElement(activity_package, "packagedElement")
        register_activity.set("xmi:type", "sysml:Activity")
        register_activity.set("xmi:id", "RegisterPatientActivity")
        register_activity.set("name", "RegisterPatient")
        
        # 活动输入参数
        input_param = ET.SubElement(register_activity, "ownedParameter")
        input_param.set("xmi:id", "PatientDataInput")
        input_param.set("name", "patientData")
        input_param.set("direction", "in")
        
        # 活动输出参数
        output_param = ET.SubElement(register_activity, "ownedParameter")
        output_param.set("xmi:id", "RegistrationResult")
        output_param.set("name", "result")
        output_param.set("direction", "out")
        
        # 格式化XML
        ET.indent(root, space="  ")
        xml_content = ET.tostring(root, encoding='unicode', xml_declaration=True)
        
        print("✅ SysML XMI文件生成完成")
        return xml_content
    
    async def demo_uml_processing(self):
        """演示UML XMI处理流程"""
        print("\n🎯 演示UML XMI处理流程")
        print("=" * 50)
        
        # 生成示例UML XMI
        uml_xmi = self.generate_sample_uml_xmi()
        
        # 处理XMI文件
        success, message, version = await self.generator.process_xmi_file(
            xmi_content=uml_xmi,
            model_name='BiomedicalUMLDemo',
            author='<EMAIL>'
        )
        
        if success:
            print(f"✅ UML模型处理成功！")
            print(f"   📝 模型名称: BiomedicalUMLDemo")
            print(f"   🔢 版本号: {version.version_number}")
            print(f"   🆔 版本ID: {version.version_id}")
            print(f"   👤 创建者: {version.created_by}")
            print(f"   ⏰ 创建时间: {version.created_time}")
            
            # 验证生成的Schema和表
            await self._verify_generated_domain('BiomedicalUMLDemo', 'uml')
            
            return version
        else:
            print(f"❌ UML模型处理失败: {message}")
            return None
    
    async def demo_sysml_processing(self):
        """演示SysML XMI处理流程"""
        print("\n🎯 演示SysML XMI处理流程")
        print("=" * 50)
        
        # 生成示例SysML XMI
        sysml_xmi = self.generate_sample_sysml_xmi()
        
        # 处理XMI文件
        success, message, version = await self.generator.process_xmi_file(
            xmi_content=sysml_xmi,
            model_name='BiomedicalSysMLDemo',
            author='<EMAIL>'
        )
        
        if success:
            print(f"✅ SysML模型处理成功！")
            print(f"   📝 模型名称: BiomedicalSysMLDemo")
            print(f"   🔢 版本号: {version.version_number}")
            print(f"   🆔 版本ID: {version.version_id}")
            print(f"   👤 创建者: {version.created_by}")
            print(f"   ⏰ 创建时间: {version.created_time}")
            
            # 验证生成的Schema和表
            await self._verify_generated_domain('BiomedicalSysMLDemo', 'sysml')
            
            return version
        else:
            print(f"❌ SysML模型处理失败: {message}")
            return None
    
    async def demo_version_management(self, model_name: str):
        """演示版本管理功能"""
        print(f"\n🎯 演示版本管理功能 - {model_name}")
        print("=" * 50)
        
        # 1. 查看版本历史
        print("📚 查看版本历史:")
        versions = await self.generator.get_model_versions(model_name)
        
        for i, version in enumerate(versions):
            print(f"   {i+1}. v{version['version_number']}")
            print(f"      🆔 ID: {version['version_id']}")
            print(f"      👤 作者: {version['created_by']}")
            print(f"      ⏰ 时间: {version['created_time']}")
            print(f"      🔍 校验和: {version['checksum'][:16]}...")
            if version['changes_summary']:
                changes = json.loads(version['changes_summary'])
                print(f"      📝 变更: {', '.join(changes[:2])}")
            print()
        
        if versions:
            # 2. 获取版本内容
            print("📄 获取最新版本的XMI内容:")
            latest_version = versions[0]
            xmi_content = await self.generator.get_xmi_content(latest_version['version_id'])
            
            if xmi_content:
                print(f"   ✅ 获取成功，内容大小: {len(xmi_content)} 字符")
                # 保存到文件
                filename = f"{model_name}_v{latest_version['version_number']}.xmi"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(xmi_content)
                print(f"   💾 已保存到文件: {filename}")
            else:
                print("   ❌ 获取版本内容失败")
            
            # 3. 演示模型更新
            await self._demo_model_update(model_name)
    
    async def _demo_model_update(self, model_name: str):
        """演示模型更新和版本创建"""
        print(f"\n📝 演示模型更新 - {model_name}")
        
        # 获取原始XMI内容
        versions = await self.generator.get_model_versions(model_name)
        if not versions:
            print("❌ 没有找到现有版本")
            return
        
        original_xmi = await self.generator.get_xmi_content(versions[0]['version_id'])
        if not original_xmi:
            print("❌ 无法获取原始XMI内容")
            return
        
        # 修改XMI内容（添加新元素）
        root = ET.fromstring(original_xmi)
        
        # 查找模型根元素
        model_elem = root.find('.//*[@name]')
        if model_elem is not None:
            # 添加新的包元素
            new_package = ET.SubElement(model_elem, "packagedElement")
            new_package.set("xmi:type", "uml:Package")
            new_package.set("xmi:id", f"NewPackage_{uuid.uuid4().hex[:8]}")
            new_package.set("name", "UpdatedPackage")
            
            # 添加新类
            new_class = ET.SubElement(new_package, "packagedElement")
            new_class.set("xmi:type", "uml:Class")
            new_class.set("xmi:id", f"NewClass_{uuid.uuid4().hex[:8]}")
            new_class.set("name", "UpdatedClass")
            new_class.set("visibility", "public")
        
        # 生成修改后的XMI
        ET.indent(root, space="  ")
        updated_xmi = ET.tostring(root, encoding='unicode', xml_declaration=True)
        
        # 处理更新后的XMI
        success, message, new_version = await self.generator.process_xmi_file(
            xmi_content=updated_xmi,
            model_name=model_name,
            author='<EMAIL>'
        )
        
        if success:
            print(f"✅ 模型更新成功！")
            print(f"   🆕 新版本号: {new_version.version_number}")
            print(f"   📝 变更摘要: {new_version.changes_summary}")
            
            # 显示版本对比
            print("\n📊 版本对比:")
            updated_versions = await self.generator.get_model_versions(model_name)
            for i, version in enumerate(updated_versions[:2]):
                status = "最新" if i == 0 else "之前"
                print(f"   {status} - v{version['version_number']} ({version['created_time']})")
        else:
            print(f"❌ 模型更新失败: {message}")
    
    async def _verify_generated_domain(self, model_name: str, model_type: str):
        """验证生成的领域结构"""
        print(f"\n🔍 验证生成的领域结构:")
        
        async with self.db_pool.acquire() as conn:
            # 查找生成的Schema
            domain_name = f"{model_name}_{model_type}".lower()
            
            # 查看Schema
            schemas = await conn.fetch("""
                SELECT schema_name FROM information_schema.schemata 
                WHERE schema_name LIKE $1
            """, f"{domain_name}%")
            
            if schemas:
                schema_name = schemas[0]['schema_name']
                print(f"   ✅ 找到Schema: {schema_name}")
                
                # 查看生成的表
                tables = await conn.fetch("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = $1
                    ORDER BY table_name
                """, schema_name)
                
                print(f"   📊 生成的表 ({len(tables)}个):")
                for table in tables:
                    print(f"      - {table['table_name']}")
                
                # 查看Element类型定义
                element_types = await conn.fetch("""
                    SELECT type_id, type_name, table_name 
                    FROM core_schema.element_type_definitions
                    WHERE type_id LIKE $1
                    ORDER BY type_id
                """, f"{model_type}_%")
                
                print(f"   🧩 注册的Element类型 ({len(element_types)}个):")
                for elem_type in element_types:
                    print(f"      - {elem_type['type_id']}: {elem_type['type_name']}")
            else:
                print(f"   ❌ 未找到对应的Schema")
    
    async def demo_batch_processing(self):
        """演示批量处理功能"""
        print("\n🎯 演示批量处理功能")
        print("=" * 50)
        
        # 生成多个XMI文件进行批量处理
        models = [
            ("CardiacModel", self.generate_sample_uml_xmi()),
            ("NeurologyModel", self.generate_sample_sysml_xmi()),
            ("PharmacologyModel", self.generate_sample_uml_xmi())
        ]
        
        results = []
        
        for model_name, xmi_content in models:
            print(f"📄 处理模型: {model_name}")
            
            success, message, version = await self.generator.process_xmi_file(
                xmi_content=xmi_content,
                model_name=model_name,
                author='<EMAIL>'
            )
            
            results.append({
                'model': model_name,
                'success': success,
                'version': version.version_number if success else None,
                'message': message
            })
            
            if success:
                print(f"   ✅ 成功 - 版本 {version.version_number}")
            else:
                print(f"   ❌ 失败 - {message}")
        
        # 汇总结果
        success_count = sum(1 for r in results if r['success'])
        print(f"\n📊 批量处理完成: {success_count}/{len(results)} 个模型成功")
        
        # 显示所有模型的概览
        print("\n📋 模型概览:")
        for result in results:
            status = "✅" if result['success'] else "❌"
            version = f"v{result['version']}" if result['version'] else "失败"
            print(f"   {status} {result['model']}: {version}")
    
    async def demo_rollback_functionality(self, model_name: str):
        """演示回滚功能"""
        print(f"\n🎯 演示回滚功能 - {model_name}")
        print("=" * 50)
        
        # 查看版本历史
        versions = await self.generator.get_model_versions(model_name)
        
        if len(versions) < 2:
            print("❌ 需要至少2个版本才能演示回滚功能")
            return
        
        print("📚 当前版本历史:")
        for i, version in enumerate(versions):
            print(f"   {i+1}. v{version['version_number']} - {version['created_time']}")
        
        # 回滚到前一个版本
        target_version = versions[1]['version_number']
        print(f"\n🔄 回滚到版本 {target_version}...")
        
        success, message = await self.generator.rollback_to_version(
            model_name=model_name,
            target_version=target_version
        )
        
        if success:
            print(f"✅ 回滚成功: {message}")
            
            # 查看回滚后的版本历史
            print("\n📚 回滚后的版本历史:")
            updated_versions = await self.generator.get_model_versions(model_name)
            for i, version in enumerate(updated_versions[:3]):
                print(f"   {i+1}. v{version['version_number']} - {version['created_time']}")
        else:
            print(f"❌ 回滚失败: {message}")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        print("🎉 XMI动态生成器完整演示")
        print("=" * 60)
        
        try:
            # 初始化
            await self.initialize()
            
            # 1. UML处理演示
            uml_version = await self.demo_uml_processing()
            
            # 2. SysML处理演示
            sysml_version = await self.demo_sysml_processing()
            
            # 3. 版本管理演示
            if uml_version:
                await self.demo_version_management('BiomedicalUMLDemo')
            
            # 4. 批量处理演示
            await self.demo_batch_processing()
            
            # 5. 回滚功能演示
            if uml_version:
                await self.demo_rollback_functionality('BiomedicalUMLDemo')
            
            print("\n🎉 完整演示完成！")
            print("✅ 所有功能测试通过")
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            logger.exception("演示异常")
        
        finally:
            if self.db_pool:
                await self.db_pool.close()

async def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行演示
    demo = XMIDemo()
    await demo.run_complete_demo()

if __name__ == "__main__":
    asyncio.run(main()) 