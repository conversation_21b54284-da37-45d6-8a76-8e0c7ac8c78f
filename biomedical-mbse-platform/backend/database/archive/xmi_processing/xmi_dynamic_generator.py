"""
XMI动态领域生成器

基于标准XMI (XML Metadata Interchange) 自动生成动态领域
支持UML 2.x、SysML等标准建模语言
包含完整的版本管理功能
"""

import asyncio
import json
import logging
import xml.etree.ElementTree as ET
import hashlib
import uuid
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timezone
from dataclasses import dataclass, field
from enum import Enum
import re

import asyncpg

# 修复引用路径
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'domain_managers'))
from domain_factory import (
    DomainFactory, DomainCreationRequest, DomainType
)
from core_domain_manager import CoreDomainManager, ElementMetadata

# 修复schemas.dynamic引用路径  
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'schemas', 'dynamic'))
from dynamic_schema_generator import DynamicSchemaGenerator

logger = logging.getLogger(__name__)

class XMIVersion(Enum):
    """XMI版本枚举"""
    XMI_2_0 = "2.0"
    XMI_2_1 = "2.1" 
    XMI_2_4 = "2.4"
    XMI_2_5 = "2.5"

class ModelType(Enum):
    """模型类型枚举"""
    UML = "uml"
    SYSML = "sysml"
    BPMN = "bpmn"
    MIXED = "mixed"

@dataclass
class XMIMetadata:
    """XMI元数据"""
    xmi_version: str
    model_type: ModelType
    metamodel_uri: str
    tool_name: Optional[str] = None
    tool_version: Optional[str] = None
    model_version: str = "1.0"
    created_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    author: Optional[str] = None
    checksum: Optional[str] = None

@dataclass
class ElementTypeDefinition:
    """从XMI推断的Element类型定义"""
    type_id: str
    type_name: str
    table_name: str
    base_class: str  # UML/SysML基类
    stereotype: Optional[str] = None
    field_definitions: Dict[str, Any] = field(default_factory=dict)
    index_definitions: List[Dict[str, Any]] = field(default_factory=list)
    relationship_definitions: Dict[str, Any] = field(default_factory=dict)
    cross_domain_refs: List[str] = field(default_factory=list)

@dataclass
class ModelVersion:
    """模型版本信息"""
    version_id: str
    model_name: str
    version_number: str
    parent_version_id: Optional[str] = None
    changes_summary: List[str] = field(default_factory=list)
    created_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    created_by: Optional[str] = None
    xmi_content: Optional[str] = None
    checksum: str = ""

class XMIDynamicGenerator:
    """XMI动态领域生成器"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.core_manager = CoreDomainManager(db_pool)
        self.domain_factory = DomainFactory(db_pool, self.core_manager)
        self.schema_generator = DynamicSchemaGenerator(db_pool)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # XMI命名空间映射
        self.xmi_namespaces = {
            'xmi': 'http://www.omg.org/XMI',
            'uml': 'http://www.eclipse.org/uml2/5.0.0/UML',
            'sysml': 'http://www.eclipse.org/papyrus/sysml/1.6/SysML',
            'ecore': 'http://www.eclipse.org/emf/2002/Ecore'
        }
        
        # 类型映射表
        self.type_mapping = {
            'string': 'string',
            'int': 'integer', 
            'integer': 'integer',
            'bool': 'boolean',
            'boolean': 'boolean',
            'real': 'decimal',
            'float': 'decimal',
            'date': 'date',
            'dateTime': 'timestamp',
            'unlimited': 'integer'
        }
    
    async def initialize(self):
        """初始化生成器"""
        await self.core_manager
        
        # 创建版本管理表
        await self._create_version_management_tables()
        
        self.logger.info("XMI动态生成器初始化完成")
    
    async def process_xmi_file(self, xmi_content: str, model_name: str, 
                              author: Optional[str] = None) -> Tuple[bool, str, ModelVersion]:
        """
        处理XMI文件并生成动态领域
        
        Args:
            xmi_content: XMI文件内容
            model_name: 模型名称
            author: 作者
            
        Returns:
            Tuple[bool, str, ModelVersion]: (是否成功, 结果信息, 版本信息)
        """
        try:
            self.logger.info(f"开始处理XMI文件: {model_name}")
            
            # 1. 解析XMI元数据
            xmi_metadata = await self._parse_xmi_metadata(xmi_content)
            self.logger.info(f"XMI版本: {xmi_metadata.xmi_version}, 模型类型: {xmi_metadata.model_type}")
            
            # 2. 计算文件校验和
            checksum = self._calculate_checksum(xmi_content)
            
            # 3. 检查是否为新版本
            is_new_version, parent_version = await self._check_model_version(model_name, checksum)
            
            # 4. 创建版本记录
            model_version = await self._create_model_version(
                model_name, xmi_content, checksum, parent_version, author, xmi_metadata
            )
            
            if not is_new_version:
                return True, f"模型未发生变化，使用现有版本 {model_version.version_number}", model_version
            
            # 5. 分析XMI结构并生成Element类型定义
            element_types = await self._analyze_xmi_structure(xmi_content, xmi_metadata)
            self.logger.info(f"分析得到 {len(element_types)} 种Element类型")
            
            # 6. 创建或更新动态领域
            domain_success = await self._create_dynamic_domain(
                model_name, element_types, xmi_metadata, model_version
            )
            
            if domain_success:
                # 7. 存储XMI内容到数据库
                await self._store_xmi_content(model_version, xmi_content, element_types)
                
                # 8. 如果是版本更新，计算变更
                if parent_version:
                    changes = await self._calculate_model_changes(parent_version, model_version)
                    model_version.changes_summary = changes
                    await self._update_version_changes(model_version.version_id, changes)
                
                self.logger.info(f"✅ XMI处理完成，生成版本 {model_version.version_number}")
                return True, f"成功生成模型版本 {model_version.version_number}", model_version
            else:
                return False, "动态领域创建失败", model_version
                
        except Exception as e:
            self.logger.error(f"XMI处理失败: {e}")
            # 创建失败的版本记录
            error_version = ModelVersion(
                version_id=str(uuid.uuid4()),
                model_name=model_name,
                version_number="error",
                checksum=self._calculate_checksum(xmi_content)
            )
            return False, str(e), error_version
    
    async def _parse_xmi_metadata(self, xmi_content: str) -> XMIMetadata:
        """解析XMI元数据"""
        try:
            root = ET.fromstring(xmi_content)
            
            # 获取XMI版本
            xmi_version = root.get('{http://www.omg.org/XMI}version', '2.0')
            
            # 推断模型类型
            model_type = self._infer_model_type(root)
            
            # 获取元模型URI
            metamodel_uri = self._extract_metamodel_uri(root)
            
            # 获取工具信息
            tool_name, tool_version = self._extract_tool_info(root)
            
            # 计算校验和
            checksum = self._calculate_checksum(xmi_content)
            
            return XMIMetadata(
                xmi_version=xmi_version,
                model_type=model_type,
                metamodel_uri=metamodel_uri,
                tool_name=tool_name,
                tool_version=tool_version,
                checksum=checksum
            )
            
        except ET.ParseError as e:
            raise ValueError(f"XMI解析错误: {e}")
    
    def _infer_model_type(self, root: ET.Element) -> ModelType:
        """推断模型类型"""
        # 检查命名空间
        if any('sysml' in uri for uri in root.nsmap.values() if root.nsmap):
            return ModelType.SYSML
        elif any('uml' in uri for uri in root.nsmap.values() if root.nsmap):
            return ModelType.UML
        elif any('bpmn' in uri for uri in root.nsmap.values() if root.nsmap):
            return ModelType.BPMN
        
        # 检查根元素
        if 'sysml' in root.tag.lower():
            return ModelType.SYSML
        elif 'uml' in root.tag.lower():
            return ModelType.UML
        
        # 检查子元素
        for child in root:
            if 'sysml' in child.tag.lower():
                return ModelType.SYSML
            elif 'uml' in child.tag.lower():
                return ModelType.UML
        
        # 默认UML
        return ModelType.UML
    
    def _extract_metamodel_uri(self, root: ET.Element) -> str:
        """提取元模型URI"""
        # 检查根元素的命名空间
        for prefix, uri in root.nsmap.items() if root.nsmap else {}:
            if prefix in ['uml', 'sysml', 'ecore']:
                return uri
        
        # 默认UML 2.5
        return "http://www.eclipse.org/uml2/5.0.0/UML"
    
    def _extract_tool_info(self, root: ET.Element) -> Tuple[Optional[str], Optional[str]]:
        """提取工具信息"""
        # 查找注释中的工具信息
        for comment in root.iter(ET.Comment):
            if comment.text:
                if 'Enterprise Architect' in comment.text:
                    return 'Enterprise Architect', self._extract_version_from_text(comment.text)
                elif 'MagicDraw' in comment.text:
                    return 'MagicDraw', self._extract_version_from_text(comment.text)
                elif 'Papyrus' in comment.text:
                    return 'Papyrus', self._extract_version_from_text(comment.text)
        
        # 检查根元素属性
        if 'tool' in root.attrib:
            return root.attrib['tool'], root.attrib.get('toolVersion')
        
        return None, None
    
    def _extract_version_from_text(self, text: str) -> Optional[str]:
        """从文本中提取版本号"""
        version_pattern = r'(\d+\.[\d\.]+)'
        match = re.search(version_pattern, text)
        return match.group(1) if match else None
    
    async def _analyze_xmi_structure(self, xmi_content: str, metadata: XMIMetadata) -> List[ElementTypeDefinition]:
        """分析XMI结构并生成Element类型定义"""
        root = ET.fromstring(xmi_content)
        element_types = []
        processed_types = set()
        
        # 遍历所有元素，收集类型信息
        for elem in root.iter():
            elem_type = self._get_element_type(elem)
            if elem_type and elem_type not in processed_types:
                processed_types.add(elem_type)
                
                # 生成Element类型定义
                type_def = await self._create_element_type_definition(elem, elem_type, metadata)
                if type_def:
                    element_types.append(type_def)
        
        # 补充标准UML/SysML类型
        standard_types = await self._add_standard_element_types(metadata.model_type)
        element_types.extend(standard_types)
        
        return element_types
    
    def _get_element_type(self, elem: ET.Element) -> Optional[str]:
        """获取元素类型"""
        # 移除命名空间前缀
        tag = elem.tag
        if '}' in tag:
            tag = tag.split('}')[1]
        
        # 过滤掉不需要的元素
        ignore_types = {'Documentation', 'xmi:Extension', 'eAnnotations'}
        if tag in ignore_types:
            return None
        
        # 标准化类型名
        return tag.lower() if tag else None
    
    async def _create_element_type_definition(self, elem: ET.Element, elem_type: str, 
                                           metadata: XMIMetadata) -> Optional[ElementTypeDefinition]:
        """创建Element类型定义"""
        try:
            # 生成类型ID和表名
            type_id = f"{metadata.model_type.value}_{elem_type}_element"
            table_name = f"{metadata.model_type.value}_{elem_type}_elements"
            type_name = f"{metadata.model_type.value.upper()} {elem_type.title()} Element"
            
            # 分析元素属性
            field_definitions = await self._analyze_element_attributes(elem, elem_type)
            
            # 添加标准字段
            field_definitions.update({
                'name': {'type': 'string', 'required': True, 'indexed': True},
                'qualified_name': {'type': 'string', 'unique': True, 'indexed': True},
                'visibility': {'type': 'string', 'default': 'public', 'indexed': True},
                'stereotype': {'type': 'string', 'indexed': True},
                'documentation': {'type': 'text'},
                'xmi_id': {'type': 'string', 'unique': True, 'indexed': True},
                'xmi_type': {'type': 'string', 'indexed': True},
                'model_version_id': {'type': 'string', 'indexed': True},
                'xml_source': {'type': 'text'}
            })
            
            # 生成索引定义
            index_definitions = [
                {'fields': ['name'], 'type': 'btree'},
                {'fields': ['qualified_name'], 'type': 'btree'},
                {'fields': ['xmi_id'], 'type': 'btree'},
                {'fields': ['model_version_id'], 'type': 'btree'}
            ]
            
            # 分析关系
            relationships = await self._analyze_element_relationships(elem, elem_type)
            
            # 确定跨域引用
            cross_refs = await self._determine_cross_domain_refs(elem_type, metadata.model_type)
            
            return ElementTypeDefinition(
                type_id=type_id,
                type_name=type_name,
                table_name=table_name,
                base_class=elem_type,
                field_definitions=field_definitions,
                index_definitions=index_definitions,
                relationship_definitions=relationships,
                cross_domain_refs=cross_refs
            )
            
        except Exception as e:
            self.logger.warning(f"创建Element类型定义失败 {elem_type}: {e}")
            return None
    
    async def _analyze_element_attributes(self, elem: ET.Element, elem_type: str) -> Dict[str, Any]:
        """分析元素属性"""
        field_definitions = {}
        
        # 分析XML属性
        for attr_name, attr_value in elem.attrib.items():
            if attr_name.startswith('{'):
                # 移除命名空间
                attr_name = attr_name.split('}')[1]
            
            # 推断属性类型
            field_type = self._infer_attribute_type(attr_value)
            
            field_definitions[attr_name] = {
                'type': field_type,
                'indexed': attr_name in ['id', 'name', 'type']
            }
        
        # 分析子元素
        child_types = set()
        for child in elem:
            child_type = self._get_element_type(child)
            if child_type:
                child_types.add(child_type)
        
        # 如果有子元素，添加引用字段
        if child_types:
            field_definitions['child_elements'] = {
                'type': 'jsonb',
                'default': '[]',
                'indexed': True
            }
        
        return field_definitions
    
    def _infer_attribute_type(self, value: str) -> str:
        """推断属性类型"""
        if not value:
            return 'string'
        
        # 尝试推断类型
        if value.lower() in ('true', 'false'):
            return 'boolean'
        
        try:
            int(value)
            return 'integer'
        except ValueError:
            pass
        
        try:
            float(value)
            return 'decimal'
        except ValueError:
            pass
        
        # 检查是否为UUID
        if re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', value, re.I):
            return 'uuid'
        
        return 'string'
    
    async def _analyze_element_relationships(self, elem: ET.Element, elem_type: str) -> Dict[str, Any]:
        """分析元素关系"""
        relationships = {}
        
        # 分析引用属性
        for attr_name, attr_value in elem.attrib.items():
            if attr_name.endswith('Ref') or attr_name.endswith('_ref'):
                relationships[attr_name] = {
                    'type': 'many_to_one',
                    'target': 'auto_detect'
                }
        
        # 分析包含关系
        child_elements = {}
        for child in elem:
            child_type = self._get_element_type(child)
            if child_type:
                if child_type in child_elements:
                    child_elements[child_type] += 1
                else:
                    child_elements[child_type] = 1
        
        for child_type, count in child_elements.items():
            rel_type = 'one_to_many' if count > 1 else 'one_to_one'
            relationships[f'owned_{child_type}'] = {
                'type': rel_type,
                'target': f"{child_type}_element"
            }
        
        return relationships
    
    async def _determine_cross_domain_refs(self, elem_type: str, model_type: ModelType) -> List[str]:
        """确定跨域引用"""
        cross_refs = []
        
        # 基于元素类型确定跨域引用
        if model_type == ModelType.UML:
            if elem_type == 'class':
                cross_refs.extend(['sysml_refs', 'database_refs', 'code_refs'])
            elif elem_type == 'usecase':
                cross_refs.extend(['sysml_requirements_refs', 'testing_refs'])
            elif elem_type == 'component':
                cross_refs.extend(['sysml_refs', 'architecture_refs'])
        
        elif model_type == ModelType.SYSML:
            if elem_type == 'block':
                cross_refs.extend(['uml_refs', 'hardware_refs', 'software_refs'])
            elif elem_type == 'requirement':
                cross_refs.extend(['uml_refs', 'testing_refs', 'verification_refs'])
            elif elem_type == 'activity':
                cross_refs.extend(['uml_refs', 'process_refs', 'workflow_refs'])
        
        # 通用引用
        cross_refs.extend(['security_refs', 'traceability_refs'])
        
        return cross_refs
    
    async def _add_standard_element_types(self, model_type: ModelType) -> List[ElementTypeDefinition]:
        """添加标准Element类型"""
        standard_types = []
        
        if model_type == ModelType.UML:
            # UML标准类型
            standard_uml_types = ['package', 'interface', 'enumeration', 'datatype', 'signal']
            for uml_type in standard_uml_types:
                type_def = ElementTypeDefinition(
                    type_id=f"uml_{uml_type}_element",
                    type_name=f"UML {uml_type.title()} Element",
                    table_name=f"uml_{uml_type}_elements",
                    base_class=uml_type,
                    field_definitions=self._get_standard_uml_fields(uml_type),
                    cross_domain_refs=['sysml_refs', 'security_refs']
                )
                standard_types.append(type_def)
        
        elif model_type == ModelType.SYSML:
            # SysML标准类型
            standard_sysml_types = ['port', 'connector', 'flowport']
            for sysml_type in standard_sysml_types:
                type_def = ElementTypeDefinition(
                    type_id=f"sysml_{sysml_type}_element",
                    type_name=f"SysML {sysml_type.title()} Element",
                    table_name=f"sysml_{sysml_type}_elements",
                    base_class=sysml_type,
                    field_definitions=self._get_standard_sysml_fields(sysml_type),
                    cross_domain_refs=['uml_refs', 'verification_refs']
                )
                standard_types.append(type_def)
        
        return standard_types
    
    def _get_standard_uml_fields(self, uml_type: str) -> Dict[str, Any]:
        """获取标准UML字段定义"""
        base_fields = {
            'name': {'type': 'string', 'required': True, 'indexed': True},
            'visibility': {'type': 'string', 'default': 'public'},
            'is_abstract': {'type': 'boolean', 'default': False},
            'is_final': {'type': 'boolean', 'default': False}
        }
        
        if uml_type == 'package':
            base_fields.update({
                'owned_elements': {'type': 'jsonb', 'default': '[]'},
                'imported_packages': {'type': 'jsonb', 'default': '[]'}
            })
        elif uml_type == 'interface':
            base_fields.update({
                'operations': {'type': 'jsonb', 'default': '[]'},
                'protocols': {'type': 'jsonb', 'default': '[]'}
            })
        
        return base_fields
    
    def _get_standard_sysml_fields(self, sysml_type: str) -> Dict[str, Any]:
        """获取标准SysML字段定义"""
        base_fields = {
            'name': {'type': 'string', 'required': True, 'indexed': True},
            'direction': {'type': 'string', 'default': 'inout'},
            'type_ref': {'type': 'string', 'indexed': True}
        }
        
        if sysml_type == 'port':
            base_fields.update({
                'is_conjugated': {'type': 'boolean', 'default': False},
                'provided_interfaces': {'type': 'jsonb', 'default': '[]'},
                'required_interfaces': {'type': 'jsonb', 'default': '[]'}
            })
        elif sysml_type == 'flowport':
            base_fields.update({
                'flow_direction': {'type': 'string', 'default': 'inout'},
                'flow_properties': {'type': 'jsonb', 'default': '[]'}
            })
        
        return base_fields
    
    async def _create_dynamic_domain(self, model_name: str, element_types: List[ElementTypeDefinition],
                                   metadata: XMIMetadata, version: ModelVersion) -> bool:
        """创建动态领域"""
        try:
            # 构建领域名称
            domain_name = f"{model_name}_{metadata.model_type.value}"
            
            # 转换为DomainFactory格式
            domain_element_types = []
            for type_def in element_types:
                domain_element_types.append({
                    'type_id': type_def.type_id,
                    'type_name': type_def.type_name,
                    'table_name': type_def.table_name,
                    'field_definitions': type_def.field_definitions,
                    'index_definitions': type_def.index_definitions,
                    'relationship_definitions': type_def.relationship_definitions,
                    'cross_domain_refs': type_def.cross_domain_refs
                })
            
            # 创建领域请求
            request = DomainCreationRequest(
                domain_name=domain_name,
                display_name=f"{model_name} {metadata.model_type.value.upper()} 模型领域",
                description=f"从XMI自动生成的{metadata.model_type.value.upper()}建模领域，版本: {version.version_number}",
                domain_type=DomainType.DOMAIN_SPECIFIC,
                custom_element_types=domain_element_types,
                cross_domain_connections=['core', 'security', 'traceability'],
                auto_optimize=True,
                namespace_prefix=metadata.model_type.value.upper()
            )
            
            # 创建领域
            result = await self.domain_factory.create_domain(request)
            
            if result.success:
                self.logger.info(f"✅ 动态领域创建成功: {domain_name}")
                return True
            else:
                self.logger.error(f"❌ 动态领域创建失败: {result.errors}")
                return False
                
        except Exception as e:
            self.logger.error(f"创建动态领域失败: {e}")
            return False
    
    # 版本管理相关方法
    
    async def _create_version_management_tables(self):
        """创建版本管理表"""
        async with self.db_pool.acquire() as conn:
            # 模型版本表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.model_versions (
                    version_id VARCHAR(50) PRIMARY KEY,
                    model_name VARCHAR(255) NOT NULL,
                    version_number VARCHAR(50) NOT NULL,
                    parent_version_id VARCHAR(50),
                    xmi_content TEXT,
                    checksum VARCHAR(64) NOT NULL,
                    metadata JSONB DEFAULT '{}',
                    changes_summary JSONB DEFAULT '[]',
                    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    created_by VARCHAR(255),
                    
                    UNIQUE(model_name, version_number),
                    FOREIGN KEY (parent_version_id) REFERENCES core_schema.model_versions(version_id)
                )
            """)
            
            # XMI内容存储表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.xmi_content_store (
                    content_id VARCHAR(50) PRIMARY KEY,
                    version_id VARCHAR(50) NOT NULL,
                    content_type VARCHAR(50) DEFAULT 'xmi',
                    compressed_content BYTEA,
                    original_size INTEGER,
                    compression_type VARCHAR(20) DEFAULT 'gzip',
                    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (version_id) REFERENCES core_schema.model_versions(version_id)
                )
            """)
            
            # 模型变更记录表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.model_changes (
                    change_id VARCHAR(50) PRIMARY KEY,
                    version_id VARCHAR(50) NOT NULL,
                    change_type VARCHAR(50) NOT NULL,
                    element_type VARCHAR(255),
                    element_id VARCHAR(255),
                    old_value JSONB,
                    new_value JSONB,
                    change_description TEXT,
                    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (version_id) REFERENCES core_schema.model_versions(version_id)
                )
            """)
            
            # 创建索引
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_model_versions_model_name 
                ON core_schema.model_versions(model_name, created_time DESC)
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_model_changes_version 
                ON core_schema.model_changes(version_id, change_type)
            """)
    
    def _calculate_checksum(self, content: str) -> str:
        """计算内容校验和"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    async def _check_model_version(self, model_name: str, checksum: str) -> Tuple[bool, Optional[str]]:
        """检查模型版本"""
        async with self.db_pool.acquire() as conn:
            # 查找最新版本
            row = await conn.fetchrow("""
                SELECT version_id, checksum FROM core_schema.model_versions
                WHERE model_name = $1
                ORDER BY created_time DESC
                LIMIT 1
            """, model_name)
            
            if not row:
                return True, None  # 新模型
            
            if row['checksum'] == checksum:
                return False, row['version_id']  # 未变更
            
            return True, row['version_id']  # 有变更
    
    async def _create_model_version(self, model_name: str, xmi_content: str, checksum: str,
                                  parent_version_id: Optional[str], author: Optional[str],
                                  metadata: XMIMetadata) -> ModelVersion:
        """创建模型版本"""
        version_id = str(uuid.uuid4())
        
        # 计算版本号
        async with self.db_pool.acquire() as conn:
            if parent_version_id:
                # 获取父版本号并递增
                parent_row = await conn.fetchrow(
                    "SELECT version_number FROM core_schema.model_versions WHERE version_id = $1",
                    parent_version_id
                )
                if parent_row:
                    parent_version = parent_row['version_number']
                    try:
                        # 尝试递增版本号
                        parts = parent_version.split('.')
                        if len(parts) >= 2:
                            major, minor = int(parts[0]), int(parts[1])
                            version_number = f"{major}.{minor + 1}"
                        else:
                            version_number = f"{parent_version}.1"
                    except ValueError:
                        version_number = "1.1"
                else:
                    version_number = "1.1"
            else:
                version_number = "1.0"
            
            # 插入版本记录
            await conn.execute("""
                INSERT INTO core_schema.model_versions (
                    version_id, model_name, version_number, parent_version_id,
                    checksum, metadata, created_by
                ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            """, version_id, model_name, version_number, parent_version_id,
                checksum, json.dumps({
                    'xmi_version': metadata.xmi_version,
                    'model_type': metadata.model_type.value,
                    'metamodel_uri': metadata.metamodel_uri,
                    'tool_name': metadata.tool_name,
                    'tool_version': metadata.tool_version
                }), author)
        
        return ModelVersion(
            version_id=version_id,
            model_name=model_name,
            version_number=version_number,
            parent_version_id=parent_version_id,
            created_by=author,
            checksum=checksum
        )
    
    async def _store_xmi_content(self, version: ModelVersion, xmi_content: str,
                               element_types: List[ElementTypeDefinition]):
        """存储XMI内容"""
        import gzip
        
        # 压缩XMI内容
        compressed_content = gzip.compress(xmi_content.encode('utf-8'))
        content_id = str(uuid.uuid4())
        
        async with self.db_pool.acquire() as conn:
            # 存储压缩的XMI内容
            await conn.execute("""
                INSERT INTO core_schema.xmi_content_store (
                    content_id, version_id, compressed_content, original_size
                ) VALUES ($1, $2, $3, $4)
            """, content_id, version.version_id, compressed_content, len(xmi_content))
            
            # 更新版本表中的XMI引用
            await conn.execute("""
                UPDATE core_schema.model_versions 
                SET xmi_content = $1 
                WHERE version_id = $2
            """, content_id, version.version_id)
    
    async def _calculate_model_changes(self, parent_version_id: str, 
                                     current_version: ModelVersion) -> List[str]:
        """计算模型变更"""
        # 这里可以实现详细的变更分析算法
        # 暂时返回简单的变更摘要
        changes = [
            f"Model updated from version {parent_version_id}",
            f"Checksum changed to {current_version.checksum[:8]}...",
            "XMI structure analyzed and element types regenerated"
        ]
        return changes
    
    async def _update_version_changes(self, version_id: str, changes: List[str]):
        """更新版本变更记录"""
        async with self.db_pool.acquire() as conn:
            await conn.execute("""
                UPDATE core_schema.model_versions 
                SET changes_summary = $1 
                WHERE version_id = $2
            """, json.dumps(changes), version_id)
    
    # 公共API方法
    
    async def get_model_versions(self, model_name: str) -> List[Dict[str, Any]]:
        """获取模型版本历史"""
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT version_id, version_number, checksum, created_time, created_by,
                       changes_summary, metadata
                FROM core_schema.model_versions
                WHERE model_name = $1
                ORDER BY created_time DESC
            """, model_name)
            
            return [dict(row) for row in rows]
    
    async def get_xmi_content(self, version_id: str) -> Optional[str]:
        """获取XMI内容"""
        import gzip
        
        async with self.db_pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT xcs.compressed_content
                FROM core_schema.model_versions mv
                JOIN core_schema.xmi_content_store xcs ON mv.xmi_content = xcs.content_id
                WHERE mv.version_id = $1
            """, version_id)
            
            if row and row['compressed_content']:
                # 解压缩内容
                compressed_content = bytes(row['compressed_content'])
                return gzip.decompress(compressed_content).decode('utf-8')
            
            return None
    
    async def rollback_to_version(self, model_name: str, target_version: str) -> Tuple[bool, str]:
        """回滚到指定版本"""
        try:
            # 获取目标版本的XMI内容
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT version_id FROM core_schema.model_versions
                    WHERE model_name = $1 AND version_number = $2
                """, model_name, target_version)
                
                if not row:
                    return False, f"版本 {target_version} 不存在"
                
                version_id = row['version_id']
                xmi_content = await self.get_xmi_content(version_id)
                
                if not xmi_content:
                    return False, f"无法获取版本 {target_version} 的XMI内容"
                
                # 重新处理XMI文件
                success, message, new_version = await self.process_xmi_file(
                    xmi_content, model_name, f"rollback_to_{target_version}"
                )
                
                if success:
                    return True, f"成功回滚到版本 {target_version}，新版本: {new_version.version_number}"
                else:
                    return False, f"回滚失败: {message}"
                
        except Exception as e:
            return False, f"回滚异常: {e}"


async def main():
    """主函数 - 演示XMI动态生成器"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'biomedical_mbse_platform',
        'user': 'mbse_user',
        'password': 'mbse_pass_2024'
    }
    
    database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    db_pool = await asyncpg.create_pool(database_url)
    
    try:
        # 初始化XMI生成器
        generator = XMIDynamicGenerator(db_pool)
        await generator.initialize()
        
        print("🎉 XMI动态生成器初始化完成！")
        print("现在可以处理标准XMI文件并自动生成动态领域了。")
        
        # 示例：处理XMI文件
        # with open('my_model.xmi', 'r', encoding='utf-8') as f:
        #     xmi_content = f.read()
        #     success, message, version = await generator.process_xmi_file(
        #         xmi_content, 'MyModel', '<EMAIL>'
        #     )
        #     print(f"处理结果: {message}")
        
    finally:
        await db_pool.close()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main()) 