#!/usr/bin/env python3
"""
UML 2.5混合策略核心演示

展示UML 2.5混合策略数据库映射的核心功能：
1. 基础继承映射分析 (improved_inheritance_mapping)
2. 扩展继承映射分析 (extended_inheritance_mapping)
3. 混合策略Schema生成 (improved_schema_generator)

这3个模块构成了UML 2.5混合策略数据库映射的完整核心。
"""

import asyncio
import asyncpg
from typing import Dict, Any, Optional

# 导入3个核心模块
from improved_inheritance_mapping import (
    get_improved_inheritance_mapping, 
    print_inheritance_analysis
)
from extended_inheritance_mapping import (
    create_extended_inheritance_mapper, 
    print_extended_inheritance_analysis
)
from improved_schema_generator import EnhancedUML25SchemaGenerator

class UMLMixedStrategyCore:
    """UML 2.5混合策略核心演示"""
    
    def __init__(self, db_config: Optional[Dict[str, Any]] = None):
        """初始化混合策略核心"""
        self.db_config = db_config or {
            'host': 'localhost',
            'port': 5432,
            'database': 'uml_core_db',
            'user': 'postgres',
            'password': 'postgres'
        }
        self.db_pool = None
        
        print("🎯 UML 2.5混合策略核心演示系统")
        print("=" * 60)
    
    async def initialize(self):
        """初始化数据库连接池"""
        try:
            self.db_pool = await asyncpg.create_pool(**self.db_config)
            print("✅ 数据库连接池初始化完成")
        except Exception as e:
            print(f"⚠️  数据库连接失败，将使用离线模式演示: {e}")
            self.db_pool = None
    
    def demo_basic_inheritance_mapping(self):
        """演示基础继承映射功能"""
        print("\n" + "="*60)
        print("🔧 1. 基础继承映射分析 (improved_inheritance_mapping)")
        print("="*60)
        
        # 获取基础映射器
        mapper = get_improved_inheritance_mapping()
        
        print("\n📊 基础映射统计:")
        print(f"  - 总类型数量: {len(mapper.inheritance_hierarchy)}")
        
        # 分析策略分布
        inherit_count = sum(1 for info in mapper.inheritance_hierarchy.values() 
                           if info.table_strategy == 'inherit')
        view_count = sum(1 for info in mapper.inheritance_hierarchy.values() 
                        if info.table_strategy == 'view')
        relation_count = sum(1 for info in mapper.inheritance_hierarchy.values() 
                            if info.table_strategy == 'relation')
        
        print(f"  - 继承表 (inherit): {inherit_count} 个")
        print(f"  - 视图表 (view): {view_count} 个") 
        print(f"  - 关系表 (relation): {relation_count} 个")
        
        # 演示核心API
        print("\n🔍 核心API演示:")
        print("  1. 获取继承链:")
        chain = mapper.get_inheritance_chain('uml:Class')
        print(f"     uml:Class -> {' -> '.join(chain)}")
        
        print("  2. 获取表策略:")
        strategy = mapper.get_table_strategy('uml:Class')
        print(f"     uml:Class 策略: {strategy}")
        
        print("  3. 获取继承表:")
        inherit_table = mapper.get_inheritance_table('uml:Class')
        print(f"     uml:Class 继承表: {inherit_table}")
        
        # 显示详细分析
        print("\n📋 详细继承分析:")
        print_inheritance_analysis()
    
    def demo_extended_inheritance_mapping(self):
        """演示扩展继承映射功能"""
        print("\n" + "="*60)
        print("🚀 2. 扩展继承映射分析 (extended_inheritance_mapping)")
        print("="*60)
        
        # 创建含SysML的扩展映射器
        extended_mapper = create_extended_inheritance_mapper(include_sysml=True)
        
        # 获取统计信息
        stats = extended_mapper.get_extended_statistics()
        print("\n📊 扩展映射统计:")
        print(f"  - UML类型: {stats['uml_types']} 个")
        print(f"  - SysML类型: {stats['sysml_types']} 个")
        print(f"  - 总类型: {stats['total_types']} 个")
        
        # 演示扩展功能
        print("\n🔍 扩展功能演示:")
        
        print("  1. 行为模型类型:")
        behavior_types = extended_mapper.get_behavior_model_types()
        print(f"     {len(behavior_types)} 个类型: {behavior_types[:5]}...")
        
        print("  2. SysML类型:")
        sysml_types = extended_mapper.get_sysml_types()
        print(f"     {len(sysml_types)} 个类型: {sysml_types}")
        
        print("  3. 用例模型类型:")
        usecase_types = extended_mapper.get_usecase_model_types()
        print(f"     {len(usecase_types)} 个类型: {usecase_types}")
        
        # 显示详细分析
        print("\n📋 详细扩展映射分析:")
        print_extended_inheritance_analysis()
    
    async def demo_schema_generation(self):
        """演示Schema生成功能"""
        print("\n" + "="*60)
        print("🏗️ 3. 混合策略Schema生成 (improved_schema_generator)")
        print("="*60)
        
        if not self.db_pool:
            print("⚠️  需要数据库连接才能演示Schema生成功能")
            print("📖 Schema生成器功能概览:")
            print("  - 基于XMI数据生成完整Schema")
            print("  - 支持表继承、视图、关系表的混合策略")
            print("  - 自动创建索引和约束")
            print("  - 提供事务管理和回滚支持")
            return
        
        try:
            # 创建Schema生成器
            generator = EnhancedUML25SchemaGenerator(self.db_pool)
            print("✅ Schema生成器初始化完成")
            
            # 模拟XMI数据
            sample_xmi = """<?xml version="1.0" encoding="UTF-8"?>
            <uml:Model xmlns:uml="http://www.eclipse.org/uml2/5.0.0/UML">
                <packagedElement xmi:type="uml:Package" name="SamplePackage">
                    <packagedElement xmi:type="uml:Class" name="SampleClass">
                        <ownedAttribute name="id" type="String"/>
                        <ownedAttribute name="name" type="String"/>
                    </packagedElement>
                </packagedElement>
            </uml:Model>"""
            
            print("\n🔧 生成Schema...")
            result = await generator.generate_schema_from_xmi(sample_xmi)
            
            if result['success']:
                print("✅ Schema生成成功!")
                data = result['data']
                
                print(f"\n📊 生成统计:")
                strategy = data.get('inheritance_strategy', {})
                print(f"  - 继承表: {strategy.get('inherit_tables', 0)} 个")
                print(f"  - 视图表: {strategy.get('view_tables', 0)} 个")
                print(f"  - 关系表: {strategy.get('relation_tables', 0)} 个")
                print(f"  - 生成时间: {data.get('generation_time', 'N/A')}")
            else:
                print(f"❌ Schema生成失败: {result['error']}")
                
        except Exception as e:
            print(f"⚠️  Schema生成演示出错: {e}")
    
    def demo_summary(self):
        """演示总结"""
        print("\n" + "="*60)
        print("📋 UML 2.5混合策略核心演示总结")
        print("="*60)
        
        print("\n🎯 核心组件:")
        print("  1. 📄 improved_inheritance_mapping.py")
        print("     - 基础UML继承映射实现")
        print("     - 表继承 + 视图 + 关系表策略")
        print("     - 5级继承层次结构")
        
        print("\n  2. 📄 extended_inheritance_mapping.py")  
        print("     - 扩展继承映射（含SysML）")
        print("     - 行为模型、用例模型支持")
        print("     - 跨语言建模能力")
        
        print("\n  3. 📄 improved_schema_generator.py")
        print("     - 混合策略Schema生成器")
        print("     - XMI解析和数据库映射")
        print("     - 事务管理和错误处理")
        
        print("\n  4. 📄 core_demo.py (本程序)")
        print("     - 核心功能演示和集成")
        print("     - API使用示例")
        print("     - 完整的混合策略展示")
        
        print("\n🏗️ 混合策略优势:")
        print("  ✅ 表继承 - 利用PostgreSQL原生特性，查询高效")
        print("  ✅ 视图策略 - 具体类的灵活查询，避免复杂JOIN")
        print("  ✅ 关系表 - 专门的关系管理，支持复杂关系分析")
        print("  ✅ 模块化设计 - 三种策略独立，易扩展维护")
        print("  ✅ 标准兼容 - 完全符合UML 2.5和SysML标准")
        
        print("\n📦 扩展功能 (在archive/目录):")
        print("  - 动态领域管理")
        print("  - 性能优化模块")
        print("  - XMI文件处理")
        print("  - PostgreSQL高级特性")
        print("  - 完整演示和测试用例")
        
        print("\n🎉 这就是UML 2.5混合策略数据库映射的核心实现！")
    
    async def run_complete_demo(self):
        """运行完整的核心演示"""
        print("🚀 开始UML 2.5混合策略核心演示...")
        
        # 初始化
        await self.initialize()
        
        # 三个核心模块演示
        self.demo_basic_inheritance_mapping()
        self.demo_extended_inheritance_mapping()
        await self.demo_schema_generation()
        
        # 总结
        self.demo_summary()
        
        # 清理
        if self.db_pool:
            await self.db_pool.close()
            print("\n✅ 数据库连接已关闭")

def main():
    """主程序入口"""
    print(__doc__)
    
    # 创建演示实例
    demo = UMLMixedStrategyCore()
    
    # 运行演示
    asyncio.run(demo.run_complete_demo())

if __name__ == "__main__":
    main() 