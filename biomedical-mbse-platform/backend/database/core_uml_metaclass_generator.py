#!/usr/bin/env python3
"""
核心UML领域元类定义表生成器

基于UML 2.5标准和现有继承映射，生成完整的核心UML元类定义表，
作为整个系统的核心数据结构和参考标准。

功能：
1. 从继承映射中提取所有UML类型定义
2. 生成完整的元类定义表(SQL和JSON格式)
3. 包含继承关系、表策略、领域信息等
4. 支持导出和验证

作者：生物医学MBSE平台开发组
版本：1.0.0
创建时间：2025年1月
"""

import asyncio
import asyncpg
import json
import csv
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
from pathlib import Path

from improved_inheritance_mapping import (
    ImprovedUMLInheritanceMapper,
    UMLTypeInfo,
    UMLInheritanceLevel
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CoreUMLMetaclassDefinition:
    """核心UML元类定义"""
    metaclass_id: str                    # 元类唯一标识
    qualified_name: str                  # 完全限定名称 (如: uml:Class)
    simple_name: str                     # 简单名称 (如: Class)
    namespace: str                       # 命名空间 (如: uml)
    is_abstract: bool                    # 是否抽象
    parent_qualified_name: Optional[str] # 父类完全限定名称
    inheritance_level: int               # 继承层级 (0-4)
    inheritance_level_name: str          # 继承层级名称
    table_strategy: str                  # 表实现策略
    description: str                     # 描述
    inheritance_chain: List[str]         # 完整继承链
    children_count: int                  # 直接子类数量
    descendants_count: int               # 所有后代数量
    concrete_descendants: List[str]      # 具体实现的后代类
    domain_category: str                 # 领域分类
    implementation_priority: int         # 实现优先级
    creation_timestamp: str              # 创建时间戳
    
class CoreUMLMetaclassGenerator:
    """核心UML元类定义生成器"""
    
    def __init__(self, schema_name: str = "uml25_core", 
                 output_dir: str = "./output"):
        self.schema_name = schema_name
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化映射器
        self.mapper = ImprovedUMLInheritanceMapper(schema_name)
        
        # 定义领域分类
        self.domain_categories = {
            'uml:Element': 'core.foundation',
            'uml:NamedElement': 'core.foundation', 
            'uml:Relationship': 'core.relationships',
            'uml:Type': 'core.typing',
            'uml:Namespace': 'core.organization',
            'uml:Feature': 'core.features',
            'uml:PackageableElement': 'core.organization',
            'uml:DirectedRelationship': 'core.relationships',
            'uml:Classifier': 'structural.classifiers',
            'uml:StructuralFeature': 'structural.features',
            'uml:BehavioralFeature': 'behavioral.features',
            'uml:Class': 'structural.classifiers',
            'uml:Interface': 'structural.classifiers',
            'uml:Enumeration': 'structural.classifiers',
            'uml:DataType': 'structural.datatypes',
            'uml:PrimitiveType': 'structural.datatypes',
            'uml:Signal': 'behavioral.communication',
            'uml:Component': 'structural.components',
            'uml:Package': 'structural.organization',
            'uml:Model': 'structural.organization',
            'uml:Profile': 'structural.organization',
            'uml:Property': 'structural.features',
            'uml:Operation': 'behavioral.features',
            'uml:Parameter': 'behavioral.features',
            'uml:Association': 'structural.relationships',
            'uml:Generalization': 'structural.relationships',
            'uml:Dependency': 'structural.relationships',
            'uml:Comment': 'documentation.annotations',
            'uml:Constraint': 'documentation.constraints'
        }
        
        # 实现优先级定义
        self.implementation_priorities = {
            UMLInheritanceLevel.ROOT: 10,        # 最高优先级 - 根类
            UMLInheritanceLevel.CORE: 9,         # 核心基类
            UMLInheritanceLevel.ABSTRACT: 8,     # 抽象层
            UMLInheritanceLevel.SPECIALIZED: 7,  # 特化层
            UMLInheritanceLevel.CONCRETE: 6      # 具体类
        }
        
    def generate_core_metaclass_definitions(self) -> List[CoreUMLMetaclassDefinition]:
        """生成核心UML元类定义列表"""
        logger.info("开始生成核心UML元类定义...")
        
        definitions = []
        inheritance_hierarchy = self.mapper.inheritance_hierarchy
        
        # 计算子类和后代信息
        children_map = self._build_children_map(inheritance_hierarchy)
        descendants_map = self._build_descendants_map(inheritance_hierarchy, children_map)
        
        for qualified_name, type_info in inheritance_hierarchy.items():
            # 解析名称
            namespace, simple_name = self._parse_qualified_name(qualified_name)
            
            # 获取继承链
            inheritance_chain = self.mapper.get_inheritance_chain(qualified_name)
            
            # 获取子类信息
            children = children_map.get(qualified_name, [])
            descendants = descendants_map.get(qualified_name, [])
            concrete_descendants = [
                desc for desc in descendants 
                if not inheritance_hierarchy[desc].is_abstract
            ]
            
            # 创建定义
            definition = CoreUMLMetaclassDefinition(
                metaclass_id=self._generate_metaclass_id(qualified_name),
                qualified_name=qualified_name,
                simple_name=simple_name,
                namespace=namespace,
                is_abstract=type_info.is_abstract,
                parent_qualified_name=type_info.parent_type,
                inheritance_level=type_info.inheritance_level.value,
                inheritance_level_name=type_info.inheritance_level.name,
                table_strategy=type_info.table_strategy,
                description=type_info.description,
                inheritance_chain=inheritance_chain,
                children_count=len(children),
                descendants_count=len(descendants),
                concrete_descendants=concrete_descendants,
                domain_category=self.domain_categories.get(qualified_name, 'unknown'),
                implementation_priority=self.implementation_priorities.get(
                    type_info.inheritance_level, 5
                ),
                creation_timestamp=datetime.now().isoformat()
            )
            
            definitions.append(definition)
        
        # 按继承层级和名称排序
        definitions.sort(key=lambda x: (x.inheritance_level, x.qualified_name))
        
        logger.info(f"生成了 {len(definitions)} 个核心UML元类定义")
        return definitions
    
    def _generate_metaclass_id(self, qualified_name: str) -> str:
        """生成元类唯一标识"""
        # 移除uml:前缀，转换为snake_case，添加_metaclass后缀
        name = qualified_name.replace('uml:', '').lower()
        # 转换为snake_case
        import re
        name = re.sub(r'([a-z])([A-Z])', r'\1_\2', name).lower()
        return f"{name}_metaclass"
    
    def _parse_qualified_name(self, qualified_name: str) -> tuple:
        """解析完全限定名称"""
        if ':' in qualified_name:
            namespace, simple_name = qualified_name.split(':', 1)
        else:
            namespace = ''
            simple_name = qualified_name
        return namespace, simple_name
    
    def _build_children_map(self, hierarchy: Dict[str, UMLTypeInfo]) -> Dict[str, List[str]]:
        """构建子类映射"""
        children_map = {}
        for qualified_name, type_info in hierarchy.items():
            if type_info.parent_type:
                parent = type_info.parent_type
                if parent not in children_map:
                    children_map[parent] = []
                children_map[parent].append(qualified_name)
        return children_map
    
    def _build_descendants_map(self, hierarchy: Dict[str, UMLTypeInfo], 
                              children_map: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """构建后代映射"""
        descendants_map = {}
        
        def get_all_descendants(qualified_name: str) -> List[str]:
            if qualified_name in descendants_map:
                return descendants_map[qualified_name]
            
            descendants = []
            children = children_map.get(qualified_name, [])
            
            for child in children:
                descendants.append(child)
                descendants.extend(get_all_descendants(child))
            
            descendants_map[qualified_name] = descendants
            return descendants
        
        # 为每个类型计算后代
        for qualified_name in hierarchy.keys():
            get_all_descendants(qualified_name)
        
        return descendants_map
    
    async def create_database_table(self, db_pool: asyncpg.Pool) -> bool:
        """在数据库中创建核心元类定义表"""
        logger.info("创建核心UML元类定义表...")
        
        try:
            async with db_pool.acquire() as conn:
                # 创建表的SQL
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.schema_name}.core_uml_metaclass_definitions (
                    -- 主键和标识
                    metaclass_id TEXT PRIMARY KEY,
                    qualified_name TEXT UNIQUE NOT NULL,
                    simple_name TEXT NOT NULL,
                    namespace TEXT NOT NULL,
                    
                    -- 基本属性
                    is_abstract BOOLEAN NOT NULL,
                    parent_qualified_name TEXT,
                    inheritance_level INTEGER NOT NULL CHECK (inheritance_level >= 0 AND inheritance_level <= 4),
                    inheritance_level_name TEXT NOT NULL,
                    table_strategy TEXT NOT NULL CHECK (table_strategy IN ('inherit', 'view', 'relation')),
                    description TEXT NOT NULL,
                    
                    -- 继承关系信息
                    inheritance_chain JSONB NOT NULL,
                    children_count INTEGER NOT NULL DEFAULT 0,
                    descendants_count INTEGER NOT NULL DEFAULT 0,
                    concrete_descendants JSONB NOT NULL,
                    
                    -- 分类和优先级
                    domain_category TEXT NOT NULL,
                    implementation_priority INTEGER NOT NULL CHECK (implementation_priority >= 1 AND implementation_priority <= 10),
                    
                    -- 时间戳
                    creation_timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                    
                    -- 外键约束
                    FOREIGN KEY (parent_qualified_name) 
                        REFERENCES {self.schema_name}.core_uml_metaclass_definitions(qualified_name) 
                        ON DELETE SET NULL
                );
                """
                
                await conn.execute(create_table_sql)
                
                # 创建索引
                indexes = [
                    f"CREATE INDEX IF NOT EXISTS idx_core_metaclass_inheritance_level ON {self.schema_name}.core_uml_metaclass_definitions(inheritance_level);",
                    f"CREATE INDEX IF NOT EXISTS idx_core_metaclass_table_strategy ON {self.schema_name}.core_uml_metaclass_definitions(table_strategy);",
                    f"CREATE INDEX IF NOT EXISTS idx_core_metaclass_domain_category ON {self.schema_name}.core_uml_metaclass_definitions(domain_category);",
                    f"CREATE INDEX IF NOT EXISTS idx_core_metaclass_priority ON {self.schema_name}.core_uml_metaclass_definitions(implementation_priority);",
                    f"CREATE INDEX IF NOT EXISTS idx_core_metaclass_abstract ON {self.schema_name}.core_uml_metaclass_definitions(is_abstract);",
                    f"CREATE INDEX IF NOT EXISTS idx_core_metaclass_parent ON {self.schema_name}.core_uml_metaclass_definitions(parent_qualified_name) WHERE parent_qualified_name IS NOT NULL;",
                    f"CREATE INDEX IF NOT EXISTS idx_core_metaclass_inheritance_chain ON {self.schema_name}.core_uml_metaclass_definitions USING GIN(inheritance_chain);"
                ]
                
                for index_sql in indexes:
                    await conn.execute(index_sql)
                
                logger.info("核心UML元类定义表创建成功")
                return True
                
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            return False
    
    async def populate_database_table(self, db_pool: asyncpg.Pool, 
                                    definitions: List[CoreUMLMetaclassDefinition]) -> bool:
        """将元类定义数据填充到数据库表中"""
        logger.info("填充核心UML元类定义数据到数据库...")
        
        try:
            async with db_pool.acquire() as conn:
                # 清除现有数据
                await conn.execute(f"DELETE FROM {self.schema_name}.core_uml_metaclass_definitions")
                
                # 插入数据
                insert_sql = f"""
                INSERT INTO {self.schema_name}.core_uml_metaclass_definitions
                (metaclass_id, qualified_name, simple_name, namespace, is_abstract,
                 parent_qualified_name, inheritance_level, inheritance_level_name,
                 table_strategy, description, inheritance_chain, children_count,
                 descendants_count, concrete_descendants, domain_category,
                 implementation_priority, creation_timestamp)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
                """
                
                for definition in definitions:
                    await conn.execute(
                        insert_sql,
                        definition.metaclass_id,
                        definition.qualified_name,
                        definition.simple_name,
                        definition.namespace,
                        definition.is_abstract,
                        definition.parent_qualified_name,
                        definition.inheritance_level,
                        definition.inheritance_level_name,
                        definition.table_strategy,
                        definition.description,
                        json.dumps(definition.inheritance_chain),
                        definition.children_count,
                        definition.descendants_count,
                        json.dumps(definition.concrete_descendants),
                        definition.domain_category,
                        definition.implementation_priority,
                        definition.creation_timestamp
                    )
                
                logger.info(f"成功插入 {len(definitions)} 条核心UML元类定义记录")
                return True
                
        except Exception as e:
            logger.error(f"填充数据库表失败: {e}")
            return False
    
    def export_to_json(self, definitions: List[CoreUMLMetaclassDefinition], 
                      filename: str = "core_uml_metaclass_definitions.json") -> bool:
        """导出为JSON格式"""
        try:
            output_file = self.output_dir / filename
            
            # 转换为可序列化的字典列表
            data = {
                'metadata': {
                    'generator': 'CoreUMLMetaclassGenerator',
                    'version': '1.0.0',
                    'generated_at': datetime.now().isoformat(),
                    'total_metaclasses': len(definitions),
                    'schema_name': self.schema_name
                },
                'metaclass_definitions': [asdict(definition) for definition in definitions]
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"JSON导出成功: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"JSON导出失败: {e}")
            return False
    
    def export_to_csv(self, definitions: List[CoreUMLMetaclassDefinition],
                     filename: str = "core_uml_metaclass_definitions.csv") -> bool:
        """导出为CSV格式"""
        try:
            output_file = self.output_dir / filename
            
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入表头
                headers = [
                    'metaclass_id', 'qualified_name', 'simple_name', 'namespace',
                    'is_abstract', 'parent_qualified_name', 'inheritance_level',
                    'inheritance_level_name', 'table_strategy', 'description',
                    'inheritance_chain', 'children_count', 'descendants_count',
                    'concrete_descendants', 'domain_category', 'implementation_priority',
                    'creation_timestamp'
                ]
                writer.writerow(headers)
                
                # 写入数据
                for definition in definitions:
                    row = [
                        definition.metaclass_id,
                        definition.qualified_name,
                        definition.simple_name,
                        definition.namespace,
                        definition.is_abstract,
                        definition.parent_qualified_name or '',
                        definition.inheritance_level,
                        definition.inheritance_level_name,
                        definition.table_strategy,
                        definition.description,
                        ' -> '.join(definition.inheritance_chain),
                        definition.children_count,
                        definition.descendants_count,
                        ', '.join(definition.concrete_descendants),
                        definition.domain_category,
                        definition.implementation_priority,
                        definition.creation_timestamp
                    ]
                    writer.writerow(row)
            
            logger.info(f"CSV导出成功: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"CSV导出失败: {e}")
            return False
    
    def export_sql_insert_statements(self, definitions: List[CoreUMLMetaclassDefinition],
                                   filename: str = "core_uml_metaclass_definitions.sql") -> bool:
        """导出为SQL插入语句"""
        try:
            output_file = self.output_dir / filename
            
            with open(output_file, 'w', encoding='utf-8') as f:
                # 写入文件头
                f.write(f"""-- 核心UML元类定义SQL插入语句
-- 生成时间: {datetime.now().isoformat()}
-- 生成器: CoreUMLMetaclassGenerator v1.0.0
-- 总计: {len(definitions)} 个元类定义

-- 创建Schema (如果不存在)
CREATE SCHEMA IF NOT EXISTS {self.schema_name};

-- 清除现有数据
DELETE FROM {self.schema_name}.core_uml_metaclass_definitions;

-- 插入核心UML元类定义数据
""")
                
                # 写入插入语句
                for definition in definitions:
                    inheritance_chain_json = json.dumps(definition.inheritance_chain)
                    concrete_descendants_json = json.dumps(definition.concrete_descendants)
                    parent_value = f"'{definition.parent_qualified_name}'" if definition.parent_qualified_name else "NULL"
                    
                    f.write(f"""
INSERT INTO {self.schema_name}.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    '{definition.metaclass_id}',
    '{definition.qualified_name}',
    '{definition.simple_name}',
    '{definition.namespace}',
    {str(definition.is_abstract).lower()},
    {parent_value},
    {definition.inheritance_level},
    '{definition.inheritance_level_name}',
    '{definition.table_strategy}',
    '{definition.description.replace("'", "''")}',
    '{inheritance_chain_json.replace("'", "''")}',
    {definition.children_count},
    {definition.descendants_count},
    '{concrete_descendants_json.replace("'", "''")}',
    '{definition.domain_category}',
    {definition.implementation_priority},
    '{definition.creation_timestamp}'
);""")
                
                f.write("\n\n-- 提交事务\nCOMMIT;\n")
            
            logger.info(f"SQL导出成功: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"SQL导出失败: {e}")
            return False
    
    def generate_summary_report(self, definitions: List[CoreUMLMetaclassDefinition],
                              filename: str = "core_uml_metaclass_summary.md") -> bool:
        """生成汇总报告"""
        try:
            output_file = self.output_dir / filename
            
            # 统计信息
            total_count = len(definitions)
            abstract_count = sum(1 for d in definitions if d.is_abstract)
            concrete_count = total_count - abstract_count
            
            # 按层级统计
            level_stats = {}
            for definition in definitions:
                level = definition.inheritance_level_name
                if level not in level_stats:
                    level_stats[level] = {'total': 0, 'abstract': 0, 'concrete': 0}
                level_stats[level]['total'] += 1
                if definition.is_abstract:
                    level_stats[level]['abstract'] += 1
                else:
                    level_stats[level]['concrete'] += 1
            
            # 按策略统计
            strategy_stats = {}
            for definition in definitions:
                strategy = definition.table_strategy
                strategy_stats[strategy] = strategy_stats.get(strategy, 0) + 1
            
            # 按领域分类统计
            domain_stats = {}
            for definition in definitions:
                domain = definition.domain_category
                domain_stats[domain] = domain_stats.get(domain, 0) + 1
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"""# 核心UML元类定义汇总报告

## 基本信息
- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **生成器版本**: CoreUMLMetaclassGenerator v1.0.0
- **Schema名称**: {self.schema_name}
- **元类总数**: {total_count}
- **抽象类数量**: {abstract_count}
- **具体类数量**: {concrete_count}

## 继承层级统计

| 层级 | 总数 | 抽象 | 具体 | 描述 |
|------|------|------|------|------|""")
                
                level_order = ['ROOT', 'CORE', 'ABSTRACT', 'SPECIALIZED', 'CONCRETE']
                level_descriptions = {
                    'ROOT': '根层级 - Element',
                    'CORE': '核心层级 - NamedElement, Relationship',
                    'ABSTRACT': '抽象层级 - Type, Namespace, Feature',
                    'SPECIALIZED': '特化层级 - Classifier, StructuralFeature',
                    'CONCRETE': '具体层级 - Class, Property, Operation'
                }
                
                for level in level_order:
                    stats = level_stats.get(level, {'total': 0, 'abstract': 0, 'concrete': 0})
                    desc = level_descriptions.get(level, '')
                    f.write(f"| {level} | {stats['total']} | {stats['abstract']} | {stats['concrete']} | {desc} |\n")
                
                f.write(f"""
## 表实现策略统计

| 策略 | 数量 | 百分比 | 描述 |
|------|------|--------|------|""")
                
                strategy_descriptions = {
                    'inherit': '表继承策略 - PostgreSQL原生继承',
                    'view': '视图策略 - 组合继承链视图',
                    'relation': '关系表策略 - 独立关系管理'
                }
                
                for strategy, count in sorted(strategy_stats.items()):
                    percentage = (count / total_count) * 100
                    desc = strategy_descriptions.get(strategy, '')
                    f.write(f"| {strategy} | {count} | {percentage:.1f}% | {desc} |\n")
                
                f.write(f"""
## 领域分类统计

| 领域分类 | 数量 | 百分比 |
|----------|------|--------|""")
                
                for domain, count in sorted(domain_stats.items()):
                    percentage = (count / total_count) * 100
                    f.write(f"| {domain} | {count} | {percentage:.1f}% |\n")
                
                f.write(f"""
## 详细元类列表

### 根层级 (ROOT)
""")
                
                for level in level_order:
                    level_definitions = [d for d in definitions if d.inheritance_level_name == level]
                    if level_definitions:
                        f.write(f"\n### {level}层级\n\n")
                        for definition in level_definitions:
                            abstract_marker = " (抽象)" if definition.is_abstract else ""
                            f.write(f"- **{definition.qualified_name}**{abstract_marker}\n")
                            f.write(f"  - 策略: {definition.table_strategy}\n")
                            f.write(f"  - 领域: {definition.domain_category}\n")
                            f.write(f"  - 子类: {definition.children_count}个\n")
                            f.write(f"  - 后代: {definition.descendants_count}个\n")
                            f.write(f"  - 描述: {definition.description}\n\n")
            
            logger.info(f"汇总报告生成成功: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"汇总报告生成失败: {e}")
            return False

async def main():
    """主函数 - 运行完整的元类定义生成流程"""
    
    # 数据库配置 (根据实际情况修改)
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'user': 'postgres',
        'password': 'your_password',
        'database': 'biomedical_mbse_platform'
    }
    
    # 创建生成器
    generator = CoreUMLMetaclassGenerator()
    
    print("🎯 核心UML元类定义生成器")
    print("=" * 50)
    
    try:
        # 1. 生成元类定义
        print("📋 生成核心UML元类定义...")
        definitions = generator.generate_core_metaclass_definitions()
        
        # 2. 导出各种格式
        print("💾 导出为多种格式...")
        generator.export_to_json(definitions)
        generator.export_to_csv(definitions)
        generator.export_sql_insert_statements(definitions)
        generator.generate_summary_report(definitions)
        
        # 3. 创建数据库表并填充数据 (可选)
        create_db = input("是否创建数据库表并填充数据? (y/N): ").strip().lower()
        if create_db == 'y':
            print("🗄️ 连接数据库并创建表...")
            database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
            
            db_pool = await asyncpg.create_pool(database_url)
            
            # 创建表
            success = await generator.create_database_table(db_pool)
            if success:
                # 填充数据
                success = await generator.populate_database_table(db_pool, definitions)
                
                if success:
                    print("✅ 数据库表创建和数据填充成功!")
                else:
                    print("❌ 数据填充失败!")
            else:
                print("❌ 数据库表创建失败!")
            
            await db_pool.close()
        
        print("\n📊 生成统计:")
        print(f"  总元类数量: {len(definitions)}")
        print(f"  抽象类: {sum(1 for d in definitions if d.is_abstract)}")
        print(f"  具体类: {sum(1 for d in definitions if not d.is_abstract)}")
        print(f"  输出目录: {generator.output_dir.absolute()}")
        
        print("\n✅ 核心UML元类定义生成完成!")
        
    except Exception as e:
        logger.error(f"生成过程中发生错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1) 