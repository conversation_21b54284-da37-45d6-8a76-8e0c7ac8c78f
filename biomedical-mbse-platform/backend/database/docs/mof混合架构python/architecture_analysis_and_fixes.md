# 混合UML元模型架构缺陷分析与修复建议

## 🚨 关键架构缺陷识别

### 1. **数据一致性危机**

#### 问题分析
当前混合存储设计存在严重的数据一致性风险：

```python
# 当前的混合存储实现存在问题
class HybridStorageManager:
    async def save_element(self, element: UMLElement, sync_secondary: bool = True) -> bool:
        # 主存储保存成功，但备用存储失败会导致不一致
        success = await self.primary_storage.save_element(element)
        
        if success and sync_secondary and self.secondary_storage:
            # ❌ 这里如果失败，两个存储就不一致了
            await self.secondary_storage.save_element(element)
        
        return success  # ❌ 返回的成功状态不反映备用存储的状态
```

#### 修复方案：实现分布式事务
```python
from enum import Enum
from dataclasses import dataclass
from typing import List, Optional
import asyncio

class TransactionPhase(Enum):
    PREPARE = "prepare"
    COMMIT = "commit"
    ROLLBACK = "rollback"

@dataclass
class TransactionOperation:
    storage_id: str
    operation_type: str  # save, delete, update
    element_data: Dict[str, Any]
    rollback_data: Optional[Dict[str, Any]] = None

class DistributedTransactionManager:
    """分布式事务管理器 - 解决数据一致性问题"""
    
    def __init__(self):
        self.active_transactions: Dict[str, List[TransactionOperation]] = {}
        self.storage_adapters: Dict[str, IMetamodelStorage] = {}
    
    async def begin_transaction(self, transaction_id: str) -> bool:
        """开始分布式事务"""
        self.active_transactions[transaction_id] = []
        return True
    
    async def add_operation(self, transaction_id: str, 
                           storage_id: str, operation_type: str, 
                           element_data: Dict[str, Any]) -> bool:
        """添加事务操作"""
        if transaction_id not in self.active_transactions:
            return False
        
        # 准备回滚数据
        rollback_data = None
        if operation_type in ['update', 'delete']:
            storage = self.storage_adapters[storage_id]
            rollback_data = await storage.get_element(element_data['id'])
        
        operation = TransactionOperation(
            storage_id=storage_id,
            operation_type=operation_type,
            element_data=element_data,
            rollback_data=rollback_data
        )
        
        self.active_transactions[transaction_id].append(operation)
        return True
    
    async def commit_transaction(self, transaction_id: str) -> bool:
        """两阶段提交"""
        if transaction_id not in self.active_transactions:
            return False
        
        operations = self.active_transactions[transaction_id]
        
        # 阶段1: 准备阶段
        prepare_results = []
        for operation in operations:
            storage = self.storage_adapters[operation.storage_id]
            try:
                # 验证操作可行性（约束检查等）
                can_proceed = await self._validate_operation(storage, operation)
                prepare_results.append((operation, can_proceed))
            except Exception as e:
                logger.error(f"Prepare phase failed for {operation.storage_id}: {e}")
                prepare_results.append((operation, False))
        
        # 如果有任何存储准备失败，回滚所有操作
        if not all(result[1] for result in prepare_results):
            await self._rollback_transaction(transaction_id)
            return False
        
        # 阶段2: 提交阶段
        commit_results = []
        for operation, _ in prepare_results:
            storage = self.storage_adapters[operation.storage_id]
            try:
                success = await self._execute_operation(storage, operation)
                commit_results.append((operation, success))
            except Exception as e:
                logger.error(f"Commit phase failed for {operation.storage_id}: {e}")
                commit_results.append((operation, False))
        
        # 如果有提交失败，进行补偿
        if not all(result[1] for result in commit_results):
            await self._compensate_partial_commit(transaction_id, commit_results)
            return False
        
        # 清理事务
        del self.active_transactions[transaction_id]
        return True
    
    async def _validate_operation(self, storage: IMetamodelStorage, 
                                 operation: TransactionOperation) -> bool:
        """验证操作可行性"""
        # 检查约束、权限等
        return True
    
    async def _execute_operation(self, storage: IMetamodelStorage, 
                                operation: TransactionOperation) -> bool:
        """执行具体操作"""
        if operation.operation_type == 'save':
            return await storage.save_element(operation.element_data)
        elif operation.operation_type == 'delete':
            return await storage.delete_element(operation.element_data['id'])
        elif operation.operation_type == 'update':
            return await storage.save_element(operation.element_data)
        return False
```

### 2. **OCL约束引擎过度简化**

#### 问题分析
当前的OCL约束引擎实现过于简单，缺乏完整的语法支持：

```python
# 当前实现的问题
class OCLInterpreter:
    def evaluate(self, expression: str, context: Dict) -> bool:
        # ❌ 硬编码的模式匹配，无法处理复杂OCL表达式
        if "self.isAbstract implies" in expression:
            return self._evaluate_implication(expression, context)
        # ❌ 缺乏完整的OCL语法解析
        return True  # ❌ 默认返回True是危险的
```

#### 修复方案：完整的OCL引擎
```python
from typing import Any, Dict, List, Union
import re
from dataclasses import dataclass
from abc import ABC, abstractmethod

@dataclass
class OCLToken:
    type: str
    value: str
    position: int

class OCLLexer:
    """OCL词法分析器"""
    
    TOKEN_PATTERNS = [
        ('KEYWORD', r'\b(self|implies|and|or|not|if|then|else|endif|forAll|exists|select|collect)\b'),
        ('OPERATOR', r'(->|<>|<=|>=|<|>|=|\+|\-|\*|/)'),
        ('IDENTIFIER', r'[a-zA-Z_][a-zA-Z0-9_]*'),
        ('NUMBER', r'\d+(\.\d+)?'),
        ('STRING', r'"[^"]*"'),
        ('LPAREN', r'\('),
        ('RPAREN', r'\)'),
        ('LBRACKET', r'\['),
        ('RBRACKET', r'\]'),
        ('DOT', r'\.'),
        ('COMMA', r','),
        ('WHITESPACE', r'\s+'),
    ]
    
    def tokenize(self, expression: str) -> List[OCLToken]:
        """词法分析"""
        tokens = []
        position = 0
        
        while position < len(expression):
            matched = False
            
            for token_type, pattern in self.TOKEN_PATTERNS:
                regex = re.compile(pattern)
                match = regex.match(expression, position)
                
                if match:
                    value = match.group(0)
                    if token_type != 'WHITESPACE':  # 跳过空白符
                        tokens.append(OCLToken(token_type, value, position))
                    position = match.end()
                    matched = True
                    break
            
            if not matched:
                raise ValueError(f"Invalid character at position {position}: {expression[position]}")
        
        return tokens

@dataclass
class OCLExpression(ABC):
    """OCL表达式抽象基类"""
    pass

@dataclass
class OCLPropertyAccess(OCLExpression):
    source: OCLExpression
    property_name: str

@dataclass
class OCLMethodCall(OCLExpression):
    source: OCLExpression
    method_name: str
    arguments: List[OCLExpression]

@dataclass
class OCLBinaryOperation(OCLExpression):
    left: OCLExpression
    operator: str
    right: OCLExpression

class OCLParser:
    """OCL语法分析器"""
    
    def __init__(self):
        self.tokens: List[OCLToken] = []
        self.position = 0
    
    def parse(self, expression: str) -> OCLExpression:
        """解析OCL表达式为AST"""
        lexer = OCLLexer()
        self.tokens = lexer.tokenize(expression)
        self.position = 0
        
        return self._parse_expression()
    
    def _parse_expression(self) -> OCLExpression:
        """解析表达式"""
        return self._parse_implies()
    
    def _parse_implies(self) -> OCLExpression:
        """解析蕴含表达式"""
        left = self._parse_or()
        
        while self._current_token() and self._current_token().value == 'implies':
            operator = self._consume().value
            right = self._parse_or()
            left = OCLBinaryOperation(left, operator, right)
        
        return left
    
    def _parse_or(self) -> OCLExpression:
        """解析或表达式"""
        left = self._parse_and()
        
        while self._current_token() and self._current_token().value == 'or':
            operator = self._consume().value
            right = self._parse_and()
            left = OCLBinaryOperation(left, operator, right)
        
        return left
    
    def _current_token(self) -> Optional[OCLToken]:
        """获取当前token"""
        if self.position < len(self.tokens):
            return self.tokens[self.position]
        return None
    
    def _consume(self) -> OCLToken:
        """消费当前token"""
        token = self._current_token()
        self.position += 1
        return token

class OCLEvaluator:
    """OCL表达式求值器"""
    
    def __init__(self, metamodel_repository):
        self.repository = metamodel_repository
    
    def evaluate(self, ast: OCLExpression, context: Dict[str, Any]) -> Any:
        """求值OCL表达式"""
        if isinstance(ast, OCLBinaryOperation):
            return self._evaluate_binary_operation(ast, context)
        elif isinstance(ast, OCLPropertyAccess):
            return self._evaluate_property_access(ast, context)
        elif isinstance(ast, OCLMethodCall):
            return self._evaluate_method_call(ast, context)
        else:
            raise ValueError(f"Unsupported expression type: {type(ast)}")
    
    def _evaluate_binary_operation(self, expr: OCLBinaryOperation, context: Dict) -> Any:
        """求值二元操作"""
        left_val = self.evaluate(expr.left, context)
        
        # 短路求值
        if expr.operator == 'implies':
            if not left_val:
                return True  # false implies anything is true
            right_val = self.evaluate(expr.right, context)
            return right_val
        elif expr.operator == 'and':
            if not left_val:
                return False
            return self.evaluate(expr.right, context)
        elif expr.operator == 'or':
            if left_val:
                return True
            return self.evaluate(expr.right, context)
        
        # 其他操作需要计算右值
        right_val = self.evaluate(expr.right, context)
        
        if expr.operator == '=':
            return left_val == right_val
        elif expr.operator == '<>':
            return left_val != right_val
        elif expr.operator == '<':
            return left_val < right_val
        elif expr.operator == '>':
            return left_val > right_val
        # ... 其他操作符
        
        raise ValueError(f"Unsupported operator: {expr.operator}")

class ProductionConstraintEngine:
    """生产级OCL约束引擎"""
    
    def __init__(self, repository):
        self.repository = repository
        self.parser = OCLParser()
        self.evaluator = OCLEvaluator(repository)
        self.constraint_cache: Dict[str, OCLExpression] = {}
    
    async def validate_element(self, element_id: str, element_data: Dict) -> List[Dict]:
        """完整的元素验证"""
        results = []
        metaclass = element_data.get('metaclass')
        
        # 获取适用的约束
        constraints = await self._get_applicable_constraints(metaclass)
        
        for constraint in constraints:
            try:
                # 解析或从缓存获取AST
                constraint_ast = await self._get_constraint_ast(constraint['expression'])
                
                # 准备上下文
                context = {
                    'self': element_data,
                    'repository': self.repository
                }
                
                # 求值
                result = self.evaluator.evaluate(constraint_ast, context)
                
                results.append({
                    'constraint': constraint['name'],
                    'result': bool(result),
                    'severity': constraint.get('severity', 'error'),
                    'message': constraint.get('description') if not result else None,
                    'execution_time': 0  # 记录执行时间
                })
                
            except Exception as e:
                results.append({
                    'constraint': constraint['name'],
                    'result': False,
                    'severity': 'error',
                    'message': f"Constraint evaluation failed: {str(e)}",
                    'execution_time': 0
                })
        
        return results
    
    async def _get_constraint_ast(self, expression: str) -> OCLExpression:
        """获取约束的AST（带缓存）"""
        if expression not in self.constraint_cache:
            self.constraint_cache[expression] = self.parser.parse(expression)
        return self.constraint_cache[expression]
```

### 3. **错误处理和回滚机制缺失**

#### 问题分析
当前实现缺乏完善的错误处理和回滚机制：

```python
# 当前的问题实现
async def save_element(self, element: UMLElement) -> bool:
    try:
        # 保存操作
        return True
    except Exception as e:
        logger.error(f"Failed to save: {e}")
        return False  # ❌ 简单返回False，没有清理或回滚
```

#### 修复方案：完善的错误处理框架
```python
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Callable, Any
import traceback

class MetamodelException(Exception):
    """元模型操作异常基类"""
    pass

class ValidationException(MetamodelException):
    """验证异常"""
    def __init__(self, constraint_name: str, element_id: str, message: str):
        self.constraint_name = constraint_name
        self.element_id = element_id
        super().__init__(message)

class StorageException(MetamodelException):
    """存储异常"""
    pass

class ConsistencyException(MetamodelException):
    """一致性异常"""
    pass

@dataclass
class OperationContext:
    """操作上下文"""
    operation_id: str
    operation_type: str
    affected_elements: List[str]
    rollback_operations: List[Callable]
    started_at: datetime
    metadata: Dict[str, Any]

class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self):
        self.active_operations: Dict[str, OperationContext] = {}
        self.error_handlers: Dict[type, Callable] = {}
    
    @asynccontextmanager
    async def operation_context(self, operation_type: str, 
                               element_ids: List[str]) -> AsyncGenerator[OperationContext, None]:
        """操作上下文管理器"""
        operation_id = str(uuid.uuid4())
        context = OperationContext(
            operation_id=operation_id,
            operation_type=operation_type,
            affected_elements=element_ids,
            rollback_operations=[],
            started_at=datetime.now(),
            metadata={}
        )
        
        self.active_operations[operation_id] = context
        
        try:
            yield context
            # 操作成功，清理上下文
            await self._cleanup_operation(operation_id)
        except Exception as e:
            # 操作失败，执行回滚
            await self._rollback_operation(operation_id, e)
            raise
        finally:
            if operation_id in self.active_operations:
                del self.active_operations[operation_id]
    
    async def _rollback_operation(self, operation_id: str, error: Exception):
        """回滚操作"""
        context = self.active_operations.get(operation_id)
        if not context:
            return
        
        logger.error(f"Rolling back operation {operation_id} due to error: {error}")
        
        # 执行回滚操作（逆序）
        for rollback_op in reversed(context.rollback_operations):
            try:
                await rollback_op()
            except Exception as rollback_error:
                logger.error(f"Rollback operation failed: {rollback_error}")
        
        # 记录错误
        await self._log_operation_failure(context, error)
    
    def add_rollback_operation(self, operation_id: str, rollback_op: Callable):
        """添加回滚操作"""
        context = self.active_operations.get(operation_id)
        if context:
            context.rollback_operations.append(rollback_op)

class RobustHybridStorageManager:
    """具有完善错误处理的混合存储管理器"""
    
    def __init__(self):
        self.primary_storage: Optional[IMetamodelStorage] = None
        self.secondary_storage: Optional[IMetamodelStorage] = None
        self.transaction_manager = DistributedTransactionManager()
        self.error_recovery = ErrorRecoveryManager()
    
    async def save_element(self, element: UMLElement) -> bool:
        """保存元素（带完整错误处理）"""
        async with self.error_recovery.operation_context(
            "save_element", [element.id]
        ) as context:
            
            # 验证元素
            validation_results = await self._validate_element_comprehensive(element)
            if not all(r['result'] for r in validation_results):
                failed_constraints = [r['constraint'] for r in validation_results if not r['result']]
                raise ValidationException(
                    constraint_name=failed_constraints[0],
                    element_id=element.id,
                    message=f"Element validation failed: {failed_constraints}"
                )
            
            # 使用分布式事务
            transaction_id = str(uuid.uuid4())
            await self.transaction_manager.begin_transaction(transaction_id)
            
            try:
                # 添加主存储操作
                await self.transaction_manager.add_operation(
                    transaction_id, "primary", "save", asdict(element)
                )
                
                # 添加备用存储操作（如果存在）
                if self.secondary_storage:
                    await self.transaction_manager.add_operation(
                        transaction_id, "secondary", "save", asdict(element)
                    )
                
                # 提交事务
                success = await self.transaction_manager.commit_transaction(transaction_id)
                
                if not success:
                    raise StorageException(f"Failed to save element {element.id}")
                
                return True
                
            except Exception as e:
                await self.transaction_manager.rollback_transaction(transaction_id)
                raise StorageException(f"Storage operation failed: {str(e)}") from e
    
    async def _validate_element_comprehensive(self, element: UMLElement) -> List[Dict]:
        """全面的元素验证"""
        results = []
        
        # 1. 基础数据验证
        if not element.id:
            results.append({
                'constraint': 'element_id_required',
                'result': False,
                'severity': 'error',
                'message': 'Element ID is required'
            })
        
        if not element.metaclass:
            results.append({
                'constraint': 'metaclass_required',
                'result': False,
                'severity': 'error',
                'message': 'Metaclass is required'
            })
        
        # 2. 元类验证
        metaclass_valid = await self._validate_metaclass(element.metaclass)
        if not metaclass_valid:
            results.append({
                'constraint': 'valid_metaclass',
                'result': False,
                'severity': 'error',
                'message': f'Invalid metaclass: {element.metaclass}'
            })
        
        # 3. 业务约束验证
        if results and not all(r['result'] for r in results):
            return results  # 如果基础验证失败，跳过业务验证
        
        business_results = await self._validate_business_constraints(element)
        results.extend(business_results)
        
        return results
```

### 4. **并发控制缺陷**

#### 问题分析
当前设计没有考虑并发访问控制：

```python
# 缺少并发控制的问题
async def save_element(self, element: UMLElement):
    # ❌ 没有锁机制，可能导致并发修改冲突
    existing = await self.get_element(element.id)
    # ❌ 在这个间隙，其他线程可能已经修改了元素
    await self._do_save(element)
```

#### 修复方案：分布式锁和版本控制
```python
import asyncio
from typing import Optional
import hashlib

class DistributedLockManager:
    """分布式锁管理器"""
    
    def __init__(self, redis_client=None):
        self.redis_client = redis_client
        self.local_locks: Dict[str, asyncio.Lock] = {}
    
    @asynccontextmanager
    async def acquire_lock(self, resource_id: str, timeout: float = 30.0):
        """获取分布式锁"""
        lock_key = f"metamodel:lock:{resource_id}"
        
        if self.redis_client:
            # 使用Redis分布式锁
            async with self._redis_lock(lock_key, timeout):
                yield
        else:
            # 使用本地锁
            if resource_id not in self.local_locks:
                self.local_locks[resource_id] = asyncio.Lock()
            
            async with self.local_locks[resource_id]:
                yield
    
    @asynccontextmanager
    async def _redis_lock(self, lock_key: str, timeout: float):
        """Redis分布式锁实现"""
        lock_value = str(uuid.uuid4())
        acquired = False
        
        try:
            # 尝试获取锁
            acquired = await self.redis_client.set(
                lock_key, lock_value, ex=int(timeout), nx=True
            )
            
            if not acquired:
                raise Exception(f"Failed to acquire lock for {lock_key}")
            
            yield
            
        finally:
            if acquired:
                # 使用Lua脚本确保原子性释放
                lua_script = """
                if redis.call("get", KEYS[1]) == ARGV[1] then
                    return redis.call("del", KEYS[1])
                else
                    return 0
                end
                """
                await self.redis_client.eval(lua_script, 1, lock_key, lock_value)

@dataclass
class ElementVersion:
    """元素版本信息"""
    element_id: str
    version_number: int
    content_hash: str
    modified_at: datetime
    modified_by: str

class OptimisticConcurrencyController:
    """乐观并发控制"""
    
    def __init__(self, storage: IMetamodelStorage):
        self.storage = storage
        self.lock_manager = DistributedLockManager()
    
    async def save_element_with_version_check(self, element: UMLElement, 
                                            expected_version: Optional[int] = None) -> bool:
        """带版本检查的保存操作"""
        async with self.lock_manager.acquire_lock(element.id):
            # 获取当前版本
            current_element = await self.storage.get_element(element.id)
            
            if current_element and expected_version is not None:
                current_version = current_element.metadata.get('version_number', 0)
                if current_version != expected_version:
                    raise ConsistencyException(
                        f"Version conflict: expected {expected_version}, "
                        f"but current is {current_version}"
                    )
            
            # 计算新版本
            new_version = (current_element.metadata.get('version_number', 0) + 1 
                          if current_element else 1)
            
            # 计算内容哈希
            content_hash = self._calculate_content_hash(element)
            
            # 更新版本信息
            element.metadata.update({
                'version_number': new_version,
                'content_hash': content_hash,
                'modified_at': datetime.now().isoformat(),
                'previous_version': expected_version
            })
            
            # 保存
            return await self.storage.save_element(element)
    
    def _calculate_content_hash(self, element: UMLElement) -> str:
        """计算元素内容哈希"""
        # 排除版本相关的元数据
        content = {
            'id': element.id,
            'metaclass': element.metaclass,
            'name': element.name,
            'properties': element.properties,
            'relationships': element.relationships
        }
        
        content_str = json.dumps(content, sort_keys=True)
        return hashlib.sha256(content_str.encode()).hexdigest()
```

### 5. **配置复杂性问题**

#### 问题分析
当前的配置系统过于复杂，难以管理：

```yaml
# 配置过于复杂的例子
storage:
  backend: "hybrid"
  connection_string: "postgresql://..."
  file_repository_path: "./repo"
  cache_enabled: true
  validation_mode: "lenient"
  backup_enabled: true
  # ... 更多配置项
```

#### 修复方案：简化配置和智能默认值
```python
from dataclasses import dataclass, field
from typing import Optional, Dict, Any
import os

@dataclass
class SimpleStorageConfig:
    """简化的存储配置"""
    
    # 必需配置（只有这一个）
    deployment_mode: str = "development"  # development, testing, production
    
    # 可选配置（智能默认值）
    database_url: Optional[str] = None
    repository_path: Optional[str] = None
    
    # 自动推导的配置
    backend: Optional[str] = field(init=False)
    cache_enabled: Optional[bool] = field(init=False)
    validation_mode: Optional[str] = field(init=False)
    
    def __post_init__(self):
        """根据部署模式自动配置"""
        if self.deployment_mode == "development":
            self.backend = "file_system"
            self.repository_path = self.repository_path or "./dev_repository"
            self.cache_enabled = True
            self.validation_mode = "lenient"
        
        elif self.deployment_mode == "testing":
            self.backend = "sqlite"
            self.database_url = self.database_url or "sqlite:///test_metamodel.db"
            self.cache_enabled = False  # 确保测试的一致性
            self.validation_mode = "strict"
        
        elif self.deployment_mode == "production":
            if not self.database_url:
                raise ValueError("Production mode requires database_url")
            self.backend = "postgresql"
            self.cache_enabled = True
            self.validation_mode = "strict"
        
        else:
            raise ValueError(f"Invalid deployment_mode: {self.deployment_mode}")

class ConfigurationManager:
    """配置管理器 - 简化版本"""
    
    @staticmethod
    def from_environment() -> SimpleStorageConfig:
        """从环境变量创建配置"""
        return SimpleStorageConfig(
            deployment_mode=os.getenv('METAMODEL_MODE', 'development'),
            database_url=os.getenv('DATABASE_URL'),
            repository_path=os.getenv('REPOSITORY_PATH')
        )
    
    @staticmethod
    def from_dict(config_dict: Dict[str, Any]) -> SimpleStorageConfig:
        """从字典创建配置"""
        return SimpleStorageConfig(**config_dict)
```

## 📊 修复后的核心架构

```python
class ImprovedUnifiedMetamodelAPI:
    """改进的统一元模型API"""
    
    def __init__(self):
        self.storage_manager: Optional[RobustHybridStorageManager] = None
        self.constraint_engine: Optional[ProductionConstraintEngine] = None
        self.concurrency_controller: Optional[OptimisticConcurrencyController] = None
        self.config: Optional[SimpleStorageConfig] = None
    
    async def initialize(self, config: Optional[SimpleStorageConfig] = None):
        """简化的初始化过程"""
        self.config = config or ConfigurationManager.from_environment()
        
        # 初始化存储管理器
        self.storage_manager = RobustHybridStorageManager()
        await self.storage_manager.initialize(self.config)
        
        # 初始化约束引擎
        self.constraint_engine = ProductionConstraintEngine(
            self.storage_manager.primary_storage
        )
        
        # 初始化并发控制
        self.concurrency_controller = OptimisticConcurrencyController(
            self.storage_manager.primary_storage
        )
    
    async def save_element_safely(self, element: UMLElement, 
                                 expected_version: Optional[int] = None) -> bool:
        """安全的元素保存（带版本控制和错误处理）"""
        return await self.concurrency_controller.save_element_with_version_check(
            element, expected_version
        )
    
    async def validate_element_comprehensive(self, element_id: str) -> Dict[str, Any]:
        """全面的元素验证"""
        element = await self.storage_manager.get_element(element_id)
        if not element:
            raise ValueError(f"Element not found: {element_id}")
        
        return await self.constraint_engine.validate_element(element_id, asdict(element))
```

## 🎯 修复优先级建议

1. **🔴 高优先级**：数据一致性、错误处理、并发控制
2. **🟡 中优先级**：OCL引擎完善、配置简化
3. **🟢 低优先级**：性能优化、监控增强

这些修复将显著提高架构的**健壮性、可靠性和可维护性**。 