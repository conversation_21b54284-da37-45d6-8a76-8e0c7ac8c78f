# 生物医学MBSE平台完整技术方案 - 第二部分
## 领域扩展、安全控制与企业级特性

### 5. 插件化领域扩展架构

```python
# =============================================================================
# 领域扩展注册表和插件架构
# =============================================================================

from abc import ABC, abstractmethod
from typing import Dict, Type, List, Any

class DomainExtension(ABC):
    """领域扩展抽象基类"""
    
    @abstractmethod
    def get_domain_name(self) -> str:
        """获取领域名称"""
        pass
    
    @abstractmethod
    def get_stereotypes(self) -> Dict[str, Type]:
        """获取刻板印象定义"""
        pass
    
    @abstractmethod
    def get_constraints(self) -> List[Dict]:
        """获取领域特定约束"""
        pass
    
    @abstractmethod
    def get_validation_rules(self) -> List[Callable]:
        """获取验证规则"""
        pass

class BiomedicalExtension(DomainExtension):
    """生物医学领域扩展"""
    
    def get_domain_name(self) -> str:
        return "biomedical"
    
    def get_stereotypes(self) -> Dict[str, Type]:
        """生物医学刻板印象"""
        return {
            "MedicalDevice": MedicalDeviceStereotype,
            "DrugSubstance": DrugSubstanceStereotype,
            "ClinicalTrial": ClinicalTrialStereotype,
            "Patient": PatientStereotype,
            "HealthcareProvider": HealthcareProviderStereotype,
            "RegulatoryApproval": RegulatoryApprovalStereotype
        }
    
    def get_constraints(self) -> List[Dict]:
        """生物医学约束"""
        return [
            {
                "name": "medical_device_classification",
                "context": "MedicalDevice",
                "expression": "self.deviceClass in ['I', 'II', 'III']",
                "severity": "error",
                "description": "Medical device must have valid FDA classification"
            },
            {
                "name": "drug_dosage_safety",
                "context": "DrugSubstance", 
                "expression": "self.dosage <= self.maxDailyDose",
                "severity": "error",
                "description": "Drug dosage cannot exceed maximum daily dose"
            },
            {
                "name": "clinical_trial_ethics",
                "context": "ClinicalTrial",
                "expression": "self.ethicsApproval = true",
                "severity": "error", 
                "description": "Clinical trial requires ethics committee approval"
            },
            {
                "name": "patient_privacy_compliance",
                "context": "Patient",
                "expression": "self.dataSharing implies self.consentGiven = true",
                "severity": "error",
                "description": "Patient data sharing requires explicit consent"
            }
        ]

@dataclass
class MedicalDeviceStereotype:
    """医疗设备刻板印象"""
    device_class: str  # I, II, III
    regulatory_status: str  # approved, pending, recalled
    safety_classification: str  # IEC 60601 classification
    intended_use: str
    contraindications: List[str]
    sterile: bool = False
    biocompatible: bool = False
    
    def validate(self) -> List[str]:
        """设备特定验证"""
        errors = []
        
        if self.device_class not in ['I', 'II', 'III']:
            errors.append("Invalid device class")
        
        if self.device_class == 'III' and self.regulatory_status != 'approved':
            errors.append("Class III devices must be approved")
        
        if self.sterile and not self.biocompatible:
            errors.append("Sterile devices should be biocompatible")
        
        return errors

@dataclass
class DrugSubstanceStereotype:
    """药物刻板印象"""
    cas_number: str
    molecular_formula: str
    therapeutic_class: str
    route_of_administration: str
    dosage_form: str
    strength: str
    contraindications: List[str]
    drug_interactions: List[str]
    max_daily_dose: float
    
    def validate(self) -> List[str]:
        """药物特定验证"""
        errors = []
        
        if not self.cas_number or len(self.cas_number) < 5:
            errors.append("Invalid CAS number")
        
        if self.max_daily_dose <= 0:
            errors.append("Maximum daily dose must be positive")
        
        return errors

class DomainExtensionRegistry:
    """领域扩展注册表"""
    
    def __init__(self):
        self.extensions: Dict[str, DomainExtension] = {}
        self.stereotype_registry: Dict[str, Type] = {}
        self.constraint_registry: Dict[str, List[Dict]] = {}
    
    def register_extension(self, extension: DomainExtension):
        """注册领域扩展"""
        domain_name = extension.get_domain_name()
        
        # 注册扩展
        self.extensions[domain_name] = extension
        
        # 注册刻板印象
        for stereotype_name, stereotype_class in extension.get_stereotypes().items():
            self.stereotype_registry[stereotype_name] = stereotype_class
        
        # 注册约束
        self.constraint_registry[domain_name] = extension.get_constraints()
        
        logger.info(f"Registered domain extension: {domain_name}")
    
    def get_applicable_stereotypes(self, element: UMLElement) -> List[Type]:
        """获取适用的刻板印象"""
        applicable = []
        
        for stereotype_name in element.applied_stereotypes:
            if stereotype_name in self.stereotype_registry:
                applicable.append(self.stereotype_registry[stereotype_name])
        
        return applicable
    
    def get_domain_constraints(self, domain: str) -> List[Dict]:
        """获取领域约束"""
        return self.constraint_registry.get(domain, [])
    
    def validate_stereotype_application(self, element: UMLElement, 
                                      stereotype_name: str, 
                                      tag_values: Dict[str, Any]) -> List[str]:
        """验证刻板印象应用"""
        if stereotype_name not in self.stereotype_registry:
            return [f"Unknown stereotype: {stereotype_name}"]
        
        stereotype_class = self.stereotype_registry[stereotype_name]
        
        try:
            # 创建刻板印象实例
            stereotype_instance = stereotype_class(**tag_values)
            
            # 执行验证
            if hasattr(stereotype_instance, 'validate'):
                return stereotype_instance.validate()
            
            return []
        except Exception as e:
            return [f"Stereotype validation failed: {str(e)}"]

# =============================================================================
# 标准数据映射引擎
# =============================================================================

class HealthcareStandardAdapter(ABC):
    """医疗标准适配器抽象基类"""
    
    @abstractmethod
    def get_standard_name(self) -> str:
        pass
    
    @abstractmethod
    async def import_data(self, data: Dict) -> UMLElement:
        pass
    
    @abstractmethod
    async def export_element(self, element: UMLElement) -> Dict:
        pass

class FHIRAdapter(HealthcareStandardAdapter):
    """HL7 FHIR适配器"""
    
    def get_standard_name(self) -> str:
        return "HL7_FHIR"
    
    async def import_data(self, fhir_resource: Dict) -> UMLElement:
        """从FHIR资源创建UML元素"""
        resource_type = fhir_resource.get("resourceType")
        
        if resource_type == "Patient":
            return self._import_patient(fhir_resource)
        elif resource_type == "Observation":
            return self._import_observation(fhir_resource)
        elif resource_type == "Procedure":
            return self._import_procedure(fhir_resource)
        else:
            raise ValueError(f"Unsupported FHIR resource type: {resource_type}")
    
    def _import_patient(self, fhir_patient: Dict) -> UMLElement:
        """导入FHIR患者资源"""
        patient_id = fhir_patient.get("id", str(uuid.uuid4()))
        name = self._extract_patient_name(fhir_patient)
        
        element = UMLElement(
            id=patient_id,
            metaclass="Class",
            name=name,
            qualified_name=f"FHIR::Patient::{name}",
            namespace="FHIR",
            applied_stereotypes=["Patient"],
            properties={
                "birth_date": fhir_patient.get("birthDate"),
                "gender": fhir_patient.get("gender"),
                "active": fhir_patient.get("active", True),
                "identifiers": fhir_patient.get("identifier", []),
                "telecom": fhir_patient.get("telecom", []),
                "address": fhir_patient.get("address", [])
            }
        )
        
        return element
    
    def _extract_patient_name(self, fhir_patient: Dict) -> str:
        """提取患者姓名"""
        names = fhir_patient.get("name", [])
        if names:
            name = names[0]
            given = " ".join(name.get("given", []))
            family = name.get("family", "")
            return f"{given} {family}".strip()
        return "Unknown Patient"

class DICOMAdapter(HealthcareStandardAdapter):
    """DICOM标准适配器"""
    
    def get_standard_name(self) -> str:
        return "DICOM"
    
    async def import_data(self, dicom_data: Dict) -> UMLElement:
        """从DICOM数据创建UML元素"""
        study_uid = dicom_data.get("StudyInstanceUID", str(uuid.uuid4()))
        
        element = UMLElement(
            id=study_uid,
            metaclass="Class",
            name=dicom_data.get("StudyDescription", "DICOM Study"),
            qualified_name=f"DICOM::Study::{study_uid}",
            namespace="DICOM",
            applied_stereotypes=["ImagingStudy"],
            properties={
                "study_uid": study_uid,
                "study_date": dicom_data.get("StudyDate"),
                "study_time": dicom_data.get("StudyTime"),
                "modality": dicom_data.get("Modality"),
                "patient_id": dicom_data.get("PatientID"),
                "patient_name": dicom_data.get("PatientName"),
                "accession_number": dicom_data.get("AccessionNumber"),
                "referring_physician": dicom_data.get("ReferringPhysicianName")
            }
        )
        
        return element

class UniversalHealthcareMapper:
    """通用医疗数据映射器"""
    
    def __init__(self):
        self.adapters: Dict[str, HealthcareStandardAdapter] = {}
        self.register_default_adapters()
    
    def register_default_adapters(self):
        """注册默认适配器"""
        self.register_adapter(FHIRAdapter())
        self.register_adapter(DICOMAdapter())
    
    def register_adapter(self, adapter: HealthcareStandardAdapter):
        """注册标准适配器"""
        self.adapters[adapter.get_standard_name()] = adapter
    
    async def import_from_standard(self, standard: str, data: Dict) -> UMLElement:
        """从任何支持的标准导入"""
        adapter = self.adapters.get(standard)
        if not adapter:
            raise ValueError(f"Unsupported standard: {standard}")
        
        return await adapter.import_data(data)
    
    async def export_to_standard(self, element: UMLElement, standard: str) -> Dict:
        """导出到特定标准"""
        adapter = self.adapters.get(standard)
        if not adapter:
            raise ValueError(f"Unsupported standard: {standard}")
        
        return await adapter.export_element(element)
```

### 6. 企业级安全和权限控制

```python
# =============================================================================
# 基于角色的访问控制（RBAC）
# =============================================================================

from enum import Enum
import hashlib
import secrets
from datetime import datetime, timedelta

class Permission(Enum):
    """权限枚举"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    VALIDATE = "validate"
    ADMIN = "admin"
    EXPORT = "export"
    IMPORT = "import"

@dataclass
class User:
    """用户实体"""
    id: str
    username: str
    email: str
    roles: List[str]
    permissions: Set[Permission]
    is_active: bool = True
    last_login: Optional[datetime] = None
    password_hash: Optional[str] = None
    
    def has_permission(self, permission: Permission) -> bool:
        """检查用户是否有特定权限"""
        return permission in self.permissions or Permission.ADMIN in self.permissions

@dataclass
class Role:
    """角色定义"""
    name: str
    permissions: Set[Permission]
    description: str
    is_system_role: bool = False

class AccessControlManager:
    """访问控制管理器"""
    
    def __init__(self):
        self.users: Dict[str, User] = {}
        self.roles: Dict[str, Role] = {}
        self.access_policies: List[Dict] = []
        self.audit_log: List[Dict] = []
        self._initialize_system_roles()
    
    def _initialize_system_roles(self):
        """初始化系统角色"""
        system_roles = [
            Role(
                name="Administrator",
                permissions={Permission.ADMIN},
                description="System administrator with full access",
                is_system_role=True
            ),
            Role(
                name="ModelDesigner",
                permissions={Permission.READ, Permission.WRITE, Permission.VALIDATE},
                description="Model designer with read/write access",
                is_system_role=True
            ),
            Role(
                name="Viewer",
                permissions={Permission.READ},
                description="Read-only access to models",
                is_system_role=True
            ),
            Role(
                name="DataAnalyst",
                permissions={Permission.READ, Permission.EXPORT},
                description="Data analyst with read and export access",
                is_system_role=True
            ),
            Role(
                name="MedicalExpert",
                permissions={Permission.READ, Permission.WRITE, Permission.VALIDATE},
                description="Medical domain expert",
                is_system_role=True
            )
        ]
        
        for role in system_roles:
            self.roles[role.name] = role
    
    def create_user(self, username: str, email: str, password: str, 
                   roles: List[str]) -> User:
        """创建用户"""
        user_id = str(uuid.uuid4())
        password_hash = self._hash_password(password)
        
        # 收集角色权限
        user_permissions = set()
        for role_name in roles:
            if role_name in self.roles:
                user_permissions.update(self.roles[role_name].permissions)
        
        user = User(
            id=user_id,
            username=username,
            email=email,
            roles=roles,
            permissions=user_permissions,
            password_hash=password_hash
        )
        
        self.users[user_id] = user
        self._audit_log(user, "create_user", f"User {username} created")
        
        return user
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """用户认证"""
        for user in self.users.values():
            if (user.username == username and 
                user.is_active and
                self._verify_password(password, user.password_hash)):
                
                user.last_login = datetime.now()
                self._audit_log(user, "login", "User logged in")
                return user
        
        return None
    
    def check_element_access(self, user: User, element_id: str, 
                           permission: Permission) -> bool:
        """检查元素访问权限"""
        # 基础权限检查
        if not user.has_permission(permission):
            return False
        
        # 应用访问策略
        for policy in self.access_policies:
            if self._policy_applies(policy, user, element_id):
                return policy.get("allowed", False)
        
        # 默认允许（如果有基础权限）
        return True
    
    def add_access_policy(self, resource_pattern: str, user_roles: List[str], 
                         permissions: List[Permission], allowed: bool = True):
        """添加访问策略"""
        policy = {
            "resource_pattern": resource_pattern,
            "user_roles": user_roles,
            "permissions": permissions,
            "allowed": allowed,
            "created_at": datetime.now().isoformat()
        }
        
        self.access_policies.append(policy)
    
    def _policy_applies(self, policy: Dict, user: User, resource: str) -> bool:
        """检查策略是否适用"""
        import fnmatch
        
        # 检查资源模式匹配
        if not fnmatch.fnmatch(resource, policy["resource_pattern"]):
            return False
        
        # 检查用户角色
        if not any(role in user.roles for role in policy["user_roles"]):
            return False
        
        return True
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        pwd_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{pwd_hash.hex()}"
    
    def _verify_password(self, password: str, stored_hash: str) -> bool:
        """验证密码"""
        try:
            salt, pwd_hash = stored_hash.split(':')
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return pwd_hash == computed_hash.hex()
        except:
            return False
    
    def _audit_log(self, user: User, action: str, details: str):
        """审计日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_id": user.id,
            "username": user.username,
            "action": action,
            "details": details,
            "ip_address": "TODO",  # 需要从请求上下文获取
            "user_agent": "TODO"
        }
        
        self.audit_log.append(log_entry)
        logger.info(f"AUDIT: {log_entry}")

# =============================================================================
# 安全的API包装器
# =============================================================================

class SecureMetamodelAPI:
    """安全的元模型API包装器"""
    
    def __init__(self, core_api: UnifiedMetamodelAPI):
        self.core_api = core_api
        self.access_control = AccessControlManager()
        self.current_user: Optional[User] = None
    
    def set_current_user(self, user: User):
        """设置当前用户上下文"""
        self.current_user = user
    
    async def save_element_secure(self, element: UMLElement) -> bool:
        """安全的元素保存"""
        if not self.current_user:
            raise PermissionError("No authenticated user")
        
        # 检查写权限
        if not self.access_control.check_element_access(
            self.current_user, element.id, Permission.WRITE
        ):
            raise PermissionError(f"User {self.current_user.username} has no write permission for {element.id}")
        
        # 记录审计
        self.access_control._audit_log(
            self.current_user, "save_element", 
            f"Saved element {element.id} ({element.name})"
        )
        
        return await self.core_api.storage_manager.save_element(element)
    
    async def get_element_secure(self, element_id: str) -> Optional[UMLElement]:
        """安全的元素获取"""
        if not self.current_user:
            raise PermissionError("No authenticated user")
        
        # 检查读权限
        if not self.access_control.check_element_access(
            self.current_user, element_id, Permission.READ
        ):
            raise PermissionError(f"User {self.current_user.username} has no read permission for {element_id}")
        
        return await self.core_api.storage_manager.get_element(element_id)
    
    async def delete_element_secure(self, element_id: str) -> bool:
        """安全的元素删除"""
        if not self.current_user:
            raise PermissionError("No authenticated user")
        
        # 检查删除权限
        if not self.access_control.check_element_access(
            self.current_user, element_id, Permission.DELETE
        ):
            raise PermissionError(f"User {self.current_user.username} has no delete permission for {element_id}")
        
        # 记录审计
        self.access_control._audit_log(
            self.current_user, "delete_element", 
            f"Deleted element {element_id}"
        )
        
        return await self.core_api.storage_manager.delete_element(element_id)
```

### 7. 智能配置管理

```python
# =============================================================================
# 智能配置管理系统
# =============================================================================

import os
import yaml
from pathlib import Path
from typing import Optional, Dict, Any

@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600

@dataclass
class CacheConfig:
    """缓存配置"""
    enabled: bool = True
    type: str = "memory"  # memory, redis
    size: int = 10000
    ttl_seconds: int = 3600
    redis_url: Optional[str] = None

@dataclass
class SecurityConfig:
    """安全配置"""
    jwt_secret: str
    jwt_expiry_hours: int = 24
    password_min_length: int = 8
    session_timeout_minutes: int = 30
    max_login_attempts: int = 5

@dataclass
class ValidationConfig:
    """验证配置"""
    mode: str = "strict"  # strict, lenient, development
    parallel_validation: bool = True
    timeout_seconds: int = 30
    cache_results: bool = True

@dataclass
class PlatformConfig:
    """统一平台配置"""
    deployment_mode: str
    database: DatabaseConfig
    cache: CacheConfig
    security: SecurityConfig
    validation: ValidationConfig
    extensions: List[str] = field(default_factory=list)
    logging_level: str = "INFO"
    
    @classmethod
    def from_environment(cls) -> 'PlatformConfig':
        """从环境变量创建配置"""
        deployment_mode = os.getenv('METAMODEL_MODE', 'development')
        
        return cls(
            deployment_mode=deployment_mode,
            database=DatabaseConfig(
                url=os.getenv('DATABASE_URL', cls._get_default_db_url(deployment_mode)),
                pool_size=int(os.getenv('DB_POOL_SIZE', '10')),
                max_overflow=int(os.getenv('DB_MAX_OVERFLOW', '20'))
            ),
            cache=CacheConfig(
                enabled=os.getenv('CACHE_ENABLED', 'true').lower() == 'true',
                type=os.getenv('CACHE_TYPE', 'memory'),
                size=int(os.getenv('CACHE_SIZE', '10000')),
                redis_url=os.getenv('REDIS_URL')
            ),
            security=SecurityConfig(
                jwt_secret=os.getenv('JWT_SECRET', secrets.token_hex(32)),
                jwt_expiry_hours=int(os.getenv('JWT_EXPIRY_HOURS', '24')),
                session_timeout_minutes=int(os.getenv('SESSION_TIMEOUT', '30'))
            ),
            validation=ValidationConfig(
                mode=os.getenv('VALIDATION_MODE', cls._get_default_validation_mode(deployment_mode)),
                parallel_validation=os.getenv('PARALLEL_VALIDATION', 'true').lower() == 'true',
                timeout_seconds=int(os.getenv('VALIDATION_TIMEOUT', '30'))
            ),
            extensions=os.getenv('EXTENSIONS', 'biomedical').split(','),
            logging_level=os.getenv('LOG_LEVEL', 'INFO')
        )
    
    @classmethod
    def from_file(cls, config_path: str) -> 'PlatformConfig':
        """从配置文件创建配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)
    
    @staticmethod
    def _get_default_db_url(deployment_mode: str) -> str:
        """获取默认数据库URL"""
        if deployment_mode == "development":
            return "sqlite:///./dev_metamodel.db"
        elif deployment_mode == "testing":
            return "sqlite:///:memory:"
        else:
            raise ValueError("Production mode requires explicit DATABASE_URL")
    
    @staticmethod
    def _get_default_validation_mode(deployment_mode: str) -> str:
        """获取默认验证模式"""
        return "lenient" if deployment_mode == "development" else "strict"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    def save_to_file(self, config_path: str):
        """保存到配置文件"""
        config_data = self.to_dict()
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)

class ConfigurationManager:
    """配置管理器"""
    
    def __init__(self, config: PlatformConfig):
        self.config = config
        self._validate_config()
    
    def _validate_config(self):
        """验证配置有效性"""
        errors = []
        
        # 验证数据库配置
        if not self.config.database.url:
            errors.append("Database URL is required")
        
        # 验证安全配置
        if len(self.config.security.jwt_secret) < 32:
            errors.append("JWT secret must be at least 32 characters")
        
        # 验证部署模式
        if self.config.deployment_mode not in ['development', 'testing', 'production']:
            errors.append("Invalid deployment mode")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def get_storage_config(self) -> StorageConfig:
        """获取存储配置"""
        if self.config.deployment_mode == "development":
            backend = StorageBackend.FILE_SYSTEM
        elif self.config.deployment_mode == "testing":
            backend = StorageBackend.FILE_SYSTEM
        else:  # production
            backend = StorageBackend.HYBRID
        
        return StorageConfig(
            deployment_mode=self.config.deployment_mode,
            database_url=self.config.database.url,
            repository_path=f"./{self.config.deployment_mode}_repository"
        )
    
    def should_enable_feature(self, feature: str) -> bool:
        """检查是否应该启用特定功能"""
        feature_flags = {
            "advanced_validation": self.config.deployment_mode != "development",
            "performance_monitoring": self.config.deployment_mode == "production",
            "distributed_caching": self.config.cache.type == "redis",
            "audit_logging": self.config.deployment_mode in ["testing", "production"]
        }
        
        return feature_flags.get(feature, False)
    
    def get_extension_configs(self) -> Dict[str, Dict]:
        """获取扩展配置"""
        extension_configs = {}
        
        for extension in self.config.extensions:
            if extension == "biomedical":
                extension_configs[extension] = {
                    "enable_fhir_integration": True,
                    "enable_dicom_support": True,
                    "regulatory_validation": self.config.deployment_mode == "production",
                    "privacy_controls": True
                }
        
        return extension_configs

# =============================================================================
# 环境特定配置模板
# =============================================================================

class ConfigTemplates:
    """配置模板生成器"""
    
    @staticmethod
    def generate_development_config() -> PlatformConfig:
        """生成开发环境配置"""
        return PlatformConfig(
            deployment_mode="development",
            database=DatabaseConfig(
                url="sqlite:///./dev_metamodel.db",
                pool_size=5
            ),
            cache=CacheConfig(
                enabled=True,
                type="memory",
                size=5000
            ),
            security=SecurityConfig(
                jwt_secret=secrets.token_hex(32),
                jwt_expiry_hours=72,  # 更长的开发期间令牌
                session_timeout_minutes=60
            ),
            validation=ValidationConfig(
                mode="lenient",
                parallel_validation=False,  # 简化调试
                timeout_seconds=60
            ),
            extensions=["biomedical"],
            logging_level="DEBUG"
        )
    
    @staticmethod
    def generate_production_config(database_url: str, redis_url: str = None) -> PlatformConfig:
        """生成生产环境配置"""
        return PlatformConfig(
            deployment_mode="production",
            database=DatabaseConfig(
                url=database_url,
                pool_size=20,
                max_overflow=30,
                pool_timeout=10,
                pool_recycle=1800
            ),
            cache=CacheConfig(
                enabled=True,
                type="redis" if redis_url else "memory",
                size=50000,
                ttl_seconds=1800,
                redis_url=redis_url
            ),
            security=SecurityConfig(
                jwt_secret=os.getenv('JWT_SECRET') or secrets.token_hex(32),
                jwt_expiry_hours=8,  # 更短的生产环境令牌
                password_min_length=12,
                session_timeout_minutes=15,
                max_login_attempts=3
            ),
            validation=ValidationConfig(
                mode="strict",
                parallel_validation=True,
                timeout_seconds=30,
                cache_results=True
            ),
            extensions=["biomedical"],
            logging_level="INFO"
        )
    
    @staticmethod
    def save_template_configs():
        """保存模板配置文件"""
        configs_dir = Path("./configs")
        configs_dir.mkdir(exist_ok=True)
        
        # 开发环境配置
        dev_config = ConfigTemplates.generate_development_config()
        dev_config.save_to_file(configs_dir / "development.yaml")
        
        # 生产环境配置模板
        prod_config = ConfigTemplates.generate_production_config(
            "postgresql://user:pass@localhost:5432/uml_metamodel",
            "redis://localhost:6379/0"
        )
        prod_config.save_to_file(configs_dir / "production.yaml.template")
        
        print("Configuration templates saved to ./configs/")
```

这完成了技术方案的第二部分，涵盖了领域扩展、安全控制和配置管理。接下来我会创建第三部分，包含实施路线图和部署方案。 