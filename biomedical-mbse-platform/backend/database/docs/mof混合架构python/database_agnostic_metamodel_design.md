# 数据库无关的UML元模型架构设计
## 轻量级、无数据库依赖的MBSE方案

### 🎯 设计理念

#### 核心原则
- **零数据库依赖**: 纯内存+文件系统存储
- **轻量级部署**: 单体应用，易于部署
- **跨平台兼容**: 支持Windows/Linux/macOS
- **文件格式标准**: 基于JSON/YAML/XML等开放格式
- **版本控制友好**: 支持Git等VCS系统

#### 技术栈选择
- **存储层**: 文件系统 + 内存缓存
- **数据格式**: JSON Schema + YAML配置
- **约束引擎**: 纯Python/Java实现
- **搜索**: 内存索引 + 倒排索引
- **API**: FastAPI/Spring Boot + GraphQL

---

## 📁 文件存储架构

### 1. 目录结构设计

```
metamodel_repository/
├── schemas/                    # 元模型模式定义
│   ├── mof/                   # MOF元元模型
│   │   ├── metaclass.schema.json
│   │   ├── metaproperty.schema.json
│   │   └── metaassociation.schema.json
│   ├── uml/                   # UML元模型
│   │   ├── core/
│   │   │   ├── element.schema.json
│   │   │   ├── named_element.schema.json
│   │   │   └── classifier.schema.json
│   │   ├── behavior/
│   │   └── profiles/
│   └── extensions/            # 领域扩展
│       ├── sysml/
│       └── autosar/
├── constraints/               # OCL约束定义
│   ├── core_constraints.yaml
│   ├── behavioral_constraints.yaml
│   └── profile_constraints.yaml
├── models/                    # 实际模型数据
│   ├── project_alpha/
│   │   ├── model.json         # 模型元数据
│   │   ├── elements/          # 元素数据
│   │   │   ├── classes/
│   │   │   │   ├── class_001.json
│   │   │   │   └── class_002.json
│   │   │   ├── relationships/
│   │   │   └── behaviors/
│   │   ├── versions/          # 版本历史
│   │   └── cache/             # 计算缓存
│   └── project_beta/
├── registry/                  # 元类注册表
│   ├── metaclass_registry.json
│   ├── inheritance_tree.json
│   └── constraint_mapping.json
└── config/                    # 系统配置
    ├── engine_config.yaml
    └── validation_rules.yaml
```

### 2. 数据存储格式

#### **元类定义（JSON Schema格式）**
```json
// schemas/uml/core/class.schema.json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "$id": "uml://core/Class",
  "title": "UML Class",
  "type": "object",
  "allOf": [
    { "$ref": "classifier.schema.json" }
  ],
  "properties": {
    "id": {
      "type": "string",
      "format": "uuid"
    },
    "metaclass": {
      "const": "Class"
    },
    "name": {
      "type": "string"
    },
    "isAbstract": {
      "type": "boolean",
      "default": false
    },
    "isActive": {
      "type": "boolean", 
      "default": false
    },
    "ownedAttributes": {
      "type": "array",
      "items": {
        "$ref": "#/$defs/PropertyReference"
      }
    },
    "ownedOperations": {
      "type": "array",
      "items": {
        "$ref": "#/$defs/OperationReference"
      }
    },
    "superClasses": {
      "type": "array",
      "items": {
        "$ref": "#/$defs/ClassReference"
      }
    }
  },
  "required": ["id", "metaclass", "name"],
  "$defs": {
    "PropertyReference": {
      "type": "object",
      "properties": {
        "ref": {"type": "string", "format": "uuid"},
        "href": {"type": "string", "format": "uri"}
      }
    },
    "ClassReference": {
      "type": "object", 
      "properties": {
        "ref": {"type": "string", "format": "uuid"},
        "qualifiedName": {"type": "string"}
      }
    }
  }
}
```

#### **约束定义（YAML格式）**
```yaml
# constraints/core_constraints.yaml
constraints:
  - name: "class_no_self_inheritance"
    context: "Class"
    type: "invariant"
    severity: "error"
    expression: |
      not self.allParents()->includes(self)
    description: "A class cannot inherit from itself"
    
  - name: "property_type_exists" 
    context: "Property"
    type: "invariant"
    severity: "error"
    expression: |
      self.type->notEmpty()
    description: "Property must have a type"
    
  - name: "abstract_class_no_instances"
    context: "Class"
    type: "invariant"
    severity: "warning"
    expression: |
      self.isAbstract implies self.instances()->isEmpty()
    description: "Abstract classes should not have direct instances"
```

#### **模型实例（JSON格式）**
```json
// models/project_alpha/elements/classes/class_001.json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "metaclass": "Class",
  "name": "Customer",
  "qualifiedName": "BusinessModel::Customer", 
  "visibility": "public",
  "isAbstract": false,
  "isActive": false,
  "namespace": {
    "ref": "550e8400-e29b-41d4-a716-446655440000",
    "qualifiedName": "BusinessModel"
  },
  "ownedAttributes": [
    {
      "ref": "550e8400-e29b-41d4-a716-446655440002",
      "href": "../properties/property_001.json"
    }
  ],
  "appliedStereotypes": [
    {
      "stereotype": "Entity",
      "tagValues": {
        "tableName": "customers",
        "primaryKey": "customer_id"
      }
    }
  ],
  "metadata": {
    "created": "2024-01-15T10:30:00Z",
    "modified": "2024-01-20T14:25:00Z", 
    "version": "1.2.0",
    "checksum": "sha256:abc123...",
    "validationStatus": "passed"
  }
}
```

---

## 🧠 内存存储引擎

### 1. 核心数据结构

```python
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json
import yaml
from pathlib import Path

@dataclass
class MetaclassDefinition:
    """元类定义"""
    name: str
    package_uri: str
    parent_metaclasses: List[str] = field(default_factory=list)
    owned_properties: Dict[str, Any] = field(default_factory=dict)
    applicable_constraints: List[str] = field(default_factory=list)
    json_schema: Dict[str, Any] = field(default_factory=dict)

@dataclass 
class ElementInstance:
    """元素实例"""
    id: str
    metaclass: str
    data: Dict[str, Any]
    relationships: Dict[str, List[str]] = field(default_factory=dict)
    validation_results: List[Dict] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ConstraintDefinition:
    """约束定义"""
    name: str
    context: str
    constraint_type: str  # invariant, pre, post, derive
    expression: str
    severity: str  # error, warning, info
    description: str

class MetamodelRepository:
    """元模型仓库 - 内存存储引擎"""
    
    def __init__(self, repository_root: Path):
        self.repository_root = repository_root
        self.metaclasses: Dict[str, MetaclassDefinition] = {}
        self.constraints: Dict[str, ConstraintDefinition] = {}
        self.elements: Dict[str, ElementInstance] = {}
        self.inheritance_tree: Dict[str, Set[str]] = {}
        self.reverse_index: Dict[str, Set[str]] = {}  # 反向索引
        self.name_index: Dict[str, str] = {}  # 名称到ID索引
        
    def load_repository(self):
        """加载整个仓库到内存"""
        self._load_metaclasses()
        self._load_constraints() 
        self._load_models()
        self._build_indices()
        
    def _load_metaclasses(self):
        """加载元类定义"""
        schemas_dir = self.repository_root / "schemas"
        for schema_file in schemas_dir.rglob("*.schema.json"):
            with open(schema_file) as f:
                schema = json.load(f)
                metaclass = self._parse_metaclass_schema(schema)
                self.metaclasses[metaclass.name] = metaclass
                
    def _load_constraints(self):
        """加载约束定义"""
        constraints_dir = self.repository_root / "constraints"
        for constraint_file in constraints_dir.glob("*.yaml"):
            with open(constraint_file) as f:
                data = yaml.safe_load(f)
                for constraint_data in data.get('constraints', []):
                    constraint = ConstraintDefinition(**constraint_data)
                    self.constraints[constraint.name] = constraint
                    
    def _load_models(self):
        """加载模型数据"""
        models_dir = self.repository_root / "models"
        for element_file in models_dir.rglob("*.json"):
            if "cache" not in str(element_file):  # 跳过缓存文件
                with open(element_file) as f:
                    data = json.load(f)
                    element = ElementInstance(
                        id=data['id'],
                        metaclass=data['metaclass'],
                        data=data,
                        metadata=data.get('metadata', {})
                    )
                    self.elements[element.id] = element
                    
    def _build_indices(self):
        """构建内存索引"""
        # 构建继承树
        for metaclass_name, metaclass in self.metaclasses.items():
            parents = set(metaclass.parent_metaclasses)
            self.inheritance_tree[metaclass_name] = parents
            
        # 构建名称索引
        for element_id, element in self.elements.items():
            if 'qualifiedName' in element.data:
                self.name_index[element.data['qualifiedName']] = element_id
```

### 2. 查询引擎

```python
class QueryEngine:
    """内存查询引擎"""
    
    def __init__(self, repository: MetamodelRepository):
        self.repository = repository
        
    def find_elements_by_metaclass(self, metaclass: str, 
                                 include_subtypes: bool = True) -> List[ElementInstance]:
        """按元类查找元素"""
        target_metaclasses = {metaclass}
        
        if include_subtypes:
            target_metaclasses.update(
                self._get_all_subclasses(metaclass)
            )
            
        return [
            element for element in self.repository.elements.values()
            if element.metaclass in target_metaclasses
        ]
        
    def find_elements_by_name_pattern(self, pattern: str) -> List[ElementInstance]:
        """按名称模式查找元素"""
        import re
        regex = re.compile(pattern, re.IGNORECASE)
        return [
            element for element in self.repository.elements.values()
            if 'name' in element.data and regex.search(element.data['name'])
        ]
        
    def find_related_elements(self, element_id: str, 
                            relationship_type: str = None) -> List[ElementInstance]:
        """查找相关元素"""
        element = self.repository.elements.get(element_id)
        if not element:
            return []
            
        related_ids = []
        if relationship_type:
            related_ids = element.relationships.get(relationship_type, [])
        else:
            # 获取所有关系的元素
            for rel_list in element.relationships.values():
                related_ids.extend(rel_list)
                
        return [
            self.repository.elements[rel_id] 
            for rel_id in related_ids 
            if rel_id in self.repository.elements
        ]
        
    def _get_all_subclasses(self, metaclass: str) -> Set[str]:
        """获取所有子类"""
        subclasses = set()
        for name, parents in self.repository.inheritance_tree.items():
            if metaclass in parents:
                subclasses.add(name)
                subclasses.update(self._get_all_subclasses(name))
        return subclasses
```

### 3. 约束验证引擎

```python
class ConstraintValidator:
    """约束验证引擎 - 纯Python实现"""
    
    def __init__(self, repository: MetamodelRepository):
        self.repository = repository
        self.ocl_interpreter = OCLInterpreter()
        
    def validate_element(self, element_id: str) -> List[Dict]:
        """验证单个元素"""
        element = self.repository.elements.get(element_id)
        if not element:
            return []
            
        applicable_constraints = self._get_applicable_constraints(element.metaclass)
        results = []
        
        for constraint in applicable_constraints:
            try:
                result = self._evaluate_constraint(constraint, element)
                results.append({
                    'constraint': constraint.name,
                    'result': result,
                    'severity': constraint.severity,
                    'message': constraint.description if not result else None
                })
            except Exception as e:
                results.append({
                    'constraint': constraint.name,
                    'result': False,
                    'severity': 'error',
                    'message': f"Validation error: {str(e)}"
                })
                
        return results
        
    def validate_model(self, model_id: str) -> Dict:
        """验证整个模型"""
        model_elements = self._get_model_elements(model_id)
        all_results = {}
        summary = {
            'total_elements': len(model_elements),
            'passed': 0,
            'failed': 0,
            'warnings': 0
        }
        
        for element in model_elements:
            results = self.validate_element(element.id)
            all_results[element.id] = results
            
            # 统计结果
            has_errors = any(r['result'] == False and r['severity'] == 'error' for r in results)
            has_warnings = any(r['result'] == False and r['severity'] == 'warning' for r in results)
            
            if has_errors:
                summary['failed'] += 1
            elif has_warnings:
                summary['warnings'] += 1
            else:
                summary['passed'] += 1
                
        return {
            'summary': summary,
            'detailed_results': all_results
        }
        
    def _get_applicable_constraints(self, metaclass: str) -> List[ConstraintDefinition]:
        """获取适用的约束"""
        # 获取元类及其所有父类
        metaclasses = {metaclass}
        metaclasses.update(self._get_all_parents(metaclass))
        
        return [
            constraint for constraint in self.repository.constraints.values()
            if constraint.context in metaclasses
        ]
        
    def _evaluate_constraint(self, constraint: ConstraintDefinition, 
                           element: ElementInstance) -> bool:
        """评估约束"""
        # 简化的OCL表达式求值
        context = {
            'self': element.data,
            'repository': self.repository
        }
        return self.ocl_interpreter.evaluate(constraint.expression, context)

class OCLInterpreter:
    """简化的OCL解释器"""
    
    def evaluate(self, expression: str, context: Dict) -> bool:
        """评估OCL表达式"""
        # 这里实现简化的OCL解释器
        # 实际实现需要完整的OCL解析器
        
        # 示例：处理简单的表达式
        if "self.isAbstract implies" in expression:
            return self._evaluate_implication(expression, context)
        elif "not self.allParents()->includes(self)" in expression:
            return self._evaluate_no_self_inheritance(context)
        elif "self.type->notEmpty()" in expression:
            return self._evaluate_type_not_empty(context)
            
        # 默认返回True（需要完整实现）
        return True
        
    def _evaluate_implication(self, expression: str, context: Dict) -> bool:
        """评估蕴含表达式"""
        element = context['self']
        if element.get('isAbstract', False):
            # 如果是抽象类，检查是否有实例
            return len(self._get_instances(element, context['repository'])) == 0
        return True
        
    def _evaluate_no_self_inheritance(self, context: Dict) -> bool:
        """检查无自身继承"""
        element = context['self']
        element_id = element['id']
        
        # 递归检查所有父类
        all_parents = self._get_all_parent_ids(element_id, context['repository'])
        return element_id not in all_parents
        
    def _evaluate_type_not_empty(self, context: Dict) -> bool:
        """检查类型非空"""
        element = context['self']
        return 'type' in element and element['type'] is not None
```

---

## 🔧 API服务层

### 1. FastAPI服务实现

```python
from fastapi import FastAPI, HTTPException, Depends
from fastapi.responses import JSONResponse
from typing import List, Optional
import asyncio

app = FastAPI(title="UML Metamodel API", version="1.0.0")

# 全局仓库实例
repository = MetamodelRepository(Path("./metamodel_repository"))
repository.load_repository()

query_engine = QueryEngine(repository)
constraint_validator = ConstraintValidator(repository)

@app.get("/api/v1/metamodel/metaclasses")
async def get_metaclasses() -> Dict[str, Any]:
    """获取所有元类"""
    return {
        "metaclasses": [
            {
                "name": name,
                "package_uri": mc.package_uri,
                "parent_metaclasses": mc.parent_metaclasses,
                "properties": list(mc.owned_properties.keys())
            }
            for name, mc in repository.metaclasses.items()
        ]
    }

@app.get("/api/v1/models/{model_id}/elements")
async def get_model_elements(
    model_id: str,
    metaclass: Optional[str] = None,
    name_pattern: Optional[str] = None,
    include_subtypes: bool = True
) -> Dict[str, Any]:
    """获取模型元素"""
    
    elements = []
    
    if metaclass:
        elements = query_engine.find_elements_by_metaclass(
            metaclass, include_subtypes
        )
    elif name_pattern:
        elements = query_engine.find_elements_by_name_pattern(name_pattern)
    else:
        # 获取所有元素
        elements = list(repository.elements.values())
    
    # 过滤属于指定模型的元素
    model_elements = [
        e for e in elements 
        if e.data.get('model_container') == model_id
    ]
    
    return {
        "total": len(model_elements),
        "elements": [
            {
                "id": e.id,
                "metaclass": e.metaclass,
                "name": e.data.get('name'),
                "qualifiedName": e.data.get('qualifiedName'),
                "validationStatus": e.metadata.get('validationStatus', 'unknown')
            }
            for e in model_elements
        ]
    }

@app.get("/api/v1/elements/{element_id}")
async def get_element(element_id: str) -> Dict[str, Any]:
    """获取特定元素"""
    element = repository.elements.get(element_id)
    if not element:
        raise HTTPException(status_code=404, detail="Element not found")
    
    return {
        "id": element.id,
        "metaclass": element.metaclass,
        "data": element.data,
        "relationships": element.relationships,
        "metadata": element.metadata
    }

@app.post("/api/v1/elements/{element_id}/validate")
async def validate_element(element_id: str) -> Dict[str, Any]:
    """验证元素"""
    results = constraint_validator.validate_element(element_id)
    
    return {
        "element_id": element_id,
        "validation_results": results,
        "summary": {
            "total_constraints": len(results),
            "passed": len([r for r in results if r['result']]),
            "failed": len([r for r in results if not r['result']])
        }
    }

@app.post("/api/v1/models/{model_id}/validate")
async def validate_model(model_id: str) -> Dict[str, Any]:
    """验证整个模型"""
    results = constraint_validator.validate_model(model_id)
    return results

@app.get("/api/v1/elements/{element_id}/relationships")
async def get_element_relationships(
    element_id: str,
    relationship_type: Optional[str] = None
) -> Dict[str, Any]:
    """获取元素关系"""
    related_elements = query_engine.find_related_elements(
        element_id, relationship_type
    )
    
    return {
        "element_id": element_id,
        "related_elements": [
            {
                "id": e.id,
                "name": e.data.get('name'),
                "metaclass": e.metaclass,
                "qualifiedName": e.data.get('qualifiedName')
            }
            for e in related_elements
        ]
    }
```

### 2. 文件持久化

```python
class FileRepository:
    """文件系统持久化"""
    
    def __init__(self, repository_root: Path):
        self.repository_root = repository_root
        
    def save_element(self, element: ElementInstance):
        """保存元素到文件"""
        element_dir = self._get_element_directory(element)
        element_dir.mkdir(parents=True, exist_ok=True)
        
        file_path = element_dir / f"{element.id}.json"
        with open(file_path, 'w') as f:
            json.dump(element.data, f, indent=2, ensure_ascii=False)
            
        # 更新索引
        self._update_indices(element)
        
    def delete_element(self, element_id: str):
        """删除元素"""
        element = self.repository.elements.get(element_id)
        if element:
            file_path = self._get_element_file_path(element)
            if file_path.exists():
                file_path.unlink()
            del self.repository.elements[element_id]
            self._rebuild_indices()
            
    def create_version_snapshot(self, model_id: str, version: str):
        """创建版本快照"""
        model_dir = self.repository_root / "models" / model_id
        version_dir = model_dir / "versions" / version
        version_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制当前状态
        import shutil
        shutil.copytree(
            model_dir / "elements",
            version_dir / "elements",
            ignore=shutil.ignore_patterns("*.cache")
        )
        
        # 保存版本元数据
        version_info = {
            "version": version,
            "created": datetime.now().isoformat(),
            "element_count": len(list((model_dir / "elements").rglob("*.json")))
        }
        
        with open(version_dir / "version_info.json", 'w') as f:
            json.dump(version_info, f, indent=2)
```

---

## 🚀 部署优势

### 1. 零依赖部署

```dockerfile
# Dockerfile - 轻量级部署
FROM python:3.11-slim

WORKDIR /app

# 只需要Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制应用代码和数据
COPY src/ ./src/
COPY metamodel_repository/ ./metamodel_repository/

EXPOSE 8000

CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# requirements.txt - 最小依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0  
pydantic==2.5.0
pyyaml==6.0.1
jsonschema==4.20.0
```

### 2. 性能特点

| 特性 | 数据库方案 | 文件系统方案 |
|------|-----------|-------------|
| **启动时间** | 5-10秒 | <2秒 |
| **内存使用** | 100-500MB | 50-200MB |
| **查询延迟** | 10-100ms | <5ms |
| **并发写入** | 高 | 中等 |
| **数据一致性** | ACID | 文件锁 |
| **备份方式** | 数据库备份 | 文件系统复制 |

### 3. 适用场景对比

#### **文件系统方案适合**：
✅ 中小型团队（<50人）  
✅ 模型规模适中（<10万元素）  
✅ 部署简单性优先  
✅ 版本控制集成重要  
✅ 离线使用需求  

#### **数据库方案适合**：
✅ 大型企业团队（>100人）  
✅ 大规模模型（>100万元素）  
✅ 高并发读写需求  
✅ 复杂查询分析需求  
✅ 企业级可靠性要求  

---

## 📊 总结

根据您的需求，可以选择：

1. **如果需要轻量级、易部署** → 选择文件系统方案
2. **如果需要企业级、高性能** → 选择数据库方案  
3. **如果需要混合方案** → 核心用文件系统，缓存用内存，复杂查询用嵌入式数据库（如SQLite）

两种方案都能提供完整的UML元模型支持，关键是根据实际需求选择合适的架构。 