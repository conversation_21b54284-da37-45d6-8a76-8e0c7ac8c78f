# 企业级UML元模型数据架构设计
## MBSE专家级设计方案

### 🎯 设计目标与原则

#### 核心目标
1. **完整性**: 100% UML 2.5语义覆盖
2. **正确性**: OCL约束完整实现
3. **性能**: 支持大规模模型（>10万元素）
4. **扩展性**: 支持SysML、UPDM等领域扩展
5. **工具链兼容**: 与主流MBSE工具互操作

#### 设计原则
- **分层架构**: 清晰的MOF四层分离
- **约束驱动**: OCL约束作为一等公民
- **元数据优先**: 丰富的元数据支持
- **版本控制**: 模型演化和变更管理
- **并发友好**: 支持多用户协作

---

## 📐 架构设计

### 1. 四层MOF架构实现

```sql
-- ===========================================
-- M3层: MOF元元模型层
-- ===========================================

-- MOF元元类表
CREATE TABLE mof_metaclass (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    abstract_syntax TEXT NOT NULL, -- MOF抽象语法
    is_abstract BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    version TEXT DEFAULT '1.0.0'
);

-- MOF元属性表  
CREATE TABLE mof_metaproperty (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    owning_metaclass UUID REFERENCES mof_metaclass(id),
    property_type TEXT NOT NULL,
    multiplicity_lower INTEGER DEFAULT 0,
    multiplicity_upper INTEGER DEFAULT 1, -- -1表示无限
    is_derived BOOLEAN DEFAULT false,
    is_ordered BOOLEAN DEFAULT false,
    is_unique BOOLEAN DEFAULT true
);

-- MOF元关联表
CREATE TABLE mof_metaassociation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    source_metaclass UUID REFERENCES mof_metaclass(id),
    target_metaclass UUID REFERENCES mof_metaclass(id),
    association_type association_type_enum,
    navigability navigability_enum
);

-- ===========================================
-- M2层: UML元模型层 
-- ===========================================

-- UML元类注册表
CREATE TABLE uml_metaclass_registry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metaclass_name TEXT NOT NULL UNIQUE,
    mof_metaclass_id UUID REFERENCES mof_metaclass(id),
    package_uri TEXT NOT NULL, -- 如'http://www.eclipse.org/uml2/5.0.0/UML'
    abstract_syntax_definition JSONB, -- 完整的抽象语法
    inheritance_depth INTEGER DEFAULT 0,
    direct_superclasses UUID[],
    all_superclasses UUID[], -- 扁平化继承链
    owned_properties JSONB, -- 属性定义
    owned_operations JSONB, -- 操作定义
    applicable_constraints UUID[], -- 适用的OCL约束
    stereotype_profile UUID, -- 如果是stereotype
    is_primitive BOOLEAN DEFAULT false,
    version TEXT DEFAULT '2.5.1'
);

-- UML包结构表
CREATE TABLE uml_package_structure (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    package_name TEXT NOT NULL,
    parent_package UUID REFERENCES uml_package_structure(id),
    contained_metaclasses UUID[],
    package_imports UUID[],
    package_merges UUID[],
    owned_profiles UUID[]
);
```

### 2. 约束引擎子系统

```sql
-- ===========================================
-- OCL约束引擎
-- ===========================================

-- OCL约束定义表
CREATE TABLE ocl_constraint_definition (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    constraint_name TEXT NOT NULL,
    constraint_type constraint_type_enum, -- inv, pre, post, derive, init
    context_metaclass UUID REFERENCES uml_metaclass_registry(id),
    ocl_expression TEXT NOT NULL, -- 原始OCL表达式
    sql_translation TEXT, -- SQL转换（如可能）
    validation_function TEXT, -- PL/pgSQL验证函数名
    severity severity_enum DEFAULT 'error',
    error_message TEXT,
    constraint_category category_enum, -- structural, behavioral, semantic
    applicable_profiles UUID[], -- 适用的Profile
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_validated TIMESTAMP WITH TIME ZONE
);

-- 约束依赖关系表
CREATE TABLE ocl_constraint_dependencies (
    constraint_id UUID REFERENCES ocl_constraint_definition(id),
    depends_on_constraint UUID REFERENCES ocl_constraint_definition(id),
    dependency_type dependency_type_enum, -- requires, conflicts, implies
    PRIMARY KEY (constraint_id, depends_on_constraint)
);

-- 约束验证日志表
CREATE TABLE constraint_validation_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    constraint_id UUID REFERENCES ocl_constraint_definition(id),
    model_element_id UUID,
    validation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    validation_result validation_result_enum, -- passed, failed, warning, skipped
    error_details JSONB,
    context_data JSONB,
    validation_duration INTERVAL
);

-- 约束引擎配置表
CREATE TABLE constraint_engine_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    validation_mode validation_mode_enum, -- strict, lenient, development
    enabled_constraint_categories category_enum[],
    batch_validation_size INTEGER DEFAULT 1000,
    timeout_per_constraint INTERVAL DEFAULT '30 seconds',
    parallel_validation BOOLEAN DEFAULT true,
    cache_validation_results BOOLEAN DEFAULT true
);
```

### 3. 核心元模型实现

```sql
-- ===========================================
-- UML核心元模型表
-- ===========================================

-- 元素基表 (对应UML::Element)
CREATE TABLE uml_element (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metaclass_id UUID REFERENCES uml_metaclass_registry(id) NOT NULL,
    element_type TEXT NOT NULL, -- 具体元类名称
    owned_comments UUID[],
    owned_annotations UUID[],
    applied_stereotypes UUID[],
    model_container UUID REFERENCES uml_model(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    version TEXT DEFAULT '1.0.0',
    element_hash TEXT, -- 内容哈希，用于变更检测
    validation_status validation_status_enum DEFAULT 'pending'
);

-- 命名元素表 (对应UML::NamedElement)
CREATE TABLE uml_named_element (
    id UUID PRIMARY KEY REFERENCES uml_element(id) ON DELETE CASCADE,
    name TEXT,
    qualified_name TEXT, -- 计算得出的全限定名
    visibility visibility_enum DEFAULT 'public',
    namespace_id UUID REFERENCES uml_namespace(id),
    alias_name TEXT,
    name_expression UUID -- 指向NameExpression
);

-- 命名空间表 (对应UML::Namespace)  
CREATE TABLE uml_namespace (
    id UUID PRIMARY KEY REFERENCES uml_named_element(id) ON DELETE CASCADE,
    owned_members UUID[], -- 包含的成员元素
    imported_members UUID[], -- 导入的成员
    package_imports UUID[], -- 包导入
    element_imports UUID[], -- 元素导入
    owned_rules UUID[], -- 拥有的约束
    namespace_uri TEXT
);

-- 可包装元素表 (对应UML::PackageableElement)
CREATE TABLE uml_packageable_element (
    id UUID PRIMARY KEY REFERENCES uml_named_element(id) ON DELETE CASCADE,
    owning_package UUID REFERENCES uml_package(id),
    template_parameter UUID REFERENCES uml_template_parameter(id),
    package_uri TEXT
);

-- 类型表 (对应UML::Type)
CREATE TABLE uml_type (
    id UUID PRIMARY KEY REFERENCES uml_packageable_element(id) ON DELETE CASCADE,
    type_category type_category_enum, -- primitive, enumeration, class, interface, etc.
    is_abstract BOOLEAN DEFAULT false,
    owned_template_signature UUID REFERENCES uml_template_signature(id),
    template_bindings UUID[]
);

-- 分类器表 (对应UML::Classifier)
CREATE TABLE uml_classifier (
    id UUID PRIMARY KEY REFERENCES uml_type(id) ON DELETE CASCADE,
    is_final_specialization BOOLEAN DEFAULT false,
    generalization_sets UUID[],
    powertype_extent UUID[], -- 作为powertype的GeneralizationSet
    inherited_members UUID[], -- 继承的成员（计算得出）
    general_classifiers UUID[], -- 直接父类（计算得出）
    all_parents UUID[], -- 所有祖先类（计算得出）
    redefined_classifiers UUID[],
    substitutions UUID[], -- Substitution关系
    representation UUID REFERENCES uml_collaboration_use(id), -- 内部结构表示
    owned_use_cases UUID[],
    used_packages UUID[]
);

-- 类表 (对应UML::Class)
CREATE TABLE uml_class (
    id UUID PRIMARY KEY REFERENCES uml_classifier(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT false,
    owned_receptions UUID[], -- 拥有的Reception
    super_classes UUID[], -- 父类（派生属性）
    nested_classifiers UUID[], -- 嵌套分类器
    owned_attributes UUID[], -- 拥有的属性
    owned_operations UUID[], -- 拥有的操作
    extensions UUID[] -- Extension关系
);
```

### 4. 关系和约束建模

```sql
-- ===========================================
-- UML关系建模
-- ===========================================

-- 关系基表 (对应UML::Relationship)
CREATE TABLE uml_relationship (
    id UUID PRIMARY KEY REFERENCES uml_element(id) ON DELETE CASCADE,
    relationship_type relationship_type_enum,
    related_elements UUID[] NOT NULL CHECK (array_length(related_elements, 1) >= 2),
    relationship_direction relationship_direction_enum, -- unidirectional, bidirectional
    relationship_strength strength_enum, -- weak, strong
    constraint_rules UUID[] -- 关系约束
);

-- 泛化关系表 (对应UML::Generalization)
CREATE TABLE uml_generalization (
    id UUID PRIMARY KEY REFERENCES uml_relationship(id) ON DELETE CASCADE,
    general UUID REFERENCES uml_classifier(id) NOT NULL,
    specific UUID REFERENCES uml_classifier(id) NOT NULL,
    generalization_set UUID REFERENCES uml_generalization_set(id),
    substitutable BOOLEAN DEFAULT true,
    CONSTRAINT no_self_generalization CHECK (general != specific)
);

-- 泛化集表 (对应UML::GeneralizationSet)
CREATE TABLE uml_generalization_set (
    id UUID PRIMARY KEY REFERENCES uml_packageable_element(id) ON DELETE CASCADE,
    is_covering BOOLEAN DEFAULT false,
    is_disjoint BOOLEAN DEFAULT false,
    powertype UUID REFERENCES uml_classifier(id),
    generalizations UUID[] -- 包含的泛化关系
);

-- 关联表 (对应UML::Association)
CREATE TABLE uml_association (
    id UUID PRIMARY KEY REFERENCES uml_classifier(id) ON DELETE CASCADE,
    is_derived BOOLEAN DEFAULT false,
    member_ends UUID[] NOT NULL CHECK (array_length(member_ends, 1) >= 2),
    owned_ends UUID[], -- 拥有的端点
    navigable_owned_ends UUID[], -- 可导航的拥有端点
    association_class UUID REFERENCES uml_class(id) -- 关联类
);

-- 属性表 (对应UML::Property)
CREATE TABLE uml_property (
    id UUID PRIMARY KEY REFERENCES uml_structural_feature(id) ON DELETE CASCADE,
    aggregation aggregation_enum DEFAULT 'none',
    is_composite BOOLEAN DEFAULT false,
    is_derived BOOLEAN DEFAULT false,
    is_derived_union BOOLEAN DEFAULT false,
    is_id BOOLEAN DEFAULT false,
    default_value UUID REFERENCES uml_value_specification(id),
    opposite_property UUID REFERENCES uml_property(id),
    owning_association UUID REFERENCES uml_association(id),
    association_end_role association_end_role_enum,
    redefined_properties UUID[],
    subsetted_properties UUID[],
    qualifier UUID[] -- 限定符
);
```

### 5. 行为建模子系统

```sql
-- ===========================================
-- UML行为建模
-- ===========================================

-- 行为表 (对应UML::Behavior)
CREATE TABLE uml_behavior (
    id UUID PRIMARY KEY REFERENCES uml_class(id) ON DELETE CASCADE,
    behavior_type behavior_type_enum, -- activity, state_machine, interaction, etc.
    is_reentrant BOOLEAN DEFAULT true,
    owned_parameters UUID[],
    owned_parameter_sets UUID[],
    preconditions UUID[], -- 前置条件
    postconditions UUID[], -- 后置条件
    context_classifier UUID REFERENCES uml_classifier(id),
    redefined_behaviors UUID[],
    specification UUID REFERENCES uml_behavioral_feature(id) -- 实现的规约
);

-- 活动表 (对应UML::Activity)
CREATE TABLE uml_activity (
    id UUID PRIMARY KEY REFERENCES uml_behavior(id) ON DELETE CASCADE,
    is_single_execution BOOLEAN DEFAULT false,
    is_read_only BOOLEAN DEFAULT false,
    owned_nodes UUID[], -- 拥有的ActivityNode
    owned_edges UUID[], -- 拥有的ActivityEdge
    owned_groups UUID[], -- 拥有的ActivityGroup
    owned_partitions UUID[], -- 拥有的ActivityPartition
    structured_nodes UUID[], -- 结构化活动节点
    variables UUID[] -- 活动变量
);

-- 状态机表 (对应UML::StateMachine)
CREATE TABLE uml_state_machine (
    id UUID PRIMARY KEY REFERENCES uml_behavior(id) ON DELETE CASCADE,
    owned_regions UUID[] NOT NULL CHECK (array_length(owned_regions, 1) >= 1),
    submachine_states UUID[], -- 作为子机的状态
    connection_points UUID[], -- 连接点
    extended_state_machines UUID[], -- 扩展的状态机
    initial_state UUID, -- 初始状态（计算得出）
    final_states UUID[] -- 终态（计算得出）
);

-- 交互表 (对应UML::Interaction)
CREATE TABLE uml_interaction (
    id UUID PRIMARY KEY REFERENCES uml_behavior(id) ON DELETE CASCADE,
    owned_lifelines UUID[], -- 拥有的生命线
    owned_messages UUID[], -- 拥有的消息
    owned_fragments UUID[], -- 拥有的交互片段
    covered_elements UUID[], -- 覆盖的元素
    formal_gates UUID[], -- 正式门
    actions UUID[] -- 关联的动作
);
```

### 6. Profile和扩展机制

```sql
-- ===========================================
-- UML Profile扩展机制
-- ===========================================

-- Profile表 (对应UML::Profile)
CREATE TABLE uml_profile (
    id UUID PRIMARY KEY REFERENCES uml_package(id) ON DELETE CASCADE,
    profile_uri TEXT NOT NULL UNIQUE,
    profile_version TEXT DEFAULT '1.0.0',
    metaclass_references UUID[], -- 引用的元类
    metamodel_references UUID[], -- 引用的元模型
    owned_stereotypes UUID[], -- 拥有的刻板印象
    profile_applications UUID[], -- Profile应用
    is_nested_profile BOOLEAN DEFAULT false,
    base_profiles UUID[] -- 基础Profile（继承）
);

-- 刻板印象表 (对应UML::Stereotype)
CREATE TABLE uml_stereotype (
    id UUID PRIMARY KEY REFERENCES uml_class(id) ON DELETE CASCADE,
    owning_profile UUID REFERENCES uml_profile(id) NOT NULL,
    extended_metaclasses UUID[] NOT NULL, -- 扩展的元类
    stereotype_uri TEXT,
    icon_path TEXT, -- 图标路径
    keyword TEXT, -- 关键字表示
    tag_definitions UUID[], -- 标签定义
    required_stereotypes UUID[], -- 必需的其他stereotype
    is_abstract_stereotype BOOLEAN DEFAULT false
);

-- 扩展表 (对应UML::Extension)
CREATE TABLE uml_extension (
    id UUID PRIMARY KEY REFERENCES uml_association(id) ON DELETE CASCADE,
    is_required BOOLEAN DEFAULT false,
    metaclass_end UUID REFERENCES uml_property(id) NOT NULL,
    stereotype_end UUID REFERENCES uml_extension_end(id) NOT NULL,
    owning_stereotype UUID REFERENCES uml_stereotype(id) NOT NULL
);

-- 标签定义表 (对应UML::TagDefinition)
CREATE TABLE uml_tag_definition (
    id UUID PRIMARY KEY REFERENCES uml_property(id) ON DELETE CASCADE,
    owning_stereotype UUID REFERENCES uml_stereotype(id) NOT NULL,
    tag_name TEXT NOT NULL,
    tag_type TEXT NOT NULL, -- 标签值类型
    default_value TEXT,
    multiplicity_constraint TEXT,
    enumeration_values TEXT[], -- 如果是枚举类型
    validation_pattern TEXT -- 验证正则表达式
);

-- 刻板印象应用表
CREATE TABLE uml_stereotype_application (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    applied_element UUID REFERENCES uml_element(id) NOT NULL,
    applied_stereotype UUID REFERENCES uml_stereotype(id) NOT NULL,
    tag_values JSONB, -- 标签值
    application_context TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    applied_by TEXT,
    UNIQUE (applied_element, applied_stereotype)
);
```

### 7. 版本控制和变更管理

```sql
-- ===========================================
-- 模型版本控制
-- ===========================================

-- 模型版本表
CREATE TABLE model_version (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id UUID REFERENCES uml_model(id) NOT NULL,
    version_number TEXT NOT NULL,
    version_type version_type_enum, -- major, minor, patch, snapshot
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT NOT NULL,
    parent_version UUID REFERENCES model_version(id),
    merge_source_versions UUID[], -- 合并来源版本
    version_description TEXT,
    change_summary JSONB, -- 变更摘要
    is_released BOOLEAN DEFAULT false,
    release_notes TEXT
);

-- 变更记录表
CREATE TABLE element_change_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    element_id UUID REFERENCES uml_element(id) NOT NULL,
    version_id UUID REFERENCES model_version(id) NOT NULL,
    change_type change_type_enum, -- created, modified, deleted, moved
    old_state JSONB, -- 变更前状态
    new_state JSONB, -- 变更后状态
    change_description TEXT,
    changed_properties TEXT[], -- 具体变更的属性
    change_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_by TEXT,
    change_reason TEXT,
    related_changes UUID[] -- 相关变更
);

-- 模型差异表
CREATE TABLE model_diff (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_version UUID REFERENCES model_version(id) NOT NULL,
    target_version UUID REFERENCES model_version(id) NOT NULL,
    diff_type diff_type_enum, -- element, relationship, constraint
    diff_operation diff_operation_enum, -- add, delete, modify, move
    affected_element UUID REFERENCES uml_element(id),
    diff_details JSONB,
    conflict_resolution JSONB, -- 冲突解决方案
    computed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔧 实现策略

### 1. 分阶段实施路线图

#### **阶段1: 核心基础架构 (3-4个月)**
```sql
-- 优先级1: MOF基础 + 核心元模型
- mof_metaclass, mof_metaproperty, mof_metaassociation
- uml_metaclass_registry
- uml_element, uml_named_element, uml_namespace
- uml_type, uml_classifier, uml_class
- 基础OCL约束引擎

-- 可交付成果
✅ 基础UML Class建模能力
✅ 简单继承关系
✅ 基础约束验证
```

#### **阶段2: 关系和约束扩展 (2-3个月)**
```sql
-- 优先级2: 关系建模 + 约束引擎
- uml_relationship, uml_generalization, uml_association
- uml_property, uml_operation
- 完整OCL约束引擎
- 基础Profile支持

-- 可交付成果  
✅ 完整的静态结构建模
✅ 复杂约束验证
✅ 基础Profile扩展
```

#### **阶段3: 行为建模 (3-4个月)**
```sql
-- 优先级3: 动态行为建模
- uml_behavior, uml_activity, uml_state_machine
- uml_interaction
- 动态约束验证
- 行为语义检查

-- 可交付成果
✅ 完整的UML行为建模
✅ 动态语义验证
✅ 多视图一致性检查
```

#### **阶段4: 企业特性 (2-3个月)**
```sql
-- 优先级4: 企业级特性
- 版本控制系统
- 大规模性能优化
- 分布式模型管理
- 高级分析和报告

-- 可交付成果
✅ 企业级模型管理
✅ 团队协作能力
✅ 模型分析和度量
```

### 2. 性能优化策略

#### **数据库级优化**
```sql
-- 分区策略
CREATE TABLE uml_element (
    ...
) PARTITION BY HASH (model_container);

-- 索引策略
CREATE INDEX CONCURRENTLY idx_element_metaclass_model 
ON uml_element (metaclass_id, model_container) 
INCLUDE (element_type, validation_status);

CREATE INDEX CONCURRENTLY idx_qualified_name_gin 
ON uml_named_element USING gin (qualified_name gin_trgm_ops);

-- 物化视图
CREATE MATERIALIZED VIEW mv_inheritance_hierarchy AS
WITH RECURSIVE hierarchy AS (
    SELECT id, id as root_id, 0 as depth, ARRAY[id] as path
    FROM uml_classifier 
    WHERE all_parents = '{}'
    
    UNION ALL
    
    SELECT c.id, h.root_id, h.depth + 1, h.path || c.id
    FROM uml_classifier c
    JOIN hierarchy h ON c.id = ANY(h.path)
) SELECT * FROM hierarchy;
```

#### **应用级优化**
```python
# 缓存策略
@cached(ttl=3600)
def get_metaclass_hierarchy(metaclass_id: UUID) -> Dict:
    """缓存元类继承层次"""
    pass

# 批量操作
async def batch_validate_constraints(
    element_ids: List[UUID],
    batch_size: int = 1000
) -> List[ValidationResult]:
    """批量约束验证"""
    pass

# 异步处理
@celery.task
def validate_model_consistency(model_id: UUID):
    """异步模型一致性检查"""
    pass
```

### 3. 约束引擎实现

#### **OCL到SQL转换引擎**
```python
class OCLToSQLTranslator:
    """OCL表达式到SQL的转换器"""
    
    def translate_constraint(self, ocl_expr: str, context: str) -> str:
        """将OCL约束转换为SQL检查函数"""
        # 解析OCL AST
        ast = self.parse_ocl(ocl_expr)
        
        # 转换为SQL
        sql_check = self.ast_to_sql(ast, context)
        
        return f"""
        CREATE OR REPLACE FUNCTION check_{constraint_name}(
            element_id UUID
        ) RETURNS BOOLEAN AS $$
        BEGIN
            RETURN ({sql_check});
        END;
        $$ LANGUAGE plpgsql;
        """

    def validate_all_constraints(self, element_id: UUID) -> List[ValidationResult]:
        """验证元素的所有适用约束"""
        pass
```

#### **约束验证触发器**
```sql
-- 实时约束验证触发器
CREATE OR REPLACE FUNCTION trigger_constraint_validation()
RETURNS TRIGGER AS $$
DECLARE
    constraint_record RECORD;
    validation_result BOOLEAN;
BEGIN
    -- 获取适用的约束
    FOR constraint_record IN 
        SELECT * FROM ocl_constraint_definition 
        WHERE context_metaclass = NEW.metaclass_id
    LOOP
        -- 执行约束验证
        EXECUTE 'SELECT ' || constraint_record.validation_function || '($1)'
        INTO validation_result USING NEW.id;
        
        -- 记录验证结果
        INSERT INTO constraint_validation_log (
            constraint_id, model_element_id, validation_result
        ) VALUES (
            constraint_record.id, NEW.id, 
            CASE WHEN validation_result THEN 'passed' ELSE 'failed' END
        );
        
        -- 严格模式下，约束失败则回滚
        IF NOT validation_result AND (
            SELECT validation_mode FROM constraint_engine_config LIMIT 1
        ) = 'strict' THEN
            RAISE EXCEPTION 'Constraint violation: %', constraint_record.constraint_name;
        END IF;
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 应用触发器到核心表
CREATE TRIGGER constraint_validation_trigger
    AFTER INSERT OR UPDATE ON uml_element
    FOR EACH ROW EXECUTE FUNCTION trigger_constraint_validation();
```

### 4. API设计示例

#### **GraphQL Schema**
```graphql
type UMLElement {
    id: ID!
    metaclass: UMLMetaclass!
    elementType: String!
    name: String
    qualifiedName: String
    namespace: UMLNamespace
    ownedComments: [UMLComment!]!
    appliedStereotypes: [StereotypeApplication!]!
    validationStatus: ValidationStatus!
    constraints: [ConstraintValidation!]!
}

type UMLClass implements UMLElement {
    isAbstract: Boolean!
    isActive: Boolean!
    superClasses: [UMLClass!]!
    ownedAttributes: [UMLProperty!]!
    ownedOperations: [UMLOperation!]!
    nestedClassifiers: [UMLClassifier!]!
}

type Query {
    # 元模型查询
    metaclass(name: String!): UMLMetaclass
    metaclassHierarchy(root: String): [UMLMetaclass!]!
    
    # 模型查询
    model(id: ID!): UMLModel
    findElements(
        metaclass: String,
        stereotype: String,
        namespace: String,
        namePattern: String
    ): [UMLElement!]!
    
    # 约束查询
    validateElement(elementId: ID!): [ConstraintValidation!]!
    validateModel(modelId: ID!): ModelValidationReport!
}

type Mutation {
    # 模型操作
    createElement(input: CreateElementInput!): UMLElement!
    updateElement(id: ID!, input: UpdateElementInput!): UMLElement!
    deleteElement(id: ID!): Boolean!
    
    # 关系操作
    createGeneralization(
        general: ID!, 
        specific: ID!
    ): UMLGeneralization!
    
    # Profile操作
    applyStereotype(
        elementId: ID!, 
        stereotypeId: ID!, 
        tagValues: [TagValue!]
    ): StereotypeApplication!
}
```

#### **REST API端点**
```python
# FastAPI实现示例
@app.get("/api/v1/models/{model_id}/elements")
async def get_model_elements(
    model_id: UUID,
    metaclass: Optional[str] = None,
    stereotype: Optional[str] = None,
    include_inherited: bool = False,
    page: int = 1,
    size: int = 100
) -> PaginatedResponse[UMLElement]:
    """获取模型中的元素"""
    pass

@app.post("/api/v1/constraints/validate")
async def validate_constraints(
    request: ConstraintValidationRequest
) -> ConstraintValidationReport:
    """执行约束验证"""
    pass

@app.get("/api/v1/metamodel/hierarchy")
async def get_metaclass_hierarchy() -> MetaclassHierarchy:
    """获取元类继承层次"""
    pass
```

---

## 📊 关键技术决策

### 1. 数据库技术栈选择

| 组件 | 技术选择 | 理由 |
|------|----------|------|
| **主数据库** | PostgreSQL 14+ | UUID、JSONB、递归查询、触发器 |
| **缓存层** | Redis Cluster | 元类定义缓存、会话管理 |
| **搜索引擎** | Elasticsearch | 全文搜索、复杂查询 |
| **时序数据** | TimescaleDB | 版本历史、性能指标 |
| **图数据库** | Neo4j | 复杂关系分析、依赖追踪 |

### 2. 约束引擎架构

```python
# 约束引擎架构
class ConstraintEngine:
    def __init__(self):
        self.ocl_parser = OCLParser()
        self.sql_translator = OCLToSQLTranslator()
        self.constraint_cache = ConstraintCache()
        self.validation_scheduler = ValidationScheduler()
    
    async def register_constraint(self, constraint: OCLConstraint):
        """注册OCL约束"""
        # 1. 解析OCL表达式
        # 2. 转换为SQL检查函数
        # 3. 注册触发器
        # 4. 缓存约束定义
    
    async def validate_element(self, element_id: UUID) -> ValidationReport:
        """验证单个元素"""
        # 1. 获取适用约束
        # 2. 执行验证
        # 3. 记录结果
        # 4. 返回报告
```

### 3. 扩展机制设计

```sql
-- Profile扩展注册
CREATE TABLE domain_extension_registry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    extension_name TEXT NOT NULL UNIQUE, -- 如'SysML', 'UPDM'
    extension_version TEXT NOT NULL,
    base_profile_id UUID REFERENCES uml_profile(id),
    extended_metaclasses JSONB, -- 扩展的元类定义
    additional_constraints JSONB, -- 额外的约束
    stereotype_library JSONB, -- 刻板印象库
    installation_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- 动态视图生成
CREATE OR REPLACE FUNCTION create_domain_views(extension_name TEXT)
RETURNS VOID AS $$
DECLARE
    metaclass_record RECORD;
BEGIN
    -- 为每个扩展元类创建视图
    FOR metaclass_record IN 
        SELECT * FROM get_extension_metaclasses(extension_name)
    LOOP
        EXECUTE format('
            CREATE VIEW %I AS
            SELECT e.*, sa.tag_values
            FROM uml_element e
            JOIN uml_stereotype_application sa ON e.id = sa.applied_element
            WHERE sa.applied_stereotype = %L
        ', metaclass_record.view_name, metaclass_record.stereotype_id);
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

---

## 🚀 部署和运维

### 1. 容器化部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: uml_metamodel
      POSTGRES_USER: uml_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  api:
    build: ./api
    environment:
      DATABASE_URL: postgresql://uml_user:${POSTGRES_PASSWORD}@postgres:5432/uml_metamodel
      REDIS_URL: redis://redis:6379
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis

  constraint-engine:
    build: ./constraint-engine
    environment:
      DATABASE_URL: postgresql://uml_user:${POSTGRES_PASSWORD}@postgres:5432/uml_metamodel
    depends_on:
      - postgres
```

### 2. 监控和告警

```python
# 性能监控
@app.middleware("http")
async def performance_monitoring(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    
    # 记录性能指标
    metrics.record_api_duration(
        endpoint=request.url.path,
        method=request.method,
        duration=duration,
        status_code=response.status_code
    )
    
    return response

# 约束验证监控
class ConstraintValidationMonitor:
    def record_validation_result(self, result: ValidationResult):
        metrics.increment('constraint_validations_total', {
            'constraint_type': result.constraint_type,
            'result': result.status,
            'metaclass': result.context_metaclass
        })
```

---

## 📈 成功指标

### 1. 功能完整性指标
- **UML覆盖率**: ≥95% UML 2.5元类支持
- **约束完整性**: ≥90% OCL约束自动验证
- **Profile支持**: 支持SysML、UPDM等主流Profile

### 2. 性能指标
- **响应时间**: 99%的API调用 <500ms
- **并发性能**: 支持1000+并发用户
- **模型规模**: 支持100万+元素的大型模型

### 3. 可靠性指标
- **可用性**: ≥99.9% SLA
- **数据一致性**: 零数据丢失
- **约束准确性**: ≥99.5% 约束验证准确率

这个设计方案提供了**生产级的UML元模型架构**，能够完整支持UML语义、扩展机制和企业级需求。通过分阶段实施，可以逐步构建一个强大、可扩展的MBSE平台基础设施。 