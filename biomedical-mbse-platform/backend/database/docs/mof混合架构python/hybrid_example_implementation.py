#!/usr/bin/env python3
"""
混合UML元模型架构 - 完整实现示例
展示如何在同一个项目中同时支持文件系统和PostgreSQL存储
"""

import asyncio
import json
import yaml
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# =============================================================================
# 核心抽象层
# =============================================================================

class StorageBackend(Enum):
    """存储后端类型"""
    FILE_SYSTEM = "file_system"
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"
    HYBRID = "hybrid"

@dataclass
class StorageConfig:
    """存储配置"""
    backend: StorageBackend
    connection_string: Optional[str] = None
    file_repository_path: Optional[str] = "./metamodel_repository"
    cache_enabled: bool = True
    validation_mode: str = "lenient"
    backup_enabled: bool = True

@dataclass
class UMLElement:
    """UML元素数据类"""
    id: str
    metaclass: str
    name: str
    qualified_name: Optional[str] = None
    visibility: str = "public"
    namespace: Optional[str] = None
    properties: Dict[str, Any] = None
    relationships: Dict[str, List[str]] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}
        if self.relationships is None:
            self.relationships = {}
        if self.metadata is None:
            self.metadata = {
                "created": datetime.now().isoformat(),
                "modified": datetime.now().isoformat(),
                "version": "1.0.0"
            }

# =============================================================================
# 存储抽象接口
# =============================================================================

from abc import ABC, abstractmethod

class IMetamodelStorage(ABC):
    """元模型存储抽象接口"""
    
    @abstractmethod
    async def initialize(self, config: StorageConfig) -> bool:
        """初始化存储后端"""
        pass
    
    @abstractmethod
    async def save_element(self, element: UMLElement) -> bool:
        """保存元素"""
        pass
    
    @abstractmethod
    async def get_element(self, element_id: str) -> Optional[UMLElement]:
        """获取元素"""
        pass
    
    @abstractmethod
    async def find_elements(self, **filters) -> List[UMLElement]:
        """查找元素"""
        pass
    
    @abstractmethod
    async def delete_element(self, element_id: str) -> bool:
        """删除元素"""
        pass
    
    @abstractmethod
    async def get_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        pass

# =============================================================================
# 文件系统存储实现
# =============================================================================

import aiofiles
import aiofiles.os
from pathlib import Path

class FileSystemStorage(IMetamodelStorage):
    """文件系统存储实现"""
    
    def __init__(self):
        self.repository_root: Optional[Path] = None
        self.elements_cache: Dict[str, UMLElement] = {}
        self.indices: Dict[str, Dict] = {}
        self.initialized = False
    
    async def initialize(self, config: StorageConfig) -> bool:
        """初始化文件系统存储"""
        try:
            self.repository_root = Path(config.file_repository_path)
            
            # 创建目录结构
            await self._create_directory_structure()
            
            # 加载现有数据
            if config.cache_enabled:
                await self._load_existing_elements()
                await self._build_indices()
            
            self.initialized = True
            logger.info(f"FileSystem storage initialized at {self.repository_root}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize file storage: {e}")
            return False
    
    async def _create_directory_structure(self):
        """创建标准目录结构"""
        directories = [
            "elements/classes",
            "elements/interfaces", 
            "elements/packages",
            "elements/associations",
            "elements/behaviors",
            "schemas",
            "constraints",
            "config",
            "metadata"
        ]
        
        for dir_path in directories:
            full_path = self.repository_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
    
    async def save_element(self, element: UMLElement) -> bool:
        """保存元素到文件"""
        try:
            # 确定保存路径
            element_dir = self._get_element_directory(element.metaclass)
            element_file = element_dir / f"{element.id}.json"
            
            # 更新修改时间
            element.metadata["modified"] = datetime.now().isoformat()
            
            # 保存到文件
            element_data = asdict(element)
            async with aiofiles.open(element_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(element_data, indent=2, ensure_ascii=False))
            
            # 更新缓存和索引
            self.elements_cache[element.id] = element
            await self._update_indices(element)
            
            logger.debug(f"Saved element {element.id} to {element_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save element {element.id}: {e}")
            return False
    
    async def get_element(self, element_id: str) -> Optional[UMLElement]:
        """获取元素"""
        # 先从缓存获取
        if element_id in self.elements_cache:
            return self.elements_cache[element_id]
        
        # 从文件系统加载
        element_file = await self._find_element_file(element_id)
        if element_file and element_file.exists():
            try:
                async with aiofiles.open(element_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    data = json.loads(content)
                    element = UMLElement(**data)
                    
                    # 更新缓存
                    self.elements_cache[element_id] = element
                    return element
                    
            except Exception as e:
                logger.error(f"Failed to load element {element_id}: {e}")
        
        return None
    
    async def find_elements(self, **filters) -> List[UMLElement]:
        """查找元素"""
        results = []
        
        # 使用索引优化查询
        candidate_ids = self._get_candidate_ids(filters)
        
        for element_id in candidate_ids:
            element = await self.get_element(element_id)
            if element and self._matches_filters(element, filters):
                results.append(element)
        
        return results
    
    async def delete_element(self, element_id: str) -> bool:
        """删除元素"""
        try:
            element_file = await self._find_element_file(element_id)
            if element_file and element_file.exists():
                element_file.unlink()
            
            # 从缓存和索引中移除
            if element_id in self.elements_cache:
                del self.elements_cache[element_id]
            
            await self._rebuild_indices()
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete element {element_id}: {e}")
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取存储统计"""
        total_elements = len(self.elements_cache)
        metaclass_counts = {}
        
        for element in self.elements_cache.values():
            metaclass = element.metaclass
            metaclass_counts[metaclass] = metaclass_counts.get(metaclass, 0) + 1
        
        return {
            "storage_type": "file_system",
            "total_elements": total_elements,
            "metaclass_distribution": metaclass_counts,
            "repository_path": str(self.repository_root),
            "cache_size": len(self.elements_cache)
        }
    
    def _get_element_directory(self, metaclass: str) -> Path:
        """获取元素目录"""
        subdir_map = {
            "Class": "classes",
            "Interface": "interfaces", 
            "Package": "packages",
            "Association": "associations",
            "Activity": "behaviors",
            "StateMachine": "behaviors"
        }
        
        subdir = subdir_map.get(metaclass, "others")
        return self.repository_root / "elements" / subdir
    
    async def _find_element_file(self, element_id: str) -> Optional[Path]:
        """查找元素文件"""
        for element_dir in (self.repository_root / "elements").iterdir():
            if element_dir.is_dir():
                element_file = element_dir / f"{element_id}.json"
                if element_file.exists():
                    return element_file
        return None
    
    async def _load_existing_elements(self):
        """加载现有元素"""
        elements_dir = self.repository_root / "elements"
        if not elements_dir.exists():
            return
        
        for element_file in elements_dir.rglob("*.json"):
            try:
                async with aiofiles.open(element_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    data = json.loads(content)
                    element = UMLElement(**data)
                    self.elements_cache[element.id] = element
                    
            except Exception as e:
                logger.warning(f"Failed to load element from {element_file}: {e}")
    
    async def _build_indices(self):
        """构建内存索引"""
        self.indices = {
            "by_metaclass": {},
            "by_name": {},
            "by_namespace": {}
        }
        
        for element_id, element in self.elements_cache.items():
            # 按元类索引
            metaclass = element.metaclass
            if metaclass not in self.indices["by_metaclass"]:
                self.indices["by_metaclass"][metaclass] = []
            self.indices["by_metaclass"][metaclass].append(element_id)
            
            # 按名称索引
            if element.name:
                self.indices["by_name"][element.name] = element_id
            
            # 按命名空间索引
            if element.namespace:
                if element.namespace not in self.indices["by_namespace"]:
                    self.indices["by_namespace"][element.namespace] = []
                self.indices["by_namespace"][element.namespace].append(element_id)
    
    async def _update_indices(self, element: UMLElement):
        """更新索引"""
        if not hasattr(self, 'indices') or not self.indices:
            await self._build_indices()
            return
        
        # 更新元类索引
        metaclass = element.metaclass
        if metaclass not in self.indices["by_metaclass"]:
            self.indices["by_metaclass"][metaclass] = []
        if element.id not in self.indices["by_metaclass"][metaclass]:
            self.indices["by_metaclass"][metaclass].append(element.id)
    
    async def _rebuild_indices(self):
        """重建索引"""
        await self._build_indices()
    
    def _get_candidate_ids(self, filters: Dict) -> List[str]:
        """根据过滤条件获取候选ID"""
        if "metaclass" in filters and filters["metaclass"] in self.indices.get("by_metaclass", {}):
            return self.indices["by_metaclass"][filters["metaclass"]]
        elif "namespace" in filters and filters["namespace"] in self.indices.get("by_namespace", {}):
            return self.indices["by_namespace"][filters["namespace"]]
        else:
            return list(self.elements_cache.keys())
    
    def _matches_filters(self, element: UMLElement, filters: Dict) -> bool:
        """检查元素是否匹配过滤条件"""
        for key, value in filters.items():
            if key == "metaclass" and element.metaclass != value:
                return False
            elif key == "name" and element.name != value:
                return False
            elif key == "namespace" and element.namespace != value:
                return False
            elif key == "name_pattern":
                import re
                if not re.search(value, element.name or "", re.IGNORECASE):
                    return False
        return True

# =============================================================================
# PostgreSQL存储实现（简化版本）
# =============================================================================

class PostgreSQLStorage(IMetamodelStorage):
    """PostgreSQL存储实现"""
    
    def __init__(self):
        self.connection_string: Optional[str] = None
        self.initialized = False
    
    async def initialize(self, config: StorageConfig) -> bool:
        """初始化PostgreSQL存储"""
        try:
            self.connection_string = config.connection_string
            # 这里应该创建数据库连接池和表结构
            # 为简化示例，只做标记
            self.initialized = True
            logger.info("PostgreSQL storage initialized (mock)")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL storage: {e}")
            return False
    
    async def save_element(self, element: UMLElement) -> bool:
        """保存元素到PostgreSQL"""
        if not self.initialized:
            return False
        
        try:
            # 模拟数据库保存
            logger.debug(f"Saving element {element.id} to PostgreSQL")
            return True
        except Exception as e:
            logger.error(f"Failed to save element to PostgreSQL: {e}")
            return False
    
    async def get_element(self, element_id: str) -> Optional[UMLElement]:
        """从PostgreSQL获取元素"""
        if not self.initialized:
            return None
        
        # 模拟数据库查询
        logger.debug(f"Getting element {element_id} from PostgreSQL")
        return None
    
    async def find_elements(self, **filters) -> List[UMLElement]:
        """从PostgreSQL查找元素"""
        if not self.initialized:
            return []
        
        # 模拟数据库查询
        logger.debug(f"Finding elements in PostgreSQL with filters: {filters}")
        return []
    
    async def delete_element(self, element_id: str) -> bool:
        """从PostgreSQL删除元素"""
        if not self.initialized:
            return False
        
        try:
            logger.debug(f"Deleting element {element_id} from PostgreSQL")
            return True
        except Exception as e:
            logger.error(f"Failed to delete element from PostgreSQL: {e}")
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取PostgreSQL存储统计"""
        return {
            "storage_type": "postgresql",
            "connection_string": self.connection_string,
            "initialized": self.initialized
        }

# =============================================================================
# 混合存储管理器
# =============================================================================

class HybridStorageManager:
    """混合存储管理器"""
    
    def __init__(self):
        self.primary_storage: Optional[IMetamodelStorage] = None
        self.secondary_storage: Optional[IMetamodelStorage] = None
        self.config: Optional[StorageConfig] = None
    
    async def initialize(self, config: StorageConfig) -> bool:
        """初始化混合存储"""
        self.config = config
        
        if config.backend == StorageBackend.FILE_SYSTEM:
            self.primary_storage = FileSystemStorage()
            
        elif config.backend == StorageBackend.POSTGRESQL:
            self.primary_storage = PostgreSQLStorage()
            
        elif config.backend == StorageBackend.HYBRID:
            # 主存储：PostgreSQL，备用存储：文件系统
            self.primary_storage = PostgreSQLStorage()
            self.secondary_storage = FileSystemStorage()
            
            # 初始化备用存储
            file_config = StorageConfig(
                backend=StorageBackend.FILE_SYSTEM,
                file_repository_path=config.file_repository_path,
                cache_enabled=config.cache_enabled
            )
            await self.secondary_storage.initialize(file_config)
        
        # 初始化主存储
        success = await self.primary_storage.initialize(config)
        
        if success:
            logger.info(f"Hybrid storage manager initialized with backend: {config.backend}")
        
        return success
    
    async def save_element(self, element: UMLElement, sync_secondary: bool = True) -> bool:
        """保存元素（支持同步到备用存储）"""
        # 保存到主存储
        success = await self.primary_storage.save_element(element)
        
        # 同步到备用存储
        if success and sync_secondary and self.secondary_storage:
            await self.secondary_storage.save_element(element)
        
        return success
    
    async def get_element(self, element_id: str, fallback: bool = True) -> Optional[UMLElement]:
        """获取元素（支持回退）"""
        # 首先从主存储获取
        element = await self.primary_storage.get_element(element_id)
        
        # 如果主存储失败且有备用存储，尝试备用存储
        if not element and fallback and self.secondary_storage:
            element = await self.secondary_storage.get_element(element_id)
            
            # 如果从备用存储找到，同步到主存储
            if element:
                await self.primary_storage.save_element(element)
        
        return element
    
    async def find_elements(self, **filters) -> List[UMLElement]:
        """查找元素"""
        return await self.primary_storage.find_elements(**filters)
    
    async def delete_element(self, element_id: str, sync_secondary: bool = True) -> bool:
        """删除元素"""
        success = await self.primary_storage.delete_element(element_id)
        
        if success and sync_secondary and self.secondary_storage:
            await self.secondary_storage.delete_element(element_id)
        
        return success
    
    async def migrate_data(self, from_storage: IMetamodelStorage, 
                          to_storage: IMetamodelStorage) -> bool:
        """数据迁移"""
        logger.info("Starting data migration...")
        
        # 获取所有元素
        all_elements = await from_storage.find_elements()
        
        # 迁移到目标存储
        success_count = 0
        failed_count = 0
        
        for element in all_elements:
            if await to_storage.save_element(element):
                success_count += 1
            else:
                failed_count += 1
        
        logger.info(f"Migration completed: {success_count} success, {failed_count} failed")
        return failed_count == 0
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取存储统计"""
        primary_stats = await self.primary_storage.get_statistics()
        
        stats = {
            "storage_mode": self.config.backend.value,
            "primary_storage": primary_stats
        }
        
        if self.secondary_storage:
            secondary_stats = await self.secondary_storage.get_statistics()
            stats["secondary_storage"] = secondary_stats
        
        return stats

# =============================================================================
# 统一API层
# =============================================================================

class UnifiedMetamodelAPI:
    """统一元模型API"""
    
    def __init__(self):
        self.storage_manager = HybridStorageManager()
        self.initialized = False
    
    async def initialize_from_config(self, config: StorageConfig):
        """从配置对象初始化"""
        success = await self.storage_manager.initialize(config)
        self.initialized = success
        return success
    
    async def initialize_from_file(self, config_path: str):
        """从配置文件初始化"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        storage_config = config_data.get('storage', {})
        backend = StorageBackend(storage_config.get('backend', 'file_system'))
        
        config = StorageConfig(
            backend=backend,
            connection_string=storage_config.get('connection_string'),
            file_repository_path=storage_config.get('file_repository_path', './metamodel_repository'),
            cache_enabled=storage_config.get('cache_enabled', True),
            validation_mode=storage_config.get('validation_mode', 'lenient'),
            backup_enabled=storage_config.get('backup_enabled', True)
        )
        
        return await self.initialize_from_config(config)
    
    # 元素操作API
    async def create_class(self, name: str, namespace: str = None, 
                          is_abstract: bool = False, **properties) -> UMLElement:
        """创建UML类"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        element = UMLElement(
            id=str(uuid.uuid4()),
            metaclass="Class",
            name=name,
            qualified_name=f"{namespace}::{name}" if namespace else name,
            namespace=namespace,
            properties={
                "isAbstract": is_abstract,
                **properties
            }
        )
        
        await self.storage_manager.save_element(element)
        return element
    
    async def create_interface(self, name: str, namespace: str = None, **properties) -> UMLElement:
        """创建UML接口"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        element = UMLElement(
            id=str(uuid.uuid4()),
            metaclass="Interface",
            name=name,
            qualified_name=f"{namespace}::{name}" if namespace else name,
            namespace=namespace,
            properties=properties
        )
        
        await self.storage_manager.save_element(element)
        return element
    
    async def create_package(self, name: str, parent_package: str = None, **properties) -> UMLElement:
        """创建UML包"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        element = UMLElement(
            id=str(uuid.uuid4()),
            metaclass="Package",
            name=name,
            qualified_name=f"{parent_package}::{name}" if parent_package else name,
            namespace=parent_package,
            properties=properties
        )
        
        await self.storage_manager.save_element(element)
        return element
    
    async def get_element(self, element_id: str) -> Optional[UMLElement]:
        """获取元素"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        return await self.storage_manager.get_element(element_id)
    
    async def find_classes(self, name_pattern: str = None, namespace: str = None) -> List[UMLElement]:
        """查找类"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        filters = {"metaclass": "Class"}
        if name_pattern:
            filters["name_pattern"] = name_pattern
        if namespace:
            filters["namespace"] = namespace
        
        return await self.storage_manager.find_elements(**filters)
    
    async def find_elements_by_metaclass(self, metaclass: str) -> List[UMLElement]:
        """按元类查找元素"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        return await self.storage_manager.find_elements(metaclass=metaclass)
    
    async def update_element(self, element_id: str, **updates) -> bool:
        """更新元素"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        element = await self.storage_manager.get_element(element_id)
        if not element:
            return False
        
        # 更新属性
        for key, value in updates.items():
            if hasattr(element, key):
                setattr(element, key, value)
            else:
                element.properties[key] = value
        
        # 更新修改时间
        element.metadata["modified"] = datetime.now().isoformat()
        
        return await self.storage_manager.save_element(element)
    
    async def delete_element(self, element_id: str) -> bool:
        """删除元素"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        return await self.storage_manager.delete_element(element_id)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        return await self.storage_manager.get_statistics()
    
    async def switch_backend(self, new_backend: StorageBackend, 
                           migrate_data: bool = True) -> bool:
        """动态切换存储后端"""
        if not self.initialized:
            raise RuntimeError("API not initialized")
        
        if migrate_data and self.storage_manager.primary_storage:
            # 创建新的存储实例
            if new_backend == StorageBackend.FILE_SYSTEM:
                new_storage = FileSystemStorage()
                new_config = StorageConfig(
                    backend=new_backend,
                    file_repository_path=self.storage_manager.config.file_repository_path
                )
            elif new_backend == StorageBackend.POSTGRESQL:
                new_storage = PostgreSQLStorage()
                new_config = StorageConfig(
                    backend=new_backend,
                    connection_string=self.storage_manager.config.connection_string
                )
            else:
                raise ValueError(f"Unsupported backend: {new_backend}")
            
            # 初始化新存储
            await new_storage.initialize(new_config)
            
            # 迁移数据
            await self.storage_manager.migrate_data(
                self.storage_manager.primary_storage, 
                new_storage
            )
        
        # 重新初始化存储管理器
        new_config = StorageConfig(
            backend=new_backend,
            connection_string=self.storage_manager.config.connection_string,
            file_repository_path=self.storage_manager.config.file_repository_path,
            cache_enabled=self.storage_manager.config.cache_enabled
        )
        
        return await self.storage_manager.initialize(new_config)

# =============================================================================
# 示例和测试
# =============================================================================

async def demo_file_system_usage():
    """演示文件系统存储的使用"""
    print("\n" + "="*60)
    print("文件系统存储演示")
    print("="*60)
    
    # 初始化API
    api = UnifiedMetamodelAPI()
    config = StorageConfig(
        backend=StorageBackend.FILE_SYSTEM,
        file_repository_path="./demo_repository",
        cache_enabled=True
    )
    
    await api.initialize_from_config(config)
    
    # 创建一些元素
    customer_class = await api.create_class(
        name="Customer",
        namespace="BusinessModel",
        is_abstract=False,
        description="客户实体类"
    )
    print(f"创建客户类: {customer_class.id}")
    
    order_class = await api.create_class(
        name="Order", 
        namespace="BusinessModel",
        is_abstract=False,
        description="订单实体类"
    )
    print(f"创建订单类: {order_class.id}")
    
    service_interface = await api.create_interface(
        name="PaymentService",
        namespace="Services",
        description="支付服务接口"
    )
    print(f"创建支付服务接口: {service_interface.id}")
    
    business_package = await api.create_package(
        name="BusinessModel",
        description="业务模型包"
    )
    print(f"创建业务模型包: {business_package.id}")
    
    # 查找元素
    all_classes = await api.find_classes()
    print(f"\n找到 {len(all_classes)} 个类:")
    for cls in all_classes:
        print(f"  - {cls.name} ({cls.qualified_name})")
    
    # 查找特定命名空间的类
    business_classes = await api.find_classes(namespace="BusinessModel")
    print(f"\nBusinessModel命名空间中的类 ({len(business_classes)} 个):")
    for cls in business_classes:
        print(f"  - {cls.name}")
    
    # 更新元素
    await api.update_element(
        customer_class.id,
        description="更新后的客户实体类",
        version="1.1.0"
    )
    print(f"\n已更新客户类")
    
    # 获取统计信息
    stats = await api.get_statistics()
    print(f"\n存储统计:")
    print(f"  存储类型: {stats['primary_storage']['storage_type']}")
    print(f"  总元素数: {stats['primary_storage']['total_elements']}")
    print(f"  元类分布: {stats['primary_storage']['metaclass_distribution']}")

async def demo_hybrid_usage():
    """演示混合存储的使用"""
    print("\n" + "="*60)
    print("混合存储演示")
    print("="*60)
    
    # 初始化混合存储API
    api = UnifiedMetamodelAPI()
    config = StorageConfig(
        backend=StorageBackend.HYBRID,
        connection_string="postgresql://user:pass@localhost:5432/uml_db",
        file_repository_path="./hybrid_repository",
        cache_enabled=True
    )
    
    await api.initialize_from_config(config)
    
    # 创建测试数据
    for i in range(5):
        await api.create_class(
            name=f"TestClass{i}",
            namespace="TestNamespace",
            description=f"测试类 {i}"
        )
    
    print("已创建5个测试类")
    
    # 获取统计信息
    stats = await api.get_statistics()
    print(f"混合存储统计:")
    print(f"  存储模式: {stats['storage_mode']}")
    print(f"  主存储: {stats['primary_storage']['storage_type']}")
    if 'secondary_storage' in stats:
        print(f"  备用存储: {stats['secondary_storage']['storage_type']}")

async def demo_backend_switching():
    """演示存储后端动态切换"""
    print("\n" + "="*60)
    print("存储后端动态切换演示")
    print("="*60)
    
    # 开始使用文件系统
    api = UnifiedMetamodelAPI()
    config = StorageConfig(
        backend=StorageBackend.FILE_SYSTEM,
        file_repository_path="./switch_test_repository"
    )
    
    await api.initialize_from_config(config)
    print("初始化文件系统存储")
    
    # 创建一些测试数据
    for i in range(3):
        await api.create_class(name=f"SwitchTestClass{i}")
    
    print("已在文件系统中创建3个类")
    
    # 获取文件系统中的数据
    fs_classes = await api.find_classes()
    print(f"文件系统中的类数量: {len(fs_classes)}")
    
    # 模拟切换到PostgreSQL（实际需要真实的数据库连接）
    print("\n准备切换到PostgreSQL存储...")
    print("(在真实环境中，这将迁移所有数据到PostgreSQL)")
    
    # success = await api.switch_backend(
    #     StorageBackend.POSTGRESQL, 
    #     migrate_data=True
    # )
    # print(f"切换结果: {'成功' if success else '失败'}")

async def main():
    """主演示函数"""
    print("UML元模型混合架构演示")
    print("支持文件系统和PostgreSQL双存储后端")
    
    try:
        # 演示文件系统存储
        await demo_file_system_usage()
        
        # 演示混合存储
        await demo_hybrid_usage()
        
        # 演示后端切换
        await demo_backend_switching()
        
        print("\n" + "="*60)
        print("演示完成！")
        print("="*60)
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main()) 