# 混合UML元模型架构设计
## 统一抽象层 + 双存储后端支持

### 🎯 设计理念

#### 核心优势
- **灵活部署**: 根据场景选择最适合的存储方案
- **无缝迁移**: 从文件系统平滑升级到数据库
- **统一API**: 相同的业务逻辑，不同的存储后端
- **混合模式**: 可同时使用两种存储方案
- **配置驱动**: 通过配置文件切换存储后端

#### 适用场景
```
开发阶段 → 文件系统（快速迭代）
测试阶段 → 文件系统（简单部署）
生产环境 → 数据库（高性能）
大型项目 → 数据库（企业级）
离线工具 → 文件系统（便携性）
```

---

## 🏗️ 统一抽象层设计

### 1. 存储抽象接口

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Set, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

class StorageBackend(Enum):
    """存储后端类型"""
    FILE_SYSTEM = "file_system"
    POSTGRESQL = "postgresql"
    SQLITE = "sqlite"
    HYBRID = "hybrid"

@dataclass
class StorageConfig:
    """存储配置"""
    backend: StorageBackend
    connection_string: Optional[str] = None
    file_repository_path: Optional[str] = None
    cache_enabled: bool = True
    validation_mode: str = "lenient"  # strict, lenient, development
    backup_enabled: bool = True

class IMetamodelStorage(ABC):
    """元模型存储抽象接口"""
    
    @abstractmethod
    async def initialize(self, config: StorageConfig) -> bool:
        """初始化存储后端"""
        pass
    
    @abstractmethod
    async def get_element(self, element_id: str) -> Optional[Dict[str, Any]]:
        """获取单个元素"""
        pass
    
    @abstractmethod
    async def save_element(self, element: Dict[str, Any]) -> bool:
        """保存元素"""
        pass
    
    @abstractmethod
    async def delete_element(self, element_id: str) -> bool:
        """删除元素"""
        pass
    
    @abstractmethod
    async def find_elements(self, 
                           metaclass: Optional[str] = None,
                           name_pattern: Optional[str] = None,
                           namespace: Optional[str] = None,
                           **filters) -> List[Dict[str, Any]]:
        """查找元素"""
        pass
    
    @abstractmethod
    async def get_metaclass_definition(self, metaclass_name: str) -> Optional[Dict]:
        """获取元类定义"""
        pass
    
    @abstractmethod
    async def validate_constraints(self, element_ids: List[str]) -> Dict[str, Any]:
        """验证约束"""
        pass
    
    @abstractmethod
    async def create_model_snapshot(self, model_id: str, version: str) -> bool:
        """创建模型快照"""
        pass
    
    @abstractmethod
    async def get_relationship_graph(self, element_id: str, depth: int = 1) -> Dict:
        """获取关系图"""
        pass

class IConstraintEngine(ABC):
    """约束引擎抽象接口"""
    
    @abstractmethod
    async def register_constraint(self, constraint: Dict[str, Any]) -> bool:
        """注册约束"""
        pass
    
    @abstractmethod
    async def validate_element(self, element_id: str, 
                              element_data: Dict[str, Any]) -> List[Dict]:
        """验证元素约束"""
        pass

class IQueryEngine(ABC):
    """查询引擎抽象接口"""
    
    @abstractmethod
    async def execute_query(self, query: str, parameters: Dict = None) -> List[Dict]:
        """执行查询"""
        pass
    
    @abstractmethod
    async def get_inheritance_hierarchy(self, metaclass: str) -> Dict:
        """获取继承层次"""
        pass
```

### 2. 文件系统存储实现

```python
import json
import yaml
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any
import aiofiles
import aiofiles.os

class FileSystemStorage(IMetamodelStorage):
    """文件系统存储实现"""
    
    def __init__(self):
        self.repository_root: Optional[Path] = None
        self.elements_cache: Dict[str, Dict] = {}
        self.metaclasses_cache: Dict[str, Dict] = {}
        self.constraints_cache: Dict[str, Dict] = {}
        self.indices: Dict[str, Dict] = {}
        
    async def initialize(self, config: StorageConfig) -> bool:
        """初始化文件系统存储"""
        try:
            self.repository_root = Path(config.file_repository_path)
            
            # 创建目录结构
            await self._create_directory_structure()
            
            # 加载缓存
            if config.cache_enabled:
                await self._load_all_to_cache()
                await self._build_indices()
            
            return True
        except Exception as e:
            print(f"Failed to initialize file storage: {e}")
            return False
    
    async def _create_directory_structure(self):
        """创建目录结构"""
        directories = [
            "schemas/mof", "schemas/uml/core", "schemas/uml/behavior", 
            "schemas/uml/profiles", "schemas/extensions/sysml",
            "constraints", "models", "registry", "config", "cache"
        ]
        
        for dir_path in directories:
            full_path = self.repository_root / dir_path
            await aiofiles.os.makedirs(full_path, exist_ok=True)
    
    async def get_element(self, element_id: str) -> Optional[Dict[str, Any]]:
        """获取单个元素"""
        # 先从缓存查找
        if element_id in self.elements_cache:
            return self.elements_cache[element_id]
        
        # 从文件查找
        element_file = await self._find_element_file(element_id)
        if element_file:
            async with aiofiles.open(element_file, 'r') as f:
                content = await f.read()
                element_data = json.loads(content)
                
                # 更新缓存
                self.elements_cache[element_id] = element_data
                return element_data
        
        return None
    
    async def save_element(self, element: Dict[str, Any]) -> bool:
        """保存元素到文件"""
        try:
            element_id = element['id']
            metaclass = element['metaclass']
            
            # 确定保存路径
            element_dir = self._get_element_directory(metaclass)
            await aiofiles.os.makedirs(element_dir, exist_ok=True)
            
            file_path = element_dir / f"{element_id}.json"
            
            # 保存到文件
            async with aiofiles.open(file_path, 'w') as f:
                await f.write(json.dumps(element, indent=2, ensure_ascii=False))
            
            # 更新缓存和索引
            self.elements_cache[element_id] = element
            await self._update_indices(element)
            
            return True
        except Exception as e:
            print(f"Failed to save element {element.get('id')}: {e}")
            return False
    
    async def find_elements(self, 
                           metaclass: Optional[str] = None,
                           name_pattern: Optional[str] = None,
                           namespace: Optional[str] = None,
                           **filters) -> List[Dict[str, Any]]:
        """查找元素"""
        results = []
        
        # 使用索引快速查找
        if metaclass and metaclass in self.indices.get('by_metaclass', {}):
            candidate_ids = self.indices['by_metaclass'][metaclass]
        else:
            candidate_ids = list(self.elements_cache.keys())
        
        for element_id in candidate_ids:
            element = await self.get_element(element_id)
            if element and self._matches_filters(element, name_pattern, namespace, **filters):
                results.append(element)
        
        return results
    
    def _matches_filters(self, element: Dict, name_pattern: str, 
                        namespace: str, **filters) -> bool:
        """检查元素是否匹配过滤条件"""
        if name_pattern:
            import re
            if not re.search(name_pattern, element.get('name', ''), re.IGNORECASE):
                return False
        
        if namespace:
            if element.get('namespace', {}).get('qualifiedName') != namespace:
                return False
        
        # 检查其他过滤条件
        for key, value in filters.items():
            if element.get(key) != value:
                return False
        
        return True
    
    async def _find_element_file(self, element_id: str) -> Optional[Path]:
        """查找元素文件"""
        models_dir = self.repository_root / "models"
        for element_file in models_dir.rglob("*.json"):
            if element_id in element_file.name:
                return element_file
        return None
    
    async def _load_all_to_cache(self):
        """加载所有数据到缓存"""
        # 加载元素
        models_dir = self.repository_root / "models"
        async for element_file in self._async_rglob(models_dir, "*.json"):
            if "cache" not in str(element_file):
                async with aiofiles.open(element_file, 'r') as f:
                    content = await f.read()
                    element_data = json.loads(content)
                    self.elements_cache[element_data['id']] = element_data
        
        # 加载元类定义
        schemas_dir = self.repository_root / "schemas"
        async for schema_file in self._async_rglob(schemas_dir, "*.schema.json"):
            async with aiofiles.open(schema_file, 'r') as f:
                content = await f.read()
                schema_data = json.loads(content)
                metaclass_name = schema_data.get('title', schema_file.stem)
                self.metaclasses_cache[metaclass_name] = schema_data
    
    async def _build_indices(self):
        """构建内存索引"""
        self.indices = {
            'by_metaclass': {},
            'by_name': {},
            'by_namespace': {}
        }
        
        for element_id, element in self.elements_cache.items():
            metaclass = element.get('metaclass')
            if metaclass:
                if metaclass not in self.indices['by_metaclass']:
                    self.indices['by_metaclass'][metaclass] = []
                self.indices['by_metaclass'][metaclass].append(element_id)
            
            name = element.get('name')
            if name:
                self.indices['by_name'][name] = element_id
            
            namespace = element.get('namespace', {}).get('qualifiedName')
            if namespace:
                if namespace not in self.indices['by_namespace']:
                    self.indices['by_namespace'][namespace] = []
                self.indices['by_namespace'][namespace].append(element_id)

class FileSystemConstraintEngine(IConstraintEngine):
    """文件系统约束引擎"""
    
    def __init__(self, storage: FileSystemStorage):
        self.storage = storage
        self.constraints: Dict[str, Dict] = {}
    
    async def validate_element(self, element_id: str, 
                              element_data: Dict[str, Any]) -> List[Dict]:
        """验证元素约束"""
        results = []
        metaclass = element_data.get('metaclass')
        
        # 获取适用的约束
        applicable_constraints = await self._get_applicable_constraints(metaclass)
        
        for constraint in applicable_constraints:
            try:
                result = await self._evaluate_constraint(constraint, element_data)
                results.append({
                    'constraint': constraint['name'],
                    'result': result,
                    'severity': constraint.get('severity', 'error'),
                    'message': constraint.get('description') if not result else None
                })
            except Exception as e:
                results.append({
                    'constraint': constraint['name'],
                    'result': False,
                    'severity': 'error',
                    'message': f"Validation error: {str(e)}"
                })
        
        return results
```

### 3. PostgreSQL存储实现

```python
import asyncpg
import json
from typing import Dict, List, Optional, Any

class PostgreSQLStorage(IMetamodelStorage):
    """PostgreSQL存储实现"""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.connection_string: Optional[str] = None
    
    async def initialize(self, config: StorageConfig) -> bool:
        """初始化PostgreSQL存储"""
        try:
            self.connection_string = config.connection_string
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            
            # 创建数据库表结构
            await self._create_schema()
            
            return True
        except Exception as e:
            print(f"Failed to initialize PostgreSQL storage: {e}")
            return False
    
    async def _create_schema(self):
        """创建数据库模式"""
        schema_sql = """
        -- 创建扩展
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        CREATE EXTENSION IF NOT EXISTS "pg_trgm";
        
        -- UML元素基表
        CREATE TABLE IF NOT EXISTS uml_element (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            metaclass TEXT NOT NULL,
            element_data JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            validation_status TEXT DEFAULT 'pending'
        );
        
        -- 元类定义表
        CREATE TABLE IF NOT EXISTS uml_metaclass (
            name TEXT PRIMARY KEY,
            definition JSONB NOT NULL,
            parent_metaclasses TEXT[],
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 约束定义表
        CREATE TABLE IF NOT EXISTS uml_constraint (
            name TEXT PRIMARY KEY,
            context TEXT NOT NULL,
            constraint_type TEXT NOT NULL,
            expression TEXT NOT NULL,
            severity TEXT DEFAULT 'error',
            description TEXT
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_element_metaclass ON uml_element(metaclass);
        CREATE INDEX IF NOT EXISTS idx_element_data_gin ON uml_element USING gin(element_data);
        CREATE INDEX IF NOT EXISTS idx_element_name ON uml_element((element_data->>'name'));
        """
        
        async with self.pool.acquire() as conn:
            await conn.execute(schema_sql)
    
    async def get_element(self, element_id: str) -> Optional[Dict[str, Any]]:
        """获取单个元素"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow(
                "SELECT element_data FROM uml_element WHERE id = $1",
                element_id
            )
            return dict(row['element_data']) if row else None
    
    async def save_element(self, element: Dict[str, Any]) -> bool:
        """保存元素"""
        try:
            async with self.pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO uml_element (id, metaclass, element_data, modified_at)
                    VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
                    ON CONFLICT (id) DO UPDATE SET
                        element_data = EXCLUDED.element_data,
                        modified_at = EXCLUDED.modified_at
                """, element['id'], element['metaclass'], json.dumps(element))
            
            return True
        except Exception as e:
            print(f"Failed to save element: {e}")
            return False
    
    async def find_elements(self, 
                           metaclass: Optional[str] = None,
                           name_pattern: Optional[str] = None,
                           namespace: Optional[str] = None,
                           **filters) -> List[Dict[str, Any]]:
        """查找元素"""
        conditions = []
        params = []
        param_count = 0
        
        if metaclass:
            param_count += 1
            conditions.append(f"metaclass = ${param_count}")
            params.append(metaclass)
        
        if name_pattern:
            param_count += 1
            conditions.append(f"element_data->>'name' ILIKE ${param_count}")
            params.append(f"%{name_pattern}%")
        
        if namespace:
            param_count += 1
            conditions.append(f"element_data->'namespace'->>'qualifiedName' = ${param_count}")
            params.append(namespace)
        
        # 构建查询
        where_clause = " AND ".join(conditions) if conditions else "TRUE"
        query = f"SELECT element_data FROM uml_element WHERE {where_clause}"
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            return [dict(row['element_data']) for row in rows]

class PostgreSQLConstraintEngine(IConstraintEngine):
    """PostgreSQL约束引擎"""
    
    def __init__(self, storage: PostgreSQLStorage):
        self.storage = storage
    
    async def validate_element(self, element_id: str, 
                              element_data: Dict[str, Any]) -> List[Dict]:
        """验证元素约束"""
        async with self.storage.pool.acquire() as conn:
            # 获取适用的约束
            constraints = await conn.fetch("""
                SELECT name, expression, severity, description
                FROM uml_constraint 
                WHERE context = $1 OR context = 'Element'
            """, element_data.get('metaclass'))
            
            results = []
            for constraint in constraints:
                # 这里需要实现OCL表达式的SQL转换和执行
                # 简化示例
                result = await self._evaluate_constraint_sql(
                    conn, constraint, element_data
                )
                results.append({
                    'constraint': constraint['name'],
                    'result': result,
                    'severity': constraint['severity'],
                    'message': constraint['description'] if not result else None
                })
            
            return results
```

### 4. 混合存储管理器

```python
class HybridStorageManager:
    """混合存储管理器"""
    
    def __init__(self):
        self.primary_storage: Optional[IMetamodelStorage] = None
        self.secondary_storage: Optional[IMetamodelStorage] = None
        self.constraint_engine: Optional[IConstraintEngine] = None
        self.config: Optional[StorageConfig] = None
    
    async def initialize(self, config: StorageConfig) -> bool:
        """初始化混合存储"""
        self.config = config
        
        if config.backend == StorageBackend.FILE_SYSTEM:
            self.primary_storage = FileSystemStorage()
            self.constraint_engine = FileSystemConstraintEngine(self.primary_storage)
            
        elif config.backend == StorageBackend.POSTGRESQL:
            self.primary_storage = PostgreSQLStorage()
            self.constraint_engine = PostgreSQLConstraintEngine(self.primary_storage)
            
        elif config.backend == StorageBackend.HYBRID:
            # 主存储：PostgreSQL，备用存储：文件系统
            self.primary_storage = PostgreSQLStorage()
            self.secondary_storage = FileSystemStorage()
            self.constraint_engine = PostgreSQLConstraintEngine(self.primary_storage)
            
            # 初始化两个存储
            await self.secondary_storage.initialize(StorageConfig(
                backend=StorageBackend.FILE_SYSTEM,
                file_repository_path=config.file_repository_path
            ))
        
        return await self.primary_storage.initialize(config)
    
    async def get_element(self, element_id: str, 
                         fallback: bool = True) -> Optional[Dict[str, Any]]:
        """获取元素（支持回退）"""
        # 首先从主存储获取
        element = await self.primary_storage.get_element(element_id)
        
        # 如果主存储失败且有备用存储，尝试备用存储
        if not element and fallback and self.secondary_storage:
            element = await self.secondary_storage.get_element(element_id)
            
            # 如果从备用存储找到，同步到主存储
            if element:
                await self.primary_storage.save_element(element)
        
        return element
    
    async def save_element(self, element: Dict[str, Any], 
                          sync_secondary: bool = True) -> bool:
        """保存元素（支持同步到备用存储）"""
        # 保存到主存储
        success = await self.primary_storage.save_element(element)
        
        # 同步到备用存储
        if success and sync_secondary and self.secondary_storage:
            await self.secondary_storage.save_element(element)
        
        return success
    
    async def migrate_data(self, from_backend: StorageBackend, 
                          to_backend: StorageBackend) -> bool:
        """数据迁移"""
        print(f"Starting migration from {from_backend} to {to_backend}")
        
        # 初始化源存储
        source_storage = self._create_storage(from_backend)
        await source_storage.initialize(self._get_config_for_backend(from_backend))
        
        # 获取所有元素
        all_elements = await source_storage.find_elements()
        
        # 迁移到目标存储
        migration_success = 0
        migration_failed = 0
        
        for element in all_elements:
            success = await self.primary_storage.save_element(element)
            if success:
                migration_success += 1
            else:
                migration_failed += 1
        
        print(f"Migration completed: {migration_success} success, {migration_failed} failed")
        return migration_failed == 0
    
    def _create_storage(self, backend: StorageBackend) -> IMetamodelStorage:
        """创建存储实例"""
        if backend == StorageBackend.FILE_SYSTEM:
            return FileSystemStorage()
        elif backend == StorageBackend.POSTGRESQL:
            return PostgreSQLStorage()
        else:
            raise ValueError(f"Unsupported backend: {backend}")

class UnifiedMetamodelAPI:
    """统一元模型API"""
    
    def __init__(self):
        self.storage_manager = HybridStorageManager()
    
    async def initialize(self, config_path: str):
        """从配置文件初始化"""
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        config = StorageConfig(**config_data['storage'])
        await self.storage_manager.initialize(config)
    
    async def get_element(self, element_id: str) -> Optional[Dict[str, Any]]:
        """统一的获取元素接口"""
        return await self.storage_manager.get_element(element_id)
    
    async def save_element(self, element: Dict[str, Any]) -> bool:
        """统一的保存元素接口"""
        return await self.storage_manager.save_element(element)
    
    async def find_elements(self, **filters) -> List[Dict[str, Any]]:
        """统一的查找元素接口"""
        return await self.storage_manager.primary_storage.find_elements(**filters)
    
    async def validate_element(self, element_id: str) -> List[Dict]:
        """统一的元素验证接口"""
        element = await self.get_element(element_id)
        if element:
            return await self.storage_manager.constraint_engine.validate_element(
                element_id, element
            )
        return []
    
    async def switch_backend(self, new_backend: StorageBackend, 
                           migrate_data: bool = True) -> bool:
        """动态切换存储后端"""
        old_backend = self.storage_manager.config.backend
        
        if migrate_data:
            await self.storage_manager.migrate_data(old_backend, new_backend)
        
        # 重新初始化新的存储后端
        new_config = StorageConfig(
            backend=new_backend,
            connection_string=self.storage_manager.config.connection_string,
            file_repository_path=self.storage_manager.config.file_repository_path
        )
        
        return await self.storage_manager.initialize(new_config)
```

---

## 📋 配置文件驱动

### 1. 统一配置文件

```yaml
# config/metamodel_config.yaml
storage:
  backend: "hybrid"  # file_system, postgresql, sqlite, hybrid
  
  # PostgreSQL配置
  connection_string: "postgresql://user:password@localhost:5432/uml_metamodel"
  
  # 文件系统配置
  file_repository_path: "./metamodel_repository"
  
  # 通用配置
  cache_enabled: true
  validation_mode: "lenient"  # strict, lenient, development
  backup_enabled: true

# 性能配置
performance:
  connection_pool_size: 20
  query_timeout: 30
  cache_size_mb: 100
  enable_parallel_validation: true

# 功能开关
features:
  enable_real_time_validation: true
  enable_version_control: true
  enable_backup_sync: true
  enable_profile_support: true

# 环境特定配置
environments:
  development:
    storage:
      backend: "file_system"
      validation_mode: "development"
    
  testing:
    storage:
      backend: "sqlite"
      connection_string: "sqlite:///test_metamodel.db"
    
  production:
    storage:
      backend: "postgresql"
      validation_mode: "strict"
```

### 2. 动态配置加载

```python
class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        with open(self.config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # 环境特定配置覆盖
        environment = os.getenv('METAMODEL_ENV', 'development')
        if environment in config.get('environments', {}):
            env_config = config['environments'][environment]
            config = self._merge_config(config, env_config)
        
        return config
    
    def get_storage_config(self) -> StorageConfig:
        """获取存储配置"""
        storage_config = self.config['storage']
        return StorageConfig(**storage_config)
    
    def _merge_config(self, base: Dict, override: Dict) -> Dict:
        """合并配置"""
        import copy
        result = copy.deepcopy(base)
        
        for key, value in override.items():
            if isinstance(value, dict) and key in result:
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
```

---

## 🚀 使用示例

### 1. 快速开始（文件系统）

```python
# 开发环境 - 文件系统存储
async def development_setup():
    api = UnifiedMetamodelAPI()
    await api.initialize("config/dev_config.yaml")
    
    # 创建一个类
    customer_class = {
        "id": "550e8400-e29b-41d4-a716-446655440001",
        "metaclass": "Class",
        "name": "Customer",
        "isAbstract": False,
        "ownedAttributes": []
    }
    
    await api.save_element(customer_class)
    
    # 查找所有类
    classes = await api.find_elements(metaclass="Class")
    print(f"Found {len(classes)} classes")
```

### 2. 生产环境（PostgreSQL）

```python
# 生产环境 - PostgreSQL存储
async def production_setup():
    api = UnifiedMetamodelAPI()
    await api.initialize("config/prod_config.yaml")
    
    # 大规模批量操作
    elements = []
    for i in range(10000):
        element = {
            "id": str(uuid.uuid4()),
            "metaclass": "Class",
            "name": f"GeneratedClass_{i}",
            "isAbstract": False
        }
        elements.append(element)
    
    # 批量保存
    for element in elements:
        await api.save_element(element)
    
    print("Batch operation completed")
```

### 3. 无缝迁移

```python
# 从文件系统迁移到PostgreSQL
async def migrate_to_database():
    api = UnifiedMetamodelAPI()
    await api.initialize("config/file_config.yaml")
    
    # 切换到PostgreSQL并迁移数据
    success = await api.switch_backend(
        StorageBackend.POSTGRESQL, 
        migrate_data=True
    )
    
    if success:
        print("Migration to PostgreSQL completed successfully")
    else:
        print("Migration failed")
```

---

## 🎯 部署场景建议

### 1. 场景映射

| 使用场景 | 推荐配置 | 存储后端 | 部署方式 |
|----------|----------|----------|----------|
| **个人开发** | 轻量级 | File System | 本地目录 |
| **小团队原型** | 快速迭代 | File System + Git | 共享仓库 |
| **测试环境** | 隔离性 | SQLite | 容器化 |
| **中型项目** | 平衡性能 | Hybrid | 文件系统 + PostgreSQL |
| **企业生产** | 高性能 | PostgreSQL | 集群部署 |
| **离线工具** | 便携性 | File System | 单机部署 |

### 2. 渐进式升级路径

```
第一阶段：原型验证
├── 文件系统存储
├── 本地开发
└── 基础功能验证

第二阶段：团队协作  
├── Git版本控制
├── 文件系统 + 缓存
└── CI/CD集成

第三阶段：生产准备
├── 混合存储模式
├── 数据库备份
└── 性能监控

第四阶段：企业级
├── PostgreSQL集群
├── 高可用部署
└── 完整运维体系
```

这个混合架构设计让您**鱼和熊掌兼得**：既有文件系统的简单性，又有数据库的强大功能，还能根据需求动态切换和迁移！ 