# PostgreSQL高级组件完全指南
## Aggregates、Collations、Domains、FTS系统详解

📅 **创建时间**: 2025年1月1日  
🎯 **目标**: 深入理解PostgreSQL高级组件及其在元类管理中的应用  
✅ **涵盖内容**: 聚合函数、排序规则、域类型、全文搜索系统  

---

## 📋 目录

1. [Aggregates (聚合函数)](#1-aggregates-聚合函数)
2. [Collations (排序规则)](#2-collations-排序规则)
3. [Domains (域类型)](#3-domains-域类型)
4. [FTS系统 (全文搜索)](#4-fts系统-全文搜索)
5. [综合应用示例](#5-综合应用示例)

---

## 🎯 1. Aggregates (聚合函数)

### 📊 概述
PostgreSQL的聚合函数允许您创建自定义的数据聚合操作，对多行数据进行计算并返回单个结果。

### 🏗️ 基础语法
```sql
CREATE AGGREGATE name (
    BASETYPE = input_data_type,
    SFUNC = state_transition_function,
    STYPE = state_data_type
    [, FINALFUNC = final_function]
    [, INITCOND = initial_condition]
    [, SORTOP = sort_operator]
);
```

### 💡 元类管理中的应用

#### 1.1 创建元类复杂度聚合函数
```sql
-- 状态转换函数：计算元类复杂度
CREATE OR REPLACE FUNCTION metaclass_complexity_sfunc(
    state INTEGER,
    props JSONB
) RETURNS INTEGER AS $$
BEGIN
    -- 基于属性数量、关系数量等计算复杂度
    RETURN state + 
           COALESCE((props->>'attribute_count')::INTEGER, 0) +
           COALESCE((props->>'relationship_count')::INTEGER, 0) * 2 +
           COALESCE((props->>'constraint_count')::INTEGER, 0) * 3;
END;
$$ LANGUAGE plpgsql;

-- 创建自定义聚合函数
CREATE AGGREGATE metaclass_complexity_avg(JSONB) (
    SFUNC = metaclass_complexity_sfunc,
    STYPE = INTEGER,
    INITCOND = '0',
    FINALFUNC = int4_avg_accum  -- 使用内置的平均值最终函数
);

-- 使用示例
SELECT 
    domain_name,
    metaclass_complexity_avg(properties) as avg_complexity
FROM core_schema.base_metaclass
GROUP BY domain_name;
```

#### 1.2 元类继承深度聚合
```sql
-- 继承深度状态函数
CREATE OR REPLACE FUNCTION inheritance_depth_sfunc(
    state INTEGER[],
    depth INTEGER
) RETURNS INTEGER[] AS $$
BEGIN
    -- state[1] = max_depth, state[2] = count, state[3] = sum
    RETURN ARRAY[
        GREATEST(state[1], depth),  -- 最大深度
        state[2] + 1,               -- 计数
        state[3] + depth            -- 深度总和
    ];
END;
$$ LANGUAGE plpgsql;

-- 最终函数：返回继承统计信息
CREATE OR REPLACE FUNCTION inheritance_stats_finalfunc(
    state INTEGER[]
) RETURNS JSONB AS $$
BEGIN
    RETURN jsonb_build_object(
        'max_depth', state[1],
        'count', state[2],
        'avg_depth', CASE WHEN state[2] > 0 THEN state[3]::FLOAT / state[2] ELSE 0 END
    );
END;
$$ LANGUAGE plpgsql;

-- 创建继承统计聚合函数
CREATE AGGREGATE inheritance_stats(INTEGER) (
    SFUNC = inheritance_depth_sfunc,
    STYPE = INTEGER[],
    INITCOND = '{0,0,0}',
    FINALFUNC = inheritance_stats_finalfunc
);

-- 使用示例
SELECT 
    metaclass_type,
    inheritance_stats(inheritance_depth) as stats
FROM core_schema.metaclass_hierarchy_view
GROUP BY metaclass_type;
```

#### 1.3 JSONB属性聚合器
```sql
-- 合并JSONB属性的聚合函数
CREATE OR REPLACE FUNCTION jsonb_merge_agg_sfunc(
    state JSONB,
    value JSONB
) RETURNS JSONB AS $$
BEGIN
    RETURN state || value;  -- JSONB合并操作符
END;
$$ LANGUAGE plpgsql;

CREATE AGGREGATE jsonb_merge_agg(JSONB) (
    SFUNC = jsonb_merge_agg_sfunc,
    STYPE = JSONB,
    INITCOND = '{}'
);

-- 聚合所有元类的属性
SELECT jsonb_merge_agg(properties) as all_properties
FROM core_schema.base_metaclass
WHERE domain_name = 'biomedicine';
```

### ✅ Aggregates优势
- **自定义计算**: 针对特定业务逻辑的数据聚合
- **性能优化**: 数据库层面的高效计算
- **类型安全**: 严格的类型检查
- **可组合性**: 可与其他SQL操作组合使用

---

## 🎯 2. Collations (排序规则)

### 📊 概述
Collations定义了字符串比较和排序的规则，支持不同语言和地区的排序需求。

### 🌍 创建自定义排序规则

#### 2.1 生物医学术语排序
```sql
-- 创建生物医学专用排序规则
CREATE COLLATION biomedical_sort (
    provider = icu,
    locale = 'en-US-u-kn-true'  -- 数字敏感排序
);

-- 元类名称自然排序
CREATE COLLATION metaclass_natural_sort (
    provider = icu,
    locale = 'en-US-u-kn-true-kf-upper'  -- 数字敏感 + 大小写不敏感
);

-- 使用示例
SELECT name
FROM core_schema.base_metaclass
ORDER BY name COLLATE metaclass_natural_sort;
```

#### 2.2 多语言元类支持
```sql
-- 中文元类排序
CREATE COLLATION chinese_metaclass_sort (
    provider = icu,
    locale = 'zh-CN-u-co-pinyin'  -- 拼音排序
);

-- 创建多语言元类表
CREATE TABLE core_schema.multilingual_metaclass (
    metaclass_id UUID PRIMARY KEY,
    name_en VARCHAR(255) COLLATE "C",
    name_zh VARCHAR(255) COLLATE chinese_metaclass_sort,
    name_default VARCHAR(255) COLLATE metaclass_natural_sort
);

-- 多语言查询示例
SELECT 
    name_en,
    name_zh
FROM core_schema.multilingual_metaclass
ORDER BY 
    CASE 
        WHEN current_setting('lc_collate') LIKE 'zh%' THEN name_zh
        ELSE name_en
    END COLLATE metaclass_natural_sort;
```

#### 2.3 版本号排序规则
```sql
-- 语义版本排序函数
CREATE OR REPLACE FUNCTION semantic_version_sort_key(version_string TEXT)
RETURNS TEXT AS $$
DECLARE
    parts TEXT[];
    result TEXT := '';
    part TEXT;
BEGIN
    -- 分割版本号 (例如: "2.1.0" -> ["2", "1", "0"])
    parts := string_to_array(version_string, '.');
    
    -- 将每部分补齐为4位数字
    FOREACH part IN ARRAY parts
    LOOP
        result := result || lpad(part, 4, '0');
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 创建版本排序索引
CREATE INDEX idx_metaclass_version_sort
ON core_schema.base_metaclass (semantic_version_sort_key(properties->>'version'));

-- 语义版本排序查询
SELECT name, properties->>'version' as version
FROM core_schema.base_metaclass
WHERE properties->>'version' IS NOT NULL
ORDER BY semantic_version_sort_key(properties->>'version');
```

### ✅ Collations优势
- **国际化支持**: 多语言环境下的正确排序
- **自然排序**: 数字敏感的自然排序
- **性能优化**: 索引友好的排序规则
- **业务特定**: 针对特定领域的排序需求

---

## 🎯 3. Domains (域类型)

### 📊 概述
Domains创建基于现有数据类型的自定义类型，包含约束检查和默认值。

### 🛡️ 元类管理中的域类型

#### 3.1 基础域类型定义
```sql
-- 元类标识符域
CREATE DOMAIN core_schema.metaclass_identifier AS VARCHAR(255)
    CHECK (VALUE ~ '^[A-Z][a-zA-Z0-9_]*$')  -- 必须以大写字母开头
    NOT NULL;

-- 限定名称域
CREATE DOMAIN core_schema.qualified_name AS VARCHAR(500)
    CHECK (VALUE ~ '^[a-zA-Z][a-zA-Z0-9_.]*::[a-zA-Z][a-zA-Z0-9_]*$')
    NOT NULL;

-- URI域
CREATE DOMAIN core_schema.namespace_uri AS TEXT
    CHECK (VALUE ~ '^https?://[a-zA-Z0-9.-]+(/[a-zA-Z0-9._~-]*)*/?$')
    DEFAULT 'http://www.example.com/metamodel';

-- 版本号域
CREATE DOMAIN core_schema.semantic_version AS VARCHAR(50)
    CHECK (VALUE ~ '^\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?(\+[a-zA-Z0-9.-]+)?$')
    DEFAULT '1.0.0';

-- 使用域类型的表
CREATE TABLE core_schema.strict_metaclass (
    metaclass_id UUID PRIMARY KEY,
    name core_schema.metaclass_identifier,
    qualified_name core_schema.qualified_name,
    namespace_uri core_schema.namespace_uri,
    version core_schema.semantic_version,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2 复合域类型
```sql
-- 坐标域类型
CREATE DOMAIN core_schema.coordinate AS NUMERIC(10,6)
    CHECK (VALUE >= -180.0 AND VALUE <= 180.0);

-- 百分比域类型
CREATE DOMAIN core_schema.percentage AS NUMERIC(5,2)
    CHECK (VALUE >= 0.0 AND VALUE <= 100.0);

-- 生物医学实体ID域
CREATE DOMAIN core_schema.bio_entity_id AS VARCHAR(100)
    CHECK (VALUE ~ '^(PATIENT|DRUG|GENE|PROTEIN|DISEASE)_[A-Z0-9]{8,}$')
    NOT NULL;

-- 基因表达值域
CREATE DOMAIN core_schema.expression_level AS NUMERIC(12,6)
    CHECK (VALUE >= 0.0);

-- 概率值域
CREATE DOMAIN core_schema.probability AS NUMERIC(10,9)
    CHECK (VALUE >= 0.0 AND VALUE <= 1.0);

-- 使用生物医学域类型的表
CREATE TABLE core_schema.gene_expression_data (
    sample_id core_schema.bio_entity_id,
    gene_id core_schema.bio_entity_id,
    expression_value core_schema.expression_level,
    p_value core_schema.probability,
    fold_change NUMERIC(10,6),
    coordinates_x core_schema.coordinate,
    coordinates_y core_schema.coordinate,
    confidence core_schema.percentage
);
```

#### 3.3 业务规则域
```sql
-- 元类状态域
CREATE DOMAIN core_schema.metaclass_lifecycle_state AS VARCHAR(20)
    CHECK (VALUE IN ('draft', 'review', 'approved', 'deprecated', 'obsolete'))
    DEFAULT 'draft';

-- 优先级域
CREATE DOMAIN core_schema.priority_level AS INTEGER
    CHECK (VALUE >= 1 AND VALUE <= 10)
    DEFAULT 5;

-- 元类复杂度域
CREATE DOMAIN core_schema.complexity_score AS INTEGER
    CHECK (VALUE >= 0 AND VALUE <= 1000)
    DEFAULT 0;

-- 可见性域
CREATE DOMAIN core_schema.visibility_level AS VARCHAR(20)
    CHECK (VALUE IN ('public', 'protected', 'package', 'private'))
    DEFAULT 'public';

-- 使用业务域的元类表
CREATE TABLE core_schema.business_metaclass (
    metaclass_id UUID PRIMARY KEY,
    name core_schema.metaclass_identifier,
    state core_schema.metaclass_lifecycle_state,
    priority core_schema.priority_level,
    complexity core_schema.complexity_score,
    visibility core_schema.visibility_level,
    
    -- 域约束的组合使用
    CONSTRAINT valid_priority_complexity 
        CHECK ((priority <= 3 AND complexity >= 100) OR (priority > 3))
);
```

#### 3.4 域类型的元数据查询
```sql
-- 查询所有自定义域类型
CREATE OR REPLACE FUNCTION core_schema.get_domain_info()
RETURNS TABLE(
    domain_name TEXT,
    base_type TEXT,
    constraint_def TEXT,
    default_value TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.typname::TEXT as domain_name,
        format_type(t.typbasetype, t.typtypmod) as base_type,
        pg_get_constraintdef(c.oid) as constraint_def,
        t.typdefault as default_value
    FROM pg_type t
    LEFT JOIN pg_constraint c ON c.contypid = t.oid
    WHERE t.typtype = 'd'  -- 'd' 表示域类型
    AND t.typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'core_schema')
    ORDER BY t.typname;
END;
$$ LANGUAGE plpgsql;

-- 使用示例
SELECT * FROM core_schema.get_domain_info();
```

### ✅ Domains优势
- **数据完整性**: 内置约束检查确保数据质量
- **代码复用**: 一次定义，多处使用
- **业务语义**: 清晰表达业务含义
- **维护简化**: 集中管理数据规则

---

## 🎯 4. FTS系统 (全文搜索)

### 📊 系统概述
PostgreSQL的全文搜索(FTS)系统包含四个核心组件：
- **Parsers**: 文本解析器
- **Dictionaries**: 词典  
- **Templates**: 模板
- **Configurations**: 配置

### 🔍 4.1 FTS Parsers (解析器)

#### 自定义生物医学解析器
```sql
-- 查看可用解析器
SELECT * FROM pg_ts_parser;

-- 创建生物医学词汇解析器配置
CREATE TEXT SEARCH CONFIGURATION biomedical (
    PARSER = default
);

-- 为生物医学术语添加特殊处理
ALTER TEXT SEARCH CONFIGURATION biomedical
    ALTER MAPPING FOR asciiword, word, numword
    WITH english_stem, simple;

-- 添加基因符号处理
ALTER TEXT SEARCH CONFIGURATION biomedical
    ALTER MAPPING FOR hword_asciipart
    WITH simple;  -- 不进行词干提取，保持原样
```

#### 自定义解析器函数
```sql
-- 创建UML关键词解析器
CREATE OR REPLACE FUNCTION uml_keyword_parser(text)
RETURNS tsvector AS $$
DECLARE
    input_text ALIAS FOR $1;
    result tsvector;
    uml_keywords TEXT[] := ARRAY[
        'class', 'interface', 'abstract', 'extends', 'implements',
        'association', 'aggregation', 'composition', 'dependency',
        'stereotype', 'profile', 'metaclass', 'attribute', 'operation'
    ];
    keyword TEXT;
BEGIN
    result := to_tsvector('simple', input_text);
    
    -- 为UML关键词添加额外权重
    FOREACH keyword IN ARRAY uml_keywords
    LOOP
        IF input_text ILIKE '%' || keyword || '%' THEN
            result := result || to_tsvector('simple', keyword || ':A');  -- 高权重
        END IF;
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;
```

### 📚 4.2 FTS Dictionaries (词典)

#### 生物医学术语词典
```sql
-- 创建生物医学同义词词典
CREATE TEXT SEARCH DICTIONARY biomedical_synonyms (
    TEMPLATE = synonym,
    SYNONYMS = biomedical_synonyms
);

-- 同义词文件内容 (需要放在data目录的tsearch_data子目录)
-- biomedical_synonyms.syn:
-- dna      deoxyribonucleic_acid
-- rna      ribonucleic_acid
-- protein  polypeptide
-- gene     genetic_element
-- drug     medication,pharmaceutical
-- patient  subject,individual

-- 创建生物医学停用词词典
CREATE TEXT SEARCH DICTIONARY biomedical_stopwords (
    TEMPLATE = simple,
    STOPWORDS = biomedical_stopwords
);

-- 停用词文件内容:
-- biomedical_stopwords.stop:
-- the, and, or, but, in, on, at, to, for, of, with, by
-- study, research, analysis, method, approach, technique
-- data, information, result, conclusion, finding

-- 创建基因命名词典
CREATE TEXT SEARCH DICTIONARY gene_naming (
    TEMPLATE = simple,
    STOPWORDS = gene_stopwords
);
```

#### 智能词典处理
```sql
-- 创建复合词典处理函数
CREATE OR REPLACE FUNCTION intelligent_biomedical_dict(
    tokens TEXT[]
) RETURNS TEXT[] AS $$
DECLARE
    processed_tokens TEXT[] := '{}';
    token TEXT;
    processed_token TEXT;
BEGIN
    FOREACH token IN ARRAY tokens
    LOOP
        -- 基因符号处理 (如 BRCA1, TP53)
        IF token ~ '^[A-Z]{2,10}[0-9]*$' THEN
            processed_token := 'gene_' || lower(token);
            
        -- 蛋白质处理 (如 p53, HER2)
        ELSIF token ~ '^[a-z]*[A-Z]+[0-9]*$' THEN
            processed_token := 'protein_' || lower(token);
            
        -- 药物编号处理 (如 DB00001)
        ELSIF token ~ '^DB[0-9]{5}$' THEN
            processed_token := 'drug_' || lower(token);
            
        -- 疾病编码处理 (如 ICD10: C50.9)
        ELSIF token ~ '^[A-Z][0-9]{2}\.[0-9]$' THEN
            processed_token := 'disease_' || replace(lower(token), '.', '_');
            
        ELSE
            processed_token := lower(token);
        END IF;
        
        processed_tokens := array_append(processed_tokens, processed_token);
    END LOOP;
    
    RETURN processed_tokens;
END;
$$ LANGUAGE plpgsql;
```

### 🎨 4.3 FTS Templates (模板)

#### 自定义搜索模板
```sql
-- 查看可用模板
SELECT * FROM pg_ts_template;

-- 创建生物医学搜索模板
CREATE TEXT SEARCH TEMPLATE biomedical_template (
    INIT = prsd_start,
    LEXIZE = biomedical_lexize,
    END = prsd_end,
    LEXTYPES = prsd_lextype
);

-- 生物医学词汇化函数
CREATE OR REPLACE FUNCTION biomedical_lexize(
    internal, internal, internal, internal, internal
) RETURNS internal AS $$
    -- 这里是C语言实现的占位符
    -- 实际应用中需要C扩展实现
    SELECT NULL::internal;
$$ LANGUAGE sql;
```

### ⚙️ 4.4 FTS Configurations (配置)

#### 完整的生物医学搜索配置
```sql
-- 创建综合生物医学搜索配置
CREATE TEXT SEARCH CONFIGURATION biomedical_search (
    PARSER = default
);

-- 配置不同类型词汇的处理
ALTER TEXT SEARCH CONFIGURATION biomedical_search
    ALTER MAPPING FOR asciiword  -- 英文单词
    WITH biomedical_synonyms, english_stem, simple;

ALTER TEXT SEARCH CONFIGURATION biomedical_search
    ALTER MAPPING FOR word       -- 其他语言单词
    WITH biomedical_synonyms, simple;

ALTER TEXT SEARCH CONFIGURATION biomedical_search
    ALTER MAPPING FOR numword    -- 数字词汇
    WITH simple;

ALTER TEXT SEARCH CONFIGURATION biomedical_search
    ALTER MAPPING FOR email      -- 邮箱地址
    WITH simple;

ALTER TEXT SEARCH CONFIGURATION biomedical_search
    ALTER MAPPING FOR url        -- URL地址
    WITH simple;

ALTER TEXT SEARCH CONFIGURATION biomedical_search
    ALTER MAPPING FOR host       -- 主机名
    WITH simple;

-- 设置停用词
ALTER TEXT SEARCH CONFIGURATION biomedical_search
    DROP MAPPING FOR biomedical_stopwords;
```

#### 元类搜索配置应用
```sql
-- 为元类表添加搜索向量
ALTER TABLE core_schema.base_metaclass 
ADD COLUMN search_vector_bio tsvector 
GENERATED ALWAYS AS (
    to_tsvector('biomedical_search', 
        coalesce(name, '') || ' ' || 
        coalesce(documentation, '') || ' ' ||
        coalesce(properties::text, '')
    )
) STORED;

-- 创建搜索索引
CREATE INDEX idx_metaclass_bio_search 
ON core_schema.base_metaclass 
USING GIN(search_vector_bio);

-- 高级搜索函数
CREATE OR REPLACE FUNCTION core_schema.advanced_search_metaclasses(
    search_query TEXT,
    domain_filter TEXT DEFAULT NULL,
    min_rank REAL DEFAULT 0.1
) RETURNS TABLE(
    metaclass_id UUID,
    name VARCHAR(255),
    rank REAL,
    headline TEXT,
    matched_terms TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.metaclass_id,
        m.name,
        ts_rank_cd(m.search_vector_bio, query) as rank,
        ts_headline('biomedical_search', m.documentation, query) as headline,
        ARRAY(
            SELECT DISTINCT unnest(
                ts_lexize('biomedical_search', search_query)
            )
        ) as matched_terms
    FROM 
        core_schema.base_metaclass m,
        plainto_tsquery('biomedical_search', search_query) query
    WHERE 
        m.search_vector_bio @@ query
        AND ts_rank_cd(m.search_vector_bio, query) >= min_rank
        AND (domain_filter IS NULL OR m.properties->>'domain' = domain_filter)
    ORDER BY rank DESC;
END;
$$ LANGUAGE plpgsql;

-- 使用示例
SELECT * FROM core_schema.advanced_search_metaclasses(
    'gene expression protein cancer',
    'biomedicine',
    0.1
);
```

### 🔍 FTS性能优化
```sql
-- 创建部分索引
CREATE INDEX idx_metaclass_active_search
ON core_schema.base_metaclass USING GIN(search_vector_bio)
WHERE NOT is_abstract AND properties->>'status' = 'active';

-- 统计搜索性能
CREATE TABLE core_schema.search_performance_log (
    search_id UUID DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    execution_time INTERVAL,
    result_count INTEGER,
    searched_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 搜索性能监控函数
CREATE OR REPLACE FUNCTION core_schema.monitored_search(
    search_text TEXT
) RETURNS TABLE(
    metaclass_id UUID,
    name VARCHAR(255),
    rank REAL
) AS $$
DECLARE
    start_time TIMESTAMP := clock_timestamp();
    end_time TIMESTAMP;
    result_count INTEGER;
BEGIN
    -- 执行搜索
    RETURN QUERY
    SELECT m.metaclass_id, m.name, ts_rank(m.search_vector_bio, query) as rank
    FROM core_schema.base_metaclass m,
         plainto_tsquery('biomedical_search', search_text) query
    WHERE m.search_vector_bio @@ query
    ORDER BY rank DESC;
    
    -- 记录性能
    GET DIAGNOSTICS result_count = ROW_COUNT;
    end_time := clock_timestamp();
    
    INSERT INTO core_schema.search_performance_log 
    (query_text, execution_time, result_count)
    VALUES (search_text, end_time - start_time, result_count);
END;
$$ LANGUAGE plpgsql;
```

### ✅ FTS系统优势
- **灵活配置**: 根据业务需求自定义搜索行为
- **多语言支持**: 支持多种语言的文本搜索
- **高性能**: GIN索引提供快速搜索
- **语义理解**: 词干提取和同义词支持
- **业务特化**: 针对特定领域的搜索优化

---

## 🚀 5. 综合应用示例

### 完整的生物医学元类搜索系统
```sql
-- 创建综合示例表
CREATE TABLE core_schema.comprehensive_biomedical_metaclass (
    -- 使用域类型
    metaclass_id UUID PRIMARY KEY,
    name core_schema.metaclass_identifier,
    qualified_name core_schema.qualified_name,
    version core_schema.semantic_version,
    state core_schema.metaclass_lifecycle_state,
    complexity core_schema.complexity_score,
    
    -- 标准字段
    documentation TEXT,
    properties JSONB,
    tags TEXT[] DEFAULT '{}',
    
    -- 多语言支持
    name_zh VARCHAR(255) COLLATE chinese_metaclass_sort,
    documentation_zh TEXT,
    
    -- 搜索向量
    search_vector_en tsvector GENERATED ALWAYS AS (
        to_tsvector('english', coalesce(name::text, '') || ' ' || coalesce(documentation, ''))
    ) STORED,
    
    search_vector_bio tsvector GENERATED ALWAYS AS (
        to_tsvector('biomedical_search', coalesce(name::text, '') || ' ' || coalesce(documentation, ''))
    ) STORED,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建多语言索引
CREATE INDEX idx_comprehensive_name_zh 
ON core_schema.comprehensive_biomedical_metaclass (name_zh);

CREATE INDEX idx_comprehensive_search_en 
ON core_schema.comprehensive_biomedical_metaclass USING GIN(search_vector_en);

CREATE INDEX idx_comprehensive_search_bio 
ON core_schema.comprehensive_biomedical_metaclass USING GIN(search_vector_bio);

-- 智能搜索API
CREATE OR REPLACE FUNCTION core_schema.intelligent_metaclass_search(
    search_terms TEXT,
    language_code VARCHAR(10) DEFAULT 'en',
    use_biomedical_config BOOLEAN DEFAULT TRUE,
    include_stats BOOLEAN DEFAULT FALSE
) RETURNS JSONB AS $$
DECLARE
    search_config TEXT;
    results JSONB;
    stats JSONB;
BEGIN
    -- 确定搜索配置
    search_config := CASE 
        WHEN use_biomedical_config THEN 'biomedical_search'
        ELSE language_code 
    END;
    
    -- 执行搜索
    WITH search_results AS (
        SELECT 
            m.metaclass_id,
            m.name,
            m.qualified_name,
            m.complexity,
            m.state,
            ts_rank_cd(
                CASE WHEN use_biomedical_config 
                     THEN m.search_vector_bio 
                     ELSE m.search_vector_en END,
                query
            ) as relevance,
            ts_headline(
                search_config,
                coalesce(m.documentation, m.name::text),
                query,
                'MaxWords=20, MinWords=5'
            ) as snippet
        FROM 
            core_schema.comprehensive_biomedical_metaclass m,
            plainto_tsquery(search_config, search_terms) query
        WHERE 
            (CASE WHEN use_biomedical_config 
                  THEN m.search_vector_bio 
                  ELSE m.search_vector_en END) @@ query
        ORDER BY relevance DESC
        LIMIT 50
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'id', metaclass_id,
            'name', name,
            'qualified_name', qualified_name,
            'complexity', complexity,
            'state', state,
            'relevance', relevance,
            'snippet', snippet
        )
    ) INTO results
    FROM search_results;
    
    -- 计算统计信息
    IF include_stats THEN
        WITH search_stats AS (
            SELECT 
                COUNT(*) as total_results,
                AVG(complexity) as avg_complexity,
                metaclass_complexity_avg(
                    jsonb_build_object('complexity', complexity)
                ) as complexity_distribution
            FROM core_schema.comprehensive_biomedical_metaclass m
            WHERE m.search_vector_bio @@ plainto_tsquery(search_config, search_terms)
        )
        SELECT jsonb_build_object(
            'total_results', total_results,
            'avg_complexity', avg_complexity,
            'complexity_distribution', complexity_distribution
        ) INTO stats
        FROM search_stats;
        
        results := jsonb_build_object(
            'results', results,
            'statistics', stats,
            'search_config', search_config,
            'language', language_code
        );
    END IF;
    
    RETURN COALESCE(results, '{"results": [], "message": "No results found"}'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- 使用示例
SELECT core_schema.intelligent_metaclass_search(
    'gene expression cancer treatment',
    'en',
    true,
    true
);
```

## 📊 总结

PostgreSQL的高级组件为UML元类管理系统提供了强大的功能：

### 🎯 核心优势
- **Aggregates**: 自定义数据聚合，深度分析元类特征
- **Collations**: 多语言排序，国际化支持
- **Domains**: 数据约束，确保业务规则一致性
- **FTS系统**: 智能搜索，语义理解能力

### 🚀 实际应用价值
- **数据质量**: 域约束确保数据完整性
- **搜索能力**: FTS提供强大的语义搜索
- **国际化**: Collations支持多语言环境
- **分析能力**: 自定义聚合函数提供深度分析

**通过这些高级组件，我们构建了一个真正企业级的、功能完整的UML元类管理系统！** 🎉 