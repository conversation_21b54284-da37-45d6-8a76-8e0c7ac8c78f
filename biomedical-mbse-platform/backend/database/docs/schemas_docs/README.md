# SQL Schemas 数据库架构

UML混合策略数据库映射系统的SQL Schema定义和管理。

## 📁 Schema文件结构

```
schemas/
├── 00_core_schema.sql       # 核心基础Schema (29KB, 627行)
├── 01_security_schema.sql   # 安全领域Schema (29KB, 644行)
└── README.md               # Schema使用指南 (5.5KB, 206行)
```

## 🎯 核心概念

### Element抽象架构
系统采用"万物皆Element"的设计理念：
- **统一元数据**: 所有业务实体都继承Element抽象
- **动态类型**: 支持运行时Element类型定义
- **跨域关系**: 统一的跨领域关系管理
- **版本控制**: Schema和数据的版本管理

### 混合部署策略
支持静态Schema和动态Schema的混合部署：
- **静态Schema**: 预定义的核心基础设施
- **动态Schema**: 运行时生成的业务领域
- **模板Schema**: 可复用的领域模板

## 📋 Schema详解

### 1. 🏗️ 核心基础Schema (`00_core_schema.sql`)

**部署优先级**: ⭐⭐⭐⭐⭐ **必须部署**

**核心表结构**:
```sql
-- Element统一元数据表
element_metadata (
    element_id UUID PRIMARY KEY,
    element_type TEXT NOT NULL,
    domain_id TEXT NOT NULL,
    qualified_name TEXT,
    properties JSONB,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 领域注册表
domain_registry (
    domain_id TEXT PRIMARY KEY,
    domain_name TEXT NOT NULL,
    domain_type TEXT NOT NULL,
    configuration JSONB,
    status TEXT DEFAULT 'active'
);

-- Element类型定义
element_type_definitions (
    type_id TEXT PRIMARY KEY,
    type_name TEXT NOT NULL,
    domain_id TEXT NOT NULL,
    field_definitions JSONB,
    constraints JSONB,
    indexes JSONB
);

-- 跨域关系管理
cross_domain_relationships (
    relationship_id UUID PRIMARY KEY,
    source_element_id UUID NOT NULL,
    target_element_id UUID NOT NULL,
    relationship_type TEXT NOT NULL,
    properties JSONB
);
```

**关键特性**:
- **统一ID管理**: UUID主键，支持分布式
- **JSONB存储**: 灵活的属性和配置存储
- **跨域索引**: 高性能的跨领域查询
- **审计支持**: 完整的时间戳和变更追踪

**部署命令**:
```bash
psql -d biomedical_mbse_platform -f 00_core_schema.sql
```

### 2. 🔒 安全领域Schema (`01_security_schema.sql`)

**部署优先级**: ⭐⭐⭐ **推荐部署** (示例模板)

**主要表结构**:
```sql
-- 用户管理
security_users (
    element_id UUID PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    profile_data JSONB
);

-- 角色管理
security_roles (
    element_id UUID PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    is_system BOOLEAN DEFAULT false
);

-- 权限管理
security_permissions (
    element_id UUID PRIMARY KEY,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    resource_type TEXT NOT NULL,
    action_type TEXT NOT NULL,
    description TEXT
);

-- 用户角色关联
security_user_roles (
    relationship_id UUID PRIMARY KEY,
    user_element_id UUID NOT NULL,
    role_element_id UUID NOT NULL,
    granted_by UUID,
    granted_at TIMESTAMP DEFAULT now()
);
```

**安全特性**:
- **RBAC模型**: 基于角色的访问控制
- **会话管理**: JWT Token和会话追踪
- **审计日志**: 完整的安全操作审计
- **密码策略**: 强密码和加密存储

**索引优化**:
```sql
-- 用户名和邮箱唯一索引
CREATE UNIQUE INDEX idx_security_users_username ON security_users(username);
CREATE UNIQUE INDEX idx_security_users_email ON security_users(email);

-- JSONB字段的GIN索引
CREATE INDEX idx_security_users_profile_gin ON security_users USING gin(profile_data);
CREATE INDEX idx_security_roles_permissions_gin ON security_roles USING gin(permissions);

-- 关系查询优化索引
CREATE INDEX idx_user_roles_user ON security_user_roles(user_element_id);
CREATE INDEX idx_user_roles_role ON security_user_roles(role_element_id);
```

## 🚀 部署策略

### 方案A: 完全动态架构 (推荐生产环境)
```bash
# 1. 部署核心基础设施
psql -d biomedical_mbse_platform -f 00_core_schema.sql

# 2. 通过代码动态创建业务领域
python << EOF
import asyncio
from domain_managers import DomainFactory, SecurityDomainManager

async def setup_dynamic_domains():
    # 创建安全领域
    security_manager = SecurityDomainManager(db_pool)
    await security_manager.ensure_security_domain_exists()
    
    # 创建其他业务领域
    domain_factory = DomainFactory(db_pool)
    await domain_factory.create_uml_modeling_domain()
    await domain_factory.create_biomedical_research_domain()

asyncio.run(setup_dynamic_domains())
EOF
```

**优势**:
- ✅ 完全的运行时灵活性
- ✅ 支持在线Schema演进
- ✅ 自动的跨域关系管理
- ✅ 内置的版本控制和回滚

### 方案B: 混合部署 (推荐开发环境)
```bash
# 1. 部署核心Schema
psql -d biomedical_mbse_platform -f 00_core_schema.sql

# 2. 部署安全Schema模板 (快速开发)
psql -d biomedical_mbse_platform -f 01_security_schema.sql

# 3. 动态创建其他领域
python -c "
from domain_managers import DomainFactory
factory = DomainFactory(db_pool)
await factory.create_research_domain()
await factory.create_analytics_domain()
"
```

**优势**:
- ✅ 快速开发和原型
- ✅ 稳定的安全基础设施
- ✅ 部分动态能力

### 方案C: 静态部署 (不推荐)
```bash
# 部署所有预定义Schema
psql -d biomedical_mbse_platform -f 00_core_schema.sql
psql -d biomedical_mbse_platform -f 01_security_schema.sql
# 其他静态Schema文件...
```

**限制**:
- ❌ 缺乏运行时灵活性
- ❌ Schema变更需要停机
- ❌ 跨域关系难以管理

## 🔧 开发指南

### 创建新的领域Schema

#### 方法1: 动态创建 (推荐)
```python
from domain_managers import DomainFactory

# 定义研究管理领域
research_domain_template = {
    'domain_id': 'research_management',
    'domain_name': '科研管理领域',
    'element_types': {
        'research_project': {
            'fields': {
                'project_name': {'type': 'VARCHAR(200)', 'required': True},
                'description': {'type': 'TEXT'},
                'budget': {'type': 'DECIMAL(15,2)'},
                'start_date': {'type': 'DATE'},
                'end_date': {'type': 'DATE'},
                'metadata': {'type': 'JSONB'}
            },
            'indexes': ['project_name', 'start_date', 'metadata'],
            'constraints': ['CHECK (start_date <= end_date)']
        },
        'research_participant': {
            'fields': {
                'participant_id': {'type': 'VARCHAR(50)', 'required': True},
                'personal_info': {'type': 'JSONB', 'encrypted': True},
                'consent_status': {'type': 'TEXT'},
                'enrollment_date': {'type': 'DATE'}
            }
        }
    },
    'relationships': {
        'project_participants': {
            'from': 'research_project',
            'to': 'research_participant',
            'type': 'many_to_many',
            'properties': ['role', 'status', 'participation_period']
        }
    }
}

# 创建领域
domain_factory = DomainFactory(db_pool)
await domain_factory.create_domain_from_template(research_domain_template)
```

#### 方法2: SQL模板 (适合复杂场景)
```sql
-- 02_research_management_schema.sql
-- 研究管理领域Schema模板

-- 遵循Element抽象原则
CREATE TABLE research_projects (
    element_id UUID PRIMARY KEY,
    project_name VARCHAR(200) NOT NULL,
    description TEXT,
    budget DECIMAL(15,2),
    start_date DATE,
    end_date DATE,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    FOREIGN KEY (element_id) REFERENCES core_schema.element_metadata(element_id),
    CHECK (start_date <= end_date)
);

-- 创建必要的索引
CREATE INDEX idx_research_projects_name ON research_projects(project_name);
CREATE INDEX idx_research_projects_dates ON research_projects(start_date, end_date);
CREATE INDEX idx_research_projects_metadata_gin ON research_projects USING gin(metadata);

-- 自动更新时间戳触发器
CREATE TRIGGER update_research_projects_timestamp
    BEFORE UPDATE ON research_projects
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_timestamp();
```

### Element设计最佳实践

#### 1. 表结构设计
```sql
-- 标准Element表结构模板
CREATE TABLE domain_entity_name (
    -- 必需字段
    element_id UUID PRIMARY KEY,
    
    -- 业务字段
    business_field_1 data_type constraints,
    business_field_2 data_type constraints,
    
    -- 可选扩展字段
    properties JSONB,
    metadata JSONB,
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    created_by UUID,
    updated_by UUID,
    
    -- 外键约束
    FOREIGN KEY (element_id) REFERENCES core_schema.element_metadata(element_id),
    FOREIGN KEY (created_by) REFERENCES security_schema.security_users(element_id),
    FOREIGN KEY (updated_by) REFERENCES security_schema.security_users(element_id)
);
```

#### 2. 索引策略
```sql
-- 主键索引 (自动创建)
-- 业务查询索引
CREATE INDEX idx_entity_business_field ON domain_entity_name(business_field_1);

-- 复合索引
CREATE INDEX idx_entity_multi_field ON domain_entity_name(field1, field2, field3);

-- JSONB字段索引
CREATE INDEX idx_entity_properties_gin ON domain_entity_name USING gin(properties);
CREATE INDEX idx_entity_specific_property ON domain_entity_name USING gin((properties->>'specific_key'));

-- 部分索引 (条件索引)
CREATE INDEX idx_entity_active ON domain_entity_name(field1) WHERE status = 'active';
```

#### 3. 关系管理
```sql
-- 多对多关系表模板
CREATE TABLE entity1_entity2_relationships (
    relationship_id UUID PRIMARY KEY,
    entity1_id UUID NOT NULL,
    entity2_id UUID NOT NULL,
    relationship_type TEXT NOT NULL,
    properties JSONB,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT now(),
    created_by UUID,
    
    FOREIGN KEY (entity1_id) REFERENCES domain1.entity1(element_id),
    FOREIGN KEY (entity2_id) REFERENCES domain2.entity2(element_id),
    FOREIGN KEY (created_by) REFERENCES security_schema.security_users(element_id),
    
    UNIQUE(entity1_id, entity2_id, relationship_type)
);
```

## 📈 Schema演进和迁移

### 在线Schema变更
```python
# 动态添加新字段
await domain_factory.add_field_to_element_type(
    domain_id='research_management',
    element_type='research_project',
    field_definition={
        'field_name': 'external_funding',
        'field_type': 'DECIMAL(15,2)',
        'default_value': 0.00,
        'nullable': True
    }
)

# 创建新的Element类型
await domain_factory.add_element_type_to_domain(
    domain_id='research_management',
    element_type_definition={
        'type_id': 'research_milestone',
        'fields': {
            'milestone_name': {'type': 'VARCHAR(200)', 'required': True},
            'target_date': {'type': 'DATE'},
            'completion_status': {'type': 'TEXT'},
            'completion_date': {'type': 'DATE'}
        }
    }
)
```

### 版本控制和回滚
```python
# 创建Schema快照
snapshot_id = await domain_factory.create_schema_snapshot('research_management')

# 应用变更
await domain_factory.apply_schema_changes(changes)

# 如果需要回滚
await domain_factory.rollback_to_snapshot(snapshot_id)
```

### 数据迁移
```python
# 复杂数据迁移示例
migration_plan = {
    'version': '2.0.0',
    'description': '研究项目字段重构',
    'operations': [
        {
            'type': 'add_column',
            'table': 'research_projects',
            'column': 'project_phase',
            'definition': 'TEXT DEFAULT \'planning\''
        },
        {
            'type': 'data_transform',
            'sql': '''
                UPDATE research_projects 
                SET project_phase = CASE 
                    WHEN start_date > now() THEN 'planning'
                    WHEN end_date < now() THEN 'completed'
                    ELSE 'active'
                END
            '''
        },
        {
            'type': 'drop_column',
            'table': 'research_projects',
            'column': 'legacy_status'
        }
    ]
}

await domain_factory.execute_migration(migration_plan)
```

## 🛡️ 安全考虑

### 权限分离
```sql
-- 为不同Schema创建专门的角色
CREATE ROLE biomedical_core_user;
CREATE ROLE biomedical_security_admin;
CREATE ROLE biomedical_research_user;

-- 分配最小必要权限
GRANT SELECT, INSERT, UPDATE ON core_schema.element_metadata TO biomedical_core_user;
GRANT ALL PRIVILEGES ON security_schema.* TO biomedical_security_admin;
GRANT SELECT, INSERT, UPDATE ON research_schema.* TO biomedical_research_user;
```

### 行级安全 (RLS)
```sql
-- 启用行级安全
ALTER TABLE research_projects ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
CREATE POLICY research_project_access ON research_projects
FOR ALL TO biomedical_research_user
USING (
    created_by = current_setting('app.current_user_id')::UUID OR
    EXISTS (
        SELECT 1 FROM project_team_members 
        WHERE project_id = element_id 
        AND user_id = current_setting('app.current_user_id')::UUID
        AND status = 'active'
    )
);
```

### 敏感数据处理
```python
# 敏感字段加密
sensitive_config = {
    'encryption_fields': [
        'research_participants.personal_info',
        'research_projects.confidential_data'
    ],
    'encryption_algorithm': 'AES-256-GCM',
    'key_rotation_days': 90
}

await domain_factory.apply_encryption_config(sensitive_config)
```

## 🔍 监控和维护

### 性能监控
```sql
-- 查询性能统计
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del
FROM pg_stat_user_tables
WHERE schemaname IN ('core_schema', 'security_schema', 'research_schema')
ORDER BY seq_scan DESC;

-- 索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE schemaname IN ('core_schema', 'security_schema', 'research_schema')
ORDER BY idx_scan DESC;
```

### 健康检查
```python
# 自动健康检查
health_report = await domain_factory.get_comprehensive_health_report()

print(f"总体状态: {health_report['overall_status']}")
print(f"域数量: {health_report['domain_count']}")
print(f"活跃连接: {health_report['active_connections']}")
print(f"存储使用: {health_report['storage_usage']}")

# 性能建议
for recommendation in health_report['recommendations']:
    print(f"建议: {recommendation['type']} - {recommendation['description']}")
```

### 维护任务
```python
# 定期维护任务
maintenance_tasks = [
    'vacuum_analyze_tables',
    'reindex_fragmented_indexes',
    'update_table_statistics',
    'cleanup_old_audit_logs',
    'optimize_jsonb_indexes'
]

for task in maintenance_tasks:
    result = await domain_factory.execute_maintenance_task(task)
    print(f"维护任务 {task}: {result['status']}")
```

## 📊 最佳实践总结

### ✅ 推荐做法
1. **优先使用动态Schema**: 提供最大的灵活性
2. **遵循Element抽象**: 保持架构一致性
3. **合理设计索引**: 平衡查询性能和存储成本
4. **实施安全策略**: 使用RLS和权限分离
5. **定期性能监控**: 及时发现和解决性能问题

### ❌ 避免的做法
1. **过度使用静态Schema**: 限制系统灵活性
2. **忽略索引策略**: 导致查询性能问题
3. **缺乏安全考虑**: 数据泄露风险
4. **忽视维护任务**: 系统性能逐渐下降
5. **缺乏监控**: 问题发现滞后

---

**🗃️ SQL Schemas是UML混合策略系统的数据基础，提供了灵活、安全、高性能的数据存储架构！** 