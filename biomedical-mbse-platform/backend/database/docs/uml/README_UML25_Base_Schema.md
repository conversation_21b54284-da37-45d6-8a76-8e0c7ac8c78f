# UML2.5基础Schema系统使用指南
## 基于OMG标准XMI的动态元类管理架构

📅 **创建时间**: 2025年1月1日  
🎯 **目标**: 基于UML2.5标准构建统一的元类管理基础，支持domain扩展  
✅ **状态**: 完整实现，可投入生产使用  

---

## 📋 目录

1. [系统概述](#系统概述)
2. [核心组件](#核心组件)
3. [快速开始](#快速开始)
4. [架构设计](#架构设计)
5. [扩展机制](#扩展机制)
6. [使用示例](#使用示例)
7. [API参考](#api参考)

---

## 🎯 系统概述

UML2.5基础Schema系统是一个基于OMG UML2.5标准的元类管理架构，通过解析标准XMI文件自动生成PostgreSQL数据库Schema，并提供强大的domain扩展机制。

### 核心价值

- **标准化基础**: 严格遵循OMG UML2.5标准，确保兼容性
- **动态生成**: 从标准XMI文件自动生成数据库结构  
- **扩展机制**: 支持domain-specific的元类扩展
- **类型安全**: PostgreSQL强类型约束保证数据完整性
- **高性能**: 完整的索引和查询优化

### 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    UML2.5基础Schema系统                     │
├─────────────────────────────────────────────────────────────┤
│  📁 资源层 (Resources)                                      │
│    └── UML2.5.xmi (OMG标准元模型定义)                      │
│                                                             │
│  🔧 解析层 (@core.parsing)                                 │
│    └── UML25XMIParser - 解析XMI文件，提取元类定义          │
│                                                             │
│  🗄️ 数据层 (@database)                                     │
│    └── UML25SchemaGenerator - 生成PostgreSQL表结构        │
│                                                             │
│  🚀 扩展层 (Domain Extensions)                             │
│    ├── 基础扩展框架                                        │
│    ├── 生物医学Domain扩展                                  │
│    └── 其他Domain扩展...                                   │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔧 核心组件

### 1. UML25XMIParser (@core.parsing)

**功能**: 解析UML2.5.xmi标准文件，提取元类定义和关系

```python
from core.parsing.uml25_xmi_parser import parse_uml25_xmi

# 解析UML2.5.xmi文件
xmi_result = await parse_uml25_xmi("path/to/UML2.5.xmi")

# 解析结果包含
{
    'metadata': {...},           # 元数据信息
    'statistics': {...},         # 解析统计
    'metaclasses': {...},        # 元类定义
    'associations': {...},       # 关联关系
    'inheritance_hierarchy': {...}  # 继承层次
}
```

**特性**:
- ✅ 解析UML包结构和元类定义
- ✅ 提取属性、操作、约束信息
- ✅ 构建继承关系图
- ✅ 验证元模型一致性
- ✅ 异步高性能处理

### 2. UML25SchemaGenerator (@database)

**功能**: 将XMI解析结果转换为PostgreSQL表结构

```python
from database.uml25_schema_generator import generate_uml25_schema

# 生成基础Schema
schema_result = await generate_uml25_schema(db_pool, xmi_result)

# 生成结果包含
{
    'metadata': {...},           # 生成元数据
    'statistics': {...},         # 生成统计
    'generated_tables': {...},   # 生成的表
    'extension_support': {...}   # 扩展支持
}
```

**特性**:
- ✅ 自动生成表结构和约束
- ✅ 创建继承关系映射
- ✅ 生成索引和性能优化
- ✅ 内置扩展支持框架
- ✅ PostgreSQL高级特性集成

---

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保Python环境
python >= 3.8

# 安装依赖
pip install asyncpg xml.etree.ElementTree

# 确保PostgreSQL数据库运行
postgresql >= 12.0
```

### 2. 运行完整演示

```bash
# 执行完整工作流程演示
python uml25_base_schema_demo.py

# 仅演示XMI解析
python uml25_base_schema_demo.py --setup

# 仅演示扩展机制
python uml25_base_schema_demo.py --extend

# 启用调试模式
python uml25_base_schema_demo.py --debug
```

### 3. 验证系统

演示完成后，您的数据库将包含：

```sql
-- 基础Schema
uml25_base.uml_element           -- 基础元素表
uml25_base.uml_classifier        -- 分类器表
uml25_base.uml_property          -- 属性表
uml25_base.uml_operation         -- 操作表
uml25_base.uml_generalization    -- 继承关系表

-- 扩展支持
uml25_base.domain_extensions     -- Domain注册表
uml25_base.extended_metaclasses  -- 扩展元类表

-- 生物医学扩展
biomedical_domain.biological_entities     -- 生物实体
biomedical_domain.clinical_entities       -- 临床实体
biomedical_domain.pharmaceutical_entities -- 药物实体
```

---

## 🏗️ 架构设计

### 基础表结构

#### 1. 核心元素表 (uml_element)

所有UML元素的根表，采用表继承模式：

```sql
CREATE TABLE uml25_base.uml_element (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    xmi_id TEXT UNIQUE,                    -- XMI原始ID
    name TEXT,                             -- 元素名称
    qualified_name TEXT,                   -- 限定名称
    element_type TEXT NOT NULL,            -- 元素类型
    package_path TEXT,                     -- 包路径
    documentation TEXT,                    -- 文档说明
    properties JSONB DEFAULT '{}',         -- 扩展属性
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 分类器表 (uml_classifier)

继承自uml_element，用于类、接口等：

```sql
CREATE TABLE uml25_base.uml_classifier (
    is_abstract BOOLEAN DEFAULT false,     -- 是否抽象
    visibility TEXT DEFAULT 'public',      -- 可见性
    is_final BOOLEAN DEFAULT false,        -- 是否最终类
    attributes_count INTEGER DEFAULT 0,    -- 属性数量
    operations_count INTEGER DEFAULT 0     -- 操作数量
) INHERITS (uml25_base.uml_element);
```

### 继承关系处理

系统使用PostgreSQL表继承 + 关系表的混合模式：

1. **表继承**: 实现UML元类的继承结构
2. **关系表**: 记录动态继承关系
3. **约束检查**: 确保继承一致性

```sql
-- 继承关系表
CREATE TABLE uml25_base.uml_generalization (
    child_id UUID REFERENCES uml25_base.uml_element(id),
    parent_id UUID REFERENCES uml25_base.uml_element(id),
    is_substitutable BOOLEAN DEFAULT true,
    PRIMARY KEY (child_id, parent_id)
);
```

---

## 🔧 扩展机制

### 设计理念

UML2.5基础Schema提供了一个三层扩展架构：

1. **基础层**: UML2.5标准元类 (不可修改)
2. **扩展层**: Domain特定扩展 (可配置)
3. **应用层**: 具体业务实现 (可定制)

### 扩展注册机制

#### 1. 注册Domain

```sql
-- 注册新的domain扩展
SELECT uml25_base.register_domain_extension(
    'biomedicine',              -- domain名称
    'biomedical_domain',        -- schema名称
    'Class'                     -- 基础元类 (可选)
);
```

#### 2. 创建扩展元类

```sql
-- 创建扩展元类
SELECT uml25_base.create_extended_metaclass(
    'biomedicine',              -- domain名称
    'Class',                    -- 基础元类
    'BiologicalEntity',         -- 扩展名称
    '{"biological_type": "TEXT", "taxonomy_id": "TEXT"}'::jsonb
);
```

### 扩展表创建

扩展表通过外键关联基础元素：

```sql
CREATE TABLE biomedical_domain.biological_entities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    base_element_id UUID REFERENCES uml25_base.uml_element(id),
    biological_type TEXT NOT NULL,
    taxonomy_id TEXT,
    gene_ontology JSONB DEFAULT '{}',
    biological_properties JSONB DEFAULT '{}'
);
```

---

## 📚 使用示例

### 示例1: 创建生物医学扩展

```python
import asyncio
from database.uml25_base_schema_demo import UML25BaseSchemaDemo

async def create_biomedical_extension():
    demo = UML25BaseSchemaDemo()
    await demo.initialize()
    
    async with demo.db_pool.acquire() as conn:
        # 1. 注册生物医学domain
        domain_id = await conn.fetchval("""
            SELECT uml25_base.register_domain_extension(
                'biomedicine', 'biomedical_domain', 'Class'
            )
        """)
        
        # 2. 创建基因元类
        gene_id = await conn.fetchval("""
            SELECT uml25_base.create_extended_metaclass(
                'biomedicine', 'Class', 'Gene',
                '{"gene_symbol": "TEXT", "chromosome": "TEXT"}'::jsonb
            )
        """)
        
        # 3. 创建具体实例
        element_id = await conn.fetchval("""
            INSERT INTO uml25_base.uml_element 
            (name, qualified_name, element_type, package_path)
            VALUES ('BRCA1', 'Gene::BRCA1', 'extended:Gene', 'Genomics')
            RETURNING id
        """)
        
        await conn.execute("""
            INSERT INTO biomedical_domain.biological_entities 
            (base_element_id, biological_type, taxonomy_id)
            VALUES ($1, 'Gene', 'ENTREZ:672')
        """, element_id)
    
    await demo.cleanup()

# 运行示例
asyncio.run(create_biomedical_extension())
```

### 示例2: 查询扩展数据

```sql
-- 查询所有生物实体
SELECT 
    e.name,
    e.qualified_name,
    b.biological_type,
    b.taxonomy_id,
    b.gene_ontology
FROM uml25_base.uml_element e
JOIN biomedical_domain.biological_entities b ON e.id = b.base_element_id
WHERE e.package_path = 'Genomics';

-- 查询继承关系
WITH RECURSIVE inheritance_tree AS (
    -- 根节点
    SELECT 
        id, name, qualified_name, 0 as level,
        ARRAY[name] as path
    FROM uml25_base.uml_element 
    WHERE name = 'Element'
    
    UNION ALL
    
    -- 递归查找子类
    SELECT 
        c.id, c.name, c.qualified_name, 
        p.level + 1,
        p.path || c.name
    FROM uml25_base.uml_element c
    JOIN uml25_base.uml_generalization g ON c.id = g.child_id
    JOIN inheritance_tree p ON g.parent_id = p.id
)
SELECT 
    repeat('  ', level) || name as hierarchy,
    qualified_name,
    level
FROM inheritance_tree
ORDER BY path;
```

### 示例3: 性能优化查询

```sql
-- 使用JSONB索引查询
SELECT name, properties->'gene_ontology' as ontology
FROM uml25_base.uml_element
WHERE properties @> '{"biological_type": "Gene"}';

-- 使用全文搜索
SELECT name, ts_headline(documentation, plainto_tsquery('DNA repair'))
FROM uml25_base.uml_element
WHERE to_tsvector(documentation) @@ plainto_tsquery('DNA repair');
```

---

## 📖 API参考

### UML25XMIParser

#### 类方法

```python
class UML25XMIParser:
    def __init__(self, xmi_file_path: str)
    async def parse(self) -> Dict[str, Any]
```

#### 便捷函数

```python
async def parse_uml25_xmi(xmi_file_path: str) -> Dict[str, Any]
def create_uml25_parser(xmi_file_path: str) -> UML25XMIParser
```

### UML25SchemaGenerator

#### 类方法

```python
class UML25SchemaGenerator:
    def __init__(self, db_pool: asyncpg.Pool)
    async def generate_schema_from_xmi(self, xmi_result: Dict[str, Any]) -> Dict[str, Any]
```

#### 便捷函数

```python
async def generate_uml25_schema(db_pool: asyncpg.Pool, xmi_result: Dict[str, Any]) -> Dict[str, Any]
def create_schema_generator(db_pool: asyncpg.Pool) -> UML25SchemaGenerator
```

### 扩展管理函数

```sql
-- 注册domain扩展
uml25_base.register_domain_extension(
    domain_name TEXT,
    extension_schema TEXT,
    base_metaclass_name TEXT DEFAULT NULL
) RETURNS UUID

-- 创建扩展元类
uml25_base.create_extended_metaclass(
    domain_name TEXT,
    base_metaclass_name TEXT,
    extended_name TEXT,
    extension_properties JSONB DEFAULT '{}'
) RETURNS UUID
```

---

## 💡 最佳实践

### 1. 扩展设计原则

- **单一职责**: 每个domain专注特定领域
- **最小侵入**: 尽量通过扩展而非修改基础Schema
- **向后兼容**: 确保扩展不破坏现有功能
- **性能优先**: 合理设计索引和查询

### 2. 命名约定

```sql
-- Schema命名
domain_name + '_domain'          -- biomedical_domain

-- 表命名  
domain_entities                  -- biological_entities
domain_specific_tables           -- clinical_entities

-- 列命名
domain_specific_columns          -- biological_type
base_element_id                  -- 固定关联列名
```

### 3. 扩展模式

```python
# 推荐的扩展创建流程
async def create_domain_extension(domain_name: str, schema_name: str):
    # 1. 注册domain
    domain_id = await register_domain(domain_name, schema_name)
    
    # 2. 创建扩展元类
    metaclass_ids = await create_extended_metaclasses(domain_id)
    
    # 3. 创建扩展表
    await create_extension_tables(schema_name)
    
    # 4. 创建索引和约束
    await create_indexes_and_constraints(schema_name)
    
    # 5. 验证扩展
    await validate_extension(domain_id)
```

---

## 🔍 故障排除

### 常见问题

1. **XMI文件解析失败**
   - 检查文件路径和权限
   - 验证XMI文件格式
   - 查看详细错误日志

2. **Schema生成失败**
   - 检查数据库连接
   - 验证权限设置
   - 检查PostgreSQL版本兼容性

3. **扩展创建失败**
   - 确认基础Schema已创建
   - 检查domain命名冲突
   - 验证扩展配置格式

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查解析结果
result = await parse_uml25_xmi(xmi_path)
print(f"解析的元类数量: {len(result['metaclasses'])}")

# 验证Schema状态
async with db_pool.acquire() as conn:
    tables = await conn.fetch("""
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'uml25_base'
    """)
    print(f"基础表数量: {len(tables)}")
```

---

## 🎉 总结

UML2.5基础Schema系统为生物医学MBSE平台提供了：

- **标准化基础**: 基于OMG UML2.5标准的统一元模型
- **动态扩展**: 灵活的domain扩展机制
- **类型安全**: PostgreSQL强类型约束保证
- **高性能**: 完整的索引和查询优化
- **可维护**: 清晰的架构和扩展模式

通过这个系统，您可以：
- ✅ 基于标准构建元类管理基础
- ✅ 快速创建领域特定扩展
- ✅ 保证数据完整性和一致性
- ✅ 实现高性能的元数据查询
- ✅ 支持复杂的业务场景

**🚀 开始使用这个强大的元类管理基础，为您的生物医学MBSE平台奠定坚实基础！** 