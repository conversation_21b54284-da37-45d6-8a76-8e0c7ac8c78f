# UML元类动态生成系统使用指南

📅 **创建时间**: 2025年1月1日  
🎯 **目标**: 基于核心模块的UML元类动态生成和实例解析系统  
✅ **功能**: 完整的元模型到实例数据流  

## 🌟 系统概览

### 🏗️ 核心架构

```
📦 UML元类动态生成系统
├── 🧠 核心模块集成
│   ├── core/parsing/xml_parser.py      # 统一XML解析器
│   ├── core/models/element.py          # 元素数据模型
│   └── core/services/                  # 核心服务层
├── 🎯 元类生成层
│   ├── uml_metaclass_generator.py      # UML元类动态生成器
│   ├── uml_instance_parser.py          # UML实例解析器
│   └── uml_metaclass_demo.py           # 完整演示脚本
├── 🗄️ 动态Schema生成
│   ├── domain_managers/                # 动态领域管理
│   ├── 元类Schema表                    # 基于元类定义生成
│   └── Profile扩展表                   # 基于Stereotype生成
└── 📊 实例数据存储
    ├── 模型实例表                      # 具体模型元素
    ├── Stereotype应用表                # Profile应用记录
    └── 关系网络                        # 元素间关系
```

### ✅ 核心优势

#### 🔧 基于Core模块
- **统一解析接口**: 使用`core.parsing.xml_parser.UnifiedXMLParser`
- **标准化数据模型**: 基于`core.models.element.Element`架构
- **性能优化**: 继承core模块的14.7倍性能提升78
- **ID稳定性**: 100%稳定的Element ID生成

#### 🎯 动态Schema生成
- **运行时生成**: 无需重启系统即可创建新的Element类型
- **元模型驱动**: 基于UML元类定义自动生成数据库表结构
- **Profile扩展**: 支持UML Profile的Stereotype和Tagged Values
- **继承关系**: 自动处理元类继承链和关系映射

#### 🔄 完整数据流
- **元模型定义** → **动态Schema** → **模型实例** → **查询分析**
- **版本管理**: 完整的元模型和实例版本控制
- **关系分析**: 自动建立模型元素间的关系网络
- **跨域连接**: 支持与其他领域的关联

## 🚀 快速开始

### 步骤1：环境初始化

```python
import asyncio
from uml_metaclass_generator import create_uml_metaclass_generator
from uml_instance_parser import create_uml_instance_parser

async def setup_system():
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'biomedical_mbse_platform',
        'user': 'mbse_user',
        'password': 'mbse_pass_2024'
    }
    
    # 创建数据库连接
    database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    db_pool = await asyncpg.create_pool(database_url)
    
    # 初始化生成器和解析器
    metaclass_generator = await create_uml_metaclass_generator(db_pool)
    instance_parser = await create_uml_instance_parser(db_pool)
    
    return metaclass_generator, instance_parser, db_pool
```

### 步骤2：处理UML元模型定义

```python
async def process_metamodel():
    metaclass_generator, _, db_pool = await setup_system()
    
    try:
        # 读取元模型XMI文件
        with open('BiomedicineMetamodel.xmi', 'r', encoding='utf-8') as f:
            metamodel_xmi = f.read()
        
        # 处理元模型定义，生成动态Schema
        success, message, result = await metaclass_generator.process_metamodel_xmi(
            metamodel_xmi, 'BiomedicineMetamodel', is_profile=False
        )
        
        if success:
            print(f"✅ 元模型处理成功！")
            print(f"   生成Schema数: {result['schemas_generated']}")
            print(f"   Schema列表: {', '.join(result['schema_details'])}")
        else:
            print(f"❌ 元模型处理失败: {message}")
            
    finally:
        await db_pool.close()

# 运行
asyncio.run(process_metamodel())
```

**预期输出**:
```
✅ 元模型处理成功！
   生成Schema数: 3
   Schema列表: UML Patient Element, UML Treatment Element, UML Drug Element
```

### 步骤3：处理UML Profile定义

```python
async def process_profile():
    metaclass_generator, _, db_pool = await setup_system()
    
    try:
        # 读取Profile XMI文件
        with open('BiomedicineProfile.xmi', 'r', encoding='utf-8') as f:
            profile_xmi = f.read()
        
        # 处理Profile定义，生成扩展Schema
        success, message, result = await metaclass_generator.process_metamodel_xmi(
            profile_xmi, 'BiomedicineProfile', is_profile=True
        )
        
        if success:
            print(f"✅ Profile处理成功！")
            print(f"   生成Schema数: {result['schemas_generated']}")
            print(f"   Schema列表: {', '.join(result['schema_details'])}")
        else:
            print(f"❌ Profile处理失败: {message}")
            
    finally:
        await db_pool.close()

# 运行
asyncio.run(process_profile())
```

**预期输出**:
```
✅ Profile处理成功！
   生成Schema数: 4
   Schema列表: Profile Patient Element, Profile TreatmentPlan Element, Profile Drug Element, Profile ClinicalEntity Element
```

### 步骤4：解析模型实例

```python
async def parse_model_instances():
    _, instance_parser, db_pool = await setup_system()
    
    try:
        # 读取模型实例XMI文件
        with open('BiomedicineModel.xmi', 'r', encoding='utf-8') as f:
            model_xmi = f.read()
        
        # 解析模型实例，生成实例数据
        result = await instance_parser.parse_model_xmi(
            model_xmi, 'BiomedicineModel', 'BiomedicineModel.xmi', '1.0'
        )
        
        if result.success:
            print(f"✅ 模型实例解析成功！")
            print(f"   创建实例数: {result.instances_created}")
            print(f"   Stereotype应用数: {result.stereotypes_applied}")
            print(f"   建立关系数: {result.relationships_established}")
            print(f"   实例摘要: {result.instance_summary}")
        else:
            print(f"❌ 模型实例解析失败:")
            for error in result.errors:
                print(f"      - {error}")
            
    finally:
        await db_pool.close()

# 运行
asyncio.run(parse_model_instances())
```

**预期输出**:
```
✅ 模型实例解析成功！
   创建实例数: 8
   Stereotype应用数: 6
   建立关系数: 12
   实例摘要: {'Class': 4, 'Activity': 2, 'Package': 3, 'stereotype_Patient': 2, 'stereotype_TreatmentPlan': 2, 'stereotype_Drug': 2}
```

## 📋 详细使用指南

### 1. UML元模型XMI格式

#### 标准元类定义示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<xmi:XMI xmi:version="2.5" 
         xmlns:xmi="http://www.omg.org/XMI"
         xmlns:ecore="http://www.eclipse.org/emf/2002/Ecore">
         
  <!-- 元模型包 -->
  <ecore:EPackage xmi:id="BiomedicineMetamodel" 
                  name="BiomedicineMetamodel"
                  nsURI="http://biomedical.org/metamodel"
                  nsPrefix="biomed">
    
    <!-- 患者元类定义 -->
    <eClassifiers xmi:type="ecore:EClass" 
                  xmi:id="PatientMetaclass"
                  name="Patient" 
                  instanceClassName="biomedical.Patient">
      
      <!-- 患者ID属性 -->
      <eStructuralFeatures xmi:type="ecore:EAttribute"
                           xmi:id="PatientID"
                           name="patientId"
                           eType="ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EString"
                           lowerBound="1"/>
      
      <!-- 年龄属性 -->
      <eStructuralFeatures xmi:type="ecore:EAttribute"
                           xmi:id="Age"
                           name="age"
                           eType="ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EInt"/>
    </eClassifiers>
    
  </ecore:EPackage>
</xmi:XMI>
```

#### 自动生成的Schema结构

```sql
-- 生成的患者元素表
CREATE TABLE uml_patient_elements (
    element_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    qualified_name VARCHAR(255) UNIQUE,
    visibility VARCHAR(50) DEFAULT 'public',
    is_abstract BOOLEAN DEFAULT FALSE,
    documentation TEXT,
    xmi_id VARCHAR(255) UNIQUE,
    package_path VARCHAR(255),
    stereotype_applications JSONB DEFAULT '[]',
    
    -- 元类特定字段
    patient_id VARCHAR(255),
    age INTEGER,
    
    -- 标准Element字段
    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 自动生成的索引
CREATE INDEX idx_uml_patient_name ON uml_patient_elements(name);
CREATE INDEX idx_uml_patient_qualified_name ON uml_patient_elements(qualified_name);
CREATE INDEX idx_uml_patient_xmi_id ON uml_patient_elements(xmi_id);
```

### 2. UML Profile XMI格式

#### Stereotype定义示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<xmi:XMI xmi:version="2.5"
         xmlns:xmi="http://www.omg.org/XMI"
         xmlns:uml="http://www.eclipse.org/uml2/5.0.0/UML">
         
  <!-- Profile定义 -->
  <uml:Profile xmi:id="BiomedicineProfile"
               name="BiomedicineProfile"
               URI="http://biomedical.org/profile"
               metamodelReference="http://www.eclipse.org/uml2/5.0.0/UML">
    
    <!-- 患者Stereotype -->
    <ownedStereotype xmi:type="uml:Stereotype"
                     xmi:id="Patient"
                     name="Patient">
      
      <!-- Tagged Value: 患者ID -->
      <ownedAttribute xmi:id="PatientIdTag"
                      name="patientId"
                      type="String"
                      multiplicity="1"/>
      
      <!-- Tagged Value: 病历号 -->
      <ownedAttribute xmi:id="MedicalRecordTag"
                      name="medicalRecordNumber"
                      type="String"/>
      
      <!-- Tagged Value: 出生日期 -->
      <ownedAttribute xmi:id="BirthDateTag"
                      name="birthDate"
                      type="Date"/>
      
      <!-- 扩展UML Class -->
      <ownedEnd xmi:type="uml:ExtensionEnd"
                xmi:id="PatientExtension"
                name="extension_Patient"
                aggregation="composite"/>
    </ownedStereotype>
    
  </uml:Profile>
</xmi:XMI>
```

#### 自动生成的Profile Schema结构

```sql
-- 生成的患者Profile表
CREATE TABLE profile_patient_elements (
    element_id VARCHAR(50) PRIMARY KEY,
    stereotype_name VARCHAR(255) DEFAULT 'Patient',
    profile_uri VARCHAR(255) DEFAULT 'http://biomedical.org/profile',
    base_element_id VARCHAR(50),
    
    -- 基础字段
    name VARCHAR(255) NOT NULL,
    qualified_name VARCHAR(255) UNIQUE,
    xmi_id VARCHAR(255) UNIQUE,
    
    -- Tagged Values
    tagged_patientId VARCHAR(255),
    tagged_medicalRecordNumber VARCHAR(255),
    tagged_birthDate DATE,
    
    -- 关系字段
    created_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (base_element_id) REFERENCES uml_class_elements(element_id)
);

-- Profile特定索引
CREATE INDEX idx_profile_patient_stereotype ON profile_patient_elements(stereotype_name);
CREATE INDEX idx_profile_patient_base_element ON profile_patient_elements(base_element_id);
```

### 3. 模型实例XMI格式

#### 模型实例和Stereotype应用示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<xmi:XMI xmi:version="2.5"
         xmlns:xmi="http://www.omg.org/XMI"
         xmlns:uml="http://www.eclipse.org/uml2/5.0.0/UML"
         xmlns:biomed="http://biomedical.org/profile">
         
  <!-- 模型根元素 -->
  <uml:Model xmi:id="BiomedicineModel" name="生物医学治疗模型">
    
    <!-- 应用Profile -->
    <profileApplication xmi:id="ProfileApp">
      <appliedProfile href="BiomedicineProfile.uml#BiomedicineProfile"/>
    </profileApplication>
    
    <!-- 患者包 -->
    <packagedElement xmi:type="uml:Package" 
                     xmi:id="PatientsPackage" 
                     name="Patients">
      
      <!-- 患者实例 -->
      <packagedElement xmi:type="uml:Class"
                       xmi:id="Patient001"
                       name="Zhang_Wei"
                       visibility="public"/>
      
    </packagedElement>
  </uml:Model>
  
  <!-- Stereotype应用 -->
  <biomed:Patient xmi:id="Patient001_Stereotype"
                  base_Class="#Patient001"
                  patientId="P001"
                  medicalRecordNumber="MR2024001"
                  birthDate="1985-03-15"/>
                  
</xmi:XMI>
```

#### 自动生成的实例数据

**UML Class表中的实例**:
```sql
INSERT INTO uml_class_elements (
    element_id, name, qualified_name, xmi_id, visibility, stereotype_applications
) VALUES (
    'uuid-patient001', 'Zhang_Wei', 'Patients::Zhang_Wei', 'Patient001', 'public', 
    '[{"stereotype": "Patient", "profile": "http://biomedical.org/profile"}]'
);
```

**Profile表中的应用**:
```sql
INSERT INTO profile_patient_elements (
    element_id, base_element_id, tagged_patientId, tagged_medicalRecordNumber, tagged_birthDate
) VALUES (
    'uuid-patient001-profile', 'uuid-patient001', 'P001', 'MR2024001', '1985-03-15'
);
```

## 🔧 高级功能

### 1. 动态Schema扩展

#### 运行时添加新元类

```python
async def add_new_metaclass():
    metaclass_generator, _, db_pool = await setup_system()
    
    # 新的基因元类定义
    gene_metaclass_xmi = """
    <ecore:EClass xmi:id="GeneMetaclass" name="Gene">
        <eStructuralFeatures xmi:type="ecore:EAttribute" 
                             name="geneSymbol" 
                             eType="ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EString"/>
        <eStructuralFeatures xmi:type="ecore:EAttribute" 
                             name="chromosomeLocation" 
                             eType="ecore:EDataType http://www.eclipse.org/emf/2002/Ecore#//EString"/>
    </ecore:EClass>
    """
    
    # 处理新元类
    success, message, result = await metaclass_generator.process_metamodel_xmi(
        gene_metaclass_xmi, 'GeneExtension', is_profile=False
    )
    
    if success:
        print("✅ 新元类添加成功，自动生成了Gene表结构")
```

#### 动态Profile扩展

```python
async def add_profile_extension():
    metaclass_generator, _, db_pool = await setup_system()
    
    # 新的基因组学Profile
    genomics_profile_xmi = """
    <uml:Stereotype xmi:id="GenomicMarker" name="GenomicMarker">
        <ownedAttribute name="markerType" type="String"/>
        <ownedAttribute name="significance" type="Real"/>
        <ownedAttribute name="references" type="String" multiplicity="0..*"/>
    </uml:Stereotype>
    """
    
    # 处理Profile扩展
    success, message, result = await metaclass_generator.process_metamodel_xmi(
        genomics_profile_xmi, 'GenomicsProfile', is_profile=True
    )
    
    if success:
        print("✅ Profile扩展成功，自动生成了GenomicMarker表结构")
```

### 2. 复杂查询和分析

#### 跨域关系查询

```python
async def complex_queries():
    _, instance_parser, db_pool = await setup_system()
    
    async with db_pool.acquire() as conn:
        # 查询应用了特定Stereotype的所有实例
        patient_instances = await conn.fetch("""
            SELECT e.name, e.qualified_name, p.tagged_patientId, p.tagged_birthDate
            FROM core_schema.element_metadata e
            JOIN profile_patient_elements p ON e.element_id = p.base_element_id
            WHERE e.element_type = 'Class'
            AND p.stereotype_name = 'Patient'
            ORDER BY e.name
        """)
        
        print("🏥 患者实例:")
        for patient in patient_instances:
            print(f"   - {patient['name']} (ID: {patient['tagged_patientid']}, 生日: {patient['tagged_birthdate']})")
        
        # 查询治疗方案和相关药物
        treatment_drug_relations = await conn.fetch("""
            SELECT t.name as treatment_name, 
                   pt.tagged_protocol,
                   pt.tagged_efficacyrate,
                   d.name as drug_name,
                   pd.tagged_activeingredient,
                   pd.tagged_dosage
            FROM core_schema.element_metadata t
            JOIN profile_treatmentplan_elements pt ON t.element_id = pt.base_element_id
            JOIN core_schema.element_metadata d ON t.parent_id = d.parent_id  -- 同包关系
            JOIN profile_drug_elements pd ON d.element_id = pd.base_element_id
            WHERE t.element_type = 'Activity' AND d.element_type = 'Class'
            ORDER BY t.name, d.name
        """)
        
        print("\n💊 治疗方案和药物关系:")
        for relation in treatment_drug_relations:
            print(f"   {relation['treatment_name']} ({relation['tagged_protocol']}) -> {relation['drug_name']} ({relation['tagged_dosage']})")
```

#### 元模型演化分析

```python
async def analyze_metamodel_evolution():
    metaclass_generator, _, db_pool = await setup_system()
    
    # 查看元类定义的演化历史
    metaclass_history = await metaclass_generator.get_metaclass_definitions()
    
    print("📈 元类演化分析:")
    for metaclass in metaclass_history:
        properties = json.loads(metaclass['properties']) if metaclass['properties'] else {}
        print(f"   {metaclass['name']} ({metaclass['metaclass_type']})")
        print(f"     属性数: {len(properties)}")
        print(f"     创建时间: {metaclass['created_time']}")
        if metaclass['base_metaclass']:
            print(f"     继承自: {metaclass['base_metaclass']}")
```

### 3. 性能优化

#### 批量处理

```python
async def batch_processing():
    metaclass_generator, instance_parser, db_pool = await setup_system()
    
    # 批量处理多个模型文件
    model_files = ['model1.xmi', 'model2.xmi', 'model3.xmi']
    
    results = []
    for file_path in model_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            xmi_content = f.read()
        
        result = await instance_parser.parse_model_xmi(
            xmi_content, f"Model_{file_path.split('.')[0]}", file_path, '1.0'
        )
        results.append(result)
    
    # 统计批量处理结果
    total_instances = sum(r.instances_created for r in results if r.success)
    total_stereotypes = sum(r.stereotypes_applied for r in results if r.success)
    
    print(f"📊 批量处理结果:")
    print(f"   处理文件数: {len(model_files)}")
    print(f"   成功数: {sum(1 for r in results if r.success)}")
    print(f"   总实例数: {total_instances}")
    print(f"   总Stereotype应用数: {total_stereotypes}")
```

#### 索引优化

```python
async def optimize_performance():
    _, _, db_pool = await setup_system()
    
    async with db_pool.acquire() as conn:
        # 创建复合索引优化查询
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_element_type_stereotype 
            ON core_schema.element_metadata(element_type, (custom_attributes->>'stereotype_name'))
        """)
        
        # 创建Profile查询优化索引
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_stereotype_tagged_values 
            ON core_schema.stereotype_applications USING GIN(tagged_values)
        """)
        
        print("✅ 性能优化索引创建完成")
```

## 📊 监控和运维

### 1. 系统状态监控

```python
async def system_monitoring():
    metaclass_generator, instance_parser, db_pool = await setup_system()
    
    # 获取系统状态
    metaclasses = await metaclass_generator.get_metaclass_definitions()
    profiles = await metaclass_generator.get_profile_definitions()
    schemas = await metaclass_generator.get_generated_schemas()
    
    print("📊 系统状态监控:")
    print(f"   注册元类数: {len(metaclasses)}")
    print(f"   注册Profile数: {len(profiles)}")
    print(f"   生成Schema数: {len(schemas)}")
    
    # 数据库使用情况
    async with db_pool.acquire() as conn:
        element_count = await conn.fetchval("SELECT COUNT(*) FROM core_schema.element_metadata")
        stereotype_count = await conn.fetchval("SELECT COUNT(*) FROM core_schema.stereotype_applications")
        
        print(f"   Element实例数: {element_count}")
        print(f"   Stereotype应用数: {stereotype_count}")
        
        # 各类型实例分布
        type_distribution = await conn.fetch("""
            SELECT element_type, COUNT(*) as count
            FROM core_schema.element_metadata
            GROUP BY element_type
            ORDER BY count DESC
        """)
        
        print("   实例类型分布:")
        for dist in type_distribution:
            print(f"     {dist['element_type']}: {dist['count']}个")
```

### 2. 数据清理和维护

```python
async def data_maintenance():
    _, _, db_pool = await setup_system()
    
    async with db_pool.acquire() as conn:
        # 清理孤立的Stereotype应用
        orphaned_stereotypes = await conn.fetchval("""
            DELETE FROM core_schema.stereotype_applications s
            WHERE NOT EXISTS (
                SELECT 1 FROM core_schema.element_metadata e 
                WHERE e.element_id = s.base_element_id
            )
            RETURNING COUNT(*)
        """)
        
        print(f"🧹 数据清理完成:")
        print(f"   清理孤立Stereotype应用: {orphaned_stereotypes}个")
        
        # 更新统计信息
        await conn.execute("ANALYZE core_schema.element_metadata")
        await conn.execute("ANALYZE core_schema.stereotype_applications")
        
        print("✅ 数据库统计信息更新完成")
```

## 🎉 总结

基于核心模块的UML元类动态生成系统为您提供了：

### ✅ 完整的元模型支持
- **标准UML元类**: 支持Class、Association、Activity等所有标准元类
- **Profile扩展**: 完整的Stereotype和Tagged Values支持
- **动态生成**: 运行时创建新的Element类型，无需重启系统
- **版本管理**: 完整的元模型演化和版本控制

### ✅ 高性能实例处理
- **14.7倍性能提升**: 继承core模块的优化成果
- **批量处理**: 支持大规模XMI文件批量处理
- **关系分析**: 自动建立模型元素间的复杂关系网络
- **智能缓存**: 优化的查询和存储性能

### ✅ 生产级特性
- **事务安全**: 完整的数据库事务支持
- **错误恢复**: 优雅的错误处理和恢复机制
- **监控运维**: 完整的系统监控和数据维护功能
- **扩展性**: 支持自定义元类和Profile扩展

**现在您可以基于标准UML元模型动态生成领域Schema，然后解析工程XMI文件生成对应的模型示例数据，实现从元模型到实例的完整数据流！** 🚀 