# UML关系设计方案分析

## 当前实现的继承关系问题

### 现状
当前系统使用PostgreSQL表继承，但继承层次不完整：
- 缺少中间抽象层：Type、Namespace、StructuralFeature等
- 继承映射不完整：很多UML类型没有正确的父类
- 与UML 2.5标准不完全一致

## 三种设计方案对比

### 方案1: 完整表继承 (推荐)
```sql
-- 完整的UML继承层次
CREATE TABLE uml_element (...);
CREATE TABLE uml_named_element (...) INHERITS (uml_element);
CREATE TABLE uml_type (...) INHERITS (uml_named_element);
CREATE TABLE uml_classifier (...) INHERITS (uml_type);
CREATE TABLE uml_class (...) INHERITS (uml_classifier);
```

**优点**:
- ✅ 完全符合UML 2.5语义
- ✅ 查询父类自动包含子类
- ✅ 强类型约束
- ✅ 继承属性自动传递

**缺点**:
- ❌ PostgreSQL外键限制
- ❌ 查询复杂度增加
- ❌ 索引继承问题

### 方案2: 平铺+关系表
```sql
-- 所有表平级
CREATE TABLE uml_element (...);
CREATE TABLE uml_class (...);
CREATE TABLE inheritance_relations (child_id, parent_id, inheritance_type);
```

**优点**:
- ✅ 外键友好
- ✅ 查询简单
- ✅ 维护容易
- ✅ 支持多重继承

**缺点**:
- ❌ 语义不直观
- ❌ 字段重复定义
- ❌ 约束复杂
- ❌ 查询需要更多JOIN

### 方案3: 混合方案 (最佳平衡)
```sql
-- 核心继承链用表继承
CREATE TABLE uml_element (...);
CREATE TABLE uml_named_element (...) INHERITS (uml_element);
CREATE TABLE uml_classifier (...) INHERITS (uml_named_element);

-- 具体类平铺，通过视图和函数模拟继承
CREATE TABLE uml_class_data (...);
CREATE VIEW uml_class AS 
  SELECT c.*, n.name, n.visibility, e.documentation 
  FROM uml_class_data c
  JOIN uml_named_element n ON c.id = n.id
  JOIN uml_element e ON c.id = e.id;

-- 复杂关系用关系表
CREATE TABLE element_relationships (
    source_id UUID,
    target_id UUID,
    relationship_type TEXT,
    properties JSONB
);
```

## 推荐实现策略

### 1. 核心抽象层：使用表继承
对于稳定的核心继承链，使用PostgreSQL表继承：
- Element → NamedElement → Type → Classifier
- Element → Relationship

### 2. 具体类：使用数据表+视图
为每个具体UML类型创建数据表，通过视图提供继承语义：
```sql
-- 数据表存储特有属性  
CREATE TABLE uml_class_data (
    id UUID PRIMARY KEY,
    is_active BOOLEAN,
    is_abstract BOOLEAN
);

-- 视图提供完整UML Class语义
CREATE VIEW uml_class AS 
SELECT 
    c.*,
    ne.name, ne.visibility,
    cl.is_final_specialization,
    e.documentation, e.stereotype
FROM uml_class_data c
JOIN uml_classifier cl ON c.id = cl.id
JOIN uml_named_element ne ON c.id = ne.id  
JOIN uml_element e ON c.id = e.id;
```

### 3. 关系：使用关系表
对于关联、依赖等关系，使用专门的关系表：
```sql
CREATE TABLE uml_associations (
    id UUID PRIMARY KEY,
    source_end_id UUID,
    target_end_id UUID,
    association_type TEXT,
    multiplicity_source TEXT,
    multiplicity_target TEXT
);
```

## 实现建议

1. **保持核心继承**: Element, NamedElement, Classifier等核心抽象类
2. **扩展中间层**: 添加Type, Namespace, Feature等中间抽象类
3. **数据视图结合**: 具体类使用数据表+视图模式
4. **关系表独立**: 用专门表管理元素间关系
5. **提供工具函数**: 封装复杂查询逻辑 