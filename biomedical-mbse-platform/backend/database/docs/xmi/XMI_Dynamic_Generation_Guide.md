# XMI标准动态生成器使用指南

📅 **创建时间**: 2025年7月1日  
🎯 **目标**: 基于标准XMI自动生成动态建模领域  
✅ **功能**: 完整的版本管理 + 智能Element生成

## 🌟 核心优势

### ✅ 标准XMI支持
- **完全兼容** OMG XMI 2.0-2.5标准
- **多工具支持** Enterprise Architect, MagicDraw, Papyrus, Visual Paradigm
- **智能识别** 自动检测UML/SysML/BPMN模型类型
- **命名空间处理** 自动解析和映射XMI命名空间

### ✅ 动态生成能力
- **运行时创建** 无需重启系统即可生成新领域
- **智能推断** 从XMI结构自动推断Element类型定义
- **关系分析** 自动分析元素间的关系和引用
- **跨域连接** 自动建立UML-SysML等跨域关系

### ✅ 完整版本管理
- **变更检测** 基于SHA256校验和自动检测模型变更
- **版本历史** 完整的版本树和变更记录
- **内容压缩** 使用gzip压缩存储XMI内容
- **回滚支持** 支持回滚到任意历史版本

## 🏗️ 架构概览

### 核心组件

```
📦 XMI动态生成器架构
├── 🎯 XMIDynamicGenerator        # 主生成器
├── 📊 XMIMetadata               # XMI元数据解析
├── 🧩 ElementTypeDefinition     # 动态Element类型定义  
├── 📚 ModelVersion              # 版本管理
├── 🔄 版本管理表                # 数据库版本存储
└── 📁 XMI内容存储               # 压缩XMI内容存储
```

### 支持的XMI格式

| 工具 | XMI版本 | 元模型 | 支持程度 |
|------|---------|--------|----------|
| Enterprise Architect | 2.1-2.5 | UML 2.x | ✅ 完全支持 |
| MagicDraw | 2.0-2.5 | UML/SysML | ✅ 完全支持 |
| Papyrus | 2.1-2.5 | UML/SysML | ✅ 完全支持 |
| Visual Paradigm | 2.1-2.4 | UML | ✅ 完全支持 |
| Rhapsody | 2.0-2.1 | UML/SysML | ✅ 部分支持 |

## 🚀 快速开始

### 步骤1：初始化生成器

```python
import asyncio
from xmi_dynamic_generator import XMIDynamicGenerator

async def setup_generator():
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'biomedical_mbse_platform',
        'user': 'mbse_user',
        'password': 'mbse_pass_2024'
    }
    
    # 创建数据库连接池
    database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    db_pool = await asyncpg.create_pool(database_url)
    
    # 初始化生成器
    generator = XMIDynamicGenerator(db_pool)
    await generator.initialize()
    
    print("✅ XMI动态生成器初始化完成！")
    return generator, db_pool
```

### 步骤2：处理XMI文件

```python
async def process_xmi_model():
    generator, db_pool = await setup_generator()
    
    try:
        # 读取XMI文件
        with open('my_uml_model.xmi', 'r', encoding='utf-8') as f:
            xmi_content = f.read()
        
        # 处理XMI文件
        success, message, model_version = await generator.process_xmi_file(
            xmi_content=xmi_content,
            model_name='BiomedicineModel',  # 模型名称
            author='<EMAIL>'  # 作者信息
        )
        
        if success:
            print(f"🎉 模型处理成功！")
            print(f"   版本: {model_version.version_number}")
            print(f"   版本ID: {model_version.version_id}")
            print(f"   变更摘要: {model_version.changes_summary}")
        else:
            print(f"❌ 模型处理失败: {message}")
            
    finally:
        await db_pool.close()

# 运行处理
asyncio.run(process_xmi_model())
```

**预期输出**:
```
✅ XMI动态生成器初始化完成！
XMI版本: 2.5, 模型类型: ModelType.UML
分析得到 8 种Element类型
✅ 动态领域创建成功: BiomedicineModel_uml
✅ XMI处理完成，生成版本 1.0
🎉 模型处理成功！
   版本: 1.0
   版本ID: 550e8400-e29b-41d4-a716-446655440000
   变更摘要: ['Initial model creation']
```

### 步骤3：验证生成结果

```sql
-- 查看生成的领域Schema
SELECT schema_name FROM information_schema.schemata 
WHERE schema_name LIKE 'biomedicinemodel_%';

-- 查看动态生成的表
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'biomedicinemodel_uml_schema';

-- 查看模型版本
SELECT model_name, version_number, created_time, created_by 
FROM core_schema.model_versions
WHERE model_name = 'BiomedicineModel'
ORDER BY created_time DESC;
```

## 📋 详细使用指南

### 1. 支持的XMI元素自动映射

#### UML元素映射表

| XMI元素 | 生成的Element类型 | 主要字段 |
|---------|------------------|----------|
| `<uml:Class>` | `uml_class_element` | name, qualified_name, attributes, operations |
| `<uml:Package>` | `uml_package_element` | name, owned_elements, imported_packages |
| `<uml:Association>` | `uml_association_element` | source_class_id, target_class_id, multiplicity |
| `<uml:UseCase>` | `uml_usecase_element` | name, actors, preconditions, scenarios |
| `<uml:Interface>` | `uml_interface_element` | name, operations, protocols |

#### SysML元素映射表

| XMI元素 | 生成的Element类型 | 主要字段 |
|---------|------------------|----------|
| `<sysml:Block>` | `sysml_block_element` | name, value_properties, part_properties |
| `<sysml:Requirement>` | `sysml_requirement_element` | requirement_id, text, verification_method |
| `<sysml:Activity>` | `sysml_activity_element` | name, input_parameters, control_flow |
| `<sysml:Port>` | `sysml_port_element` | name, direction, provided_interfaces |

### 2. 智能字段类型推断

```python
# 生成器会自动推断XMI属性的数据类型

# 示例XMI片段
"""
<uml:Class xmi:id="CLASS_001" name="Patient" isAbstract="false">
    <ownedAttribute name="age" type="Integer"/>
    <ownedAttribute name="isActive" type="Boolean"/> 
    <ownedAttribute name="weight" type="Real"/>
</uml:Class>
"""

# 自动生成的字段定义
{
    'name': {'type': 'string', 'required': True, 'indexed': True},
    'is_abstract': {'type': 'boolean', 'default': False},
    'xmi_id': {'type': 'string', 'unique': True, 'indexed': True},
    'owned_attributes': {'type': 'jsonb', 'default': '[]'},
    # ... 其他标准字段
}
```

### 3. 版本管理操作

#### 查看版本历史

```python
async def view_version_history():
    generator = await setup_generator()
    
    # 获取模型的所有版本
    versions = await generator.get_model_versions('BiomedicineModel')
    
    print("📚 模型版本历史:")
    for version in versions:
        print(f"   v{version['version_number']} - {version['created_time']}")
        print(f"   作者: {version['created_by']}")
        print(f"   校验和: {version['checksum'][:8]}...")
        if version['changes_summary']:
            changes = json.loads(version['changes_summary'])
            print(f"   变更: {', '.join(changes[:2])}")
        print()
```

#### 获取指定版本的XMI内容

```python
async def get_version_content():
    generator = await setup_generator()
    
    # 获取特定版本的XMI内容
    version_id = "550e8400-e29b-41d4-a716-446655440000"
    xmi_content = await generator.get_xmi_content(version_id)
    
    if xmi_content:
        print(f"✅ 获取版本内容成功，大小: {len(xmi_content)} 字符")
        # 可以保存到文件或进一步处理
        with open('restored_model.xmi', 'w', encoding='utf-8') as f:
            f.write(xmi_content)
    else:
        print("❌ 未找到版本内容")
```

#### 版本回滚

```python
async def rollback_model():
    generator = await setup_generator()
    
    # 回滚到指定版本
    success, message = await generator.rollback_to_version(
        model_name='BiomedicineModel',
        target_version='1.0'
    )
    
    if success:
        print(f"✅ 回滚成功: {message}")
    else:
        print(f"❌ 回滚失败: {message}")
```

### 4. 批量处理多个XMI文件

```python
async def batch_process_xmi_files():
    generator, db_pool = await setup_generator()
    
    import glob
    import os
    
    try:
        # 批量处理目录中的所有XMI文件
        xmi_files = glob.glob('models/*.xmi')
        results = []
        
        for file_path in xmi_files:
            model_name = os.path.splitext(os.path.basename(file_path))[0]
            
            print(f"处理文件: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                xmi_content = f.read()
            
            success, message, version = await generator.process_xmi_file(
                xmi_content=xmi_content,
                model_name=model_name,
                author='batch_processor'
            )
            
            results.append({
                'file': file_path,
                'model': model_name, 
                'success': success,
                'version': version.version_number if success else None,
                'message': message
            })
            
            if success:
                print(f"   ✅ 成功 - 版本 {version.version_number}")
            else:
                print(f"   ❌ 失败 - {message}")
        
        # 汇总结果
        success_count = sum(1 for r in results if r['success'])
        print(f"\n📊 批量处理完成: {success_count}/{len(results)} 个文件成功")
        
    finally:
        await db_pool.close()
```

## 🔧 高级功能

### 1. 自定义XMI元素解析

```python
class CustomXMIParser(XMIDynamicGenerator):
    """自定义XMI解析器"""
    
    async def _create_element_type_definition(self, elem, elem_type, metadata):
        """重写Element类型定义创建逻辑"""
        
        # 调用父类方法获取基础定义
        base_def = await super()._create_element_type_definition(elem, elem_type, metadata)
        
        if not base_def:
            return None
        
        # 添加生物医学特定字段
        if elem_type == 'class' and self._has_biomedical_stereotype(elem):
            base_def.field_definitions.update({
                'ncbi_gene_id': {'type': 'string', 'indexed': True},
                'uniprot_id': {'type': 'string', 'indexed': True},
                'go_annotations': {'type': 'jsonb', 'default': '[]'},
                'pathway_refs': {'type': 'jsonb', 'default': '[]'}
            })
            
            # 添加生物医学跨域引用
            base_def.cross_domain_refs.extend([
                'genomics_refs', 'proteomics_refs', 'pathway_refs'
            ])
        
        return base_def
    
    def _has_biomedical_stereotype(self, elem):
        """检查是否有生物医学构造型"""
        stereotype = self._extract_stereotype(elem)
        biomedical_stereotypes = ['Gene', 'Protein', 'Pathway', 'Drug', 'Disease']
        return stereotype in biomedical_stereotypes
```

### 2. 模型变更分析

```python
async def analyze_model_changes():
    generator = await setup_generator()
    
    # 获取两个版本进行比较
    versions = await generator.get_model_versions('BiomedicineModel')
    
    if len(versions) >= 2:
        latest = versions[0]
        previous = versions[1]
        
        # 获取两个版本的XMI内容
        latest_xmi = await generator.get_xmi_content(latest['version_id'])
        previous_xmi = await generator.get_xmi_content(previous['version_id'])
        
        # 分析变更（可以使用XML diff库）
        changes = analyze_xmi_differences(previous_xmi, latest_xmi)
        
        print(f"📊 版本变更分析 v{previous['version_number']} -> v{latest['version_number']}:")
        for change in changes:
            print(f"   {change['type']}: {change['description']}")

def analyze_xmi_differences(old_xmi, new_xmi):
    """分析XMI文件差异"""
    # 这里可以实现详细的XML差异分析
    # 返回结构化的变更信息
    return [
        {'type': 'ADDED', 'description': 'Added new class: Treatment'},
        {'type': 'MODIFIED', 'description': 'Modified class Patient: added attribute insurance'},
        {'type': 'DELETED', 'description': 'Removed association: PatientToDoctor'}
    ]
```

### 3. 性能优化配置

```python
# 大型XMI文件处理优化
async def process_large_xmi():
    generator = await setup_generator()
    
    # 配置大文件处理参数
    generator.batch_size = 1000  # 批处理大小
    generator.enable_streaming = True  # 启用流式处理
    generator.compression_level = 6  # 压缩级别
    
    # 使用分块处理
    with open('large_model.xmi', 'r', encoding='utf-8') as f:
        success, message, version = await generator.process_xmi_file(
            xmi_content=f.read(),
            model_name='LargeBioModel',
            author='data_engineer'
        )
```

## 📊 监控和运维

### 1. 版本存储统计

```sql
-- 查看版本存储使用情况
SELECT 
    model_name,
    COUNT(*) as version_count,
    MAX(created_time) as latest_version,
    SUM(original_size) / (1024*1024) as total_size_mb,
    AVG(original_size) / 1024 as avg_size_kb
FROM core_schema.model_versions mv
JOIN core_schema.xmi_content_store xcs ON mv.xmi_content = xcs.content_id
GROUP BY model_name
ORDER BY total_size_mb DESC;
```

### 2. 压缩效率分析

```sql
-- 分析压缩效率
SELECT 
    model_name,
    version_number,
    original_size,
    LENGTH(compressed_content) as compressed_size,
    ROUND(
        (1 - LENGTH(compressed_content)::float / original_size) * 100, 2
    ) as compression_ratio_percent
FROM core_schema.model_versions mv
JOIN core_schema.xmi_content_store xcs ON mv.xmi_content = xcs.content_id
ORDER BY compression_ratio_percent DESC;
```

### 3. 自动清理历史版本

```python
async def cleanup_old_versions():
    """清理超过30天且非主要版本的历史记录"""
    
    async with db_pool.acquire() as conn:
        # 删除30天前的非主要版本
        await conn.execute("""
            DELETE FROM core_schema.model_versions 
            WHERE created_time < NOW() - INTERVAL '30 days'
            AND version_number NOT LIKE '%.0'  -- 保留主要版本
            AND version_id NOT IN (
                -- 保留每个模型的最新版本
                SELECT DISTINCT ON (model_name) version_id
                FROM core_schema.model_versions
                ORDER BY model_name, created_time DESC
            )
        """)
```

## 🎉 总结

基于标准XMI的动态生成器为您提供了：

### ✅ 标准化支持
- 完全支持OMG XMI标准
- 兼容主流建模工具
- 自动识别模型类型

### ✅ 智能生成
- 运行时动态领域创建
- 智能字段类型推断
- 自动关系分析

### ✅ 完整版本管理
- SHA256变更检测
- 压缩存储优化
- 完整回滚支持

### ✅ 生产级特性
- 批量处理支持
- 性能监控
- 自动清理机制

**现在您可以将任何XMI格式的建模文件导入系统，自动生成对应的动态领域，并享受完整的版本管理功能！** 🚀 