"""
核心领域管理器 - 抽象基类和通用实现

提供Element抽象架构的基础管理功能
支持跨领域的Element操作和关系管理
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Union
import asyncio
import uuid
import json
import logging
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum

import asyncpg
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class OperationResult(Enum):
    """操作结果枚举"""
    SUCCESS = "success"
    FAILURE = "failure"
    ERROR = "error"
    WARNING = "warning"

@dataclass
class ElementMetadata:
    """Element元数据"""
    element_id: str
    domain_schema: str
    element_type: str
    tag: str
    local_name: str
    status: str = "active"
    version: str = "1.0"
    semantic_tags: List[str] = None
    domain_category: Optional[str] = None
    created_by: Optional[str] = None

class CrossDomainRelationship(BaseModel):
    """跨领域关系模型"""
    relationship_id: str = Field(..., pattern=r'^rel_[a-zA-Z0-9_-]+$')
    source_element_id: str
    target_element_id: str
    source_domain: str
    target_domain: str
    relationship_type: str
    relationship_direction: str = "directed"
    relationship_data: Dict[str, Any] = Field(default_factory=dict)
    strength: float = Field(default=1.0, ge=0.0, le=1.0)
    semantic_meaning: Optional[str] = None

class DomainManager(ABC):
    """领域管理器抽象基类"""
    
    def __init__(self, db_pool: asyncpg.Pool, domain_name: str, schema_name: str):
        self.db_pool = db_pool
        self.domain_name = domain_name
        self.schema_name = schema_name
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
    
    @property
    @abstractmethod
    def supported_element_types(self) -> List[str]:
        """返回支持的Element类型列表"""
        pass
    
    @abstractmethod
    async def create_element(self, element_data: Dict[str, Any], created_by: Optional[str] = None) -> Tuple[bool, str]:
        """
        创建领域内Element
        
        Args:
            element_data: Element数据
            created_by: 创建者
            
        Returns:
            Tuple[bool, str]: (是否成功, 元素ID或错误信息)
        """
        pass
    
    @abstractmethod
    async def get_element(self, element_id: str) -> Optional[Dict[str, Any]]:
        """
        获取领域内Element
        
        Args:
            element_id: Element ID
            
        Returns:
            Optional[Dict]: Element数据
        """
        pass
    
    @abstractmethod
    async def update_element(self, element_id: str, updates: Dict[str, Any], 
                           updated_by: Optional[str] = None) -> bool:
        """
        更新领域内Element
        
        Args:
            element_id: Element ID
            updates: 更新数据
            updated_by: 更新者
            
        Returns:
            bool: 是否成功
        """
        pass
    
    @abstractmethod
    async def query_elements(self, filters: Dict[str, Any], 
                           limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        查询领域内Elements
        
        Args:
            filters: 查询过滤条件
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            List[Dict]: Element列表
        """
        pass
    
    async def delete_element(self, element_id: str, soft_delete: bool = True) -> bool:
        """
        删除Element（默认软删除）
        
        Args:
            element_id: Element ID
            soft_delete: 是否软删除
            
        Returns:
            bool: 是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                if soft_delete:
                    # 软删除：更新状态为deleted
                    await conn.execute(
                        "UPDATE core_schema.element_metadata SET status = 'deleted', updated_at = $1 WHERE element_id = $2",
                        datetime.now(timezone.utc), element_id
                    )
                else:
                    # 硬删除：物理删除
                    await conn.execute(
                        "DELETE FROM core_schema.element_metadata WHERE element_id = $1",
                        element_id
                    )
                
                await self._log_operation("delete_element", element_id, OperationResult.SUCCESS)
                return True
                
        except Exception as e:
            self.logger.error(f"删除Element失败 {element_id}: {e}")
            await self._log_operation("delete_element", element_id, OperationResult.ERROR, str(e))
            return False

class CoreDomainManager(DomainManager):
    """核心领域管理器 - 管理Element抽象和跨领域功能"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        super().__init__(db_pool, "core", "core_schema")
    
    @property
    def supported_element_types(self) -> List[str]:
        return ["abstract_element", "domain_element", "relationship_element"]
    
    async def create_element(self, element_data: Dict[str, Any], created_by: Optional[str] = None) -> Tuple[bool, str]:
        """核心Schema不直接创建Element，而是管理其他领域的Element注册"""
        return False, "核心Schema不直接创建Element"
    
    async def get_element(self, element_id: str) -> Optional[Dict[str, Any]]:
        """获取Element元数据"""
        try:
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT * FROM core_schema.element_metadata WHERE element_id = $1 AND status != 'deleted'",
                    element_id
                )
                return dict(row) if row else None
        except Exception as e:
            self.logger.error(f"获取Element元数据失败 {element_id}: {e}")
            return None
    
    async def update_element(self, element_id: str, updates: Dict[str, Any], 
                           updated_by: Optional[str] = None) -> bool:
        """更新Element元数据"""
        try:
            async with self.db_pool.acquire() as conn:
                # 构建更新SQL
                update_fields = []
                params = []
                param_count = 1
                
                for field, value in updates.items():
                    if field in ['tag', 'local_name', 'status', 'version', 'semantic_tags', 
                                'domain_category', 'description', 'metadata']:
                        update_fields.append(f"{field} = ${param_count}")
                        params.append(value)
                        param_count += 1
                
                if not update_fields:
                    return False
                
                # 添加updated_at和updated_by
                update_fields.extend([f"updated_at = ${param_count}", f"updated_by = ${param_count + 1}"])
                params.extend([datetime.now(timezone.utc), updated_by])
                param_count += 2
                
                # 添加element_id条件
                params.append(element_id)
                
                sql = f"""
                    UPDATE core_schema.element_metadata 
                    SET {', '.join(update_fields)}
                    WHERE element_id = ${param_count}
                """
                
                result = await conn.execute(sql, *params)
                success = result.split()[-1] == "1"  # 检查影响行数
                
                if success:
                    await self._log_operation("update_element_metadata", element_id, OperationResult.SUCCESS)
                
                return success
                
        except Exception as e:
            self.logger.error(f"更新Element元数据失败 {element_id}: {e}")
            await self._log_operation("update_element_metadata", element_id, OperationResult.ERROR, str(e))
            return False
    
    async def query_elements(self, filters: Dict[str, Any], 
                           limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """查询Element元数据"""
        try:
            async with self.db_pool.acquire() as conn:
                # 构建查询条件
                where_conditions = ["status != 'deleted'"]
                params = []
                param_count = 1
                
                for field, value in filters.items():
                    if field in ['domain_schema', 'element_type', 'status', 'domain_category']:
                        where_conditions.append(f"{field} = ${param_count}")
                        params.append(value)
                        param_count += 1
                    elif field == 'semantic_tags' and isinstance(value, list):
                        where_conditions.append(f"semantic_tags @> ${param_count}")
                        params.append(json.dumps(value))
                        param_count += 1
                
                # 添加limit和offset
                params.extend([limit, offset])
                
                sql = f"""
                    SELECT * FROM core_schema.element_metadata 
                    WHERE {' AND '.join(where_conditions)}
                    ORDER BY created_at DESC
                    LIMIT ${param_count} OFFSET ${param_count + 1}
                """
                
                rows = await conn.fetch(sql, *params)
                return [dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"查询Element元数据失败: {e}")
            return []
    
    async def register_element_metadata(self, metadata: ElementMetadata) -> bool:
        """
        注册Element元数据到核心Schema
        
        Args:
            metadata: Element元数据
            
        Returns:
            bool: 是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO core_schema.element_metadata (
                        element_id, domain_schema, element_type, tag, local_name,
                        status, version, semantic_tags, domain_category, created_by
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (element_id) DO UPDATE SET
                        tag = EXCLUDED.tag,
                        local_name = EXCLUDED.local_name,
                        status = EXCLUDED.status,
                        version = EXCLUDED.version,
                        semantic_tags = EXCLUDED.semantic_tags,
                        domain_category = EXCLUDED.domain_category,
                        updated_at = CURRENT_TIMESTAMP,
                        updated_by = EXCLUDED.created_by
                """, 
                    metadata.element_id,
                    metadata.domain_schema,
                    metadata.element_type,
                    metadata.tag,
                    metadata.local_name,
                    metadata.status,
                    metadata.version,
                    json.dumps(metadata.semantic_tags or []),
                    metadata.domain_category,
                    metadata.created_by
                )
                
                await self._log_operation("register_element_metadata", metadata.element_id, OperationResult.SUCCESS)
                return True
                
        except Exception as e:
            self.logger.error(f"注册Element元数据失败 {metadata.element_id}: {e}")
            await self._log_operation("register_element_metadata", metadata.element_id, OperationResult.ERROR, str(e))
            return False
    
    async def create_cross_domain_relationship(self, relationship: CrossDomainRelationship) -> bool:
        """
        创建跨领域关系
        
        Args:
            relationship: 跨领域关系
            
        Returns:
            bool: 是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO core_schema.cross_domain_relationships (
                        relationship_id, source_element_id, target_element_id,
                        source_domain, target_domain, relationship_type,
                        relationship_direction, relationship_data, strength,
                        semantic_meaning
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                """,
                    relationship.relationship_id,
                    relationship.source_element_id,
                    relationship.target_element_id,
                    relationship.source_domain,
                    relationship.target_domain,
                    relationship.relationship_type,
                    relationship.relationship_direction,
                    json.dumps(relationship.relationship_data),
                    relationship.strength,
                    relationship.semantic_meaning
                )
                
                await self._log_operation("create_cross_domain_relationship", 
                                         relationship.relationship_id, OperationResult.SUCCESS)
                return True
                
        except Exception as e:
            self.logger.error(f"创建跨领域关系失败 {relationship.relationship_id}: {e}")
            await self._log_operation("create_cross_domain_relationship", 
                                     relationship.relationship_id, OperationResult.ERROR, str(e))
            return False
    
    async def get_cross_domain_relationships(self, element_id: str, 
                                           relationship_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取Element的跨领域关系
        
        Args:
            element_id: Element ID
            relationship_type: 关系类型过滤
            
        Returns:
            List[Dict]: 关系列表
        """
        try:
            async with self.db_pool.acquire() as conn:
                params = [element_id, element_id]
                where_clause = "(source_element_id = $1 OR target_element_id = $2) AND status = 'active'"
                
                if relationship_type:
                    where_clause += " AND relationship_type = $3"
                    params.append(relationship_type)
                
                sql = f"""
                    SELECT * FROM core_schema.cross_domain_relationships 
                    WHERE {where_clause}
                    ORDER BY created_at DESC
                """
                
                rows = await conn.fetch(sql, *params)
                return [dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"获取跨领域关系失败 {element_id}: {e}")
            return []
    
    async def get_domain_registry(self) -> List[Dict[str, Any]]:
        """获取所有已注册的领域"""
        try:
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(
                    "SELECT * FROM core_schema.domain_registry WHERE is_active = true ORDER BY domain_name"
                )
                return [dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"获取领域注册表失败: {e}")
            return []
    
    async def register_domain(self, domain_config: Dict[str, Any]) -> bool:
        """
        注册新领域
        
        Args:
            domain_config: 领域配置
            
        Returns:
            bool: 是否成功
        """
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO core_schema.domain_registry (
                        domain_name, schema_name, display_name, description,
                        domain_config, element_types, namespace_prefix, is_core_domain
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ON CONFLICT (domain_name) DO UPDATE SET
                        display_name = EXCLUDED.display_name,
                        description = EXCLUDED.description,
                        domain_config = EXCLUDED.domain_config,
                        element_types = EXCLUDED.element_types,
                        updated_at = CURRENT_TIMESTAMP
                """,
                    domain_config['domain_name'],
                    domain_config['schema_name'],
                    domain_config['display_name'],
                    domain_config['description'],
                    json.dumps(domain_config.get('domain_config', {})),
                    json.dumps(domain_config.get('element_types', [])),
                    domain_config.get('namespace_prefix'),
                    domain_config.get('is_core_domain', False)
                )
                
                await self._log_operation("register_domain", domain_config['domain_name'], OperationResult.SUCCESS)
                return True
                
        except Exception as e:
            self.logger.error(f"注册领域失败 {domain_config.get('domain_name')}: {e}")
            await self._log_operation("register_domain", domain_config.get('domain_name'), 
                                     OperationResult.ERROR, str(e))
            return False
    
    async def _log_operation(self, operation_type: str, target_element_id: Optional[str], 
                           result: OperationResult, error_message: Optional[str] = None,
                           user_id: Optional[str] = None, execution_time_ms: Optional[int] = None):
        """记录操作日志到系统日志表"""
        try:
            log_id = f"log_{uuid.uuid4().hex[:12]}"
            
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO core_schema.system_logs (
                        log_id, operation_type, target_schema, target_element_id,
                        operation_result, error_message, execution_time_ms, user_id
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                    log_id,
                    operation_type,
                    self.schema_name,
                    target_element_id,
                    result.value,
                    error_message,
                    execution_time_ms,
                    user_id
                )
                
        except Exception as e:
            # 日志记录失败不应该影响主要操作
            self.logger.warning(f"记录操作日志失败: {e}")

# 工厂函数
async def create_database_pool(database_url: str, min_size: int = 10, max_size: int = 20) -> asyncpg.Pool:
    """
    创建数据库连接池
    
    Args:
        database_url: 数据库连接URL
        min_size: 最小连接数
        max_size: 最大连接数
        
    Returns:
        asyncpg.Pool: 数据库连接池
    """
    return await asyncpg.create_pool(
        database_url,
        min_size=min_size,
        max_size=max_size,
        command_timeout=60
    )

def generate_element_id(prefix: str = "elem") -> str:
    """生成Element ID"""
    return f"{prefix}_{uuid.uuid4().hex[:12]}"

def generate_relationship_id(prefix: str = "rel") -> str:
    """生成关系ID"""
    return f"{prefix}_{uuid.uuid4().hex[:12]}" 