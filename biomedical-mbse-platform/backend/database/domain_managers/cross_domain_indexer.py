"""
跨域联动管理器 - 智能跨域关系和索引管理

功能：
1. 自动发现跨域关系模式
2. 动态创建跨域索引
3. 管理跨域引用字段
4. 优化跨域查询性能
5. 跨域数据一致性保证
"""

import asyncio
import json
import uuid
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict

import asyncpg

logger = logging.getLogger(__name__)

class CrossDomainRelationType(Enum):
    """跨域关系类型"""
    OWNERSHIP = "ownership"          # 所有权关系（如用户拥有项目）
    PARTICIPATION = "participation"  # 参与关系（如用户参与项目）
    DEPENDENCY = "dependency"        # 依赖关系（如模型依赖组件）
    REFERENCE = "reference"          # 引用关系（如模型引用生物通路）
    COLLABORATION = "collaboration"  # 协作关系（如用户协作项目）

class IndexStrategy(Enum):
    """索引策略"""
    EAGER = "eager"          # 立即创建索引
    LAZY = "lazy"            # 懒加载索引
    ADAPTIVE = "adaptive"    # 自适应索引
    MANUAL = "manual"        # 手动管理

@dataclass
class CrossDomainPattern:
    """跨域关系模式"""
    pattern_id: str
    source_domain: str
    target_domain: str
    relationship_type: CrossDomainRelationType
    frequency: int = 0
    performance_impact: float = 0.0
    suggested_indexes: List[str] = None

@dataclass
class CrossDomainIndex:
    """跨域索引定义"""
    index_id: str
    index_name: str
    source_schema: str
    target_schema: str
    indexed_fields: List[str]
    index_type: str = "btree"
    is_composite: bool = False
    usage_count: int = 0
    performance_gain: float = 0.0

class CrossDomainIndexer:
    """跨域联动管理器"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.relationship_patterns = {}
        self.active_indexes = {}
        
    async def analyze_cross_domain_patterns(self) -> List[CrossDomainPattern]:
        """
        分析跨域关系模式
        
        Returns:
            List[CrossDomainPattern]: 发现的跨域关系模式
        """
        try:
            async with self.db_pool.acquire() as conn:
                # 查询所有跨域关系
                relationships = await conn.fetch("""
                    SELECT 
                        source_domain,
                        target_domain,
                        relationship_type,
                        source_type,
                        target_type,
                        COUNT(*) as frequency,
                        AVG(strength) as avg_strength
                    FROM core_schema.cross_domain_relationships 
                    WHERE status = 'active'
                    GROUP BY source_domain, target_domain, relationship_type, source_type, target_type
                    ORDER BY frequency DESC
                """)
                
                patterns = []
                for row in relationships:
                    pattern = CrossDomainPattern(
                        pattern_id=f"pattern_{uuid.uuid4().hex[:8]}",
                        source_domain=row['source_domain'],
                        target_domain=row['target_domain'],
                        relationship_type=CrossDomainRelationType(row['relationship_type']) 
                            if row['relationship_type'] in [e.value for e in CrossDomainRelationType] 
                            else CrossDomainRelationType.REFERENCE,
                        frequency=row['frequency']
                    )
                    
                    # 分析性能影响
                    pattern.performance_impact = await self._calculate_performance_impact(
                        conn, pattern
                    )
                    
                    # 建议索引
                    pattern.suggested_indexes = await self._suggest_indexes_for_pattern(
                        conn, pattern
                    )
                    
                    patterns.append(pattern)
                    
                self.logger.info(f"发现 {len(patterns)} 个跨域关系模式")
                return patterns
                
        except Exception as e:
            self.logger.error(f"分析跨域关系模式失败: {e}")
            return []
    
    async def create_cross_domain_indexes(self, patterns: List[CrossDomainPattern], 
                                        strategy: IndexStrategy = IndexStrategy.ADAPTIVE) -> List[CrossDomainIndex]:
        """
        根据模式创建跨域索引
        
        Args:
            patterns: 跨域关系模式
            strategy: 索引策略
            
        Returns:
            List[CrossDomainIndex]: 创建的索引列表
        """
        created_indexes = []
        
        for pattern in patterns:
            if self._should_create_index(pattern, strategy):
                indexes = await self._create_pattern_indexes(pattern)
                created_indexes.extend(indexes)
        
        self.logger.info(f"创建了 {len(created_indexes)} 个跨域索引")
        return created_indexes
    
    async def optimize_cross_domain_queries(self) -> Dict[str, Any]:
        """
        优化跨域查询性能
        
        Returns:
            Dict[str, Any]: 优化结果统计
        """
        optimization_stats = {
            "analyzed_queries": 0,
            "created_indexes": 0,
            "performance_improvements": [],
            "recommendations": []
        }
        
        try:
            async with self.db_pool.acquire() as conn:
                # 1. 分析慢查询日志（如果有的话）
                slow_queries = await self._analyze_slow_queries(conn)
                optimization_stats["analyzed_queries"] = len(slow_queries)
                
                # 2. 为高频跨域查询创建优化索引
                for query_pattern in slow_queries:
                    if await self._is_cross_domain_query(query_pattern):
                        index = await self._create_optimization_index(conn, query_pattern)
                        if index:
                            optimization_stats["created_indexes"] += 1
                            optimization_stats["performance_improvements"].append({
                                "query_pattern": query_pattern["pattern"],
                                "index_name": index.index_name,
                                "estimated_improvement": index.performance_gain
                            })
                
                # 3. 生成优化建议
                recommendations = await self._generate_optimization_recommendations(conn)
                optimization_stats["recommendations"] = recommendations
                
        except Exception as e:
            self.logger.error(f"跨域查询优化失败: {e}")
            
        return optimization_stats
    
    async def manage_cross_domain_references(self, element_type_def: Dict[str, Any]) -> bool:
        """
        管理Element类型的跨域引用字段
        
        Args:
            element_type_def: Element类型定义
            
        Returns:
            bool: 是否成功
        """
        try:
            cross_domain_refs = element_type_def.get('cross_domain_refs', [])
            if not cross_domain_refs:
                return True
            
            async with self.db_pool.acquire() as conn:
                schema_name = element_type_def['domain_schema']
                table_name = element_type_def['table_name']
                
                for ref_field in cross_domain_refs:
                    success = await self._create_cross_domain_reference_field(
                        conn, schema_name, table_name, ref_field, element_type_def
                    )
                    if not success:
                        self.logger.warning(f"创建跨域引用字段失败: {ref_field}")
                
                # 创建跨域引用索引
                await self._create_cross_domain_reference_indexes(
                    conn, schema_name, table_name, cross_domain_refs
                )
                
            return True
            
        except Exception as e:
            self.logger.error(f"管理跨域引用失败: {e}")
            return False
    
    async def auto_discover_cross_domain_relationships(self) -> List[Dict[str, Any]]:
        """
        自动发现潜在的跨域关系
        
        Returns:
            List[Dict[str, Any]]: 发现的潜在关系
        """
        discovered_relationships = []
        
        try:
            async with self.db_pool.acquire() as conn:
                # 1. 基于命名模式发现关系
                naming_relationships = await self._discover_by_naming_patterns(conn)
                discovered_relationships.extend(naming_relationships)
                
                # 2. 基于数据模式发现关系
                data_relationships = await self._discover_by_data_patterns(conn)
                discovered_relationships.extend(data_relationships)
                
                # 3. 基于语义标签发现关系
                semantic_relationships = await self._discover_by_semantic_patterns(conn)
                discovered_relationships.extend(semantic_relationships)
                
                self.logger.info(f"自动发现 {len(discovered_relationships)} 个潜在跨域关系")
                
        except Exception as e:
            self.logger.error(f"自动发现跨域关系失败: {e}")
            
        return discovered_relationships
    
    async def _calculate_performance_impact(self, conn: asyncpg.Connection, 
                                          pattern: CrossDomainPattern) -> float:
        """计算跨域关系模式的性能影响"""
        try:
            # 基于频率和复杂度计算性能影响
            base_impact = pattern.frequency * 0.1
            
            # 查询涉及的表数量
            table_count = await conn.fetchval("""
                SELECT COUNT(DISTINCT CONCAT(source_domain, ':', target_domain))
                FROM core_schema.cross_domain_relationships
                WHERE source_domain = $1 AND target_domain = $2
            """, pattern.source_domain, pattern.target_domain)
            
            complexity_factor = min(table_count * 0.2, 2.0)
            
            return min(base_impact * complexity_factor, 10.0)
            
        except Exception as e:
            self.logger.warning(f"计算性能影响失败: {e}")
            return 1.0
    
    async def _suggest_indexes_for_pattern(self, conn: asyncpg.Connection, 
                                          pattern: CrossDomainPattern) -> List[str]:
        """为跨域关系模式建议索引"""
        suggestions = []
        
        # 基于关系类型建议不同的索引策略
        if pattern.relationship_type == CrossDomainRelationType.OWNERSHIP:
            suggestions.extend([
                f"idx_{pattern.source_domain}_{pattern.target_domain}_ownership",
                f"idx_{pattern.target_domain}_{pattern.source_domain}_owned_by"
            ])
        elif pattern.relationship_type == CrossDomainRelationType.PARTICIPATION:
            suggestions.extend([
                f"idx_{pattern.source_domain}_{pattern.target_domain}_participation",
                f"idx_participation_active_users"
            ])
        elif pattern.relationship_type == CrossDomainRelationType.REFERENCE:
            suggestions.append(f"idx_{pattern.source_domain}_{pattern.target_domain}_refs")
        
        return suggestions
    
    def _should_create_index(self, pattern: CrossDomainPattern, strategy: IndexStrategy) -> bool:
        """判断是否应该为模式创建索引"""
        if strategy == IndexStrategy.MANUAL:
            return False
        
        if strategy == IndexStrategy.EAGER:
            return True
        
        if strategy == IndexStrategy.LAZY:
            return pattern.frequency > 10
        
        if strategy == IndexStrategy.ADAPTIVE:
            return (pattern.frequency > 5 and pattern.performance_impact > 2.0)
        
        return False
    
    async def _create_pattern_indexes(self, pattern: CrossDomainPattern) -> List[CrossDomainIndex]:
        """为模式创建索引"""
        created_indexes = []
        
        try:
            async with self.db_pool.acquire() as conn:
                for suggested_index in pattern.suggested_indexes:
                    index = await self._create_single_cross_domain_index(
                        conn, pattern, suggested_index
                    )
                    if index:
                        created_indexes.append(index)
                        
        except Exception as e:
            self.logger.error(f"创建模式索引失败: {e}")
            
        return created_indexes
    
    async def _create_single_cross_domain_index(self, conn: asyncpg.Connection,
                                               pattern: CrossDomainPattern, 
                                               index_name: str) -> Optional[CrossDomainIndex]:
        """创建单个跨域索引"""
        try:
            index_id = f"idx_{uuid.uuid4().hex[:8]}"
            
            # 根据关系类型确定索引字段
            if "ownership" in index_name:
                indexed_fields = ["source_element_id", "relationship_type"]
            elif "participation" in index_name:
                indexed_fields = ["source_element_id", "target_element_id", "relationship_type"]
            else:
                indexed_fields = ["source_element_id", "target_element_id"]
            
            # 创建索引
            sql = f"""
                CREATE INDEX IF NOT EXISTS {index_name}
                ON core_schema.cross_domain_relationships ({', '.join(indexed_fields)})
                WHERE source_domain = '{pattern.source_domain}' 
                  AND target_domain = '{pattern.target_domain}'
                  AND status = 'active'
            """
            
            await conn.execute(sql)
            
            # 记录索引信息
            await conn.execute("""
                INSERT INTO core_schema.cross_domain_indexes (
                    index_id, index_name, source_schema, target_schema,
                    indexed_fields, index_type, auto_created
                ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (index_name) DO NOTHING
            """, 
                index_id, index_name, 
                f"{pattern.source_domain}_schema", f"{pattern.target_domain}_schema",
                json.dumps(indexed_fields), "btree", True
            )
            
            index = CrossDomainIndex(
                index_id=index_id,
                index_name=index_name,
                source_schema=f"{pattern.source_domain}_schema",
                target_schema=f"{pattern.target_domain}_schema",
                indexed_fields=indexed_fields,
                performance_gain=pattern.performance_impact * 0.3
            )
            
            self.logger.info(f"跨域索引创建成功: {index_name}")
            return index
            
        except Exception as e:
            self.logger.error(f"创建跨域索引失败 {index_name}: {e}")
            return None
    
    async def _analyze_slow_queries(self, conn: asyncpg.Connection) -> List[Dict[str, Any]]:
        """分析慢查询（模拟实现）"""
        # 在实际实现中，这里会分析PostgreSQL的查询日志
        # 目前返回一些模拟的慢查询模式
        return [
            {
                "pattern": "cross_domain_join",
                "domains": ["security", "modeling"],
                "frequency": 50,
                "avg_duration_ms": 1200
            },
            {
                "pattern": "cross_domain_aggregation", 
                "domains": ["modeling", "biomedical"],
                "frequency": 30,
                "avg_duration_ms": 800
            }
        ]
    
    async def _is_cross_domain_query(self, query_pattern: Dict[str, Any]) -> bool:
        """判断是否为跨域查询"""
        return len(query_pattern.get("domains", [])) > 1
    
    async def _create_optimization_index(self, conn: asyncpg.Connection,
                                        query_pattern: Dict[str, Any]) -> Optional[CrossDomainIndex]:
        """为查询模式创建优化索引"""
        try:
            domains = query_pattern["domains"]
            index_name = f"opt_idx_{'_'.join(domains)}_{uuid.uuid4().hex[:6]}"
            
            # 创建复合索引来优化跨域查询
            sql = f"""
                CREATE INDEX IF NOT EXISTS {index_name}
                ON core_schema.cross_domain_relationships (source_domain, target_domain, relationship_type, source_element_id)
                WHERE source_domain = ANY(ARRAY{domains}) AND target_domain = ANY(ARRAY{domains})
            """
            
            await conn.execute(sql)
            
            return CrossDomainIndex(
                index_id=f"opt_{uuid.uuid4().hex[:8]}",
                index_name=index_name,
                source_schema="multi",
                target_schema="multi", 
                indexed_fields=["source_domain", "target_domain", "relationship_type", "source_element_id"],
                is_composite=True,
                performance_gain=query_pattern["avg_duration_ms"] * 0.4 / 1000
            )
            
        except Exception as e:
            self.logger.error(f"创建优化索引失败: {e}")
            return None
    
    async def _generate_optimization_recommendations(self, conn: asyncpg.Connection) -> List[Dict[str, Any]]:
        """生成优化建议"""
        recommendations = []
        
        try:
            # 查询高频但未优化的跨域关系
            high_frequency_relations = await conn.fetch("""
                SELECT 
                    source_domain,
                    target_domain,
                    relationship_type,
                    COUNT(*) as frequency
                FROM core_schema.cross_domain_relationships
                WHERE status = 'active'
                GROUP BY source_domain, target_domain, relationship_type
                HAVING COUNT(*) > 20
                ORDER BY frequency DESC
                LIMIT 10
            """)
            
            for row in high_frequency_relations:
                recommendations.append({
                    "type": "create_index",
                    "priority": "high" if row['frequency'] > 100 else "medium",
                    "description": f"为 {row['source_domain']} -> {row['target_domain']} 关系创建索引",
                    "frequency": row['frequency'],
                    "estimated_benefit": "20-40% query performance improvement"
                })
            
            # 检查缺失的跨域引用字段
            missing_refs = await self._find_missing_cross_domain_references(conn)
            for ref in missing_refs:
                recommendations.append({
                    "type": "add_reference_field",
                    "priority": "medium",
                    "description": f"在 {ref['schema']}.{ref['table']} 中添加 {ref['field']} 引用字段",
                    "benefit": "Improved query efficiency and data consistency"
                })
                
        except Exception as e:
            self.logger.error(f"生成优化建议失败: {e}")
            
        return recommendations
    
    async def _create_cross_domain_reference_field(self, conn: asyncpg.Connection,
                                                  schema_name: str, table_name: str,
                                                  ref_field: str, element_type_def: Dict[str, Any]) -> bool:
        """创建跨域引用字段"""
        try:
            # 解析引用字段配置
            if isinstance(ref_field, str):
                field_name = f"{ref_field}_refs"
                field_type = "JSONB"
            else:
                field_name = ref_field.get('name', 'cross_domain_refs')
                field_type = ref_field.get('type', 'JSONB')
            
            # 添加字段（如果不存在）
            sql = f"""
                ALTER TABLE {schema_name}.{table_name} 
                ADD COLUMN IF NOT EXISTS {field_name} {field_type} DEFAULT '{{}}'
            """
            
            await conn.execute(sql)
            self.logger.info(f"跨域引用字段创建成功: {schema_name}.{table_name}.{field_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建跨域引用字段失败: {e}")
            return False
    
    async def _create_cross_domain_reference_indexes(self, conn: asyncpg.Connection,
                                                   schema_name: str, table_name: str,
                                                   cross_domain_refs: List[str]):
        """为跨域引用字段创建索引"""
        for ref_field in cross_domain_refs:
            try:
                if isinstance(ref_field, str):
                    field_name = f"{ref_field}_refs"
                else:
                    field_name = ref_field.get('name', 'cross_domain_refs')
                
                index_name = f"idx_{table_name}_{field_name}"
                
                # 为JSONB字段创建GIN索引
                sql = f"""
                    CREATE INDEX IF NOT EXISTS {index_name}
                    ON {schema_name}.{table_name} USING GIN ({field_name})
                """
                
                await conn.execute(sql)
                self.logger.info(f"跨域引用索引创建成功: {index_name}")
                
            except Exception as e:
                self.logger.warning(f"创建跨域引用索引失败: {e}")
    
    async def _discover_by_naming_patterns(self, conn: asyncpg.Connection) -> List[Dict[str, Any]]:
        """基于命名模式发现关系"""
        relationships = []
        
        try:
            # 查找包含其他领域名称的Element
            elements = await conn.fetch("""
                SELECT em.element_id, em.domain_schema, em.local_name, em.semantic_tags
                FROM core_schema.element_metadata em
                WHERE em.status = 'active'
            """)
            
            domain_keywords = {
                'security': ['user', 'auth', 'login', 'permission'],
                'modeling': ['project', 'model', 'component', 'design'],
                'biomedical': ['bio', 'protein', 'gene', 'pathway', 'organism']
            }
            
            for element in elements:
                current_domain = element['domain_schema'].replace('_schema', '')
                local_name = element['local_name'].lower()
                
                for domain, keywords in domain_keywords.items():
                    if domain != current_domain:
                        for keyword in keywords:
                            if keyword in local_name:
                                relationships.append({
                                    "type": "naming_pattern",
                                    "source_element": element['element_id'],
                                    "source_domain": current_domain,
                                    "target_domain": domain,
                                    "confidence": 0.7,
                                    "pattern": f"名称包含{domain}领域关键词: {keyword}"
                                })
                                
        except Exception as e:
            self.logger.error(f"基于命名模式发现关系失败: {e}")
            
        return relationships
    
    async def _discover_by_data_patterns(self, conn: asyncpg.Connection) -> List[Dict[str, Any]]:
        """基于数据模式发现关系"""
        relationships = []
        
        try:
            # 查找跨域Element ID引用模式
            # 这里可以分析各个Schema中是否有字段值匹配其他Schema的Element ID
            # 这是一个简化的实现
            pass
            
        except Exception as e:
            self.logger.error(f"基于数据模式发现关系失败: {e}")
            
        return relationships
    
    async def _discover_by_semantic_patterns(self, conn: asyncpg.Connection) -> List[Dict[str, Any]]:
        """基于语义标签发现关系"""
        relationships = []
        
        try:
            # 查找具有相似语义标签的跨域Elements
            elements_with_tags = await conn.fetch("""
                SELECT element_id, domain_schema, semantic_tags
                FROM core_schema.element_metadata 
                WHERE status = 'active' AND semantic_tags IS NOT NULL
            """)
            
            # 分析语义标签重叠度
            semantic_map = defaultdict(list)
            for element in elements_with_tags:
                domain = element['domain_schema'].replace('_schema', '')
                tags = json.loads(element['semantic_tags']) if element['semantic_tags'] else []
                
                for tag in tags:
                    semantic_map[tag].append({
                        'element_id': element['element_id'],
                        'domain': domain
                    })
            
            # 找出跨域的共同语义标签
            for tag, elements in semantic_map.items():
                domains = set(e['domain'] for e in elements)
                if len(domains) > 1:
                    for i, elem1 in enumerate(elements):
                        for elem2 in elements[i+1:]:
                            if elem1['domain'] != elem2['domain']:
                                relationships.append({
                                    "type": "semantic_pattern",
                                    "source_element": elem1['element_id'],
                                    "target_element": elem2['element_id'], 
                                    "source_domain": elem1['domain'],
                                    "target_domain": elem2['domain'],
                                    "confidence": 0.6,
                                    "pattern": f"共同语义标签: {tag}"
                                })
                                
        except Exception as e:
            self.logger.error(f"基于语义模式发现关系失败: {e}")
            
        return relationships
    
    async def _find_missing_cross_domain_references(self, conn: asyncpg.Connection) -> List[Dict[str, Any]]:
        """查找缺失的跨域引用字段"""
        missing_refs = []
        
        try:
            # 查询频繁的跨域关系，但没有对应引用字段的情况
            frequent_relations = await conn.fetch("""
                SELECT 
                    source_domain,
                    target_domain,
                    source_type,
                    COUNT(*) as frequency
                FROM core_schema.cross_domain_relationships
                WHERE status = 'active'
                GROUP BY source_domain, target_domain, source_type
                HAVING COUNT(*) > 10
            """)
            
            for relation in frequent_relations:
                # 检查源类型是否有对应的跨域引用字段
                type_def = await conn.fetchrow("""
                    SELECT cross_domain_refs, table_name, domain_schema
                    FROM core_schema.element_type_definitions
                    WHERE type_id = $1
                """, relation['source_type'])
                
                if type_def:
                    refs = json.loads(type_def['cross_domain_refs']) if type_def['cross_domain_refs'] else []
                    target_ref = f"{relation['target_domain']}_refs"
                    
                    if target_ref not in refs and not any(target_ref in str(ref) for ref in refs):
                        missing_refs.append({
                            "schema": type_def['domain_schema'],
                            "table": type_def['table_name'],
                            "field": target_ref,
                            "frequency": relation['frequency']
                        })
                        
        except Exception as e:
            self.logger.error(f"查找缺失跨域引用失败: {e}")
            
        return missing_refs 