"""
领域工厂 - 动态领域创建和管理的统一入口

功能：
1. 动态创建新的业务领域
2. 整合Schema生成器和跨域索引器
3. 提供领域模板系统
4. 管理领域生命周期
5. 自动优化跨域性能
"""

import asyncio
import json
import uuid
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum

import asyncpg

from schemas.dynamic.dynamic_schema_generator import DynamicSchemaGenerator, FieldType
from .cross_domain_indexer import CrossDomainIndexer, IndexStrategy, CrossDomainRelationType
from .core_domain_manager import CoreDomainManager, ElementMetadata

logger = logging.getLogger(__name__)

class DomainType(Enum):
    """领域类型"""
    BUSINESS = "business"         # 业务领域
    TECHNICAL = "technical"       # 技术领域  
    DOMAIN_SPECIFIC = "domain_specific"  # 特定领域
    INTEGRATION = "integration"   # 集成领域

class DomainStatus(Enum):
    """领域状态"""
    DESIGNING = "designing"       # 设计中
    CREATING = "creating"        # 创建中
    ACTIVE = "active"            # 活跃
    DEPRECATED = "deprecated"    # 已弃用
    MIGRATING = "migrating"      # 迁移中

@dataclass
class DomainTemplate:
    """领域模板"""
    template_id: str
    template_name: str
    domain_type: DomainType
    description: str
    element_types: List[Dict[str, Any]]
    default_relationships: List[Dict[str, Any]]
    suggested_cross_domain_refs: List[str]
    configuration: Dict[str, Any]

@dataclass 
class DomainCreationRequest:
    """领域创建请求"""
    domain_name: str
    display_name: str
    description: str
    domain_type: DomainType
    template_id: Optional[str] = None
    custom_element_types: List[Dict[str, Any]] = None
    cross_domain_connections: List[str] = None
    auto_optimize: bool = True
    namespace_prefix: str = None

@dataclass
class DomainCreationResult:
    """领域创建结果"""
    success: bool
    domain_name: str
    schema_name: str
    created_tables: List[str]
    created_indexes: List[str]
    cross_domain_relationships: List[str]
    warnings: List[str]
    errors: List[str]
    performance_baseline: Dict[str, Any]

class DomainFactory:
    """领域工厂 - 动态领域创建和管理"""
    
    def __init__(self, db_pool: asyncpg.Pool, core_manager: CoreDomainManager):
        self.db_pool = db_pool
        self.core_manager = core_manager
        self.schema_generator = DynamicSchemaGenerator(db_pool)
        self.cross_domain_indexer = CrossDomainIndexer(db_pool)
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        
        # 内置领域模板
        self.built_in_templates = {}
        self._initialize_built_in_templates()
    
    async def create_domain(self, request: DomainCreationRequest) -> DomainCreationResult:
        """
        创建新领域
        
        Args:
            request: 领域创建请求
            
        Returns:
            DomainCreationResult: 创建结果
        """
        result = DomainCreationResult(
            success=False,
            domain_name=request.domain_name,
            schema_name=f"{request.domain_name}_schema",
            created_tables=[],
            created_indexes=[],
            cross_domain_relationships=[],
            warnings=[],
            errors=[]
        )
        
        try:
            self.logger.info(f"开始创建领域: {request.domain_name}")
            
            # 1. 验证和准备
            validation_result = await self._validate_domain_request(request)
            if not validation_result['valid']:
                result.errors.extend(validation_result['errors'])
                return result
            
            # 2. 获取或生成Element类型定义
            element_types = await self._prepare_element_types(request)
            if not element_types:
                result.errors.append("无法准备Element类型定义")
                return result
            
            # 3. 注册领域到核心
            domain_config = await self._prepare_domain_config(request, element_types)
            success = await self.core_manager.register_domain(domain_config)
            if not success:
                result.errors.append("领域注册失败")
                return result
            
            # 4. 创建Schema和表结构
            schema_success, schema_message = await self.schema_generator.create_domain_schema(
                request.domain_name, result.schema_name, element_types
            )
            if not schema_success:
                result.errors.append(f"Schema创建失败: {schema_message}")
                return result
            
            # 5. 设置跨域引用和索引
            if request.auto_optimize:
                await self._setup_cross_domain_optimizations(request, element_types, result)
            
            # 6. 建立预定义的跨域关系
            if request.cross_domain_connections:
                await self._establish_cross_domain_connections(request, result)
            
            # 7. 性能基线测试
            if request.auto_optimize:
                result.performance_baseline = await self._establish_performance_baseline(request.domain_name)
            
            # 8. 获取创建统计
            await self._populate_creation_statistics(result)
            
            result.success = True
            self.logger.info(f"领域创建成功: {request.domain_name}")
            
        except Exception as e:
            self.logger.error(f"领域创建失败 {request.domain_name}: {e}")
            result.errors.append(str(e))
        
        return result
    
    async def add_element_type_to_domain(self, domain_name: str, 
                                       element_type_def: Dict[str, Any]) -> Tuple[bool, str]:
        """
        向现有领域添加新的Element类型
        
        Args:
            domain_name: 领域名称
            element_type_def: Element类型定义
            
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息)
        """
        try:
            # 1. 验证Element类型定义
            validation_result = self._validate_element_type_definition(element_type_def)
            if not validation_result['valid']:
                return False, f"Element类型定义验证失败: {validation_result['errors']}"
            
            # 2. 设置领域信息
            element_type_def['domain_schema'] = f"{domain_name}_schema"
            
            # 3. 创建表和索引
            success, message = await self.schema_generator.add_element_type(domain_name, element_type_def)
            if not success:
                return False, message
            
            # 4. 设置跨域引用
            if element_type_def.get('cross_domain_refs'):
                await self.cross_domain_indexer.manage_cross_domain_references(element_type_def)
            
            # 5. 自动发现和创建跨域关系
            await self._auto_establish_relationships(element_type_def)
            
            self.logger.info(f"Element类型添加成功: {element_type_def['type_id']}")
            return True, f"Element类型 {element_type_def['type_id']} 添加成功"
            
        except Exception as e:
            self.logger.error(f"添加Element类型失败: {e}")
            return False, str(e)
    
    async def optimize_domain_performance(self, domain_name: str) -> Dict[str, Any]:
        """
        优化领域性能
        
        Args:
            domain_name: 领域名称
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        optimization_result = {
            "domain_name": domain_name,
            "optimization_applied": [],
            "performance_improvements": {},
            "recommendations": []
        }
        
        try:
            # 1. 分析跨域关系模式
            patterns = await self.cross_domain_indexer.analyze_cross_domain_patterns()
            domain_patterns = [p for p in patterns if 
                             p.source_domain == domain_name or p.target_domain == domain_name]
            
            if domain_patterns:
                # 2. 创建优化索引
                created_indexes = await self.cross_domain_indexer.create_cross_domain_indexes(
                    domain_patterns, IndexStrategy.ADAPTIVE
                )
                optimization_result["optimization_applied"].append(f"创建了 {len(created_indexes)} 个跨域索引")
            
            # 3. 全局跨域查询优化
            query_optimization = await self.cross_domain_indexer.optimize_cross_domain_queries()
            optimization_result["optimization_applied"].append(f"优化了 {query_optimization['analyzed_queries']} 个查询")
            optimization_result["recommendations"].extend(query_optimization['recommendations'])
            
            # 4. 自动发现潜在关系
            discovered_relationships = await self.cross_domain_indexer.auto_discover_cross_domain_relationships()
            if discovered_relationships:
                optimization_result["recommendations"].append({
                    "type": "potential_relationships",
                    "count": len(discovered_relationships),
                    "description": "发现了潜在的跨域关系，建议手动验证并建立"
                })
            
            self.logger.info(f"领域性能优化完成: {domain_name}")
            
        except Exception as e:
            self.logger.error(f"领域性能优化失败 {domain_name}: {e}")
            optimization_result["error"] = str(e)
        
        return optimization_result
    
    async def get_domain_health_report(self, domain_name: str) -> Dict[str, Any]:
        """
        获取领域健康报告
        
        Args:
            domain_name: 领域名称
            
        Returns:
            Dict[str, Any]: 健康报告
        """
        report = {
            "domain_name": domain_name,
            "status": "unknown",
            "element_statistics": {},
            "relationship_statistics": {},
            "performance_metrics": {},
            "recommendations": [],
            "issues": []
        }
        
        try:
            async with self.db_pool.acquire() as conn:
                # 1. 基本状态信息
                domain_info = await conn.fetchrow("""
                    SELECT dr.*, ds.creation_status, ds.created_tables, ds.created_indexes
                    FROM core_schema.domain_registry dr
                    LEFT JOIN core_schema.dynamic_schemas ds ON dr.schema_name = ds.schema_name
                    WHERE dr.domain_name = $1
                """, domain_name)
                
                if not domain_info:
                    report["status"] = "not_found"
                    report["issues"].append("领域不存在")
                    return report
                
                report["status"] = domain_info['creation_status'] or "active"
                
                # 2. Element统计
                element_stats = await conn.fetch("""
                    SELECT em.element_type, COUNT(*) as count, em.status
                    FROM core_schema.element_metadata em
                    WHERE em.domain_schema = $1
                    GROUP BY em.element_type, em.status
                """, f"{domain_name}_schema")
                
                report["element_statistics"] = {
                    row['element_type']: {"count": row['count'], "status": row['status']}
                    for row in element_stats
                }
                
                # 3. 跨域关系统计
                relationship_stats = await conn.fetch("""
                    SELECT target_domain, relationship_type, COUNT(*) as count
                    FROM core_schema.cross_domain_relationships
                    WHERE source_domain = $1 AND status = 'active'
                    GROUP BY target_domain, relationship_type
                """, domain_name)
                
                report["relationship_statistics"] = {
                    f"{row['target_domain']}_{row['relationship_type']}": row['count']
                    for row in relationship_stats
                }
                
                # 4. 性能指标
                await self._collect_performance_metrics(conn, domain_name, report)
                
                # 5. 健康检查和建议
                await self._generate_health_recommendations(conn, domain_name, report)
                
        except Exception as e:
            self.logger.error(f"获取领域健康报告失败 {domain_name}: {e}")
            report["issues"].append(f"报告生成失败: {e}")
        
        return report
    
    def get_available_templates(self) -> List[DomainTemplate]:
        """获取可用的领域模板"""
        return list(self.built_in_templates.values())
    
    def _initialize_built_in_templates(self):
        """初始化内置领域模板"""
        
        # 数据分析领域模板
        self.built_in_templates['data_analytics'] = DomainTemplate(
            template_id='data_analytics',
            template_name='数据分析领域',
            domain_type=DomainType.TECHNICAL,
            description='用于数据科学和分析的领域模板',
            element_types=[
                {
                    'type_id': 'dataset_element',
                    'type_name': 'Dataset Element',
                    'table_name': 'dataset_elements',
                    'field_definitions': {
                        'dataset_name': {'type': 'string', 'required': True, 'indexed': True},
                        'description': {'type': 'text'},
                        'data_format': {'type': 'string', 'default': 'csv'},
                        'size_bytes': {'type': 'bigint'},
                        'metadata': {'type': 'jsonb', 'default': '{}'},
                        'is_public': {'type': 'boolean', 'default': False}
                    },
                    'cross_domain_refs': ['security_refs', 'modeling_refs']
                },
                {
                    'type_id': 'analysis_element',
                    'type_name': 'Analysis Element', 
                    'table_name': 'analysis_elements',
                    'field_definitions': {
                        'analysis_name': {'type': 'string', 'required': True},
                        'analysis_type': {'type': 'string', 'required': True},
                        'parameters': {'type': 'jsonb', 'default': '{}'},
                        'results': {'type': 'jsonb', 'default': '{}'},
                        'status': {'type': 'string', 'default': 'pending'}
                    },
                    'relationship_definitions': {
                        'datasets': {'type': 'many_to_many', 'target': 'dataset_element'}
                    }
                }
            ],
            default_relationships=[
                {'type': 'ownership', 'source': 'user_element', 'target': 'dataset_element'},
                {'type': 'dependency', 'source': 'analysis_element', 'target': 'dataset_element'}
            ],
            suggested_cross_domain_refs=['security', 'modeling'],
            configuration={
                'auto_create_indexes': True,
                'enable_versioning': True,
                'cache_policy': 'aggressive'
            }
        )
        
        # 工作流领域模板
        self.built_in_templates['workflow'] = DomainTemplate(
            template_id='workflow',
            template_name='工作流领域',
            domain_type=DomainType.BUSINESS,
            description='用于业务流程管理的领域模板',
            element_types=[
                {
                    'type_id': 'process_element',
                    'type_name': 'Process Element',
                    'table_name': 'process_elements',
                    'field_definitions': {
                        'process_name': {'type': 'string', 'required': True, 'indexed': True},
                        'description': {'type': 'text'},
                        'process_definition': {'type': 'jsonb', 'required': True},
                        'version': {'type': 'string', 'default': '1.0'},
                        'is_active': {'type': 'boolean', 'default': True}
                    },
                    'cross_domain_refs': ['security_refs', 'modeling_refs']
                },
                {
                    'type_id': 'task_element',
                    'type_name': 'Task Element',
                    'table_name': 'task_elements', 
                    'field_definitions': {
                        'task_name': {'type': 'string', 'required': True},
                        'task_type': {'type': 'string', 'required': True},
                        'assignee_id': {'type': 'string'},
                        'status': {'type': 'string', 'default': 'pending'},
                        'due_date': {'type': 'timestamp'},
                        'priority': {'type': 'integer', 'default': 1}
                    },
                    'relationship_definitions': {
                        'process': {'type': 'many_to_one', 'target': 'process_element'}
                    },
                    'cross_domain_refs': ['security_refs']
                }
            ],
            default_relationships=[
                {'type': 'ownership', 'source': 'user_element', 'target': 'process_element'},
                {'type': 'participation', 'source': 'user_element', 'target': 'task_element'}
            ],
            suggested_cross_domain_refs=['security', 'modeling'],
            configuration={
                'auto_create_indexes': True,
                'enable_audit': True,
                'supports_versioning': True
            }
        )
    
    async def _validate_domain_request(self, request: DomainCreationRequest) -> Dict[str, Any]:
        """验证领域创建请求"""
        result = {'valid': True, 'errors': [], 'warnings': []}
        
        # 检查领域名称
        if not request.domain_name or not request.domain_name.isidentifier():
            result['errors'].append("领域名称必须是有效的标识符")
            result['valid'] = False
        
        # 检查是否已存在
        try:
            async with self.db_pool.acquire() as conn:
                existing = await conn.fetchval(
                    "SELECT domain_name FROM core_schema.domain_registry WHERE domain_name = $1",
                    request.domain_name
                )
                if existing:
                    result['errors'].append(f"领域已存在: {request.domain_name}")
                    result['valid'] = False
        except Exception as e:
            result['warnings'].append(f"无法检查领域是否已存在: {e}")
        
        # 检查命名空间前缀
        if request.namespace_prefix and not request.namespace_prefix.isalnum():
            result['errors'].append("命名空间前缀必须是字母数字组合")
            result['valid'] = False
        
        return result
    
    async def _prepare_element_types(self, request: DomainCreationRequest) -> List[Dict[str, Any]]:
        """准备Element类型定义"""
        element_types = []
        
        try:
            # 如果使用模板
            if request.template_id and request.template_id in self.built_in_templates:
                template = self.built_in_templates[request.template_id]
                element_types = template.element_types.copy()
                
                # 设置领域信息
                for element_type in element_types:
                    element_type['domain_schema'] = f"{request.domain_name}_schema"
            
            # 添加自定义Element类型
            if request.custom_element_types:
                for custom_type in request.custom_element_types:
                    custom_type['domain_schema'] = f"{request.domain_name}_schema"
                    element_types.append(custom_type)
            
            # 如果没有任何Element类型，创建默认的
            if not element_types:
                default_element = {
                    'type_id': f'{request.domain_name}_element',
                    'type_name': f'{request.display_name} Element',
                    'domain_schema': f"{request.domain_name}_schema",
                    'table_name': f'{request.domain_name}_elements',
                    'field_definitions': {
                        'name': {'type': 'string', 'required': True, 'indexed': True},
                        'description': {'type': 'text'},
                        'metadata': {'type': 'jsonb', 'default': '{}'}
                    },
                    'cross_domain_refs': request.cross_domain_connections or []
                }
                element_types.append(default_element)
            
        except Exception as e:
            self.logger.error(f"准备Element类型失败: {e}")
            return []
        
        return element_types
    
    async def _prepare_domain_config(self, request: DomainCreationRequest, 
                                   element_types: List[Dict[str, Any]]) -> Dict[str, Any]:
        """准备领域配置"""
        element_type_ids = [et['type_id'] for et in element_types]
        
        domain_config = {
            'domain_name': request.domain_name,
            'schema_name': f"{request.domain_name}_schema",
            'display_name': request.display_name,
            'description': request.description,
            'domain_config': {
                'domain_type': request.domain_type.value,
                'auto_optimize': request.auto_optimize,
                'supports_cross_domain': bool(request.cross_domain_connections)
            },
            'element_types': element_type_ids,
            'namespace_prefix': request.namespace_prefix or request.domain_name[:3],
            'is_core_domain': False,
            'is_auto_generated': True
        }
        
        return domain_config
    
    async def _setup_cross_domain_optimizations(self, request: DomainCreationRequest,
                                               element_types: List[Dict[str, Any]],
                                               result: DomainCreationResult):
        """设置跨域优化"""
        try:
            for element_type in element_types:
                if element_type.get('cross_domain_refs'):
                    await self.cross_domain_indexer.manage_cross_domain_references(element_type)
                    result.created_indexes.append(f"跨域引用索引: {element_type['type_id']}")
            
            # 分析并创建跨域索引
            patterns = await self.cross_domain_indexer.analyze_cross_domain_patterns()
            relevant_patterns = [p for p in patterns if 
                               p.source_domain == request.domain_name or p.target_domain == request.domain_name]
            
            if relevant_patterns:
                indexes = await self.cross_domain_indexer.create_cross_domain_indexes(
                    relevant_patterns, IndexStrategy.ADAPTIVE
                )
                result.created_indexes.extend([idx.index_name for idx in indexes])
                
        except Exception as e:
            result.warnings.append(f"跨域优化设置失败: {e}")
    
    async def _establish_cross_domain_connections(self, request: DomainCreationRequest,
                                                result: DomainCreationResult):
        """建立跨域连接"""
        # 这里可以根据request.cross_domain_connections自动建立一些预定义的关系
        # 实际实现会根据具体需求来定制
        pass
    
    async def _establish_performance_baseline(self, domain_name: str) -> Dict[str, Any]:
        """建立性能基线"""
        baseline = {
            "measurement_time": datetime.now(timezone.utc).isoformat(),
            "query_performance": {},
            "index_usage": {},
            "cross_domain_latency": {}
        }
        
        try:
            async with self.db_pool.acquire() as conn:
                # 简单的性能测试查询
                start_time = datetime.now()
                await conn.fetchval(f"SELECT COUNT(*) FROM core_schema.element_metadata WHERE domain_schema = $1", f"{domain_name}_schema")
                end_time = datetime.now()
                
                baseline["query_performance"]["basic_count"] = (end_time - start_time).total_seconds() * 1000
                
        except Exception as e:
            self.logger.warning(f"性能基线建立失败: {e}")
        
        return baseline
    
    async def _populate_creation_statistics(self, result: DomainCreationResult):
        """填充创建统计信息"""
        try:
            async with self.db_pool.acquire() as conn:
                # 获取创建的表和索引信息
                schema_info = await conn.fetchrow("""
                    SELECT created_tables, created_indexes 
                    FROM core_schema.dynamic_schemas 
                    WHERE schema_name = $1
                """, result.schema_name)
                
                if schema_info:
                    if schema_info['created_tables']:
                        result.created_tables = json.loads(schema_info['created_tables'])
                    if schema_info['created_indexes']:
                        result.created_indexes.extend(json.loads(schema_info['created_indexes']))
                        
        except Exception as e:
            result.warnings.append(f"获取创建统计失败: {e}")
    
    def _validate_element_type_definition(self, element_type_def: Dict[str, Any]) -> Dict[str, Any]:
        """验证Element类型定义"""
        result = {'valid': True, 'errors': []}
        
        required_fields = ['type_id', 'type_name', 'table_name', 'field_definitions']
        for field in required_fields:
            if field not in element_type_def:
                result['errors'].append(f"缺少必需字段: {field}")
                result['valid'] = False
        
        # 验证type_id格式
        if 'type_id' in element_type_def:
            if not element_type_def['type_id'].endswith('_element'):
                result['errors'].append("type_id必须以'_element'结尾")
                result['valid'] = False
        
        return result
    
    async def _auto_establish_relationships(self, element_type_def: Dict[str, Any]):
        """自动建立关系"""
        try:
            # 自动发现潜在的跨域关系
            discovered = await self.cross_domain_indexer.auto_discover_cross_domain_relationships()
            
            # 这里可以根据发现的关系自动建立一些连接
            # 实际实现会根据具体的业务逻辑来定制
            
        except Exception as e:
            self.logger.warning(f"自动建立关系失败: {e}")
    
    async def _collect_performance_metrics(self, conn: asyncpg.Connection, 
                                         domain_name: str, report: Dict[str, Any]):
        """收集性能指标"""
        try:
            # 查询响应时间统计
            # 这里可以从数据库的统计视图获取真实的性能数据
            report["performance_metrics"] = {
                "avg_query_time_ms": 50,  # 模拟数据
                "index_hit_ratio": 0.95,
                "cross_domain_query_count": 0
            }
            
        except Exception as e:
            report["issues"].append(f"性能指标收集失败: {e}")
    
    async def _generate_health_recommendations(self, conn: asyncpg.Connection,
                                             domain_name: str, report: Dict[str, Any]):
        """生成健康建议"""
        try:
            # 检查是否有未使用的索引
            # 检查是否有缺失的跨域引用
            # 检查性能瓶颈
            
            # 基于统计数据生成建议
            element_count = sum(stats.get('count', 0) for stats in report['element_statistics'].values())
            
            if element_count == 0:
                report["recommendations"].append({
                    "type": "warning",
                    "message": "领域中没有Element，建议添加一些数据"
                })
            elif element_count > 10000:
                report["recommendations"].append({
                    "type": "performance",
                    "message": "Element数量较多，建议考虑分区或归档策略"
                })
                
        except Exception as e:
            report["issues"].append(f"健康建议生成失败: {e}") 