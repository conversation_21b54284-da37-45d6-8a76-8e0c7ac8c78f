"""
安全领域管理器 - SecurityDomain的具体实现

提供用户、角色、权限的Element化管理功能
实现基于Element的用户认证和授权系统

注意：在新的动态架构中，安全领域应该通过DomainFactory创建，
而不是预先假设security_schema存在。
"""

import hashlib
import secrets
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone, timedelta
import json
import bcrypt

from .core_domain_manager import DomainManager, ElementMetadata, OperationResult, generate_element_id

class SecurityDomainManager(DomainManager):
    """安全领域管理器 - 适配动态架构"""
    
    def __init__(self, db_pool, core_manager, domain_factory=None):
        # 默认使用security域，但支持自定义
        super().__init__(db_pool, "security", "security_schema")
        self.core_manager = core_manager
        self.domain_factory = domain_factory
        self._is_domain_initialized = False
    
    async def ensure_domain_exists(self) -> bool:
        """确保安全领域存在，如果不存在则创建"""
        if self._is_domain_initialized:
            return True
            
        try:
            # 检查领域是否已存在
            async with self.db_pool.acquire() as conn:
                existing = await conn.fetchval(
                    "SELECT domain_name FROM core_schema.domain_registry WHERE domain_name = $1",
                    self.domain_name
                )
                
                if existing:
                    self._is_domain_initialized = True
                    return True
            
            # 如果有DomainFactory，尝试创建安全领域
            if self.domain_factory:
                success = await self._create_security_domain_via_factory()
                if success:
                    self._is_domain_initialized = True
                    return True
            
            # 回退：检查是否有静态Schema
            try:
                async with self.db_pool.acquire() as conn:
                    await conn.fetchval("SELECT 1 FROM security_schema.user_elements LIMIT 1")
                    self._is_domain_initialized = True
                    return True
            except:
                self.logger.warning("安全领域不存在且无法自动创建")
                return False
                
        except Exception as e:
            self.logger.error(f"确保安全领域存在失败: {e}")
            return False
    
    async def _create_security_domain_via_factory(self) -> bool:
        """通过DomainFactory创建安全领域"""
        try:
            from .domain_factory import DomainCreationRequest, DomainType
            
            # 定义安全领域的Element类型
            security_element_types = [
                {
                    'type_id': 'user_element',
                    'type_name': 'User Element',
                    'table_name': 'user_elements',
                    'field_definitions': {
                        'username': {'type': 'string', 'unique': True, 'required': True, 'max_length': 64},
                        'email': {'type': 'string', 'unique': True, 'required': True},
                        'password_hash': {'type': 'string', 'required': True},
                        'profile': {'type': 'jsonb', 'default': '{}'},
                        'preferences': {'type': 'jsonb', 'default': '{}'},
                        'security_config': {'type': 'jsonb', 'default': '{}'},
                        'is_active': {'type': 'boolean', 'default': True},
                        'is_verified': {'type': 'boolean', 'default': False},
                        'is_superuser': {'type': 'boolean', 'default': False},
                        'last_login': {'type': 'timestamp', 'nullable': True},
                        'login_count': {'type': 'integer', 'default': 0},
                        'failed_attempts': {'type': 'integer', 'default': 0},
                        'locked_until': {'type': 'timestamp', 'nullable': True}
                    },
                    'index_definitions': [
                        {'fields': ['username'], 'type': 'btree', 'unique': True},
                        {'fields': ['email'], 'type': 'btree', 'unique': True},
                        {'fields': ['is_active'], 'type': 'btree'},
                        {'fields': ['profile'], 'type': 'gin'}
                    ],
                    'cross_domain_refs': ['project_participations', 'model_ownerships']
                },
                {
                    'type_id': 'role_element',
                    'type_name': 'Role Element',
                    'table_name': 'role_elements',
                    'field_definitions': {
                        'role_name': {'type': 'string', 'unique': True, 'required': True},
                        'display_name': {'type': 'string', 'required': True},
                        'description': {'type': 'text', 'nullable': True},
                        'role_config': {'type': 'jsonb', 'default': '{}'},
                        'permissions': {'type': 'jsonb', 'default': '[]'},
                        'permission_policies': {'type': 'jsonb', 'default': '{}'},
                        'parent_role_id': {'type': 'string', 'nullable': True},
                        'role_level': {'type': 'integer', 'default': 0},
                        'role_hierarchy_path': {'type': 'string', 'nullable': True},
                        'scope_config': {'type': 'jsonb', 'default': '{}'},
                        'is_builtin': {'type': 'boolean', 'default': False},
                        'is_active': {'type': 'boolean', 'default': True},
                        'is_default': {'type': 'boolean', 'default': False},
                        'user_count': {'type': 'integer', 'default': 0}
                    },
                    'relationship_definitions': {
                        'users': {'type': 'many_to_many', 'target': 'user_element'},
                        'permissions': {'type': 'many_to_many', 'target': 'permission_element'},
                        'parent_role': {'type': 'many_to_one', 'target': 'role_element'}
                    },
                    'index_definitions': [
                        {'fields': ['role_name'], 'type': 'btree', 'unique': True},
                        {'fields': ['is_active'], 'type': 'btree'},
                        {'fields': ['parent_role_id'], 'type': 'btree'}
                    ]
                },
                {
                    'type_id': 'permission_element',
                    'type_name': 'Permission Element',
                    'table_name': 'permission_elements',
                    'field_definitions': {
                        'permission_name': {'type': 'string', 'unique': True, 'required': True},
                        'display_name': {'type': 'string', 'required': True},
                        'description': {'type': 'text', 'nullable': True},
                        'resource_type': {'type': 'string', 'required': True},
                        'action': {'type': 'string', 'required': True},
                        'resource_pattern': {'type': 'string', 'default': '*'},
                        'permission_config': {'type': 'jsonb', 'default': '{}'},
                        'conditions': {'type': 'jsonb', 'default': '[]'},
                        'constraints': {'type': 'jsonb', 'default': '{}'},
                        'category': {'type': 'string', 'default': 'functional'},
                        'priority': {'type': 'integer', 'default': 0},
                        'is_dangerous': {'type': 'boolean', 'default': False},
                        'is_system': {'type': 'boolean', 'default': False},
                        'is_active': {'type': 'boolean', 'default': True},
                        'usage_count': {'type': 'integer', 'default': 0},
                        'last_used': {'type': 'timestamp', 'nullable': True}
                    },
                    'relationship_definitions': {
                        'roles': {'type': 'many_to_many', 'target': 'role_element'}
                    },
                    'index_definitions': [
                        {'fields': ['permission_name'], 'type': 'btree', 'unique': True},
                        {'fields': ['resource_type', 'action'], 'type': 'btree'},
                        {'fields': ['category'], 'type': 'btree'},
                        {'fields': ['is_active'], 'type': 'btree'}
                    ]
                }
            ]
            
            # 创建安全领域请求
            request = DomainCreationRequest(
                domain_name='security',
                display_name='安全管理领域',
                description='用户、角色、权限管理的安全领域',
                domain_type=DomainType.TECHNICAL,
                custom_element_types=security_element_types,
                auto_optimize=True,
                namespace_prefix='SEC'
            )
            
            # 创建领域
            result = await self.domain_factory.create_domain(request)
            
            if result.success:
                # 创建默认角色和权限
                await self._create_default_security_data()
                self.logger.info("安全领域通过DomainFactory创建成功")
                return True
            else:
                self.logger.error(f"安全领域创建失败: {result.errors}")
                return False
                
        except Exception as e:
            self.logger.error(f"通过DomainFactory创建安全领域失败: {e}")
            return False
    
    async def _create_default_security_data(self):
        """创建默认的安全数据（角色、权限）"""
        try:
            # 创建默认权限
            default_permissions = [
                {
                    'permission_name': 'user:read',
                    'display_name': '查看用户信息',
                    'resource_type': 'user',
                    'action': 'read',
                    'category': 'functional'
                },
                {
                    'permission_name': 'user:write',
                    'display_name': '修改用户信息',
                    'resource_type': 'user', 
                    'action': 'write',
                    'category': 'functional'
                },
                {
                    'permission_name': 'admin:all',
                    'display_name': '系统管理权限',
                    'resource_type': 'all',
                    'action': 'all',
                    'category': 'administrative',
                    'is_system': True
                }
            ]
            
            permission_ids = []
            for perm_data in default_permissions:
                perm_data['element_type'] = 'permission_element'
                success, perm_id = await self.create_element(perm_data, 'system')
                if success:
                    permission_ids.append(perm_id)
            
            # 创建默认角色
            default_roles = [
                {
                    'role_name': 'user_default',
                    'display_name': '默认用户',
                    'description': '新用户的默认角色，拥有基础权限',
                    'is_builtin': True,
                    'is_default': True
                },
                {
                    'role_name': 'system_admin',
                    'display_name': '系统管理员',
                    'description': '拥有系统完全访问权限的管理员角色',
                    'is_builtin': True
                }
            ]
            
            for role_data in default_roles:
                role_data['element_type'] = 'role_element'
                success, role_id = await self.create_element(role_data, 'system')
                if success:
                    self.logger.info(f"默认角色创建成功: {role_data['role_name']}")
            
        except Exception as e:
            self.logger.warning(f"创建默认安全数据失败: {e}")
    
    @property
    def supported_element_types(self) -> List[str]:
        return ["user_element", "role_element", "permission_element", "session_element"]
    
    async def create_element(self, element_data: Dict[str, Any], created_by: Optional[str] = None) -> Tuple[bool, str]:
        """创建安全领域Element - 确保领域存在"""
        # 确保安全领域存在
        if not await self.ensure_domain_exists():
            return False, "安全领域不存在且无法创建"
        
        element_type = element_data.get('element_type')
        
        if element_type == 'user_element':
            return await self.create_user_element(element_data, created_by)
        elif element_type == 'role_element':
            return await self.create_role_element(element_data, created_by)
        elif element_type == 'permission_element':
            return await self.create_permission_element(element_data, created_by)
        else:
            return False, f"不支持的Element类型: {element_type}"
    
    async def create_user_element(self, user_data: Dict[str, Any], created_by: Optional[str] = None) -> Tuple[bool, str]:
        """创建用户Element"""
        try:
            element_id = user_data.get('element_id') or generate_element_id("user")
            username = user_data.get('username')
            email = user_data.get('email')
            password = user_data.get('password', '')
            
            if not username or not email:
                return False, "用户名和邮箱为必填项"
            
            # 密码哈希
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # 1. 注册Element元数据
                    metadata = ElementMetadata(
                        element_id=element_id,
                        domain_schema="security_schema",
                        element_type="user_element",
                        tag=f"security:user",
                        local_name=username,
                        semantic_tags=["security:user", "org:华望", "active:user"],
                        domain_category="user_management",
                        created_by=created_by
                    )
                    
                    if not await self.core_manager.register_element_metadata(metadata):
                        return False, "注册Element元数据失败"
                    
                    # 2. 创建用户Element
                    await conn.execute("""
                        INSERT INTO security_schema.user_elements (
                            element_id, username, email, password_hash, profile, is_active
                        ) VALUES ($1, $2, $3, $4, $5, $6)
                    """, 
                        element_id, username, email, password_hash,
                        json.dumps(user_data.get('profile', {'display_name': username})),
                        user_data.get('is_active', True)
                    )
                    
                    # 3. 分配默认角色
                    await self._assign_default_role(element_id, conn)
                    
                await self._log_operation("create_user_element", element_id, OperationResult.SUCCESS)
                return True, element_id
                
        except Exception as e:
            self.logger.error(f"创建用户Element失败: {e}")
            await self._log_operation("create_user_element", element_id, OperationResult.ERROR, str(e))
            return False, str(e)
    
    async def create_role_element(self, role_data: Dict[str, Any], created_by: Optional[str] = None) -> Tuple[bool, str]:
        """创建角色Element"""
        try:
            element_id = role_data.get('element_id') or generate_element_id("role")
            role_name = role_data.get('role_name')
            display_name = role_data.get('display_name', role_name)
            
            if not role_name:
                return False, "角色名称为必填项"
            
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # 1. 注册Element元数据
                    metadata = ElementMetadata(
                        element_id=element_id,
                        domain_schema="security_schema",
                        element_type="role_element",
                        tag="security:role",
                        local_name=role_name,
                        semantic_tags=["security:role", f"name:{role_name}"],
                        domain_category="access_control",
                        created_by=created_by
                    )
                    
                    if not await self.core_manager.register_element_metadata(metadata):
                        return False, "注册Element元数据失败"
                    
                    # 2. 创建角色Element
                    await conn.execute("""
                        INSERT INTO security_schema.role_elements (
                            element_id, role_name, display_name, description, is_active
                        ) VALUES ($1, $2, $3, $4, $5)
                    """,
                        element_id, role_name, display_name,
                        role_data.get('description', ''), True
                    )
                    
                await self._log_operation("create_role_element", element_id, OperationResult.SUCCESS)
                return True, element_id
                
        except Exception as e:
            self.logger.error(f"创建角色Element失败: {e}")
            return False, str(e)
    
    async def get_element(self, element_id: str) -> Optional[Dict[str, Any]]:
        """获取安全领域Element"""
        try:
            async with self.db_pool.acquire() as conn:
                # 首先从核心Schema获取元数据
                metadata = await self.core_manager.get_element(element_id)
                if not metadata:
                    return None
                
                element_type = metadata.get('element_type')
                
                if element_type == 'user_element':
                    row = await conn.fetchrow(
                        "SELECT * FROM security_schema.user_elements WHERE element_id = $1",
                        element_id
                    )
                elif element_type == 'role_element':
                    row = await conn.fetchrow(
                        "SELECT * FROM security_schema.role_elements WHERE element_id = $1",
                        element_id
                    )
                elif element_type == 'permission_element':
                    row = await conn.fetchrow(
                        "SELECT * FROM security_schema.permission_elements WHERE element_id = $1",
                        element_id
                    )
                else:
                    return None
                
                if row:
                    result = dict(row)
                    result['_metadata'] = metadata
                    return result
                    
        except Exception as e:
            self.logger.error(f"获取安全Element失败 {element_id}: {e}")
            
        return None
    
    async def authenticate_user(self, login: str, password: str) -> Optional[Dict[str, Any]]:
        """用户认证"""
        try:
            async with self.db_pool.acquire() as conn:
                # 查找用户
                row = await conn.fetchrow("""
                    SELECT element_id, username, email, password_hash, is_active, failed_attempts, locked_until
                    FROM security_schema.user_elements 
                    WHERE (username = $1 OR email = $1) AND is_active = true
                """, login)
                
                if not row:
                    return None
                
                # 检查账户锁定
                if row['locked_until'] and row['locked_until'] > datetime.now(timezone.utc):
                    return None
                
                # 验证密码
                if bcrypt.checkpw(password.encode('utf-8'), row['password_hash'].encode('utf-8')):
                    # 认证成功，更新登录信息
                    await conn.execute("""
                        UPDATE security_schema.user_elements 
                        SET last_login = $1, login_count = login_count + 1, failed_attempts = 0
                        WHERE element_id = $2
                    """, datetime.now(timezone.utc), row['element_id'])
                    
                    # 获取用户完整信息
                    user = await self.get_element(row['element_id'])
                    
                    # 记录审计日志
                    await self._log_security_audit(row['element_id'], None, "user_login", "success")
                    
                    return user
                else:
                    # 认证失败，更新失败次数
                    await conn.execute("""
                        UPDATE security_schema.user_elements 
                        SET failed_attempts = failed_attempts + 1
                        WHERE element_id = $1
                    """, row['element_id'])
                    
                    await self._log_security_audit(row['element_id'], None, "user_login", "failure")
                    
        except Exception as e:
            self.logger.error(f"用户认证失败: {e}")
            
        return None
    
    async def get_user_permissions(self, user_element_id: str) -> List[str]:
        """获取用户的所有权限"""
        try:
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    WITH user_roles AS (
                        SELECT role_element_id
                        FROM security_schema.user_role_relationships 
                        WHERE user_element_id = $1 AND is_active = true
                    ),
                    role_permissions AS (
                        SELECT permission_element_id
                        FROM security_schema.role_permission_relationships rpr
                        JOIN user_roles ur ON rpr.role_element_id = ur.role_element_id
                        WHERE rpr.is_active = true
                    )
                    SELECT DISTINCT pe.permission_name
                    FROM security_schema.permission_elements pe
                    JOIN role_permissions rp ON pe.element_id = rp.permission_element_id
                    WHERE pe.is_active = true
                """, user_element_id)
                
                return [row['permission_name'] for row in rows]
                
        except Exception as e:
            self.logger.error(f"获取用户权限失败 {user_element_id}: {e}")
            return []
    
    async def assign_role_to_user(self, user_element_id: str, role_element_id: str, 
                                 granted_by: Optional[str] = None) -> bool:
        """为用户分配角色"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO security_schema.user_role_relationships (
                        user_element_id, role_element_id, granted_by, is_active
                    ) VALUES ($1, $2, $3, $4)
                    ON CONFLICT (user_element_id, role_element_id) DO UPDATE SET
                        is_active = true, granted_by = EXCLUDED.granted_by, updated_at = CURRENT_TIMESTAMP
                """, user_element_id, role_element_id, granted_by, True)
                
                await self._log_security_audit(user_element_id, None, "assign_role", "success", 
                                             {"role_id": role_element_id})
                return True
                
        except Exception as e:
            self.logger.error(f"分配角色失败: {e}")
            return False
    
    async def _assign_default_role(self, user_element_id: str, conn):
        """分配默认角色"""
        try:
            # 获取默认角色
            default_role = await conn.fetchrow(
                "SELECT element_id FROM security_schema.role_elements WHERE role_name = 'user_default' AND is_active = true"
            )
            
            if default_role:
                await conn.execute("""
                    INSERT INTO security_schema.user_role_relationships (
                        user_element_id, role_element_id, granted_by, is_active
                    ) VALUES ($1, $2, 'system', true)
                """, user_element_id, default_role['element_id'])
                
        except Exception as e:
            self.logger.warning(f"分配默认角色失败: {e}")
    
    async def _log_security_audit(self, user_element_id: Optional[str], session_id: Optional[str],
                                 action_type: str, operation_result: str, details: Dict = None):
        """记录安全审计日志"""
        try:
            log_id = f"audit_{secrets.token_hex(8)}"
            
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO security_schema.security_audit_logs (
                        log_id, user_element_id, session_id, action_type,
                        operation_result, details
                    ) VALUES ($1, $2, $3, $4, $5, $6)
                """,
                    log_id, user_element_id, session_id, action_type,
                    operation_result, json.dumps(details or {})
                )
                
        except Exception as e:
            self.logger.warning(f"记录安全审计日志失败: {e}")
    
    # 实现抽象方法
    async def update_element(self, element_id: str, updates: Dict[str, Any], 
                           updated_by: Optional[str] = None) -> bool:
        """更新安全领域Element"""
        # 实现省略，类似于create_element的逻辑
        return True
    
    async def query_elements(self, filters: Dict[str, Any], 
                           limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """查询安全领域Elements"""
        # 实现省略，根据filters查询不同类型的Element
        return []
    
    async def create_permission_element(self, permission_data: Dict[str, Any], created_by: Optional[str] = None) -> Tuple[bool, str]:
        """创建权限Element"""
        try:
            element_id = permission_data.get('element_id') or generate_element_id("perm")
            permission_name = permission_data.get('permission_name')
            display_name = permission_data.get('display_name', permission_name)
            resource_type = permission_data.get('resource_type')
            action = permission_data.get('action')
            
            if not permission_name or not resource_type or not action:
                return False, "权限名称、资源类型和操作为必填项"
            
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # 1. 注册Element元数据
                    metadata = ElementMetadata(
                        element_id=element_id,
                        domain_schema=self.schema_name,
                        element_type="permission_element",
                        tag="security:permission",
                        local_name=permission_name,
                        semantic_tags=["security:permission", f"resource:{resource_type}", f"action:{action}"],
                        domain_category="access_control",
                        created_by=created_by
                    )
                    
                    if not await self.core_manager.register_element_metadata(metadata):
                        return False, "注册Element元数据失败"
                    
                    # 2. 创建权限Element
                    await conn.execute(f"""
                        INSERT INTO {self.schema_name}.permission_elements (
                            element_id, permission_name, display_name, description,
                            resource_type, action, resource_pattern, category, is_active
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    """,
                        element_id, permission_name, display_name,
                        permission_data.get('description', ''),
                        resource_type, action,
                        permission_data.get('resource_pattern', '*'),
                        permission_data.get('category', 'functional'),
                        True
                    )
                    
                await self._log_operation("create_permission_element", element_id, OperationResult.SUCCESS)
                return True, element_id
                
        except Exception as e:
            self.logger.error(f"创建权限Element失败: {e}")
            return False, str(e) 