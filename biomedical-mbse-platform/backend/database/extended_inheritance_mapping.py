#!/usr/bin/env python3
"""
扩展的UML 2.5 + SysML继承关系映射

基于improved_inheritance_mapping.py，扩展支持：
- 完整的行为模型 (Activity, StateMachine, Interaction)
- 用例模型 (UseCase, Actor, Include, Extend)
- 部署模型 (Node, Artifact, Deployment)
- 模板支持 (Template, Generic)
- SysML核心扩展 (Block, Requirement, ValueType)
"""

from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum
from .improved_inheritance_mapping import (
    UMLInheritanceLevel, UMLTypeInfo, ImprovedUMLInheritanceMapper
)

class ExtendedUMLInheritanceMapper(ImprovedUMLInheritanceMapper):
    """扩展的UML继承关系映射器"""
    
    def __init__(self, schema_name: str = "uml25_extended", include_sysml: bool = True):
        self.include_sysml = include_sysml
        super().__init__(schema_name)
    
    def _init_complete_inheritance_map(self):
        """初始化扩展的完整继承关系映射"""
        
        # 首先调用父类方法获取基础映射
        super()._init_complete_inheritance_map()
        
        # 扩展行为模型支持
        self._add_behavior_model_types()
        
        # 扩展用例模型支持
        self._add_usecase_model_types()
        
        # 扩展部署模型支持
        self._add_deployment_model_types()
        
        # 扩展模板支持
        self._add_template_support()
        
        # 扩展交互模型
        self._add_interaction_model_types()
        
        # 扩展状态机模型
        self._add_statemachine_model_types()
        
        # SysML扩展（可选）
        if self.include_sysml:
            self._add_sysml_extensions()
    
    def _add_behavior_model_types(self):
        """添加行为模型类型"""
        behavior_types = {
            # 行为基类 - 抽象
            'uml:Behavior': UMLTypeInfo(
                'uml:Behavior', True, 'uml:Classifier', UMLInheritanceLevel.SPECIALIZED,
                'inherit', '行为抽象基类'
            ),
            
            # 具体行为类型
            'uml:Activity': UMLTypeInfo(
                'uml:Activity', False, 'uml:Behavior', UMLInheritanceLevel.CONCRETE,
                'view', '活动元素'
            ),
            'uml:StateMachine': UMLTypeInfo(
                'uml:StateMachine', False, 'uml:Behavior', UMLInheritanceLevel.CONCRETE,
                'view', '状态机元素'
            ),
            'uml:Interaction': UMLTypeInfo(
                'uml:Interaction', False, 'uml:Behavior', UMLInheritanceLevel.CONCRETE,
                'view', '交互元素'
            ),
            'uml:OpaqueBehavior': UMLTypeInfo(
                'uml:OpaqueBehavior', False, 'uml:Behavior', UMLInheritanceLevel.CONCRETE,
                'view', '不透明行为元素'
            ),
            'uml:FunctionBehavior': UMLTypeInfo(
                'uml:FunctionBehavior', False, 'uml:OpaqueBehavior', UMLInheritanceLevel.CONCRETE,
                'view', '函数行为元素'
            ),
        }
        
        self.inheritance_hierarchy.update(behavior_types)
    
    def _add_usecase_model_types(self):
        """添加用例模型类型"""
        usecase_types = {
            # 用例相关元素
            'uml:UseCase': UMLTypeInfo(
                'uml:UseCase', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '用例元素'
            ),
            'uml:Actor': UMLTypeInfo(
                'uml:Actor', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '参与者元素'
            ),
            
            # 用例关系
            'uml:Include': UMLTypeInfo(
                'uml:Include', False, 'uml:DirectedRelationship', UMLInheritanceLevel.CONCRETE,
                'relation', '包含关系'
            ),
            'uml:Extend': UMLTypeInfo(
                'uml:Extend', False, 'uml:DirectedRelationship', UMLInheritanceLevel.CONCRETE,
                'relation', '扩展关系'
            ),
            'uml:ExtensionPoint': UMLTypeInfo(
                'uml:ExtensionPoint', False, 'uml:NamedElement', UMLInheritanceLevel.CONCRETE,
                'view', '扩展点元素'
            ),
        }
        
        self.inheritance_hierarchy.update(usecase_types)
    
    def _add_deployment_model_types(self):
        """添加部署模型类型"""
        deployment_types = {
            # 部署相关元素
            'uml:Node': UMLTypeInfo(
                'uml:Node', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '节点元素'
            ),
            'uml:Device': UMLTypeInfo(
                'uml:Device', False, 'uml:Node', UMLInheritanceLevel.CONCRETE,
                'view', '设备元素'
            ),
            'uml:ExecutionEnvironment': UMLTypeInfo(
                'uml:ExecutionEnvironment', False, 'uml:Node', UMLInheritanceLevel.CONCRETE,
                'view', '执行环境元素'
            ),
            'uml:Artifact': UMLTypeInfo(
                'uml:Artifact', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '制品元素'
            ),
            'uml:DeploymentSpecification': UMLTypeInfo(
                'uml:DeploymentSpecification', False, 'uml:Artifact', UMLInheritanceLevel.CONCRETE,
                'view', '部署规范元素'
            ),
            
            # 部署关系
            'uml:Deployment': UMLTypeInfo(
                'uml:Deployment', False, 'uml:Dependency', UMLInheritanceLevel.CONCRETE,
                'relation', '部署关系'
            ),
            'uml:Manifestation': UMLTypeInfo(
                'uml:Manifestation', False, 'uml:Abstraction', UMLInheritanceLevel.CONCRETE,
                'relation', '显现关系'
            ),
        }
        
        self.inheritance_hierarchy.update(deployment_types)
    
    def _add_template_support(self):
        """添加模板支持"""
        template_types = {
            # 模板基础抽象类
            'uml:TemplateableElement': UMLTypeInfo(
                'uml:TemplateableElement', True, 'uml:Element', UMLInheritanceLevel.ABSTRACT,
                'inherit', '可模板化元素抽象基类'
            ),
            
            # 模板元素
            'uml:TemplateSignature': UMLTypeInfo(
                'uml:TemplateSignature', False, 'uml:Element', UMLInheritanceLevel.CONCRETE,
                'view', '模板签名元素'
            ),
            'uml:TemplateParameter': UMLTypeInfo(
                'uml:TemplateParameter', False, 'uml:Element', UMLInheritanceLevel.CONCRETE,
                'view', '模板参数元素'
            ),
            'uml:ClassifierTemplateParameter': UMLTypeInfo(
                'uml:ClassifierTemplateParameter', False, 'uml:TemplateParameter', UMLInheritanceLevel.CONCRETE,
                'view', '分类器模板参数'
            ),
            'uml:OperationTemplateParameter': UMLTypeInfo(
                'uml:OperationTemplateParameter', False, 'uml:TemplateParameter', UMLInheritanceLevel.CONCRETE,
                'view', '操作模板参数'
            ),
            
            # 模板绑定关系
            'uml:TemplateBinding': UMLTypeInfo(
                'uml:TemplateBinding', False, 'uml:DirectedRelationship', UMLInheritanceLevel.CONCRETE,
                'relation', '模板绑定关系'
            ),
            'uml:TemplateParameterSubstitution': UMLTypeInfo(
                'uml:TemplateParameterSubstitution', False, 'uml:Element', UMLInheritanceLevel.CONCRETE,
                'relation', '模板参数替换'
            ),
        }
        
        self.inheritance_hierarchy.update(template_types)
    
    def _add_interaction_model_types(self):
        """添加交互模型类型"""
        interaction_types = {
            # 交互基础元素
            'uml:Lifeline': UMLTypeInfo(
                'uml:Lifeline', False, 'uml:NamedElement', UMLInheritanceLevel.CONCRETE,
                'view', '生命线元素'
            ),
            'uml:Message': UMLTypeInfo(
                'uml:Message', False, 'uml:NamedElement', UMLInheritanceLevel.CONCRETE,
                'view', '消息元素'
            ),
            'uml:InteractionFragment': UMLTypeInfo(
                'uml:InteractionFragment', True, 'uml:NamedElement', UMLInheritanceLevel.ABSTRACT,
                'inherit', '交互片段抽象基类'
            ),
            'uml:InteractionOperand': UMLTypeInfo(
                'uml:InteractionOperand', False, 'uml:InteractionFragment', UMLInheritanceLevel.CONCRETE,
                'view', '交互操作数元素'
            ),
            'uml:CombinedFragment': UMLTypeInfo(
                'uml:CombinedFragment', False, 'uml:InteractionFragment', UMLInheritanceLevel.CONCRETE,
                'view', '组合片段元素'
            ),
            'uml:ExecutionSpecification': UMLTypeInfo(
                'uml:ExecutionSpecification', True, 'uml:InteractionFragment', UMLInheritanceLevel.ABSTRACT,
                'inherit', '执行规范抽象基类'
            ),
            'uml:BehaviorExecutionSpecification': UMLTypeInfo(
                'uml:BehaviorExecutionSpecification', False, 'uml:ExecutionSpecification', UMLInheritanceLevel.CONCRETE,
                'view', '行为执行规范'
            ),
            'uml:ActionExecutionSpecification': UMLTypeInfo(
                'uml:ActionExecutionSpecification', False, 'uml:ExecutionSpecification', UMLInheritanceLevel.CONCRETE,
                'view', '动作执行规范'
            ),
        }
        
        self.inheritance_hierarchy.update(interaction_types)
    
    def _add_statemachine_model_types(self):
        """添加状态机模型类型"""
        statemachine_types = {
            # 状态机基础元素
            'uml:Vertex': UMLTypeInfo(
                'uml:Vertex', True, 'uml:NamedElement', UMLInheritanceLevel.ABSTRACT,
                'inherit', '顶点抽象基类'
            ),
            'uml:State': UMLTypeInfo(
                'uml:State', False, 'uml:Vertex', UMLInheritanceLevel.CONCRETE,
                'view', '状态元素'
            ),
            'uml:FinalState': UMLTypeInfo(
                'uml:FinalState', False, 'uml:State', UMLInheritanceLevel.CONCRETE,
                'view', '终止状态元素'
            ),
            'uml:Pseudostate': UMLTypeInfo(
                'uml:Pseudostate', False, 'uml:Vertex', UMLInheritanceLevel.CONCRETE,
                'view', '伪状态元素'
            ),
            'uml:ConnectionPointReference': UMLTypeInfo(
                'uml:ConnectionPointReference', False, 'uml:Vertex', UMLInheritanceLevel.CONCRETE,
                'view', '连接点引用元素'
            ),
            
            # 转换和触发器
            'uml:Transition': UMLTypeInfo(
                'uml:Transition', False, 'uml:NamedElement', UMLInheritanceLevel.CONCRETE,
                'view', '转换元素'
            ),
            'uml:Trigger': UMLTypeInfo(
                'uml:Trigger', False, 'uml:NamedElement', UMLInheritanceLevel.CONCRETE,
                'view', '触发器元素'
            ),
            'uml:Event': UMLTypeInfo(
                'uml:Event', True, 'uml:NamedElement', UMLInheritanceLevel.ABSTRACT,
                'inherit', '事件抽象基类'
            ),
            'uml:CallEvent': UMLTypeInfo(
                'uml:CallEvent', False, 'uml:Event', UMLInheritanceLevel.CONCRETE,
                'view', '调用事件元素'
            ),
            'uml:SignalEvent': UMLTypeInfo(
                'uml:SignalEvent', False, 'uml:Event', UMLInheritanceLevel.CONCRETE,
                'view', '信号事件元素'
            ),
            'uml:ChangeEvent': UMLTypeInfo(
                'uml:ChangeEvent', False, 'uml:Event', UMLInheritanceLevel.CONCRETE,
                'view', '变化事件元素'
            ),
            'uml:TimeEvent': UMLTypeInfo(
                'uml:TimeEvent', False, 'uml:Event', UMLInheritanceLevel.CONCRETE,
                'view', '时间事件元素'
            ),
            
            # 状态机区域
            'uml:Region': UMLTypeInfo(
                'uml:Region', False, 'uml:Namespace', UMLInheritanceLevel.CONCRETE,
                'view', '区域元素'
            ),
        }
        
        self.inheritance_hierarchy.update(statemachine_types)
    
    def _add_sysml_extensions(self):
        """添加SysML扩展"""
        sysml_types = {
            # SysML核心Block扩展
            'sysml:Block': UMLTypeInfo(
                'sysml:Block', False, 'uml:Class', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML块元素'
            ),
            'sysml:ConstraintBlock': UMLTypeInfo(
                'sysml:ConstraintBlock', False, 'sysml:Block', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML约束块元素'
            ),
            
            # SysML值类型
            'sysml:ValueType': UMLTypeInfo(
                'sysml:ValueType', False, 'uml:DataType', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML值类型'
            ),
            'sysml:Unit': UMLTypeInfo(
                'sysml:Unit', False, 'sysml:ValueType', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML单位'
            ),
            'sysml:Dimension': UMLTypeInfo(
                'sysml:Dimension', False, 'sysml:ValueType', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML维度'
            ),
            
            # SysML需求模型
            'sysml:Requirement': UMLTypeInfo(
                'sysml:Requirement', False, 'uml:Class', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML需求元素'
            ),
            'sysml:TestCase': UMLTypeInfo(
                'sysml:TestCase', False, 'uml:Operation', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML测试用例'
            ),
            
            # SysML关系
            'sysml:Satisfy': UMLTypeInfo(
                'sysml:Satisfy', False, 'uml:Dependency', UMLInheritanceLevel.CONCRETE,
                'relation', 'SysML满足关系'
            ),
            'sysml:Verify': UMLTypeInfo(
                'sysml:Verify', False, 'uml:Dependency', UMLInheritanceLevel.CONCRETE,
                'relation', 'SysML验证关系'
            ),
            'sysml:Refine': UMLTypeInfo(
                'sysml:Refine', False, 'uml:Dependency', UMLInheritanceLevel.CONCRETE,
                'relation', 'SysML细化关系'
            ),
            'sysml:DeriveReqt': UMLTypeInfo(
                'sysml:DeriveReqt', False, 'uml:Dependency', UMLInheritanceLevel.CONCRETE,
                'relation', 'SysML派生需求关系'
            ),
            'sysml:Copy': UMLTypeInfo(
                'sysml:Copy', False, 'uml:Dependency', UMLInheritanceLevel.CONCRETE,
                'relation', 'SysML复制关系'
            ),
            
            # SysML活动扩展
            'sysml:ControlOperator': UMLTypeInfo(
                'sysml:ControlOperator', False, 'uml:Activity', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML控制操作符'
            ),
            'sysml:Probability': UMLTypeInfo(
                'sysml:Probability', False, 'uml:Element', UMLInheritanceLevel.CONCRETE,
                'relation', 'SysML概率'
            ),
            'sysml:Rate': UMLTypeInfo(
                'sysml:Rate', False, 'uml:Element', UMLInheritanceLevel.CONCRETE,
                'relation', 'SysML速率'
            ),
            
            # SysML分配
            'sysml:Allocate': UMLTypeInfo(
                'sysml:Allocate', False, 'uml:Dependency', UMLInheritanceLevel.CONCRETE,
                'relation', 'SysML分配关系'
            ),
            'sysml:AllocateActivityPartition': UMLTypeInfo(
                'sysml:AllocateActivityPartition', False, 'uml:ActivityPartition', UMLInheritanceLevel.CONCRETE,
                'view', 'SysML分配活动分区'
            ),
        }
        
        self.inheritance_hierarchy.update(sysml_types)
    
    def get_extended_statistics(self) -> Dict[str, int]:
        """获取扩展统计信息"""
        stats = {
            'total_types': len(self.inheritance_hierarchy),
            'inherit_strategy': 0,
            'view_strategy': 0,
            'relation_strategy': 0,
            'uml_types': 0,
            'sysml_types': 0,
            'by_level': {level.name: 0 for level in UMLInheritanceLevel}
        }
        
        for qualified_name, type_info in self.inheritance_hierarchy.items():
            # 策略统计
            stats[f"{type_info.table_strategy}_strategy"] += 1
            
            # 级别统计
            stats['by_level'][type_info.inheritance_level.name] += 1
            
            # 命名空间统计
            if qualified_name.startswith('uml:'):
                stats['uml_types'] += 1
            elif qualified_name.startswith('sysml:'):
                stats['sysml_types'] += 1
        
        return stats
    
    def get_behavior_model_types(self) -> List[str]:
        """获取行为模型相关类型"""
        behavior_types = []
        for qualified_name, type_info in self.inheritance_hierarchy.items():
            if any(keyword in qualified_name.lower() for keyword in 
                   ['behavior', 'activity', 'statemachine', 'interaction', 'state', 'transition']):
                behavior_types.append(qualified_name)
        return sorted(behavior_types)
    
    def get_sysml_types(self) -> List[str]:
        """获取SysML相关类型"""
        return sorted([qname for qname in self.inheritance_hierarchy.keys() 
                      if qname.startswith('sysml:')])

# 便捷函数
def create_extended_inheritance_mapper(
    schema_name: str = "uml25_extended", 
    include_sysml: bool = True
) -> ExtendedUMLInheritanceMapper:
    """创建扩展的继承映射器"""
    return ExtendedUMLInheritanceMapper(schema_name, include_sysml)

def print_extended_inheritance_analysis():
    """打印扩展继承关系分析"""
    mapper = ExtendedUMLInheritanceMapper()
    stats = mapper.get_extended_statistics()
    
    print("=== 扩展UML 2.5 + SysML 继承层次分析 ===")
    print(f"总类型数量: {stats['total_types']}")
    print(f"UML类型: {stats['uml_types']}")
    print(f"SysML类型: {stats['sysml_types']}")
    
    print(f"\n=== 表策略分布 ===")
    print(f"继承策略 (inherit): {stats['inherit_strategy']}")
    print(f"视图策略 (view): {stats['view_strategy']}")
    print(f"关系策略 (relation): {stats['relation_strategy']}")
    
    print(f"\n=== 级别分布 ===")
    for level_name, count in stats['by_level'].items():
        print(f"{level_name}: {count}")
    
    print(f"\n=== 行为模型类型 ===")
    behavior_types = mapper.get_behavior_model_types()
    for btype in behavior_types[:10]:  # 显示前10个
        print(f"  {btype}")
    if len(behavior_types) > 10:
        print(f"  ... 还有 {len(behavior_types) - 10} 个")
    
    if mapper.include_sysml:
        print(f"\n=== SysML扩展类型 ===")
        sysml_types = mapper.get_sysml_types()
        for stype in sysml_types[:10]:  # 显示前10个
            print(f"  {stype}")
        if len(sysml_types) > 10:
            print(f"  ... 还有 {len(sysml_types) - 10} 个")

if __name__ == "__main__":
    print_extended_inheritance_analysis() 