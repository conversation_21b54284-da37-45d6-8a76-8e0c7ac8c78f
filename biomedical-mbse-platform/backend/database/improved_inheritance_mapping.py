#!/usr/bin/env python3
"""
改进的UML 2.5继承关系映射

基于UML 2.5标准文档，提供完整的继承层次映射和表结构设计
参考文档：uml_metamodel_complete.txt, uml_abstract_classes_detailed.txt
"""

from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

class UMLInheritanceLevel(Enum):
    """UML继承层次级别"""
    ROOT = 0           # Element
    CORE = 1          # NamedElement, Relationship  
    ABSTRACT = 2      # Type, Namespace, Feature
    SPECIALIZED = 3   # Classifier, StructuralFeature
    CONCRETE = 4      # Class, Property, Operation

@dataclass
class UMLTypeInfo:
    """UML类型信息"""
    qualified_name: str
    is_abstract: bool
    parent_type: Optional[str]
    inheritance_level: UMLInheritanceLevel
    table_strategy: str  # 'inherit', 'view', 'relation'
    description: str

class ImprovedUMLInheritanceMapper:
    """改进的UML继承关系映射器"""
    
    def __init__(self, schema_name: str = "uml25_base"):
        self.schema_name = schema_name
        self._init_complete_inheritance_map()
    
    def _init_complete_inheritance_map(self):
        """初始化完整的继承关系映射"""
        
        # 完整的UML 2.5继承层次 (基于标准文档)
        self.inheritance_hierarchy = {
            # 级别0: 根类
            'uml:Element': UMLTypeInfo(
                'uml:Element', True, None, UMLInheritanceLevel.ROOT,
                'inherit', '所有UML元素的抽象根类'
            ),
            
            # 级别1: 核心基类
            'uml:NamedElement': UMLTypeInfo(
                'uml:NamedElement', True, 'uml:Element', UMLInheritanceLevel.CORE,
                'inherit', '具有名称的元素抽象基类'
            ),
            'uml:Relationship': UMLTypeInfo(
                'uml:Relationship', True, 'uml:Element', UMLInheritanceLevel.CORE,
                'inherit', '关系的抽象基类'
            ),
            
            # 级别2: 重要抽象层
            'uml:Type': UMLTypeInfo(
                'uml:Type', True, 'uml:NamedElement', UMLInheritanceLevel.ABSTRACT,
                'inherit', '所有类型的抽象基类'
            ),
            'uml:Namespace': UMLTypeInfo(
                'uml:Namespace', True, 'uml:NamedElement', UMLInheritanceLevel.ABSTRACT,
                'inherit', '具有命名空间的元素'
            ),
            'uml:Feature': UMLTypeInfo(
                'uml:Feature', True, 'uml:NamedElement', UMLInheritanceLevel.ABSTRACT,
                'inherit', '分类器拥有的特征抽象基类'
            ),
            'uml:PackageableElement': UMLTypeInfo(
                'uml:PackageableElement', True, 'uml:NamedElement', UMLInheritanceLevel.ABSTRACT,
                'inherit', '可放入包中的元素'
            ),
            'uml:DirectedRelationship': UMLTypeInfo(
                'uml:DirectedRelationship', True, 'uml:Relationship', UMLInheritanceLevel.ABSTRACT,
                'inherit', '有方向的关系'
            ),
            
            # 级别3: 特化抽象类
            'uml:Classifier': UMLTypeInfo(
                'uml:Classifier', True, 'uml:Type', UMLInheritanceLevel.SPECIALIZED,
                'inherit', '分类器抽象基类'
            ),
            'uml:StructuralFeature': UMLTypeInfo(
                'uml:StructuralFeature', True, 'uml:Feature', UMLInheritanceLevel.SPECIALIZED,
                'inherit', '结构特征抽象基类'
            ),
            'uml:BehavioralFeature': UMLTypeInfo(
                'uml:BehavioralFeature', True, 'uml:Feature', UMLInheritanceLevel.SPECIALIZED,
                'inherit', '行为特征抽象基类'
            ),
            
            # 级别4: 具体类 - 分类器类别
            'uml:Class': UMLTypeInfo(
                'uml:Class', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '类元素'
            ),
            'uml:Interface': UMLTypeInfo(
                'uml:Interface', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '接口元素'
            ),
            'uml:Enumeration': UMLTypeInfo(
                'uml:Enumeration', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '枚举元素'
            ),
            'uml:DataType': UMLTypeInfo(
                'uml:DataType', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '数据类型元素'
            ),
            'uml:PrimitiveType': UMLTypeInfo(
                'uml:PrimitiveType', False, 'uml:DataType', UMLInheritanceLevel.CONCRETE,
                'view', '原始类型元素'
            ),
            'uml:Signal': UMLTypeInfo(
                'uml:Signal', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '信号元素'
            ),
            'uml:Component': UMLTypeInfo(
                'uml:Component', False, 'uml:Classifier', UMLInheritanceLevel.CONCRETE,
                'view', '组件元素'
            ),
            
            # 级别4: 具体类 - 命名空间类别
            'uml:Package': UMLTypeInfo(
                'uml:Package', False, 'uml:Namespace', UMLInheritanceLevel.CONCRETE,
                'view', '包元素'
            ),
            'uml:Model': UMLTypeInfo(
                'uml:Model', False, 'uml:Package', UMLInheritanceLevel.CONCRETE,
                'view', '模型元素'
            ),
            'uml:Profile': UMLTypeInfo(
                'uml:Profile', False, 'uml:Package', UMLInheritanceLevel.CONCRETE,
                'view', '配置文件元素'
            ),
            
            # 级别4: 具体类 - 特征类别
            'uml:Property': UMLTypeInfo(
                'uml:Property', False, 'uml:StructuralFeature', UMLInheritanceLevel.CONCRETE,
                'view', '属性元素'
            ),
            'uml:Operation': UMLTypeInfo(
                'uml:Operation', False, 'uml:BehavioralFeature', UMLInheritanceLevel.CONCRETE,
                'view', '操作元素'
            ),
            'uml:Parameter': UMLTypeInfo(
                'uml:Parameter', False, 'uml:NamedElement', UMLInheritanceLevel.CONCRETE,
                'view', '参数元素'
            ),
            
            # 级别4: 具体类 - 关系类别
            'uml:Association': UMLTypeInfo(
                'uml:Association', False, 'uml:Relationship', UMLInheritanceLevel.CONCRETE,
                'view', '关联关系'
            ),
            'uml:Generalization': UMLTypeInfo(
                'uml:Generalization', False, 'uml:DirectedRelationship', UMLInheritanceLevel.CONCRETE,
                'relation', '泛化关系'
            ),
            'uml:Dependency': UMLTypeInfo(
                'uml:Dependency', False, 'uml:DirectedRelationship', UMLInheritanceLevel.CONCRETE,
                'relation', '依赖关系'
            ),
            
            # 描述性类型 - 使用关系表
            'uml:Comment': UMLTypeInfo(
                'uml:Comment', False, 'uml:Element', UMLInheritanceLevel.CONCRETE,
                'relation', '注释元素'
            ),
            'uml:Constraint': UMLTypeInfo(
                'uml:Constraint', False, 'uml:NamedElement', UMLInheritanceLevel.CONCRETE,
                'relation', '约束元素'
            ),
        }
    
    def get_inheritance_table(self, qualified_name: str) -> Optional[str]:
        """获取类型的继承表"""
        type_info = self.inheritance_hierarchy.get(qualified_name)
        if not type_info or not type_info.parent_type:
            return None
        
        parent_info = self.inheritance_hierarchy.get(type_info.parent_type)
        if parent_info and parent_info.table_strategy == 'inherit':
            # 只从使用表继承的父类继承
            parent_table = self._get_table_name(type_info.parent_type)
            return f"{self.schema_name}.{parent_table}"
        
        return None
    
    def get_table_strategy(self, qualified_name: str) -> str:
        """获取表实现策略"""
        type_info = self.inheritance_hierarchy.get(qualified_name)
        return type_info.table_strategy if type_info else 'inherit'
    
    def should_create_inheritance_table(self, qualified_name: str) -> bool:
        """判断是否应该创建继承表"""
        type_info = self.inheritance_hierarchy.get(qualified_name)
        return type_info and type_info.table_strategy == 'inherit'
    
    def should_create_data_table(self, qualified_name: str) -> bool:
        """判断是否应该创建数据表"""
        type_info = self.inheritance_hierarchy.get(qualified_name)
        return type_info and type_info.table_strategy == 'view'
    
    def should_create_relation_entry(self, qualified_name: str) -> bool:
        """判断是否应该创建关系表条目"""
        type_info = self.inheritance_hierarchy.get(qualified_name)
        return type_info and type_info.table_strategy == 'relation'
    
    def _get_table_name(self, qualified_name: str) -> str:
        """获取表名"""
        if ':' in qualified_name:
            namespace, type_name = qualified_name.split(':', 1)
            return f"{namespace.lower()}_{type_name.lower()}"
        return f"uml_{qualified_name.lower()}"
    
    def get_inheritance_chain(self, qualified_name: str) -> List[str]:
        """获取完整的继承链"""
        chain = []
        current = qualified_name
        
        while current and current in self.inheritance_hierarchy:
            chain.append(current)
            current = self.inheritance_hierarchy[current].parent_type
        
        return list(reversed(chain))  # 从根到子
    
    def get_core_inheritance_tables(self) -> List[str]:
        """获取需要创建的核心继承表"""
        core_tables = []
        
        for qualified_name, type_info in self.inheritance_hierarchy.items():
            if type_info.table_strategy == 'inherit':
                core_tables.append(qualified_name)
        
        # 按继承层次排序
        core_tables.sort(key=lambda x: self.inheritance_hierarchy[x].inheritance_level.value)
        return core_tables
    
    def get_view_definitions(self) -> Dict[str, str]:
        """获取视图定义"""
        views = {}
        
        for qualified_name, type_info in self.inheritance_hierarchy.items():
            if type_info.table_strategy == 'view':
                view_sql = self._generate_view_sql(qualified_name, type_info)
                views[qualified_name] = view_sql
        
        return views
    
    def _generate_view_sql(self, qualified_name: str, type_info: UMLTypeInfo) -> str:
        """生成视图SQL"""
        table_name = self._get_table_name(qualified_name)
        data_table = f"{table_name}_data"
        
        # 获取继承链上所有表
        inheritance_chain = self.get_inheritance_chain(qualified_name)
        
        # 构建JOIN查询
        joins = []
        select_fields = [f"d.*"]  # 数据表的所有字段
        
        for parent_type in inheritance_chain[:-1]:  # 排除自己
            parent_info = self.inheritance_hierarchy.get(parent_type)
            if parent_info and parent_info.table_strategy == 'inherit':
                parent_table = self._get_table_name(parent_type)
                alias = parent_table.split('_')[-1][:2]  # 简短别名
                joins.append(f"JOIN {self.schema_name}.{parent_table} {alias} ON d.id = {alias}.id")
                select_fields.append(f"{alias}.*")
        
        view_sql = f"""
CREATE OR REPLACE VIEW {self.schema_name}.{table_name} AS
SELECT {', '.join(select_fields)}
FROM {self.schema_name}.{data_table} d
{' '.join(joins)};

COMMENT ON VIEW {self.schema_name}.{table_name} IS '{type_info.description}';
"""
        return view_sql
    
    def get_relationship_table_schema(self) -> str:
        """获取关系表的schema定义"""
        return f"""
-- 元素关系表 - 处理所有元素间的关系
CREATE TABLE {self.schema_name}.element_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_id UUID NOT NULL,
    target_id UUID NOT NULL,
    relationship_type TEXT NOT NULL,
    relationship_subtype TEXT,
    multiplicity_source TEXT DEFAULT '1',
    multiplicity_target TEXT DEFAULT '1',
    is_navigable_source BOOLEAN DEFAULT true,
    is_navigable_target BOOLEAN DEFAULT true,
    properties JSONB DEFAULT '{{}}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (source_id) REFERENCES {self.schema_name}.uml_element(id) ON DELETE CASCADE,
    FOREIGN KEY (target_id) REFERENCES {self.schema_name}.uml_element(id) ON DELETE CASCADE,
    
    -- 确保关系唯一性
    UNIQUE(source_id, target_id, relationship_type)
);

CREATE INDEX idx_element_relationships_source ON {self.schema_name}.element_relationships(source_id);
CREATE INDEX idx_element_relationships_target ON {self.schema_name}.element_relationships(target_id);
CREATE INDEX idx_element_relationships_type ON {self.schema_name}.element_relationships(relationship_type);

COMMENT ON TABLE {self.schema_name}.element_relationships IS '元素间关系表 - 统一管理所有UML关系';
"""

# 使用示例和工具函数
def get_improved_inheritance_mapping(schema_name: str = "uml25_base") -> ImprovedUMLInheritanceMapper:
    """获取改进的继承关系映射器"""
    return ImprovedUMLInheritanceMapper(schema_name)

def print_inheritance_analysis():
    """打印继承关系分析"""
    mapper = ImprovedUMLInheritanceMapper()
    
    print("=== UML 2.5 完整继承层次分析 ===")
    
    # 按级别分组
    by_level = {}
    for qualified_name, type_info in mapper.inheritance_hierarchy.items():
        level = type_info.inheritance_level
        if level not in by_level:
            by_level[level] = []
        by_level[level].append((qualified_name, type_info))
    
    for level in UMLInheritanceLevel:
        if level in by_level:
            print(f"\n{level.name} (级别 {level.value}):")
            for qualified_name, type_info in by_level[level]:
                parent = f" <- {type_info.parent_type}" if type_info.parent_type else ""
                strategy = f"[{type_info.table_strategy}]"
                abstract = "[抽象]" if type_info.is_abstract else "[具体]"
                print(f"  {qualified_name}{parent} {strategy} {abstract}")
    
    print(f"\n=== 表策略统计 ===")
    strategies = {}
    for type_info in mapper.inheritance_hierarchy.values():
        strategy = type_info.table_strategy
        strategies[strategy] = strategies.get(strategy, 0) + 1
    
    for strategy, count in strategies.items():
        print(f"{strategy}: {count} 个类型")

if __name__ == "__main__":
    print_inheritance_analysis() 