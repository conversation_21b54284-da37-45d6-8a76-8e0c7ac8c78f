#!/usr/bin/env python3
"""
增强版UML25 Schema生成器

基于改进的继承映射策略，生成高性能的UML25数据库Schema：
1. 继承表策略 - 核心UML类型的高效继承结构  
2. 视图策略 - 复合型数据的优化视图
3. 关系表策略 - 标准化的关系存储
4. 性能优化的索引和约束
"""

import asyncio
import asyncpg
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass, field
import time

from .improved_inheritance_mapping import ImprovedUMLInheritanceMapper
from schemas.dynamic.uml25_schema_generator import TableDefinition, TableColumn, UML25SchemaGenerator

logger = logging.getLogger(__name__)

class EnhancedUML25SchemaGenerator(UML25SchemaGenerator):
    """增强版UML25 Schema生成器"""
    
    def __init__(self, db_pool: asyncpg.Pool, config: Dict[str, Any] = None):
        super().__init__(db_pool, config)
        self.inheritance_mapper = ImprovedUMLInheritanceMapper(self.schema_name)
        self.view_definitions = {}
        
    async def generate_schema_from_xmi(self, xmi_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成增强版Schema"""
        start_time = time.time()
        
        try:
            logger.info("开始生成增强版UML25 Schema...")
            
            self.uml_metamodel = xmi_result
            
            # 1. 创建Schema命名空间
            await self._create_schema_namespace()
            
            # 2. 生成核心继承表 (inherit策略)
            await self._generate_core_inheritance_tables()
            
            # 3. 生成数据表 (view策略的数据部分)  
            await self._generate_data_tables()
            
            # 4. 生成关系表 (relation策略)
            await self._generate_enhanced_relationship_tables()
            
            # 5. 创建视图 (view策略的视图部分)
            await self._create_inheritance_views()
            
            # 6. 创建索引和约束
            await self._create_enhanced_indexes_and_constraints()
            
            # 7. 生成扩展支持
            await self._create_extension_support()
            
            # 8. 创建便捷查询函数
            await self._create_query_functions()
            
            self.generation_stats['generation_time'] = time.time() - start_time
            
            logger.info(f"增强版Schema生成完成:")
            logger.info(f"  - 继承表: {len(self.inheritance_mapper.get_core_inheritance_tables())}")
            logger.info(f"  - 数据表: {len([t for t in self.inheritance_mapper.inheritance_hierarchy.values() if t.table_strategy == 'view'])}")
            logger.info(f"  - 视图: {len(self.view_definitions)}")
            logger.info(f"  - 总生成时间: {self.generation_stats['generation_time']:.2f}秒")
            
            return await self._build_enhanced_generation_result()
            
        except Exception as e:
            self.generation_stats['errors'].append(str(e))
            logger.error(f"增强版Schema生成失败: {e}")
            raise
    
    async def _generate_core_inheritance_tables(self):
        """生成核心继承表 (inherit策略)"""
        core_tables = self.inheritance_mapper.get_core_inheritance_tables()
        
        logger.info(f"生成{len(core_tables)}个核心继承表...")
        
        for qualified_name in core_tables:
            await self._create_inheritance_table(qualified_name)
        
        logger.info("核心继承表生成完成")
    
    async def _create_inheritance_table(self, qualified_name: str):
        """创建单个继承表"""
        type_info = self.inheritance_mapper.inheritance_hierarchy[qualified_name]
        table_name = self.inheritance_mapper._get_table_name(qualified_name)
        
        # 确定继承关系
        inheritance_table = self.inheritance_mapper.get_inheritance_table(qualified_name)
        
        table_def = TableDefinition(
            name=table_name,
            schema_name=self.schema_name,
            inheritance=inheritance_table,
            comment=f"UML继承表: {type_info.description}"
        )
        
        # 根据UML标准为每个级别添加适当的列
        await self._add_columns_for_inheritance_table(table_def, qualified_name, type_info)
        
        await self._create_table(table_def)
        
    async def _add_columns_for_inheritance_table(self, table_def: TableDefinition, qualified_name: str, type_info):
        """为继承表添加标准列"""
        
        if qualified_name == 'uml:Element':
            # 根类 - 所有元素的基础属性
            table_def.columns = [
                TableColumn("id", "UUID", False, "uuid_generate_v4()", True, 
                           comment="元素唯一标识"),
                TableColumn("xmi_id", "TEXT", True, None, False, True,
                           comment="XMI标识符"),
                TableColumn("element_type", "TEXT", False, comment="具体元素类型"),
                TableColumn("documentation", "TEXT", True, comment="文档说明"),
                TableColumn("stereotype", "TEXT", True, comment="构造型"),
                TableColumn("properties", "JSONB", True, "'{}'", comment="扩展属性"),
                TableColumn("created_at", "TIMESTAMP WITH TIME ZONE", False, "CURRENT_TIMESTAMP"),
                TableColumn("updated_at", "TIMESTAMP WITH TIME ZONE", False, "CURRENT_TIMESTAMP")
            ]
            table_def.primary_keys = ["id"]
            
        elif qualified_name == 'uml:NamedElement':
            # 命名元素 - 具有名称的元素
            table_def.columns = [
                TableColumn("name", "TEXT", True, comment="元素名称"),
                TableColumn("qualified_name", "TEXT", True, comment="限定名称"),
                TableColumn("visibility", "TEXT", False, "'public'", comment="可见性"),
                TableColumn("namespace", "TEXT", True, comment="命名空间"),
                TableColumn("qualified_name_delimiter", "TEXT", False, "'::'", comment="限定名分隔符")
            ]
            
        elif qualified_name == 'uml:Type':
            # 类型 - 所有类型的基础
            table_def.columns = [
                TableColumn("package_path", "TEXT", True, comment="包路径"),
                TableColumn("conformance_rules", "JSONB", True, "'[]'", comment="一致性规则")
            ]
            
        elif qualified_name == 'uml:Classifier':
            # 分类器 - 分类器基础属性
            table_def.columns = [
                TableColumn("is_abstract", "BOOLEAN", False, "false", comment="是否抽象"),
                TableColumn("is_final_specialization", "BOOLEAN", False, "false", comment="是否最终特化"),
                TableColumn("is_leaf", "BOOLEAN", False, "false", comment="是否叶节点"),
                TableColumn("attributes_count", "INTEGER", False, "0", comment="拥有属性数量"),
                TableColumn("operations_count", "INTEGER", False, "0", comment="拥有操作数量"),
                TableColumn("generalizations", "JSONB", True, "'[]'", comment="泛化关系"),
                TableColumn("specializations", "JSONB", True, "'[]'", comment="特化关系"),
                TableColumn("owned_use_cases", "JSONB", True, "'[]'", comment="拥有的用例")
            ]
            
        elif qualified_name == 'uml:Feature':
            # 特征 - 分类器特征基础
            table_def.columns = [
                TableColumn("is_static", "BOOLEAN", False, "false", comment="是否静态"),
                TableColumn("featuring_classifier_id", "UUID", True, comment="拥有该特征的分类器")
            ]
            table_def.foreign_keys = [
                {"column": "featuring_classifier_id", "references": f"{self.schema_name}.uml_classifier(id)"}
            ]
            
        elif qualified_name == 'uml:StructuralFeature':
            # 结构特征 - 属性基础
            table_def.columns = [
                TableColumn("type_id", "UUID", True, comment="特征类型"),
                TableColumn("multiplicity", "TEXT", False, "'1'", comment="多重性"),
                TableColumn("is_ordered", "BOOLEAN", False, "false", comment="是否有序"),
                TableColumn("is_unique", "BOOLEAN", False, "true", comment="是否唯一"),
                TableColumn("is_read_only", "BOOLEAN", False, "false", comment="是否只读")
            ]
            table_def.foreign_keys = [
                {"column": "type_id", "references": f"{self.schema_name}.uml_type(id)"}
            ]
            
        elif qualified_name == 'uml:BehavioralFeature':
            # 行为特征 - 操作基础
            table_def.columns = [
                TableColumn("is_abstract", "BOOLEAN", False, "false", comment="是否抽象"),
                TableColumn("concurrency", "TEXT", False, "'sequential'", comment="并发性"),
                TableColumn("is_query", "BOOLEAN", False, "false", comment="是否查询"),
                TableColumn("parameters", "JSONB", True, "'[]'", comment="参数列表")
            ]
            
        elif qualified_name == 'uml:Namespace':
            # 命名空间 - 包含其他元素的容器
            table_def.columns = [
                TableColumn("imported_members", "JSONB", True, "'[]'", comment="导入成员"),
                TableColumn("owned_members", "JSONB", True, "'[]'", comment="拥有成员"),
                TableColumn("package_imports", "JSONB", True, "'[]'", comment="包导入")
            ]
            
        elif qualified_name == 'uml:Relationship':
            # 关系基础
            table_def.columns = [
                TableColumn("related_elements", "JSONB", False, "'[]'", comment="相关元素列表"),
                TableColumn("relationship_type", "TEXT", False, comment="关系类型")
            ]
            
        elif qualified_name == 'uml:DirectedRelationship':
            # 有向关系
            table_def.columns = [
                TableColumn("sources", "JSONB", False, "'[]'", comment="源元素"),
                TableColumn("targets", "JSONB", False, "'[]'", comment="目标元素")
            ]
        
        self.generation_stats['columns_generated'] += len(table_def.columns)
    
    async def _generate_data_tables(self):
        """生成数据表 (view策略的数据部分)"""
        metaclasses = self.uml_metamodel.get('data', {}).get('metaclasses', {})
        view_types = [qname for qname, info in self.inheritance_mapper.inheritance_hierarchy.items() 
                      if info.table_strategy == 'view']
        
        logger.info(f"生成{len(view_types)}个数据表...")
        
        for qualified_name in view_types:
            # 查找对应的元类数据
            matching_metaclass = None
            for metaclass_id, metaclass_data in metaclasses.items():
                if metaclass_data.get('qualified_name') == qualified_name:
                    matching_metaclass = metaclass_data
                    break
            
            if matching_metaclass:
                await self._create_data_table(qualified_name, matching_metaclass)
            else:
                # 创建基础数据表
                await self._create_basic_data_table(qualified_name)
        
        logger.info("数据表生成完成")
    
    async def _create_data_table(self, qualified_name: str, metaclass_data: Dict[str, Any]):
        """创建具体的数据表"""
        type_info = self.inheritance_mapper.inheritance_hierarchy[qualified_name]
        table_name = self.inheritance_mapper._get_table_name(qualified_name) + "_data"
        
        table_def = TableDefinition(
            name=table_name,
            schema_name=self.schema_name,
            comment=f"数据表: {type_info.description}"
        )
        
        # 数据表只需要ID和该类型特有的属性
        table_def.columns = [
            TableColumn("id", "UUID", False, "uuid_generate_v4()", True, 
                       comment="元素唯一标识")
        ]
        table_def.primary_keys = ["id"]
        
        # 添加外键到最接近的继承表
        inheritance_chain = self.inheritance_mapper.get_inheritance_chain(qualified_name)
        for parent_type in reversed(inheritance_chain[:-1]):  # 从子到父查找
            parent_info = self.inheritance_mapper.inheritance_hierarchy.get(parent_type)
            if parent_info and parent_info.table_strategy == 'inherit':
                parent_table = self.inheritance_mapper._get_table_name(parent_type)
                table_def.foreign_keys = [
                    {"column": "id", "references": f"{self.schema_name}.{parent_table}(id)", "on_delete": "CASCADE"}
                ]
                break
        
        # 添加类型特有的属性
        await self._add_type_specific_columns(table_def, qualified_name, metaclass_data)
        
        await self._create_table(table_def)
    
    async def _create_basic_data_table(self, qualified_name: str):
        """创建基础数据表（当没有具体元类数据时）"""
        type_info = self.inheritance_mapper.inheritance_hierarchy[qualified_name]
        table_name = self.inheritance_mapper._get_table_name(qualified_name) + "_data"
        
        table_def = TableDefinition(
            name=table_name,
            schema_name=self.schema_name,
            comment=f"基础数据表: {type_info.description}"
        )
        
        table_def.columns = [
            TableColumn("id", "UUID", False, "uuid_generate_v4()", True, 
                       comment="元素唯一标识")
        ]
        table_def.primary_keys = ["id"]
        
        # 添加外键到父类继承表
        inheritance_chain = self.inheritance_mapper.get_inheritance_chain(qualified_name)
        for parent_type in reversed(inheritance_chain[:-1]):
            parent_info = self.inheritance_mapper.inheritance_hierarchy.get(parent_type)
            if parent_info and parent_info.table_strategy == 'inherit':
                parent_table = self.inheritance_mapper._get_table_name(parent_type)
                table_def.foreign_keys = [
                    {"column": "id", "references": f"{self.schema_name}.{parent_table}(id)", "on_delete": "CASCADE"}
                ]
                break
        
        await self._create_table(table_def)
    
    async def _add_type_specific_columns(self, table_def: TableDefinition, qualified_name: str, metaclass_data: Dict[str, Any]):
        """添加类型特有的列"""
        
        # 根据具体类型添加特有属性
        if qualified_name == 'uml:Class':
            table_def.columns.extend([
                TableColumn("is_active", "BOOLEAN", False, "false", comment="是否活动类"),
                TableColumn("owned_attributes", "JSONB", True, "'[]'", comment="拥有的属性"),
                TableColumn("owned_operations", "JSONB", True, "'[]'", comment="拥有的操作"),
                TableColumn("nested_classifiers", "JSONB", True, "'[]'", comment="嵌套分类器")
            ])
            
        elif qualified_name == 'uml:Interface':
            table_def.columns.extend([
                TableColumn("owned_operations", "JSONB", True, "'[]'", comment="拥有的操作"),
                TableColumn("nested_classifiers", "JSONB", True, "'[]'", comment="嵌套分类器"),
                TableColumn("owned_receptions", "JSONB", True, "'[]'", comment="拥有的接收")
            ])
            
        elif qualified_name == 'uml:Property':
            table_def.columns.extend([
                TableColumn("default_value", "TEXT", True, comment="默认值"),
                TableColumn("is_composite", "BOOLEAN", False, "false", comment="是否组合"),
                TableColumn("is_derived", "BOOLEAN", False, "false", comment="是否派生"),
                TableColumn("is_derived_union", "BOOLEAN", False, "false", comment="是否派生联合"),
                TableColumn("aggregation", "TEXT", False, "'none'", comment="聚合类型"),
                TableColumn("association_id", "UUID", True, comment="所属关联")
            ])
            
        elif qualified_name == 'uml:Operation':
            table_def.columns.extend([
                TableColumn("return_type_id", "UUID", True, comment="返回类型"),
                TableColumn("is_query", "BOOLEAN", False, "false", comment="是否查询操作"),
                TableColumn("body_condition", "TEXT", True, comment="体条件"),
                TableColumn("postconditions", "JSONB", True, "'[]'", comment="后置条件"),
                TableColumn("preconditions", "JSONB", True, "'[]'", comment="前置条件")
            ])
            
        elif qualified_name == 'uml:Package':
            table_def.columns.extend([
                TableColumn("package_merge", "JSONB", True, "'[]'", comment="包合并"),
                TableColumn("packaged_elements", "JSONB", True, "'[]'", comment="包含元素"),
                TableColumn("profile_applications", "JSONB", True, "'[]'", comment="Profile应用")
            ])
        
        # 添加来自元类定义的属性
        attributes = metaclass_data.get('attributes', [])
        for attr in attributes:
            attr_name = attr.get('name', '')
            if not attr_name or any(col.name == attr_name for col in table_def.columns):
                continue
                
            column_name = self._clean_column_name(attr_name)
            uml_type = attr.get('type_ref', 'String')
            pg_type = self._map_uml_type_to_postgres(uml_type)
            
            column = TableColumn(
                name=column_name,
                data_type=pg_type,
                nullable=True,
                comment=f"UML属性: {attr_name}"
            )
            
            table_def.columns.append(column)
        
        self.generation_stats['columns_generated'] += len(table_def.columns) - 1  # 排除id列
    
    async def _generate_enhanced_relationship_tables(self):
        """生成增强的关系表"""
        
        # 创建主关系表
        relationship_sql = self.inheritance_mapper.get_relationship_table_schema()
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(relationship_sql)
        
        # 为每种关系类型创建便捷表
        relation_types = [qname for qname, info in self.inheritance_mapper.inheritance_hierarchy.items() 
                         if info.table_strategy == 'relation']
        
        for qualified_name in relation_types:
            await self._create_specific_relationship_table(qualified_name)
        
        logger.info("增强关系表生成完成")
        
    async def _create_specific_relationship_table(self, qualified_name: str):
        """为特定关系类型创建表"""
        type_info = self.inheritance_mapper.inheritance_hierarchy[qualified_name]
        table_name = self.inheritance_mapper._get_table_name(qualified_name)
        
        if qualified_name == 'uml:Generalization':
            # 泛化关系表
            sql = f"""
CREATE TABLE {self.schema_name}.{table_name} (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    general_id UUID NOT NULL,
    specific_id UUID NOT NULL,
    is_substitutable BOOLEAN DEFAULT true,
    generalization_set_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (general_id) REFERENCES {self.schema_name}.uml_classifier(id) ON DELETE CASCADE,
    FOREIGN KEY (specific_id) REFERENCES {self.schema_name}.uml_classifier(id) ON DELETE CASCADE,
    
    UNIQUE(general_id, specific_id)
);

CREATE INDEX idx_{table_name}_general ON {self.schema_name}.{table_name}(general_id);
CREATE INDEX idx_{table_name}_specific ON {self.schema_name}.{table_name}(specific_id);

COMMENT ON TABLE {self.schema_name}.{table_name} IS '{type_info.description}';
"""
            
        elif qualified_name == 'uml:Dependency':
            # 依赖关系表
            sql = f"""
CREATE TABLE {self.schema_name}.{table_name} (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL,
    supplier_id UUID NOT NULL,
    dependency_type TEXT DEFAULT 'dependency',
    mapping JSONB DEFAULT '{{}}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES {self.schema_name}.uml_named_element(id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id) REFERENCES {self.schema_name}.uml_named_element(id) ON DELETE CASCADE,
    
    UNIQUE(client_id, supplier_id, dependency_type)
);

CREATE INDEX idx_{table_name}_client ON {self.schema_name}.{table_name}(client_id);
CREATE INDEX idx_{table_name}_supplier ON {self.schema_name}.{table_name}(supplier_id);

COMMENT ON TABLE {self.schema_name}.{table_name} IS '{type_info.description}';
"""
            
        else:
            # 通用关系表
            sql = f"""
CREATE TABLE {self.schema_name}.{table_name} (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_id UUID NOT NULL,
    target_id UUID NOT NULL,
    properties JSONB DEFAULT '{{}}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (source_id) REFERENCES {self.schema_name}.uml_element(id) ON DELETE CASCADE,
    FOREIGN KEY (target_id) REFERENCES {self.schema_name}.uml_element(id) ON DELETE CASCADE
);

CREATE INDEX idx_{table_name}_source ON {self.schema_name}.{table_name}(source_id);
CREATE INDEX idx_{table_name}_target ON {self.schema_name}.{table_name}(target_id);

COMMENT ON TABLE {self.schema_name}.{table_name} IS '{type_info.description}';
"""
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(sql)
    
    async def _create_inheritance_views(self):
        """创建继承视图"""
        view_definitions = self.inheritance_mapper.get_view_definitions()
        
        logger.info(f"创建{len(view_definitions)}个继承视图...")
        
        async with self.db_pool.acquire() as conn:
            for qualified_name, view_sql in view_definitions.items():
                try:
                    await conn.execute(view_sql)
                    self.view_definitions[qualified_name] = view_sql
                except Exception as e:
                    logger.warning(f"创建视图 {qualified_name} 失败: {e}")
        
        logger.info("继承视图创建完成")
    
    async def _create_query_functions(self):
        """创建便捷查询函数"""
        
        functions_sql = f"""
-- 获取元素的完整继承信息
CREATE OR REPLACE FUNCTION {self.schema_name}.get_element_inheritance(element_id UUID)
RETURNS TABLE(
    level INTEGER,
    type_name TEXT,
    table_name TEXT,
    is_abstract BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    WITH RECURSIVE inheritance_tree AS (
        SELECT 0 as level, e.element_type as type_name, 
               'uml_element' as table_name, true as is_abstract
        FROM {self.schema_name}.uml_element e 
        WHERE e.id = element_id
    )
    SELECT * FROM inheritance_tree;
END;
$$ LANGUAGE plpgsql;

-- 获取分类器的所有特征
CREATE OR REPLACE FUNCTION {self.schema_name}.get_classifier_features(classifier_id UUID)
RETURNS TABLE(
    feature_id UUID,
    feature_name TEXT,
    feature_type TEXT,
    is_static BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT f.id, f.name, f.element_type, f.is_static
    FROM {self.schema_name}.uml_feature f
    WHERE f.featuring_classifier_id = classifier_id;
END;
$$ LANGUAGE plpgsql;

-- 检查继承关系
CREATE OR REPLACE FUNCTION {self.schema_name}.is_subtype_of(child_id UUID, parent_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    result BOOLEAN := false;
BEGIN
    -- 检查是否存在泛化关系
    SELECT EXISTS(
        SELECT 1 FROM {self.schema_name}.uml_generalization g
        WHERE g.specific_id = child_id AND g.general_id = parent_id
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;
"""
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(functions_sql)
        
        logger.info("便捷查询函数创建完成")
    
    async def _build_enhanced_generation_result(self) -> Dict[str, Any]:
        """构建增强版生成结果"""
        base_result = await self._build_generation_result()
        
        # 添加增强信息
        base_result['data']['inheritance_strategy'] = {
            'inherit_tables': len(self.inheritance_mapper.get_core_inheritance_tables()),
            'view_tables': len([t for t in self.inheritance_mapper.inheritance_hierarchy.values() if t.table_strategy == 'view']),
            'relation_tables': len([t for t in self.inheritance_mapper.inheritance_hierarchy.values() if t.table_strategy == 'relation']),
            'view_definitions': self.view_definitions,
            'inheritance_hierarchy': {
                qname: {
                    'parent': info.parent_type,
                    'level': info.inheritance_level.value,
                    'strategy': info.table_strategy,
                    'is_abstract': info.is_abstract
                }
                for qname, info in self.inheritance_mapper.inheritance_hierarchy.items()
            }
        }
        
        return base_result

# 便捷函数
async def generate_enhanced_uml25_schema(
    db_pool: asyncpg.Pool, 
    xmi_result: Dict[str, Any],
    config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """生成增强版UML25 Schema"""
    generator = EnhancedUML25SchemaGenerator(db_pool, config)
    return await generator.generate_schema_from_xmi(xmi_result)

def create_enhanced_schema_generator(db_pool: asyncpg.Pool, config: Dict[str, Any] = None) -> EnhancedUML25SchemaGenerator:
    """创建增强版Schema生成器"""
    return EnhancedUML25SchemaGenerator(db_pool, config) 