metaclass_id,qualified_name,simple_name,namespace,is_abstract,parent_qualified_name,inheritance_level,inheritance_level_name,table_strategy,description,inheritance_chain,children_count,descendants_count,concrete_descendants,domain_category,implementation_priority,creation_timestamp
element_metaclass,uml:Element,Element,uml,True,,0,ROOT,inherit,所有UML元素的抽象根类,uml:Element,3,28,"uml:Class, uml:Interface, uml:Enumeration, uml:DataType, uml:PrimitiveType, uml:Signal, uml:Component, uml:Package, uml:Model, uml:Profile, uml:Property, uml:Operation, uml:Parameter, uml:Constraint, uml:Generalization, uml:Dependency, uml:Association, uml:Comment",core.foundation,10,2025-07-01T22:40:14.588556
namedelement_metaclass,uml:NamedElement,NamedElement,uml,True,uml:Element,1,CORE,inherit,具有名称的元素抽象基类,uml:Element -> uml:NamedElement,6,21,"uml:Class, uml:Interface, uml:Enumeration, uml:DataType, uml:PrimitiveType, uml:Signal, uml:Component, uml:Package, uml:Model, uml:Profile, uml:Property, uml:Operation, uml:Parameter, uml:Constraint",core.foundation,9,2025-07-01T22:40:14.588576
relationship_metaclass,uml:Relationship,Relationship,uml,True,uml:Element,1,CORE,inherit,关系的抽象基类,uml:Element -> uml:Relationship,2,4,"uml:Generalization, uml:Dependency, uml:Association",core.relationships,9,2025-07-01T22:40:14.588585
directedrelationship_metaclass,uml:DirectedRelationship,DirectedRelationship,uml,True,uml:Relationship,2,ABSTRACT,inherit,有方向的关系,uml:Element -> uml:Relationship -> uml:DirectedRelationship,2,2,"uml:Generalization, uml:Dependency",core.relationships,8,2025-07-01T22:40:14.588610
feature_metaclass,uml:Feature,Feature,uml,True,uml:NamedElement,2,ABSTRACT,inherit,分类器拥有的特征抽象基类,uml:Element -> uml:NamedElement -> uml:Feature,2,4,"uml:Property, uml:Operation",core.features,8,2025-07-01T22:40:14.588602
namespace_metaclass,uml:Namespace,Namespace,uml,True,uml:NamedElement,2,ABSTRACT,inherit,具有命名空间的元素,uml:Element -> uml:NamedElement -> uml:Namespace,1,3,"uml:Package, uml:Model, uml:Profile",core.organization,8,2025-07-01T22:40:14.588598
packageableelement_metaclass,uml:PackageableElement,PackageableElement,uml,True,uml:NamedElement,2,ABSTRACT,inherit,可放入包中的元素,uml:Element -> uml:NamedElement -> uml:PackageableElement,0,0,,core.organization,8,2025-07-01T22:40:14.588606
type_metaclass,uml:Type,Type,uml,True,uml:NamedElement,2,ABSTRACT,inherit,所有类型的抽象基类,uml:Element -> uml:NamedElement -> uml:Type,1,8,"uml:Class, uml:Interface, uml:Enumeration, uml:DataType, uml:PrimitiveType, uml:Signal, uml:Component",core.typing,8,2025-07-01T22:40:14.588592
behavioralfeature_metaclass,uml:BehavioralFeature,BehavioralFeature,uml,True,uml:Feature,3,SPECIALIZED,inherit,行为特征抽象基类,uml:Element -> uml:NamedElement -> uml:Feature -> uml:BehavioralFeature,1,1,uml:Operation,behavioral.features,7,2025-07-01T22:40:14.588624
classifier_metaclass,uml:Classifier,Classifier,uml,True,uml:Type,3,SPECIALIZED,inherit,分类器抽象基类,uml:Element -> uml:NamedElement -> uml:Type -> uml:Classifier,6,7,"uml:Class, uml:Interface, uml:Enumeration, uml:DataType, uml:PrimitiveType, uml:Signal, uml:Component",structural.classifiers,7,2025-07-01T22:40:14.588615
structuralfeature_metaclass,uml:StructuralFeature,StructuralFeature,uml,True,uml:Feature,3,SPECIALIZED,inherit,结构特征抽象基类,uml:Element -> uml:NamedElement -> uml:Feature -> uml:StructuralFeature,1,1,uml:Property,structural.features,7,2025-07-01T22:40:14.588619
association_metaclass,uml:Association,Association,uml,False,uml:Relationship,4,CONCRETE,view,关联关系,uml:Element -> uml:Relationship -> uml:Association,0,0,,structural.relationships,6,2025-07-01T22:40:14.588682
class_metaclass,uml:Class,Class,uml,False,uml:Classifier,4,CONCRETE,view,类元素,uml:Element -> uml:NamedElement -> uml:Type -> uml:Classifier -> uml:Class,0,0,,structural.classifiers,6,2025-07-01T22:40:14.588629
comment_metaclass,uml:Comment,Comment,uml,False,uml:Element,4,CONCRETE,relation,注释元素,uml:Element -> uml:Comment,0,0,,documentation.annotations,6,2025-07-01T22:40:14.588695
component_metaclass,uml:Component,Component,uml,False,uml:Classifier,4,CONCRETE,view,组件元素,uml:Element -> uml:NamedElement -> uml:Type -> uml:Classifier -> uml:Component,0,0,,structural.components,6,2025-07-01T22:40:14.588654
constraint_metaclass,uml:Constraint,Constraint,uml,False,uml:NamedElement,4,CONCRETE,relation,约束元素,uml:Element -> uml:NamedElement -> uml:Constraint,0,0,,documentation.constraints,6,2025-07-01T22:40:14.588699
datatype_metaclass,uml:DataType,DataType,uml,False,uml:Classifier,4,CONCRETE,view,数据类型元素,uml:Element -> uml:NamedElement -> uml:Type -> uml:Classifier -> uml:DataType,1,1,uml:PrimitiveType,structural.datatypes,6,2025-07-01T22:40:14.588641
dependency_metaclass,uml:Dependency,Dependency,uml,False,uml:DirectedRelationship,4,CONCRETE,relation,依赖关系,uml:Element -> uml:Relationship -> uml:DirectedRelationship -> uml:Dependency,0,0,,structural.relationships,6,2025-07-01T22:40:14.588691
enumeration_metaclass,uml:Enumeration,Enumeration,uml,False,uml:Classifier,4,CONCRETE,view,枚举元素,uml:Element -> uml:NamedElement -> uml:Type -> uml:Classifier -> uml:Enumeration,0,0,,structural.classifiers,6,2025-07-01T22:40:14.588637
generalization_metaclass,uml:Generalization,Generalization,uml,False,uml:DirectedRelationship,4,CONCRETE,relation,泛化关系,uml:Element -> uml:Relationship -> uml:DirectedRelationship -> uml:Generalization,0,0,,structural.relationships,6,2025-07-01T22:40:14.588686
interface_metaclass,uml:Interface,Interface,uml,False,uml:Classifier,4,CONCRETE,view,接口元素,uml:Element -> uml:NamedElement -> uml:Type -> uml:Classifier -> uml:Interface,0,0,,structural.classifiers,6,2025-07-01T22:40:14.588633
model_metaclass,uml:Model,Model,uml,False,uml:Package,4,CONCRETE,view,模型元素,uml:Element -> uml:NamedElement -> uml:Namespace -> uml:Package -> uml:Model,0,0,,structural.organization,6,2025-07-01T22:40:14.588662
operation_metaclass,uml:Operation,Operation,uml,False,uml:BehavioralFeature,4,CONCRETE,view,操作元素,uml:Element -> uml:NamedElement -> uml:Feature -> uml:BehavioralFeature -> uml:Operation,0,0,,behavioral.features,6,2025-07-01T22:40:14.588674
package_metaclass,uml:Package,Package,uml,False,uml:Namespace,4,CONCRETE,view,包元素,uml:Element -> uml:NamedElement -> uml:Namespace -> uml:Package,2,2,"uml:Model, uml:Profile",structural.organization,6,2025-07-01T22:40:14.588658
parameter_metaclass,uml:Parameter,Parameter,uml,False,uml:NamedElement,4,CONCRETE,view,参数元素,uml:Element -> uml:NamedElement -> uml:Parameter,0,0,,behavioral.features,6,2025-07-01T22:40:14.588678
primitivetype_metaclass,uml:PrimitiveType,PrimitiveType,uml,False,uml:DataType,4,CONCRETE,view,原始类型元素,uml:Element -> uml:NamedElement -> uml:Type -> uml:Classifier -> uml:DataType -> uml:PrimitiveType,0,0,,structural.datatypes,6,2025-07-01T22:40:14.588646
profile_metaclass,uml:Profile,Profile,uml,False,uml:Package,4,CONCRETE,view,配置文件元素,uml:Element -> uml:NamedElement -> uml:Namespace -> uml:Package -> uml:Profile,0,0,,structural.organization,6,2025-07-01T22:40:14.588667
property_metaclass,uml:Property,Property,uml,False,uml:StructuralFeature,4,CONCRETE,view,属性元素,uml:Element -> uml:NamedElement -> uml:Feature -> uml:StructuralFeature -> uml:Property,0,0,,structural.features,6,2025-07-01T22:40:14.588671
signal_metaclass,uml:Signal,Signal,uml,False,uml:Classifier,4,CONCRETE,view,信号元素,uml:Element -> uml:NamedElement -> uml:Type -> uml:Classifier -> uml:Signal,0,0,,behavioral.communication,6,2025-07-01T22:40:14.588650
