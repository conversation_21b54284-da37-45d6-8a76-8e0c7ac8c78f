{"metadata": {"generator": "CoreUMLMetaclassGenerator", "version": "1.0.0", "generated_at": "2025-07-01T22:40:14.588932", "total_metaclasses": 29, "schema_name": "uml25_core"}, "metaclass_definitions": [{"metaclass_id": "element_metaclass", "qualified_name": "uml:Element", "simple_name": "Element", "namespace": "uml", "is_abstract": true, "parent_qualified_name": null, "inheritance_level": 0, "inheritance_level_name": "ROOT", "table_strategy": "inherit", "description": "所有UML元素的抽象根类", "inheritance_chain": ["uml:Element"], "children_count": 3, "descendants_count": 28, "concrete_descendants": ["uml:Class", "uml:Interface", "uml:Enumeration", "uml:DataType", "uml:PrimitiveType", "uml:Signal", "uml:Component", "uml:Package", "uml:Model", "uml:Profile", "uml:Property", "uml:Operation", "uml:Parameter", "uml:Constraint", "uml:Generalization", "uml:Dependency", "uml:Association", "uml:Comment"], "domain_category": "core.foundation", "implementation_priority": 10, "creation_timestamp": "2025-07-01T22:40:14.588556"}, {"metaclass_id": "namedelement_metaclass", "qualified_name": "uml:Named<PERSON><PERSON>", "simple_name": "NamedElement", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Element", "inheritance_level": 1, "inheritance_level_name": "CORE", "table_strategy": "inherit", "description": "具有名称的元素抽象基类", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>"], "children_count": 6, "descendants_count": 21, "concrete_descendants": ["uml:Class", "uml:Interface", "uml:Enumeration", "uml:DataType", "uml:PrimitiveType", "uml:Signal", "uml:Component", "uml:Package", "uml:Model", "uml:Profile", "uml:Property", "uml:Operation", "uml:Parameter", "uml:Constraint"], "domain_category": "core.foundation", "implementation_priority": 9, "creation_timestamp": "2025-07-01T22:40:14.588576"}, {"metaclass_id": "relationship_metaclass", "qualified_name": "uml:Relationship", "simple_name": "Relationship", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Element", "inheritance_level": 1, "inheritance_level_name": "CORE", "table_strategy": "inherit", "description": "关系的抽象基类", "inheritance_chain": ["uml:Element", "uml:Relationship"], "children_count": 2, "descendants_count": 4, "concrete_descendants": ["uml:Generalization", "uml:Dependency", "uml:Association"], "domain_category": "core.relationships", "implementation_priority": 9, "creation_timestamp": "2025-07-01T22:40:14.588585"}, {"metaclass_id": "directedrelationship_metaclass", "qualified_name": "uml:DirectedRelationship", "simple_name": "DirectedRelationship", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Relationship", "inheritance_level": 2, "inheritance_level_name": "ABSTRACT", "table_strategy": "inherit", "description": "有方向的关系", "inheritance_chain": ["uml:Element", "uml:Relationship", "uml:DirectedRelationship"], "children_count": 2, "descendants_count": 2, "concrete_descendants": ["uml:Generalization", "uml:Dependency"], "domain_category": "core.relationships", "implementation_priority": 8, "creation_timestamp": "2025-07-01T22:40:14.588610"}, {"metaclass_id": "feature_metaclass", "qualified_name": "uml:Feature", "simple_name": "Feature", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Named<PERSON><PERSON>", "inheritance_level": 2, "inheritance_level_name": "ABSTRACT", "table_strategy": "inherit", "description": "分类器拥有的特征抽象基类", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Feature"], "children_count": 2, "descendants_count": 4, "concrete_descendants": ["uml:Property", "uml:Operation"], "domain_category": "core.features", "implementation_priority": 8, "creation_timestamp": "2025-07-01T22:40:14.588602"}, {"metaclass_id": "namespace_metaclass", "qualified_name": "uml:Namespace", "simple_name": "Namespace", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Named<PERSON><PERSON>", "inheritance_level": 2, "inheritance_level_name": "ABSTRACT", "table_strategy": "inherit", "description": "具有命名空间的元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Namespace"], "children_count": 1, "descendants_count": 3, "concrete_descendants": ["uml:Package", "uml:Model", "uml:Profile"], "domain_category": "core.organization", "implementation_priority": 8, "creation_timestamp": "2025-07-01T22:40:14.588598"}, {"metaclass_id": "packageableelement_metaclass", "qualified_name": "uml:PackageableElement", "simple_name": "PackageableElement", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Named<PERSON><PERSON>", "inheritance_level": 2, "inheritance_level_name": "ABSTRACT", "table_strategy": "inherit", "description": "可放入包中的元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:PackageableElement"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "core.organization", "implementation_priority": 8, "creation_timestamp": "2025-07-01T22:40:14.588606"}, {"metaclass_id": "type_metaclass", "qualified_name": "uml:Type", "simple_name": "Type", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Named<PERSON><PERSON>", "inheritance_level": 2, "inheritance_level_name": "ABSTRACT", "table_strategy": "inherit", "description": "所有类型的抽象基类", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type"], "children_count": 1, "descendants_count": 8, "concrete_descendants": ["uml:Class", "uml:Interface", "uml:Enumeration", "uml:DataType", "uml:PrimitiveType", "uml:Signal", "uml:Component"], "domain_category": "core.typing", "implementation_priority": 8, "creation_timestamp": "2025-07-01T22:40:14.588592"}, {"metaclass_id": "behavioralfeature_metaclass", "qualified_name": "uml:BehavioralFeature", "simple_name": "BehavioralFeature", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Feature", "inheritance_level": 3, "inheritance_level_name": "SPECIALIZED", "table_strategy": "inherit", "description": "行为特征抽象基类", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Feature", "uml:BehavioralFeature"], "children_count": 1, "descendants_count": 1, "concrete_descendants": ["uml:Operation"], "domain_category": "behavioral.features", "implementation_priority": 7, "creation_timestamp": "2025-07-01T22:40:14.588624"}, {"metaclass_id": "classifier_metaclass", "qualified_name": "uml:Classifier", "simple_name": "Classifier", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Type", "inheritance_level": 3, "inheritance_level_name": "SPECIALIZED", "table_strategy": "inherit", "description": "分类器抽象基类", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type", "uml:Classifier"], "children_count": 6, "descendants_count": 7, "concrete_descendants": ["uml:Class", "uml:Interface", "uml:Enumeration", "uml:DataType", "uml:PrimitiveType", "uml:Signal", "uml:Component"], "domain_category": "structural.classifiers", "implementation_priority": 7, "creation_timestamp": "2025-07-01T22:40:14.588615"}, {"metaclass_id": "structuralfeature_metaclass", "qualified_name": "uml:StructuralFeature", "simple_name": "StructuralFeature", "namespace": "uml", "is_abstract": true, "parent_qualified_name": "uml:Feature", "inheritance_level": 3, "inheritance_level_name": "SPECIALIZED", "table_strategy": "inherit", "description": "结构特征抽象基类", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Feature", "uml:StructuralFeature"], "children_count": 1, "descendants_count": 1, "concrete_descendants": ["uml:Property"], "domain_category": "structural.features", "implementation_priority": 7, "creation_timestamp": "2025-07-01T22:40:14.588619"}, {"metaclass_id": "association_metaclass", "qualified_name": "uml:Association", "simple_name": "Association", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Relationship", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "关联关系", "inheritance_chain": ["uml:Element", "uml:Relationship", "uml:Association"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.relationships", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588682"}, {"metaclass_id": "class_metaclass", "qualified_name": "uml:Class", "simple_name": "Class", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Classifier", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "类元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type", "uml:Classifier", "uml:Class"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.classifiers", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588629"}, {"metaclass_id": "comment_metaclass", "qualified_name": "uml:Comment", "simple_name": "Comment", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Element", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "relation", "description": "注释元素", "inheritance_chain": ["uml:Element", "uml:Comment"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "documentation.annotations", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588695"}, {"metaclass_id": "component_metaclass", "qualified_name": "uml:Component", "simple_name": "Component", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Classifier", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "组件元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type", "uml:Classifier", "uml:Component"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.components", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588654"}, {"metaclass_id": "constraint_metaclass", "qualified_name": "uml:Constraint", "simple_name": "Constraint", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Named<PERSON><PERSON>", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "relation", "description": "约束元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Constraint"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "documentation.constraints", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588699"}, {"metaclass_id": "datatype_metaclass", "qualified_name": "uml:DataType", "simple_name": "DataType", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Classifier", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "数据类型元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type", "uml:Classifier", "uml:DataType"], "children_count": 1, "descendants_count": 1, "concrete_descendants": ["uml:PrimitiveType"], "domain_category": "structural.datatypes", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588641"}, {"metaclass_id": "dependency_metaclass", "qualified_name": "uml:Dependency", "simple_name": "Dependency", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:DirectedRelationship", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "relation", "description": "依赖关系", "inheritance_chain": ["uml:Element", "uml:Relationship", "uml:DirectedRelationship", "uml:Dependency"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.relationships", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588691"}, {"metaclass_id": "enumeration_metaclass", "qualified_name": "uml:Enumeration", "simple_name": "Enumeration", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Classifier", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "枚举元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type", "uml:Classifier", "uml:Enumeration"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.classifiers", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588637"}, {"metaclass_id": "generalization_metaclass", "qualified_name": "uml:Generalization", "simple_name": "Generalization", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:DirectedRelationship", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "relation", "description": "泛化关系", "inheritance_chain": ["uml:Element", "uml:Relationship", "uml:DirectedRelationship", "uml:Generalization"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.relationships", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588686"}, {"metaclass_id": "interface_metaclass", "qualified_name": "uml:Interface", "simple_name": "Interface", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Classifier", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "接口元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type", "uml:Classifier", "uml:Interface"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.classifiers", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588633"}, {"metaclass_id": "model_metaclass", "qualified_name": "uml:Model", "simple_name": "Model", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Package", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "模型元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Namespace", "uml:Package", "uml:Model"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.organization", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588662"}, {"metaclass_id": "operation_metaclass", "qualified_name": "uml:Operation", "simple_name": "Operation", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:BehavioralFeature", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "操作元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Feature", "uml:BehavioralFeature", "uml:Operation"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "behavioral.features", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588674"}, {"metaclass_id": "package_metaclass", "qualified_name": "uml:Package", "simple_name": "Package", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Namespace", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "包元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Namespace", "uml:Package"], "children_count": 2, "descendants_count": 2, "concrete_descendants": ["uml:Model", "uml:Profile"], "domain_category": "structural.organization", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588658"}, {"metaclass_id": "parameter_metaclass", "qualified_name": "uml:Parameter", "simple_name": "Parameter", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Named<PERSON><PERSON>", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "参数元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Parameter"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "behavioral.features", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588678"}, {"metaclass_id": "primitivetype_metaclass", "qualified_name": "uml:PrimitiveType", "simple_name": "PrimitiveType", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:DataType", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "原始类型元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type", "uml:Classifier", "uml:DataType", "uml:PrimitiveType"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.datatypes", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588646"}, {"metaclass_id": "profile_metaclass", "qualified_name": "uml:Profile", "simple_name": "Profile", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Package", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "配置文件元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Namespace", "uml:Package", "uml:Profile"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.organization", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588667"}, {"metaclass_id": "property_metaclass", "qualified_name": "uml:Property", "simple_name": "Property", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:StructuralFeature", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "属性元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Feature", "uml:StructuralFeature", "uml:Property"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "structural.features", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588671"}, {"metaclass_id": "signal_metaclass", "qualified_name": "uml:Signal", "simple_name": "Signal", "namespace": "uml", "is_abstract": false, "parent_qualified_name": "uml:Classifier", "inheritance_level": 4, "inheritance_level_name": "CONCRETE", "table_strategy": "view", "description": "信号元素", "inheritance_chain": ["uml:Element", "uml:Named<PERSON><PERSON>", "uml:Type", "uml:Classifier", "uml:Signal"], "children_count": 0, "descendants_count": 0, "concrete_descendants": [], "domain_category": "behavioral.communication", "implementation_priority": 6, "creation_timestamp": "2025-07-01T22:40:14.588650"}]}