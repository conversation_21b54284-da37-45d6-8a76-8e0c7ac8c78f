-- 核心UML元类定义SQL插入语句
-- 生成时间: 2025-07-01T22:40:14.591471
-- 生成器: CoreUMLMetaclassGenerator v1.0.0
-- 总计: 29 个元类定义

-- 创建Schema (如果不存在)
CREATE SCHEMA IF NOT EXISTS uml25_core;

-- 清除现有数据
DELETE FROM uml25_core.core_uml_metaclass_definitions;

-- 插入核心UML元类定义数据

INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'element_metaclass',
    'uml:Element',
    'Element',
    'uml',
    true,
    NULL,
    0,
    'ROOT',
    'inherit',
    '所有UML元素的抽象根类',
    '["uml:Element"]',
    3,
    28,
    '["uml:Class", "uml:Interface", "uml:Enumeration", "uml:DataType", "uml:PrimitiveType", "uml:Signal", "uml:Component", "uml:Package", "uml:Model", "uml:Profile", "uml:Property", "uml:Operation", "uml:Parameter", "uml:Constraint", "uml:Generalization", "uml:Dependency", "uml:Association", "uml:Comment"]',
    'core.foundation',
    10,
    '2025-07-01T22:40:14.588556'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'namedelement_metaclass',
    'uml:NamedElement',
    'NamedElement',
    'uml',
    true,
    'uml:Element',
    1,
    'CORE',
    'inherit',
    '具有名称的元素抽象基类',
    '["uml:Element", "uml:NamedElement"]',
    6,
    21,
    '["uml:Class", "uml:Interface", "uml:Enumeration", "uml:DataType", "uml:PrimitiveType", "uml:Signal", "uml:Component", "uml:Package", "uml:Model", "uml:Profile", "uml:Property", "uml:Operation", "uml:Parameter", "uml:Constraint"]',
    'core.foundation',
    9,
    '2025-07-01T22:40:14.588576'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'relationship_metaclass',
    'uml:Relationship',
    'Relationship',
    'uml',
    true,
    'uml:Element',
    1,
    'CORE',
    'inherit',
    '关系的抽象基类',
    '["uml:Element", "uml:Relationship"]',
    2,
    4,
    '["uml:Generalization", "uml:Dependency", "uml:Association"]',
    'core.relationships',
    9,
    '2025-07-01T22:40:14.588585'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'directedrelationship_metaclass',
    'uml:DirectedRelationship',
    'DirectedRelationship',
    'uml',
    true,
    'uml:Relationship',
    2,
    'ABSTRACT',
    'inherit',
    '有方向的关系',
    '["uml:Element", "uml:Relationship", "uml:DirectedRelationship"]',
    2,
    2,
    '["uml:Generalization", "uml:Dependency"]',
    'core.relationships',
    8,
    '2025-07-01T22:40:14.588610'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'feature_metaclass',
    'uml:Feature',
    'Feature',
    'uml',
    true,
    'uml:NamedElement',
    2,
    'ABSTRACT',
    'inherit',
    '分类器拥有的特征抽象基类',
    '["uml:Element", "uml:NamedElement", "uml:Feature"]',
    2,
    4,
    '["uml:Property", "uml:Operation"]',
    'core.features',
    8,
    '2025-07-01T22:40:14.588602'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'namespace_metaclass',
    'uml:Namespace',
    'Namespace',
    'uml',
    true,
    'uml:NamedElement',
    2,
    'ABSTRACT',
    'inherit',
    '具有命名空间的元素',
    '["uml:Element", "uml:NamedElement", "uml:Namespace"]',
    1,
    3,
    '["uml:Package", "uml:Model", "uml:Profile"]',
    'core.organization',
    8,
    '2025-07-01T22:40:14.588598'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'packageableelement_metaclass',
    'uml:PackageableElement',
    'PackageableElement',
    'uml',
    true,
    'uml:NamedElement',
    2,
    'ABSTRACT',
    'inherit',
    '可放入包中的元素',
    '["uml:Element", "uml:NamedElement", "uml:PackageableElement"]',
    0,
    0,
    '[]',
    'core.organization',
    8,
    '2025-07-01T22:40:14.588606'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'type_metaclass',
    'uml:Type',
    'Type',
    'uml',
    true,
    'uml:NamedElement',
    2,
    'ABSTRACT',
    'inherit',
    '所有类型的抽象基类',
    '["uml:Element", "uml:NamedElement", "uml:Type"]',
    1,
    8,
    '["uml:Class", "uml:Interface", "uml:Enumeration", "uml:DataType", "uml:PrimitiveType", "uml:Signal", "uml:Component"]',
    'core.typing',
    8,
    '2025-07-01T22:40:14.588592'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'behavioralfeature_metaclass',
    'uml:BehavioralFeature',
    'BehavioralFeature',
    'uml',
    true,
    'uml:Feature',
    3,
    'SPECIALIZED',
    'inherit',
    '行为特征抽象基类',
    '["uml:Element", "uml:NamedElement", "uml:Feature", "uml:BehavioralFeature"]',
    1,
    1,
    '["uml:Operation"]',
    'behavioral.features',
    7,
    '2025-07-01T22:40:14.588624'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'classifier_metaclass',
    'uml:Classifier',
    'Classifier',
    'uml',
    true,
    'uml:Type',
    3,
    'SPECIALIZED',
    'inherit',
    '分类器抽象基类',
    '["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier"]',
    6,
    7,
    '["uml:Class", "uml:Interface", "uml:Enumeration", "uml:DataType", "uml:PrimitiveType", "uml:Signal", "uml:Component"]',
    'structural.classifiers',
    7,
    '2025-07-01T22:40:14.588615'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'structuralfeature_metaclass',
    'uml:StructuralFeature',
    'StructuralFeature',
    'uml',
    true,
    'uml:Feature',
    3,
    'SPECIALIZED',
    'inherit',
    '结构特征抽象基类',
    '["uml:Element", "uml:NamedElement", "uml:Feature", "uml:StructuralFeature"]',
    1,
    1,
    '["uml:Property"]',
    'structural.features',
    7,
    '2025-07-01T22:40:14.588619'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'association_metaclass',
    'uml:Association',
    'Association',
    'uml',
    false,
    'uml:Relationship',
    4,
    'CONCRETE',
    'view',
    '关联关系',
    '["uml:Element", "uml:Relationship", "uml:Association"]',
    0,
    0,
    '[]',
    'structural.relationships',
    6,
    '2025-07-01T22:40:14.588682'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'class_metaclass',
    'uml:Class',
    'Class',
    'uml',
    false,
    'uml:Classifier',
    4,
    'CONCRETE',
    'view',
    '类元素',
    '["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier", "uml:Class"]',
    0,
    0,
    '[]',
    'structural.classifiers',
    6,
    '2025-07-01T22:40:14.588629'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'comment_metaclass',
    'uml:Comment',
    'Comment',
    'uml',
    false,
    'uml:Element',
    4,
    'CONCRETE',
    'relation',
    '注释元素',
    '["uml:Element", "uml:Comment"]',
    0,
    0,
    '[]',
    'documentation.annotations',
    6,
    '2025-07-01T22:40:14.588695'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'component_metaclass',
    'uml:Component',
    'Component',
    'uml',
    false,
    'uml:Classifier',
    4,
    'CONCRETE',
    'view',
    '组件元素',
    '["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier", "uml:Component"]',
    0,
    0,
    '[]',
    'structural.components',
    6,
    '2025-07-01T22:40:14.588654'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'constraint_metaclass',
    'uml:Constraint',
    'Constraint',
    'uml',
    false,
    'uml:NamedElement',
    4,
    'CONCRETE',
    'relation',
    '约束元素',
    '["uml:Element", "uml:NamedElement", "uml:Constraint"]',
    0,
    0,
    '[]',
    'documentation.constraints',
    6,
    '2025-07-01T22:40:14.588699'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'datatype_metaclass',
    'uml:DataType',
    'DataType',
    'uml',
    false,
    'uml:Classifier',
    4,
    'CONCRETE',
    'view',
    '数据类型元素',
    '["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier", "uml:DataType"]',
    1,
    1,
    '["uml:PrimitiveType"]',
    'structural.datatypes',
    6,
    '2025-07-01T22:40:14.588641'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'dependency_metaclass',
    'uml:Dependency',
    'Dependency',
    'uml',
    false,
    'uml:DirectedRelationship',
    4,
    'CONCRETE',
    'relation',
    '依赖关系',
    '["uml:Element", "uml:Relationship", "uml:DirectedRelationship", "uml:Dependency"]',
    0,
    0,
    '[]',
    'structural.relationships',
    6,
    '2025-07-01T22:40:14.588691'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'enumeration_metaclass',
    'uml:Enumeration',
    'Enumeration',
    'uml',
    false,
    'uml:Classifier',
    4,
    'CONCRETE',
    'view',
    '枚举元素',
    '["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier", "uml:Enumeration"]',
    0,
    0,
    '[]',
    'structural.classifiers',
    6,
    '2025-07-01T22:40:14.588637'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'generalization_metaclass',
    'uml:Generalization',
    'Generalization',
    'uml',
    false,
    'uml:DirectedRelationship',
    4,
    'CONCRETE',
    'relation',
    '泛化关系',
    '["uml:Element", "uml:Relationship", "uml:DirectedRelationship", "uml:Generalization"]',
    0,
    0,
    '[]',
    'structural.relationships',
    6,
    '2025-07-01T22:40:14.588686'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'interface_metaclass',
    'uml:Interface',
    'Interface',
    'uml',
    false,
    'uml:Classifier',
    4,
    'CONCRETE',
    'view',
    '接口元素',
    '["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier", "uml:Interface"]',
    0,
    0,
    '[]',
    'structural.classifiers',
    6,
    '2025-07-01T22:40:14.588633'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'model_metaclass',
    'uml:Model',
    'Model',
    'uml',
    false,
    'uml:Package',
    4,
    'CONCRETE',
    'view',
    '模型元素',
    '["uml:Element", "uml:NamedElement", "uml:Namespace", "uml:Package", "uml:Model"]',
    0,
    0,
    '[]',
    'structural.organization',
    6,
    '2025-07-01T22:40:14.588662'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'operation_metaclass',
    'uml:Operation',
    'Operation',
    'uml',
    false,
    'uml:BehavioralFeature',
    4,
    'CONCRETE',
    'view',
    '操作元素',
    '["uml:Element", "uml:NamedElement", "uml:Feature", "uml:BehavioralFeature", "uml:Operation"]',
    0,
    0,
    '[]',
    'behavioral.features',
    6,
    '2025-07-01T22:40:14.588674'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'package_metaclass',
    'uml:Package',
    'Package',
    'uml',
    false,
    'uml:Namespace',
    4,
    'CONCRETE',
    'view',
    '包元素',
    '["uml:Element", "uml:NamedElement", "uml:Namespace", "uml:Package"]',
    2,
    2,
    '["uml:Model", "uml:Profile"]',
    'structural.organization',
    6,
    '2025-07-01T22:40:14.588658'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'parameter_metaclass',
    'uml:Parameter',
    'Parameter',
    'uml',
    false,
    'uml:NamedElement',
    4,
    'CONCRETE',
    'view',
    '参数元素',
    '["uml:Element", "uml:NamedElement", "uml:Parameter"]',
    0,
    0,
    '[]',
    'behavioral.features',
    6,
    '2025-07-01T22:40:14.588678'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'primitivetype_metaclass',
    'uml:PrimitiveType',
    'PrimitiveType',
    'uml',
    false,
    'uml:DataType',
    4,
    'CONCRETE',
    'view',
    '原始类型元素',
    '["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier", "uml:DataType", "uml:PrimitiveType"]',
    0,
    0,
    '[]',
    'structural.datatypes',
    6,
    '2025-07-01T22:40:14.588646'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'profile_metaclass',
    'uml:Profile',
    'Profile',
    'uml',
    false,
    'uml:Package',
    4,
    'CONCRETE',
    'view',
    '配置文件元素',
    '["uml:Element", "uml:NamedElement", "uml:Namespace", "uml:Package", "uml:Profile"]',
    0,
    0,
    '[]',
    'structural.organization',
    6,
    '2025-07-01T22:40:14.588667'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'property_metaclass',
    'uml:Property',
    'Property',
    'uml',
    false,
    'uml:StructuralFeature',
    4,
    'CONCRETE',
    'view',
    '属性元素',
    '["uml:Element", "uml:NamedElement", "uml:Feature", "uml:StructuralFeature", "uml:Property"]',
    0,
    0,
    '[]',
    'structural.features',
    6,
    '2025-07-01T22:40:14.588671'
);
INSERT INTO uml25_core.core_uml_metaclass_definitions
(metaclass_id, qualified_name, simple_name, namespace, is_abstract,
 parent_qualified_name, inheritance_level, inheritance_level_name,
 table_strategy, description, inheritance_chain, children_count,
 descendants_count, concrete_descendants, domain_category,
 implementation_priority, creation_timestamp)
VALUES (
    'signal_metaclass',
    'uml:Signal',
    'Signal',
    'uml',
    false,
    'uml:Classifier',
    4,
    'CONCRETE',
    'view',
    '信号元素',
    '["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier", "uml:Signal"]',
    0,
    0,
    '[]',
    'behavioral.communication',
    6,
    '2025-07-01T22:40:14.588650'
);

-- 提交事务
COMMIT;
