# 核心UML元类定义汇总报告

## 基本信息
- **生成时间**: 2025-07-01 22:40:14
- **生成器版本**: CoreUMLMetaclassGenerator v1.0.0
- **Schema名称**: uml25_core
- **元类总数**: 29
- **抽象类数量**: 11
- **具体类数量**: 18

## 继承层级统计

| 层级 | 总数 | 抽象 | 具体 | 描述 |
|------|------|------|------|------|| ROOT | 1 | 1 | 0 | 根层级 - Element |
| CORE | 2 | 2 | 0 | 核心层级 - NamedElement, Relationship |
| ABSTRACT | 5 | 5 | 0 | 抽象层级 - Type, Namespace, Feature |
| SPECIALIZED | 3 | 3 | 0 | 特化层级 - Classifier, StructuralFeature |
| CONCRETE | 18 | 0 | 18 | 具体层级 - Class, Property, Operation |

## 表实现策略统计

| 策略 | 数量 | 百分比 | 描述 |
|------|------|--------|------|| inherit | 11 | 37.9% | 表继承策略 - PostgreSQL原生继承 |
| relation | 4 | 13.8% | 关系表策略 - 独立关系管理 |
| view | 14 | 48.3% | 视图策略 - 组合继承链视图 |

## 领域分类统计

| 领域分类 | 数量 | 百分比 |
|----------|------|--------|| behavioral.communication | 1 | 3.4% |
| behavioral.features | 3 | 10.3% |
| core.features | 1 | 3.4% |
| core.foundation | 2 | 6.9% |
| core.organization | 2 | 6.9% |
| core.relationships | 2 | 6.9% |
| core.typing | 1 | 3.4% |
| documentation.annotations | 1 | 3.4% |
| documentation.constraints | 1 | 3.4% |
| structural.classifiers | 4 | 13.8% |
| structural.components | 1 | 3.4% |
| structural.datatypes | 2 | 6.9% |
| structural.features | 2 | 6.9% |
| structural.organization | 3 | 10.3% |
| structural.relationships | 3 | 10.3% |

## 详细元类列表

### 根层级 (ROOT)

### ROOT层级

- **uml:Element** (抽象)
  - 策略: inherit
  - 领域: core.foundation
  - 子类: 3个
  - 后代: 28个
  - 描述: 所有UML元素的抽象根类


### CORE层级

- **uml:NamedElement** (抽象)
  - 策略: inherit
  - 领域: core.foundation
  - 子类: 6个
  - 后代: 21个
  - 描述: 具有名称的元素抽象基类

- **uml:Relationship** (抽象)
  - 策略: inherit
  - 领域: core.relationships
  - 子类: 2个
  - 后代: 4个
  - 描述: 关系的抽象基类


### ABSTRACT层级

- **uml:DirectedRelationship** (抽象)
  - 策略: inherit
  - 领域: core.relationships
  - 子类: 2个
  - 后代: 2个
  - 描述: 有方向的关系

- **uml:Feature** (抽象)
  - 策略: inherit
  - 领域: core.features
  - 子类: 2个
  - 后代: 4个
  - 描述: 分类器拥有的特征抽象基类

- **uml:Namespace** (抽象)
  - 策略: inherit
  - 领域: core.organization
  - 子类: 1个
  - 后代: 3个
  - 描述: 具有命名空间的元素

- **uml:PackageableElement** (抽象)
  - 策略: inherit
  - 领域: core.organization
  - 子类: 0个
  - 后代: 0个
  - 描述: 可放入包中的元素

- **uml:Type** (抽象)
  - 策略: inherit
  - 领域: core.typing
  - 子类: 1个
  - 后代: 8个
  - 描述: 所有类型的抽象基类


### SPECIALIZED层级

- **uml:BehavioralFeature** (抽象)
  - 策略: inherit
  - 领域: behavioral.features
  - 子类: 1个
  - 后代: 1个
  - 描述: 行为特征抽象基类

- **uml:Classifier** (抽象)
  - 策略: inherit
  - 领域: structural.classifiers
  - 子类: 6个
  - 后代: 7个
  - 描述: 分类器抽象基类

- **uml:StructuralFeature** (抽象)
  - 策略: inherit
  - 领域: structural.features
  - 子类: 1个
  - 后代: 1个
  - 描述: 结构特征抽象基类


### CONCRETE层级

- **uml:Association**
  - 策略: view
  - 领域: structural.relationships
  - 子类: 0个
  - 后代: 0个
  - 描述: 关联关系

- **uml:Class**
  - 策略: view
  - 领域: structural.classifiers
  - 子类: 0个
  - 后代: 0个
  - 描述: 类元素

- **uml:Comment**
  - 策略: relation
  - 领域: documentation.annotations
  - 子类: 0个
  - 后代: 0个
  - 描述: 注释元素

- **uml:Component**
  - 策略: view
  - 领域: structural.components
  - 子类: 0个
  - 后代: 0个
  - 描述: 组件元素

- **uml:Constraint**
  - 策略: relation
  - 领域: documentation.constraints
  - 子类: 0个
  - 后代: 0个
  - 描述: 约束元素

- **uml:DataType**
  - 策略: view
  - 领域: structural.datatypes
  - 子类: 1个
  - 后代: 1个
  - 描述: 数据类型元素

- **uml:Dependency**
  - 策略: relation
  - 领域: structural.relationships
  - 子类: 0个
  - 后代: 0个
  - 描述: 依赖关系

- **uml:Enumeration**
  - 策略: view
  - 领域: structural.classifiers
  - 子类: 0个
  - 后代: 0个
  - 描述: 枚举元素

- **uml:Generalization**
  - 策略: relation
  - 领域: structural.relationships
  - 子类: 0个
  - 后代: 0个
  - 描述: 泛化关系

- **uml:Interface**
  - 策略: view
  - 领域: structural.classifiers
  - 子类: 0个
  - 后代: 0个
  - 描述: 接口元素

- **uml:Model**
  - 策略: view
  - 领域: structural.organization
  - 子类: 0个
  - 后代: 0个
  - 描述: 模型元素

- **uml:Operation**
  - 策略: view
  - 领域: behavioral.features
  - 子类: 0个
  - 后代: 0个
  - 描述: 操作元素

- **uml:Package**
  - 策略: view
  - 领域: structural.organization
  - 子类: 2个
  - 后代: 2个
  - 描述: 包元素

- **uml:Parameter**
  - 策略: view
  - 领域: behavioral.features
  - 子类: 0个
  - 后代: 0个
  - 描述: 参数元素

- **uml:PrimitiveType**
  - 策略: view
  - 领域: structural.datatypes
  - 子类: 0个
  - 后代: 0个
  - 描述: 原始类型元素

- **uml:Profile**
  - 策略: view
  - 领域: structural.organization
  - 子类: 0个
  - 后代: 0个
  - 描述: 配置文件元素

- **uml:Property**
  - 策略: view
  - 领域: structural.features
  - 子类: 0个
  - 后代: 0个
  - 描述: 属性元素

- **uml:Signal**
  - 策略: view
  - 领域: behavioral.communication
  - 子类: 0个
  - 后代: 0个
  - 描述: 信号元素

