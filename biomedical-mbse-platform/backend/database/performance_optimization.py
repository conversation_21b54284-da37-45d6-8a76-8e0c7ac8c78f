#!/usr/bin/env python3
"""
UML25数据库性能优化模块

包含：
- 智能索引策略
- 查询优化
- 物化视图管理
- 性能基准测试
- 并发性能分析
"""

import asyncio
import asyncpg
import time
import logging
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import uuid
import json

logger = logging.getLogger(__name__)

@dataclass
class IndexDefinition:
    """索引定义"""
    name: str
    table: str
    columns: List[str]
    index_type: str = "btree"  # btree, gin, gist, hash
    is_unique: bool = False
    is_partial: bool = False
    condition: Optional[str] = None
    concurrently: bool = True
    comment: str = ""

@dataclass
class MaterializedViewDefinition:
    """物化视图定义"""
    name: str
    schema: str
    query: str
    refresh_schedule: str = "HOURLY"  # HOURLY, DAILY, WEEKLY
    indexes: List[IndexDefinition] = field(default_factory=list)
    comment: str = ""

@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation: str
    duration_ms: float
    rows_affected: int
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)

class UML25PerformanceOptimizer:
    """UML25性能优化器"""
    
    def __init__(self, db_pool: asyncpg.Pool, schema_name: str = "uml25_base"):
        self.db_pool = db_pool
        self.schema_name = schema_name
        self.metrics_history: List[PerformanceMetrics] = []
        
    async def apply_all_optimizations(self):
        """应用所有性能优化"""
        logger.info("开始应用UML25性能优化...")
        
        start_time = time.time()
        
        # 1. 创建核心索引
        await self._create_core_indexes()
        
        # 2. 创建关系查询索引
        await self._create_relationship_indexes()
        
        # 3. 创建全文搜索索引
        await self._create_fulltext_indexes()
        
        # 4. 创建物化视图
        await self._create_materialized_views()
        
        # 5. 创建分区表（如果需要）
        await self._setup_partitioning()
        
        # 6. 创建查询优化函数
        await self._create_optimization_functions()
        
        total_time = time.time() - start_time
        logger.info(f"性能优化完成，总耗时: {total_time:.2f}秒")
        
        return {
            'status': 'success',
            'duration': total_time,
            'optimizations_applied': [
                'core_indexes', 'relationship_indexes', 'fulltext_indexes',
                'materialized_views', 'partitioning', 'optimization_functions'
            ]
        }
    
    async def _create_core_indexes(self):
        """创建核心索引"""
        logger.info("创建核心索引...")
        
        core_indexes = [
            # Element表核心索引
            IndexDefinition(
                "idx_element_type_xmi", f"{self.schema_name}.uml_element",
                ["element_type", "xmi_id"],
                comment="元素类型和XMI ID组合索引"
            ),
            IndexDefinition(
                "idx_element_properties_gin", f"{self.schema_name}.uml_element",
                ["properties"], index_type="gin",
                comment="元素属性GIN索引，支持JSON查询"
            ),
            IndexDefinition(
                "idx_element_created_at", f"{self.schema_name}.uml_element",
                ["created_at"],
                comment="元素创建时间索引"
            ),
            
            # NamedElement表索引
            IndexDefinition(
                "idx_named_element_name_trgm", f"{self.schema_name}.uml_named_element",
                ["name"], index_type="gin",
                comment="名称模糊搜索三元组索引"
            ),
            IndexDefinition(
                "idx_named_element_qualified_name", f"{self.schema_name}.uml_named_element",
                ["qualified_name"],
                comment="限定名称索引"
            ),
            IndexDefinition(
                "idx_named_element_visibility", f"{self.schema_name}.uml_named_element",
                ["visibility", "element_type"],
                comment="可见性和类型组合索引"
            ),
            
            # Classifier表索引
            IndexDefinition(
                "idx_classifier_abstract_leaf", f"{self.schema_name}.uml_classifier",
                ["is_abstract", "is_leaf"],
                comment="抽象和叶节点状态索引"
            ),
            IndexDefinition(
                "idx_classifier_generalizations_gin", f"{self.schema_name}.uml_classifier",
                ["generalizations"], index_type="gin",
                comment="泛化关系GIN索引"
            ),
            
            # Feature表索引
            IndexDefinition(
                "idx_feature_classifier_static", f"{self.schema_name}.uml_feature",
                ["featuring_classifier_id", "is_static"],
                comment="特征分类器和静态标志索引"
            ),
            
            # StructuralFeature表索引
            IndexDefinition(
                "idx_structural_feature_type", f"{self.schema_name}.uml_structural_feature",
                ["type_id", "multiplicity"],
                comment="结构特征类型和多重性索引"
            ),
        ]
        
        await self._create_indexes_batch(core_indexes)
    
    async def _create_relationship_indexes(self):
        """创建关系查询索引"""
        logger.info("创建关系查询索引...")
        
        relationship_indexes = [
            # 元素关系表索引
            IndexDefinition(
                "idx_relationships_source_type", f"{self.schema_name}.element_relationships",
                ["source_id", "relationship_type"],
                comment="关系源和类型索引"
            ),
            IndexDefinition(
                "idx_relationships_target_type", f"{self.schema_name}.element_relationships",
                ["target_id", "relationship_type"],
                comment="关系目标和类型索引"
            ),
            IndexDefinition(
                "idx_relationships_properties_gin", f"{self.schema_name}.element_relationships",
                ["properties"], index_type="gin",
                comment="关系属性GIN索引"
            ),
            
            # 泛化关系索引
            IndexDefinition(
                "idx_generalization_general", f"{self.schema_name}.uml_generalization",
                ["general_id"],
                comment="泛化关系通用端索引"
            ),
            IndexDefinition(
                "idx_generalization_specific", f"{self.schema_name}.uml_generalization",
                ["specific_id"],
                comment="泛化关系特化端索引"
            ),
            
            # 依赖关系索引
            IndexDefinition(
                "idx_dependency_client_supplier", f"{self.schema_name}.uml_dependency",
                ["client_id", "supplier_id"],
                comment="依赖关系客户端和供应商索引"
            ),
        ]
        
        await self._create_indexes_batch(relationship_indexes)
    
    async def _create_fulltext_indexes(self):
        """创建全文搜索索引"""
        logger.info("创建全文搜索索引...")
        
        # 启用pg_trgm扩展
        async with self.db_pool.acquire() as conn:
            await conn.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm")
            await conn.execute("CREATE EXTENSION IF NOT EXISTS btree_gin")
        
        fulltext_indexes = [
            # 名称全文搜索
            IndexDefinition(
                "idx_named_element_fulltext", f"{self.schema_name}.uml_named_element",
                ["to_tsvector('english', coalesce(name, '') || ' ' || coalesce(qualified_name, ''))"],
                index_type="gin",
                comment="名称和限定名称全文搜索索引"
            ),
            
            # 文档全文搜索
            IndexDefinition(
                "idx_element_documentation_fulltext", f"{self.schema_name}.uml_element",
                ["to_tsvector('english', coalesce(documentation, ''))"],
                index_type="gin",
                condition="documentation IS NOT NULL",
                is_partial=True,
                comment="文档内容全文搜索索引"
            ),
        ]
        
        await self._create_indexes_batch(fulltext_indexes)
    
    async def _create_materialized_views(self):
        """创建物化视图"""
        logger.info("创建物化视图...")
        
        materialized_views = [
            # 分类器继承层次视图
            MaterializedViewDefinition(
                "classifier_hierarchy",
                self.schema_name,
                f"""
                WITH RECURSIVE hierarchy AS (
                    -- 根分类器（没有泛化关系的）
                    SELECT 
                        c.id, c.name, c.element_type, c.is_abstract,
                        0 as level, 
                        ARRAY[c.id] as path,
                        c.id as root_id
                    FROM {self.schema_name}.uml_classifier c
                    WHERE NOT EXISTS (
                        SELECT 1 FROM {self.schema_name}.uml_generalization g
                        WHERE g.specific_id = c.id
                    )
                    
                    UNION ALL
                    
                    -- 子分类器
                    SELECT 
                        c.id, c.name, c.element_type, c.is_abstract,
                        h.level + 1,
                        h.path || c.id,
                        h.root_id
                    FROM {self.schema_name}.uml_classifier c
                    JOIN {self.schema_name}.uml_generalization g ON g.specific_id = c.id
                    JOIN hierarchy h ON h.id = g.general_id
                    WHERE NOT c.id = ANY(h.path)  -- 防止循环引用
                )
                SELECT * FROM hierarchy
                """,
                refresh_schedule="HOURLY",
                indexes=[
                    IndexDefinition(
                        "idx_classifier_hierarchy_level", "classifier_hierarchy",
                        ["level", "root_id"],
                        comment="层次级别和根ID索引"
                    ),
                    IndexDefinition(
                        "idx_classifier_hierarchy_path", "classifier_hierarchy",
                        ["path"], index_type="gin",
                        comment="继承路径GIN索引"
                    ),
                ],
                comment="分类器继承层次物化视图"
            ),
            
            # 特征统计视图
            MaterializedViewDefinition(
                "classifier_feature_stats",
                self.schema_name,
                f"""
                SELECT 
                    c.id as classifier_id,
                    c.name as classifier_name,
                    c.element_type,
                    COUNT(CASE WHEN f.element_type LIKE '%Property%' THEN 1 END) as property_count,
                    COUNT(CASE WHEN f.element_type LIKE '%Operation%' THEN 1 END) as operation_count,
                    COUNT(CASE WHEN f.is_static = true THEN 1 END) as static_feature_count,
                    COUNT(*) as total_feature_count
                FROM {self.schema_name}.uml_classifier c
                LEFT JOIN {self.schema_name}.uml_feature f ON f.featuring_classifier_id = c.id
                GROUP BY c.id, c.name, c.element_type
                """,
                refresh_schedule="DAILY",
                indexes=[
                    IndexDefinition(
                        "idx_classifier_feature_stats_counts", "classifier_feature_stats",
                        ["property_count", "operation_count"],
                        comment="特征数量统计索引"
                    ),
                ],
                comment="分类器特征统计物化视图"
            ),
            
            # 关系统计视图
            MaterializedViewDefinition(
                "relationship_summary",
                self.schema_name,
                f"""
                SELECT 
                    relationship_type,
                    COUNT(*) as relationship_count,
                    COUNT(DISTINCT source_id) as unique_sources,
                    COUNT(DISTINCT target_id) as unique_targets,
                    AVG(CASE WHEN properties ? 'weight' THEN (properties->>'weight')::numeric END) as avg_weight
                FROM {self.schema_name}.element_relationships
                GROUP BY relationship_type
                """,
                refresh_schedule="HOURLY",
                comment="关系类型统计汇总视图"
            ),
        ]
        
        for mv in materialized_views:
            await self._create_materialized_view(mv)
    
    async def _create_materialized_view(self, mv_def: MaterializedViewDefinition):
        """创建单个物化视图"""
        async with self.db_pool.acquire() as conn:
            try:
                # 创建物化视图
                create_sql = f"""
                CREATE MATERIALIZED VIEW {mv_def.schema}.{mv_def.name} AS
                {mv_def.query};
                """
                
                await conn.execute(create_sql)
                
                # 添加注释
                if mv_def.comment:
                    await conn.execute(
                        f"COMMENT ON MATERIALIZED VIEW {mv_def.schema}.{mv_def.name} IS '{mv_def.comment}'"
                    )
                
                # 创建索引
                for index_def in mv_def.indexes:
                    await self._create_index(index_def, conn)
                
                # 创建刷新函数
                await self._create_refresh_function(mv_def)
                
                logger.info(f"物化视图 {mv_def.name} 创建成功")
                
            except Exception as e:
                logger.warning(f"创建物化视图 {mv_def.name} 失败: {e}")
    
    async def _create_refresh_function(self, mv_def: MaterializedViewDefinition):
        """为物化视图创建刷新函数"""
        function_sql = f"""
        CREATE OR REPLACE FUNCTION refresh_{mv_def.name}()
        RETURNS void AS $$
        BEGIN
            REFRESH MATERIALIZED VIEW CONCURRENTLY {mv_def.schema}.{mv_def.name};
            INSERT INTO {self.schema_name}.materialized_view_refresh_log 
            (view_name, refresh_time, status)
            VALUES ('{mv_def.name}', CURRENT_TIMESTAMP, 'success');
        EXCEPTION WHEN OTHERS THEN
            INSERT INTO {self.schema_name}.materialized_view_refresh_log 
            (view_name, refresh_time, status, error_message)
            VALUES ('{mv_def.name}', CURRENT_TIMESTAMP, 'error', SQLERRM);
            RAISE;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(function_sql)
    
    async def _setup_partitioning(self):
        """设置表分区（如果数据量大）"""
        logger.info("检查分区需求...")
        
        # 检查数据量，决定是否需要分区
        async with self.db_pool.acquire() as conn:
            element_count = await conn.fetchval(
                f"SELECT COUNT(*) FROM {self.schema_name}.uml_element"
            )
            
            if element_count > 100000:  # 超过10万条记录考虑分区
                await self._create_element_partitions(conn)
    
    async def _create_element_partitions(self, conn):
        """创建元素表分区"""
        partition_sql = f"""
        -- 创建分区表（按element_type分区）
        CREATE TABLE {self.schema_name}.uml_element_partitioned (
            LIKE {self.schema_name}.uml_element INCLUDING ALL
        ) PARTITION BY LIST (element_type);
        
        -- 创建常见类型的分区
        CREATE TABLE {self.schema_name}.uml_element_class 
        PARTITION OF {self.schema_name}.uml_element_partitioned 
        FOR VALUES IN ('uml:Class', 'Class');
        
        CREATE TABLE {self.schema_name}.uml_element_interface 
        PARTITION OF {self.schema_name}.uml_element_partitioned 
        FOR VALUES IN ('uml:Interface', 'Interface');
        
        CREATE TABLE {self.schema_name}.uml_element_property 
        PARTITION OF {self.schema_name}.uml_element_partitioned 
        FOR VALUES IN ('uml:Property', 'Property');
        
        CREATE TABLE {self.schema_name}.uml_element_operation 
        PARTITION OF {self.schema_name}.uml_element_partitioned 
        FOR VALUES IN ('uml:Operation', 'Operation');
        
        -- 其他类型的默认分区
        CREATE TABLE {self.schema_name}.uml_element_other 
        PARTITION OF {self.schema_name}.uml_element_partitioned 
        DEFAULT;
        """
        
        try:
            await conn.execute(partition_sql)
            logger.info("元素表分区创建成功")
        except Exception as e:
            logger.warning(f"分区创建失败: {e}")
    
    async def _create_optimization_functions(self):
        """创建查询优化函数"""
        logger.info("创建查询优化函数...")
        
        optimization_functions = f"""
        -- 获取分类器完整信息（优化版）
        CREATE OR REPLACE FUNCTION {self.schema_name}.get_classifier_full_info(classifier_id UUID)
        RETURNS TABLE(
            id UUID, name TEXT, element_type TEXT, is_abstract BOOLEAN,
            property_count BIGINT, operation_count BIGINT,
            parent_classifiers JSONB, child_classifiers JSONB
        ) AS $$
        BEGIN
            RETURN QUERY
            WITH classifier_info AS (
                SELECT c.id, c.name, c.element_type, c.is_abstract
                FROM {self.schema_name}.uml_classifier c
                WHERE c.id = classifier_id
            ),
            feature_counts AS (
                SELECT 
                    COUNT(CASE WHEN f.element_type LIKE '%Property%' THEN 1 END) as prop_count,
                    COUNT(CASE WHEN f.element_type LIKE '%Operation%' THEN 1 END) as op_count
                FROM {self.schema_name}.uml_feature f
                WHERE f.featuring_classifier_id = classifier_id
            ),
            parents AS (
                SELECT jsonb_agg(jsonb_build_object('id', c.id, 'name', c.name)) as parent_list
                FROM {self.schema_name}.uml_generalization g
                JOIN {self.schema_name}.uml_classifier c ON c.id = g.general_id
                WHERE g.specific_id = classifier_id
            ),
            children AS (
                SELECT jsonb_agg(jsonb_build_object('id', c.id, 'name', c.name)) as child_list
                FROM {self.schema_name}.uml_generalization g
                JOIN {self.schema_name}.uml_classifier c ON c.id = g.specific_id
                WHERE g.general_id = classifier_id
            )
            SELECT 
                ci.id, ci.name, ci.element_type, ci.is_abstract,
                fc.prop_count, fc.op_count,
                COALESCE(p.parent_list, '[]'::jsonb),
                COALESCE(ch.child_list, '[]'::jsonb)
            FROM classifier_info ci
            CROSS JOIN feature_counts fc
            LEFT JOIN parents p ON true
            LEFT JOIN children ch ON true;
        END;
        $$ LANGUAGE plpgsql STABLE;
        
        -- 快速搜索分类器
        CREATE OR REPLACE FUNCTION {self.schema_name}.search_classifiers(
            search_term TEXT,
            limit_count INTEGER DEFAULT 50
        )
        RETURNS TABLE(
            id UUID, name TEXT, qualified_name TEXT, element_type TEXT,
            rank REAL
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT 
                c.id, c.name, c.qualified_name, c.element_type,
                ts_rank(
                    to_tsvector('english', coalesce(c.name, '') || ' ' || coalesce(c.qualified_name, '')),
                    plainto_tsquery('english', search_term)
                ) as rank
            FROM {self.schema_name}.uml_classifier c
            WHERE 
                to_tsvector('english', coalesce(c.name, '') || ' ' || coalesce(c.qualified_name, '')) 
                @@ plainto_tsquery('english', search_term)
                OR c.name ILIKE '%' || search_term || '%'
                OR c.qualified_name ILIKE '%' || search_term || '%'
            ORDER BY rank DESC, similarity(c.name, search_term) DESC
            LIMIT limit_count;
        END;
        $$ LANGUAGE plpgsql STABLE;
        
        -- 获取继承路径
        CREATE OR REPLACE FUNCTION {self.schema_name}.get_inheritance_path(
            classifier_id UUID,
            direction TEXT DEFAULT 'up'  -- 'up' for ancestors, 'down' for descendants
        )
        RETURNS TABLE(
            level INTEGER, classifier_id UUID, classifier_name TEXT, element_type TEXT
        ) AS $$
        BEGIN
            IF direction = 'up' THEN
                RETURN QUERY
                WITH RECURSIVE ancestors AS (
                    SELECT 0 as lvl, c.id, c.name, c.element_type
                    FROM {self.schema_name}.uml_classifier c
                    WHERE c.id = classifier_id
                    
                    UNION ALL
                    
                    SELECT a.lvl + 1, c.id, c.name, c.element_type
                    FROM ancestors a
                    JOIN {self.schema_name}.uml_generalization g ON g.specific_id = a.id
                    JOIN {self.schema_name}.uml_classifier c ON c.id = g.general_id
                    WHERE a.lvl < 10  -- 防止无限递归
                )
                SELECT lvl, id, name, element_type FROM ancestors ORDER BY lvl;
            ELSE
                RETURN QUERY
                WITH RECURSIVE descendants AS (
                    SELECT 0 as lvl, c.id, c.name, c.element_type
                    FROM {self.schema_name}.uml_classifier c
                    WHERE c.id = classifier_id
                    
                    UNION ALL
                    
                    SELECT d.lvl + 1, c.id, c.name, c.element_type
                    FROM descendants d
                    JOIN {self.schema_name}.uml_generalization g ON g.general_id = d.id
                    JOIN {self.schema_name}.uml_classifier c ON c.id = g.specific_id
                    WHERE d.lvl < 10  -- 防止无限递归
                )
                SELECT lvl, id, name, element_type FROM descendants ORDER BY lvl;
            END IF;
        END;
        $$ LANGUAGE plpgsql STABLE;
        """
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(optimization_functions)
    
    async def _create_indexes_batch(self, indexes: List[IndexDefinition]):
        """批量创建索引"""
        for index_def in indexes:
            await self._create_index(index_def)
    
    async def _create_index(self, index_def: IndexDefinition, conn=None):
        """创建单个索引"""
        if conn is None:
            async with self.db_pool.acquire() as conn:
                await self._create_index_with_conn(index_def, conn)
        else:
            await self._create_index_with_conn(index_def, conn)
    
    async def _create_index_with_conn(self, index_def: IndexDefinition, conn):
        """使用指定连接创建索引"""
        try:
            # 构建索引创建SQL
            columns_str = ", ".join(index_def.columns)
            
            sql_parts = ["CREATE"]
            if index_def.is_unique:
                sql_parts.append("UNIQUE")
            sql_parts.append("INDEX")
            if index_def.concurrently:
                sql_parts.append("CONCURRENTLY")
            
            sql_parts.extend([
                index_def.name,
                "ON",
                index_def.table,
                f"USING {index_def.index_type}",
                f"({columns_str})"
            ])
            
            if index_def.condition:
                sql_parts.extend(["WHERE", index_def.condition])
            
            create_sql = " ".join(sql_parts)
            
            await conn.execute(create_sql)
            
            # 添加注释
            if index_def.comment:
                await conn.execute(
                    f"COMMENT ON INDEX {index_def.name} IS '{index_def.comment}'"
                )
            
            logger.debug(f"索引 {index_def.name} 创建成功")
            
        except Exception as e:
            if "already exists" not in str(e):
                logger.warning(f"创建索引 {index_def.name} 失败: {e}")

class PerformanceBenchmark:
    """性能基准测试"""
    
    def __init__(self, db_pool: asyncpg.Pool, schema_name: str = "uml25_base"):
        self.db_pool = db_pool
        self.schema_name = schema_name
        self.results: Dict[str, List[float]] = {}
    
    async def run_full_benchmark(self) -> Dict[str, Any]:
        """运行完整的性能基准测试"""
        logger.info("开始性能基准测试...")
        
        # 准备测试数据
        await self._prepare_test_data()
        
        # 单表查询基准
        await self._benchmark_single_table_queries()
        
        # 继承查询基准
        await self._benchmark_inheritance_queries()
        
        # 关系查询基准
        await self._benchmark_relationship_queries()
        
        # 并发查询基准
        await self._benchmark_concurrent_queries()
        
        # 复杂查询基准
        await self._benchmark_complex_queries()
        
        return self._generate_benchmark_report()
    
    async def _prepare_test_data(self):
        """准备测试数据"""
        async with self.db_pool.acquire() as conn:
            # 检查是否有足够的测试数据
            element_count = await conn.fetchval(
                f"SELECT COUNT(*) FROM {self.schema_name}.uml_element"
            )
            
            if element_count < 1000:
                logger.info("生成测试数据...")
                await self._generate_test_data(conn)
    
    async def _generate_test_data(self, conn):
        """生成测试数据"""
        # 生成测试元素
        for i in range(1000):
            element_id = uuid.uuid4()
            await conn.execute(f"""
                INSERT INTO {self.schema_name}.uml_element 
                (id, element_type, documentation, properties)
                VALUES ($1, $2, $3, $4)
            """, element_id, 'TestElement', f'Test documentation {i}', 
                json.dumps({'test_property': f'value_{i}'}))
    
    async def _benchmark_single_table_queries(self):
        """单表查询基准测试"""
        queries = [
            ("simple_select", f"SELECT * FROM {self.schema_name}.uml_element LIMIT 100"),
            ("filtered_select", f"SELECT * FROM {self.schema_name}.uml_element WHERE element_type = 'TestElement' LIMIT 100"),
            ("json_query", f"SELECT * FROM {self.schema_name}.uml_element WHERE properties ? 'test_property' LIMIT 100"),
        ]
        
        for query_name, sql in queries:
            await self._benchmark_query(query_name, sql)
    
    async def _benchmark_inheritance_queries(self):
        """继承查询基准测试"""
        queries = [
            ("inheritance_view", f"SELECT * FROM {self.schema_name}.uml_class LIMIT 100"),
            ("classifier_hierarchy", f"SELECT * FROM {self.schema_name}.classifier_hierarchy LIMIT 100"),
        ]
        
        for query_name, sql in queries:
            await self._benchmark_query(query_name, sql)
    
    async def _benchmark_relationship_queries(self):
        """关系查询基准测试"""
        queries = [
            ("generalization_join", f"""
                SELECT c1.name, c2.name 
                FROM {self.schema_name}.uml_generalization g
                JOIN {self.schema_name}.uml_classifier c1 ON g.specific_id = c1.id
                JOIN {self.schema_name}.uml_classifier c2 ON g.general_id = c2.id
                LIMIT 100
            """),
            ("element_relationships", f"SELECT * FROM {self.schema_name}.element_relationships LIMIT 100"),
        ]
        
        for query_name, sql in queries:
            await self._benchmark_query(query_name, sql)
    
    async def _benchmark_concurrent_queries(self):
        """并发查询基准测试"""
        
        async def run_concurrent_query():
            async with self.db_pool.acquire() as conn:
                await conn.fetch(f"SELECT * FROM {self.schema_name}.uml_element LIMIT 10")
        
        # 测试不同并发级别
        for concurrency in [1, 5, 10, 20]:
            start_time = time.time()
            
            tasks = [run_concurrent_query() for _ in range(concurrency)]
            await asyncio.gather(*tasks)
            
            duration = (time.time() - start_time) * 1000  # 转换为毫秒
            
            query_name = f"concurrent_{concurrency}"
            if query_name not in self.results:
                self.results[query_name] = []
            self.results[query_name].append(duration)
    
    async def _benchmark_complex_queries(self):
        """复杂查询基准测试"""
        complex_queries = [
            ("complex_aggregation", f"""
                SELECT 
                    c.element_type,
                    COUNT(*) as count,
                    COUNT(CASE WHEN c.is_abstract THEN 1 END) as abstract_count
                FROM {self.schema_name}.uml_classifier c
                GROUP BY c.element_type
            """),
            ("recursive_hierarchy", f"""
                WITH RECURSIVE hierarchy AS (
                    SELECT id, name, 0 as level
                    FROM {self.schema_name}.uml_classifier
                    WHERE id NOT IN (SELECT specific_id FROM {self.schema_name}.uml_generalization)
                    LIMIT 10
                    
                    UNION ALL
                    
                    SELECT c.id, c.name, h.level + 1
                    FROM {self.schema_name}.uml_classifier c
                    JOIN {self.schema_name}.uml_generalization g ON g.specific_id = c.id
                    JOIN hierarchy h ON h.id = g.general_id
                    WHERE h.level < 5
                )
                SELECT * FROM hierarchy
            """),
        ]
        
        for query_name, sql in complex_queries:
            await self._benchmark_query(query_name, sql)
    
    async def _benchmark_query(self, query_name: str, sql: str, iterations: int = 10):
        """基准测试单个查询"""
        if query_name not in self.results:
            self.results[query_name] = []
        
        for _ in range(iterations):
            start_time = time.time()
            
            async with self.db_pool.acquire() as conn:
                try:
                    await conn.fetch(sql)
                except Exception as e:
                    logger.warning(f"查询 {query_name} 执行失败: {e}")
                    continue
            
            duration = (time.time() - start_time) * 1000  # 转换为毫秒
            self.results[query_name].append(duration)
    
    def _generate_benchmark_report(self) -> Dict[str, Any]:
        """生成基准测试报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {},
            'details': {}
        }
        
        for query_name, durations in self.results.items():
            if durations:
                stats = {
                    'count': len(durations),
                    'min_ms': min(durations),
                    'max_ms': max(durations),
                    'avg_ms': statistics.mean(durations),
                    'median_ms': statistics.median(durations),
                    'p95_ms': sorted(durations)[int(len(durations) * 0.95)] if len(durations) >= 20 else max(durations),
                    'std_dev': statistics.stdev(durations) if len(durations) > 1 else 0
                }
                
                report['details'][query_name] = stats
                report['summary'][query_name] = {
                    'avg_ms': stats['avg_ms'],
                    'p95_ms': stats['p95_ms']
                }
        
        return report

# 便捷函数
async def optimize_uml25_performance(
    db_pool: asyncpg.Pool, 
    schema_name: str = "uml25_base"
) -> Dict[str, Any]:
    """优化UML25数据库性能"""
    optimizer = UML25PerformanceOptimizer(db_pool, schema_name)
    return await optimizer.apply_all_optimizations()

async def benchmark_uml25_performance(
    db_pool: asyncpg.Pool, 
    schema_name: str = "uml25_base"
) -> Dict[str, Any]:
    """基准测试UML25数据库性能"""
    benchmark = PerformanceBenchmark(db_pool, schema_name)
    return await benchmark.run_full_benchmark() 