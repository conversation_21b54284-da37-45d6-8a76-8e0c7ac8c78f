"""
基于PostgreSQL高级功能的增强型UML元类管理器

借鉴PostgreSQL核心功能：
1. 表继承（Table Inheritance）- 元类继承层次
2. 系统目录（Information Schema）- 元模型自省
3. 存储过程和函数 - 智能元类操作
4. 物化视图 - 元类关系缓存
5. 分区表 - 大规模模型管理
6. 全文搜索 - 语义查找
7. 自定义类型 - 领域特定数据类型
8. 动态SQL - 运行时Schema生成
9. 事件通知 - 元模型变更监听
10. JSON路径查询 - 复杂元数据检索
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from datetime import datetime, timezone
from dataclasses import dataclass, field
from enum import Enum
import asyncpg

logger = logging.getLogger(__name__)

class PostgreSQLFeature(Enum):
    """PostgreSQL功能枚举"""
    TABLE_INHERITANCE = "table_inheritance"
    MATERIALIZED_VIEW = "materialized_view"
    PARTITIONING = "partitioning"
    FULL_TEXT_SEARCH = "full_text_search"
    CUSTOM_TYPES = "custom_types"
    STORED_PROCEDURES = "stored_procedures"
    TRIGGERS = "triggers"
    JSONB_OPERATORS = "jsonb_operators"
    ARRAY_TYPES = "array_types"
    RANGE_TYPES = "range_types"

@dataclass
class MetaclassInheritanceInfo:
    """元类继承信息"""
    metaclass_id: str
    name: str
    parent_metaclass: Optional[str] = None
    children_metaclasses: List[str] = field(default_factory=list)
    inheritance_depth: int = 0
    is_abstract: bool = False
    concrete_implementations: List[str] = field(default_factory=list)

class PostgreSQLEnhancedMetaclassManager:
    """基于PostgreSQL高级功能的增强型元类管理器"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # PostgreSQL系统目录查询
        self.system_queries = {
            'tables': """
                SELECT schemaname, tablename, tableowner, hasindexes, hasrules, hastriggers
                FROM pg_tables 
                WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            """,
            'inheritance': """
                SELECT 
                    c.relname as child_table,
                    p.relname as parent_table,
                    n.nspname as schema_name
                FROM pg_inherits i
                JOIN pg_class c ON i.inhrelid = c.oid
                JOIN pg_class p ON i.inhparent = p.oid
                JOIN pg_namespace n ON c.relnamespace = n.oid
            """,
            'columns': """
                SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
                FROM information_schema.columns 
                WHERE table_schema = $1 AND table_name = $2
            """
        }
    
    async def initialize(self):
        """初始化增强型元类管理器"""
        await self._create_custom_types()
        await self._create_inheritance_tables()
        await self._create_materialized_views()
        await self._create_partitioned_tables()
        await self._create_stored_procedures()
        await self._setup_full_text_search()
        await self._create_event_triggers()
        
        self.logger.info("PostgreSQL增强型元类管理器初始化完成")
    
    # ========== 1. 表继承（Table Inheritance） ==========
    
    async def _create_inheritance_tables(self):
        """创建基于PostgreSQL表继承的元类层次结构"""
        async with self.db_pool.acquire() as conn:
            # 创建基础元类表（父表）
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.base_metaclass (
                    metaclass_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    name VARCHAR(255) NOT NULL,
                    qualified_name VARCHAR(500) UNIQUE NOT NULL,
                    namespace_uri VARCHAR(500),
                    is_abstract BOOLEAN DEFAULT FALSE,
                    documentation TEXT,
                    properties JSONB DEFAULT '{}',
                    constraints_def JSONB DEFAULT '[]',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    -- 全文搜索向量
                    search_vector tsvector GENERATED ALWAYS AS (
                        to_tsvector('english', coalesce(name, '') || ' ' || coalesce(documentation, ''))
                    ) STORED
                )
            """)
            
            # 创建具体元类表（子表，继承自base_metaclass）
            metaclass_types = ['class', 'association', 'activity', 'component', 'interface', 'package']
            
            for mc_type in metaclass_types:
                await conn.execute(f"""
                    CREATE TABLE IF NOT EXISTS core_schema.{mc_type}_metaclass (
                        -- 特化字段
                        {mc_type}_specific_data JSONB DEFAULT '{{}}',
                        visibility VARCHAR(50) DEFAULT 'public',
                        
                        -- 类型特定索引字段
                        type_category VARCHAR(100) GENERATED ALWAYS AS (
                            properties->>'category'
                        ) STORED
                        
                    ) INHERITS (core_schema.base_metaclass)
                """)
                
                # 为每个子表创建专门的索引
                await conn.execute(f"""
                    CREATE INDEX IF NOT EXISTS idx_{mc_type}_metaclass_name 
                    ON core_schema.{mc_type}_metaclass(name);
                    
                    CREATE INDEX IF NOT EXISTS idx_{mc_type}_metaclass_search 
                    ON core_schema.{mc_type}_metaclass USING GIN(search_vector);
                    
                    CREATE INDEX IF NOT EXISTS idx_{mc_type}_metaclass_properties 
                    ON core_schema.{mc_type}_metaclass USING GIN(properties);
                """)
    
    async def create_inherited_metaclass(self, parent_type: str, child_name: str, 
                                       additional_fields: Dict[str, Any] = None) -> bool:
        """动态创建继承的元类表"""
        async with self.db_pool.acquire() as conn:
            try:
                additional_fields = additional_fields or {}
                field_definitions = []
                
                for field_name, field_config in additional_fields.items():
                    field_type = field_config.get('type', 'TEXT')
                    nullable = '' if field_config.get('required', False) else 'NULL'
                    default = f"DEFAULT '{field_config.get('default', '')}'" if field_config.get('default') else ''
                    
                    field_definitions.append(f"{field_name} {field_type} {nullable} {default}")
                
                additional_fields_sql = ',\n        '.join(field_definitions) if field_definitions else ''
                comma = ',' if additional_fields_sql else ''
                
                sql = f"""
                    CREATE TABLE IF NOT EXISTS core_schema.{child_name}_metaclass (
                        {additional_fields_sql}{comma}
                        specialized_config JSONB DEFAULT '{{}}'
                    ) INHERITS (core_schema.{parent_type}_metaclass)
                """
                
                await conn.execute(sql)
                self.logger.info(f"继承元类表创建成功: {child_name}_metaclass")
                return True
                
            except Exception as e:
                self.logger.error(f"创建继承元类表失败: {e}")
                return False
    
    # ========== 2. 物化视图（Materialized Views） ==========
    
    async def _create_materialized_views(self):
        """创建元类关系的物化视图"""
        async with self.db_pool.acquire() as conn:
            # 元类继承层次视图
            await conn.execute("""
                CREATE MATERIALIZED VIEW IF NOT EXISTS core_schema.metaclass_hierarchy_view AS
                WITH RECURSIVE inheritance_tree AS (
                    -- 基础查询：根元类
                    SELECT 
                        metaclass_id,
                        name,
                        qualified_name,
                        NULL::UUID as parent_id,
                        0 as depth,
                        ARRAY[name] as path
                    FROM core_schema.base_metaclass 
                    WHERE metaclass_id NOT IN (
                        SELECT child_id FROM core_schema.metaclass_inheritance 
                        WHERE child_id IS NOT NULL
                    )
                    
                    UNION ALL
                    
                    -- 递归查询：子元类
                    SELECT 
                        c.metaclass_id,
                        c.name,
                        c.qualified_name,
                        i.parent_id,
                        it.depth + 1,
                        it.path || c.name
                    FROM core_schema.base_metaclass c
                    JOIN core_schema.metaclass_inheritance i ON c.metaclass_id = i.child_id
                    JOIN inheritance_tree it ON i.parent_id = it.metaclass_id
                    WHERE it.depth < 10  -- 防止无限递归
                )
                SELECT 
                    metaclass_id,
                    name,
                    qualified_name,
                    parent_id,
                    depth,
                    path,
                    array_to_string(path, '::') as inheritance_path
                FROM inheritance_tree
                ORDER BY depth, name;
            """)
            
            # 元类使用统计视图
            await conn.execute("""
                CREATE MATERIALIZED VIEW IF NOT EXISTS core_schema.metaclass_usage_stats AS
                SELECT 
                    m.name as metaclass_name,
                    m.qualified_name,
                    COUNT(e.element_id) as instance_count,
                    COUNT(DISTINCT e.domain_name) as domain_count,
                    MIN(e.created_at) as first_usage,
                    MAX(e.updated_at) as last_usage,
                    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY e.created_at) as median_creation_time
                FROM core_schema.base_metaclass m
                LEFT JOIN core_schema.element_metadata e ON m.name = e.element_type
                GROUP BY m.metaclass_id, m.name, m.qualified_name
                ORDER BY instance_count DESC;
            """)
            
            # 创建自动刷新物化视图的函数
            await conn.execute("""
                CREATE OR REPLACE FUNCTION core_schema.refresh_metaclass_views()
                RETURNS void AS $$
                BEGIN
                    REFRESH MATERIALIZED VIEW core_schema.metaclass_hierarchy_view;
                    REFRESH MATERIALIZED VIEW core_schema.metaclass_usage_stats;
                    
                    -- 记录刷新时间
                    INSERT INTO core_schema.view_refresh_log (view_name, refreshed_at)
                    VALUES ('metaclass_views', CURRENT_TIMESTAMP);
                END;
                $$ LANGUAGE plpgsql;
            """)
    
    # ========== 3. 分区表（Table Partitioning） ==========
    
    async def _create_partitioned_tables(self):
        """创建分区表用于大规模模型实例管理"""
        async with self.db_pool.acquire() as conn:
            # 按时间分区的模型实例表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.model_instances_partitioned (
                    instance_id UUID DEFAULT uuid_generate_v4(),
                    model_name VARCHAR(255) NOT NULL,
                    metaclass_type VARCHAR(100) NOT NULL,
                    instance_data JSONB NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    domain_name VARCHAR(255),
                    file_source VARCHAR(500),
                    
                    PRIMARY KEY (instance_id, created_at)
                ) PARTITION BY RANGE (created_at);
            """)
            
            # 创建月度分区
            current_year = datetime.now().year
            for month in range(1, 13):
                start_date = f"{current_year}-{month:02d}-01"
                end_date = f"{current_year}-{month+1 if month < 12 else current_year+1}-01-01" if month < 12 else f"{current_year+1}-01-01"
                
                await conn.execute(f"""
                    CREATE TABLE IF NOT EXISTS core_schema.model_instances_{current_year}_{month:02d}
                    PARTITION OF core_schema.model_instances_partitioned
                    FOR VALUES FROM ('{start_date}') TO ('{end_date}');
                """)
                
                # 为每个分区创建索引
                await conn.execute(f"""
                    CREATE INDEX IF NOT EXISTS idx_instances_{current_year}_{month:02d}_model
                    ON core_schema.model_instances_{current_year}_{month:02d}(model_name, metaclass_type);
                """)
    
    # ========== 4. 自定义数据类型 ==========
    
    async def _create_custom_types(self):
        """创建领域特定的自定义数据类型"""
        async with self.db_pool.acquire() as conn:
            # 元类状态枚举
            await conn.execute("""
                DO $$ BEGIN
                    CREATE TYPE core_schema.metaclass_status AS ENUM (
                        'draft', 'active', 'deprecated', 'obsolete'
                    );
                EXCEPTION
                    WHEN duplicate_object THEN null;
                END $$;
            """)
            
            # UML可见性枚举
            await conn.execute("""
                DO $$ BEGIN
                    CREATE TYPE core_schema.uml_visibility AS ENUM (
                        'public', 'private', 'protected', 'package'
                    );
                EXCEPTION
                    WHEN duplicate_object THEN null;
                END $$;
            """)
            
            # 复合类型：元类版本信息
            await conn.execute("""
                DO $$ BEGIN
                    CREATE TYPE core_schema.metaclass_version AS (
                        major_version INTEGER,
                        minor_version INTEGER,
                        patch_version INTEGER,
                        version_string VARCHAR(50),
                        release_date TIMESTAMP WITH TIME ZONE
                    );
                EXCEPTION
                    WHEN duplicate_object THEN null;
                END $$;
            """)
            
            # 数组类型：标签数组
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS core_schema.metaclass_tags (
                    metaclass_id UUID REFERENCES core_schema.base_metaclass(metaclass_id),
                    tags TEXT[] DEFAULT '{}',
                    semantic_tags JSONB DEFAULT '{}',
                    computed_categories TEXT[] GENERATED ALWAYS AS (
                        ARRAY(SELECT jsonb_array_elements_text(semantic_tags->'categories'))
                    ) STORED
                );
            """)
    
    # ========== 5. 存储过程和函数 ==========
    
    async def _create_stored_procedures(self):
        """创建智能元类操作存储过程"""
        async with self.db_pool.acquire() as conn:
            # 智能元类继承分析函数
            await conn.execute("""
                CREATE OR REPLACE FUNCTION core_schema.analyze_metaclass_inheritance(
                    p_metaclass_name VARCHAR(255)
                )
                RETURNS TABLE(
                    level INTEGER,
                    metaclass_name VARCHAR(255),
                    relation_type VARCHAR(20),
                    distance INTEGER
                ) AS $$
                WITH RECURSIVE inheritance_analysis AS (
                    -- 向上查找父类
                    SELECT 
                        0 as level,
                        m.name as metaclass_name,
                        'self' as relation_type,
                        0 as distance
                    FROM core_schema.base_metaclass m
                    WHERE m.name = p_metaclass_name
                    
                    UNION ALL
                    
                    SELECT 
                        ia.level - 1,
                        pm.name,
                        'parent' as relation_type,
                        abs(ia.level - 1) as distance
                    FROM inheritance_analysis ia
                    JOIN core_schema.metaclass_inheritance mi ON ia.metaclass_name = (
                        SELECT name FROM core_schema.base_metaclass WHERE metaclass_id = mi.child_id
                    )
                    JOIN core_schema.base_metaclass pm ON mi.parent_id = pm.metaclass_id
                    WHERE ia.level > -5  -- 限制向上查找深度
                    
                    UNION ALL
                    
                    -- 向下查找子类
                    SELECT 
                        ia.level + 1,
                        cm.name,
                        'child' as relation_type,
                        ia.level + 1 as distance
                    FROM inheritance_analysis ia
                    JOIN core_schema.metaclass_inheritance mi ON (
                        SELECT metaclass_id FROM core_schema.base_metaclass WHERE name = ia.metaclass_name
                    ) = mi.parent_id
                    JOIN core_schema.base_metaclass cm ON mi.child_id = cm.metaclass_id
                    WHERE ia.level < 5  -- 限制向下查找深度
                )
                SELECT * FROM inheritance_analysis
                ORDER BY level, metaclass_name;
                $$ LANGUAGE SQL;
            """)
            
            # 动态Schema生成函数
            await conn.execute("""
                CREATE OR REPLACE FUNCTION core_schema.generate_metaclass_table(
                    p_metaclass_name VARCHAR(255),
                    p_schema_name VARCHAR(255),
                    p_field_definitions JSONB
                )
                RETURNS BOOLEAN AS $$
                DECLARE
                    sql_statement TEXT;
                    field_def JSONB;
                    field_name TEXT;
                    field_type TEXT;
                    field_constraint TEXT;
                BEGIN
                    -- 构建CREATE TABLE语句
                    sql_statement := format('CREATE TABLE IF NOT EXISTS %I.%I (', 
                                          p_schema_name, p_metaclass_name);
                    
                    -- 添加基础字段
                    sql_statement := sql_statement || 
                        'element_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), ';
                    
                    -- 动态添加字段定义
                    FOR field_name, field_def IN SELECT * FROM jsonb_each(p_field_definitions)
                    LOOP
                        field_type := field_def->>'type';
                        field_constraint := '';
                        
                        IF field_def->>'required' = 'true' THEN
                            field_constraint := field_constraint || ' NOT NULL';
                        END IF;
                        
                        IF field_def->>'unique' = 'true' THEN
                            field_constraint := field_constraint || ' UNIQUE';
                        END IF;
                        
                        sql_statement := sql_statement || format('%I %s%s, ', 
                                                               field_name, field_type, field_constraint);
                    END LOOP;
                    
                    -- 添加时间戳字段
                    sql_statement := sql_statement || 
                        'created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, ' ||
                        'updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP)';
                    
                    -- 执行SQL
                    EXECUTE sql_statement;
                    
                    -- 记录操作日志
                    INSERT INTO core_schema.schema_generation_log (
                        operation_type, target_table, sql_executed, success
                    ) VALUES (
                        'CREATE_TABLE', 
                        format('%s.%s', p_schema_name, p_metaclass_name),
                        sql_statement,
                        TRUE
                    );
                    
                    RETURN TRUE;
                EXCEPTION
                    WHEN OTHERS THEN
                        -- 记录错误日志
                        INSERT INTO core_schema.schema_generation_log (
                            operation_type, target_table, sql_executed, success, error_message
                        ) VALUES (
                            'CREATE_TABLE',
                            format('%s.%s', p_schema_name, p_metaclass_name),
                            sql_statement,
                            FALSE,
                            SQLERRM
                        );
                        RETURN FALSE;
                END;
                $$ LANGUAGE plpgsql;
            """)
    
    # ========== 6. 全文搜索 ==========
    
    async def _setup_full_text_search(self):
        """设置全文搜索功能"""
        async with self.db_pool.acquire() as conn:
            # 创建自定义搜索配置
            await conn.execute("""
                CREATE TEXT SEARCH CONFIGURATION IF NOT EXISTS core_schema.uml_search (
                    COPY = english
                );
            """)
            
            # 创建全文搜索函数
            await conn.execute("""
                CREATE OR REPLACE FUNCTION core_schema.search_metaclasses(
                    p_search_text TEXT,
                    p_limit INTEGER DEFAULT 50
                )
                RETURNS TABLE(
                    metaclass_id UUID,
                    name VARCHAR(255),
                    qualified_name VARCHAR(500),
                    search_rank REAL,
                    snippet TEXT
                ) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        m.metaclass_id,
                        m.name,
                        m.qualified_name,
                        ts_rank(m.search_vector, plainto_tsquery('english', p_search_text)) as search_rank,
                        ts_headline('english', 
                                   coalesce(m.documentation, m.name), 
                                   plainto_tsquery('english', p_search_text)) as snippet
                    FROM core_schema.base_metaclass m
                    WHERE m.search_vector @@ plainto_tsquery('english', p_search_text)
                    ORDER BY search_rank DESC
                    LIMIT p_limit;
                END;
                $$ LANGUAGE plpgsql;
            """)
    
    # ========== 7. 事件触发器 ==========
    
    async def _create_event_triggers(self):
        """创建元模型变更事件触发器"""
        async with self.db_pool.acquire() as conn:
            # 元类变更通知函数
            await conn.execute("""
                CREATE OR REPLACE FUNCTION core_schema.notify_metaclass_change()
                RETURNS trigger AS $$
                DECLARE
                    notification_payload JSONB;
                BEGIN
                    -- 构建通知载荷
                    notification_payload := jsonb_build_object(
                        'operation', TG_OP,
                        'table', TG_TABLE_NAME,
                        'timestamp', CURRENT_TIMESTAMP,
                        'metaclass_id', COALESCE(NEW.metaclass_id, OLD.metaclass_id),
                        'metaclass_name', COALESCE(NEW.name, OLD.name)
                    );
                    
                    -- 发送异步通知
                    PERFORM pg_notify('metaclass_changes', notification_payload::TEXT);
                    
                    -- 记录变更日志
                    INSERT INTO core_schema.metaclass_change_log (
                        operation_type,
                        metaclass_id,
                        old_data,
                        new_data,
                        changed_at
                    ) VALUES (
                        TG_OP,
                        COALESCE(NEW.metaclass_id, OLD.metaclass_id),
                        to_jsonb(OLD),
                        to_jsonb(NEW),
                        CURRENT_TIMESTAMP
                    );
                    
                    RETURN COALESCE(NEW, OLD);
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            # 创建触发器
            await conn.execute("""
                CREATE TRIGGER trigger_metaclass_change_notification
                    AFTER INSERT OR UPDATE OR DELETE 
                    ON core_schema.base_metaclass
                    FOR EACH ROW
                    EXECUTE FUNCTION core_schema.notify_metaclass_change();
            """)
    
    # ========== 公共API方法 ==========
    
    async def get_metaclass_inheritance_tree(self, metaclass_name: str) -> Dict[str, Any]:
        """获取元类继承树"""
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT * FROM core_schema.analyze_metaclass_inheritance($1)
            """, metaclass_name)
            
            tree = {
                'root': metaclass_name,
                'parents': [],
                'children': [],
                'siblings': []
            }
            
            for row in rows:
                if row['relation_type'] == 'parent':
                    tree['parents'].append({
                        'name': row['metaclass_name'],
                        'distance': row['distance']
                    })
                elif row['relation_type'] == 'child':
                    tree['children'].append({
                        'name': row['metaclass_name'],
                        'distance': row['distance']
                    })
            
            return tree
    
    async def search_metaclasses(self, search_text: str, limit: int = 50) -> List[Dict[str, Any]]:
        """全文搜索元类"""
        async with self.db_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT * FROM core_schema.search_metaclasses($1, $2)
            """, search_text, limit)
            
            return [dict(row) for row in rows]
    
    async def create_dynamic_metaclass_table(self, metaclass_name: str, 
                                           schema_name: str, 
                                           field_definitions: Dict[str, Any]) -> bool:
        """动态创建元类表"""
        async with self.db_pool.acquire() as conn:
            result = await conn.fetchval("""
                SELECT core_schema.generate_metaclass_table($1, $2, $3)
            """, metaclass_name, schema_name, json.dumps(field_definitions))
            
            return result
    
    async def get_system_metadata(self) -> Dict[str, Any]:
        """获取系统元数据（借鉴PostgreSQL系统目录）"""
        async with self.db_pool.acquire() as conn:
            # 获取所有用户Schema
            schemas = await conn.fetch(self.system_queries['tables'])
            
            # 获取继承关系
            inheritance = await conn.fetch(self.system_queries['inheritance'])
            
            # 获取数据库统计信息
            stats = await conn.fetchrow("""
                SELECT 
                    COUNT(DISTINCT schemaname) as schema_count,
                    COUNT(*) as table_count,
                    SUM(CASE WHEN hasindexes THEN 1 ELSE 0 END) as indexed_tables,
                    SUM(CASE WHEN hastriggers THEN 1 ELSE 0 END) as tables_with_triggers
                FROM pg_tables 
                WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
            """)
            
            return {
                'schemas': [dict(row) for row in schemas],
                'inheritance_relationships': [dict(row) for row in inheritance],
                'statistics': dict(stats)
            }
    
    async def refresh_materialized_views(self):
        """刷新物化视图"""
        async with self.db_pool.acquire() as conn:
            await conn.execute("SELECT core_schema.refresh_metaclass_views()")
    
    async def listen_for_metaclass_changes(self, callback_func):
        """监听元类变更事件"""
        async with self.db_pool.acquire() as conn:
            await conn.add_listener('metaclass_changes', callback_func)

# 便捷函数
async def create_postgresql_enhanced_manager(db_pool: asyncpg.Pool) -> PostgreSQLEnhancedMetaclassManager:
    """创建PostgreSQL增强型元类管理器"""
    manager = PostgreSQLEnhancedMetaclassManager(db_pool)
    await manager.initialize()
    return manager

# 演示用法
async def demo_postgresql_features():
    """演示PostgreSQL高级功能"""
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'biomedical_mbse_platform',
        'user': 'mbse_user',
        'password': 'mbse_pass_2024'
    }
    
    database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    db_pool = await asyncpg.create_pool(database_url)
    
    try:
        manager = await create_postgresql_enhanced_manager(db_pool)
        
        print("🚀 PostgreSQL增强功能演示")
        print("=" * 50)
        
        # 1. 表继承功能
        print("\n1️⃣ 表继承功能:")
        success = await manager.create_inherited_metaclass(
            'class', 'biomedicine_entity',
            {'patient_id': {'type': 'VARCHAR(50)', 'required': True}}
        )
        print(f"   创建继承表: {'成功' if success else '失败'}")
        
        # 2. 全文搜索
        print("\n2️⃣ 全文搜索功能:")
        search_results = await manager.search_metaclasses("patient treatment")
        print(f"   搜索结果: {len(search_results)}个")
        
        # 3. 动态表生成
        print("\n3️⃣ 动态表生成:")
        fields = {
            'gene_symbol': {'type': 'VARCHAR(100)', 'required': True},
            'chromosome': {'type': 'VARCHAR(10)'},
            'expression_level': {'type': 'DECIMAL(10,4)'}
        }
        success = await manager.create_dynamic_metaclass_table(
            'gene_expression', 'biomedicine_schema', fields
        )
        print(f"   动态创建表: {'成功' if success else '失败'}")
        
        # 4. 系统元数据
        print("\n4️⃣ 系统元数据:")
        metadata = await manager.get_system_metadata()
        print(f"   Schema数量: {metadata['statistics']['schema_count']}")
        print(f"   表数量: {metadata['statistics']['table_count']}")
        
        # 5. 物化视图刷新
        print("\n5️⃣ 物化视图刷新:")
        await manager.refresh_materialized_views()
        print("   物化视图已刷新")
        
        print("\n✅ PostgreSQL增强功能演示完成！")
        
    finally:
        await db_pool.close()

if __name__ == "__main__":
    asyncio.run(demo_postgresql_features()) 