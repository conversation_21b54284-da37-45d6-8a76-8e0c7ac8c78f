#!/usr/bin/env python3
"""
核心UML元类定义生成器运行脚本

简化的运行脚本，用于快速生成核心UML元类定义表并验证结果。

使用方法:
    python run_core_uml_generator.py [--test] [--database] [--output-dir OUTPUT_DIR]

作者：生物医学MBSE平台开发组
版本：1.0.0
创建时间：2025年1月
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core_uml_metaclass_generator import CoreUMLMetaclassGenerator
from improved_inheritance_mapping import print_inheritance_analysis

def print_banner():
    """打印横幅"""
    print("🎯 核心UML元类定义生成器")
    print("=" * 60)
    print("基于UML 2.5标准生成完整的核心元类定义表")
    print("生物医学MBSE平台 - 数据库映射系统")
    print("=" * 60)

def print_summary(definitions, output_dir):
    """打印生成摘要"""
    total_count = len(definitions)
    abstract_count = sum(1 for d in definitions if d.is_abstract)
    concrete_count = total_count - abstract_count
    
    # 统计策略分布
    strategy_stats = {}
    for definition in definitions:
        strategy = definition.table_strategy
        strategy_stats[strategy] = strategy_stats.get(strategy, 0) + 1
    
    # 统计层级分布
    level_stats = {}
    for definition in definitions:
        level = definition.inheritance_level_name
        level_stats[level] = level_stats.get(level, 0) + 1
    
    print(f"\n📊 生成摘要")
    print("-" * 40)
    print(f"元类总数: {total_count}")
    print(f"抽象类: {abstract_count} ({abstract_count/total_count*100:.1f}%)")
    print(f"具体类: {concrete_count} ({concrete_count/total_count*100:.1f}%)")
    
    print(f"\n继承层级分布:")
    for level, count in sorted(level_stats.items(), key=lambda x: ['ROOT', 'CORE', 'ABSTRACT', 'SPECIALIZED', 'CONCRETE'].index(x[0])):
        percentage = count / total_count * 100
        print(f"  {level}: {count} ({percentage:.1f}%)")
    
    print(f"\n表策略分布:")
    for strategy, count in sorted(strategy_stats.items()):
        percentage = count / total_count * 100
        print(f"  {strategy}: {count} ({percentage:.1f}%)")
    
    print(f"\n输出文件位置:")
    print(f"  📁 输出目录: {output_dir}")
    print(f"  📄 JSON文件: {output_dir}/core_uml_metaclass_definitions.json")
    print(f"  📊 CSV文件: {output_dir}/core_uml_metaclass_definitions.csv")
    print(f"  🗃️ SQL文件: {output_dir}/core_uml_metaclass_definitions.sql")
    print(f"  📋 报告文件: {output_dir}/core_uml_metaclass_summary.md")

async def run_generator(output_dir="./output", run_tests=False, include_database=False):
    """运行生成器"""
    print_banner()
    
    # 创建生成器
    print("🔧 初始化生成器...")
    generator = CoreUMLMetaclassGenerator(output_dir=output_dir)
    
    # 显示继承关系分析
    print("\n🌳 UML继承关系分析:")
    print_inheritance_analysis()
    
    # 生成元类定义
    print(f"\n📋 生成核心UML元类定义...")
    start_time = datetime.now()
    definitions = generator.generate_core_metaclass_definitions()
    generation_time = (datetime.now() - start_time).total_seconds()
    
    print(f"✅ 生成完成! 用时 {generation_time:.3f}秒")
    print(f"   生成了 {len(definitions)} 个核心UML元类定义")
    
    # 导出各种格式
    print(f"\n💾 导出为多种格式...")
    
    success_count = 0
    total_exports = 4
    
    # JSON导出
    if generator.export_to_json(definitions):
        print("   ✅ JSON格式导出成功")
        success_count += 1
    else:
        print("   ❌ JSON格式导出失败")
    
    # CSV导出
    if generator.export_to_csv(definitions):
        print("   ✅ CSV格式导出成功")
        success_count += 1
    else:
        print("   ❌ CSV格式导出失败")
    
    # SQL导出
    if generator.export_sql_insert_statements(definitions):
        print("   ✅ SQL插入语句导出成功")
        success_count += 1
    else:
        print("   ❌ SQL插入语句导出失败")
    
    # 报告导出
    if generator.generate_summary_report(definitions):
        print("   ✅ 汇总报告导出成功")
        success_count += 1
    else:
        print("   ❌ 汇总报告导出失败")
    
    print(f"\n📊 导出结果: {success_count}/{total_exports} 成功")
    
    # 数据库操作 (可选)
    if include_database:
        print(f"\n🗄️ 数据库操作...")
        db_config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'your_password',
            'database': 'biomedical_mbse_platform'
        }
        
        try:
            import asyncpg
            database_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
            db_pool = await asyncpg.create_pool(database_url)
            
            # 创建表
            if await generator.create_database_table(db_pool):
                print("   ✅ 数据库表创建成功")
                
                # 填充数据
                if await generator.populate_database_table(db_pool, definitions):
                    print("   ✅ 数据填充成功")
                else:
                    print("   ❌ 数据填充失败")
            else:
                print("   ❌ 数据库表创建失败")
            
            await db_pool.close()
            
        except Exception as e:
            print(f"   ⚠️ 数据库操作失败: {e}")
            print("   💡 请检查数据库配置和连接")
    
    # 运行测试 (可选)
    if run_tests:
        print(f"\n🧪 运行验证测试...")
        try:
            # 导入并运行测试
            from tests.test_core_uml_metaclass_definitions import run_tests
            
            # 临时修改sys.argv来传递测试参数
            original_argv = sys.argv.copy()
            sys.argv = ['test_core_uml_metaclass_definitions.py']
            if include_database:
                sys.argv.append('--database')
            
            test_success = run_tests()
            sys.argv = original_argv
            
            if test_success:
                print("   ✅ 所有测试通过!")
            else:
                print("   ❌ 部分测试失败，请查看详细报告")
                
        except Exception as e:
            print(f"   ⚠️ 测试运行失败: {e}")
            print("   💡 请确保测试模块可用")
    
    # 打印最终摘要
    print_summary(definitions, generator.output_dir)
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='核心UML元类定义生成器')
    parser.add_argument('--output-dir', type=str, default='./output',
                       help='输出目录 (默认: ./output)')
    parser.add_argument('--test', action='store_true',
                       help='运行验证测试')
    parser.add_argument('--database', action='store_true',
                       help='包含数据库操作')
    parser.add_argument('--show-inheritance', action='store_true',
                       help='仅显示继承关系分析')
    
    args = parser.parse_args()
    
    # 如果只是显示继承关系
    if args.show_inheritance:
        print("🌳 UML 2.5 继承关系分析")
        print("=" * 50)
        print_inheritance_analysis()
        return
    
    # 确保输出目录存在
    output_path = Path(args.output_dir)
    output_path.mkdir(exist_ok=True)
    
    try:
        # 运行生成器
        success = asyncio.run(run_generator(
            output_dir=args.output_dir,
            run_tests=args.test,
            include_database=args.database
        ))
        
        if success:
            print(f"\n🎉 核心UML元类定义生成完成!")
            print(f"🔍 查看输出目录 {output_path.absolute()} 获取生成的文件")
            
            if args.test:
                print(f"📋 查看 test_output/test_report.json 获取测试详情")
            
            print(f"\n💡 使用提示:")
            print(f"  - 查看 core_uml_metaclass_summary.md 了解详细统计")
            print(f"  - 导入 core_uml_metaclass_definitions.sql 到数据库")
            print(f"  - 使用 core_uml_metaclass_definitions.json 进行编程集成")
        else:
            print(f"\n❌ 生成过程中发生错误")
            return False
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 