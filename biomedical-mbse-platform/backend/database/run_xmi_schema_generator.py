#!/usr/bin/env python3
"""
XMI结构驱动的数据库Schema生成器运行脚本

使用方法:
    python run_xmi_schema_generator.py [选项]

示例:
    python run_xmi_schema_generator.py                           # 基本运行
    python run_xmi_schema_generator.py --sample                  # 使用样本XMI文件
    python run_xmi_schema_generator.py --output-dir ./output     # 指定输出目录
    python run_xmi_schema_generator.py --schema-name myschema    # 指定Schema名称
    python run_xmi_schema_generator.py --analyze-only           # 仅分析不生成SQL

作者：生物医学MBSE平台开发组
版本：1.0.0
"""

import argparse
import sys
import time
from pathlib import Path
from xmi_based_schema_generator import XMIBasedSchemaGenerator

# YAML配置文件支持
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

def load_database_config(config_file: str = None) -> dict:
    """从YAML配置文件加载数据库配置"""
    default_config = {
        'host': 'localhost',
        'port': 5432,
        'name': 'biomedical_mbse_platform',
        'user': 'mbse_user',
        'password': 'mbse_pass_2024'
    }
    
    if not config_file:
        # 尝试找到配置文件
        possible_paths = [
            '../../deployment/config/database.yaml',
            '../deployment/config/database.yaml',
            './database.yaml',
            '../config/database.yaml'
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                config_file = path
                break
    
    if config_file and Path(config_file).exists() and YAML_AVAILABLE:
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            db_config = config.get('database', {})
            return {
                'host': db_config.get('host', default_config['host']),
                'port': db_config.get('port', default_config['port']),
                'name': db_config.get('name', default_config['name']),
                'user': db_config.get('user', default_config['user']),
                'password': db_config.get('password', default_config['password'])
            }
        except Exception as e:
            print(f"⚠️  读取配置文件失败: {e}，使用默认配置")
            return default_config
    else:
        if not YAML_AVAILABLE:
            print("⚠️  PyYAML未安装，使用默认数据库配置")
        return default_config

def create_sample_xmi():
    """创建一个示例XMI文件用于测试"""
    sample_content = '''<?xml version="1.0" encoding="UTF-8"?>
<xmi:XMI xmlns:uml="http://www.omg.org/spec/UML/20131001"
         xmlns:xmi="http://www.omg.org/spec/XMI/20131001">
    <uml:Package xmi:type="uml:Package" xmi:id="Model" name="TestModel">
        <packageImport xmi:type="uml:PackageImport" xmi:id="import1" 
                       importedPackage="CommonStructure"/>
        
        <packagedElement xmi:type="uml:Package" xmi:id="BusinessLayer" name="BusinessLayer">
            <packagedElement xmi:type="uml:Class" xmi:id="Patient" name="Patient" isAbstract="false">
                <ownedComment xmi:type="uml:Comment" xmi:id="comment1"
                              body="Patient class represents a person receiving medical care">
                    <annotatedElement xmi:idref="Patient"/>
                </ownedComment>
                
                <ownedAttribute xmi:type="uml:Property" xmi:id="patientId" name="patientId" 
                                type="String" visibility="private">
                    <ownedComment xmi:type="uml:Comment" xmi:id="comment2"
                                  body="Unique identifier for the patient">
                        <annotatedElement xmi:idref="patientId"/>
                    </ownedComment>
                </ownedAttribute>
                
                <ownedAttribute xmi:type="uml:Property" xmi:id="name" name="name" 
                                type="String" visibility="public"/>
                                
                <ownedOperation xmi:type="uml:Operation" xmi:id="getName" name="getName" 
                                visibility="public" type="String">
                    <ownedParameter xmi:type="uml:Parameter" xmi:id="return1" 
                                    direction="return" type="String"/>
                </ownedOperation>
            </packagedElement>
            
            <packagedElement xmi:type="uml:Class" xmi:id="Doctor" name="Doctor">
                <generalization xmi:type="uml:Generalization" xmi:id="gen1" general="Person"/>
            </packagedElement>
            
            <packagedElement xmi:type="uml:Association" xmi:id="patientDoctorAssoc" 
                            memberEnd="patientEnd doctorEnd">
                <ownedEnd xmi:type="uml:Property" xmi:id="doctorEnd" name="doctor" 
                          type="Doctor" aggregation="none">
                    <lowerValue xmi:type="uml:LiteralInteger" xmi:id="lower1" value="1"/>
                    <upperValue xmi:type="uml:LiteralUnlimitedNatural" xmi:id="upper1" value="*"/>
                </ownedEnd>
            </packagedElement>
        </packagedElement>
        
        <packagedElement xmi:type="uml:Package" xmi:id="DataLayer" name="DataLayer">
            <packagedElement xmi:type="uml:Interface" xmi:id="Repository" name="Repository">
                <ownedOperation xmi:type="uml:Operation" xmi:id="save" name="save">
                    <ownedParameter xmi:type="uml:Parameter" xmi:id="entity" name="entity" 
                                    direction="in" type="Object"/>
                </ownedOperation>
            </packagedElement>
        </packagedElement>
    </uml:Package>
</xmi:XMI>'''
    
    sample_file = Path("sample_test.xmi")
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    return sample_file

def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("🔍 XMI结构驱动的数据库Schema生成器")
    print("基于XMI文件实际结构动态生成数据库表和关系")
    print("=" * 70)
    print()

def print_analysis_summary(generator: XMIBasedSchemaGenerator):
    """打印分析摘要"""
    table_defs = generator.table_definitions
    
    print("📊 分析结果摘要:")
    print("-" * 50)
    print(f"发现的表数量: {len(table_defs)}")
    print(f"XMI类型数量: {len(generator.type_counts)}")
    print(f"引用关系数量: {len(generator.reference_relationships)}")
    print()
    
    # 命名空间分布
    namespaces = {}
    for table_def in table_defs.values():
        ns = table_def.namespace
        namespaces[ns] = namespaces.get(ns, 0) + 1
    
    print("📁 命名空间分布:")
    for namespace, count in sorted(namespaces.items()):
        print(f"  - {namespace}: {count} 个表")
    print()
    
    # 热门类型
    print("🔥 热门XMI类型 (实例数量TOP5):")
    sorted_types = sorted(generator.type_counts.items(), 
                         key=lambda x: x[1], reverse=True)
    for i, (xmi_type, count) in enumerate(sorted_types[:5], 1):
        table_name = generator._generate_table_name(xmi_type)
        print(f"  {i}. {xmi_type}: {count} 个实例 → {table_name}")
    print()
    
    # 复杂表
    print("🧩 复杂表 (属性数量TOP5):")
    complex_tables = sorted(table_defs.values(), 
                           key=lambda x: len(x.attributes), reverse=True)
    for i, table_def in enumerate(complex_tables[:5], 1):
        print(f"  {i}. {table_def.table_name}: {len(table_def.attributes)} 个属性")
    print()
    
    # 层次结构预览
    print("🌳 表层次结构预览:")
    root_tables = [t for t in table_defs.values() if not t.parent_tables]
    for root_table in root_tables[:3]:  # 只显示前3个根表
        print(f"  📦 {root_table.table_name}")
        for child_name in root_table.child_tables[:2]:  # 每个根表只显示前2个子表
            if child_name in table_defs:
                child_table = table_defs[child_name]
                print(f"    └── {child_table.table_name} ({child_table.instance_count} instances)")
        if len(root_table.child_tables) > 2:
            print(f"    └── ... ({len(root_table.child_tables)-2} 个其他子表)")
    print()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="XMI结构驱动的数据库Schema生成器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                                    # 使用默认UML2.5.xmi文件
  %(prog)s --sample                           # 创建并使用样本XMI文件
  %(prog)s --xmi-file my_model.xmi            # 使用指定的XMI文件
  %(prog)s --output-dir ./schema_output       # 指定输出目录
  %(prog)s --schema-name production_schema    # 指定PostgreSQL Schema名称
  %(prog)s --analyze-only                     # 仅分析结构，不生成SQL
  
数据库创建示例:
  %(prog)s --create-database                  # 连接到默认数据库并创建表
  %(prog)s --create-database --db-host ************* --db-user myuser --db-password mypass
  %(prog)s --create-database --drop-schema --max-tables 10 --verbose
  %(prog)s --sample --create-database --schema-name test_schema

注意: 数据库功能需要安装 psycopg2: pip install psycopg2-binary
        """
    )
    
    parser.add_argument('--xmi-file', 
                       default='biomedical-mbse-platform/resources/domainXmi/UML2.5.xmi',
                       help='XMI文件路径 (默认: UML2.5.xmi)')
    
    parser.add_argument('--sample', action='store_true',
                       help='创建并使用样本XMI文件进行测试')
    
    parser.add_argument('--output-dir', default='./xmi_schema_output',
                       help='输出目录 (默认: ./xmi_schema_output)')
    
    parser.add_argument('--schema-name', default='xmi_generated',
                       help='PostgreSQL Schema名称 (默认: xmi_generated)')
    
    parser.add_argument('--analyze-only', action='store_true',
                       help='仅分析XMI结构，不生成SQL文件')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出模式')
    
    parser.add_argument('--max-tables', type=int, default=None,
                       help='限制生成的表数量（用于大型XMI文件测试）')
    
    # 数据库相关选项
    parser.add_argument('--create-database', action='store_true',
                       help='连接数据库并实际创建表')
    
    parser.add_argument('--config-file',
                       help='数据库配置文件路径 (YAML格式)')
    
    parser.add_argument('--db-host', default=None,
                       help='数据库主机地址 (默认: 从配置文件读取)')
    
    parser.add_argument('--db-port', type=int, default=None,
                       help='数据库端口 (默认: 从配置文件读取)')
    
    parser.add_argument('--db-name', default=None,
                       help='数据库名称 (默认: 从配置文件读取)')
    
    parser.add_argument('--db-user', default=None,
                       help='数据库用户名 (默认: 从配置文件读取)')
    
    parser.add_argument('--db-password', default=None,
                       help='数据库密码 (默认: 从配置文件读取)')
    
    parser.add_argument('--drop-schema', action='store_true',
                       help='删除现有Schema后重新创建')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 确定XMI文件
    if args.sample:
        print("🧪 创建样本XMI文件用于测试...")
        xmi_file = create_sample_xmi()
        print(f"✅ 样本文件创建: {xmi_file}")
    else:
        xmi_file = Path(args.xmi_file)
        if not xmi_file.exists():
            print(f"❌ XMI文件不存在: {xmi_file}")
            sys.exit(1)
    
    print(f"📄 使用XMI文件: {xmi_file}")
    print()
    
    try:
        # 创建生成器
        print("🚀 初始化XMI结构分析器...")
        generator = XMIBasedSchemaGenerator(output_dir=args.output_dir)
        
        # 分析XMI文件
        print(f"🔍 分析XMI文件结构...")
        start_time = time.time()
        
        table_definitions = generator.analyze_xmi_file(str(xmi_file))
        
        analysis_time = time.time() - start_time
        print(f"✅ XMI分析完成! (耗时: {analysis_time:.2f}秒)")
        print()
        
        # 打印分析摘要
        print_analysis_summary(generator)
        
        # 限制表数量（用于测试）
        if args.max_tables and len(table_definitions) > args.max_tables:
            print(f"⚠️  限制表数量到 {args.max_tables} 个（原始: {len(table_definitions)} 个）")
            # 保留最重要的表
            important_tables = sorted(table_definitions.values(), 
                                    key=lambda x: (x.instance_count * len(x.attributes)), 
                                    reverse=True)
            limited_tables = {t.table_name: t for t in important_tables[:args.max_tables]}
            generator.table_definitions = limited_tables
            print()
        
        if not args.analyze_only:
            # 生成Schema文件
            print("💾 生成Schema文件...")
            start_time = time.time()
            
            exported_files = generator.export_schema_files(f"xmi_schema_{int(time.time())}")
            
            export_time = time.time() - start_time
            print(f"✅ Schema文件生成完成! (耗时: {export_time:.2f}秒)")
            print()
            
            # 显示生成的文件
            print("📁 生成的文件:")
            for file_type, file_path in exported_files.items():
                size = file_path.stat().st_size / 1024  # KB
                print(f"  📄 {file_type.upper()}: {file_path} ({size:.1f} KB)")
            print()
            
            # 显示PostgreSQL Schema预览
            if 'sql' in exported_files:
                sql_file = exported_files['sql']
                with open(sql_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    table_lines = [line for line in lines if line.strip().startswith('CREATE TABLE')]
                    
                print(f"🗄️  PostgreSQL Schema预览 (Schema: {args.schema_name}):")
                print(f"   共生成 {len(table_lines)} 个表")
                if table_lines:
                    print("   前5个表:")
                    for line in table_lines[:5]:
                        table_name = line.split('.')[-1].split('(')[0].strip()
                        print(f"     - {table_name}")
                    if len(table_lines) > 5:
                        print(f"     - ... (还有 {len(table_lines)-5} 个表)")
        
        # 数据库创建选项
        if args.create_database:
            print()
            print("🗄️  开始连接数据库并创建表...")
            print("-" * 60)
            
            # 加载数据库配置
            print("📋 加载数据库配置...")
            db_config = load_database_config(args.config_file)
            
            # 命令行参数覆盖配置文件
            db_host = args.db_host or db_config['host']
            db_port = args.db_port or db_config['port']
            db_name = args.db_name or db_config['name']
            db_user = args.db_user or db_config['user']
            db_password = args.db_password or db_config['password']
            
            print(f"  📍 主机: {db_host}:{db_port}")
            print(f"  🗄️  数据库: {db_name}")
            print(f"  👤 用户: {db_user}")
            
            # 连接数据库
            print(f"🔌 连接数据库...")
            success = generator.connect_database(
                host=db_host,
                port=db_port,
                database=db_name,
                user=db_user,
                password=db_password
            )
            
            if not success:
                print("❌ 数据库连接失败，跳过表创建")
            else:
                try:
                    # 创建Schema
                    print(f"🏗️  创建Schema: {args.schema_name}")
                    generator.create_schema_in_database(
                        schema_name=args.schema_name,
                        drop_if_exists=args.drop_schema
                    )
                    
                    # 创建表
                    print(f"📋 创建数据库表...")
                    table_results = generator.create_tables_in_database(
                        schema_name=args.schema_name,
                        max_tables=args.max_tables
                    )
                    
                    # 显示结果统计
                    success_count = sum(1 for success in table_results.values() if success)
                    total_count = len(table_results)
                    print()
                    print(f"📊 表创建结果:")
                    print(f"  ✅ 成功: {success_count} 个表")
                    print(f"  ❌ 失败: {total_count - success_count} 个表")
                    print(f"  📈 成功率: {success_count/total_count*100:.1f}%")
                    
                    if args.verbose and total_count - success_count > 0:
                        print(f"\n❌ 失败的表:")
                        for table_name, success in table_results.items():
                            if not success:
                                print(f"  - {table_name}")
                    
                    # 获取数据库表信息
                    if success_count > 0:
                        print(f"\n🔍 验证数据库表:")
                        table_info = generator.get_database_table_info(args.schema_name)
                        if table_info:
                            print(f"  📋 数据库中的表数量: {len(table_info)}")
                            print(f"  📊 前5个表的信息:")
                            for i, (table_name, info) in enumerate(list(table_info.items())[:5], 1):
                                print(f"    {i}. {table_name}: {info['column_count']} 列, {info['row_count']} 行")
                        
                        print(f"\n🎯 数据库访问信息:")
                        print(f"  📍 连接字符串: postgresql://{db_user}@{db_host}:{db_port}/{db_name}")
                        print(f"  🏗️  Schema名称: {args.schema_name}")
                        print(f"  📝 查询示例: SELECT * FROM {args.schema_name}.xmi_classifier LIMIT 10;")
                    
                except Exception as e:
                    print(f"❌ 数据库操作失败: {e}")
                    if args.verbose:
                        import traceback
                        traceback.print_exc()
                finally:
                    # 断开数据库连接
                    generator.disconnect_database()
        
        print()
        print("🎉 XMI结构分析和Schema生成完成!")
        
        if args.verbose:
            print()
            print("🔍 详细信息:")
            print(f"  - 输出目录: {Path(args.output_dir).absolute()}")
            print(f"  - 表定义数量: {len(generator.table_definitions)}")
            print(f"  - 引用关系数量: {len(generator.reference_relationships)}")
            print(f"  - 命名空间数量: {len(generator.namespaces)}")
        
        # 清理样本文件
        if args.sample and xmi_file.exists():
            xmi_file.unlink()
            if args.verbose:
                print(f"🧹 清理样本文件: {xmi_file}")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 生成失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 