-- =====================================================
-- 核心Schema设计 - Element抽象元对象架构
-- 版本：2.0.0 (动态架构支持版)
-- 创建时间：2024-12-09
-- 描述：实现领域驱动的Element抽象架构 + 动态Schema支持
-- 
-- 🎯 使用说明：
-- 此文件是动态Element架构的核心基础设施，提供：
-- 1. Element抽象的基础表结构
-- 2. 领域注册和管理功能
-- 3. 动态Schema创建支持
-- 4. 跨域关系管理
-- 
-- 📋 部署方式：
-- - 必须首先执行此文件以建立核心抽象层
-- - 所有其他领域通过DomainFactory动态创建
-- - 此Schema是整个系统运行的前提条件
-- =====================================================

-- 创建核心Schema
CREATE SCHEMA IF NOT EXISTS core_schema;

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. Element元数据表 - 所有Element的抽象注册
-- =====================================================
CREATE TABLE IF NOT EXISTS core_schema.element_metadata (
    element_id TEXT PRIMARY KEY,               -- 全局唯一Element ID
    domain_schema TEXT NOT NULL,               -- 所属领域Schema名称
    element_type TEXT NOT NULL,                -- Element类型
    tag TEXT NOT NULL,                         -- Element标签
    local_name TEXT NOT NULL,                  -- 本地名称
    
    -- Element通用属性
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deprecated', 'deleted', 'pending')),
    version TEXT DEFAULT '1.0',                -- Element版本
    
    -- 层次结构
    parent_element_id TEXT,                    -- 父Element ID
    depth_level INTEGER DEFAULT 0 CHECK (depth_level >= 0),
    hierarchy_path TEXT,                       -- 层次路径 (如: /root/domain/type/)
    
    -- 语义信息
    semantic_tags JSONB DEFAULT '[]',          -- 语义标签数组
    ontology_mappings JSONB DEFAULT '{}',      -- 本体映射
    domain_category TEXT,                      -- 领域分类
    domain_code TEXT,                          -- 领域代码
    
    -- 元数据
    description TEXT,                          -- 描述
    metadata JSONB DEFAULT '{}',               -- 扩展元数据
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,                           -- 创建者
    updated_by TEXT,                           -- 更新者
    
    -- 外键约束
    FOREIGN KEY (parent_element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE SET NULL,
    
    -- 约束
    CONSTRAINT valid_element_id CHECK (element_id ~ '^[a-zA-Z][a-zA-Z0-9_-]*$'),
    CONSTRAINT valid_hierarchy_path CHECK (hierarchy_path IS NULL OR hierarchy_path ~ '^(/[^/]+)+/$')
);

-- =====================================================
-- 2. Element类型定义表 - 动态Element类型注册
-- =====================================================
CREATE TABLE IF NOT EXISTS core_schema.element_type_definitions (
    type_id TEXT PRIMARY KEY,                  -- 类型ID (如: user_element, project_element)
    type_name TEXT NOT NULL,                   -- 类型名称
    domain_schema TEXT NOT NULL,               -- 所属领域Schema
    
    -- 字段定义 (JSON Schema格式)
    field_definitions JSONB NOT NULL,          -- 字段定义
    table_name TEXT NOT NULL,                  -- 对应的表名
    
    -- 索引定义
    index_definitions JSONB DEFAULT '[]',      -- 索引定义
    constraint_definitions JSONB DEFAULT '[]', -- 约束定义
    
    -- 关系定义
    relationship_definitions JSONB DEFAULT '{}', -- 与其他Element类型的关系定义
    cross_domain_refs JSONB DEFAULT '[]',      -- 跨域引用字段
    
    -- 类型配置
    type_config JSONB DEFAULT '{
        "auto_create_table": true,
        "supports_versioning": false,
        "enable_audit": true,
        "cache_policy": "default"
    }',
    
    -- 状态
    is_active BOOLEAN DEFAULT true,
    is_system_type BOOLEAN DEFAULT false,      -- 是否为系统内置类型
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (domain_schema) REFERENCES core_schema.domain_registry(schema_name),
    
    -- 约束
    CONSTRAINT valid_type_id CHECK (type_id ~ '^[a-z][a-z0-9_]*_element$'),
    CONSTRAINT valid_table_name CHECK (table_name ~ '^[a-z][a-z0-9_]*_elements$')
);

-- =====================================================
-- 3. 领域注册表 - 管理所有领域Schema (增强版)
-- =====================================================
CREATE TABLE IF NOT EXISTS core_schema.domain_registry (
    domain_name TEXT PRIMARY KEY,              -- 领域名称
    schema_name TEXT UNIQUE NOT NULL,          -- 对应的Schema名称
    display_name TEXT NOT NULL,                -- 显示名称
    description TEXT,                          -- 领域描述
    
    -- 领域配置
    domain_config JSONB NOT NULL DEFAULT '{}', -- 领域配置
    element_types JSONB NOT NULL DEFAULT '[]', -- 支持的Element类型列表
    api_endpoints JSONB DEFAULT '{}',          -- API端点配置
    
    -- 动态Schema配置
    schema_template JSONB DEFAULT '{}',        -- Schema模板
    auto_create_indexes BOOLEAN DEFAULT true,  -- 是否自动创建索引
    enable_cross_domain_refs BOOLEAN DEFAULT true, -- 是否启用跨域引用
    
    -- 领域元数据
    namespace_prefix TEXT,                      -- 命名空间前缀
    ontology_uri TEXT,                         -- 本体URI
    version TEXT DEFAULT '1.0',                -- 领域Schema版本
    
    -- 状态管理
    is_active BOOLEAN DEFAULT true,
    is_core_domain BOOLEAN DEFAULT false,      -- 是否为核心领域
    is_auto_generated BOOLEAN DEFAULT false,   -- 是否为自动生成
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CONSTRAINT valid_schema_name CHECK (schema_name ~ '^[a-z][a-z0-9_]*_schema$'),
    CONSTRAINT valid_namespace_prefix CHECK (namespace_prefix IS NULL OR namespace_prefix ~ '^[a-z][a-z0-9]*$')
);

-- =====================================================
-- 4. 动态Schema状态表 - 跟踪动态创建的Schema
-- =====================================================
CREATE TABLE IF NOT EXISTS core_schema.dynamic_schemas (
    schema_name TEXT PRIMARY KEY,              -- Schema名称
    domain_name TEXT NOT NULL,                 -- 关联的领域
    
    -- Schema状态
    creation_status TEXT DEFAULT 'pending' CHECK (creation_status IN ('pending', 'creating', 'active', 'error')),
    creation_log JSONB DEFAULT '[]',           -- 创建日志
    
    -- Schema信息
    created_tables JSONB DEFAULT '[]',         -- 已创建的表列表
    created_indexes JSONB DEFAULT '[]',        -- 已创建的索引列表
    created_triggers JSONB DEFAULT '[]',       -- 已创建的触发器列表
    
    -- 依赖关系
    dependencies JSONB DEFAULT '[]',           -- 依赖的其他Schema
    dependents JSONB DEFAULT '[]',             -- 依赖此Schema的其他Schema
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (domain_name) REFERENCES core_schema.domain_registry(domain_name)
);

-- =====================================================
-- 5. 跨领域关系表 - 管理不同Schema间的关系 (增强版)
-- =====================================================
CREATE TABLE IF NOT EXISTS core_schema.cross_domain_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    relationship_id TEXT UNIQUE NOT NULL,      -- 关系标识符
    
    -- 关系端点
    source_element_id TEXT NOT NULL,           -- 源Element ID
    target_element_id TEXT NOT NULL,           -- 目标Element ID
    source_domain TEXT NOT NULL,               -- 源领域
    target_domain TEXT NOT NULL,               -- 目标领域
    
    -- 关系类型
    relationship_type TEXT NOT NULL,           -- 关系类型
    relationship_direction TEXT DEFAULT 'directed' CHECK (relationship_direction IN ('directed', 'undirected', 'bidirectional')),
    
    -- 关系属性
    relationship_data JSONB DEFAULT '{}',      -- 关系的详细数据
    strength DECIMAL(3,2) DEFAULT 1.0 CHECK (strength >= 0.0 AND strength <= 1.0),
    is_direct BOOLEAN DEFAULT true,            -- 是否直接关系
    
    -- 索引优化字段
    source_type TEXT,                          -- 源Element类型 (冗余，用于索引)
    target_type TEXT,                          -- 目标Element类型 (冗余，用于索引)
    
    -- 关系语义
    semantic_meaning TEXT,                     -- 语义含义
    ontology_mapping JSONB DEFAULT '{}',       -- 本体映射
    
    -- 时间戳和状态
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deprecated', 'deleted')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    
    -- 外键约束
    FOREIGN KEY (source_element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
    FOREIGN KEY (target_element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
    FOREIGN KEY (source_domain) REFERENCES core_schema.domain_registry(domain_name),
    FOREIGN KEY (target_domain) REFERENCES core_schema.domain_registry(domain_name),
    
    -- 唯一约束
    UNIQUE(source_element_id, target_element_id, relationship_type),
    
    -- 约束
    CONSTRAINT no_self_reference CHECK (source_element_id != target_element_id),
    CONSTRAINT valid_relationship_id CHECK (relationship_id ~ '^rel_[a-zA-Z0-9_-]+$')
);

-- =====================================================
-- 6. 跨域索引管理表 - 自动管理跨域查询索引
-- =====================================================
CREATE TABLE IF NOT EXISTS core_schema.cross_domain_indexes (
    index_id TEXT PRIMARY KEY,                 -- 索引ID
    index_name TEXT UNIQUE NOT NULL,           -- 索引名称
    
    -- 索引定义
    source_schema TEXT NOT NULL,               -- 源Schema
    target_schema TEXT NOT NULL,               -- 目标Schema
    index_type TEXT NOT NULL,                  -- 索引类型 (btree, gin, gist, hash)
    
    -- 索引字段
    indexed_fields JSONB NOT NULL,             -- 被索引的字段
    index_expression TEXT,                     -- 索引表达式
    
    -- 索引配置
    is_unique BOOLEAN DEFAULT false,
    is_partial BOOLEAN DEFAULT false,          -- 是否为部分索引
    partial_condition TEXT,                    -- 部分索引条件
    
    -- 使用统计
    usage_count INTEGER DEFAULT 0,             -- 使用次数
    last_used TIMESTAMP WITH TIME ZONE,        -- 最后使用时间
    performance_impact JSONB DEFAULT '{}',     -- 性能影响统计
    
    -- 状态
    is_active BOOLEAN DEFAULT true,
    auto_created BOOLEAN DEFAULT false,        -- 是否自动创建
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. Element属性映射表 - 跨Schema的属性同步
-- =====================================================
CREATE TABLE IF NOT EXISTS core_schema.element_attribute_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    element_id TEXT NOT NULL,                  -- Element ID
    
    -- 属性信息
    attribute_name TEXT NOT NULL,              -- 属性名称
    attribute_path TEXT NOT NULL,              -- 属性路径 (如: profile.display_name)
    data_type TEXT NOT NULL,                   -- 数据类型
    is_indexed BOOLEAN DEFAULT false,          -- 是否建立索引
    
    -- 同步配置
    sync_to_core BOOLEAN DEFAULT false,        -- 是否同步到核心
    sync_frequency TEXT DEFAULT 'on_change',   -- 同步频率
    
    -- 跨域映射
    cross_domain_mappings JSONB DEFAULT '{}',  -- 跨域属性映射
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
    UNIQUE(element_id, attribute_name)
);

-- =====================================================
-- 8. 系统日志表 - 全局操作日志
-- =====================================================
CREATE TABLE IF NOT EXISTS core_schema.system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_id TEXT UNIQUE NOT NULL,               -- 日志ID
    
    -- 操作信息
    operation_type TEXT NOT NULL,              -- 操作类型
    target_schema TEXT,                        -- 目标Schema
    target_element_id TEXT,                    -- 目标Element
    operation_result TEXT NOT NULL CHECK (operation_result IN ('success', 'failure', 'error', 'warning')),
    
    -- 操作详情
    operation_details JSONB DEFAULT '{}',      -- 操作详情
    error_message TEXT,                        -- 错误信息
    execution_time_ms INTEGER,                 -- 执行时间(毫秒)
    
    -- 操作上下文
    user_id TEXT,                              -- 操作用户
    session_id TEXT,                           -- 会话ID
    ip_address INET,                           -- IP地址
    user_agent TEXT,                           -- 用户代理
    
    -- 时间戳
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引字段
    INDEX idx_operation_type (operation_type),
    INDEX idx_target_schema (target_schema),
    INDEX idx_timestamp (timestamp),
    INDEX idx_user_id (user_id),
    INDEX idx_operation_result (operation_result)
);

-- =====================================================
-- 索引创建 (增强版)
-- =====================================================

-- element_metadata表索引
CREATE INDEX IF NOT EXISTS idx_element_metadata_domain_type ON core_schema.element_metadata(domain_schema, element_type);
CREATE INDEX IF NOT EXISTS idx_element_metadata_status ON core_schema.element_metadata(status) WHERE status != 'deleted';
CREATE INDEX IF NOT EXISTS idx_element_metadata_semantic_tags ON core_schema.element_metadata USING GIN(semantic_tags);
CREATE INDEX IF NOT EXISTS idx_element_metadata_parent ON core_schema.element_metadata(parent_element_id) WHERE parent_element_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_element_metadata_hierarchy ON core_schema.element_metadata(hierarchy_path) WHERE hierarchy_path IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_element_metadata_created_at ON core_schema.element_metadata(created_at);
CREATE INDEX IF NOT EXISTS idx_element_metadata_domain_category ON core_schema.element_metadata(domain_category) WHERE domain_category IS NOT NULL;

-- element_type_definitions表索引
CREATE INDEX IF NOT EXISTS idx_element_type_definitions_domain ON core_schema.element_type_definitions(domain_schema);
CREATE INDEX IF NOT EXISTS idx_element_type_definitions_active ON core_schema.element_type_definitions(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_element_type_definitions_field_defs ON core_schema.element_type_definitions USING GIN(field_definitions);

-- domain_registry表索引
CREATE INDEX IF NOT EXISTS idx_domain_registry_active ON core_schema.domain_registry(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_domain_registry_core ON core_schema.domain_registry(is_core_domain) WHERE is_core_domain = true;
CREATE INDEX IF NOT EXISTS idx_domain_registry_auto_generated ON core_schema.domain_registry(is_auto_generated);

-- dynamic_schemas表索引
CREATE INDEX IF NOT EXISTS idx_dynamic_schemas_status ON core_schema.dynamic_schemas(creation_status);
CREATE INDEX IF NOT EXISTS idx_dynamic_schemas_domain ON core_schema.dynamic_schemas(domain_name);

-- cross_domain_relationships表索引 (增强版)
CREATE INDEX IF NOT EXISTS idx_cross_relationships_source ON core_schema.cross_domain_relationships(source_element_id, source_domain);
CREATE INDEX IF NOT EXISTS idx_cross_relationships_target ON core_schema.cross_domain_relationships(target_element_id, target_domain);
CREATE INDEX IF NOT EXISTS idx_cross_relationships_type ON core_schema.cross_domain_relationships(relationship_type);
CREATE INDEX IF NOT EXISTS idx_cross_relationships_status ON core_schema.cross_domain_relationships(status) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_cross_relationships_types ON core_schema.cross_domain_relationships(source_type, target_type);
CREATE INDEX IF NOT EXISTS idx_cross_relationships_created_at ON core_schema.cross_domain_relationships(created_at);

-- cross_domain_indexes表索引
CREATE INDEX IF NOT EXISTS idx_cross_domain_indexes_schemas ON core_schema.cross_domain_indexes(source_schema, target_schema);
CREATE INDEX IF NOT EXISTS idx_cross_domain_indexes_active ON core_schema.cross_domain_indexes(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_cross_domain_indexes_usage ON core_schema.cross_domain_indexes(usage_count, last_used);

-- element_attribute_mappings表索引
CREATE INDEX IF NOT EXISTS idx_attribute_mappings_element ON core_schema.element_attribute_mappings(element_id);
CREATE INDEX IF NOT EXISTS idx_attribute_mappings_sync ON core_schema.element_attribute_mappings(sync_to_core) WHERE sync_to_core = true;
CREATE INDEX IF NOT EXISTS idx_attribute_mappings_cross_domain ON core_schema.element_attribute_mappings USING GIN(cross_domain_mappings);

-- =====================================================
-- 触发器创建
-- =====================================================

-- 自动更新updated_at字段的触发器函数
CREATE OR REPLACE FUNCTION core_schema.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为各表创建更新时间戳触发器
CREATE TRIGGER trigger_element_metadata_updated_at 
    BEFORE UPDATE ON core_schema.element_metadata 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_element_type_definitions_updated_at 
    BEFORE UPDATE ON core_schema.element_type_definitions 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_domain_registry_updated_at 
    BEFORE UPDATE ON core_schema.domain_registry 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_dynamic_schemas_updated_at 
    BEFORE UPDATE ON core_schema.dynamic_schemas 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_cross_relationships_updated_at 
    BEFORE UPDATE ON core_schema.cross_domain_relationships 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_cross_domain_indexes_updated_at 
    BEFORE UPDATE ON core_schema.cross_domain_indexes 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_attribute_mappings_updated_at 
    BEFORE UPDATE ON core_schema.element_attribute_mappings 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

-- 层次路径自动生成触发器
CREATE OR REPLACE FUNCTION core_schema.generate_hierarchy_path()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.parent_element_id IS NOT NULL THEN
        -- 获取父Element的层次路径和深度
        SELECT 
            COALESCE(hierarchy_path, '/') || NEW.element_id || '/',
            depth_level + 1
        INTO NEW.hierarchy_path, NEW.depth_level
        FROM core_schema.element_metadata 
        WHERE element_id = NEW.parent_element_id;
    ELSE
        NEW.hierarchy_path = '/' || NEW.element_id || '/';
        NEW.depth_level = 0;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_element_hierarchy_path 
    BEFORE INSERT OR UPDATE ON core_schema.element_metadata 
    FOR EACH ROW EXECUTE FUNCTION core_schema.generate_hierarchy_path();

-- 跨域关系类型自动填充触发器
CREATE OR REPLACE FUNCTION core_schema.auto_fill_relationship_types()
RETURNS TRIGGER AS $$
BEGIN
    -- 自动填充源和目标Element类型
    SELECT element_type INTO NEW.source_type
    FROM core_schema.element_metadata
    WHERE element_id = NEW.source_element_id;
    
    SELECT element_type INTO NEW.target_type
    FROM core_schema.element_metadata
    WHERE element_id = NEW.target_element_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_auto_fill_relationship_types
    BEFORE INSERT OR UPDATE ON core_schema.cross_domain_relationships
    FOR EACH ROW EXECUTE FUNCTION core_schema.auto_fill_relationship_types();

-- =====================================================
-- 初始数据插入 (增强版)
-- =====================================================

-- 注册核心领域
INSERT INTO core_schema.domain_registry (
    domain_name, schema_name, display_name, description,
    domain_config, element_types, is_core_domain, namespace_prefix,
    schema_template, auto_create_indexes, enable_cross_domain_refs
) VALUES 
    ('core', 'core_schema', '核心架构领域', '提供Element抽象和跨领域服务',
     '{"provides_abstraction": true, "manages_cross_domain": true, "supports_dynamic_creation": true}',
     '["abstract_element", "domain_element", "relationship_element"]',
     true, 'core', '{}', true, true),
    ('security', 'security_schema', '安全领域', '用户、角色、权限管理',
     '{"authentication": true, "authorization": true, "audit": true, "supports_rbac": true}',
     '["user_element", "role_element", "permission_element", "session_element"]',
     false, 'sec', '{}', true, true),
    ('modeling', 'modeling_schema', '建模领域', '项目、模型、组件管理',
     '{"supports_mbse": true, "version_control": true, "collaborative": true}',
     '["project_element", "model_element", "component_element", "relationship_element"]',
     false, 'mdl', '{}', true, true),
    ('biomedical', 'biomedical_schema', '生物医学领域', '生物体、蛋白质、通路管理',
     '{"biological_data": true, "pathway_analysis": true, "genomics": true}',
     '["organism_element", "protein_element", "pathway_element", "experiment_element"]',
     false, 'bio', '{}', true, true)
ON CONFLICT (domain_name) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    domain_config = EXCLUDED.domain_config,
    element_types = EXCLUDED.element_types,
    schema_template = EXCLUDED.schema_template,
    auto_create_indexes = EXCLUDED.auto_create_indexes,
    enable_cross_domain_refs = EXCLUDED.enable_cross_domain_refs,
    updated_at = CURRENT_TIMESTAMP;

-- 注册基础Element类型定义
INSERT INTO core_schema.element_type_definitions (
    type_id, type_name, domain_schema, field_definitions, table_name,
    index_definitions, relationship_definitions, cross_domain_refs, type_config
) VALUES 
    ('user_element', 'User Element', 'security_schema', '{
        "username": {"type": "string", "unique": true, "required": true, "max_length": 64},
        "email": {"type": "string", "unique": true, "required": true, "format": "email"},
        "password_hash": {"type": "string", "required": true, "encrypted": true},
        "profile": {"type": "jsonb", "default": "{}"},
        "is_active": {"type": "boolean", "default": true},
        "last_login": {"type": "timestamp", "nullable": true}
    }', 'user_elements', 
    '[{"fields": ["username"], "type": "btree"}, {"fields": ["email"], "type": "btree"}]',
    '{"roles": {"type": "many_to_many", "target": "role_element"}}',
    '["project_participations", "model_ownerships"]',
    '{"auto_create_table": true, "supports_versioning": false, "enable_audit": true}'),
    
    ('role_element', 'Role Element', 'security_schema', '{
        "role_name": {"type": "string", "unique": true, "required": true},
        "display_name": {"type": "string", "required": true},
        "description": {"type": "text", "nullable": true},
        "is_active": {"type": "boolean", "default": true}
    }', 'role_elements',
    '[{"fields": ["role_name"], "type": "btree"}]',
    '{"users": {"type": "many_to_many", "target": "user_element"}, "permissions": {"type": "many_to_many", "target": "permission_element"}}',
    '[]',
    '{"auto_create_table": true, "supports_versioning": false, "enable_audit": true}')
ON CONFLICT (type_id) DO UPDATE SET
    field_definitions = EXCLUDED.field_definitions,
    index_definitions = EXCLUDED.index_definitions,
    relationship_definitions = EXCLUDED.relationship_definitions,
    cross_domain_refs = EXCLUDED.cross_domain_refs,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 权限设置
-- =====================================================

-- 创建核心Schema的只读角色
CREATE ROLE IF NOT EXISTS core_schema_reader;
GRANT USAGE ON SCHEMA core_schema TO core_schema_reader;
GRANT SELECT ON ALL TABLES IN SCHEMA core_schema TO core_schema_reader;

-- 创建核心Schema的管理员角色
CREATE ROLE IF NOT EXISTS core_schema_admin;
GRANT USAGE ON SCHEMA core_schema TO core_schema_admin;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA core_schema TO core_schema_admin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA core_schema TO core_schema_admin;

-- 默认权限设置
ALTER DEFAULT PRIVILEGES IN SCHEMA core_schema GRANT SELECT ON TABLES TO core_schema_reader;
ALTER DEFAULT PRIVILEGES IN SCHEMA core_schema GRANT ALL ON TABLES TO core_schema_admin;

-- =====================================================
-- 视图创建 (增强版)
-- =====================================================

-- Element概览视图
CREATE OR REPLACE VIEW core_schema.v_element_overview AS
SELECT 
    em.element_id,
    em.domain_schema,
    em.element_type,
    em.tag,
    em.local_name,
    em.status,
    em.version,
    em.depth_level,
    em.semantic_tags,
    em.created_at,
    em.updated_at,
    dr.display_name as domain_display_name,
    dr.namespace_prefix,
    etd.type_name as element_type_name
FROM core_schema.element_metadata em
JOIN core_schema.domain_registry dr ON em.domain_schema = dr.schema_name
LEFT JOIN core_schema.element_type_definitions etd ON em.element_type = etd.type_id
WHERE em.status != 'deleted';

-- 跨领域关系视图
CREATE OR REPLACE VIEW core_schema.v_cross_domain_relationships AS
SELECT 
    cdr.relationship_id,
    cdr.source_element_id,
    cdr.target_element_id,
    cdr.source_domain,
    cdr.target_domain,
    cdr.relationship_type,
    cdr.strength,
    cdr.semantic_meaning,
    cdr.created_at,
    se.tag as source_tag,
    te.tag as target_tag,
    cdr.source_type,
    cdr.target_type
FROM core_schema.cross_domain_relationships cdr
JOIN core_schema.element_metadata se ON cdr.source_element_id = se.element_id
JOIN core_schema.element_metadata te ON cdr.target_element_id = te.element_id
WHERE cdr.status = 'active';

-- 动态Schema状态视图
CREATE OR REPLACE VIEW core_schema.v_dynamic_schema_status AS
SELECT 
    ds.schema_name,
    ds.domain_name,
    ds.creation_status,
    dr.display_name as domain_display_name,
    array_length(ds.created_tables::text[], 1) as table_count,
    array_length(ds.created_indexes::text[], 1) as index_count,
    ds.created_at,
    ds.updated_at
FROM core_schema.dynamic_schemas ds
JOIN core_schema.domain_registry dr ON ds.domain_name = dr.domain_name
ORDER BY ds.created_at DESC;

-- Element类型统计视图
CREATE OR REPLACE VIEW core_schema.v_element_type_stats AS
SELECT 
    etd.type_id,
    etd.type_name,
    etd.domain_schema,
    COUNT(em.element_id) as element_count,
    etd.is_active,
    etd.created_at
FROM core_schema.element_type_definitions etd
LEFT JOIN core_schema.element_metadata em ON etd.type_id = em.element_type
WHERE etd.is_active = true
GROUP BY etd.type_id, etd.type_name, etd.domain_schema, etd.is_active, etd.created_at
ORDER BY element_count DESC;

-- 完成提示
SELECT 'Core Schema 增强版初始化完成！支持动态领域创建和跨域联动架构。' as message; 