-- =====================================================
-- 安全领域Schema设计 - SecurityDomain具体实现
-- 版本：1.0.0 (示例模板版)
-- 创建时间：2024-12-09
-- 描述：实现基于Element的用户、角色、权限管理
-- 
-- ⚠️  重要说明：在新的动态架构中的作用：
-- 
-- 🎯 作为示例模板：
-- - 展示如何基于Element设计领域Schema的最佳实践
-- - 提供完整的安全领域实现参考
-- - 演示表结构、索引、触发器的设计模式
-- 
-- 🔧 部署选项：
-- A) 静态部署：直接执行此SQL创建传统的security_schema
-- B) 动态创建：使用SecurityDomainManager通过DomainFactory创建
-- C) 自定义模板：基于此文件修改创建自定义安全领域
-- 
-- 📋 推荐方式：
-- - 开发环境：可直接部署用于快速原型
-- - 生产环境：建议通过DomainFactory动态创建以获得完整的动态架构支持
-- - 自定义项目：基于此模板调整字段和约束
-- =====================================================

-- 创建安全Schema
CREATE SCHEMA IF NOT EXISTS security_schema;

-- =====================================================
-- 1. 用户Element表 - UserElement的具体实现
-- =====================================================
CREATE TABLE IF NOT EXISTS security_schema.user_elements (
    element_id TEXT PRIMARY KEY,                   -- 继承自core_schema.element_metadata
    
    -- 基本身份信息
    username TEXT UNIQUE NOT NULL,                 -- 用户名
    email TEXT UNIQUE NOT NULL,                    -- 邮箱
    password_hash TEXT NOT NULL,                   -- 密码哈希
    phone TEXT,                                    -- 电话号码
    
    -- 用户资料 (JSONB for flexibility)
    profile JSONB NOT NULL DEFAULT '{
        "display_name": "",
        "first_name": "",
        "last_name": "",
        "department": "",
        "position": "",
        "avatar": null,
        "bio": "",
        "location": ""
    }',
    
    -- 用户偏好设置
    preferences JSONB DEFAULT '{
        "language": "zh-CN",
        "timezone": "Asia/Shanghai",
        "theme": "default",
        "notifications": {
            "email": true,
            "system": true,
            "mobile": false
        }
    }',
    
    -- 安全配置
    security_config JSONB DEFAULT '{
        "two_factor_enabled": false,
        "password_policy": "standard",
        "session_timeout": 3600,
        "allowed_login_attempts": 5
    }',
    
    -- 状态信息
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,              -- 邮箱验证状态
    is_superuser BOOLEAN DEFAULT false,             -- 超级用户标志
    
    -- 登录和安全状态
    last_login TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    failed_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
    
    -- 约束
    CONSTRAINT valid_username CHECK (username ~ '^[a-zA-Z][a-zA-Z0-9_-]{2,31}$'),
    CONSTRAINT valid_email CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_phone CHECK (phone IS NULL OR phone ~ '^\+?[1-9]\d{1,14}$'),
    CONSTRAINT valid_failed_attempts CHECK (failed_attempts >= 0),
    CONSTRAINT valid_login_count CHECK (login_count >= 0)
);

-- =====================================================
-- 2. 角色Element表 - RoleElement的具体实现
-- =====================================================
CREATE TABLE IF NOT EXISTS security_schema.role_elements (
    element_id TEXT PRIMARY KEY,                   -- 继承自core_schema.element_metadata
    
    -- 角色基本信息
    role_name TEXT UNIQUE NOT NULL,                -- 角色名称
    display_name TEXT NOT NULL,                    -- 显示名称
    description TEXT,                              -- 角色描述
    
    -- 角色配置
    role_config JSONB DEFAULT '{
        "assignable": true,
        "visible": true,
        "system_role": false,
        "auto_assign_conditions": []
    }',
    
    -- 权限配置 (存储权限ID列表)
    permissions JSONB DEFAULT '[]',                -- 权限ID数组
    permission_policies JSONB DEFAULT '{}',        -- 权限策略配置
    
    -- 层级信息
    parent_role_id TEXT,                           -- 父角色ID
    role_level INTEGER DEFAULT 0,                  -- 角色层级
    role_hierarchy_path TEXT,                      -- 角色层级路径
    
    -- 角色范围和限制
    scope_config JSONB DEFAULT '{
        "scope_type": "global",
        "scope_values": [],
        "resource_restrictions": {}
    }',
    
    -- 状态信息
    is_builtin BOOLEAN DEFAULT false,              -- 是否内置角色
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,              -- 是否默认角色
    
    -- 统计信息
    user_count INTEGER DEFAULT 0,                  -- 拥有此角色的用户数
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
    FOREIGN KEY (parent_role_id) REFERENCES security_schema.role_elements(element_id) ON DELETE SET NULL,
    
    -- 约束
    CONSTRAINT valid_role_name CHECK (role_name ~ '^[a-zA-Z][a-zA-Z0-9_-]{1,63}$'),
    CONSTRAINT valid_role_level CHECK (role_level >= 0),
    CONSTRAINT no_role_self_reference CHECK (element_id != parent_role_id)
);

-- =====================================================
-- 3. 权限Element表 - PermissionElement的具体实现
-- =====================================================
CREATE TABLE IF NOT EXISTS security_schema.permission_elements (
    element_id TEXT PRIMARY KEY,                   -- 继承自core_schema.element_metadata
    
    -- 权限基本信息
    permission_name TEXT UNIQUE NOT NULL,          -- 权限名称
    display_name TEXT NOT NULL,                    -- 显示名称
    description TEXT,                              -- 权限描述
    
    -- 权限定义
    resource_type TEXT NOT NULL,                   -- 资源类型
    action TEXT NOT NULL,                          -- 操作类型
    resource_pattern TEXT DEFAULT '*',             -- 资源模式匹配
    
    -- 权限配置
    permission_config JSONB DEFAULT '{
        "requires_ownership": false,
        "requires_approval": false,
        "context_sensitive": false,
        "time_based": false
    }',
    
    -- 条件和约束
    conditions JSONB DEFAULT '[]',                 -- 权限条件
    constraints JSONB DEFAULT '{}',                -- 权限约束
    
    -- 权限分类
    category TEXT DEFAULT 'functional',            -- functional, administrative, system
    priority INTEGER DEFAULT 0,                    -- 权限优先级
    
    -- 状态信息
    is_dangerous BOOLEAN DEFAULT false,            -- 是否为危险权限
    is_system BOOLEAN DEFAULT false,               -- 是否为系统权限
    is_active BOOLEAN DEFAULT true,
    
    -- 使用统计
    usage_count INTEGER DEFAULT 0,                 -- 使用次数
    last_used TIMESTAMP WITH TIME ZONE,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
    
    -- 约束
    CONSTRAINT valid_permission_name CHECK (permission_name ~ '^[a-zA-Z][a-zA-Z0-9_:-]{1,127}$'),
    CONSTRAINT valid_resource_type CHECK (resource_type ~ '^[a-zA-Z][a-zA-Z0-9_]*$'),
    CONSTRAINT valid_action CHECK (action ~ '^[a-zA-Z][a-zA-Z0-9_]*$'),
    CONSTRAINT valid_category CHECK (category IN ('functional', 'administrative', 'system')),
    CONSTRAINT valid_priority CHECK (priority >= 0 AND priority <= 100)
);

-- =====================================================
-- 4. 用户会话表 - 会话管理
-- =====================================================
CREATE TABLE IF NOT EXISTS security_schema.user_sessions (
    session_id TEXT PRIMARY KEY,                   -- 会话ID
    user_element_id TEXT NOT NULL,                 -- 用户Element ID
    
    -- Token信息
    access_token_hash TEXT,                        -- 访问Token哈希
    refresh_token_hash TEXT,                       -- 刷新Token哈希
    token_type TEXT DEFAULT 'bearer',              -- Token类型
    
    -- 会话信息
    session_data JSONB DEFAULT '{}',               -- 会话数据
    
    -- 客户端信息
    ip_address INET,                               -- 客户端IP
    user_agent TEXT,                               -- 用户代理
    device_fingerprint TEXT,                       -- 设备指纹
    platform TEXT,                                -- 平台信息
    
    -- 地理位置信息
    location_info JSONB DEFAULT '{}',              -- 位置信息
    
    -- 时间管理
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- 状态
    is_active BOOLEAN DEFAULT true,
    is_suspicious BOOLEAN DEFAULT false,           -- 可疑会话标志
    
    FOREIGN KEY (user_element_id) REFERENCES security_schema.user_elements(element_id) ON DELETE CASCADE,
    
    -- 约束
    CONSTRAINT valid_expires_at CHECK (expires_at > created_at),
    CONSTRAINT valid_session_id CHECK (session_id ~ '^sess_[a-zA-Z0-9_-]+$')
);

-- =====================================================
-- 5. 用户角色关系表 - 用户-角色多对多关系
-- =====================================================
CREATE TABLE IF NOT EXISTS security_schema.user_role_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_element_id TEXT NOT NULL,                 -- 用户Element ID
    role_element_id TEXT NOT NULL,                 -- 角色Element ID
    
    -- 授权信息
    granted_by TEXT,                               -- 授权者
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,           -- 角色过期时间
    
    -- 关系配置
    relationship_config JSONB DEFAULT '{}',        -- 关系配置
    
    -- 状态
    is_active BOOLEAN DEFAULT true,
    is_primary BOOLEAN DEFAULT false,              -- 是否为主要角色
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_element_id) REFERENCES security_schema.user_elements(element_id) ON DELETE CASCADE,
    FOREIGN KEY (role_element_id) REFERENCES security_schema.role_elements(element_id) ON DELETE CASCADE,
    
    -- 唯一约束
    UNIQUE(user_element_id, role_element_id),
    
    -- 约束
    CONSTRAINT valid_expires_at CHECK (expires_at IS NULL OR expires_at > granted_at)
);

-- =====================================================
-- 6. 角色权限关系表 - 角色-权限多对多关系
-- =====================================================
CREATE TABLE IF NOT EXISTS security_schema.role_permission_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_element_id TEXT NOT NULL,                 -- 角色Element ID
    permission_element_id TEXT NOT NULL,           -- 权限Element ID
    
    -- 授权信息
    granted_by TEXT,                               -- 授权者
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 权限配置
    permission_config JSONB DEFAULT '{}',          -- 权限配置覆盖
    
    -- 状态
    is_active BOOLEAN DEFAULT true,
    is_inherited BOOLEAN DEFAULT false,            -- 是否继承自父角色
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (role_element_id) REFERENCES security_schema.role_elements(element_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_element_id) REFERENCES security_schema.permission_elements(element_id) ON DELETE CASCADE,
    
    -- 唯一约束
    UNIQUE(role_element_id, permission_element_id)
);

-- =====================================================
-- 7. 安全审计日志表 - 安全相关操作审计
-- =====================================================
CREATE TABLE IF NOT EXISTS security_schema.security_audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_id TEXT UNIQUE NOT NULL,                   -- 日志ID
    
    -- 操作主体
    user_element_id TEXT,                          -- 操作用户
    session_id TEXT,                               -- 会话ID
    
    -- 操作信息
    action_type TEXT NOT NULL,                     -- 操作类型
    target_type TEXT,                              -- 目标类型
    target_id TEXT,                                -- 目标ID
    operation_result TEXT NOT NULL CHECK (operation_result IN ('success', 'failure', 'error', 'blocked')),
    
    -- 详细信息
    details JSONB DEFAULT '{}',                    -- 操作详情
    risk_score INTEGER DEFAULT 0,                  -- 风险评分 (0-100)
    
    -- 客户端信息
    ip_address INET,
    user_agent TEXT,
    
    -- 时间戳
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_element_id) REFERENCES security_schema.user_elements(element_id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES security_schema.user_sessions(session_id) ON DELETE SET NULL,
    
    -- 约束
    CONSTRAINT valid_risk_score CHECK (risk_score >= 0 AND risk_score <= 100),
    CONSTRAINT valid_log_id CHECK (log_id ~ '^audit_[a-zA-Z0-9_-]+$')
);

-- =====================================================
-- 索引创建
-- =====================================================

-- user_elements表索引
CREATE INDEX IF NOT EXISTS idx_user_elements_username ON security_schema.user_elements(username);
CREATE INDEX IF NOT EXISTS idx_user_elements_email ON security_schema.user_elements(email);
CREATE INDEX IF NOT EXISTS idx_user_elements_active ON security_schema.user_elements(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_elements_last_login ON security_schema.user_elements(last_login) WHERE last_login IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_user_elements_profile ON security_schema.user_elements USING GIN(profile);
CREATE INDEX IF NOT EXISTS idx_user_elements_locked ON security_schema.user_elements(locked_until) WHERE locked_until IS NOT NULL;

-- role_elements表索引
CREATE INDEX IF NOT EXISTS idx_role_elements_name ON security_schema.role_elements(role_name);
CREATE INDEX IF NOT EXISTS idx_role_elements_active ON security_schema.role_elements(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_role_elements_builtin ON security_schema.role_elements(is_builtin);
CREATE INDEX IF NOT EXISTS idx_role_elements_parent ON security_schema.role_elements(parent_role_id) WHERE parent_role_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_role_elements_level ON security_schema.role_elements(role_level);

-- permission_elements表索引
CREATE INDEX IF NOT EXISTS idx_permission_elements_name ON security_schema.permission_elements(permission_name);
CREATE INDEX IF NOT EXISTS idx_permission_elements_resource_action ON security_schema.permission_elements(resource_type, action);
CREATE INDEX IF NOT EXISTS idx_permission_elements_category ON security_schema.permission_elements(category);
CREATE INDEX IF NOT EXISTS idx_permission_elements_active ON security_schema.permission_elements(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_permission_elements_dangerous ON security_schema.permission_elements(is_dangerous) WHERE is_dangerous = true;

-- user_sessions表索引
CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON security_schema.user_sessions(user_element_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON security_schema.user_sessions(is_active, expires_at) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON security_schema.user_sessions(access_token_hash) WHERE access_token_hash IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_user_sessions_ip ON security_schema.user_sessions(ip_address);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON security_schema.user_sessions(expires_at);

-- 关系表索引
CREATE INDEX IF NOT EXISTS idx_user_role_relationships_user ON security_schema.user_role_relationships(user_element_id, is_active);
CREATE INDEX IF NOT EXISTS idx_user_role_relationships_role ON security_schema.user_role_relationships(role_element_id, is_active);
CREATE INDEX IF NOT EXISTS idx_role_permission_relationships_role ON security_schema.role_permission_relationships(role_element_id, is_active);
CREATE INDEX IF NOT EXISTS idx_role_permission_relationships_permission ON security_schema.role_permission_relationships(permission_element_id, is_active);

-- 审计日志索引
CREATE INDEX IF NOT EXISTS idx_security_audit_user ON security_schema.security_audit_logs(user_element_id, timestamp);
CREATE INDEX IF NOT EXISTS idx_security_audit_action ON security_schema.security_audit_logs(action_type, timestamp);
CREATE INDEX IF NOT EXISTS idx_security_audit_result ON security_schema.security_audit_logs(operation_result, timestamp);
CREATE INDEX IF NOT EXISTS idx_security_audit_risk ON security_schema.security_audit_logs(risk_score) WHERE risk_score > 50;
CREATE INDEX IF NOT EXISTS idx_security_audit_timestamp ON security_schema.security_audit_logs(timestamp);

-- =====================================================
-- 触发器创建
-- =====================================================

-- 自动更新updated_at字段的触发器
CREATE TRIGGER trigger_user_elements_updated_at 
    BEFORE UPDATE ON security_schema.user_elements 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_role_elements_updated_at 
    BEFORE UPDATE ON security_schema.role_elements 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_permission_elements_updated_at 
    BEFORE UPDATE ON security_schema.permission_elements 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_user_role_relationships_updated_at 
    BEFORE UPDATE ON security_schema.user_role_relationships 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_role_permission_relationships_updated_at 
    BEFORE UPDATE ON security_schema.role_permission_relationships 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

-- 角色层级路径自动生成触发器
CREATE OR REPLACE FUNCTION security_schema.generate_role_hierarchy_path()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.parent_role_id IS NOT NULL THEN
        -- 获取父角色的层级路径和深度
        SELECT 
            COALESCE(role_hierarchy_path, '/') || NEW.element_id || '/',
            role_level + 1
        INTO NEW.role_hierarchy_path, NEW.role_level
        FROM security_schema.role_elements 
        WHERE element_id = NEW.parent_role_id;
    ELSE
        NEW.role_hierarchy_path = '/' || NEW.element_id || '/';
        NEW.role_level = 0;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_role_hierarchy_path 
    BEFORE INSERT OR UPDATE ON security_schema.role_elements 
    FOR EACH ROW EXECUTE FUNCTION security_schema.generate_role_hierarchy_path();

-- 用户统计更新触发器
CREATE OR REPLACE FUNCTION security_schema.update_role_user_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW.is_active = true THEN
        UPDATE security_schema.role_elements 
        SET user_count = user_count + 1 
        WHERE element_id = NEW.role_element_id;
    ELSIF TG_OP = 'DELETE' AND OLD.is_active = true THEN
        UPDATE security_schema.role_elements 
        SET user_count = user_count - 1 
        WHERE element_id = OLD.role_element_id;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.is_active = true AND NEW.is_active = false THEN
            UPDATE security_schema.role_elements 
            SET user_count = user_count - 1 
            WHERE element_id = NEW.role_element_id;
        ELSIF OLD.is_active = false AND NEW.is_active = true THEN
            UPDATE security_schema.role_elements 
            SET user_count = user_count + 1 
            WHERE element_id = NEW.role_element_id;
        END IF;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_role_user_count 
    AFTER INSERT OR UPDATE OR DELETE ON security_schema.user_role_relationships 
    FOR EACH ROW EXECUTE FUNCTION security_schema.update_role_user_count();

-- =====================================================
-- 权限设置
-- =====================================================

-- 创建安全Schema的角色
CREATE ROLE IF NOT EXISTS security_schema_user;
CREATE ROLE IF NOT EXISTS security_schema_admin;
CREATE ROLE IF NOT EXISTS security_schema_reader;

-- 授予Schema访问权限
GRANT USAGE ON SCHEMA security_schema TO security_schema_user, security_schema_admin, security_schema_reader;

-- 授予表权限
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA security_schema TO security_schema_user;
GRANT DELETE ON security_schema.user_sessions, security_schema.security_audit_logs TO security_schema_user;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA security_schema TO security_schema_admin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA security_schema TO security_schema_admin;

GRANT SELECT ON ALL TABLES IN SCHEMA security_schema TO security_schema_reader;

-- 行级安全策略
ALTER TABLE security_schema.user_elements ENABLE ROW LEVEL SECURITY;

CREATE POLICY user_elements_isolation ON security_schema.user_elements
    FOR ALL TO security_schema_user
    USING (element_id = current_setting('app.current_user_id', true));

-- 默认权限设置
ALTER DEFAULT PRIVILEGES IN SCHEMA security_schema GRANT SELECT, INSERT, UPDATE ON TABLES TO security_schema_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA security_schema GRANT ALL ON TABLES TO security_schema_admin;
ALTER DEFAULT PRIVILEGES IN SCHEMA security_schema GRANT SELECT ON TABLES TO security_schema_reader;

-- =====================================================
-- 视图创建
-- =====================================================

-- 用户概览视图
CREATE OR REPLACE VIEW security_schema.v_user_overview AS
SELECT 
    ue.element_id,
    ue.username,
    ue.email,
    ue.profile->>'display_name' as display_name,
    ue.profile->>'department' as department,
    ue.is_active,
    ue.is_verified,
    ue.last_login,
    ue.login_count,
    ue.created_at,
    array_agg(re.role_name) FILTER (WHERE urr.is_active = true) as roles
FROM security_schema.user_elements ue
LEFT JOIN security_schema.user_role_relationships urr ON ue.element_id = urr.user_element_id
LEFT JOIN security_schema.role_elements re ON urr.role_element_id = re.element_id
WHERE ue.is_active = true
GROUP BY ue.element_id, ue.username, ue.email, ue.profile, ue.is_active, ue.is_verified, 
         ue.last_login, ue.login_count, ue.created_at;

-- 角色权限视图
CREATE OR REPLACE VIEW security_schema.v_role_permissions AS
SELECT 
    re.element_id as role_id,
    re.role_name,
    re.display_name as role_display_name,
    array_agg(pe.permission_name) FILTER (WHERE rpr.is_active = true) as permissions,
    array_agg(pe.resource_type || ':' || pe.action) FILTER (WHERE rpr.is_active = true) as permission_patterns
FROM security_schema.role_elements re
LEFT JOIN security_schema.role_permission_relationships rpr ON re.element_id = rpr.role_element_id
LEFT JOIN security_schema.permission_elements pe ON rpr.permission_element_id = pe.element_id
WHERE re.is_active = true
GROUP BY re.element_id, re.role_name, re.display_name;

-- 用户权限视图 (通过角色继承)
CREATE OR REPLACE VIEW security_schema.v_user_permissions AS
WITH user_roles AS (
    SELECT ue.element_id as user_id, ue.username, re.element_id as role_id, re.role_name
    FROM security_schema.user_elements ue
    JOIN security_schema.user_role_relationships urr ON ue.element_id = urr.user_element_id
    JOIN security_schema.role_elements re ON urr.role_element_id = re.element_id
    WHERE ue.is_active = true AND urr.is_active = true AND re.is_active = true
),
role_permissions AS (
    SELECT ur.user_id, ur.username, pe.permission_name, pe.resource_type, pe.action
    FROM user_roles ur
    JOIN security_schema.role_permission_relationships rpr ON ur.role_id = rpr.role_element_id
    JOIN security_schema.permission_elements pe ON rpr.permission_element_id = pe.element_id
    WHERE rpr.is_active = true AND pe.is_active = true
)
SELECT 
    user_id,
    username,
    array_agg(DISTINCT permission_name) as permissions,
    array_agg(DISTINCT resource_type || ':' || action) as permission_patterns
FROM role_permissions
GROUP BY user_id, username;

-- 活跃会话视图
CREATE OR REPLACE VIEW security_schema.v_active_sessions AS
SELECT 
    us.session_id,
    us.user_element_id,
    ue.username,
    us.ip_address,
    us.platform,
    us.created_at,
    us.last_active_at,
    us.expires_at,
    EXTRACT(EPOCH FROM (us.expires_at - CURRENT_TIMESTAMP))::INTEGER as seconds_until_expiry
FROM security_schema.user_sessions us
JOIN security_schema.user_elements ue ON us.user_element_id = ue.element_id
WHERE us.is_active = true 
  AND us.expires_at > CURRENT_TIMESTAMP
ORDER BY us.last_active_at DESC;

-- =====================================================
-- 初始数据插入
-- =====================================================

-- 首先在core_schema中注册安全领域的Element
INSERT INTO core_schema.element_metadata (
    element_id, domain_schema, element_type, tag, local_name,
    status, semantic_tags, domain_category, created_by
) VALUES 
    ('role_system_admin', 'security_schema', 'role_element', 'security:role', 'system_admin',
     'active', '["security:role", "level:admin", "scope:system", "builtin:true"]', 'access_control', 'system'),
    ('role_user_default', 'security_schema', 'role_element', 'security:role', 'user_default',
     'active', '["security:role", "level:user", "scope:basic", "builtin:true"]', 'access_control', 'system'),
    ('perm_user_read', 'security_schema', 'permission_element', 'security:permission', 'user_read',
     'active', '["security:permission", "action:read", "resource:user"]', 'access_control', 'system'),
    ('perm_user_write', 'security_schema', 'permission_element', 'security:permission', 'user_write',
     'active', '["security:permission", "action:write", "resource:user"]', 'access_control', 'system'),
    ('perm_admin_all', 'security_schema', 'permission_element', 'security:permission', 'admin_all',
     'active', '["security:permission", "action:all", "resource:all", "level:admin"]', 'access_control', 'system');

-- 创建默认角色
INSERT INTO security_schema.role_elements (
    element_id, role_name, display_name, description,
    role_config, is_builtin, is_active
) VALUES 
    ('role_system_admin', 'system_admin', '系统管理员', '拥有系统完全访问权限的管理员角色',
     '{"assignable": false, "visible": true, "system_role": true}', true, true),
    ('role_user_default', 'user_default', '默认用户', '新用户的默认角色，拥有基础权限',
     '{"assignable": true, "visible": true, "system_role": false, "auto_assign_conditions": ["new_user"]}', true, true);

-- 创建基础权限
INSERT INTO security_schema.permission_elements (
    element_id, permission_name, display_name, description,
    resource_type, action, category, is_system
) VALUES 
    ('perm_user_read', 'user:read', '查看用户信息', '允许查看用户基本信息',
     'user', 'read', 'functional', false),
    ('perm_user_write', 'user:write', '修改用户信息', '允许修改用户基本信息',
     'user', 'write', 'functional', false),
    ('perm_admin_all', 'admin:all', '系统管理权限', '拥有系统所有操作权限',
     'all', 'all', 'administrative', true);

-- 建立角色-权限关系
INSERT INTO security_schema.role_permission_relationships (
    role_element_id, permission_element_id, granted_by, is_active
) VALUES 
    ('role_user_default', 'perm_user_read', 'system', true),
    ('role_user_default', 'perm_user_write', 'system', true),
    ('role_system_admin', 'perm_admin_all', 'system', true);

-- 完成提示
SELECT 'Security Schema 初始化完成！支持基于Element的用户、角色、权限管理。' as message; 