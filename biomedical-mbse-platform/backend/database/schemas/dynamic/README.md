# Dynamic Schema 动态Schema实现

UML混合策略数据库映射系统的动态Schema生成和管理核心实现。

## 📁 文件结构

```
schemas/dynamic/
├── README.md                       # 本文档 - 动态Schema说明
├── schema_version_manager.py       # Schema版本管理器 (32KB, 812行)
├── uml25_schema_generator.py       # UML2.5 Schema生成器 (42KB, 1008行)
├── dynamic_schema_generator.py     # 动态Schema生成器 (24KB, 592行)
└── dynamic_domain_example.py       # 动态领域使用示例 (20KB, 460行)
```

## 🎯 核心功能

### 1. 📋 Schema版本管理器 (`schema_version_manager.py`)

**核心功能**:
- **版本跟踪**: 完整的Schema版本历史记录
- **自动迁移**: 支持无缝的Schema版本升级
- **回滚支持**: 安全的版本回滚机制
- **数据完整性**: 迁移过程中的数据完整性验证
- **备份恢复**: 自动备份和恢复机制

**主要API**:
```python
class SchemaVersionManager:
    async def initialize_version_tracking(self)
    async def get_current_version(self) -> str
    async def migrate_to_version(self, target_version: str) -> Dict[str, Any]
    async def validate_schema_integrity(self) -> Dict[str, Any]
    async def create_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]
```

**使用场景**:
- 生产环境Schema升级
- 开发环境数据库同步
- 数据迁移项目
- 灾难恢复场景

### 2. 🏗️ UML2.5 Schema生成器 (`uml25_schema_generator.py`)

**核心功能**:
- **XMI解析**: 从UML2.5 XMI文件生成数据库Schema
- **元类映射**: 将UML元类转换为PostgreSQL表结构
- **继承支持**: 完整的UML继承关系映射
- **关联处理**: 自动生成关联表和外键约束
- **扩展支持**: 支持UML Profile和Stereotype

**主要API**:
```python
class UML25SchemaGenerator:
    async def generate_schema_from_xmi(self, xmi_result: Dict[str, Any]) -> Dict[str, Any]
    async def _generate_core_tables(self)
    async def _generate_metaclass_tables(self)
    async def _generate_association_tables(self)
    async def _create_inheritance_structure(self)
```

**支持的UML元素**:
- **类和接口**: Class, Interface, DataType, Enumeration
- **关系**: Association, Aggregation, Composition, Generalization
- **包结构**: Package, Model, Profile
- **行为元素**: Operation, Property, Parameter
- **约束**: Constraint, Stereotype应用

### 3. ⚡ 动态Schema生成器 (`dynamic_schema_generator.py`)

**核心功能**:
- **运行时创建**: 运行时动态创建数据库表结构
- **字段类型**: 支持多种字段类型和约束
- **索引管理**: 自动创建和优化索引
- **关系处理**: 动态创建关系表和外键
- **触发器**: 自动生成数据完整性触发器

**主要API**:
```python
class DynamicSchemaGenerator:
    async def create_domain_schema(self, domain_name: str, schema_name: str, 
                                  element_types: List[Dict[str, Any]]) -> Tuple[bool, str]
    async def add_element_type(self, domain_name: str, element_type_def: Dict[str, Any]) -> Tuple[bool, str]
```

**支持的字段类型**:
- **基础类型**: STRING, INTEGER, BOOLEAN, TIMESTAMP, DATE, DECIMAL
- **高级类型**: JSONB, TEXT, UUID, BIGINT
- **约束支持**: NOT NULL, UNIQUE, DEFAULT, CHECK约束
- **索引类型**: BTREE, GIN, GIST, HASH索引

### 4. 🚀 动态领域示例 (`dynamic_domain_example.py`)

**演示功能**:
- **领域创建**: 完整的领域创建流程演示
- **元素类型**: 动态添加自定义Element类型
- **跨域关系**: 建立和管理跨领域关系
- **性能优化**: 性能监控和优化示例
- **健康监控**: 系统健康状态监控

**示例场景**:
- **实验室管理**: 实验室、实验、样本管理领域
- **数据分析**: 基于模板的数据分析领域
- **跨域联动**: 多领域协同工作示例

## 🛠️ 使用指南

### 快速开始

#### 1. 初始化版本管理
```python
import asyncio
from schemas.dynamic.schema_version_manager import SchemaVersionManager

async def init_version_management():
    manager = SchemaVersionManager(db_pool)
    await manager.initialize_version_tracking()
    current_version = await manager.get_current_version()
    print(f"当前Schema版本: {current_version}")
```

#### 2. 从XMI生成Schema
```python
from schemas.dynamic.uml25_schema_generator import UML25SchemaGenerator

async def generate_from_xmi():
    generator = UML25SchemaGenerator(db_pool)
    
    # XMI解析结果
    xmi_result = {...}  # 从XMI文件解析的结果
    
    result = await generator.generate_schema_from_xmi(xmi_result)
    if result['success']:
        print(f"Schema生成成功: {result['stats']['tables_generated']} 个表")
    else:
        print(f"Schema生成失败: {result['errors']}")
```

#### 3. 动态创建领域
```python
from schemas.dynamic.dynamic_schema_generator import DynamicSchemaGenerator

async def create_dynamic_domain():
    generator = DynamicSchemaGenerator(db_pool)
    
    # 定义Element类型
    element_types = [
        {
            'type_id': 'user_element',
            'type_name': '用户Element',
            'table_name': 'users',
            'field_definitions': {
                'username': {'type': 'string', 'required': True, 'unique': True},
                'email': {'type': 'string', 'required': True, 'unique': True},
                'created_at': {'type': 'timestamp', 'default': 'now()'}
            }
        }
    ]
    
    success, message = await generator.create_domain_schema(
        'user_management', 'user_mgmt', element_types
    )
    print(f"领域创建: {message}")
```

#### 4. 运行完整演示
```python
from schemas.dynamic.dynamic_domain_example import DynamicDomainDemo

async def run_demo():
    demo = DynamicDomainDemo(db_config)
    await demo.initialize()
    await demo.run_complete_demo()
```

### 高级功能

#### Schema版本迁移
```python
# 升级到指定版本
result = await manager.migrate_to_version("2.0.0")

# 验证Schema完整性
integrity_report = await manager.validate_schema_integrity()

# 创建备份
backup_result = await manager.create_backup("before_migration")
```

#### 自定义元类映射
```python
# 扩展类型映射
generator.type_mappings.update({
    'CustomType': 'JSONB',
    'GeoLocation': 'GEOMETRY'
})

# 自定义表生成逻辑
class CustomSchemaGenerator(UML25SchemaGenerator):
    async def _generate_custom_metaclass_table(self, metaclass_data):
        # 自定义逻辑
        pass
```

#### 复杂关系定义
```python
element_types = [
    {
        'type_id': 'project',
        'relationship_definitions': {
            'members': {
                'type': 'many_to_many',
                'target': 'user_element',
                'through_table': 'project_members',
                'additional_fields': {
                    'role': {'type': 'string'},
                    'joined_at': {'type': 'timestamp'}
                }
            }
        }
    }
]
```

## 📊 性能特征

### 生成性能
- **小型Schema** (< 50个表): < 30秒
- **中型Schema** (50-200个表): < 2分钟
- **大型Schema** (> 200个表): < 5分钟
- **内存使用**: 每个表约1MB内存

### 迁移性能
- **简单迁移**: < 10秒
- **复杂迁移**: < 5分钟
- **大数据量迁移**: 支持分批处理
- **回滚时间**: < 1分钟

### 动态创建性能
- **单表创建**: < 1秒
- **批量创建**: 10个表 < 5秒
- **索引创建**: 每个索引 < 2秒
- **关系表**: 每个关系 < 3秒

## 🔧 配置选项

### 版本管理配置
```python
VERSION_CONFIG = {
    'backup_retention_days': 30,
    'migration_timeout': 300,
    'integrity_check_level': 'full',
    'auto_backup': True
}
```

### Schema生成配置
```python
GENERATION_CONFIG = {
    'default_schema': 'uml25_base',
    'table_prefix': '',
    'create_indexes': True,
    'validate_constraints': True,
    'enable_inheritance': True
}
```

### 动态创建配置
```python
DYNAMIC_CONFIG = {
    'auto_optimize': True,
    'create_triggers': True,
    'enable_audit': True,
    'default_permissions': 'read_write'
}
```

## ⚠️ 注意事项

### 生产环境部署
1. **测试验证**: 在测试环境充分验证后再部署
2. **数据备份**: 迁移前必须创建完整备份
3. **权限管理**: 严格控制Schema修改权限
4. **监控告警**: 配置Schema变更监控

### 性能优化
1. **批量操作**: 大量表创建时使用批量模式
2. **索引策略**: 合理设计索引避免性能问题
3. **并发控制**: 避免并发的Schema修改操作
4. **资源管理**: 监控内存和磁盘使用

### 故障恢复
1. **迁移失败**: 自动回滚到上一个稳定版本
2. **数据损坏**: 从备份恢复数据
3. **性能下降**: 重新构建索引和统计信息
4. **完整性问题**: 运行完整性检查和修复

## 🔗 相关文档

- [SQL Schemas](../README.md) - 数据库架构总览
- [核心Schema](../00_core_schema.sql) - 基础表结构
- [UML文档](../../docs/uml/) - UML技术实现
- [领域管理器](../../docs/domain_managers_docs/) - 领域管理组件

---

**⚡ 这里是UML混合策略系统的动态Schema核心实现，提供了完整的运行时数据库结构管理能力！** 