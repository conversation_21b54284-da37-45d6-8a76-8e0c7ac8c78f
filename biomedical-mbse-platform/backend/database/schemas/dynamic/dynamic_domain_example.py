"""
动态领域扩展完整示例

展示如何使用动态架构：
1. 创建新的业务领域
2. 动态添加Element类型
3. 建立跨域关系
4. 优化性能
5. 监控健康状态
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any

import asyncpg

from schemas.dynamic.dynamic_schema_generator import DynamicSchemaGenerator
from domain_managers.core_domain_manager import CoreDomainManager
from domain_managers.domain_factory import (
    DomainFactory, DomainCreationRequest, DomainType
)
from domain_managers.cross_domain_indexer import (
    CrossDomainIndexer, IndexStrategy, CrossDomainRelationType
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DynamicDomainDemo:
    """动态领域扩展演示"""
    
    def __init__(self, db_config: Dict[str, Any]):
        self.db_config = db_config
        self.db_pool = None
        self.core_manager = None
        self.domain_factory = None
        self.schema_generator = None
    
    async def initialize(self):
        """初始化数据库连接和管理器"""
        self.db_pool = await asyncpg.create_pool(**self.db_config)
        self.core_manager = CoreDomainManager(self.db_pool)
        self.domain_factory = DomainFactory(self.db_pool, self.core_manager)
        self.schema_generator = DynamicSchemaGenerator(self.db_pool)
        
        logger.info("动态领域系统初始化完成")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        try:
            logger.info("=== 开始动态领域扩展演示 ===")
            
            # 1. 创建实验室管理领域
            await self.demo_create_lab_management_domain()
            
            # 2. 创建数据分析领域（使用模板）
            await self.demo_create_data_analytics_domain()
            
            # 3. 动态添加新的Element类型
            await self.demo_add_custom_element_types()
            
            # 4. 展示跨域联动
            await self.demo_cross_domain_interactions()
            
            # 5. 性能优化演示
            await self.demo_performance_optimization()
            
            # 6. 健康监控演示
            await self.demo_health_monitoring()
            
            logger.info("=== 动态领域扩展演示完成 ===")
            
        except Exception as e:
            logger.error(f"演示过程中发生错误: {e}")
            raise
    
    async def demo_create_lab_management_domain(self):
        """演示：创建实验室管理领域"""
        logger.info("--- 创建实验室管理领域 ---")
        
        # 定义实验室领域的Element类型
        lab_element_types = [
            {
                'type_id': 'laboratory_element',
                'type_name': '实验室Element',
                'table_name': 'laboratory_elements',
                'field_definitions': {
                    'lab_name': {'type': 'string', 'required': True, 'indexed': True},
                    'location': {'type': 'string', 'required': True},
                    'capacity': {'type': 'integer', 'default': 10},
                    'equipment_list': {'type': 'jsonb', 'default': '[]'},
                    'safety_level': {'type': 'string', 'default': 'BSL-1'},
                    'is_operational': {'type': 'boolean', 'default': True}
                },
                'index_definitions': [
                    {'fields': ['lab_name', 'location'], 'type': 'btree'},
                    {'fields': ['safety_level'], 'type': 'btree'},
                    {'fields': ['equipment_list'], 'type': 'gin'}
                ],
                'cross_domain_refs': ['security_refs', 'modeling_refs']
            },
            {
                'type_id': 'experiment_element',
                'type_name': '实验Element',
                'table_name': 'experiment_elements',
                'field_definitions': {
                    'experiment_name': {'type': 'string', 'required': True, 'indexed': True},
                    'protocol': {'type': 'text', 'required': True},
                    'start_date': {'type': 'timestamp'},
                    'end_date': {'type': 'timestamp'},
                    'status': {'type': 'string', 'default': 'planned'},
                    'results': {'type': 'jsonb', 'default': '{}'},
                    'lab_id': {'type': 'string'}  # 关联到实验室
                },
                'relationship_definitions': {
                    'laboratory': {'type': 'many_to_one', 'target': 'laboratory_element'}
                },
                'cross_domain_refs': ['security_refs', 'biomedical_refs']
            },
            {
                'type_id': 'sample_element',
                'type_name': '样本Element',
                'table_name': 'sample_elements',
                'field_definitions': {
                    'sample_id': {'type': 'string', 'required': True, 'unique': True, 'indexed': True},
                    'sample_type': {'type': 'string', 'required': True},
                    'source_organism': {'type': 'string'},
                    'collection_date': {'type': 'timestamp', 'required': True},
                    'storage_conditions': {'type': 'jsonb', 'default': '{}'},
                    'metadata': {'type': 'jsonb', 'default': '{}'},
                    'experiment_id': {'type': 'string'}  # 关联到实验
                },
                'relationship_definitions': {
                    'experiment': {'type': 'many_to_one', 'target': 'experiment_element'}
                },
                'cross_domain_refs': ['biomedical_refs']
            }
        ]
        
        # 创建领域请求
        request = DomainCreationRequest(
            domain_name='laboratory',
            display_name='实验室管理领域',
            description='用于管理实验室、实验和样本的领域',
            domain_type=DomainType.DOMAIN_SPECIFIC,
            custom_element_types=lab_element_types,
            cross_domain_connections=['security', 'biomedical', 'modeling'],
            auto_optimize=True,
            namespace_prefix='LAB'
        )
        
        # 创建领域
        result = await self.domain_factory.create_domain(request)
        
        if result.success:
            logger.info(f"✅ 实验室管理领域创建成功！")
            logger.info(f"   - Schema: {result.schema_name}")
            logger.info(f"   - 创建的表: {len(result.created_tables)} 个")
            logger.info(f"   - 创建的索引: {len(result.created_indexes)} 个")
            if result.warnings:
                logger.warning(f"   - 警告: {result.warnings}")
        else:
            logger.error(f"❌ 实验室管理领域创建失败: {result.errors}")
            
        return result.success
    
    async def demo_create_data_analytics_domain(self):
        """演示：使用模板创建数据分析领域"""
        logger.info("--- 使用模板创建数据分析领域 ---")
        
        # 查看可用模板
        templates = self.domain_factory.get_available_templates()
        logger.info(f"可用模板: {[t.template_name for t in templates]}")
        
        # 使用数据分析模板
        request = DomainCreationRequest(
            domain_name='analytics',
            display_name='数据分析领域',
            description='基于模板的数据分析领域',
            domain_type=DomainType.TECHNICAL,
            template_id='data_analytics',
            cross_domain_connections=['security', 'laboratory'],
            auto_optimize=True
        )
        
        result = await self.domain_factory.create_domain(request)
        
        if result.success:
            logger.info(f"✅ 数据分析领域创建成功（基于模板）！")
            logger.info(f"   - 创建的表: {result.created_tables}")
            logger.info(f"   - 性能基线: {result.performance_baseline}")
        else:
            logger.error(f"❌ 数据分析领域创建失败: {result.errors}")
            
        return result.success
    
    async def demo_add_custom_element_types(self):
        """演示：向现有领域动态添加Element类型"""
        logger.info("--- 动态添加Element类型 ---")
        
        # 向实验室领域添加设备管理Element
        equipment_element = {
            'type_id': 'equipment_element',
            'type_name': '设备Element',
            'table_name': 'equipment_elements',
            'field_definitions': {
                'equipment_name': {'type': 'string', 'required': True, 'indexed': True},
                'equipment_type': {'type': 'string', 'required': True},
                'manufacturer': {'type': 'string'},
                'model': {'type': 'string'},
                'serial_number': {'type': 'string', 'unique': True},
                'purchase_date': {'type': 'date'},
                'maintenance_schedule': {'type': 'jsonb', 'default': '{}'},
                'status': {'type': 'string', 'default': 'operational'},
                'lab_id': {'type': 'string'}  # 关联到实验室
            },
            'relationship_definitions': {
                'laboratory': {'type': 'many_to_one', 'target': 'laboratory_element'}
            },
            'cross_domain_refs': ['modeling_refs']  # 可能关联到建模领域的组件
        }
        
        success, message = await self.domain_factory.add_element_type_to_domain(
            'laboratory', equipment_element
        )
        
        if success:
            logger.info(f"✅ 设备Element类型添加成功: {message}")
        else:
            logger.error(f"❌ 设备Element类型添加失败: {message}")
        
        # 向数据分析领域添加机器学习模型Element
        ml_model_element = {
            'type_id': 'ml_model_element',
            'type_name': '机器学习模型Element',
            'table_name': 'ml_model_elements',
            'field_definitions': {
                'model_name': {'type': 'string', 'required': True, 'indexed': True},
                'algorithm_type': {'type': 'string', 'required': True},
                'hyperparameters': {'type': 'jsonb', 'default': '{}'},
                'training_data_refs': {'type': 'jsonb', 'default': '[]'},
                'performance_metrics': {'type': 'jsonb', 'default': '{}'},
                'model_version': {'type': 'string', 'default': '1.0'},
                'is_deployed': {'type': 'boolean', 'default': False}
            },
            'cross_domain_refs': ['laboratory_refs', 'biomedical_refs']
        }
        
        success, message = await self.domain_factory.add_element_type_to_domain(
            'analytics', ml_model_element
        )
        
        if success:
            logger.info(f"✅ 机器学习模型Element类型添加成功: {message}")
        else:
            logger.error(f"❌ 机器学习模型Element类型添加失败: {message}")
        
        return True
    
    async def demo_cross_domain_interactions(self):
        """演示：跨域联动功能"""
        logger.info("--- 演示跨域联动功能 ---")
        
        indexer = CrossDomainIndexer(self.db_pool)
        
        # 1. 分析跨域关系模式
        patterns = await indexer.analyze_cross_domain_patterns()
        logger.info(f"发现 {len(patterns)} 个跨域关系模式")
        
        for pattern in patterns[:3]:  # 显示前3个模式
            logger.info(f"  模式: {pattern.source_domain} -> {pattern.target_domain}")
            logger.info(f"    关系类型: {pattern.relationship_type.value}")
            logger.info(f"    频率: {pattern.frequency}")
            logger.info(f"    性能影响: {pattern.performance_impact:.2f}")
        
        # 2. 创建跨域索引
        created_indexes = await indexer.create_cross_domain_indexes(
            patterns, IndexStrategy.ADAPTIVE
        )
        logger.info(f"创建了 {len(created_indexes)} 个跨域索引")
        
        # 3. 自动发现潜在关系
        discovered = await indexer.auto_discover_cross_domain_relationships()
        logger.info(f"自动发现 {len(discovered)} 个潜在跨域关系")
        
        for relationship in discovered[:2]:  # 显示前2个发现的关系
            logger.info(f"  发现关系: {relationship}")
        
        # 4. 优化跨域查询
        optimization = await indexer.optimize_cross_domain_queries()
        logger.info(f"查询优化结果:")
        logger.info(f"  分析查询数: {optimization['analyzed_queries']}")
        logger.info(f"  创建索引数: {optimization['created_indexes']}")
        logger.info(f"  优化建议数: {len(optimization['recommendations'])}")
        
        return True
    
    async def demo_performance_optimization(self):
        """演示：性能优化功能"""
        logger.info("--- 演示性能优化功能 ---")
        
        # 优化实验室管理领域
        lab_optimization = await self.domain_factory.optimize_domain_performance('laboratory')
        logger.info(f"实验室领域优化结果:")
        logger.info(f"  应用的优化: {lab_optimization['optimization_applied']}")
        logger.info(f"  建议数: {len(lab_optimization['recommendations'])}")
        
        # 优化数据分析领域
        analytics_optimization = await self.domain_factory.optimize_domain_performance('analytics')
        logger.info(f"数据分析领域优化结果:")
        logger.info(f"  应用的优化: {analytics_optimization['optimization_applied']}")
        logger.info(f"  建议数: {len(analytics_optimization['recommendations'])}")
        
        return True
    
    async def demo_health_monitoring(self):
        """演示：健康监控功能"""
        logger.info("--- 演示健康监控功能 ---")
        
        # 获取实验室领域健康报告
        lab_health = await self.domain_factory.get_domain_health_report('laboratory')
        logger.info(f"实验室领域健康报告:")
        logger.info(f"  状态: {lab_health['status']}")
        logger.info(f"  Element统计: {lab_health['element_statistics']}")
        logger.info(f"  关系统计: {lab_health['relationship_statistics']}")
        logger.info(f"  性能指标: {lab_health['performance_metrics']}")
        logger.info(f"  建议数: {len(lab_health['recommendations'])}")
        logger.info(f"  问题数: {len(lab_health['issues'])}")
        
        # 获取数据分析领域健康报告
        analytics_health = await self.domain_factory.get_domain_health_report('analytics')
        logger.info(f"数据分析领域健康报告:")
        logger.info(f"  状态: {analytics_health['status']}")
        logger.info(f"  Element统计: {analytics_health['element_statistics']}")
        
        return True
    
    async def demo_create_sample_data(self):
        """演示：创建示例数据"""
        logger.info("--- 创建示例数据 ---")
        
        try:
            async with self.db_pool.acquire() as conn:
                # 创建示例实验室Element
                lab_element_id = f"LAB.laboratory.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # 首先在core_schema.element_metadata中注册
                await conn.execute("""
                    INSERT INTO core_schema.element_metadata (
                        element_id, domain_schema, element_type, local_name, 
                        namespace_prefix, semantic_tags, created_by, version
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """, 
                    lab_element_id, 'laboratory_schema', 'laboratory_element',
                    'central_lab', 'LAB', json.dumps(['laboratory', 'research', 'equipment']),
                    'demo_user', '1.0'
                )
                
                # 然后在领域特定表中创建数据
                await conn.execute("""
                    INSERT INTO laboratory_schema.laboratory_elements (
                        element_id, lab_name, location, capacity, 
                        equipment_list, safety_level, is_operational
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                    lab_element_id, '中央实验室', '生物医学楼3层',
                    20, json.dumps(['PCR仪', '显微镜', '离心机']), 'BSL-2', True
                )
                
                logger.info(f"✅ 创建示例实验室Element: {lab_element_id}")
                
                # 创建示例实验Element
                exp_element_id = f"LAB.experiment.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                await conn.execute("""
                    INSERT INTO core_schema.element_metadata (
                        element_id, domain_schema, element_type, local_name,
                        namespace_prefix, semantic_tags, created_by, version
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                    exp_element_id, 'laboratory_schema', 'experiment_element',
                    'protein_analysis_exp', 'LAB', json.dumps(['experiment', 'protein', 'analysis']),
                    'demo_user', '1.0'
                )
                
                await conn.execute("""
                    INSERT INTO laboratory_schema.experiment_elements (
                        element_id, experiment_name, protocol, start_date,
                        status, lab_id
                    ) VALUES ($1, $2, $3, $4, $5, $6)
                """,
                    exp_element_id, '蛋白质分析实验', 
                    '使用质谱仪分析蛋白质结构和功能', 
                    datetime.now(timezone.utc), 'in_progress', lab_element_id
                )
                
                logger.info(f"✅ 创建示例实验Element: {exp_element_id}")
                
                # 创建跨域关系
                await conn.execute("""
                    INSERT INTO core_schema.cross_domain_relationships (
                        relationship_id, source_element_id, target_element_id,
                        source_domain, target_domain, relationship_type,
                        strength, metadata, status
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                    f"rel_{uuid.uuid4().hex[:8]}", exp_element_id, lab_element_id,
                    'laboratory', 'laboratory', 'dependency', 0.8,
                    json.dumps({'relationship': 'experiment_conducted_in_lab'}), 'active'
                )
                
                logger.info("✅ 创建跨域关系")
                
        except Exception as e:
            logger.error(f"创建示例数据失败: {e}")
            return False
        
        return True
    
    async def cleanup(self):
        """清理资源"""
        if self.db_pool:
            await self.db_pool.close()
        logger.info("资源清理完成")

async def main():
    """主函数"""
    # 数据库配置（请根据实际情况修改）
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'biomedical_mbse_platform',
        'user': 'postgres',
        'password': 'your_password'
    }
    
    demo = DynamicDomainDemo(db_config)
    
    try:
        await demo.initialize()
        await demo.run_complete_demo()
        await demo.demo_create_sample_data()
        
    except Exception as e:
        logger.error(f"演示失败: {e}")
        raise
    finally:
        await demo.cleanup()

if __name__ == "__main__":
    # 运行演示
    asyncio.run(main()) 