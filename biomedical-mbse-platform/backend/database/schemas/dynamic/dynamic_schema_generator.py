"""
动态Schema生成器 - 根据Element类型定义动态创建数据库结构

支持：
1. 根据JSON Schema定义自动生成表结构
2. 自动创建索引和约束
3. 生成跨域引用字段
4. 动态触发器创建
5. 关系表自动生成
"""

import asyncio
import json
import uuid
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum

import asyncpg

logger = logging.getLogger(__name__)

class FieldType(Enum):
    """字段类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    BIGINT = "bigint"
    BOOLEAN = "boolean"
    TIMESTAMP = "timestamp"
    DATE = "date"
    DECIMAL = "decimal"
    JSONB = "jsonb"
    TEXT = "text"
    UUID = "uuid"

class IndexType(Enum):
    """索引类型枚举"""
    BTREE = "btree"
    GIN = "gin"
    GIST = "gist"
    HASH = "hash"

@dataclass
class FieldDefinition:
    """字段定义"""
    name: str
    field_type: FieldType
    nullable: bool = True
    unique: bool = False
    required: bool = False
    default_value: Any = None
    max_length: Optional[int] = None
    encrypted: bool = False
    indexed: bool = False

@dataclass
class IndexDefinition:
    """索引定义"""
    name: str
    fields: List[str]
    index_type: IndexType = IndexType.BTREE
    unique: bool = False
    partial_condition: Optional[str] = None

@dataclass
class RelationshipDefinition:
    """关系定义"""
    name: str
    target_element_type: str
    relationship_type: str  # one_to_one, one_to_many, many_to_many
    foreign_key_field: Optional[str] = None
    through_table: Optional[str] = None

class DynamicSchemaGenerator:
    """动态Schema生成器"""
    
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
    
    async def create_domain_schema(self, domain_name: str, schema_name: str, 
                                  element_types: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """
        创建领域Schema及其相关表结构
        
        Args:
            domain_name: 领域名称
            schema_name: Schema名称  
            element_types: Element类型定义列表
            
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息)
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # 1. 创建Schema
                    await self._create_schema(conn, schema_name)
                    
                    # 2. 记录Schema创建状态
                    await self._init_schema_status(conn, domain_name, schema_name)
                    
                    # 3. 为每个Element类型创建表
                    created_tables = []
                    created_indexes = []
                    
                    for element_type_def in element_types:
                        success, table_name, indexes = await self._create_element_table(
                            conn, schema_name, element_type_def
                        )
                        if success:
                            created_tables.append(table_name)
                            created_indexes.extend(indexes)
                        else:
                            raise Exception(f"创建表失败: {table_name}")
                    
                    # 4. 创建关系表
                    relationship_tables = await self._create_relationship_tables(
                        conn, schema_name, element_types
                    )
                    created_tables.extend(relationship_tables)
                    
                    # 5. 创建通用触发器
                    await self._create_common_triggers(conn, schema_name)
                    
                    # 6. 更新Schema状态
                    await self._update_schema_status(
                        conn, schema_name, 'active', created_tables, created_indexes
                    )
                    
                    self.logger.info(f"领域Schema创建成功: {schema_name}")
                    return True, f"Schema {schema_name} 创建成功，包含 {len(created_tables)} 个表"
                    
        except Exception as e:
            self.logger.error(f"创建领域Schema失败 {schema_name}: {e}")
            # 更新失败状态
            try:
                async with self.db_pool.acquire() as conn:
                    await self._update_schema_status(conn, schema_name, 'error', [], [], str(e))
            except:
                pass
            return False, str(e)
    
    async def add_element_type(self, domain_name: str, element_type_def: Dict[str, Any]) -> Tuple[bool, str]:
        """
        向现有领域添加新的Element类型
        
        Args:
            domain_name: 领域名称
            element_type_def: Element类型定义
            
        Returns:
            Tuple[bool, str]: (是否成功, 结果信息)
        """
        try:
            async with self.db_pool.acquire() as conn:
                # 获取领域Schema名称
                row = await conn.fetchrow(
                    "SELECT schema_name FROM core_schema.domain_registry WHERE domain_name = $1",
                    domain_name
                )
                if not row:
                    return False, f"领域不存在: {domain_name}"
                
                schema_name = row['schema_name']
                
                async with conn.transaction():
                    # 1. 注册Element类型定义
                    await self._register_element_type_definition(conn, element_type_def)
                    
                    # 2. 创建对应的表
                    success, table_name, indexes = await self._create_element_table(
                        conn, schema_name, element_type_def
                    )
                    
                    if success:
                        # 3. 更新Schema状态
                        await self._add_table_to_schema_status(conn, schema_name, table_name, indexes)
                        
                        self.logger.info(f"Element类型添加成功: {element_type_def['type_id']}")
                        return True, f"Element类型 {element_type_def['type_id']} 添加成功"
                    else:
                        return False, f"创建表失败: {table_name}"
                        
        except Exception as e:
            self.logger.error(f"添加Element类型失败: {e}")
            return False, str(e)
    
    async def _create_schema(self, conn: asyncpg.Connection, schema_name: str):
        """创建Schema"""
        await conn.execute(f"CREATE SCHEMA IF NOT EXISTS {schema_name}")
        self.logger.info(f"Schema创建: {schema_name}")
    
    async def _init_schema_status(self, conn: asyncpg.Connection, domain_name: str, schema_name: str):
        """初始化Schema状态记录"""
        await conn.execute("""
            INSERT INTO core_schema.dynamic_schemas (schema_name, domain_name, creation_status)
            VALUES ($1, $2, 'creating')
            ON CONFLICT (schema_name) DO UPDATE SET
                creation_status = 'creating',
                updated_at = CURRENT_TIMESTAMP
        """, schema_name, domain_name)
    
    async def _create_element_table(self, conn: asyncpg.Connection, schema_name: str, 
                                   element_type_def: Dict[str, Any]) -> Tuple[bool, str, List[str]]:
        """
        根据Element类型定义创建表
        
        Returns:
            Tuple[bool, str, List[str]]: (是否成功, 表名, 创建的索引列表)
        """
        try:
            type_id = element_type_def['type_id']
            table_name = element_type_def['table_name']
            field_definitions = element_type_def['field_definitions']
            index_definitions = element_type_def.get('index_definitions', [])
            
            # 1. 解析字段定义
            fields = self._parse_field_definitions(field_definitions)
            
            # 2. 生成建表SQL
            create_table_sql = self._generate_create_table_sql(
                schema_name, table_name, fields, type_id
            )
            
            # 3. 执行建表
            await conn.execute(create_table_sql)
            self.logger.info(f"表创建成功: {schema_name}.{table_name}")
            
            # 4. 创建索引
            created_indexes = []
            for index_def in index_definitions:
                index_name = await self._create_table_index(
                    conn, schema_name, table_name, index_def
                )
                if index_name:
                    created_indexes.append(index_name)
            
            # 5. 创建自动字段索引
            auto_indexes = await self._create_auto_indexes(
                conn, schema_name, table_name, fields
            )
            created_indexes.extend(auto_indexes)
            
            return True, table_name, created_indexes
            
        except Exception as e:
            self.logger.error(f"创建Element表失败 {type_id}: {e}")
            return False, element_type_def.get('table_name', 'unknown'), []
    
    def _parse_field_definitions(self, field_definitions: Dict[str, Any]) -> List[FieldDefinition]:
        """解析字段定义"""
        fields = []
        
        # 添加基础字段（继承自core_schema.element_metadata）
        fields.append(FieldDefinition(
            name="element_id",
            field_type=FieldType.STRING,
            nullable=False,
            unique=True,
            required=True
        ))
        
        # 解析自定义字段
        for field_name, field_config in field_definitions.items():
            field_type_str = field_config.get('type', 'string')
            field_type = FieldType(field_type_str)
            
            field = FieldDefinition(
                name=field_name,
                field_type=field_type,
                nullable=not field_config.get('required', False),
                unique=field_config.get('unique', False),
                required=field_config.get('required', False),
                default_value=field_config.get('default'),
                max_length=field_config.get('max_length'),
                encrypted=field_config.get('encrypted', False),
                indexed=field_config.get('indexed', False)
            )
            fields.append(field)
        
        # 添加通用时间戳字段
        fields.extend([
            FieldDefinition(
                name="created_at",
                field_type=FieldType.TIMESTAMP,
                nullable=False,
                default_value="CURRENT_TIMESTAMP"
            ),
            FieldDefinition(
                name="updated_at", 
                field_type=FieldType.TIMESTAMP,
                nullable=False,
                default_value="CURRENT_TIMESTAMP"
            )
        ])
        
        return fields
    
    def _generate_create_table_sql(self, schema_name: str, table_name: str, 
                                  fields: List[FieldDefinition], element_type_id: str) -> str:
        """生成建表SQL"""
        field_sqls = []
        
        for field in fields:
            field_sql = f"{field.name} {self._get_postgres_type(field)}"
            
            # 处理约束
            if not field.nullable:
                field_sql += " NOT NULL"
            
            if field.unique:
                field_sql += " UNIQUE"
            
            if field.default_value:
                if field.default_value == "CURRENT_TIMESTAMP":
                    field_sql += " DEFAULT CURRENT_TIMESTAMP"
                elif isinstance(field.default_value, str):
                    field_sql += f" DEFAULT '{field.default_value}'"
                else:
                    field_sql += f" DEFAULT {field.default_value}"
            
            field_sqls.append(field_sql)
        
        # 添加主键约束
        field_sqls.append("PRIMARY KEY (element_id)")
        
        # 添加外键约束到core_schema.element_metadata
        field_sqls.append(
            "FOREIGN KEY (element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE"
        )
        
        sql = f"""
            CREATE TABLE IF NOT EXISTS {schema_name}.{table_name} (
                {', '.join(field_sqls)}
            )
        """
        
        return sql
    
    def _get_postgres_type(self, field: FieldDefinition) -> str:
        """获取PostgreSQL数据类型"""
        type_mapping = {
            FieldType.STRING: f"VARCHAR({field.max_length or 255})",
            FieldType.INTEGER: "INTEGER",
            FieldType.BIGINT: "BIGINT", 
            FieldType.BOOLEAN: "BOOLEAN",
            FieldType.TIMESTAMP: "TIMESTAMP WITH TIME ZONE",
            FieldType.DATE: "DATE",
            FieldType.DECIMAL: "DECIMAL(10,2)",
            FieldType.JSONB: "JSONB",
            FieldType.TEXT: "TEXT",
            FieldType.UUID: "UUID"
        }
        
        return type_mapping.get(field.field_type, "TEXT")
    
    async def _create_table_index(self, conn: asyncpg.Connection, schema_name: str, 
                                 table_name: str, index_def: Dict[str, Any]) -> Optional[str]:
        """创建表索引"""
        try:
            fields = index_def['fields']
            index_type = index_def.get('type', 'btree')
            unique = index_def.get('unique', False)
            
            index_name = f"idx_{table_name}_{'_'.join(fields)}"
            unique_clause = "UNIQUE " if unique else ""
            
            sql = f"""
                CREATE {unique_clause}INDEX IF NOT EXISTS {index_name}
                ON {schema_name}.{table_name} USING {index_type} ({', '.join(fields)})
            """
            
            await conn.execute(sql)
            self.logger.info(f"索引创建成功: {index_name}")
            return index_name
            
        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
            return None
    
    async def _create_auto_indexes(self, conn: asyncpg.Connection, schema_name: str,
                                  table_name: str, fields: List[FieldDefinition]) -> List[str]:
        """创建自动索引"""
        created_indexes = []
        
        for field in fields:
            if field.indexed or field.unique:
                try:
                    index_name = f"idx_{table_name}_{field.name}"
                    unique_clause = "UNIQUE " if field.unique else ""
                    
                    if field.field_type == FieldType.JSONB:
                        index_type = "GIN"
                    else:
                        index_type = "BTREE"
                    
                    sql = f"""
                        CREATE {unique_clause}INDEX IF NOT EXISTS {index_name}
                        ON {schema_name}.{table_name} USING {index_type} ({field.name})
                    """
                    
                    await conn.execute(sql)
                    created_indexes.append(index_name)
                    
                except Exception as e:
                    self.logger.warning(f"自动索引创建失败 {field.name}: {e}")
        
        return created_indexes
    
    async def _create_relationship_tables(self, conn: asyncpg.Connection, schema_name: str,
                                         element_types: List[Dict[str, Any]]) -> List[str]:
        """创建关系表"""
        created_tables = []
        
        for element_type_def in element_types:
            relationships = element_type_def.get('relationship_definitions', {})
            
            for rel_name, rel_config in relationships.items():
                if rel_config.get('type') == 'many_to_many':
                    table_name = await self._create_many_to_many_table(
                        conn, schema_name, element_type_def['type_id'], rel_config
                    )
                    if table_name:
                        created_tables.append(table_name)
        
        return created_tables
    
    async def _create_many_to_many_table(self, conn: asyncpg.Connection, schema_name: str,
                                        source_type: str, rel_config: Dict[str, Any]) -> Optional[str]:
        """创建多对多关系表"""
        try:
            target_type = rel_config['target']
            table_name = f"{source_type}_{target_type}_relationships"
            
            sql = f"""
                CREATE TABLE IF NOT EXISTS {schema_name}.{table_name} (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    source_element_id TEXT NOT NULL,
                    target_element_id TEXT NOT NULL,
                    relationship_data JSONB DEFAULT '{{}}',
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (source_element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
                    FOREIGN KEY (target_element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
                    
                    UNIQUE(source_element_id, target_element_id)
                )
            """
            
            await conn.execute(sql)
            
            # 创建索引
            await conn.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_{table_name}_source 
                ON {schema_name}.{table_name}(source_element_id, is_active)
            """)
            
            await conn.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_{table_name}_target 
                ON {schema_name}.{table_name}(target_element_id, is_active)
            """)
            
            self.logger.info(f"关系表创建成功: {table_name}")
            return table_name
            
        except Exception as e:
            self.logger.error(f"创建关系表失败: {e}")
            return None
    
    async def _create_common_triggers(self, conn: asyncpg.Connection, schema_name: str):
        """创建通用触发器"""
        try:
            # 确保更新时间戳函数存在
            await conn.execute(f"""
                CREATE OR REPLACE FUNCTION {schema_name}.update_updated_at_column()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = CURRENT_TIMESTAMP;
                    RETURN NEW;
                END;
                $$ language 'plpgsql';
            """)
            
            self.logger.info(f"通用触发器函数创建成功: {schema_name}")
            
        except Exception as e:
            self.logger.error(f"创建通用触发器失败: {e}")
    
    async def _update_schema_status(self, conn: asyncpg.Connection, schema_name: str,
                                   status: str, created_tables: List[str] = None,
                                   created_indexes: List[str] = None, error_msg: str = None):
        """更新Schema状态"""
        update_data = {
            'creation_status': status,
            'updated_at': datetime.now(timezone.utc)
        }
        
        if created_tables is not None:
            update_data['created_tables'] = json.dumps(created_tables)
        
        if created_indexes is not None:
            update_data['created_indexes'] = json.dumps(created_indexes)
        
        if error_msg:
            current_log = await conn.fetchval(
                "SELECT creation_log FROM core_schema.dynamic_schemas WHERE schema_name = $1",
                schema_name
            )
            if current_log:
                log_entries = json.loads(current_log)
            else:
                log_entries = []
            
            log_entries.append({
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'level': 'error',
                'message': error_msg
            })
            update_data['creation_log'] = json.dumps(log_entries)
        
        # 构建UPDATE SQL
        set_clauses = []
        params = []
        param_count = 1
        
        for field, value in update_data.items():
            set_clauses.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        params.append(schema_name)
        
        sql = f"""
            UPDATE core_schema.dynamic_schemas 
            SET {', '.join(set_clauses)}
            WHERE schema_name = ${param_count}
        """
        
        await conn.execute(sql, *params)
    
    async def _add_table_to_schema_status(self, conn: asyncpg.Connection, schema_name: str,
                                         table_name: str, indexes: List[str]):
        """向Schema状态添加新创建的表"""
        # 获取当前状态
        row = await conn.fetchrow(
            "SELECT created_tables, created_indexes FROM core_schema.dynamic_schemas WHERE schema_name = $1",
            schema_name
        )
        
        if row:
            current_tables = json.loads(row['created_tables']) if row['created_tables'] else []
            current_indexes = json.loads(row['created_indexes']) if row['created_indexes'] else []
            
            current_tables.append(table_name)
            current_indexes.extend(indexes)
            
            await conn.execute("""
                UPDATE core_schema.dynamic_schemas 
                SET created_tables = $1, created_indexes = $2, updated_at = CURRENT_TIMESTAMP
                WHERE schema_name = $3
            """, json.dumps(current_tables), json.dumps(current_indexes), schema_name)
    
    async def _register_element_type_definition(self, conn: asyncpg.Connection, element_type_def: Dict[str, Any]):
        """注册Element类型定义到core_schema"""
        await conn.execute("""
            INSERT INTO core_schema.element_type_definitions (
                type_id, type_name, domain_schema, field_definitions, table_name,
                index_definitions, relationship_definitions, cross_domain_refs, type_config
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            ON CONFLICT (type_id) DO UPDATE SET
                field_definitions = EXCLUDED.field_definitions,
                index_definitions = EXCLUDED.index_definitions,
                relationship_definitions = EXCLUDED.relationship_definitions,
                cross_domain_refs = EXCLUDED.cross_domain_refs,
                type_config = EXCLUDED.type_config,
                updated_at = CURRENT_TIMESTAMP
        """,
            element_type_def['type_id'],
            element_type_def['type_name'],
            element_type_def['domain_schema'],
            json.dumps(element_type_def['field_definitions']),
            element_type_def['table_name'],
            json.dumps(element_type_def.get('index_definitions', [])),
            json.dumps(element_type_def.get('relationship_definitions', {})),
            json.dumps(element_type_def.get('cross_domain_refs', [])),
            json.dumps(element_type_def.get('type_config', {}))
        ) 