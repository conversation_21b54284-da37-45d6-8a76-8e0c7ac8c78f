#!/usr/bin/env python3
"""
UML25 Schema版本管理和数据迁移模块

功能：
- Schema版本跟踪
- 自动迁移执行
- 回滚支持
- 数据完整性验证
- 备份和恢复
"""

import asyncio
import asyncpg
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import json
import re
from enum import Enum

logger = logging.getLogger(__name__)

class MigrationStatus(Enum):
    """迁移状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"

@dataclass
class MigrationScript:
    """迁移脚本定义"""
    version: str
    name: str
    description: str
    up_script: str
    down_script: Optional[str] = None
    dependencies: List[str] = field(default_factory=list)
    estimated_duration: Optional[int] = None  # 预估执行时间（秒）
    requires_downtime: bool = False
    validation_queries: List[str] = field(default_factory=list)

@dataclass
class MigrationExecution:
    """迁移执行记录"""
    id: str
    version: str
    status: MigrationStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    execution_time_ms: Optional[float] = None
    checksum: Optional[str] = None

class SchemaVersionManager:
    """Schema版本管理器"""
    
    def __init__(self, db_pool: asyncpg.Pool, schema_name: str = "uml25_base"):
        self.db_pool = db_pool
        self.schema_name = schema_name
        self.current_version = "1.0.0"
        self.migration_scripts: Dict[str, MigrationScript] = {}
        self.migration_path = Path(__file__).parent / "migrations"
        
    async def initialize_version_tracking(self):
        """初始化版本跟踪表"""
        await self._create_version_tables()
        await self._register_initial_version()
    
    async def _create_version_tables(self):
        """创建版本管理相关表"""
        version_tables_sql = f"""
        -- Schema版本元数据表
        CREATE TABLE IF NOT EXISTS {self.schema_name}.schema_metadata (
            id SERIAL PRIMARY KEY,
            version VARCHAR(20) NOT NULL,
            description TEXT,
            migration_script TEXT,
            checksum VARCHAR(64),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            created_by VARCHAR(100) DEFAULT CURRENT_USER
        );
        
        -- 迁移执行历史表
        CREATE TABLE IF NOT EXISTS {self.schema_name}.migration_history (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            version VARCHAR(20) NOT NULL,
            script_name VARCHAR(200) NOT NULL,
            status VARCHAR(20) NOT NULL,
            started_at TIMESTAMP WITH TIME ZONE NOT NULL,
            completed_at TIMESTAMP WITH TIME ZONE,
            execution_time_ms BIGINT,
            error_message TEXT,
            checksum VARCHAR(64),
            rollback_script TEXT,
            created_by VARCHAR(100) DEFAULT CURRENT_USER
        );
        
        -- 物化视图刷新日志表
        CREATE TABLE IF NOT EXISTS {self.schema_name}.materialized_view_refresh_log (
            id SERIAL PRIMARY KEY,
            view_name VARCHAR(200) NOT NULL,
            refresh_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            status VARCHAR(20) NOT NULL,
            execution_time_ms BIGINT,
            error_message TEXT
        );
        
        -- 数据完整性检查日志表
        CREATE TABLE IF NOT EXISTS {self.schema_name}.integrity_check_log (
            id SERIAL PRIMARY KEY,
            check_type VARCHAR(100) NOT NULL,
            check_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            status VARCHAR(20) NOT NULL,
            issues_found INTEGER DEFAULT 0,
            details JSONB,
            fixed_issues INTEGER DEFAULT 0
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_schema_metadata_version 
        ON {self.schema_name}.schema_metadata(version);
        
        CREATE INDEX IF NOT EXISTS idx_migration_history_version_status 
        ON {self.schema_name}.migration_history(version, status);
        
        CREATE INDEX IF NOT EXISTS idx_migration_history_started_at 
        ON {self.schema_name}.migration_history(started_at);
        """
        
        async with self.db_pool.acquire() as conn:
            await conn.execute(version_tables_sql)
        
        logger.info("版本管理表初始化完成")
    
    async def _register_initial_version(self):
        """注册初始版本"""
        async with self.db_pool.acquire() as conn:
            # 检查是否已有版本记录
            existing_version = await conn.fetchval(
                f"SELECT version FROM {self.schema_name}.schema_metadata ORDER BY created_at DESC LIMIT 1"
            )
            
            if not existing_version:
                await conn.execute(f"""
                    INSERT INTO {self.schema_name}.schema_metadata 
                    (version, description, migration_script)
                    VALUES ($1, $2, $3)
                """, "1.0.0", "初始版本 - 基础UML 2.5 Schema", "initial_schema.sql")
                
                logger.info("初始版本 1.0.0 已注册")
    
    async def get_current_version(self) -> str:
        """获取当前Schema版本"""
        async with self.db_pool.acquire() as conn:
            version = await conn.fetchval(
                f"SELECT version FROM {self.schema_name}.schema_metadata ORDER BY created_at DESC LIMIT 1"
            )
            return version or "0.0.0"
    
    async def load_migration_scripts(self):
        """加载迁移脚本"""
        if not self.migration_path.exists():
            self.migration_path.mkdir(parents=True, exist_ok=True)
            await self._create_sample_migrations()
        
        # 加载所有迁移脚本
        for script_file in self.migration_path.glob("*.py"):
            await self._load_migration_script(script_file)
        
        logger.info(f"已加载 {len(self.migration_scripts)} 个迁移脚本")
    
    async def _load_migration_script(self, script_file: Path):
        """加载单个迁移脚本"""
        try:
            # 这里简化处理，实际应该动态导入Python模块
            script_name = script_file.stem
            version_match = re.match(r'v(\d+\.\d+\.\d+)_(.+)', script_name)
            
            if version_match:
                version = version_match.group(1)
                name = version_match.group(2).replace('_', ' ')
                
                # 读取脚本内容
                content = script_file.read_text(encoding='utf-8')
                
                # 解析up和down脚本
                up_script, down_script = self._parse_migration_content(content)
                
                migration = MigrationScript(
                    version=version,
                    name=name,
                    description=f"Migration to version {version}: {name}",
                    up_script=up_script,
                    down_script=down_script
                )
                
                self.migration_scripts[version] = migration
                
        except Exception as e:
            logger.warning(f"加载迁移脚本 {script_file} 失败: {e}")
    
    def _parse_migration_content(self, content: str) -> Tuple[str, Optional[str]]:
        """解析迁移脚本内容"""
        # 简单的解析逻辑，寻找UP和DOWN标记
        up_match = re.search(r'# UP\s*\n(.*?)(?=# DOWN|\Z)', content, re.DOTALL)
        down_match = re.search(r'# DOWN\s*\n(.*)', content, re.DOTALL)
        
        up_script = up_match.group(1).strip() if up_match else content
        down_script = down_match.group(1).strip() if down_match else None
        
        return up_script, down_script
    
    async def _create_sample_migrations(self):
        """创建示例迁移脚本"""
        
        # 1.1.0 - 添加行为模型支持
        migration_1_1_0 = '''# UP
-- 添加行为基类继承表
CREATE TABLE {schema}.uml_behavior () INHERITS ({schema}.uml_classifier);
ALTER TABLE {schema}.uml_behavior ADD COLUMN is_reentrant BOOLEAN DEFAULT false;

-- 添加活动数据表
CREATE TABLE {schema}.uml_activity_data (
    id UUID PRIMARY KEY,
    is_single_execution BOOLEAN DEFAULT false,
    is_read_only BOOLEAN DEFAULT false,
    FOREIGN KEY (id) REFERENCES {schema}.uml_behavior(id) ON DELETE CASCADE
);

-- 创建活动视图
CREATE VIEW {schema}.uml_activity AS
SELECT d.*, b.*, c.*, t.*, ne.*, e.*
FROM {schema}.uml_activity_data d
JOIN {schema}.uml_behavior b ON d.id = b.id
JOIN {schema}.uml_classifier c ON b.id = c.id
JOIN {schema}.uml_type t ON c.id = t.id
JOIN {schema}.uml_named_element ne ON t.id = ne.id
JOIN {schema}.uml_element e ON ne.id = e.id;

-- 更新版本信息
INSERT INTO {schema}.schema_metadata (version, description, migration_script)
VALUES ('1.1.0', '添加行为模型支持', 'v1.1.0_add_behavior_model.py');

# DOWN
-- 删除活动视图
DROP VIEW IF EXISTS {schema}.uml_activity;

-- 删除活动数据表
DROP TABLE IF EXISTS {schema}.uml_activity_data;

-- 删除行为继承表
DROP TABLE IF EXISTS {schema}.uml_behavior;

-- 删除版本记录
DELETE FROM {schema}.schema_metadata WHERE version = '1.1.0';
'''.format(schema=self.schema_name)
        
        # 1.2.0 - 添加SysML支持
        migration_1_2_0 = '''# UP
-- 添加SysML Block数据表
CREATE TABLE {schema}.sysml_block_data (
    id UUID PRIMARY KEY,
    is_encapsulated BOOLEAN DEFAULT false,
    value_properties JSONB DEFAULT '[]',
    FOREIGN KEY (id) REFERENCES {schema}.uml_class_data(id) ON DELETE CASCADE
);

-- 创建SysML Block视图
CREATE VIEW {schema}.sysml_block AS
SELECT sbd.*, cd.*, c.*, t.*, ne.*, e.*
FROM {schema}.sysml_block_data sbd
JOIN {schema}.uml_class_data cd ON sbd.id = cd.id
JOIN {schema}.uml_classifier c ON cd.id = c.id
JOIN {schema}.uml_type t ON c.id = t.id
JOIN {schema}.uml_named_element ne ON t.id = ne.id
JOIN {schema}.uml_element e ON ne.id = e.id;

-- 添加SysML需求数据表
CREATE TABLE {schema}.sysml_requirement_data (
    id UUID PRIMARY KEY,
    text TEXT,
    requirement_id VARCHAR(100),
    derived_requirements JSONB DEFAULT '[]',
    satisfied_by JSONB DEFAULT '[]',
    verified_by JSONB DEFAULT '[]',
    FOREIGN KEY (id) REFERENCES {schema}.uml_class_data(id) ON DELETE CASCADE
);

-- 创建SysML需求视图
CREATE VIEW {schema}.sysml_requirement AS
SELECT srd.*, cd.*, c.*, t.*, ne.*, e.*
FROM {schema}.sysml_requirement_data srd
JOIN {schema}.uml_class_data cd ON srd.id = cd.id
JOIN {schema}.uml_classifier c ON cd.id = c.id
JOIN {schema}.uml_type t ON c.id = t.id
JOIN {schema}.uml_named_element ne ON t.id = ne.id
JOIN {schema}.uml_element e ON ne.id = e.id;

-- 更新版本信息
INSERT INTO {schema}.schema_metadata (version, description, migration_script)
VALUES ('1.2.0', '添加SysML核心扩展支持', 'v1.2.0_add_sysml_support.py');

# DOWN
-- 删除SysML视图
DROP VIEW IF EXISTS {schema}.sysml_requirement;
DROP VIEW IF EXISTS {schema}.sysml_block;

-- 删除SysML数据表
DROP TABLE IF EXISTS {schema}.sysml_requirement_data;
DROP TABLE IF EXISTS {schema}.sysml_block_data;

-- 删除版本记录
DELETE FROM {schema}.schema_metadata WHERE version = '1.2.0';
'''.format(schema=self.schema_name)
        
        # 写入文件
        (self.migration_path / "v1.1.0_add_behavior_model.py").write_text(migration_1_1_0, encoding='utf-8')
        (self.migration_path / "v1.2.0_add_sysml_support.py").write_text(migration_1_2_0, encoding='utf-8')
        
        logger.info("示例迁移脚本已创建")
    
    async def migrate_to_version(self, target_version: str, dry_run: bool = False) -> Dict[str, Any]:
        """迁移到指定版本"""
        current_version = await self.get_current_version()
        
        if current_version == target_version:
            return {
                'status': 'success',
                'message': f'已经是目标版本 {target_version}',
                'migrations_applied': []
            }
        
        # 计算迁移路径
        migration_path = self._calculate_migration_path(current_version, target_version)
        
        if not migration_path:
            return {
                'status': 'error',
                'message': f'无法从版本 {current_version} 迁移到 {target_version}',
                'migrations_applied': []
            }
        
        if dry_run:
            return {
                'status': 'dry_run',
                'message': f'迁移路径: {" -> ".join(migration_path)}',
                'migrations_planned': migration_path
            }
        
        # 执行迁移
        applied_migrations = []
        
        try:
            for version in migration_path:
                if version == current_version:
                    continue
                
                migration = self.migration_scripts.get(version)
                if not migration:
                    raise Exception(f"找不到版本 {version} 的迁移脚本")
                
                logger.info(f"执行迁移到版本 {version}: {migration.name}")
                
                execution_record = await self._execute_migration(migration)
                applied_migrations.append(execution_record)
                
                if execution_record.status == MigrationStatus.FAILED:
                    raise Exception(f"迁移失败: {execution_record.error_message}")
            
            return {
                'status': 'success',
                'message': f'成功迁移到版本 {target_version}',
                'migrations_applied': [m.version for m in applied_migrations]
            }
            
        except Exception as e:
            logger.error(f"迁移过程中发生错误: {e}")
            
            # 尝试回滚
            await self._rollback_migrations(applied_migrations)
            
            return {
                'status': 'error',
                'message': str(e),
                'migrations_applied': [m.version for m in applied_migrations],
                'rollback_attempted': True
            }
    
    def _calculate_migration_path(self, current_version: str, target_version: str) -> List[str]:
        """计算迁移路径"""
        available_versions = sorted(self.migration_scripts.keys(), key=self._version_key)
        
        try:
            current_idx = available_versions.index(current_version)
            target_idx = available_versions.index(target_version)
            
            if target_idx > current_idx:
                # 向前迁移
                return available_versions[current_idx:target_idx + 1]
            else:
                # 向后迁移（回滚）
                return list(reversed(available_versions[target_idx:current_idx + 1]))
                
        except ValueError:
            return []
    
    def _version_key(self, version: str) -> tuple:
        """版本排序key"""
        return tuple(map(int, version.split('.')))
    
    async def _execute_migration(self, migration: MigrationScript) -> MigrationExecution:
        """执行单个迁移"""
        execution_id = f"migration_{migration.version}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        execution = MigrationExecution(
            id=execution_id,
            version=migration.version,
            status=MigrationStatus.RUNNING,
            started_at=datetime.now()
        )
        
        try:
            # 记录开始执行
            await self._record_migration_start(execution)
            
            # 执行迁移脚本
            start_time = datetime.now()
            
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # 执行UP脚本
                    await conn.execute(migration.up_script)
                    
                    # 验证迁移结果
                    if migration.validation_queries:
                        for query in migration.validation_queries:
                            await conn.fetchval(query)
            
            execution.completed_at = datetime.now()
            execution.execution_time_ms = (execution.completed_at - start_time).total_seconds() * 1000
            execution.status = MigrationStatus.SUCCESS
            
            # 记录成功完成
            await self._record_migration_completion(execution)
            
            logger.info(f"迁移 {migration.version} 执行成功，耗时 {execution.execution_time_ms:.2f}ms")
            
        except Exception as e:
            execution.completed_at = datetime.now()
            execution.status = MigrationStatus.FAILED
            execution.error_message = str(e)
            
            # 记录执行失败
            await self._record_migration_completion(execution)
            
            logger.error(f"迁移 {migration.version} 执行失败: {e}")
        
        return execution
    
    async def _record_migration_start(self, execution: MigrationExecution):
        """记录迁移开始"""
        async with self.db_pool.acquire() as conn:
            await conn.execute(f"""
                INSERT INTO {self.schema_name}.migration_history 
                (id, version, script_name, status, started_at)
                VALUES ($1, $2, $3, $4, $5)
            """, execution.id, execution.version, f"migration_{execution.version}",
                execution.status.value, execution.started_at)
    
    async def _record_migration_completion(self, execution: MigrationExecution):
        """记录迁移完成"""
        async with self.db_pool.acquire() as conn:
            await conn.execute(f"""
                UPDATE {self.schema_name}.migration_history 
                SET status = $1, completed_at = $2, execution_time_ms = $3, error_message = $4
                WHERE id = $5
            """, execution.status.value, execution.completed_at, 
                execution.execution_time_ms, execution.error_message, execution.id)
    
    async def _rollback_migrations(self, executions: List[MigrationExecution]):
        """回滚迁移"""
        logger.info("开始回滚迁移...")
        
        for execution in reversed(executions):
            if execution.status == MigrationStatus.SUCCESS:
                try:
                    migration = self.migration_scripts.get(execution.version)
                    if migration and migration.down_script:
                        async with self.db_pool.acquire() as conn:
                            async with conn.transaction():
                                await conn.execute(migration.down_script)
                        
                        # 更新状态为已回滚
                        await conn.execute(f"""
                            UPDATE {self.schema_name}.migration_history 
                            SET status = $1 WHERE id = $2
                        """, MigrationStatus.ROLLED_BACK.value, execution.id)
                        
                        logger.info(f"迁移 {execution.version} 已回滚")
                    
                except Exception as e:
                    logger.error(f"回滚迁移 {execution.version} 失败: {e}")
    
    async def validate_schema_integrity(self) -> Dict[str, Any]:
        """验证Schema完整性"""
        logger.info("开始Schema完整性检查...")
        
        validation_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'success',
            'checks': {}
        }
        
        # 检查外键约束
        fk_check = await self._check_foreign_keys()
        validation_results['checks']['foreign_keys'] = fk_check
        
        # 检查继承表结构
        inheritance_check = await self._check_inheritance_structure()
        validation_results['checks']['inheritance'] = inheritance_check
        
        # 检查视图定义
        view_check = await self._check_view_definitions()
        validation_results['checks']['views'] = view_check
        
        # 检查索引状态
        index_check = await self._check_index_status()
        validation_results['checks']['indexes'] = index_check
        
        # 检查数据一致性
        consistency_check = await self._check_data_consistency()
        validation_results['checks']['data_consistency'] = consistency_check
        
        # 确定总体状态
        if any(check.get('status') == 'failed' for check in validation_results['checks'].values()):
            validation_results['overall_status'] = 'failed'
        elif any(check.get('status') == 'warning' for check in validation_results['checks'].values()):
            validation_results['overall_status'] = 'warning'
        
        # 记录检查结果
        await self._record_integrity_check(validation_results)
        
        return validation_results
    
    async def _check_foreign_keys(self) -> Dict[str, Any]:
        """检查外键约束"""
        async with self.db_pool.acquire() as conn:
            # 查找违反外键约束的记录
            fk_violations = await conn.fetch(f"""
                SELECT 
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage ccu 
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY' 
                    AND tc.table_schema = '{self.schema_name}'
            """)
            
            return {
                'status': 'success' if len(fk_violations) == 0 else 'failed',
                'violations_count': len(fk_violations),
                'violations': [dict(row) for row in fk_violations]
            }
    
    async def _check_inheritance_structure(self) -> Dict[str, Any]:
        """检查继承表结构"""
        async with self.db_pool.acquire() as conn:
            # 检查继承表是否存在循环引用
            inheritance_tables = await conn.fetch(f"""
                SELECT schemaname, tablename, inherits
                FROM pg_tables 
                WHERE schemaname = '{self.schema_name}' 
                    AND inherits IS NOT NULL
            """)
            
            return {
                'status': 'success',
                'inheritance_tables_count': len(inheritance_tables),
                'tables': [dict(row) for row in inheritance_tables]
            }
    
    async def _check_view_definitions(self) -> Dict[str, Any]:
        """检查视图定义"""
        async with self.db_pool.acquire() as conn:
            # 检查视图是否可以正常查询
            views = await conn.fetch(f"""
                SELECT table_name 
                FROM information_schema.views 
                WHERE table_schema = '{self.schema_name}'
            """)
            
            broken_views = []
            
            for view in views:
                try:
                    await conn.fetchval(f"SELECT 1 FROM {self.schema_name}.{view['table_name']} LIMIT 1")
                except Exception as e:
                    broken_views.append({
                        'view_name': view['table_name'],
                        'error': str(e)
                    })
            
            return {
                'status': 'success' if len(broken_views) == 0 else 'failed',
                'total_views': len(views),
                'broken_views_count': len(broken_views),
                'broken_views': broken_views
            }
    
    async def _check_index_status(self) -> Dict[str, Any]:
        """检查索引状态"""
        async with self.db_pool.acquire() as conn:
            # 检查索引是否有效
            invalid_indexes = await conn.fetch(f"""
                SELECT schemaname, tablename, indexname
                FROM pg_indexes 
                WHERE schemaname = '{self.schema_name}' 
                    AND indexdef LIKE '%INVALID%'
            """)
            
            return {
                'status': 'success' if len(invalid_indexes) == 0 else 'warning',
                'invalid_indexes_count': len(invalid_indexes),
                'invalid_indexes': [dict(row) for row in invalid_indexes]
            }
    
    async def _check_data_consistency(self) -> Dict[str, Any]:
        """检查数据一致性"""
        async with self.db_pool.acquire() as conn:
            issues = []
            
            # 检查orphaned records
            try:
                orphaned_features = await conn.fetchval(f"""
                    SELECT COUNT(*) 
                    FROM {self.schema_name}.uml_feature f
                    LEFT JOIN {self.schema_name}.uml_classifier c ON f.featuring_classifier_id = c.id
                    WHERE c.id IS NULL AND f.featuring_classifier_id IS NOT NULL
                """)
                
                if orphaned_features > 0:
                    issues.append({
                        'type': 'orphaned_features',
                        'count': orphaned_features,
                        'description': '存在没有对应分类器的特征'
                    })
                
            except Exception as e:
                issues.append({
                    'type': 'check_error',
                    'error': str(e),
                    'description': '数据一致性检查时发生错误'
                })
            
            return {
                'status': 'success' if len(issues) == 0 else 'warning',
                'issues_count': len(issues),
                'issues': issues
            }
    
    async def _record_integrity_check(self, results: Dict[str, Any]):
        """记录完整性检查结果"""
        async with self.db_pool.acquire() as conn:
            await conn.execute(f"""
                INSERT INTO {self.schema_name}.integrity_check_log 
                (check_type, status, issues_found, details)
                VALUES ($1, $2, $3, $4)
            """, 'full_schema_check', results['overall_status'],
                sum(check.get('violations_count', check.get('issues_count', 0)) 
                    for check in results['checks'].values()),
                json.dumps(results['checks']))
    
    async def create_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """创建Schema备份"""
        if not backup_name:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_info = {
            'name': backup_name,
            'timestamp': datetime.now().isoformat(),
            'version': await self.get_current_version(),
            'status': 'in_progress'
        }
        
        try:
            # 这里应该实现具体的备份逻辑
            # 例如使用pg_dump或者数据复制
            
            backup_info['status'] = 'success'
            backup_info['message'] = f'备份 {backup_name} 创建成功'
            
            logger.info(f"Schema备份 {backup_name} 创建成功")
            
        except Exception as e:
            backup_info['status'] = 'failed'
            backup_info['error'] = str(e)
            logger.error(f"创建备份失败: {e}")
        
        return backup_info
    
    async def get_migration_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取迁移历史"""
        async with self.db_pool.acquire() as conn:
            history = await conn.fetch(f"""
                SELECT version, script_name, status, started_at, completed_at, 
                       execution_time_ms, error_message, created_by
                FROM {self.schema_name}.migration_history
                ORDER BY started_at DESC
                LIMIT $1
            """, limit)
            
            return [dict(row) for row in history]
    
    async def get_schema_info(self) -> Dict[str, Any]:
        """获取Schema信息摘要"""
        async with self.db_pool.acquire() as conn:
            # 统计表数量
            table_count = await conn.fetchval(f"""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = '{self.schema_name}'
            """)
            
            # 统计视图数量
            view_count = await conn.fetchval(f"""
                SELECT COUNT(*) FROM information_schema.views 
                WHERE table_schema = '{self.schema_name}'
            """)
            
            # 统计索引数量
            index_count = await conn.fetchval(f"""
                SELECT COUNT(*) FROM pg_indexes 
                WHERE schemaname = '{self.schema_name}'
            """)
            
            # 统计元素数量
            element_count = await conn.fetchval(f"""
                SELECT COUNT(*) FROM {self.schema_name}.uml_element
            """) if await self._table_exists(conn, "uml_element") else 0
            
            return {
                'current_version': await self.get_current_version(),
                'schema_name': self.schema_name,
                'tables_count': table_count,
                'views_count': view_count,
                'indexes_count': index_count,
                'elements_count': element_count,
                'last_migration': await self._get_last_migration_info(),
                'available_migrations': list(self.migration_scripts.keys())
            }
    
    async def _table_exists(self, conn, table_name: str) -> bool:
        """检查表是否存在"""
        result = await conn.fetchval(f"""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_schema = '{self.schema_name}' 
                    AND table_name = '{table_name}'
            )
        """)
        return result
    
    async def _get_last_migration_info(self) -> Optional[Dict[str, Any]]:
        """获取最后一次迁移信息"""
        async with self.db_pool.acquire() as conn:
            last_migration = await conn.fetchrow(f"""
                SELECT version, status, started_at, execution_time_ms
                FROM {self.schema_name}.migration_history
                ORDER BY started_at DESC
                LIMIT 1
            """)
            
            return dict(last_migration) if last_migration else None

# 便捷函数
async def create_schema_version_manager(
    db_pool: asyncpg.Pool, 
    schema_name: str = "uml25_base"
) -> SchemaVersionManager:
    """创建Schema版本管理器"""
    manager = SchemaVersionManager(db_pool, schema_name)
    await manager.initialize_version_tracking()
    await manager.load_migration_scripts()
    return manager

async def migrate_schema_to_latest(
    db_pool: asyncpg.Pool, 
    schema_name: str = "uml25_base"
) -> Dict[str, Any]:
    """迁移Schema到最新版本"""
    manager = await create_schema_version_manager(db_pool, schema_name)
    
    # 找到最新版本
    available_versions = sorted(manager.migration_scripts.keys(), key=manager._version_key)
    if available_versions:
        latest_version = available_versions[-1]
        return await manager.migrate_to_version(latest_version)
    else:
        return {
            'status': 'success',
            'message': '没有可用的迁移脚本',
            'migrations_applied': []
        } 