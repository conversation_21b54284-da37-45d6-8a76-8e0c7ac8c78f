#!/usr/bin/env python3
"""
UML2.5 Schema生成器

基于UML2.5 XMI解析结果，动态生成PostgreSQL数据库Schema。
这是@database层的核心组件，负责将元类定义转换为可执行的SQL DDL。

功能特性:
- 基于UML2.5元类生成表结构
- 支持继承关系映射
- 动态约束生成
- Domain扩展支持
- PostgreSQL高级特性集成

作者: XML元数据系统开发团队
版本: 1.0.0
日期: 2025年1月1日
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import asyncpg
import json
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class TableColumn:
    """表列定义"""
    name: str
    data_type: str
    nullable: bool = True
    default_value: Optional[str] = None
    primary_key: bool = False
    unique: bool = False
    check_constraint: Optional[str] = None
    foreign_key: Optional[Dict[str, str]] = None
    comment: str = ""
    
    def to_sql(self) -> str:
        """生成SQL列定义"""
        sql_parts = [f'"{self.name}"', self.data_type]
        
        if not self.nullable:
            sql_parts.append('NOT NULL')
        
        if self.default_value:
            sql_parts.append(f'DEFAULT {self.default_value}')
        
        if self.unique:
            sql_parts.append('UNIQUE')
            
        return ' '.join(sql_parts)

@dataclass 
class TableDefinition:
    """表定义"""
    name: str
    schema_name: str = "uml25_base"
    columns: List[TableColumn] = field(default_factory=list)
    primary_keys: List[str] = field(default_factory=list)
    foreign_keys: List[Dict[str, str]] = field(default_factory=list)
    indexes: List[Dict[str, Any]] = field(default_factory=list)
    constraints: List[Dict[str, str]] = field(default_factory=list)
    inheritance: Optional[str] = None
    comment: str = ""
    
    def get_qualified_name(self) -> str:
        """获取完全限定表名"""
        return f"{self.schema_name}.{self.name}"
    
    def to_create_sql(self) -> str:
        """生成CREATE TABLE SQL"""
        lines = [f'CREATE TABLE {self.get_qualified_name()} (']
        
        # 添加列定义
        column_defs = []
        for col in self.columns:
            column_defs.append(f'    {col.to_sql()}')
        
        # 添加主键
        if self.primary_keys:
            pk_cols = ', '.join(f'"{pk}"' for pk in self.primary_keys)
            column_defs.append(f'    PRIMARY KEY ({pk_cols})')
        
        lines.append(',\n'.join(column_defs))
        lines.append(')')
        
        # 继承
        if self.inheritance:
            lines.append(f'INHERITS ({self.inheritance})')
        
        sql = '\n'.join(lines) + ';'
        
        # 添加注释
        if self.comment:
            sql += f"\nCOMMENT ON TABLE {self.get_qualified_name()} IS '{self.comment}';"
        
        return sql

class UML25SchemaGenerator:
    """UML2.5 Schema生成器"""
    
    def __init__(self, db_pool: asyncpg.Pool, config: Dict[str, Any] = None):
        """
        初始化Schema生成器
        
        Args:
            db_pool: 数据库连接池
            config: 配置参数
        """
        self.db_pool = db_pool
        self.config = config or {}
        self.uml_metamodel: Dict[str, Any] = {}
        self.generated_tables: Dict[str, TableDefinition] = {}
        self.type_mappings: Dict[str, str] = {}
        self.schema_name = "uml25_base"
        
        # 初始化类型映射
        self._init_type_mappings()
        
        # 生成统计
        self.generation_stats = {
            'tables_generated': 0,
            'columns_generated': 0,
            'constraints_generated': 0,
            'indexes_generated': 0,
            'generation_time': 0.0,
            'errors': []
        }
    
    def _init_type_mappings(self):
        """初始化UML到PostgreSQL类型映射"""
        self.type_mappings = {
            # UML基本类型
            'Boolean': 'BOOLEAN',
            'Integer': 'INTEGER',
            'UnlimitedNatural': 'INTEGER',
            'String': 'TEXT',
            'Real': 'NUMERIC',
            'DateTime': 'TIMESTAMP WITH TIME ZONE',
            'URI': 'TEXT',
            'Any': 'JSONB',
            
            # 扩展类型
            'UUID': 'UUID',
            'JSON': 'JSONB',
            'Array': 'TEXT[]',
            'Blob': 'BYTEA',
            
            # 默认类型
            'default': 'TEXT'
        }
    
    async def generate_schema_from_xmi(self, xmi_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        从XMI解析结果生成Schema
        
        Args:
            xmi_result: UML2.5 XMI解析结果
            
        Returns:
            Schema生成结果
        """
        import time
        start_time = time.time()
        
        try:
            logger.info("开始生成UML2.5基础Schema...")
            
            # 1. 保存元模型数据
            self.uml_metamodel = xmi_result
            
            # 2. 创建Schema命名空间
            await self._create_schema_namespace()
            
            # 3. 生成核心抽象基础表
            await self._generate_core_tables()
            
            # 4. 生成具体元类表
            await self._generate_metaclass_tables()
            
            # 5. 生成关联表
            await self._generate_association_tables()
            
            # 6. 创建继承关系
            await self._create_inheritance_structure()
            
            # 7. 创建索引和约束
            await self._create_indexes_and_constraints()
            
            # 8. 生成扩展支持
            await self._create_extension_support()
            
            self.generation_stats['generation_time'] = time.time() - start_time
            
            logger.info(f"Schema生成完成:")
            logger.info(f"  - 表数量: {self.generation_stats['tables_generated']}")
            logger.info(f"  - 列数量: {self.generation_stats['columns_generated']}")
            logger.info(f"  - 约束数量: {self.generation_stats['constraints_generated']}")
            logger.info(f"  - 生成时间: {self.generation_stats['generation_time']:.2f}秒")
            
            return await self._build_generation_result()
            
        except Exception as e:
            self.generation_stats['errors'].append(str(e))
            logger.error(f"Schema生成失败: {e}")
            raise
    
    async def _create_schema_namespace(self):
        """创建Schema命名空间"""
        async with self.db_pool.acquire() as conn:
            await conn.execute(f'CREATE SCHEMA IF NOT EXISTS {self.schema_name}')
            
            # 创建基础扩展
            await conn.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
            await conn.execute('CREATE EXTENSION IF NOT EXISTS "hstore"')
            
            logger.info(f"创建Schema命名空间: {self.schema_name}")
    
    async def _generate_core_tables(self):
        """生成核心抽象基础表"""
        
        # 1. 根元素表 (UML::Element)
        element_table = TableDefinition(
            name="uml_element",
            schema_name=self.schema_name,
            comment="UML Element元类 - 所有UML元素的抽象根"
        )
        
        element_table.columns = [
            TableColumn("id", "UUID", False, "uuid_generate_v4()", True, 
                       comment="元素唯一标识"),
            TableColumn("xmi_id", "TEXT", True, None, False, True,
                       comment="XMI标识符"),
            TableColumn("name", "TEXT", True, comment="元素名称"),
            TableColumn("qualified_name", "TEXT", True, comment="限定名称"),
            TableColumn("element_type", "TEXT", False, comment="具体元素类型"),
            TableColumn("namespace", "TEXT", True, comment="命名空间"),
            TableColumn("package_path", "TEXT", True, comment="包路径"),
            TableColumn("documentation", "TEXT", True, comment="文档说明"),
            TableColumn("stereotype", "TEXT", True, comment="构造型"),
            TableColumn("properties", "JSONB", True, "'{}'", comment="扩展属性"),
            TableColumn("created_at", "TIMESTAMP WITH TIME ZONE", False, "CURRENT_TIMESTAMP"),
            TableColumn("updated_at", "TIMESTAMP WITH TIME ZONE", False, "CURRENT_TIMESTAMP")
        ]
        element_table.primary_keys = ["id"]
        
        await self._create_table(element_table)
        
        # 2. 命名元素表 (UML::NamedElement)
        named_element_table = TableDefinition(
            name="uml_named_element",
            schema_name=self.schema_name,
            inheritance=f"{self.schema_name}.uml_element",
            comment="UML NamedElement元类 - 具有名称的元素"
        )
        
        named_element_table.columns = [
            TableColumn("visibility", "TEXT", False, "'public'", comment="可见性"),
            TableColumn("qualified_name_delimiter", "TEXT", False, "'::'", comment="限定名分隔符")
        ]
        
        await self._create_table(named_element_table)
        
        # 3. 分类器表 (UML::Classifier)  
        classifier_table = TableDefinition(
            name="uml_classifier",
            schema_name=self.schema_name,
            inheritance=f"{self.schema_name}.uml_named_element",
            comment="UML Classifier元类 - 分类器基础"
        )
        
        classifier_table.columns = [
            TableColumn("is_abstract", "BOOLEAN", False, "false", comment="是否抽象"),
            TableColumn("is_final_specialization", "BOOLEAN", False, "false", comment="是否最终特化"),
            TableColumn("is_leaf", "BOOLEAN", False, "false", comment="是否叶节点"),
            TableColumn("attributes_count", "INTEGER", False, "0", comment="拥有属性数量"),
            TableColumn("operations_count", "INTEGER", False, "0", comment="拥有操作数量"),
            TableColumn("generalizations", "JSONB", True, "'[]'", comment="泛化关系"),
            TableColumn("specializations", "JSONB", True, "'[]'", comment="特化关系")
        ]
        
        await self._create_table(classifier_table)
        
        # 4. 特征表 (UML::Feature)
        feature_table = TableDefinition(
            name="uml_feature",
            schema_name=self.schema_name,
            inheritance=f"{self.schema_name}.uml_named_element", 
            comment="UML Feature元类 - 特征基础"
        )
        
        feature_table.columns = [
            TableColumn("is_static", "BOOLEAN", False, "false", comment="是否静态"),
            TableColumn("featuring_classifier_id", "UUID", True, comment="拥有该特征的分类器")
        ]
        
        feature_table.foreign_keys = [
            {"column": "featuring_classifier_id", "references": f"{self.schema_name}.uml_classifier(id)"}
        ]
        
        await self._create_table(feature_table)
        
        # 5. 关系表 (UML::Relationship)
        relationship_table = TableDefinition(
            name="uml_relationship", 
            schema_name=self.schema_name,
            inheritance=f"{self.schema_name}.uml_element",
            comment="UML Relationship元类 - 关系基础"
        )
        
        relationship_table.columns = [
            TableColumn("related_elements", "JSONB", False, "'[]'", comment="相关元素列表"),
            TableColumn("relationship_type", "TEXT", False, comment="关系类型")
        ]
        
        await self._create_table(relationship_table)
        
        logger.info("核心抽象基础表生成完成")
    
    async def _generate_metaclass_tables(self):
        """基于xmi:type定义生成元类表结构"""
        metaclasses = self.uml_metamodel.get('data', {}).get('metaclasses', {})
        
        if not metaclasses:
            logger.warning("没有找到元类定义，无法生成表结构")
            return
        
        logger.info(f"开始为{len(metaclasses)}个xmi:type生成表结构...")
        
        # 为每个元类类型生成对应的表
        for metaclass_id, metaclass_data in metaclasses.items():
            await self._generate_metaclass_type_table(metaclass_id, metaclass_data)
        
        logger.info(f"元类表结构生成完成，共{len(metaclasses)}个表")
    
    async def _generate_metaclass_type_table(self, metaclass_id: str, metaclass_data: Dict[str, Any]):
        """为特定的xmi:type生成表结构"""
        try:
            name = metaclass_data.get('name', '').lower()
            qualified_name = metaclass_data.get('qualified_name', '')
            element_type = metaclass_data.get('element_type', '')
            
            # 生成表名：基于类型名称
            if ':' in qualified_name:
                # uml:Class -> uml_class
                namespace, type_name = qualified_name.split(':', 1)
                table_name = f"{namespace.lower()}_{type_name.lower()}"
            else:
                table_name = f"uml_{name.lower()}"
            
            # 确定继承关系
            inheritance_table = self._determine_type_inheritance(qualified_name)
            
            table_def = TableDefinition(
                name=table_name,
                schema_name=self.schema_name,
                inheritance=inheritance_table,
                comment=f"元类表: {qualified_name} - 基于xmi:type定义生成"
            )
            
            # 只有当没有继承关系时才添加基础字段
            if not inheritance_table:
                table_def.columns = [
                    TableColumn("id", "UUID", False, "uuid_generate_v4()", True, 
                               comment="元素唯一标识"),
                    TableColumn("xmi_id", "TEXT", True, None, False, True,
                               comment="XMI标识符"),
                    TableColumn("name", "TEXT", True, comment="元素名称"),
                ]
                table_def.primary_keys = ["id"]
            
            # 根据元类属性定义添加列（排除继承的字段）
            await self._add_columns_from_metaclass_attributes(table_def, metaclass_data, inheritance_table)
            
            # 只对非继承表添加元数据列
            if not inheritance_table:
                table_def.columns.extend([
                    TableColumn("namespace", "TEXT", True, comment="命名空间"),
                    TableColumn("package_path", "TEXT", True, comment="包路径"),
                    TableColumn("documentation", "TEXT", True, comment="文档说明"),
                    TableColumn("stereotype", "TEXT", True, comment="构造型"),
                    TableColumn("properties", "JSONB", True, "'{}'", comment="扩展属性"),
                    TableColumn("created_at", "TIMESTAMP WITH TIME ZONE", False, "CURRENT_TIMESTAMP"),
                    TableColumn("updated_at", "TIMESTAMP WITH TIME ZONE", False, "CURRENT_TIMESTAMP")
                ])
            
            # 创建表
            await self._create_table(table_def)
            
            self.generation_stats['tables_generated'] += 1
            logger.info(f"生成元类表: {table_name} (基于 {qualified_name})")
            
        except Exception as e:
            logger.warning(f"生成元类表失败 {metaclass_data.get('qualified_name', 'Unknown')}: {e}")
            self.generation_stats['errors'].append(f"元类表生成失败: {e}")
    
    def _determine_type_inheritance(self, qualified_name: str) -> Optional[str]:
        """确定类型的继承关系"""
        
        # UML元模型继承层次
        inheritance_mapping = {
            # 具体分类器继承自Classifier
            'uml:Class': f"{self.schema_name}.uml_classifier",
            'uml:Interface': f"{self.schema_name}.uml_classifier", 
            'uml:Enumeration': f"{self.schema_name}.uml_classifier",
            'uml:PrimitiveType': f"{self.schema_name}.uml_classifier",
            'uml:DataType': f"{self.schema_name}.uml_classifier",
            
            # 特征继承自Feature
            'uml:Property': f"{self.schema_name}.uml_feature",
            'uml:Operation': f"{self.schema_name}.uml_feature",
            
            # 关系继承自Relationship
            'uml:Association': f"{self.schema_name}.uml_relationship",
            'uml:Generalization': f"{self.schema_name}.uml_relationship",
            'uml:Dependency': f"{self.schema_name}.uml_relationship",
            
            # 其他直接继承自Element
            'uml:Package': f"{self.schema_name}.uml_element",
            'uml:Comment': f"{self.schema_name}.uml_element",
            'uml:Constraint': f"{self.schema_name}.uml_element",
        }
        
        return inheritance_mapping.get(qualified_name)
    
    async def _add_columns_from_metaclass_attributes(self, table_def: TableDefinition, metaclass_data: Dict[str, Any], inheritance_table: Optional[str] = None):
        """根据元类属性定义添加表列"""
        attributes = metaclass_data.get('attributes', [])
        qualified_name = metaclass_data.get('qualified_name', '')
        
        # 定义继承字段集合，避免重复定义
        inherited_fields = set()
        if inheritance_table:
            # 根据继承关系确定已继承的字段
            if 'uml_element' in inheritance_table:
                inherited_fields.update(['id', 'xmi_id', 'name', 'qualified_name', 'element_type', 
                                       'namespace', 'package_path', 'documentation', 'stereotype', 
                                       'properties', 'created_at', 'updated_at'])
            if 'uml_named_element' in inheritance_table:
                inherited_fields.update(['visibility', 'qualified_name_delimiter'])
            if 'uml_classifier' in inheritance_table:
                inherited_fields.update(['is_abstract', 'is_final_specialization', 'is_leaf', 
                                       'attributes_count', 'operations_count', 'generalizations', 'specializations'])
            if 'uml_feature' in inheritance_table:
                inherited_fields.update(['is_static', 'featuring_classifier_id'])
            if 'uml_relationship' in inheritance_table:
                inherited_fields.update(['related_elements', 'relationship_type'])
        
        for attr in attributes:
            attr_name = attr.get('name', '')
            if not attr_name:
                continue
            
            # 清理属性名称
            column_name = self._clean_column_name(attr_name)
            
            # 跳过已继承的字段
            if column_name in inherited_fields or attr_name in inherited_fields:
                continue
            
            # 检查是否与已存在的列重复
            if any(col.name == column_name for col in table_def.columns):
                continue
            
            # 映射UML类型到PostgreSQL类型
            uml_type = attr.get('type_ref', 'String')
            pg_type = self._map_uml_type_to_postgres(uml_type)
            
            # 确定是否可空
            multiplicity = attr.get('multiplicity', '1')
            nullable = multiplicity.startswith('0') or multiplicity == '0..1'
            
            # 生成默认值
            default_value = self._generate_default_value(uml_type, attr.get('default_value'))
            
            # 创建列定义
            column = TableColumn(
                name=column_name,
                data_type=pg_type,
                nullable=nullable,
                default_value=default_value,
                comment=attr.get('documentation', f"UML属性: {attr_name}")
            )
            
            # 检查是否复合属性
            if attr.get('is_composite', False):
                # 为复合属性添加外键引用
                if uml_type not in ['String', 'Integer', 'Boolean', 'Real']:
                    column.foreign_key = {
                        'references': f"{self.schema_name}.uml_element(id)",
                        'on_delete': 'CASCADE'
                    }
            
            table_def.columns.append(column)
            self.generation_stats['columns_generated'] += 1
    
    def _clean_column_name(self, attr_name: str) -> str:
        """清理列名称，确保符合PostgreSQL命名规范"""
        # 移除前缀
        if attr_name.startswith('attr_'):
            attr_name = attr_name[5:]
        elif attr_name.startswith('xml_'):
            attr_name = attr_name[4:]
        
        # 转换为snake_case
        import re
        column_name = re.sub(r'([A-Z])', r'_\1', attr_name).lower()
        column_name = column_name.strip('_')
        
        # 确保是有效的PostgreSQL标识符
        column_name = re.sub(r'[^a-z0-9_]', '_', column_name)
        
        # 处理特殊字段名映射
        field_mappings = {
            'uml_type': 'element_type_ref',
            'type': 'type_ref',
            'is_abstract': 'is_abstract_element',
            'is_query': 'is_query_operation',
        }
        
        if column_name in field_mappings:
            column_name = field_mappings[column_name]
        
        # 避免PostgreSQL保留字
        reserved_words = ['user', 'order', 'group', 'table', 'index', 'type', 'value', 'constraint']
        if column_name in reserved_words:
            column_name = f"uml_{column_name}"
        
        return column_name
    
    def _generate_default_value(self, uml_type: str, attr_default: Any) -> Optional[str]:
        """生成默认值"""
        if attr_default is not None:
            if uml_type == 'Boolean':
                return str(attr_default).lower()
            elif uml_type in ['Integer', 'Real']:
                return str(attr_default)
            else:
                return f"'{attr_default}'"
        
        # 类型默认值
        type_defaults = {
            'Boolean': 'false',
            'Integer': '0',
            'Real': '0.0',
            'String': "''",
            'Collection': "'[]'",
            'Map': "'{}'"
        }
        
        return type_defaults.get(uml_type)
    
    def _map_uml_type_to_postgres(self, uml_type: str) -> str:
        """映射UML类型到PostgreSQL类型"""
        if not uml_type:
            return self.type_mappings['default']
        
        # 直接映射
        if uml_type in self.type_mappings:
            return self.type_mappings[uml_type]
        
        # 检查是否是引用类型
        if uml_type.startswith('http://'):
            return 'TEXT'  # URI类型
        
        # 检查是否是集合类型
        if uml_type.endswith('[]') or 'Collection' in uml_type:
            base_type = uml_type.replace('[]', '').replace('Collection<', '').replace('>', '')
            return f"{self._map_uml_type_to_postgres(base_type)}[]"
        
        return self.type_mappings['default']
    
    async def _generate_association_tables(self):
        """生成关联表"""
        associations = self.uml_metamodel.get('data', {}).get('associations', {})
        
        for assoc_id, assoc_data in associations.items():
            await self._generate_single_association_table(assoc_id, assoc_data)
        
        logger.info(f"生成关联表完成，共{len(associations)}个关联表")
    
    async def _generate_single_association_table(self, assoc_id: str, assoc_data: Dict[str, Any]):
        """生成单个关联表"""
        try:
            name = assoc_data.get('name', assoc_id)
            table_name = f"assoc_{name.lower()}" if name else f"assoc_{assoc_id[:8]}"
            
            table_def = TableDefinition(
                name=table_name,
                schema_name=self.schema_name,
                comment=f"关联表: {name}"
            )
            
            # 基础关联列
            table_def.columns = [
                TableColumn("id", "UUID", False, "uuid_generate_v4()", True),
                TableColumn("source_id", "UUID", False, comment="源元素ID"),
                TableColumn("target_id", "UUID", False, comment="目标元素ID"),
                TableColumn("association_type", "TEXT", False, comment="关联类型"),
                TableColumn("properties", "JSONB", True, "'{}'", comment="关联属性")
            ]
            
            table_def.primary_keys = ["id"]
            table_def.foreign_keys = [
                {"column": "source_id", "references": f"{self.schema_name}.uml_element(id)"},
                {"column": "target_id", "references": f"{self.schema_name}.uml_element(id)"}
            ]
            
            await self._create_table(table_def)
            
        except Exception as e:
            logger.warning(f"生成关联表失败: {e}")
    
    async def _create_inheritance_structure(self):
        """创建继承结构支持"""
        async with self.db_pool.acquire() as conn:
            
            # 继承关系表
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema_name}.uml_generalization (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    child_id UUID NOT NULL REFERENCES {self.schema_name}.uml_element(id),
                    parent_id UUID NOT NULL REFERENCES {self.schema_name}.uml_element(id),
                    is_substitutable BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(child_id, parent_id)
                );
                
                COMMENT ON TABLE {self.schema_name}.uml_generalization IS 'UML继承关系表';
            """)
            
            # 插入继承关系数据
            await self._populate_inheritance_data(conn)
        
        logger.info("继承结构创建完成")
    
    async def _populate_inheritance_data(self, conn: asyncpg.Connection):
        """填充继承关系数据"""
        metaclasses = self.uml_metamodel.get('data', {}).get('metaclasses', {})
        
        for metaclass_id, metaclass_data in metaclasses.items():
            super_classes = metaclass_data.get('super_classes', [])
            
            for super_id in super_classes:
                if super_id in metaclasses:
                    try:
                        await conn.execute(f"""
                            INSERT INTO {self.schema_name}.uml_generalization 
                            (child_id, parent_id)
                            SELECT 
                                (SELECT id FROM {self.schema_name}.uml_element WHERE xmi_id = $1),
                                (SELECT id FROM {self.schema_name}.uml_element WHERE xmi_id = $2)
                            WHERE EXISTS (SELECT 1 FROM {self.schema_name}.uml_element WHERE xmi_id = $1)
                            AND EXISTS (SELECT 1 FROM {self.schema_name}.uml_element WHERE xmi_id = $2)
                            ON CONFLICT (child_id, parent_id) DO NOTHING
                        """, metaclass_id, super_id)
                    except Exception as e:
                        logger.warning(f"插入继承关系失败 {metaclass_id} -> {super_id}: {e}")
    
    async def _create_indexes_and_constraints(self):
        """创建索引和约束"""
        async with self.db_pool.acquire() as conn:
            
            # 基础索引
            indexes = [
                f"CREATE INDEX IF NOT EXISTS idx_uml_element_type ON {self.schema_name}.uml_element(element_type);",
                f"CREATE INDEX IF NOT EXISTS idx_uml_element_name ON {self.schema_name}.uml_element(name);",
                f"CREATE INDEX IF NOT EXISTS idx_uml_element_package ON {self.schema_name}.uml_element(package_path);",
                f"CREATE INDEX IF NOT EXISTS idx_uml_element_xmi_id ON {self.schema_name}.uml_element(xmi_id);",
                f"CREATE INDEX IF NOT EXISTS idx_uml_property_owner ON {self.schema_name}.uml_property(owner_id);",
                f"CREATE INDEX IF NOT EXISTS idx_uml_operation_owner ON {self.schema_name}.uml_operation(owner_id);",
                f"CREATE INDEX IF NOT EXISTS idx_uml_generalization_child ON {self.schema_name}.uml_generalization(child_id);",
                f"CREATE INDEX IF NOT EXISTS idx_uml_generalization_parent ON {self.schema_name}.uml_generalization(parent_id);"
            ]
            
            for index_sql in indexes:
                try:
                    await conn.execute(index_sql)
                    self.generation_stats['indexes_generated'] += 1
                except Exception as e:
                    logger.warning(f"创建索引失败: {e}")
            
            # JSONB索引
            jsonb_indexes = [
                f"CREATE INDEX IF NOT EXISTS idx_uml_element_properties ON {self.schema_name}.uml_element USING GIN(properties);",
                f"CREATE INDEX IF NOT EXISTS idx_uml_operation_parameters ON {self.schema_name}.uml_operation USING GIN(parameters);"
            ]
            
            for index_sql in jsonb_indexes:
                try:
                    await conn.execute(index_sql)
                    self.generation_stats['indexes_generated'] += 1
                except Exception as e:
                    logger.warning(f"创建JSONB索引失败: {e}")
        
        logger.info(f"创建索引完成，共{self.generation_stats['indexes_generated']}个索引")
    
    async def _create_extension_support(self):
        """创建扩展支持"""
        async with self.db_pool.acquire() as conn:
            
            # 扩展域注册表
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema_name}.domain_extensions (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    domain_name TEXT NOT NULL UNIQUE,
                    base_metaclass_id UUID REFERENCES {self.schema_name}.uml_element(id),
                    extension_schema TEXT NOT NULL,
                    extension_config JSONB DEFAULT '{{}}',
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                
                COMMENT ON TABLE {self.schema_name}.domain_extensions IS 'Domain扩展注册表';
            """)
            
            # 扩展元类表
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema_name}.extended_metaclasses (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    domain_id UUID NOT NULL REFERENCES {self.schema_name}.domain_extensions(id),
                    base_metaclass_id UUID NOT NULL REFERENCES {self.schema_name}.uml_element(id),
                    extended_name TEXT NOT NULL,
                    extension_properties JSONB DEFAULT '{{}}',
                    table_name TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                
                COMMENT ON TABLE {self.schema_name}.extended_metaclasses IS '扩展元类定义表';
            """)
            
            # 创建扩展管理函数
            await self._create_extension_functions(conn)
        
        logger.info("扩展支持创建完成")
    
    async def _create_extension_functions(self, conn: asyncpg.Connection):
        """创建扩展管理函数"""
        
        # 注册Domain扩展函数
        await conn.execute(f"""
            CREATE OR REPLACE FUNCTION {self.schema_name}.register_domain_extension(
                p_domain_name TEXT,
                p_extension_schema TEXT,
                p_base_metaclass_name TEXT DEFAULT NULL
            ) RETURNS UUID AS $$
            DECLARE
                v_domain_id UUID;
                v_base_metaclass_id UUID;
            BEGIN
                -- 查找基础元类
                IF p_base_metaclass_name IS NOT NULL THEN
                    SELECT id INTO v_base_metaclass_id 
                    FROM {self.schema_name}.uml_element 
                    WHERE name = p_base_metaclass_name 
                    LIMIT 1;
                END IF;
                
                -- 插入域扩展
                INSERT INTO {self.schema_name}.domain_extensions 
                (domain_name, base_metaclass_id, extension_schema)
                VALUES (p_domain_name, v_base_metaclass_id, p_extension_schema)
                RETURNING id INTO v_domain_id;
                
                RETURN v_domain_id;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        # 创建扩展元类函数
        await conn.execute(f"""
            CREATE OR REPLACE FUNCTION {self.schema_name}.create_extended_metaclass(
                p_domain_name TEXT,
                p_base_metaclass_name TEXT,
                p_extended_name TEXT,
                p_extension_properties JSONB DEFAULT '{{}}'
            ) RETURNS UUID AS $$
            DECLARE
                v_extended_id UUID;
                v_domain_id UUID;
                v_base_id UUID;
                v_table_name TEXT;
            BEGIN
                -- 获取域ID
                SELECT id INTO v_domain_id 
                FROM {self.schema_name}.domain_extensions 
                WHERE domain_name = p_domain_name;
                
                IF v_domain_id IS NULL THEN
                    RAISE EXCEPTION 'Domain not found: %', p_domain_name;
                END IF;
                
                -- 获取基础元类ID
                SELECT id INTO v_base_id 
                FROM {self.schema_name}.uml_element 
                WHERE name = p_base_metaclass_name;
                
                IF v_base_id IS NULL THEN
                    RAISE EXCEPTION 'Base metaclass not found: %', p_base_metaclass_name;
                END IF;
                
                -- 生成表名
                v_table_name := lower(p_domain_name) || '_' || lower(p_extended_name);
                
                -- 插入扩展元类
                INSERT INTO {self.schema_name}.extended_metaclasses 
                (domain_id, base_metaclass_id, extended_name, extension_properties, table_name)
                VALUES (v_domain_id, v_base_id, p_extended_name, p_extension_properties, v_table_name)
                RETURNING id INTO v_extended_id;
                
                RETURN v_extended_id;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        logger.info("扩展管理函数创建完成")
    
    async def _create_table(self, table_def: TableDefinition):
        """创建数据库表 - 支持IF NOT EXISTS和清理选项"""
        table_name = table_def.name
        
        try:
            async with self.db_pool.acquire() as conn:
                # 如果配置了清理模式，先删除现有表
                if self.config.get('drop_existing_tables', False):
                    try:
                        await conn.execute(f"DROP TABLE IF EXISTS {self.schema_name}.{table_name} CASCADE")
                        logger.info(f"已删除现有表: {self.schema_name}.{table_name}")
                    except Exception as e:
                        logger.warning(f"删除表失败: {e}")
                
                # 检查表是否已存在
                table_exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = $1 AND table_name = $2
                    )
                """, self.schema_name, table_name)
                
                if table_exists:
                    logger.info(f"表已存在，跳过创建: {self.schema_name}.{table_name}")
                    self.generated_tables[table_name] = table_def
                    return
                
                # 构建CREATE TABLE语句
                columns_sql = []
                for col in table_def.columns:
                    column_sql = f'"{col.name}" {col.data_type}'
                    if not col.nullable:
                        column_sql += " NOT NULL"
                    if col.default_value:
                        column_sql += f" DEFAULT {col.default_value}"
                    if col.unique:
                        column_sql += " UNIQUE"
                    columns_sql.append(column_sql)
                
                # 添加主键（仅对非继承表）
                if table_def.primary_keys and not table_def.inheritance:
                    pk_cols = ', '.join(f'"{pk}"' for pk in table_def.primary_keys)
                    columns_sql.append(f"PRIMARY KEY ({pk_cols})")
                
                # 构建表SQL
                if columns_sql:
                    create_sql = f"""
                    CREATE TABLE {self.schema_name}.{table_name} (
                        {','.join(columns_sql)}
                    )
                    """
                else:
                    # 对于纯继承表（没有新增列）
                    create_sql = f"CREATE TABLE {self.schema_name}.{table_name} ()"
                
                # 添加继承关系
                if table_def.inheritance:
                    create_sql += f" INHERITS ({table_def.inheritance})"
                
                await conn.execute(create_sql)
                
                # 添加表注释
                if table_def.comment:
                    comment_sql = f"""
                    COMMENT ON TABLE {self.schema_name}.{table_name} 
                    IS '{table_def.comment}'
                    """
                    await conn.execute(comment_sql)
                
                # 添加外键约束（如果有）
                for fk in table_def.foreign_keys:
                    try:
                        fk_sql = f"""
                        ALTER TABLE {self.schema_name}.{table_name}
                        ADD CONSTRAINT fk_{table_name}_{fk['column']}
                        FOREIGN KEY ({fk['column']}) REFERENCES {fk['references']}
                        ON DELETE CASCADE
                        """
                        await conn.execute(fk_sql)
                    except Exception as e:
                        logger.warning(f"添加外键约束失败: {e}")
                
                self.generated_tables[table_name] = table_def
                logger.debug(f"创建表成功: {self.schema_name}.{table_name}")
                
                # 添加列注释
                for col in table_def.columns:
                    if col.comment:
                        try:
                            await conn.execute(f"""
                                COMMENT ON COLUMN {self.schema_name}.{table_name}."{col.name}" 
                                IS '{col.comment}'
                            """)
                        except Exception as e:
                            # 列可能来自继承表，忽略注释错误
                            pass
                
                self.generation_stats['tables_generated'] += 1
                self.generation_stats['columns_generated'] += len(table_def.columns)
                
        except Exception as e:
            if "已经存在" in str(e) or "already exists" in str(e):
                logger.info(f"表已存在，跳过创建: {self.schema_name}.{table_name}")
                self.generated_tables[table_name] = table_def
            else:
                logger.error(f"创建表失败 {self.schema_name}.{table_name}: {e}")
                self.generation_stats['errors'].append(f"创建表失败 {table_name}: {e}")
                # 不要重新抛出异常，继续处理其他表
    
    async def _build_generation_result(self) -> Dict[str, Any]:
        """构建生成结果"""
        return {
            'metadata': {
                'generator_version': '1.0.0',
                'generation_timestamp': datetime.now().isoformat(),
                'schema_name': self.schema_name,
                'source_standard': 'UML 2.5'
            },
            'statistics': self.generation_stats,
            'generated_tables': {
                name: {
                    'qualified_name': table.get_qualified_name(),
                    'column_count': len(table.columns),
                    'inheritance': table.inheritance,
                    'comment': table.comment
                }
                for name, table in self.generated_tables.items()
            },
            'extension_support': {
                'domain_registry_table': f"{self.schema_name}.domain_extensions",
                'extended_metaclass_table': f"{self.schema_name}.extended_metaclasses",
                'extension_functions': [
                    f"{self.schema_name}.register_domain_extension",
                    f"{self.schema_name}.create_extended_metaclass"
                ]
            },
            'type_mappings': self.type_mappings,
            'usage_guide': {
                'register_domain': f"SELECT {self.schema_name}.register_domain_extension('domain_name', 'schema_name')",
                'extend_metaclass': f"SELECT {self.schema_name}.create_extended_metaclass('domain', 'Class', 'MyClass')"
            }
        }

# 便捷函数
async def generate_uml25_schema(
    db_pool: asyncpg.Pool, 
    xmi_result: Dict[str, Any],
    config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    便捷函数：生成UML2.5基础Schema
    
    Args:
        db_pool: 数据库连接池
        xmi_result: XMI解析结果
        config: 可选配置参数
        
    Returns:
        Schema生成结果
    """
    generator = UML25SchemaGenerator(db_pool, config)
    return await generator.generate_schema_from_xmi(xmi_result)

def create_schema_generator(db_pool: asyncpg.Pool, config: Dict[str, Any] = None) -> UML25SchemaGenerator:
    """
    创建Schema生成器实例
    
    Args:
        db_pool: 数据库连接池
        config: 可选配置参数
        
    Returns:
        生成器实例
    """
    return UML25SchemaGenerator(db_pool, config)

# 导出接口
__all__ = [
    'UML25SchemaGenerator',
    'TableDefinition',
    'TableColumn',
    'generate_uml25_schema',
    'create_schema_generator'
] 