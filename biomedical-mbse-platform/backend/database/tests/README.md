# Tests

本目录包含测试和验证脚本。

## 📁 文件说明

### 验证脚本
- **`verify_structure.py`** - 项目结构验证脚本
  - 检查Python文件的语法正确性
  - 验证导入依赖的可用性
  - 检查类和方法的完整性
  - 生成项目结构报告

## 🎯 验证功能

### 代码结构验证
- **文件存在性检查** - 验证关键文件是否存在
- **语法解析验证** - 检查Python代码语法
- **类和方法提取** - 分析代码结构
- **依赖关系检查** - 验证import语句的正确性

### 特定模块验证
- **动态领域示例** - 验证`dynamic_domain_example.py`
- **使用示例** - 验证`example_usage.py`
- **领域管理器** - 验证`domain_managers/`模块
- **配置示例** - 验证各种配置和依赖

### 依赖检查
- **Python包** - asyncpg, bcrypt, pydantic等
- **内置模块** - asyncio, json, logging等
- **自定义模块** - 项目内部模块依赖

## 🚀 运行验证

```bash
# 运行完整的结构验证
python verify_structure.py

# 验证特定功能模块
python -c "
import verify_structure
verify_structure.verify_dynamic_domain_example()
verify_structure.verify_domain_managers()
"
```

## 📋 验证报告

### 验证项目
- ✅ **语法检查** - 所有Python文件语法正确
- ✅ **导入验证** - 所有依赖包可正常导入
- ✅ **结构完整性** - 关键类和方法存在
- ✅ **配置有效性** - 配置文件格式正确

### 常见问题检查
- **缺失依赖** - 检查是否有未安装的Python包
- **导入错误** - 检查模块路径是否正确
- **语法错误** - 检查代码语法问题
- **版本兼容性** - 检查Python版本兼容性

## 📊 验证结果

```
📊 验证结果摘要:
✅ 文件存在: 15/15
✅ 语法正确: 15/15  
✅ 导入成功: 12/15
❌ 导入失败: 3/15 (缺少依赖包)
✅ 类结构: 25/25
✅ 方法完整: 150/150
```

## 🛠️ 添加新测试

### 添加文件验证
```python
def verify_new_module():
    """验证新模块"""
    filepath = "path/to/new_module.py"
    
    # 检查文件存在
    if not check_file_exists(filepath):
        return False
    
    # 解析语法
    tree = parse_python_file(filepath)
    if not tree:
        return False
    
    # 提取结构
    structure = extract_classes_and_methods(tree)
    
    return True
```

### 添加依赖检查
```python
def check_new_dependency():
    """检查新依赖"""
    try:
        import new_package
        return True
    except ImportError:
        return False
```

## 📚 相关文档

验证脚本确保以下文档中描述的功能正常工作：
- dependency_installation_summary.md
- verification_report.md 