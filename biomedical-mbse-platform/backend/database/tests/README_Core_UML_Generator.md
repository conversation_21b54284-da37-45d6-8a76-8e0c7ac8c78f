# 核心UML元类定义生成器和测试套件

基于UML 2.5标准的核心元类定义表生成器，提供完整的继承关系映射、数据验证和测试功能。

## 🎯 概述

本系统基于现有的`improved_inheritance_mapping.py`实现，生成一个包含所有核心UML元类信息的完整定义表，作为整个数据库映射系统的核心数据结构。

### 核心功能

- **🏗️ 元类定义生成** - 从继承映射提取完整的UML类型定义
- **📊 多格式导出** - 支持JSON、CSV、SQL、Markdown格式
- **🗄️ 数据库集成** - 自动创建表结构并填充数据
- **🧪 全面测试** - 包含10项测试验证数据正确性
- **📈 性能分析** - 生成性能报告和统计信息

## 📁 文件结构

```
database/
├── core_uml_metaclass_generator.py      # 核心生成器
├── run_core_uml_generator.py            # 运行脚本
├── tests/
│   ├── test_core_uml_metaclass_definitions.py  # 测试套件
│   └── README_Core_UML_Generator.md     # 本文档
├── improved_inheritance_mapping.py      # 继承映射基础
└── output/                              # 生成输出目录
    ├── core_uml_metaclass_definitions.json
    ├── core_uml_metaclass_definitions.csv
    ├── core_uml_metaclass_definitions.sql
    └── core_uml_metaclass_summary.md
```

## 🚀 快速开始

### 1. 基础生成

```bash
# 生成核心UML元类定义表
python run_core_uml_generator.py

# 指定输出目录
python run_core_uml_generator.py --output-dir ./my_output

# 仅查看继承关系分析
python run_core_uml_generator.py --show-inheritance
```

### 2. 包含测试验证

```bash
# 生成并运行完整测试
python run_core_uml_generator.py --test

# 包含数据库测试
python run_core_uml_generator.py --test --database
```

### 3. 直接运行测试

```bash
# 运行基础测试
python tests/test_core_uml_metaclass_definitions.py

# 运行包含数据库的测试
python tests/test_core_uml_metaclass_definitions.py --database --verbose
```

## 📋 生成的数据结构

### 核心字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `metaclass_id` | TEXT | 元类唯一标识 (如: element_metaclass) |
| `qualified_name` | TEXT | 完全限定名称 (如: uml:Element) |
| `simple_name` | TEXT | 简单名称 (如: Element) |
| `namespace` | TEXT | 命名空间 (如: uml) |
| `is_abstract` | BOOLEAN | 是否抽象类 |
| `parent_qualified_name` | TEXT | 父类完全限定名称 |
| `inheritance_level` | INTEGER | 继承层级 (0-4) |
| `table_strategy` | TEXT | 表实现策略 (inherit/view/relation) |
| `inheritance_chain` | JSONB | 完整继承链 |
| `children_count` | INTEGER | 直接子类数量 |
| `descendants_count` | INTEGER | 所有后代数量 |
| `concrete_descendants` | JSONB | 具体实现的后代类列表 |
| `domain_category` | TEXT | 领域分类 |
| `implementation_priority` | INTEGER | 实现优先级 (1-10) |

### 示例数据

```json
{
  "metaclass_id": "class_metaclass",
  "qualified_name": "uml:Class",
  "simple_name": "Class",
  "namespace": "uml",
  "is_abstract": false,
  "parent_qualified_name": "uml:Classifier",
  "inheritance_level": 4,
  "inheritance_level_name": "CONCRETE",
  "table_strategy": "view",
  "description": "类元素",
  "inheritance_chain": ["uml:Element", "uml:NamedElement", "uml:Type", "uml:Classifier", "uml:Class"],
  "children_count": 0,
  "descendants_count": 0,
  "concrete_descendants": [],
  "domain_category": "structural.classifiers",
  "implementation_priority": 6
}
```

## 🧪 测试套件详解

### 测试类别

#### 1. 基础数据测试 (`TestCoreUMLMetaclassDefinitions`)

- **测试1: 数据完整性** - 验证必填字段、唯一性约束
- **测试2: 继承关系一致性** - 验证父子关系、继承链正确性
- **测试3: 表策略分配** - 验证策略选择逻辑正确性
- **测试4: 领域分类** - 验证领域分类准确性
- **测试5: 优先级分配** - 验证实现优先级合理性
- **测试6: 子类后代计数** - 验证计数统计正确性
- **测试7: 具体后代列表** - 验证具体后代列表准确性

#### 2. 数据库集成测试 (`TestDatabaseIntegration`)

- **测试8: 数据库表创建** - 验证表结构和索引创建
- **测试9: 数据插入** - 验证数据完整性和约束

#### 3. 性能测试 (`TestPerformance`)

- **测试10: 生成性能** - 验证生成时间和内存使用

### 运行测试示例

```bash
# 运行基础测试
python tests/test_core_uml_metaclass_definitions.py

# 运行详细测试
python tests/test_core_uml_metaclass_definitions.py --verbose

# 包含数据库测试 (需要PostgreSQL)
python tests/test_core_uml_metaclass_definitions.py --database --verbose
```

### 测试结果解读

```
🧪 核心UML元类定义表测试套件
============================================================

🧪 初始化测试环境
   生成了 29 个元类定义
   测试输出目录: ./test_output

🔍 测试1: 数据完整性验证
   ✅ 元类数量正确: 29
   ✅ 所有必填字段完整
   ✅ 所有标识符唯一

🌳 测试2: 继承关系一致性验证
   ✅ 父子关系正确
   ✅ 继承链正确
   ✅ 根节点正确

[...更多测试结果...]

📊 测试报告
========================================
总测试数: 10
成功: 10
失败: 0
错误: 0

✅ 所有测试通过!
```

## 📊 生成统计示例

基于UML 2.5标准，系统将生成如下统计信息：

### 继承层级分布
- **ROOT**: 1个 (3.4%) - Element
- **CORE**: 2个 (6.9%) - NamedElement, Relationship  
- **ABSTRACT**: 5个 (17.2%) - Type, Namespace, Feature等
- **SPECIALIZED**: 3个 (10.3%) - Classifier, StructuralFeature等
- **CONCRETE**: 18个 (62.1%) - Class, Property, Operation等

### 表策略分布
- **inherit**: 11个 (37.9%) - 抽象类使用表继承
- **view**: 14个 (48.3%) - 具体类使用视图策略
- **relation**: 4个 (13.8%) - 关系类使用关系表

### 领域分类分布
- **core.foundation**: 核心基础类
- **structural.classifiers**: 结构分类器
- **structural.features**: 结构特征
- **behavioral.features**: 行为特征
- **documentation.annotations**: 文档注释

## 🗄️ 数据库集成

### 表结构

生成的数据库表包含完整的约束和索引：

```sql
CREATE TABLE uml25_core.core_uml_metaclass_definitions (
    -- 主键和标识
    metaclass_id TEXT PRIMARY KEY,
    qualified_name TEXT UNIQUE NOT NULL,
    simple_name TEXT NOT NULL,
    namespace TEXT NOT NULL,
    
    -- 基本属性
    is_abstract BOOLEAN NOT NULL,
    parent_qualified_name TEXT,
    inheritance_level INTEGER NOT NULL CHECK (inheritance_level >= 0 AND inheritance_level <= 4),
    table_strategy TEXT NOT NULL CHECK (table_strategy IN ('inherit', 'view', 'relation')),
    
    -- 继承关系信息
    inheritance_chain JSONB NOT NULL,
    children_count INTEGER NOT NULL DEFAULT 0,
    descendants_count INTEGER NOT NULL DEFAULT 0,
    concrete_descendants JSONB NOT NULL,
    
    -- 分类和优先级
    domain_category TEXT NOT NULL,
    implementation_priority INTEGER NOT NULL CHECK (implementation_priority >= 1 AND implementation_priority <= 10),
    
    -- 外键约束
    FOREIGN KEY (parent_qualified_name) 
        REFERENCES uml25_core.core_uml_metaclass_definitions(qualified_name) 
        ON DELETE SET NULL
);
```

### 索引优化

系统自动创建以下索引：

- 继承层级索引
- 表策略索引
- 领域分类索引
- 优先级索引
- 继承链GIN索引

### 数据库配置

在使用数据库功能前，请更新配置：

```python
# 在 core_uml_metaclass_generator.py 或 run_core_uml_generator.py 中
db_config = {
    'host': 'localhost',
    'port': 5432,
    'user': 'your_username',
    'password': 'your_password', 
    'database': 'your_database'
}
```

## 🔧 高级用法

### 1. 编程式使用

```python
from core_uml_metaclass_generator import CoreUMLMetaclassGenerator

# 创建生成器
generator = CoreUMLMetaclassGenerator(schema_name="my_schema")

# 生成定义
definitions = generator.generate_core_metaclass_definitions()

# 筛选具体类
concrete_classes = [d for d in definitions if not d.is_abstract]

# 按优先级排序
sorted_by_priority = sorted(definitions, 
                          key=lambda x: x.implementation_priority, 
                          reverse=True)

# 导出自定义格式
generator.export_to_json(definitions, "custom_output.json")
```

### 2. 自定义领域分类

```python
# 修改生成器的领域分类
generator.domain_categories.update({
    'uml:CustomType': 'custom.domain',
    'uml:SpecialClass': 'special.category'
})
```

### 3. 数据库查询示例

```sql
-- 查询所有抽象类及其子类数量
SELECT qualified_name, children_count, descendants_count
FROM uml25_core.core_uml_metaclass_definitions
WHERE is_abstract = true
ORDER BY descendants_count DESC;

-- 查询特定领域的元类
SELECT qualified_name, table_strategy, implementation_priority
FROM uml25_core.core_uml_metaclass_definitions
WHERE domain_category LIKE 'structural.%'
ORDER BY implementation_priority DESC;

-- 查询继承链包含特定类型的元类
SELECT qualified_name, inheritance_chain
FROM uml25_core.core_uml_metaclass_definitions
WHERE inheritance_chain ? 'uml:Classifier';
```

## 🐛 故障排除

### 常见问题

1. **生成器导入失败**
   ```
   ImportError: No module named 'improved_inheritance_mapping'
   ```
   - 确保在正确的目录运行脚本
   - 检查Python路径设置

2. **数据库连接失败**
   ```
   asyncpg.exceptions.InvalidAuthorizationSpecificationError
   ```
   - 检查数据库配置信息
   - 确保PostgreSQL服务运行
   - 验证用户权限

3. **测试失败**
   ```
   AssertionError: 继承链中的uml:SomeType不存在
   ```
   - 检查继承映射定义是否完整
   - 验证数据一致性

### 调试技巧

```bash
# 启用详细日志
export PYTHONPATH=$PYTHONPATH:$(pwd)
python -v run_core_uml_generator.py --test --verbose

# 检查生成的数据
python -c "
from core_uml_metaclass_generator import CoreUMLMetaclassGenerator
gen = CoreUMLMetaclassGenerator()
defs = gen.generate_core_metaclass_definitions()
print(f'生成了 {len(defs)} 个定义')
for d in defs[:5]:
    print(f'  {d.qualified_name}: {d.table_strategy}')
"
```

## 📈 性能基准

### 预期性能指标

- **生成时间**: < 1秒 (29个元类)
- **内存使用**: < 1MB
- **JSON导出**: < 0.5秒
- **数据库插入**: < 2秒
- **测试执行**: < 10秒 (不含数据库)

### 优化建议

- 对于大量数据，考虑批量插入
- 使用连接池优化数据库性能
- 缓存生成的定义避免重复计算

## 🤝 贡献指南

1. **扩展元类定义**: 在`improved_inheritance_mapping.py`中添加新类型
2. **添加测试用例**: 在测试套件中添加新的验证逻辑
3. **优化性能**: 改进生成算法和数据库操作
4. **增强导出**: 支持新的输出格式

## 📚 相关文档

- [UML 2.5 Inheritance Design Evaluation](../UML25_Inheritance_Design_Evaluation.md)
- [Improved Inheritance Mapping](../improved_inheritance_mapping.py)
- [Schema Documentation](../schemas/README.md)
- [Main Project README](../README.md)

---

**版本**: 1.0.0  
**更新时间**: 2025年1月  
**维护团队**: 生物医学MBSE开发组 