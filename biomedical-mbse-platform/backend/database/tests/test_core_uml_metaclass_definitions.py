#!/usr/bin/env python3
"""
核心UML元类定义表测试套件

验证核心UML元类定义表的正确性，包括：
1. 数据完整性测试
2. 继承关系验证
3. 表策略正确性测试
4. 数据库表结构验证
5. 性能测试
6. 数据一致性验证

使用方法:
    python test_core_uml_metaclass_definitions.py [--database] [--performance] [--verbose]

作者：生物医学MBSE平台开发组
版本：1.0.0
创建时间：2025年1月
"""

import asyncio
import asyncpg
import json
import sys
import os
import unittest
import logging
import time
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from pathlib import Path
from dataclasses import asdict

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_uml_metaclass_generator import (
    CoreUMLMetaclassGenerator,
    CoreUMLMetaclassDefinition
)
from improved_inheritance_mapping import (
    ImprovedUMLInheritanceMapper,
    UMLInheritanceLevel
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestCoreUMLMetaclassDefinitions(unittest.TestCase):
    """核心UML元类定义测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.generator = CoreUMLMetaclassGenerator(schema_name="uml25_test")
        cls.definitions = cls.generator.generate_core_metaclass_definitions()
        cls.mapper = cls.generator.mapper
        
        # 测试输出目录
        cls.test_output_dir = Path("./test_output")
        cls.test_output_dir.mkdir(exist_ok=True)
        
        print(f"\n🧪 初始化测试环境")
        print(f"   生成了 {len(cls.definitions)} 个元类定义")
        print(f"   测试输出目录: {cls.test_output_dir}")
    
    def test_01_data_completeness(self):
        """测试数据完整性"""
        print(f"\n🔍 测试1: 数据完整性验证")
        
        # 验证基本数量
        expected_uml_types = len(self.mapper.inheritance_hierarchy)
        actual_count = len(self.definitions)
        
        self.assertEqual(actual_count, expected_uml_types,
                        f"元类定义数量不匹配: 期望{expected_uml_types}, 实际{actual_count}")
        print(f"   ✅ 元类数量正确: {actual_count}")
        
        # 验证必填字段
        required_fields = [
            'metaclass_id', 'qualified_name', 'simple_name', 'namespace',
            'is_abstract', 'inheritance_level', 'table_strategy', 'description'
        ]
        
        for definition in self.definitions:
            for field in required_fields:
                value = getattr(definition, field)
                self.assertIsNotNone(value, f"{definition.qualified_name}的{field}字段为空")
                if isinstance(value, str):
                    self.assertTrue(len(value.strip()) > 0, 
                                  f"{definition.qualified_name}的{field}字段为空字符串")
        
        print(f"   ✅ 所有必填字段完整")
        
        # 验证唯一性
        qualified_names = [d.qualified_name for d in self.definitions]
        metaclass_ids = [d.metaclass_id for d in self.definitions]
        
        self.assertEqual(len(qualified_names), len(set(qualified_names)),
                        "qualified_name存在重复")
        self.assertEqual(len(metaclass_ids), len(set(metaclass_ids)),
                        "metaclass_id存在重复")
        print(f"   ✅ 所有标识符唯一")
    
    def test_02_inheritance_consistency(self):
        """测试继承关系一致性"""
        print(f"\n🌳 测试2: 继承关系一致性验证")
        
        # 构建定义映射
        definitions_map = {d.qualified_name: d for d in self.definitions}
        
        # 验证父子关系
        for definition in self.definitions:
            if definition.parent_qualified_name:
                parent_name = definition.parent_qualified_name
                
                # 父类必须存在
                self.assertIn(parent_name, definitions_map,
                            f"{definition.qualified_name}的父类{parent_name}不存在")
                
                parent_def = definitions_map[parent_name]
                
                # 在UML中，具体类也可以被继承（如Package -> Model），所以不强制要求父类必须是抽象的
                # 但如果父类是抽象的，那很好；如果是具体的，也是允许的
                
                # 继承层级必须合理（子类层级应该大于等于父类层级）
                self.assertGreaterEqual(definition.inheritance_level, parent_def.inheritance_level,
                                 f"{definition.qualified_name}的继承层级({definition.inheritance_level})不能小于父类{parent_name}的层级({parent_def.inheritance_level})")
        
        print(f"   ✅ 父子关系正确")
        
        # 验证继承链
        for definition in self.definitions:
            chain = definition.inheritance_chain
            self.assertIsInstance(chain, list, f"{definition.qualified_name}的继承链不是列表")
            
            if len(chain) > 1:
                # 最后一个应该是自己
                self.assertEqual(chain[-1], definition.qualified_name,
                               f"{definition.qualified_name}的继承链结尾不正确")
                
                # 第一个应该是根元素
                self.assertEqual(chain[0], 'uml:Element',
                               f"{definition.qualified_name}的继承链根不是Element")
                
                # 链中的每个元素都应该存在
                for qualified_name in chain:
                    self.assertIn(qualified_name, definitions_map,
                                f"继承链中的{qualified_name}不存在")
        
        print(f"   ✅ 继承链正确")
        
        # 验证根节点
        root_elements = [d for d in self.definitions if d.inheritance_level == 0]
        self.assertEqual(len(root_elements), 1, "应该只有一个根元素")
        self.assertEqual(root_elements[0].qualified_name, 'uml:Element', "根元素应该是uml:Element")
        print(f"   ✅ 根节点正确")
        
        # 验证具体类可以作为父类的合理情况
        concrete_parents = []
        for definition in self.definitions:
            if definition.parent_qualified_name:
                parent_def = definitions_map[definition.parent_qualified_name]
                if not parent_def.is_abstract:
                    concrete_parents.append((definition.qualified_name, definition.parent_qualified_name))
        
        if concrete_parents:
            print(f"   ℹ️ 具体类作为父类的情况 ({len(concrete_parents)}个):")
            for child, parent in concrete_parents:
                print(f"      {child} <- {parent}")
        
        print(f"   ✅ 继承关系一致性验证通过")
    
    def test_03_table_strategy_validation(self):
        """测试表策略分配正确性"""
        print(f"\n🗂️ 测试3: 表策略分配验证")
        
        # 统计策略分布
        strategy_stats = {}
        level_strategy_stats = {}
        
        for definition in self.definitions:
            strategy = definition.table_strategy
            level = definition.inheritance_level_name
            
            strategy_stats[strategy] = strategy_stats.get(strategy, 0) + 1
            
            if level not in level_strategy_stats:
                level_strategy_stats[level] = {}
            level_strategy_stats[level][strategy] = level_strategy_stats[level].get(strategy, 0) + 1
        
        # 验证策略有效性
        valid_strategies = {'inherit', 'view', 'relation'}
        for definition in self.definitions:
            self.assertIn(definition.table_strategy, valid_strategies,
                         f"{definition.qualified_name}的表策略{definition.table_strategy}无效")
        
        print(f"   ✅ 所有表策略有效")
        
        # 验证策略分配逻辑
        for definition in self.definitions:
            level = definition.inheritance_level
            strategy = definition.table_strategy
            is_abstract = definition.is_abstract
            
            # 抽象类通常使用inherit策略
            if is_abstract and level <= 3:  # ROOT到SPECIALIZED层级
                self.assertEqual(strategy, 'inherit',
                               f"{definition.qualified_name}作为抽象类应使用inherit策略")
            
            # 具体类通常使用view或relation策略
            if not is_abstract:
                self.assertIn(strategy, {'view', 'relation'},
                            f"{definition.qualified_name}作为具体类应使用view或relation策略")
        
        print(f"   ✅ 策略分配逻辑正确")
        
        # 打印策略统计
        print(f"   📊 策略分布:")
        for strategy, count in sorted(strategy_stats.items()):
            percentage = (count / len(self.definitions)) * 100
            print(f"      {strategy}: {count} ({percentage:.1f}%)")
    
    def test_04_domain_categorization(self):
        """测试领域分类正确性"""
        print(f"\n🏷️ 测试4: 领域分类验证")
        
        # 验证领域分类存在
        domain_stats = {}
        for definition in self.definitions:
            domain = definition.domain_category
            self.assertIsNotNone(domain, f"{definition.qualified_name}的领域分类为空")
            self.assertNotEqual(domain.strip(), '', f"{definition.qualified_name}的领域分类为空字符串")
            
            domain_stats[domain] = domain_stats.get(domain, 0) + 1
        
        print(f"   ✅ 所有元类都有领域分类")
        
        # 验证核心元素的领域分类
        core_elements = {
            'uml:Element': 'core.foundation',
            'uml:NamedElement': 'core.foundation',
            'uml:Relationship': 'core.relationships',
            'uml:Type': 'core.typing',
            'uml:Classifier': 'structural.classifiers'
        }
        
        definitions_map = {d.qualified_name: d for d in self.definitions}
        for element, expected_domain in core_elements.items():
            if element in definitions_map:
                actual_domain = definitions_map[element].domain_category
                self.assertEqual(actual_domain, expected_domain,
                               f"{element}的领域分类应为{expected_domain}, 实际为{actual_domain}")
        
        print(f"   ✅ 核心元素领域分类正确")
        
        # 打印领域分布
        print(f"   📊 领域分布:")
        for domain, count in sorted(domain_stats.items()):
            percentage = (count / len(self.definitions)) * 100
            print(f"      {domain}: {count} ({percentage:.1f}%)")
    
    def test_05_priority_assignment(self):
        """测试实现优先级分配"""
        print(f"\n⚡ 测试5: 实现优先级验证")
        
        # 验证优先级范围
        for definition in self.definitions:
            priority = definition.implementation_priority
            self.assertGreaterEqual(priority, 1, f"{definition.qualified_name}的优先级过低")
            self.assertLessEqual(priority, 10, f"{definition.qualified_name}的优先级过高")
        
        print(f"   ✅ 所有优先级在有效范围内")
        
        # 验证层级与优先级的关系
        level_priorities = {}
        for definition in self.definitions:
            level = definition.inheritance_level
            priority = definition.implementation_priority
            
            if level not in level_priorities:
                level_priorities[level] = []
            level_priorities[level].append(priority)
        
        # 根层级应该有最高优先级
        root_priorities = level_priorities.get(0, [])
        if root_priorities:
            self.assertEqual(max(root_priorities), 10, "根层级应有最高优先级")
        
        print(f"   ✅ 层级与优先级关系正确")
        
        # 打印优先级分布
        priority_stats = {}
        for definition in self.definitions:
            priority = definition.implementation_priority
            priority_stats[priority] = priority_stats.get(priority, 0) + 1
        
        print(f"   📊 优先级分布:")
        for priority in sorted(priority_stats.keys(), reverse=True):
            count = priority_stats[priority]
            print(f"      优先级{priority}: {count}个元类")
    
    def test_06_children_and_descendants_counts(self):
        """测试子类和后代计数正确性"""
        print(f"\n👨‍👩‍👧‍👦 测试6: 子类和后代计数验证")
        
        # 构建实际的父子关系
        definitions_map = {d.qualified_name: d for d in self.definitions}
        actual_children = {}
        actual_descendants = {}
        
        # 计算实际子类
        for definition in self.definitions:
            if definition.parent_qualified_name:
                parent = definition.parent_qualified_name
                if parent not in actual_children:
                    actual_children[parent] = []
                actual_children[parent].append(definition.qualified_name)
        
        # 计算实际后代
        def get_all_descendants(qualified_name: str) -> Set[str]:
            descendants = set()
            children = actual_children.get(qualified_name, [])
            for child in children:
                descendants.add(child)
                descendants.update(get_all_descendants(child))
            return descendants
        
        for qualified_name in definitions_map.keys():
            actual_descendants[qualified_name] = get_all_descendants(qualified_name)
        
        # 验证计数正确性
        errors = []
        for definition in self.definitions:
            qualified_name = definition.qualified_name
            
            # 验证子类计数
            expected_children_count = len(actual_children.get(qualified_name, []))
            if definition.children_count != expected_children_count:
                errors.append(f"{qualified_name}: 子类计数错误 (期望{expected_children_count}, 实际{definition.children_count})")
            
            # 验证后代计数
            expected_descendants_count = len(actual_descendants.get(qualified_name, set()))
            if definition.descendants_count != expected_descendants_count:
                errors.append(f"{qualified_name}: 后代计数错误 (期望{expected_descendants_count}, 实际{definition.descendants_count})")
        
        if errors:
            self.fail(f"子类/后代计数错误:\n" + "\n".join(errors[:10]))  # 只显示前10个错误
        
        print(f"   ✅ 所有子类和后代计数正确")
        
        # 统计信息
        total_children = sum(d.children_count for d in self.definitions)
        total_descendants = sum(d.descendants_count for d in self.definitions)
        max_children = max(d.children_count for d in self.definitions)
        max_descendants = max(d.descendants_count for d in self.definitions)
        
        print(f"   📊 子类后代统计:")
        print(f"      总子类关系: {total_children}")
        print(f"      总后代关系: {total_descendants}")
        print(f"      最多子类数: {max_children}")
        print(f"      最多后代数: {max_descendants}")
    
    def test_07_concrete_descendants_accuracy(self):
        """测试具体后代列表准确性"""
        print(f"\n🏗️ 测试7: 具体后代列表验证")
        
        definitions_map = {d.qualified_name: d for d in self.definitions}
        
        # 计算实际具体后代
        def get_concrete_descendants(qualified_name: str) -> Set[str]:
            concrete_descendants = set()
            
            def traverse(current_name: str):
                current_def = definitions_map.get(current_name)
                if not current_def:
                    return
                
                # 如果是具体类，添加到结果中
                if not current_def.is_abstract:
                    concrete_descendants.add(current_name)
                
                # 递归遍历子类
                for definition in self.definitions:
                    if definition.parent_qualified_name == current_name:
                        traverse(definition.qualified_name)
            
            traverse(qualified_name)
            concrete_descendants.discard(qualified_name)  # 移除自己
            return concrete_descendants
        
        # 验证具体后代列表
        errors = []
        for definition in self.definitions:
            qualified_name = definition.qualified_name
            expected_concrete = get_concrete_descendants(qualified_name)
            actual_concrete = set(definition.concrete_descendants)
            
            if expected_concrete != actual_concrete:
                missing = expected_concrete - actual_concrete
                extra = actual_concrete - expected_concrete
                error_msg = f"{qualified_name}: 具体后代列表不匹配"
                if missing:
                    error_msg += f" (缺少: {missing})"
                if extra:
                    error_msg += f" (多余: {extra})"
                errors.append(error_msg)
        
        if errors:
            self.fail(f"具体后代列表错误:\n" + "\n".join(errors[:5]))  # 只显示前5个错误
        
        print(f"   ✅ 所有具体后代列表正确")
        
        # 统计信息
        total_concrete_relationships = sum(len(d.concrete_descendants) for d in self.definitions)
        abstract_with_concrete = sum(1 for d in self.definitions if d.is_abstract and d.concrete_descendants)
        
        print(f"   📊 具体后代统计:")
        print(f"      总具体后代关系: {total_concrete_relationships}")
        print(f"      有具体后代的抽象类: {abstract_with_concrete}")


class TestDatabaseIntegration(unittest.TestCase):
    """数据库集成测试"""
    
    @classmethod
    def setUpClass(cls):
        """数据库测试初始化"""
        cls.db_config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'test_password',
            'database': 'test_biomedical_mbse'
        }
        cls.generator = CoreUMLMetaclassGenerator(schema_name="uml25_test")
        cls.definitions = cls.generator.generate_core_metaclass_definitions()
        cls.db_pool = None
    
    async def async_setUp(self):
        """异步测试准备"""
        try:
            database_url = f"postgresql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"
            self.db_pool = await asyncpg.create_pool(database_url, min_size=1, max_size=5)
            return True
        except Exception as e:
            print(f"   ⚠️ 数据库连接失败，跳过数据库测试: {e}")
            return False
    
    async def async_tearDown(self):
        """异步测试清理"""
        if self.db_pool:
            await self.db_pool.close()
    
    def test_08_database_table_creation(self):
        """测试数据库表创建"""
        print(f"\n🗄️ 测试8: 数据库表创建验证")
        
        async def run_test():
            connected = await self.async_setUp()
            if not connected:
                self.skipTest("数据库连接失败")
                return
            
            try:
                # 创建表
                success = await self.generator.create_database_table(self.db_pool)
                self.assertTrue(success, "数据库表创建失败")
                print(f"   ✅ 数据库表创建成功")
                
                # 验证表结构
                async with self.db_pool.acquire() as conn:
                    # 检查表是否存在
                    table_exists = await conn.fetchval("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'uml25_test' 
                            AND table_name = 'core_uml_metaclass_definitions'
                        )
                    """)
                    self.assertTrue(table_exists, "表不存在")
                    print(f"   ✅ 表存在验证通过")
                    
                    # 检查列
                    columns = await conn.fetch("""
                        SELECT column_name, data_type, is_nullable
                        FROM information_schema.columns
                        WHERE table_schema = 'uml25_test'
                        AND table_name = 'core_uml_metaclass_definitions'
                        ORDER BY ordinal_position
                    """)
                    
                    required_columns = {
                        'metaclass_id', 'qualified_name', 'simple_name',
                        'namespace', 'is_abstract', 'table_strategy'
                    }
                    actual_columns = {col['column_name'] for col in columns}
                    
                    missing_columns = required_columns - actual_columns
                    self.assertEqual(len(missing_columns), 0, 
                                   f"缺少必要列: {missing_columns}")
                    print(f"   ✅ 表结构验证通过 ({len(columns)}列)")
                    
            finally:
                await self.async_tearDown()
        
        asyncio.run(run_test())
    
    def test_09_database_data_insertion(self):
        """测试数据库数据插入"""
        print(f"\n📝 测试9: 数据库数据插入验证")
        
        async def run_test():
            connected = await self.async_setUp()
            if not connected:
                self.skipTest("数据库连接失败")
                return
            
            try:
                # 创建表
                await self.generator.create_database_table(self.db_pool)
                
                # 插入数据
                success = await self.generator.populate_database_table(self.db_pool, self.definitions)
                self.assertTrue(success, "数据插入失败")
                print(f"   ✅ 数据插入成功")
                
                # 验证数据数量
                async with self.db_pool.acquire() as conn:
                    count = await conn.fetchval("""
                        SELECT COUNT(*) FROM uml25_test.core_uml_metaclass_definitions
                    """)
                    self.assertEqual(count, len(self.definitions), 
                                   f"数据数量不匹配: 期望{len(self.definitions)}, 实际{count}")
                    print(f"   ✅ 数据数量正确: {count}")
                    
                    # 验证关键数据
                    root_element = await conn.fetchrow("""
                        SELECT * FROM uml25_test.core_uml_metaclass_definitions
                        WHERE qualified_name = 'uml:Element'
                    """)
                    self.assertIsNotNone(root_element, "根元素uml:Element不存在")
                    self.assertEqual(root_element['inheritance_level'], 0, "根元素层级错误")
                    self.assertTrue(root_element['is_abstract'], "根元素应为抽象类")
                    print(f"   ✅ 关键数据验证通过")
                    
            finally:
                await self.async_tearDown()
        
        asyncio.run(run_test())


class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    def setUp(self):
        """性能测试准备"""
        self.generator = CoreUMLMetaclassGenerator()
    
    def test_10_generation_performance(self):
        """测试生成性能"""
        print(f"\n⚡ 测试10: 生成性能验证")
        
        # 测试生成时间
        start_time = time.time()
        definitions = self.generator.generate_core_metaclass_definitions()
        generation_time = time.time() - start_time
        
        self.assertLess(generation_time, 5.0, "生成时间过长 (超过5秒)")
        print(f"   ✅ 生成时间: {generation_time:.3f}秒")
        
        # 测试内存使用
        import sys
        total_size = sys.getsizeof(definitions)
        for definition in definitions:
            total_size += sys.getsizeof(asdict(definition))
        
        print(f"   📊 内存使用: {total_size / 1024:.1f} KB")
        
        # 测试导出性能
        start_time = time.time()
        self.generator.export_to_json(definitions, "test_performance.json")
        export_time = time.time() - start_time
        
        self.assertLess(export_time, 2.0, "导出时间过长 (超过2秒)")
        print(f"   ✅ JSON导出时间: {export_time:.3f}秒")


def run_tests():
    """运行所有测试"""
    print("🧪 核心UML元类定义表测试套件")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加基础测试
    suite.addTests(loader.loadTestsFromTestCase(TestCoreUMLMetaclassDefinitions))
    
    # 添加性能测试
    suite.addTests(loader.loadTestsFromTestCase(TestPerformance))
    
    # 根据参数决定是否运行数据库测试
    if '--database' in sys.argv:
        print("🗄️ 包含数据库集成测试")
        suite.addTests(loader.loadTestsFromTestCase(TestDatabaseIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2 if '--verbose' in sys.argv else 1)
    result = runner.run(suite)
    
    # 生成测试报告
    generate_test_report(result)
    
    return result.wasSuccessful()


def generate_test_report(result):
    """生成测试报告"""
    print(f"\n📊 测试报告")
    print("=" * 40)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError: ')[-1].split('\n')[0]}")
    
    if result.errors:
        print(f"\n🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('\n')[-2]}")
    
    if result.wasSuccessful():
        print(f"\n✅ 所有测试通过!")
    else:
        print(f"\n❌ 有测试失败，请检查上述错误")
    
    # 保存详细报告
    report_file = Path("./test_output/test_report.json")
    report_file.parent.mkdir(exist_ok=True)
    
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': result.testsRun,
        'successful': result.testsRun - len(result.failures) - len(result.errors),
        'failures': len(result.failures),
        'errors': len(result.errors),
        'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun if result.testsRun > 0 else 0,
        'failed_tests': [str(test) for test, _ in result.failures],
        'error_tests': [str(test) for test, _ in result.errors]
    }
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 详细报告已保存到: {report_file}")


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1) 