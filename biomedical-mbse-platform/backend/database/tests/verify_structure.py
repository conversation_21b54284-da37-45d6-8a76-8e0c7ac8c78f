#!/usr/bin/env python3
"""
验证动态领域示例结构的脚本

检查：
1. 文件是否存在
2. 语法是否正确
3. 类和方法定义是否完整
4. 依赖关系是否合理
5. 配置结构是否正确
"""

import ast
import os
import sys
from typing import Dict, List, Any, Set

def check_file_exists(filepath: str) -> bool:
    """检查文件是否存在"""
    return os.path.exists(filepath) and os.path.isfile(filepath)

def parse_python_file(filepath: str) -> ast.AST:
    """解析Python文件为AST"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        return ast.parse(content, filename=filepath)
    except Exception as e:
        print(f"❌ 解析文件失败 {filepath}: {e}")
        return None

def extract_classes_and_methods(tree: ast.AST) -> Dict[str, List[str]]:
    """从AST中提取类和方法信息"""
    classes = {}
    
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            methods = []
            for item in node.body:
                if isinstance(item, ast.FunctionDef):
                    methods.append(item.name)
            classes[node.name] = methods
    
    return classes

def extract_imports(tree: ast.AST) -> Set[str]:
    """从AST中提取导入信息"""
    imports = set()
    
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.add(alias.name)
        elif isinstance(node, ast.ImportFrom):
            if node.module:
                imports.add(node.module)
                for alias in node.names:
                    imports.add(f"{node.module}.{alias.name}")
    
    return imports

def check_required_dependencies() -> Dict[str, bool]:
    """检查必需的依赖包"""
    print("=" * 50)
    print("📦 检查Python依赖包需求")
    print("=" * 50)
    
    required_packages = [
        'asyncpg',      # PostgreSQL异步驱动
        'bcrypt',       # 密码加密
        'asyncio',      # 异步编程 (内置)
        'json',         # JSON处理 (内置)
        'logging',      # 日志 (内置)
        'datetime',     # 时间处理 (内置)
        'typing',       # 类型注解 (内置)
        'dataclasses',  # 数据类 (Python 3.7+)
        'enum',         # 枚举 (内置)
        'uuid',         # UUID生成 (内置)
    ]
    
    results = {}
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
            results[package] = True
        except ImportError:
            if package in ['asyncpg', 'bcrypt']:
                print(f"⚠️  {package} (需要安装: pip install {package})")
            else:
                print(f"❌ {package} (缺失)")
            results[package] = False
    
    return results

def verify_dynamic_domain_example():
    """验证dynamic_domain_example.py"""
    print("\n" + "=" * 50)
    print("🔍 验证 dynamic_domain_example.py")
    print("=" * 50)
    
    filepath = "dynamic_domain_example.py"
    
    if not check_file_exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    print(f"✅ 文件存在: {filepath}")
    
    # 解析文件
    tree = parse_python_file(filepath)
    if not tree:
        return False
    
    print("✅ 语法解析成功")
    
    # 提取类和方法
    classes = extract_classes_and_methods(tree)
    
    # 检查DynamicDomainDemo类
    if 'DynamicDomainDemo' in classes:
        print("✅ 找到 DynamicDomainDemo 类")
        methods = classes['DynamicDomainDemo']
        
        required_methods = [
            '__init__', 'initialize', 'run_complete_demo',
            'demo_create_lab_management_domain', 'demo_create_data_analytics_domain',
            'demo_add_custom_element_types', 'demo_cross_domain_interactions',
            'demo_performance_optimization', 'demo_health_monitoring',
            'demo_create_sample_data', 'cleanup'
        ]
        
        for method in required_methods:
            if method in methods:
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ 缺失方法: {method}")
    else:
        print("❌ 未找到 DynamicDomainDemo 类")
    
    # 检查main函数
    main_functions = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef) and node.name == 'main':
            main_functions.append(node.name)
    
    if main_functions:
        print("✅ 找到 main 函数")
    else:
        print("❌ 未找到 main 函数")
    
    # 检查导入
    imports = extract_imports(tree)
    required_imports = [
        'asyncio', 'json', 'logging', 'datetime', 'typing', 'asyncpg',
        'domain_managers.core_domain_manager', 'domain_managers.domain_factory',
        'domain_managers.dynamic_schema_generator', 'domain_managers.cross_domain_indexer'
    ]
    
    print("\n📋 导入检查:")
    for imp in required_imports:
        if any(imp in existing_imp for existing_imp in imports):
            print(f"  ✅ {imp}")
        else:
            print(f"  ⚠️  {imp} (未直接导入，可能在from语句中)")
    
    return True

def verify_example_usage():
    """验证example_usage.py"""
    print("\n" + "=" * 50)
    print("🔍 验证 example_usage.py")
    print("=" * 50)
    
    filepath = "example_usage.py"
    
    if not check_file_exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    print(f"✅ 文件存在: {filepath}")
    
    # 解析文件
    tree = parse_python_file(filepath)
    if not tree:
        return False
    
    print("✅ 语法解析成功")
    
    # 提取类和方法
    classes = extract_classes_and_methods(tree)
    
    # 检查ElementPlatformManager类
    if 'ElementPlatformManager' in classes:
        print("✅ 找到 ElementPlatformManager 类")
        methods = classes['ElementPlatformManager']
        
        required_methods = [
            '__init__', 'initialize', 'demo_user_management',
            'demo_cross_domain_relationships', 'demo_element_metadata_query', 'close'
        ]
        
        for method in required_methods:
            if method in methods:
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ 缺失方法: {method}")
    else:
        print("❌ 未找到 ElementPlatformManager 类")
    
    # 检查导入
    imports = extract_imports(tree)
    required_imports = [
        'asyncio', 'asyncpg',
        'domain_managers.core_domain_manager', 'domain_managers.security_domain_manager'
    ]
    
    print("\n📋 导入检查:")
    for imp in required_imports:
        if any(imp in existing_imp for existing_imp in imports):
            print(f"  ✅ {imp}")
        else:
            print(f"  ⚠️  {imp} (未直接导入)")
    
    return True

def verify_domain_managers():
    """验证domain_managers模块"""
    print("\n" + "=" * 50)
    print("🔍 验证 domain_managers 模块")
    print("=" * 50)
    
    modules = [
        'core_domain_manager.py',
        'domain_factory.py', 
        'dynamic_schema_generator.py',
        'cross_domain_indexer.py',
        'security_domain_manager.py'
    ]
    
    results = {}
    
    for module in modules:
        filepath = f"domain_managers/{module}"
        
        if check_file_exists(filepath):
            print(f"✅ {module}")
            
            # 检查语法
            tree = parse_python_file(filepath)
            if tree:
                print(f"  ✅ 语法正确")
                
                # 提取类信息
                classes = extract_classes_and_methods(tree)
                if classes:
                    print(f"  ✅ 包含类: {list(classes.keys())}")
                else:
                    print(f"  ⚠️  未找到类定义")
                
                results[module] = True
            else:
                print(f"  ❌ 语法错误")
                results[module] = False
        else:
            print(f"❌ {module} 文件不存在")
            results[module] = False
    
    return results

def verify_configuration_examples():
    """验证配置示例"""
    print("\n" + "=" * 50)
    print("⚙️  验证配置示例")
    print("=" * 50)
    
    # 检查dynamic_domain_example.py中的配置
    tree = parse_python_file("dynamic_domain_example.py")
    if tree:
        # 查找数据库配置字典
        config_found = False
        for node in ast.walk(tree):
            if isinstance(node, ast.Dict):
                # 检查是否包含数据库配置的键
                keys = []
                for key in node.keys:
                    if isinstance(key, ast.Constant):
                        keys.append(key.value)
                
                if 'host' in keys and 'port' in keys and 'database' in keys:
                    print("✅ dynamic_domain_example.py 包含数据库配置")
                    config_found = True
                    break
        
        if not config_found:
            print("⚠️  dynamic_domain_example.py 数据库配置可能需要检查")
    
    # 检查example_usage.py中的配置
    tree = parse_python_file("example_usage.py")
    if tree:
        # 查找database_url字符串
        url_found = False
        for node in ast.walk(tree):
            if isinstance(node, ast.Constant) and isinstance(node.value, str):
                if 'postgresql://' in node.value:
                    print("✅ example_usage.py 包含数据库URL配置")
                    url_found = True
                    break
        
        if not url_found:
            print("⚠️  example_usage.py 数据库URL配置可能需要检查")

def main():
    """主验证函数"""
    print("🚀 开始验证动态领域示例结构")
    print("时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 检查依赖
    deps = check_required_dependencies()
    
    # 验证各个文件
    results = []
    results.append(("dynamic_domain_example", verify_dynamic_domain_example()))
    results.append(("example_usage", verify_example_usage()))
    
    # 验证domain_managers模块
    manager_results = verify_domain_managers()
    results.append(("domain_managers", all(manager_results.values())))
    
    # 验证配置
    verify_configuration_examples()
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📋 结构验证结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
    
    print(f"\n📈 总计: {passed}/{total} 测试通过")
    
    # 依赖包总结
    missing_deps = [pkg for pkg, available in deps.items() if not available and pkg in ['asyncpg', 'bcrypt']]
    if missing_deps:
        print(f"\n📦 需要安装的依赖包:")
        for dep in missing_deps:
            print(f"   pip install {dep}")
    
    if passed == total and not missing_deps:
        print("\n🎉 所有结构验证通过！实现结构完整且正确。")
        return True
    elif passed == total:
        print("\n⚠️  结构验证通过，但需要安装依赖包才能运行。")
        return True
    else:
        print("\n❌ 部分结构验证失败，需要检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 