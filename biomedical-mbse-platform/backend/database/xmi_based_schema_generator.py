#!/usr/bin/env python3
"""
基于XMI结构的动态数据库表生成器

分析XMI文件的实际结构，为每个xmi:type创建对应的数据库表，
建立网状的关系结构，反映XMI中的嵌套包含关系。

核心思路：
1. 每个带有xmi:type的XML节点都是一个表定义
2. 嵌套的带xmi:type的节点与父表有包含的聚合引用关系（外键）
3. 不带xmi:type的XML属性成为表的列
4. xmi:id和xmi:idref建立引用关系
5. 生成网状的表关系结构

作者：生物医学MBSE平台开发组
版本：1.0.0
创建时间：2025年1月
"""

import xml.etree.ElementTree as ET
import json
import logging
from typing import Dict, List, Set, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from collections import defaultdict
import re

# 数据库连接相关导入
try:
    import psycopg2
    from psycopg2 import sql
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("psycopg2 未安装，无法连接PostgreSQL数据库")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class XMIAttributeInfo:
    """XMI属性信息"""
    name: str                    # 属性名
    value_type: str              # 值类型（text, boolean, integer, reference等）
    is_required: bool = False    # 是否必需
    default_value: str = None    # 默认值
    is_reference: bool = False   # 是否是引用（xmi:idref等）
    reference_target: str = None # 引用目标类型
    description: str = ""        # 描述
    sample_values: Set[str] = field(default_factory=set)  # 样本值

@dataclass
class XMITableDefinition:
    """XMI表定义"""
    table_name: str                              # 表名
    xmi_type: str                               # 原始xmi:type
    attributes: Dict[str, XMIAttributeInfo]     # 属性列表
    parent_tables: List[str] = field(default_factory=list)  # 父表列表（包含关系）
    child_tables: List[str] = field(default_factory=list)   # 子表列表
    instance_count: int = 0                     # 实例数量
    namespace: str = ""                         # 命名空间
    description: str = ""                       # 描述
    sample_instances: List[Dict] = field(default_factory=list)  # 样本实例

class XMIBasedSchemaGenerator:
    """基于XMI结构的数据库Schema生成器"""
    
    def __init__(self, output_dir: str = "./xmi_schema_output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 发现的表定义
        self.table_definitions: Dict[str, XMITableDefinition] = {}
        
        # 类型统计
        self.type_counts: Dict[str, int] = defaultdict(int)
        self.namespace_types: Dict[str, Set[str]] = defaultdict(set)
        
        # 引用关系
        self.reference_relationships: List[Tuple[str, str, str, str]] = []  # (source_table, source_attr, target_table, target_attr)
        
        # XML命名空间映射
        self.namespaces = {}
        
        # 数据库连接
        self.db_connection = None
    
    def connect_database(self, host: str = "localhost", port: int = 5432, 
                        database: str = "postgres", user: str = "postgres", 
                        password: str = "password") -> bool:
        """连接到PostgreSQL数据库"""
        if not PSYCOPG2_AVAILABLE:
            logger.error("psycopg2 未安装，请运行: pip install psycopg2-binary")
            return False
        
        try:
            self.db_connection = psycopg2.connect(
                host=host,
                port=port,
                database=database,
                user=user,
                password=password,
                client_encoding='utf8'  # 明确设置客户端编码
            )
            self.db_connection.autocommit = True
            logger.info(f"✅ 成功连接到数据库: {host}:{port}/{database}")
            return True
        except psycopg2.OperationalError as e:
            error_msg = str(e)
            logger.error(f"❌ 数据库连接失败 (操作错误): {error_msg}")
            
            # 提供一些常见错误的解决建议
            if "password authentication failed" in error_msg:
                logger.error("💡 建议: 检查用户名和密码是否正确")
            elif "could not connect to server" in error_msg:
                logger.error("💡 建议: 检查PostgreSQL服务是否启动，主机地址和端口是否正确")
            elif "database" in error_msg and "does not exist" in error_msg:
                logger.error("💡 建议: 检查数据库名称是否正确，或尝试连接到 'postgres' 数据库")
            
            return False
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            logger.error(f"💡 建议: 确保PostgreSQL服务正在运行，并检查连接参数")
            return False
    
    def disconnect_database(self):
        """断开数据库连接"""
        if self.db_connection:
            self.db_connection.close()
            self.db_connection = None
            logger.info("📡 数据库连接已断开")
    
    def create_schema_in_database(self, schema_name: str = "xmi_generated", 
                                 drop_if_exists: bool = False) -> bool:
        """在数据库中创建Schema"""
        if not self.db_connection:
            logger.error("❌ 未连接到数据库")
            return False
        
        try:
            cursor = self.db_connection.cursor()
            
            # 检查Schema是否存在
            cursor.execute("""
                SELECT schema_name FROM information_schema.schemata 
                WHERE schema_name = %s
            """, (schema_name,))
            
            schema_exists = cursor.fetchone() is not None
            
            if schema_exists:
                if drop_if_exists:
                    logger.info(f"🗑️  删除现有Schema: {schema_name}")
                    cursor.execute(sql.SQL("DROP SCHEMA IF EXISTS {} CASCADE").format(
                        sql.Identifier(schema_name)
                    ))
                else:
                    logger.warning(f"⚠️  Schema {schema_name} 已存在，使用现有Schema")
                    cursor.close()
                    return True
            
            # 创建Schema
            logger.info(f"🏗️  创建Schema: {schema_name}")
            cursor.execute(sql.SQL("CREATE SCHEMA IF NOT EXISTS {}").format(
                sql.Identifier(schema_name)
            ))
            
            cursor.close()
            logger.info(f"✅ Schema {schema_name} 创建成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建Schema失败: {e}")
            return False
    
    def create_tables_in_database(self, schema_name: str = "xmi_generated", 
                                 max_tables: int = None) -> Dict[str, bool]:
        """在数据库中创建表"""
        if not self.db_connection:
            logger.error("❌ 未连接到数据库")
            return {}
        
        if not self.table_definitions:
            logger.error("❌ 没有表定义，请先分析XMI文件")
            return {}
        
        results = {}
        cursor = self.db_connection.cursor()
        
        try:
            # 限制表数量（用于测试）
            tables_to_create = list(self.table_definitions.values())
            if max_tables:
                # 按重要性排序（实例数 * 属性数）
                tables_to_create.sort(key=lambda x: x.instance_count * len(x.attributes), reverse=True)
                tables_to_create = tables_to_create[:max_tables]
                logger.info(f"🔧 限制创建表数量: {max_tables}")
            
            logger.info(f"🏗️  开始创建 {len(tables_to_create)} 个表...")
            
            # 第一轮：创建所有表（不包含外键）
            for table_def in tables_to_create:
                try:
                    table_sql = self._generate_table_sql_for_database(table_def, schema_name, include_fk=False)
                    cursor.execute(table_sql)
                    results[table_def.table_name] = True
                    logger.info(f"  ✅ {table_def.table_name}")
                except Exception as e:
                    logger.error(f"  ❌ {table_def.table_name}: {e}")
                    results[table_def.table_name] = False
            
            # 第二轮：添加外键约束
            logger.info("🔗 添加外键约束...")
            for table_def in tables_to_create:
                if not results.get(table_def.table_name):
                    continue  # 跳过创建失败的表
                
                try:
                    # 添加父表外键
                    for parent_table in table_def.parent_tables:
                        if parent_table in [t.table_name for t in tables_to_create]:
                            fk_sql = f"""
                                ALTER TABLE {schema_name}.{table_def.table_name}
                                ADD CONSTRAINT fk_{table_def.table_name}_{parent_table}
                                FOREIGN KEY (parent_{parent_table}_id) 
                                REFERENCES {schema_name}.{parent_table}(id)
                            """
                            cursor.execute(fk_sql)
                    
                    # 添加引用外键
                    for source_table, source_attr, target_table, target_attr in self.reference_relationships:
                        if (source_table == table_def.table_name and 
                            target_table in [t.table_name for t in tables_to_create]):
                            ref_fk_sql = f"""
                                ALTER TABLE {schema_name}.{source_table}
                                ADD CONSTRAINT fk_{source_table}_{source_attr}
                                FOREIGN KEY ({source_attr}) 
                                REFERENCES {schema_name}.{target_table}({target_attr})
                            """
                            try:
                                cursor.execute(ref_fk_sql)
                            except Exception:
                                # 引用外键可能失败，这是正常的
                                pass
                    
                except Exception as e:
                    logger.warning(f"  ⚠️  {table_def.table_name} 外键创建部分失败: {e}")
            
            # 创建索引
            logger.info("📊 创建索引...")
            for table_def in tables_to_create:
                if not results.get(table_def.table_name):
                    continue
                
                try:
                    # xmi_id索引
                    cursor.execute(f"""
                        CREATE INDEX IF NOT EXISTS idx_{table_def.table_name}_xmi_id 
                        ON {schema_name}.{table_def.table_name}(xmi_id)
                    """)
                    
                    # xmi_type索引
                    cursor.execute(f"""
                        CREATE INDEX IF NOT EXISTS idx_{table_def.table_name}_xmi_type 
                        ON {schema_name}.{table_def.table_name}(xmi_type)
                    """)
                except Exception as e:
                    logger.warning(f"  ⚠️  {table_def.table_name} 索引创建失败: {e}")
            
            cursor.close()
            
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"🎉 表创建完成! 成功: {success_count}/{len(tables_to_create)}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 批量创建表失败: {e}")
            cursor.close()
            return results
    
    def _generate_table_sql_for_database(self, table_def: XMITableDefinition, 
                                        schema_name: str, include_fk: bool = True) -> str:
        """为数据库执行生成单个表的SQL（简化版，避免复杂的外键）"""
        lines = []
        
        # 表定义开始
        lines.append(f"CREATE TABLE {schema_name}.{table_def.table_name} (")
        
        # 主键
        lines.append("    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),")
        
        # XMI相关列
        lines.append("    xmi_id TEXT,")
        lines.append("    xmi_type TEXT NOT NULL,")
        
        # 父表外键（仅在include_fk=True时添加）
        if include_fk:
            for parent_table in table_def.parent_tables:
                fk_col = f"parent_{parent_table}_id"
                lines.append(f"    {fk_col} UUID,")  # 先不加外键约束
        
        # 属性列
        for attr_name, attr_info in table_def.attributes.items():
            if attr_name in ['id', 'xmi_id', 'xmi_type']:  # 跳过已定义的列
                continue
            
            col_def = self._generate_column_definition(attr_name, attr_info)
            lines.append(f"    {col_def},")
        
        # 标准时间戳
        lines.append("    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,")
        lines.append("    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP")
        
        lines.append(")")
        
        return "\n".join(lines)
    
    def get_database_table_info(self, schema_name: str = "xmi_generated") -> Dict[str, Any]:
        """获取数据库中表的信息"""
        if not self.db_connection:
            logger.error("❌ 未连接到数据库")
            return {}
        
        try:
            cursor = self.db_connection.cursor()
            
            # 获取表列表
            cursor.execute("""
                SELECT table_name, 
                       (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_schema = %s AND table_name = t.table_name) as column_count
                FROM information_schema.tables t
                WHERE table_schema = %s
                ORDER BY table_name
            """, (schema_name, schema_name))
            
            tables = cursor.fetchall()
            
            # 获取每个表的行数
            table_info = {}
            for table_name, column_count in tables:
                try:
                    cursor.execute(sql.SQL("SELECT COUNT(*) FROM {}.{}").format(
                        sql.Identifier(schema_name),
                        sql.Identifier(table_name)
                    ))
                    row_count = cursor.fetchone()[0]
                    
                    table_info[table_name] = {
                        'column_count': column_count,
                        'row_count': row_count
                    }
                except Exception:
                    table_info[table_name] = {
                        'column_count': column_count,
                        'row_count': 0
                    }
            
            cursor.close()
            return table_info
            
        except Exception as e:
            logger.error(f"❌ 获取数据库表信息失败: {e}")
            return {}
    
    def analyze_xmi_file(self, xmi_file_path: str) -> Dict[str, XMITableDefinition]:
        """分析XMI文件结构"""
        logger.info(f"开始分析XMI文件: {xmi_file_path}")
        
        try:
            # 解析XML
            tree = ET.parse(xmi_file_path)
            root = tree.getroot()
            
            # 获取命名空间
            self.namespaces = self._extract_namespaces(root)
            logger.info(f"发现命名空间: {self.namespaces}")
            
            # 递归分析XML结构
            self._analyze_element(root, parent_table=None, depth=0)
            
            # 后处理：建立引用关系
            self._build_reference_relationships()
            
            # 统计信息
            logger.info(f"分析完成！发现 {len(self.table_definitions)} 个表定义")
            logger.info(f"类型分布: {dict(self.type_counts)}")
            
            return self.table_definitions
            
        except Exception as e:
            logger.error(f"分析XMI文件失败: {e}")
            raise
    
    def _extract_namespaces(self, root: ET.Element) -> Dict[str, str]:
        """提取XML命名空间"""
        namespaces = {}
        # 检查根元素的所有命名空间声明
        for key, value in root.attrib.items():
            if key.startswith('xmlns:'):
                prefix = key[6:]  # 移除'xmlns:'前缀
                namespaces[prefix] = value
            elif key == 'xmlns':
                namespaces[''] = value  # 默认命名空间
        
        # 注册命名空间以便ElementTree使用
        for prefix, uri in namespaces.items():
            if prefix:  # 有前缀的命名空间
                ET.register_namespace(prefix, uri)
        
        return namespaces
    
    def _analyze_element(self, element: ET.Element, parent_table: str = None, depth: int = 0):
        """递归分析XML元素"""
        # 调试输出（可选）
        if depth < 3:  # 只在前几层输出调试信息
            logger.debug(f"{'  ' * depth}分析元素: {element.tag}, 属性: {list(element.attrib.keys())}")
        
        # 检查是否有xmi:type属性
        xmi_type = self._get_xmi_type(element)
        
        if xmi_type:
            # 这是一个表定义
            table_name = self._generate_table_name(xmi_type)
            
            logger.debug(f"{'  ' * depth}发现表定义: {xmi_type} -> {table_name}")
            
            # 创建或更新表定义
            if table_name not in self.table_definitions:
                self.table_definitions[table_name] = XMITableDefinition(
                    table_name=table_name,
                    xmi_type=xmi_type,
                    attributes={},
                    namespace=self._get_namespace_from_type(xmi_type)
                )
            
            table_def = self.table_definitions[table_name]
            table_def.instance_count += 1
            self.type_counts[xmi_type] += 1
            
            # 分析所有XML属性作为表列
            self._analyze_attributes(element, table_def)
            
            # 建立父子关系
            if parent_table and parent_table != table_name:
                if parent_table not in table_def.parent_tables:
                    table_def.parent_tables.append(parent_table)
                if table_name not in self.table_definitions[parent_table].child_tables:
                    self.table_definitions[parent_table].child_tables.append(table_name)
            
            # 记录样本实例
            if len(table_def.sample_instances) < 3:
                sample = self._extract_sample_instance(element)
                table_def.sample_instances.append(sample)
            
            # 设置当前表作为子元素的父表
            current_parent = table_name
        else:
            # 没有xmi:type，继续使用父表
            current_parent = parent_table
        
        # 递归处理子元素
        for child in element:
            self._analyze_element(child, parent_table=current_parent, depth=depth+1)
    
    def _get_xmi_type(self, element: ET.Element) -> Optional[str]:
        """获取元素的xmi:type属性"""
        # 尝试多种方式获取xmi:type
        
        # 方式1：直接查找xmi:type
        if 'xmi:type' in element.attrib:
            return element.attrib['xmi:type']
        
        # 方式2：查找包含type的属性名
        for attr_name, attr_value in element.attrib.items():
            if attr_name.endswith(':type') or attr_name == 'type':
                return attr_value
        
        # 方式3：检查元素本身的标签是否包含类型信息
        tag = element.tag
        if ':' in tag:
            # 如果标签本身就包含命名空间，可能就是类型
            return tag
        
        return None
    
    def _generate_table_name(self, xmi_type: str) -> str:
        """根据xmi:type生成表名"""
        # 移除命名空间前缀，转换为snake_case
        if ':' in xmi_type:
            _, type_name = xmi_type.split(':', 1)
        else:
            type_name = xmi_type
        
        # 清理特殊字符
        # 移除大括号、斜杠、点等特殊字符
        clean_name = re.sub(r'[{}\[\]/\\.:-]', '_', type_name)
        # 移除多个下划线
        clean_name = re.sub(r'_+', '_', clean_name)
        # 移除开头和结尾的下划线
        clean_name = clean_name.strip('_')
        
        # 转换为snake_case
        table_name = re.sub(r'([a-z])([A-Z])', r'\1_\2', clean_name).lower()
        
        # 确保表名不为空
        if not table_name:
            table_name = "unknown_type"
        
        # 限制表名长度（PostgreSQL标识符限制63字符）
        if len(table_name) > 50:  # 留一些空间给前缀
            table_name = table_name[:50]
        
        return f"xmi_{table_name}"
    
    def _get_namespace_from_type(self, xmi_type: str) -> str:
        """从xmi:type获取命名空间"""
        if ':' in xmi_type:
            namespace, _ = xmi_type.split(':', 1)
            return namespace
        return 'default'
    
    def _analyze_attributes(self, element: ET.Element, table_def: XMITableDefinition):
        """分析XML元素的属性作为表列"""
        for attr_name, attr_value in element.attrib.items():
            # 跳过xmi:type，因为它用于表分类
            if attr_name.endswith(':type'):
                continue
            
            # 标准化属性名
            clean_attr_name = self._clean_attribute_name(attr_name)
            
            # 创建或更新属性信息
            if clean_attr_name not in table_def.attributes:
                table_def.attributes[clean_attr_name] = XMIAttributeInfo(
                    name=clean_attr_name,
                    value_type=self._infer_value_type(attr_value, attr_name),
                    is_reference=self._is_reference_attribute(attr_name),
                    description=f"从XML属性发现: {attr_name}"
                )
            
            attr_info = table_def.attributes[clean_attr_name]
            attr_info.sample_values.add(attr_value[:100])  # 限制样本值长度
            
            # 推断是否必需（基于出现频率）
            if table_def.instance_count > 5 and len(attr_info.sample_values) == table_def.instance_count:
                attr_info.is_required = True
    
    def _clean_attribute_name(self, attr_name: str) -> str:
        """清理属性名为有效的数据库列名"""
        # 移除命名空间前缀
        if ':' in attr_name:
            _, clean_name = attr_name.split(':', 1)
        else:
            clean_name = attr_name
        
        # 处理特殊字符
        clean_name = re.sub(r'[^\w]', '_', clean_name)
        clean_name = re.sub(r'_+', '_', clean_name)  # 合并多个下划线
        clean_name = clean_name.strip('_')
        
        # 确保不是SQL关键字
        if clean_name.lower() in ['id', 'name', 'type', 'order', 'group']:
            clean_name = f"{clean_name}_attr"
        
        return clean_name
    
    def _infer_value_type(self, value: str, attr_name: str) -> str:
        """推断属性值的数据类型"""
        if not value:
            return 'text'
        
        # 引用类型
        if self._is_reference_attribute(attr_name):
            return 'reference'
        
        # 布尔类型
        if value.lower() in ['true', 'false']:
            return 'boolean'
        
        # 整数类型
        try:
            int(value)
            return 'integer'
        except ValueError:
            pass
        
        # 浮点数类型
        try:
            float(value)
            return 'decimal'
        except ValueError:
            pass
        
        # URL类型
        if value.startswith(('http://', 'https://', 'file://')):
            return 'url'
        
        # 长文本（可能需要TEXT字段）
        if len(value) > 255:
            return 'text'
        
        # 默认为变长字符串
        return 'varchar'
    
    def _is_reference_attribute(self, attr_name: str) -> bool:
        """判断是否是引用属性"""
        reference_patterns = [
            'idref', 'href', 'type', 'general', 'specific',
            'supplier', 'client', 'target', 'source',
            'importedPackage', 'constrainedElement'
        ]
        
        clean_name = attr_name.lower()
        return any(pattern in clean_name for pattern in reference_patterns)
    
    def _extract_sample_instance(self, element: ET.Element) -> Dict[str, Any]:
        """提取样本实例数据"""
        sample = {}
        for attr_name, attr_value in element.attrib.items():
            clean_name = self._clean_attribute_name(attr_name)
            sample[clean_name] = attr_value
        
        # 添加文本内容（如果有）
        if element.text and element.text.strip():
            sample['_text_content'] = element.text.strip()
        
        return sample
    
    def _build_reference_relationships(self):
        """构建引用关系"""
        logger.info("构建表间引用关系...")
        
        for table_name, table_def in self.table_definitions.items():
            for attr_name, attr_info in table_def.attributes.items():
                if attr_info.is_reference:
                    # 尝试找到引用的目标表
                    target_table = self._find_reference_target(attr_info.sample_values)
                    if target_table:
                        attr_info.reference_target = target_table
                        self.reference_relationships.append(
                            (table_name, attr_name, target_table, 'id')
                        )
    
    def _find_reference_target(self, sample_values: Set[str]) -> Optional[str]:
        """根据样本值找到引用目标表"""
        # 这是一个简化的实现，实际中可能需要更复杂的逻辑
        for value in sample_values:
            # 如果值是另一个xmi:type，找到对应的表
            for table_name, table_def in self.table_definitions.items():
                if value == table_def.xmi_type:
                    return table_name
        return None
    
    def generate_postgresql_schema(self, schema_name: str = "xmi_generated") -> str:
        """生成PostgreSQL数据库Schema"""
        logger.info("生成PostgreSQL Schema...")
        
        sql_parts = []
        
        # Schema创建
        sql_parts.append(f"-- 基于XMI结构生成的数据库Schema")
        sql_parts.append(f"-- 生成时间: {self._get_timestamp()}")
        sql_parts.append(f"-- 表数量: {len(self.table_definitions)}")
        sql_parts.append("")
        sql_parts.append(f"CREATE SCHEMA IF NOT EXISTS {schema_name};")
        sql_parts.append("")
        
        # 为每个表生成SQL
        for table_name, table_def in self.table_definitions.items():
            sql_parts.append(self._generate_table_sql(table_def, schema_name))
            sql_parts.append("")
        
        # 生成外键约束
        sql_parts.append("-- 外键约束")
        for source_table, source_attr, target_table, target_attr in self.reference_relationships:
            fk_name = f"fk_{source_table}_{source_attr}"
            sql_parts.append(f"ALTER TABLE {schema_name}.{source_table}")
            sql_parts.append(f"    ADD CONSTRAINT {fk_name}")
            sql_parts.append(f"    FOREIGN KEY ({source_attr}) REFERENCES {schema_name}.{target_table}({target_attr});")
            sql_parts.append("")
        
        return "\n".join(sql_parts)
    
    def _generate_table_sql(self, table_def: XMITableDefinition, schema_name: str) -> str:
        """为单个表生成SQL"""
        lines = []
        
        # 表注释
        lines.append(f"-- 表: {table_def.table_name}")
        lines.append(f"-- XMI类型: {table_def.xmi_type}")
        lines.append(f"-- 实例数量: {table_def.instance_count}")
        if table_def.parent_tables:
            lines.append(f"-- 父表: {', '.join(table_def.parent_tables)}")
        if table_def.child_tables:
            lines.append(f"-- 子表: {', '.join(table_def.child_tables)}")
        
        # 表定义开始
        lines.append(f"CREATE TABLE {schema_name}.{table_def.table_name} (")
        
        # 主键
        lines.append("    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),")
        
        # XMI相关列
        lines.append("    xmi_id TEXT,")
        lines.append("    xmi_type TEXT NOT NULL,")
        
        # 父表外键（用于包含关系）
        for parent_table in table_def.parent_tables:
            fk_col = f"parent_{parent_table}_id"
            lines.append(f"    {fk_col} UUID REFERENCES {schema_name}.{parent_table}(id),")
        
        # 属性列
        for attr_name, attr_info in table_def.attributes.items():
            if attr_name in ['id', 'xmi_id', 'xmi_type']:  # 跳过已定义的列
                continue
            
            col_def = self._generate_column_definition(attr_name, attr_info)
            lines.append(f"    {col_def},")
        
        # 标准时间戳
        lines.append("    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,")
        lines.append("    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP")
        
        lines.append(");")
        
        # 表注释
        comment = f"XMI类型表: {table_def.xmi_type}"
        if table_def.description:
            comment += f" - {table_def.description}"
        lines.append(f"COMMENT ON TABLE {schema_name}.{table_def.table_name} IS '{comment}';")
        
        # 索引
        lines.append(f"CREATE INDEX IF NOT EXISTS idx_{table_def.table_name}_xmi_id ON {schema_name}.{table_def.table_name}(xmi_id);")
        lines.append(f"CREATE INDEX IF NOT EXISTS idx_{table_def.table_name}_xmi_type ON {schema_name}.{table_def.table_name}(xmi_type);")
        
        return "\n".join(lines)
    
    def _generate_column_definition(self, attr_name: str, attr_info: XMIAttributeInfo) -> str:
        """生成列定义"""
        # 数据类型映射
        type_mapping = {
            'text': 'TEXT',
            'varchar': 'VARCHAR(255)',
            'boolean': 'BOOLEAN',
            'integer': 'INTEGER',
            'decimal': 'DECIMAL(10,2)',
            'url': 'TEXT',
            'reference': 'TEXT'  # 引用先用TEXT，后续建立外键
        }
        
        sql_type = type_mapping.get(attr_info.value_type, 'TEXT')
        
        # 构建列定义
        col_def = f"{attr_name} {sql_type}"
        
        # 非空约束
        if attr_info.is_required:
            col_def += " NOT NULL"
        
        # 默认值
        if attr_info.default_value:
            if attr_info.value_type == 'boolean':
                col_def += f" DEFAULT {attr_info.default_value.lower()}"
            elif attr_info.value_type in ['integer', 'decimal']:
                col_def += f" DEFAULT {attr_info.default_value}"
            else:
                col_def += f" DEFAULT '{attr_info.default_value}'"
        
        return col_def
    
    def generate_analysis_report(self) -> str:
        """生成分析报告"""
        report_lines = []
        
        report_lines.append("# XMI结构分析报告")
        report_lines.append("=" * 50)
        report_lines.append("")
        
        # 总体统计
        report_lines.append("## 总体统计")
        report_lines.append(f"- **发现的表数量**: {len(self.table_definitions)}")
        report_lines.append(f"- **XMI类型数量**: {len(self.type_counts)}")
        report_lines.append(f"- **引用关系数量**: {len(self.reference_relationships)}")
        report_lines.append("")
        
        # 命名空间分布
        report_lines.append("## 命名空间分布")
        namespace_stats = defaultdict(int)
        for table_def in self.table_definitions.values():
            namespace_stats[table_def.namespace] += 1
        
        for namespace, count in sorted(namespace_stats.items()):
            report_lines.append(f"- **{namespace}**: {count} 个表")
        report_lines.append("")
        
        # 表层次结构
        report_lines.append("## 表层次结构")
        root_tables = [t for t in self.table_definitions.values() if not t.parent_tables]
        for root_table in root_tables:
            report_lines.append(f"### {root_table.table_name}")
            self._add_table_hierarchy(root_table, report_lines, depth=1)
        report_lines.append("")
        
        # 热门类型（实例数量最多）
        report_lines.append("## 热门类型（实例数量排序）")
        sorted_types = sorted(self.type_counts.items(), key=lambda x: x[1], reverse=True)
        for xmi_type, count in sorted_types[:10]:
            table_name = self._generate_table_name(xmi_type)
            report_lines.append(f"- **{xmi_type}**: {count} 个实例 → `{table_name}`表")
        report_lines.append("")
        
        # 复杂表（属性最多）
        report_lines.append("## 复杂表（属性数量排序）")
        complex_tables = sorted(self.table_definitions.values(), 
                               key=lambda x: len(x.attributes), reverse=True)
        for table_def in complex_tables[:10]:
            report_lines.append(f"- **{table_def.table_name}**: {len(table_def.attributes)} 个属性")
        report_lines.append("")
        
        # 详细表定义（前5个最重要的）
        report_lines.append("## 主要表定义详情")
        important_tables = sorted(self.table_definitions.values(), 
                                 key=lambda x: (x.instance_count * len(x.attributes)), reverse=True)
        
        for table_def in important_tables[:5]:
            report_lines.append(f"### {table_def.table_name}")
            report_lines.append(f"- **XMI类型**: `{table_def.xmi_type}`")
            report_lines.append(f"- **实例数量**: {table_def.instance_count}")
            report_lines.append(f"- **属性数量**: {len(table_def.attributes)}")
            
            if table_def.parent_tables:
                report_lines.append(f"- **父表**: {', '.join(table_def.parent_tables)}")
            if table_def.child_tables:
                report_lines.append(f"- **子表**: {', '.join(table_def.child_tables)}")
            
            # 主要属性
            if table_def.attributes:
                report_lines.append("- **主要属性**:")
                for attr_name, attr_info in list(table_def.attributes.items())[:5]:
                    required_mark = " *(必需)*" if attr_info.is_required else ""
                    ref_mark = " *(引用)*" if attr_info.is_reference else ""
                    report_lines.append(f"  - `{attr_name}`: {attr_info.value_type}{required_mark}{ref_mark}")
            
            report_lines.append("")
        
        return "\n".join(report_lines)
    
    def _add_table_hierarchy(self, table_def: XMITableDefinition, lines: List[str], depth: int):
        """递归添加表层次结构"""
        indent = "  " * depth
        lines.append(f"{indent}- {table_def.table_name} ({table_def.instance_count} instances)")
        
        for child_table_name in table_def.child_tables:
            if child_table_name in self.table_definitions:
                child_table = self.table_definitions[child_table_name]
                self._add_table_hierarchy(child_table, lines, depth + 1)
    
    def export_schema_files(self, base_name: str = "xmi_generated_schema"):
        """导出所有Schema文件"""
        logger.info("导出Schema文件...")
        
        # 1. PostgreSQL Schema
        schema_sql = self.generate_postgresql_schema()
        sql_file = self.output_dir / f"{base_name}.sql"
        with open(sql_file, 'w', encoding='utf-8') as f:
            f.write(schema_sql)
        logger.info(f"PostgreSQL Schema导出: {sql_file}")
        
        # 2. 分析报告
        report = self.generate_analysis_report()
        report_file = self.output_dir / f"{base_name}_analysis.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info(f"分析报告导出: {report_file}")
        
        # 3. JSON Schema定义
        json_schema = self._generate_json_schema()
        json_file = self.output_dir / f"{base_name}_definitions.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_schema, f, indent=2, ensure_ascii=False, default=str)
        logger.info(f"JSON Schema导出: {json_file}")
        
        # 4. CSV表列表
        csv_file = self.output_dir / f"{base_name}_tables.csv"
        self._export_csv_summary(csv_file)
        logger.info(f"CSV摘要导出: {csv_file}")
        
        return {
            'sql': sql_file,
            'report': report_file,
            'json': json_file,
            'csv': csv_file
        }
    
    def _generate_json_schema(self) -> Dict[str, Any]:
        """生成JSON格式的Schema定义"""
        return {
            'metadata': {
                'generator': 'XMIBasedSchemaGenerator',
                'version': '1.0.0',
                'generated_at': self._get_timestamp(),
                'total_tables': len(self.table_definitions),
                'total_relationships': len(self.reference_relationships)
            },
            'namespaces': self.namespaces,
            'table_definitions': {
                name: {
                    'table_name': table_def.table_name,
                    'xmi_type': table_def.xmi_type,
                    'namespace': table_def.namespace,
                    'instance_count': table_def.instance_count,
                    'parent_tables': table_def.parent_tables,
                    'child_tables': table_def.child_tables,
                    'attributes': {
                        attr_name: {
                            'name': attr_info.name,
                            'value_type': attr_info.value_type,
                            'is_required': attr_info.is_required,
                            'is_reference': attr_info.is_reference,
                            'reference_target': attr_info.reference_target,
                            'sample_values': list(attr_info.sample_values)[:5]  # 限制样本数量
                        } for attr_name, attr_info in table_def.attributes.items()
                    }
                } for name, table_def in self.table_definitions.items()
            },
            'reference_relationships': [
                {
                    'source_table': src_table,
                    'source_attribute': src_attr,
                    'target_table': tgt_table,
                    'target_attribute': tgt_attr
                } for src_table, src_attr, tgt_table, tgt_attr in self.reference_relationships
            ]
        }
    
    def _export_csv_summary(self, csv_file: Path):
        """导出CSV格式的表摘要"""
        import csv
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 表头
            writer.writerow([
                'table_name', 'xmi_type', 'namespace', 'instance_count',
                'attribute_count', 'parent_tables', 'child_tables', 'description'
            ])
            
            # 数据行
            for table_def in self.table_definitions.values():
                writer.writerow([
                    table_def.table_name,
                    table_def.xmi_type,
                    table_def.namespace,
                    table_def.instance_count,
                    len(table_def.attributes),
                    ', '.join(table_def.parent_tables),
                    ', '.join(table_def.child_tables),
                    table_def.description
                ])
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

def main():
    """主函数演示"""
    
    # XMI文件路径
    xmi_file = "biomedical-mbse-platform/resources/domainXmi/UML2.5.xmi"
    
    print("🔍 XMI结构驱动的数据库Schema生成器")
    print("=" * 60)
    
    try:
        # 创建生成器
        generator = XMIBasedSchemaGenerator()
        
        # 分析XMI文件
        print(f"📋 分析XMI文件: {xmi_file}")
        table_definitions = generator.analyze_xmi_file(xmi_file)
        
        print(f"✅ 发现 {len(table_definitions)} 个表定义")
        
        # 导出所有文件
        print(f"💾 导出Schema文件...")
        exported_files = generator.export_schema_files("uml25_xmi_schema")
        
        print(f"\n📊 生成结果:")
        for file_type, file_path in exported_files.items():
            print(f"  {file_type.upper()}: {file_path}")
        
        # 显示一些统计信息
        print(f"\n📈 统计信息:")
        print(f"  表数量: {len(table_definitions)}")
        print(f"  引用关系: {len(generator.reference_relationships)}")
        
        # 显示前5个最重要的表
        important_tables = sorted(table_definitions.values(), 
                                 key=lambda x: (x.instance_count * len(x.attributes)), reverse=True)
        print(f"\n🏆 主要表:")
        for table_def in important_tables[:5]:
            print(f"  - {table_def.table_name}: {table_def.instance_count} instances, {len(table_def.attributes)} attributes")
        
        print(f"\n🎉 XMI Schema生成完成!")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 