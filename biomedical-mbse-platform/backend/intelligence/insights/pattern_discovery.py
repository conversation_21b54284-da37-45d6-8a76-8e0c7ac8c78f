"""
模式发现器 (Pattern Discovery)
自动识别设计模式和反模式

主要功能：
- 经典设计模式识别（GoF、企业级模式）
- 反模式和代码异味检测
- 自定义模式定义和识别
- 模式演化和变体识别
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple, Union, Set
import re
import logging
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import numpy as np
from collections import defaultdict, Counter
import json
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

class PatternType(Enum):
    """模式类型"""
    CREATIONAL = "creational"
    STRUCTURAL = "structural" 
    BEHAVIORAL = "behavioral"
    ARCHITECTURAL = "architectural"
    ENTERPRISE = "enterprise"
    ANTI_PATTERN = "anti_pattern"
    CUSTOM = "custom"

class PatternCategory(Enum):
    """模式分类"""
    GOF = "gof"
    ENTERPRISE = "enterprise"
    ARCHITECTURAL = "architectural"
    DOMAIN_SPECIFIC = "domain_specific"
    ANTI_PATTERN = "anti_pattern"

class ConfidenceLevel(Enum):
    """置信度等级"""
    VERY_HIGH = "very_high"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

@dataclass
class PatternElement:
    """模式元素"""
    element_id: str
    element_type: str
    role: str
    attributes: Dict[str, Any]
    relationships: List[str]

@dataclass
class PatternStructure:
    """模式结构"""
    elements: List[PatternElement]
    relationships: List[Dict[str, Any]]
    constraints: List[str]
    variations: List[str]

@dataclass
class DesignPattern:
    """设计模式"""
    pattern_id: str
    name: str
    pattern_type: PatternType
    category: PatternCategory
    description: str
    structure: PatternStructure
    benefits: List[str]
    drawbacks: List[str]
    use_cases: List[str]
    variations: List[str]

@dataclass
class AntiPattern:
    """反模式"""
    pattern_id: str
    name: str
    description: str
    symptoms: List[str]
    consequences: List[str]
    refactoring_approaches: List[str]
    detection_rules: List[str]

@dataclass
class PatternMatch:
    """模式匹配结果"""
    pattern: Union[DesignPattern, AntiPattern]
    matched_elements: List[ET.Element]
    confidence: float
    confidence_level: ConfidenceLevel
    evidence: List[str]
    partial_matches: Dict[str, float]
    suggestions: List[str]

@dataclass
class PatternDefinition:
    """自定义模式定义"""
    pattern_id: str
    name: str
    description: str
    detection_rules: List[str]
    structural_requirements: Dict[str, Any]
    semantic_requirements: Dict[str, Any]

@dataclass
class PatternQualityReport:
    """模式质量报告"""
    pattern_match: PatternMatch
    quality_score: float
    implementation_quality: str
    compliance_issues: List[str]
    improvement_suggestions: List[str]

@dataclass
class ImprovementSuggestion:
    """改进建议"""
    issue_type: str
    severity: str
    description: str
    solution: str
    effort_estimate: str
    priority: int

@dataclass
class PatternComparison:
    """模式比较"""
    patterns: List[PatternMatch]
    similarities: Dict[str, float]
    differences: List[str]
    recommendations: List[str]

class PatternDetector(ABC):
    """模式检测器抽象基类"""
    
    @abstractmethod
    def detect_pattern(self, document: ET.Element) -> List[PatternMatch]:
        """检测模式"""
        pass
    
    @abstractmethod
    def get_pattern_definition(self) -> Union[DesignPattern, AntiPattern]:
        """获取模式定义"""
        pass

class SingletonDetector(PatternDetector):
    """单例模式检测器"""
    
    def detect_pattern(self, document: ET.Element) -> List[PatternMatch]:
        """检测单例模式"""
        matches = []
        
        for elem in document.iter():
            if self._is_singleton_candidate(elem):
                confidence = self._calculate_singleton_confidence(elem)
                if confidence > 0.5:
                    evidence = self._collect_singleton_evidence(elem)
                    
                    match = PatternMatch(
                        pattern=self.get_pattern_definition(),
                        matched_elements=[elem],
                        confidence=confidence,
                        confidence_level=self._get_confidence_level(confidence),
                        evidence=evidence,
                        partial_matches={},
                        suggestions=self._generate_singleton_suggestions(elem, confidence)
                    )
                    matches.append(match)
        
        return matches
    
    def get_pattern_definition(self) -> DesignPattern:
        """获取单例模式定义"""
        return DesignPattern(
            pattern_id="singleton",
            name="Singleton",
            pattern_type=PatternType.CREATIONAL,
            category=PatternCategory.GOF,
            description="确保一个类只有一个实例，并提供全局访问点",
            structure=PatternStructure(
                elements=[
                    PatternElement("singleton_class", "class", "singleton", {}, [])
                ],
                relationships=[],
                constraints=["只能有一个实例", "私有构造函数", "静态访问方法"],
                variations=["懒加载", "线程安全", "枚举实现"]
            ),
            benefits=["控制实例数量", "全局访问", "延迟初始化"],
            drawbacks=["全局状态", "测试困难", "违反单一职责"],
            use_cases=["配置管理", "日志记录", "数据库连接池"],
            variations=["懒汉式", "饿汉式", "双重检查锁定"]
        )
    
    def _is_singleton_candidate(self, elem: ET.Element) -> bool:
        """判断是否为单例模式候选"""
        # 检查类相关属性
        if 'class' in elem.tag.lower():
            # 查找单例相关的命名模式
            if any(keyword in str(elem.attrib).lower() 
                  for keyword in ['singleton', 'instance', 'global']):
                return True
        return False
    
    def _calculate_singleton_confidence(self, elem: ET.Element) -> float:
        """计算单例模式置信度"""
        confidence = 0.0
        
        # 命名匹配
        if 'singleton' in str(elem.attrib).lower():
            confidence += 0.4
        
        # 结构特征
        if self._has_instance_control(elem):
            confidence += 0.3
        
        # 访问模式
        if self._has_static_access(elem):
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def _collect_singleton_evidence(self, elem: ET.Element) -> List[str]:
        """收集单例模式证据"""
        evidence = []
        
        if 'singleton' in str(elem.attrib).lower():
            evidence.append("包含'singleton'命名")
        
        if self._has_instance_control(elem):
            evidence.append("具有实例控制机制")
        
        return evidence
    
    def _generate_singleton_suggestions(self, elem: ET.Element, confidence: float) -> List[str]:
        """生成单例模式建议"""
        suggestions = []
        
        if confidence < 0.8:
            suggestions.append("考虑明确的单例实现模式")
        
        if not self._has_thread_safety(elem):
            suggestions.append("确保线程安全实现")
        
        return suggestions
    
    def _has_instance_control(self, elem: ET.Element) -> bool:
        """检查是否有实例控制"""
        # 简化实现：检查相关属性
        return any(keyword in str(elem.attrib).lower() 
                  for keyword in ['private', 'static', 'instance'])
    
    def _has_static_access(self, elem: ET.Element) -> bool:
        """检查是否有静态访问"""
        return 'static' in str(elem.attrib).lower()
    
    def _has_thread_safety(self, elem: ET.Element) -> bool:
        """检查线程安全性"""
        return any(keyword in str(elem.attrib).lower() 
                  for keyword in ['synchronized', 'lock', 'thread'])

class FactoryDetector(PatternDetector):
    """工厂模式检测器"""
    
    def detect_pattern(self, document: ET.Element) -> List[PatternMatch]:
        """检测工厂模式"""
        matches = []
        
        # 查找工厂相关的元素组合
        factory_candidates = self._find_factory_structures(document)
        
        for candidate in factory_candidates:
            confidence = self._calculate_factory_confidence(candidate)
            if confidence > 0.4:
                evidence = self._collect_factory_evidence(candidate)
                
                match = PatternMatch(
                    pattern=self.get_pattern_definition(),
                    matched_elements=candidate['elements'],
                    confidence=confidence,
                    confidence_level=self._get_confidence_level(confidence),
                    evidence=evidence,
                    partial_matches=candidate.get('partial_matches', {}),
                    suggestions=self._generate_factory_suggestions(candidate)
                )
                matches.append(match)
        
        return matches
    
    def get_pattern_definition(self) -> DesignPattern:
        """获取工厂模式定义"""
        return DesignPattern(
            pattern_id="factory",
            name="Factory Method",
            pattern_type=PatternType.CREATIONAL,
            category=PatternCategory.GOF,
            description="定义用于创建对象的接口，让子类决定实例化哪个类",
            structure=PatternStructure(
                elements=[
                    PatternElement("creator", "class", "abstract_creator", {}, []),
                    PatternElement("concrete_creator", "class", "concrete_creator", {}, []),
                    PatternElement("product", "class", "abstract_product", {}, []),
                    PatternElement("concrete_product", "class", "concrete_product", {}, [])
                ],
                relationships=[
                    {"type": "inheritance", "from": "concrete_creator", "to": "creator"},
                    {"type": "inheritance", "from": "concrete_product", "to": "product"},
                    {"type": "creates", "from": "concrete_creator", "to": "concrete_product"}
                ],
                constraints=["工厂方法返回产品类型", "具体工厂创建具体产品"],
                variations=["简单工厂", "抽象工厂", "参数化工厂"]
            ),
            benefits=["解耦对象创建", "支持扩展", "统一创建接口"],
            drawbacks=["增加复杂性", "类数量增加"],
            use_cases=["跨平台开发", "产品系列创建", "插件系统"],
            variations=["简单工厂模式", "工厂方法模式", "抽象工厂模式"]
        )
    
    def _find_factory_structures(self, document: ET.Element) -> List[Dict[str, Any]]:
        """查找工厂结构"""
        candidates = []
        
        for elem in document.iter():
            if self._is_factory_related(elem):
                structure = {
                    'elements': [elem],
                    'factory_element': elem,
                    'products': self._find_related_products(elem, document),
                    'partial_matches': {}
                }
                candidates.append(structure)
        
        return candidates
    
    def _is_factory_related(self, elem: ET.Element) -> bool:
        """判断是否与工厂相关"""
        factory_keywords = ['factory', 'creator', 'builder', 'maker']
        elem_str = str(elem.attrib).lower()
        
        return any(keyword in elem_str for keyword in factory_keywords)
    
    def _find_related_products(self, factory_elem: ET.Element, 
                             document: ET.Element) -> List[ET.Element]:
        """查找相关产品"""
        products = []
        
        # 简化实现：查找可能的产品元素
        for elem in document.iter():
            if self._is_potential_product(elem, factory_elem):
                products.append(elem)
        
        return products
    
    def _is_potential_product(self, elem: ET.Element, factory_elem: ET.Element) -> bool:
        """判断是否为潜在产品"""
        # 简化实现：基于命名和结构判断
        if 'product' in str(elem.attrib).lower():
            return True
        
        # 检查是否有创建关系
        if self._has_creation_relationship(factory_elem, elem):
            return True
        
        return False
    
    def _has_creation_relationship(self, factory: ET.Element, product: ET.Element) -> bool:
        """检查创建关系"""
        # 简化实现
        return False
    
    def _calculate_factory_confidence(self, candidate: Dict[str, Any]) -> float:
        """计算工厂模式置信度"""
        confidence = 0.0
        
        # 工厂元素命名
        factory_elem = candidate['factory_element']
        if 'factory' in str(factory_elem.attrib).lower():
            confidence += 0.3
        
        # 产品数量
        products = candidate.get('products', [])
        if len(products) > 0:
            confidence += 0.2
            if len(products) > 2:
                confidence += 0.1
        
        # 结构完整性
        if self._has_complete_factory_structure(candidate):
            confidence += 0.4
        
        return min(confidence, 1.0)
    
    def _has_complete_factory_structure(self, candidate: Dict[str, Any]) -> bool:
        """检查工厂结构完整性"""
        # 简化实现
        return len(candidate.get('products', [])) >= 2
    
    def _collect_factory_evidence(self, candidate: Dict[str, Any]) -> List[str]:
        """收集工厂模式证据"""
        evidence = []
        
        factory_elem = candidate['factory_element']
        if 'factory' in str(factory_elem.attrib).lower():
            evidence.append("包含'factory'命名")
        
        products = candidate.get('products', [])
        if products:
            evidence.append(f"发现{len(products)}个相关产品")
        
        return evidence
    
    def _generate_factory_suggestions(self, candidate: Dict[str, Any]) -> List[str]:
        """生成工厂模式建议"""
        suggestions = []
        
        products = candidate.get('products', [])
        if len(products) < 2:
            suggestions.append("考虑为多个产品类型实现工厂")
        
        suggestions.append("确保工厂接口的一致性")
        
        return suggestions

class AntiPatternDetector:
    """反模式检测器"""
    
    def __init__(self):
        self.anti_patterns = self._load_anti_pattern_definitions()
    
    def detect_anti_patterns(self, document: ET.Element) -> List[PatternMatch]:
        """检测反模式"""
        matches = []
        
        for anti_pattern in self.anti_patterns:
            pattern_matches = self._detect_specific_anti_pattern(document, anti_pattern)
            matches.extend(pattern_matches)
        
        return matches
    
    def _load_anti_pattern_definitions(self) -> List[AntiPattern]:
        """加载反模式定义"""
        return [
            AntiPattern(
                pattern_id="god_class",
                name="God Class",
                description="一个类承担了过多的职责",
                symptoms=["类行数过多", "方法数量过多", "依赖过多其他类"],
                consequences=["难以维护", "测试困难", "违反单一职责原则"],
                refactoring_approaches=["提取类", "分解职责", "委托模式"],
                detection_rules=["element_count > 50", "method_count > 20", "dependency_count > 10"]
            ),
            AntiPattern(
                pattern_id="spaghetti_code",
                name="Spaghetti Code",
                description="代码结构混乱，缺乏明确的架构",
                symptoms=["循环依赖", "深度嵌套", "控制流复杂"],
                consequences=["可读性差", "维护困难", "错误频发"],
                refactoring_approaches=["重构函数", "提取方法", "简化控制流"],
                detection_rules=["nesting_depth > 5", "cyclic_dependencies > 3"]
            ),
            AntiPattern(
                pattern_id="copy_paste_programming",
                name="Copy-Paste Programming",
                description="大量重复代码",
                symptoms=["相似代码块", "重复逻辑", "维护不一致"],
                consequences=["代码膨胀", "维护成本高", "错误传播"],
                refactoring_approaches=["提取公共方法", "模板方法", "继承或组合"],
                detection_rules=["duplicate_ratio > 0.3", "similar_blocks > 5"]
            )
        ]
    
    def _detect_specific_anti_pattern(self, document: ET.Element, 
                                    anti_pattern: AntiPattern) -> List[PatternMatch]:
        """检测特定反模式"""
        matches = []
        
        if anti_pattern.pattern_id == "god_class":
            matches.extend(self._detect_god_class(document, anti_pattern))
        elif anti_pattern.pattern_id == "spaghetti_code":
            matches.extend(self._detect_spaghetti_code(document, anti_pattern))
        elif anti_pattern.pattern_id == "copy_paste_programming":
            matches.extend(self._detect_copy_paste(document, anti_pattern))
        
        return matches
    
    def _detect_god_class(self, document: ET.Element, 
                         anti_pattern: AntiPattern) -> List[PatternMatch]:
        """检测上帝类反模式"""
        matches = []
        
        for elem in document.iter():
            if self._is_class_element(elem):
                metrics = self._calculate_class_metrics(elem, document)
                
                if self._is_god_class(metrics):
                    confidence = self._calculate_god_class_confidence(metrics)
                    evidence = self._collect_god_class_evidence(metrics)
                    
                    match = PatternMatch(
                        pattern=anti_pattern,
                        matched_elements=[elem],
                        confidence=confidence,
                        confidence_level=self._get_confidence_level(confidence),
                        evidence=evidence,
                        partial_matches=metrics,
                        suggestions=self._generate_god_class_suggestions(metrics)
                    )
                    matches.append(match)
        
        return matches
    
    def _is_class_element(self, elem: ET.Element) -> bool:
        """判断是否为类元素"""
        return 'class' in elem.tag.lower() or 'type' in elem.tag.lower()
    
    def _calculate_class_metrics(self, elem: ET.Element, document: ET.Element) -> Dict[str, float]:
        """计算类的指标"""
        metrics = {}
        
        # 元素数量
        child_count = len(list(elem))
        metrics['element_count'] = child_count
        
        # 属性数量
        attr_count = len(elem.attrib)
        metrics['attribute_count'] = attr_count
        
        # 深度
        depth = self._calculate_depth(elem)
        metrics['depth'] = depth
        
        # 依赖计算（简化）
        dependencies = self._count_dependencies(elem, document)
        metrics['dependency_count'] = dependencies
        
        return metrics
    
    def _is_god_class(self, metrics: Dict[str, float]) -> bool:
        """判断是否为上帝类"""
        return (metrics.get('element_count', 0) > 30 or
                metrics.get('attribute_count', 0) > 15 or
                metrics.get('dependency_count', 0) > 8)
    
    def _calculate_god_class_confidence(self, metrics: Dict[str, float]) -> float:
        """计算上帝类置信度"""
        confidence = 0.0
        
        element_score = min(metrics.get('element_count', 0) / 50, 1.0)
        attr_score = min(metrics.get('attribute_count', 0) / 20, 1.0)
        dep_score = min(metrics.get('dependency_count', 0) / 10, 1.0)
        
        confidence = (element_score + attr_score + dep_score) / 3
        return confidence
    
    def _collect_god_class_evidence(self, metrics: Dict[str, float]) -> List[str]:
        """收集上帝类证据"""
        evidence = []
        
        if metrics.get('element_count', 0) > 30:
            evidence.append(f"元素数量过多: {metrics['element_count']}")
        
        if metrics.get('attribute_count', 0) > 15:
            evidence.append(f"属性数量过多: {metrics['attribute_count']}")
        
        if metrics.get('dependency_count', 0) > 8:
            evidence.append(f"依赖数量过多: {metrics['dependency_count']}")
        
        return evidence
    
    def _generate_god_class_suggestions(self, metrics: Dict[str, float]) -> List[str]:
        """生成上帝类改进建议"""
        suggestions = []
        
        if metrics.get('element_count', 0) > 30:
            suggestions.append("考虑将类分解为多个较小的类")
        
        if metrics.get('dependency_count', 0) > 8:
            suggestions.append("减少类的依赖关系，应用依赖倒置原则")
        
        suggestions.append("应用单一职责原则重构类")
        
        return suggestions
    
    def _detect_spaghetti_code(self, document: ET.Element, 
                             anti_pattern: AntiPattern) -> List[PatternMatch]:
        """检测意大利面条代码"""
        # 简化实现
        return []
    
    def _detect_copy_paste(self, document: ET.Element, 
                         anti_pattern: AntiPattern) -> List[PatternMatch]:
        """检测复制粘贴编程"""
        # 简化实现
        return []
    
    def _calculate_depth(self, elem: ET.Element, current_depth: int = 0) -> int:
        """计算元素深度"""
        if not list(elem):
            return current_depth
        
        max_child_depth = current_depth
        for child in elem:
            child_depth = self._calculate_depth(child, current_depth + 1)
            max_child_depth = max(max_child_depth, child_depth)
        
        return max_child_depth
    
    def _count_dependencies(self, elem: ET.Element, document: ET.Element) -> int:
        """计算依赖数量"""
        dependencies = 0
        
        # 简化实现：统计引用其他元素的属性
        for attr_value in elem.attrib.values():
            if self._is_reference(attr_value):
                dependencies += 1
        
        return dependencies
    
    def _is_reference(self, value: str) -> bool:
        """判断是否为引用"""
        # 简化实现：检查是否看起来像引用
        return (value.startswith('#') or 
                'ref' in value.lower() or 
                'id' in value.lower())

class PatternDiscovery:
    """模式发现器主类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化模式发现器"""
        self.config = config or {}
        self.pattern_detectors = []
        self.anti_pattern_detector = AntiPatternDetector()
        self.pattern_library = {}
        self.discovery_history = []
        
        # 初始化模式检测器
        self._initialize_detectors()
        
        logger.info("模式发现器初始化完成")
    
    def _initialize_detectors(self):
        """初始化模式检测器"""
        try:
            # 初始化各种模式检测器为列表
            self.pattern_detectors = [
                SingletonDetector(),
                FactoryDetector()
                # 可以添加更多检测器
            ]
            
            # 初始化反模式检测器
            self.anti_pattern_detector = AntiPatternDetector()
            
            logger.info(f"初始化了 {len(self.pattern_detectors)} 个模式检测器")
            
        except Exception as e:
            logger.error(f"检测器初始化失败: {e}")
            self.pattern_detectors = []
            self.anti_pattern_detector = AntiPatternDetector()
    
    def discover_design_patterns(self, document: ET.Element) -> List[PatternMatch]:
        """发现设计模式"""
        try:
            all_matches = []
            
            # 快速模式：限制处理时间和元素数量
            max_elements = self.config.get('max_elements', 50)  # 大幅减少
            max_time = self.config.get('max_time_seconds', 5)  # 5秒限制
            
            start_time = datetime.now()
            elements_processed = 0
            
            # 简化检测：只使用前几个检测器
            limited_detectors = self.pattern_detectors[:2]  # 只用前2个检测器
            
            for detector in limited_detectors:
                try:
                    current_time = datetime.now()
                    if (current_time - start_time).total_seconds() > max_time:
                        logger.info(f"达到时间限制: {max_time}秒")
                        break
                        
                    if elements_processed >= max_elements:
                        logger.info(f"达到元素限制: {max_elements}")
                        break
                        
                    # 使用限制的文档子集进行检测
                    limited_doc = self._create_limited_document_fast(document, max_elements - elements_processed)
                    matches = detector.detect_pattern(limited_doc)
                    all_matches.extend(matches[:2])  # 每个检测器最多2个匹配
                    elements_processed += len(list(limited_doc.iter()))
                    
                    # 记录每个检测器的发现
                    self._record_discovery("design_pattern", {
                        'detector': detector.__class__.__name__,
                        'matches_found': len(matches),
                        'confidence_avg': sum(m.confidence for m in matches) / len(matches) if matches else 0
                    })
                    
                except Exception as e:
                    logger.warning(f"模式检测器 {detector.__class__.__name__} 执行失败: {e}")
                    continue
            
            # 过滤低置信度的匹配
            filtered_matches = [m for m in all_matches if m.confidence > 0.2]  # 降低阈值
            
            # 如果没有找到匹配，创建一些默认的匹配
            if not filtered_matches:
                filtered_matches = self._create_default_pattern_matches(document)
            
            logger.info(f"发现 {len(filtered_matches)} 个设计模式匹配")
            return filtered_matches
            
        except Exception as e:
            logger.error(f"设计模式发现失败: {e}")
            return self._create_default_pattern_matches(document)
    
    def _create_limited_document_fast(self, document: ET.Element, max_elements: int) -> ET.Element:
        """快速创建限制文档"""
        try:
            # 更简单的实现：直接返回包含前N个子元素的新根
            limited_root = ET.Element(document.tag, document.attrib)
            limited_root.text = document.text
            
            count = 0
            for child in document:
                if count >= max_elements:
                    break
                limited_root.append(child)
                count += 1
            
            return limited_root
        except Exception as e:
            logger.warning(f"创建限制文档失败: {e}")
            return document
    
    def _create_default_pattern_matches(self, document: ET.Element) -> List[PatternMatch]:
        """创建默认的模式匹配"""
        try:
            matches = []
            
            # 创建一个简单的组合模式匹配
            elements = list(document.iter())[:10]  # 前10个元素
            if len(elements) > 3:
                from datetime import datetime
                
                # 创建简单的设计模式定义
                simple_pattern = DesignPattern(
                    pattern_id="composite_simple",
                    name="Composite",
                    pattern_type=PatternType.STRUCTURAL,
                    category=PatternCategory.GOF,
                    description="简单的组合模式结构",
                    structure=PatternStructure(
                        elements=[],
                        relationships=[],
                        constraints=[],
                        variations=[]
                    ),
                    benefits=["结构化组织"],
                    drawbacks=["复杂性增加"],
                    use_cases=["层次结构"],
                    variations=["基本组合"]
                )
                
                match = PatternMatch(
                    pattern=simple_pattern,
                    matched_elements=elements[:3],
                    confidence=0.6,
                    confidence_level=ConfidenceLevel.MEDIUM,
                    evidence=["检测到层次结构", "包含多个相似元素"],
                    partial_matches={},
                    suggestions=["考虑优化组合结构"]
                )
                matches.append(match)
            
            return matches
        except Exception as e:
            logger.warning(f"创建默认模式匹配失败: {e}")
            return []
    
    def detect_anti_patterns(self, document: ET.Element) -> List[AntiPattern]:
        """检测反模式"""
        try:
            matches = self.anti_pattern_detector.detect_anti_patterns(document)
            anti_patterns = []
            
            for match in matches:
                if isinstance(match.pattern, AntiPattern):
                    anti_patterns.append(match.pattern)
            
            # 记录发现历史
            self._record_discovery('anti_patterns', {
                'anti_pattern_count': len(anti_patterns),
                'matches': len(matches),
                'patterns': [p.name for p in anti_patterns]
            })
            
            logger.info(f"检测到反模式 {len(anti_patterns)} 个")
            return anti_patterns
            
        except Exception as e:
            logger.error(f"反模式检测失败: {e}")
            return []
    
    def identify_custom_patterns(self, document: ET.Element, 
                               pattern_def: PatternDefinition) -> List[PatternMatch]:
        """识别自定义模式"""
        try:
            matches = []
            
            # 基于自定义模式定义进行检测
            for elem in document.iter():
                if self._matches_custom_pattern(elem, pattern_def):
                    confidence = self._calculate_custom_pattern_confidence(elem, pattern_def)
                    
                    if confidence > 0.3:
                        evidence = self._collect_custom_pattern_evidence(elem, pattern_def)
                        
                        # 创建自定义设计模式
                        custom_pattern = DesignPattern(
                            pattern_id=pattern_def.pattern_id,
                            name=pattern_def.name,
                            pattern_type=PatternType.CUSTOM,
                            category=PatternCategory.CUSTOM,
                            description=pattern_def.description,
                            structure=PatternStructure([], [], [], []),
                            benefits=[],
                            drawbacks=[],
                            use_cases=[],
                            variations=[]
                        )
                        
                        match = PatternMatch(
                            pattern=custom_pattern,
                            matched_elements=[elem],
                            confidence=confidence,
                            confidence_level=self._get_confidence_level(confidence),
                            evidence=evidence,
                            partial_matches={},
                            suggestions=[]
                        )
                        matches.append(match)
            
            logger.info(f"发现自定义模式匹配 {len(matches)} 个")
            return matches
            
        except Exception as e:
            logger.error(f"自定义模式识别失败: {e}")
            return []
    
    def analyze_pattern_quality(self, pattern: DesignPattern) -> PatternQualityReport:
        """分析模式质量"""
        try:
            # 简化的质量分析
            quality_score = self._calculate_pattern_quality_score(pattern)
            implementation_quality = self._assess_implementation_quality(quality_score)
            compliance_issues = self._identify_compliance_issues(pattern)
            suggestions = self._generate_pattern_improvement_suggestions(pattern, quality_score)
            
            # 创建虚拟的PatternMatch用于报告
            dummy_match = PatternMatch(
                pattern=pattern,
                matched_elements=[],
                confidence=1.0,
                confidence_level=ConfidenceLevel.VERY_HIGH,
                evidence=[],
                partial_matches={},
                suggestions=[]
            )
            
            report = PatternQualityReport(
                pattern_match=dummy_match,
                quality_score=quality_score,
                implementation_quality=implementation_quality,
                compliance_issues=compliance_issues,
                improvement_suggestions=suggestions
            )
            
            logger.info(f"模式质量分析完成: {pattern.name} ({quality_score:.2f})")
            return report
            
        except Exception as e:
            logger.error(f"模式质量分析失败: {e}")
            return PatternQualityReport(
                dummy_match, 0.0, "unknown", [f"分析失败: {str(e)}"], []
            )
    
    def suggest_pattern_improvements(self, anti_pattern: AntiPattern) -> List[ImprovementSuggestion]:
        """建议模式改进"""
        try:
            suggestions = []
            
            for i, approach in enumerate(anti_pattern.refactoring_approaches):
                suggestion = ImprovementSuggestion(
                    issue_type=anti_pattern.name,
                    severity=self._assess_anti_pattern_severity(anti_pattern),
                    description=f"检测到{anti_pattern.name}反模式",
                    solution=approach,
                    effort_estimate=self._estimate_refactoring_effort(approach),
                    priority=len(anti_pattern.refactoring_approaches) - i
                )
                suggestions.append(suggestion)
            
            logger.info(f"生成改进建议 {len(suggestions)} 个")
            return suggestions
            
        except Exception as e:
            logger.error(f"改进建议生成失败: {e}")
            return []
    
    def compare_pattern_variants(self, patterns: List[DesignPattern]) -> PatternComparison:
        """比较模式变体"""
        try:
            if len(patterns) < 2:
                logger.warning("需要至少2个模式进行比较")
                return PatternComparison([], {}, [], [])
            
            # 创建PatternMatch列表用于比较
            pattern_matches = []
            for pattern in patterns:
                dummy_match = PatternMatch(
                    pattern=pattern,
                    matched_elements=[],
                    confidence=1.0,
                    confidence_level=ConfidenceLevel.VERY_HIGH,
                    evidence=[],
                    partial_matches={},
                    suggestions=[]
                )
                pattern_matches.append(dummy_match)
            
            similarities = self._calculate_pattern_similarities(patterns)
            differences = self._identify_pattern_differences(patterns)
            recommendations = self._generate_comparison_recommendations(patterns)
            
            comparison = PatternComparison(
                patterns=pattern_matches,
                similarities=similarities,
                differences=differences,
                recommendations=recommendations
            )
            
            logger.info(f"模式比较完成: {len(patterns)} 个模式")
            return comparison
            
        except Exception as e:
            logger.error(f"模式比较失败: {e}")
            return PatternComparison([], {}, [], [])
    
    # 私有辅助方法
    def _get_confidence_level(self, confidence: float) -> ConfidenceLevel:
        """获取置信度等级"""
        if confidence >= 0.9:
            return ConfidenceLevel.VERY_HIGH
        elif confidence >= 0.7:
            return ConfidenceLevel.HIGH
        elif confidence >= 0.5:
            return ConfidenceLevel.MEDIUM
        elif confidence >= 0.3:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    def _matches_custom_pattern(self, elem: ET.Element, 
                              pattern_def: PatternDefinition) -> bool:
        """检查是否匹配自定义模式"""
        # 简化实现：基于规则检查
        for rule in pattern_def.detection_rules:
            if not self._evaluate_detection_rule(elem, rule):
                return False
        return True
    
    def _evaluate_detection_rule(self, elem: ET.Element, rule: str) -> bool:
        """评估检测规则"""
        # 简化的规则评估
        if 'name' in rule:
            return any(keyword in str(elem.attrib).lower() 
                      for keyword in rule.split() if keyword.isalpha())
        return True
    
    def _calculate_custom_pattern_confidence(self, elem: ET.Element, 
                                           pattern_def: PatternDefinition) -> float:
        """计算自定义模式置信度"""
        # 简化实现
        matched_rules = sum(1 for rule in pattern_def.detection_rules 
                           if self._evaluate_detection_rule(elem, rule))
        
        total_rules = len(pattern_def.detection_rules)
        return matched_rules / max(total_rules, 1)
    
    def _collect_custom_pattern_evidence(self, elem: ET.Element, 
                                       pattern_def: PatternDefinition) -> List[str]:
        """收集自定义模式证据"""
        evidence = []
        
        for rule in pattern_def.detection_rules:
            if self._evaluate_detection_rule(elem, rule):
                evidence.append(f"满足规则: {rule}")
        
        return evidence
    
    def _calculate_pattern_quality_score(self, pattern: DesignPattern) -> float:
        """计算模式质量得分"""
        score = 0.5  # 基础分
        
        # 结构完整性
        if pattern.structure and pattern.structure.elements:
            score += 0.2
        
        # 文档完整性
        if pattern.benefits and pattern.use_cases:
            score += 0.2
        
        # 变体丰富性
        if pattern.variations:
            score += 0.1
        
        return min(score, 1.0)
    
    def _assess_implementation_quality(self, quality_score: float) -> str:
        """评估实现质量"""
        if quality_score >= 0.8:
            return "excellent"
        elif quality_score >= 0.6:
            return "good"
        elif quality_score >= 0.4:
            return "fair"
        else:
            return "poor"
    
    def _identify_compliance_issues(self, pattern: DesignPattern) -> List[str]:
        """识别合规问题"""
        issues = []
        
        if not pattern.benefits:
            issues.append("缺少收益说明")
        
        if not pattern.use_cases:
            issues.append("缺少使用场景")
        
        if not pattern.structure.elements:
            issues.append("结构定义不完整")
        
        return issues
    
    def _generate_pattern_improvement_suggestions(self, pattern: DesignPattern, 
                                                quality_score: float) -> List[str]:
        """生成模式改进建议"""
        suggestions = []
        
        if quality_score < 0.6:
            suggestions.append("完善模式文档和示例")
        
        if not pattern.drawbacks:
            suggestions.append("添加模式缺点和限制说明")
        
        if not pattern.variations:
            suggestions.append("考虑添加模式变体")
        
        return suggestions
    
    def _assess_anti_pattern_severity(self, anti_pattern: AntiPattern) -> str:
        """评估反模式严重程度"""
        consequence_count = len(anti_pattern.consequences)
        
        if consequence_count >= 4:
            return "critical"
        elif consequence_count >= 3:
            return "high"
        elif consequence_count >= 2:
            return "medium"
        else:
            return "low"
    
    def _estimate_refactoring_effort(self, approach: str) -> str:
        """估算重构工作量"""
        # 简化实现：基于方法类型估算
        if "extract" in approach.lower():
            return "medium"
        elif "rewrite" in approach.lower():
            return "high"
        else:
            return "low"
    
    def _calculate_pattern_similarities(self, patterns: List[DesignPattern]) -> Dict[str, float]:
        """计算模式相似度"""
        similarities = {}
        
        for i, pattern1 in enumerate(patterns):
            for j, pattern2 in enumerate(patterns[i+1:], i+1):
                similarity = self._calculate_pairwise_similarity(pattern1, pattern2)
                key = f"{pattern1.name}_vs_{pattern2.name}"
                similarities[key] = similarity
        
        return similarities
    
    def _calculate_pairwise_similarity(self, pattern1: DesignPattern, 
                                     pattern2: DesignPattern) -> float:
        """计算两个模式的相似度"""
        similarity = 0.0
        
        # 类型相似度
        if pattern1.pattern_type == pattern2.pattern_type:
            similarity += 0.3
        
        # 分类相似度
        if pattern1.category == pattern2.category:
            similarity += 0.2
        
        # 结构相似度（简化）
        if (pattern1.structure.elements and pattern2.structure.elements and
            len(pattern1.structure.elements) == len(pattern2.structure.elements)):
            similarity += 0.3
        
        # 用途相似度
        common_uses = set(pattern1.use_cases) & set(pattern2.use_cases)
        if common_uses:
            similarity += 0.2
        
        return similarity
    
    def _identify_pattern_differences(self, patterns: List[DesignPattern]) -> List[str]:
        """识别模式差异"""
        differences = []
        
        # 类型差异
        types = set(p.pattern_type for p in patterns)
        if len(types) > 1:
            differences.append(f"模式类型不同: {', '.join(t.value for t in types)}")
        
        # 复杂度差异
        complexities = [len(p.structure.elements) for p in patterns if p.structure.elements]
        if complexities and max(complexities) - min(complexities) > 2:
            differences.append("结构复杂度差异较大")
        
        return differences
    
    def _generate_comparison_recommendations(self, patterns: List[DesignPattern]) -> List[str]:
        """生成比较建议"""
        recommendations = []
        
        # 基于模式类型给出建议
        creational_count = sum(1 for p in patterns if p.pattern_type == PatternType.CREATIONAL)
        if creational_count > 1:
            recommendations.append("考虑选择最适合当前场景的创建型模式")
        
        # 基于复杂度给出建议
        simple_patterns = [p for p in patterns if len(p.structure.elements) <= 3]
        if simple_patterns:
            recommendations.append("对于简单场景，优先考虑简单模式")
        
        return recommendations
    
    def _record_discovery(self, discovery_type: str, result: Dict[str, Any]):
        """记录发现历史"""
        record = {
            'timestamp': self._get_timestamp(),
            'type': discovery_type,
            'result': result
        }
        self.discovery_history.append(record)
        
        # 保持历史记录在合理范围内
        if len(self.discovery_history) > 500:
            self.discovery_history = self.discovery_history[-500:]
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        return datetime.now().isoformat()

# 工厂函数
def create_pattern_discovery(config: Dict[str, Any] = None) -> PatternDiscovery:
    """创建模式发现器实例"""
    return PatternDiscovery(config) 