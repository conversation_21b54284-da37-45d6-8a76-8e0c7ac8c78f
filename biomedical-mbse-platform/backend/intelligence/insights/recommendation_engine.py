"""
推荐引擎 (Recommendation Engine)
智能改进建议和最佳实践推荐

主要功能：
- 基于当前文档状态的改进建议
- 最佳实践和标准规范推荐
- 个性化建议（基于用户偏好）
- 渐进式改进路径规划
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
import logging
from collections import defaultdict, Counter
from dataclasses import dataclass, field
from enum import Enum
import json
import re
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

class RecommendationType(Enum):
    """推荐类型"""
    IMPROVEMENT = "improvement"
    BEST_PRACTICE = "best_practice"
    REFACTORING = "refactoring"
    OPTIMIZATION = "optimization"
    STANDARDIZATION = "standardization"
    SECURITY = "security"

class Priority(Enum):
    """优先级"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class EffortLevel(Enum):
    """工作量等级"""
    MINIMAL = "minimal"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTENSIVE = "extensive"

@dataclass
class ImprovementRecommendation:
    """改进建议"""
    recommendation_id: str
    title: str
    description: str
    recommendation_type: RecommendationType
    priority: Priority
    effort_level: EffortLevel
    affected_elements: List[str]
    benefits: List[str]
    implementation_steps: List[str]
    estimated_impact: float
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class BestPractice:
    """最佳实践"""
    practice_id: str
    name: str
    description: str
    domain: str
    category: str
    implementation_guide: List[str]
    examples: List[str]
    benefits: List[str]
    prerequisites: List[str]
    difficulty_level: EffortLevel

@dataclass
class DocumentContext:
    """文档上下文"""
    document_type: str
    domain: str
    complexity_level: str
    size_metrics: Dict[str, int]
    quality_metrics: Dict[str, float]
    detected_patterns: List[str]
    identified_issues: List[str]

@dataclass
class QualityIssue:
    """质量问题"""
    issue_id: str
    issue_type: str
    severity: str
    description: str
    affected_elements: List[str]
    root_cause: str

@dataclass
class RefactoringPlan:
    """重构计划"""
    plan_id: str
    target_issues: List[QualityIssue]
    refactoring_steps: List[Dict[str, Any]]
    estimated_effort: str
    expected_benefits: List[str]
    success_metrics: List[str]
    risk_assessment: str

@dataclass
class UserProfile:
    """用户画像"""
    user_id: str
    skill_level: str
    experience_domains: List[str]
    preferences: Dict[str, Any]
    learning_goals: List[str]
    past_interactions: List[Dict[str, Any]]

@dataclass
class UserFeedback:
    """用户反馈"""
    recommendation_id: str
    feedback_type: str
    rating: float
    implemented: bool
    comments: str
    effectiveness: float

@dataclass
class SkillGap:
    """技能差距"""
    skill_area: str
    current_level: str
    target_level: str
    importance: float
    learning_priority: int

@dataclass
class LearningResource:
    """学习资源"""
    resource_id: str
    title: str
    resource_type: str
    description: str
    url: str
    difficulty: str
    estimated_time: str

@dataclass
class DocumentState:
    """文档状态"""
    current_quality_score: float
    complexity_metrics: Dict[str, float]
    identified_patterns: List[str]
    detected_issues: List[str]
    compliance_status: Dict[str, bool]

@dataclass
class ImprovementRoadmap:
    """改进路线图"""
    roadmap_id: str
    phases: List[Dict[str, Any]]
    total_duration: str
    total_effort: str
    expected_roi: float
    risk_factors: List[str]

@dataclass
class Constraints:
    """约束条件"""
    time_constraints: Dict[str, Any]
    resource_constraints: Dict[str, Any]
    technical_constraints: List[str]
    business_constraints: List[str]

@dataclass
class PrioritizedRecommendations:
    """优先级排序的建议"""
    recommendations: List[ImprovementRecommendation]
    prioritization_criteria: Dict[str, float]
    groupings: Dict[str, List[str]]

@dataclass
class EffortEstimate:
    """工作量估算"""
    development_hours: float
    testing_hours: float
    documentation_hours: float
    total_hours: float
    complexity_factors: List[str]
    assumptions: List[str]

class RecommendationEngine:
    """推荐引擎"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化推荐引擎"""
        self.config = config or {}
        
        # 最佳实践库
        self.best_practices_db = {}
        
        # 用户画像存储
        self.user_profiles = {}
        
        # 反馈历史
        self.feedback_history = []
        
        # 推荐历史
        self.recommendation_history = []
        
        # 初始化组件
        self._load_best_practices()
        self._load_improvement_patterns()
        
    def recommend_improvements(self, document: ET.Element) -> List[ImprovementRecommendation]:
        """推荐改进建议 - 快速模式"""
        try:
            recommendations = []
            
            # 快速模式：限制分析深度
            fast_mode = self.config.get('fast_mode', True)
            max_elements = self.config.get('max_elements', 100)
            
            if fast_mode:
                # 创建简化的上下文
                context = self._create_fast_context(document, max_elements)
                
                # 生成基本改进建议（简化版本）
                recommendations = self._generate_fast_recommendations(document, context)
            else:
                # 原有的完整分析
                context = self._analyze_document_context(document)
                
                # 生成结构改进建议
                structural_recs = self._generate_structural_recommendations(document, context)
                recommendations.extend(structural_recs)
                
                # 生成质量改进建议
                quality_recs = self._generate_quality_recommendations(document, context)
                recommendations.extend(quality_recs)
                
                # 生成性能改进建议
                performance_recs = self._generate_performance_recommendations(document, context)
                recommendations.extend(performance_recs)
                
                # 生成标准化建议
                standardization_recs = self._generate_standardization_recommendations(document, context)
                recommendations.extend(standardization_recs)
                
                # 排序和过滤
                recommendations = self._rank_recommendations(recommendations)
            
            # 记录推荐历史
            self._record_recommendations(recommendations)
            
            logger.info(f"生成了 {len(recommendations)} 个改进建议")
            return recommendations
            
        except Exception as e:
            logger.error(f"改进建议生成失败: {e}")
            return self._create_default_recommendations()
    
    def _create_fast_context(self, document: ET.Element, max_elements: int) -> DocumentContext:
        """创建快速上下文"""
        try:
            # 限制分析的元素数量
            elements = list(document.iter())[:max_elements]
            
            return DocumentContext(
                document_type="XML",
                domain="UML",
                complexity_level="medium",
                size_metrics={
                    'total_elements': len(elements),
                    'max_depth': 5,  # 简化值
                    'attributes_count': sum(len(elem.attrib) for elem in elements[:20])
                },
                quality_metrics={
                    'completeness': 0.8,
                    'consistency': 0.7,
                    'clarity': 0.75
                },
                detected_patterns=[],
                identified_issues=["naming_inconsistency", "missing_documentation"]
            )
        except Exception:
            return DocumentContext(
                document_type="XML",
                domain="unknown",
                complexity_level="low",
                size_metrics={'total_elements': 0},
                quality_metrics={'completeness': 0.5},
                detected_patterns=[],
                identified_issues=[]
            )
    
    def _generate_fast_recommendations(self, document: ET.Element, context: DocumentContext) -> List[ImprovementRecommendation]:
        """生成快速推荐"""
        recommendations = []
        
        # 1. 文档结构建议
        rec1 = ImprovementRecommendation(
            recommendation_id=f"struct_{int(datetime.now().timestamp())}",
            title="改善文档结构",
            description="优化XML文档的层次结构和组织方式",
            recommendation_type=RecommendationType.IMPROVEMENT,
            priority=Priority.MEDIUM,
            effort_level=EffortLevel.MEDIUM,
            affected_elements=["document_structure"],
            benefits=["提高可读性", "减少维护成本"],
            implementation_steps=[
                "分析当前结构",
                "重新组织元素层次",
                "验证结构优化效果"
            ],
            estimated_impact=0.7,
            confidence=0.8
        )
        recommendations.append(rec1)
        
        # 2. 命名规范建议
        rec2 = ImprovementRecommendation(
            recommendation_id=f"naming_{int(datetime.now().timestamp())}",
            title="统一命名规范",
            description="建立一致的元素和属性命名约定",
            recommendation_type=RecommendationType.STANDARDIZATION,
            priority=Priority.HIGH,
            effort_level=EffortLevel.LOW,
            affected_elements=["naming_convention"],
            benefits=["提高一致性", "增强可维护性"],
            implementation_steps=[
                "定义命名规范",
                "批量重命名元素",
                "建立验证规则"
            ],
            estimated_impact=0.8,
            confidence=0.9
        )
        recommendations.append(rec2)
        
        # 3. 文档完整性建议
        rec3 = ImprovementRecommendation(
            recommendation_id=f"docs_{int(datetime.now().timestamp())}",
            title="完善文档注释",
            description="为关键元素添加必要的说明和注释",
            recommendation_type=RecommendationType.IMPROVEMENT,
            priority=Priority.MEDIUM,
            effort_level=EffortLevel.LOW,
            affected_elements=["documentation"],
            benefits=["提高理解度", "便于团队协作"],
            implementation_steps=[
                "识别缺少注释的元素",
                "添加描述性注释",
                "建立文档规范"
            ],
            estimated_impact=0.6,
            confidence=0.7
        )
        recommendations.append(rec3)
        
        return recommendations
    
    def _create_default_recommendations(self) -> List[ImprovementRecommendation]:
        """创建默认推荐"""
        return [
            ImprovementRecommendation(
                recommendation_id=f"default_{int(datetime.now().timestamp())}",
                title="基本质量改进",
                description="建议进行基本的质量改进",
                recommendation_type=RecommendationType.IMPROVEMENT,
                priority=Priority.MEDIUM,
                effort_level=EffortLevel.MEDIUM,
                affected_elements=["general"],
                benefits=["提升整体质量"],
                implementation_steps=["评估当前状态", "制定改进计划"],
                estimated_impact=0.5,
                confidence=0.6
            )
        ]
    
    def suggest_best_practices(self, context: DocumentContext) -> List[BestPractice]:
        """建议最佳实践"""
        try:
            relevant_practices = []
            
            # 根据领域筛选
            domain_practices = self._get_practices_by_domain(context.domain)
            
            # 根据复杂度筛选
            complexity_practices = self._get_practices_by_complexity(context.complexity_level)
            
            # 根据检测到的问题筛选
            issue_practices = self._get_practices_for_issues(context.identified_issues)
            
            # 合并并去重
            all_practices = domain_practices + complexity_practices + issue_practices
            relevant_practices = self._deduplicate_practices(all_practices)
            
            # 按相关性排序
            relevant_practices = self._rank_practices(relevant_practices, context)
            
            logger.info(f"推荐了 {len(relevant_practices)} 个最佳实践")
            return relevant_practices
            
        except Exception as e:
            logger.error(f"最佳实践推荐失败: {e}")
            return []
    
    def recommend_refactoring(self, quality_issues: List[QualityIssue]) -> RefactoringPlan:
        """推荐重构方案"""
        try:
            # 分析问题模式
            issue_patterns = self._analyze_issue_patterns(quality_issues)
            
            # 生成重构步骤
            refactoring_steps = self._generate_refactoring_steps(quality_issues, issue_patterns)
            
            # 估算工作量
            effort_estimate = self._estimate_refactoring_effort(refactoring_steps)
            
            # 评估收益
            expected_benefits = self._calculate_refactoring_benefits(quality_issues)
            
            # 定义成功指标
            success_metrics = self._define_success_metrics(quality_issues)
            
            # 风险评估
            risk_assessment = self._assess_refactoring_risks(refactoring_steps)
            
            plan = RefactoringPlan(
                plan_id=f"refactoring_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                target_issues=quality_issues,
                refactoring_steps=refactoring_steps,
                estimated_effort=effort_estimate,
                expected_benefits=expected_benefits,
                success_metrics=success_metrics,
                risk_assessment=risk_assessment
            )
            
            logger.info(f"生成重构计划，包含 {len(refactoring_steps)} 个步骤")
            return plan
            
        except Exception as e:
            logger.error(f"重构计划生成失败: {e}")
            return RefactoringPlan("", [], [], "", [], [], "")
    
    def personalize_recommendations(self, user_profile: UserProfile, 
                                  recommendations: List[ImprovementRecommendation]) -> List[ImprovementRecommendation]:
        """个性化推荐"""
        try:
            personalized = []
            
            for rec in recommendations:
                # 根据用户技能水平调整
                adjusted_rec = self._adjust_for_skill_level(rec, user_profile.skill_level)
                
                # 根据用户偏好过滤
                if self._matches_user_preferences(adjusted_rec, user_profile.preferences):
                    # 计算个性化分数
                    personalized_score = self._calculate_personalization_score(adjusted_rec, user_profile)
                    adjusted_rec.metadata['personalized_score'] = personalized_score
                    personalized.append(adjusted_rec)
            
            # 按个性化分数排序
            personalized.sort(key=lambda x: x.metadata.get('personalized_score', 0), reverse=True)
            
            logger.info(f"个性化后保留 {len(personalized)} 个建议")
            return personalized
            
        except Exception as e:
            logger.error(f"个性化推荐失败: {e}")
            return recommendations
    
    def learn_user_preferences(self, user_feedback: UserFeedback):
        """学习用户偏好"""
        try:
            # 记录反馈
            self.feedback_history.append(user_feedback)
            
            # 更新用户画像
            if user_feedback.recommendation_id in [r.recommendation_id for r in self.recommendation_history]:
                self._update_user_profile_from_feedback(user_feedback)
            
            # 调整推荐算法参数
            self._adjust_recommendation_parameters(user_feedback)
            
            logger.info(f"已学习用户反馈: {user_feedback.feedback_type}")
            
        except Exception as e:
            logger.error(f"用户偏好学习失败: {e}")
    
    def recommend_learning_resources(self, skill_gaps: List[SkillGap]) -> List[LearningResource]:
        """推荐学习资源"""
        try:
            resources = []
            
            for gap in skill_gaps:
                # 查找相关资源
                relevant_resources = self._find_resources_for_skill(gap.skill_area, gap.target_level)
                
                # 按优先级排序
                relevant_resources.sort(key=lambda x: self._calculate_resource_relevance(x, gap))
                
                resources.extend(relevant_resources[:3])  # 每个技能推荐3个资源
            
            # 去重并最终排序
            unique_resources = self._deduplicate_resources(resources)
            
            logger.info(f"推荐了 {len(unique_resources)} 个学习资源")
            return unique_resources
            
        except Exception as e:
            logger.error(f"学习资源推荐失败: {e}")
            return []
    
    def plan_improvement_roadmap(self, current_state: DocumentState, 
                               target_state: DocumentState) -> ImprovementRoadmap:
        """规划改进路线图"""
        try:
            # 分析差距
            gaps = self._analyze_state_gaps(current_state, target_state)
            
            # 生成改进阶段
            phases = self._generate_improvement_phases(gaps)
            
            # 估算时间和工作量
            duration, effort = self._estimate_roadmap_effort(phases)
            
            # 计算ROI
            roi = self._calculate_improvement_roi(current_state, target_state, effort)
            
            # 识别风险因素
            risks = self._identify_roadmap_risks(phases)
            
            roadmap = ImprovementRoadmap(
                roadmap_id=f"roadmap_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                phases=phases,
                total_duration=duration,
                total_effort=effort,
                expected_roi=roi,
                risk_factors=risks
            )
            
            logger.info(f"生成改进路线图，包含 {len(phases)} 个阶段")
            return roadmap
            
        except Exception as e:
            logger.error(f"改进路线图规划失败: {e}")
            return ImprovementRoadmap("", [], "", "", 0.0, [])
    
    def prioritize_recommendations(self, recommendations: List[ImprovementRecommendation], 
                                 constraints: Constraints) -> PrioritizedRecommendations:
        """优先级排序推荐"""
        try:
            # 定义优先级权重
            criteria = {
                'business_impact': 0.3,
                'implementation_ease': 0.2,
                'resource_availability': 0.2,
                'risk_level': 0.15,
                'urgency': 0.15
            }
            
            # 计算优先级分数
            scored_recommendations = []
            for rec in recommendations:
                score = self._calculate_priority_score(rec, constraints, criteria)
                rec.metadata['priority_score'] = score
                scored_recommendations.append(rec)
            
            # 排序
            scored_recommendations.sort(key=lambda x: x.metadata['priority_score'], reverse=True)
            
            # 分组
            groupings = self._group_recommendations(scored_recommendations)
            
            return PrioritizedRecommendations(
                recommendations=scored_recommendations,
                prioritization_criteria=criteria,
                groupings=groupings
            )
            
        except Exception as e:
            logger.error(f"优先级排序失败: {e}")
            return PrioritizedRecommendations(recommendations, {}, {})
    
    def estimate_implementation_effort(self, recommendation: ImprovementRecommendation) -> EffortEstimate:
        """估算实施工作量"""
        try:
            # 基础工作量估算
            base_hours = self._estimate_base_hours(recommendation)
            
            # 复杂度因子调整
            complexity_factors = self._identify_complexity_factors(recommendation)
            complexity_multiplier = self._calculate_complexity_multiplier(complexity_factors)
            
            # 分解工作量
            dev_hours = base_hours * 0.6 * complexity_multiplier
            test_hours = base_hours * 0.25 * complexity_multiplier
            doc_hours = base_hours * 0.15 * complexity_multiplier
            
            total_hours = dev_hours + test_hours + doc_hours
            
            # 识别假设条件
            assumptions = self._identify_effort_assumptions(recommendation)
            
            return EffortEstimate(
                development_hours=dev_hours,
                testing_hours=test_hours,
                documentation_hours=doc_hours,
                total_hours=total_hours,
                complexity_factors=complexity_factors,
                assumptions=assumptions
            )
            
        except Exception as e:
            logger.error(f"工作量估算失败: {e}")
            return EffortEstimate(0, 0, 0, 0, [], [])
    
    # 私有方法
    def _analyze_document_context(self, document: ET.Element) -> DocumentContext:
        """分析文档上下文"""
        # 计算大小指标
        size_metrics = {
            'total_elements': len(list(document.iter())),
            'max_depth': self._calculate_max_depth(document),
            'unique_tags': len(set(elem.tag for elem in document.iter()))
        }
        
        # 计算质量指标（简化版）
        quality_metrics = {
            'completeness': 0.8,  # 示例值
            'consistency': 0.7,
            'clarity': 0.75
        }
        
        return DocumentContext(
            document_type="xml",
            domain="general",
            complexity_level="medium",
            size_metrics=size_metrics,
            quality_metrics=quality_metrics,
            detected_patterns=[],
            identified_issues=[]
        )
    
    def _generate_structural_recommendations(self, document: ET.Element, 
                                           context: DocumentContext) -> List[ImprovementRecommendation]:
        """生成结构改进建议"""
        recommendations = []
        
        # 检查深度嵌套
        max_depth = context.size_metrics.get('max_depth', 0)
        if max_depth > 8:
            rec = ImprovementRecommendation(
                recommendation_id=f"struct_depth_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                title="减少嵌套深度",
                description=f"当前最大嵌套深度为{max_depth}，建议重构以减少复杂度",
                recommendation_type=RecommendationType.IMPROVEMENT,
                priority=Priority.MEDIUM,
                effort_level=EffortLevel.MEDIUM,
                affected_elements=["深度嵌套元素"],
                benefits=["提高可读性", "减少维护成本", "改善性能"],
                implementation_steps=[
                    "识别深度嵌套的元素",
                    "重构为更扁平的结构",
                    "使用组合模式替代深度继承"
                ],
                estimated_impact=0.6,
                confidence=0.8
            )
            recommendations.append(rec)
        
        return recommendations
    
    def _generate_quality_recommendations(self, document: ET.Element, 
                                        context: DocumentContext) -> List[ImprovementRecommendation]:
        """生成质量改进建议"""
        recommendations = []
        
        # 检查命名一致性
        naming_issues = self._check_naming_consistency(document)
        if naming_issues:
            rec = ImprovementRecommendation(
                recommendation_id=f"naming_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                title="统一命名规范",
                description="发现不一致的命名风格，建议统一命名规范",
                recommendation_type=RecommendationType.STANDARDIZATION,
                priority=Priority.LOW,
                effort_level=EffortLevel.LOW,
                affected_elements=["命名不一致的元素"],
                benefits=["提高可读性", "减少理解成本", "便于维护"],
                implementation_steps=[
                    "建立命名规范文档",
                    "使用工具检查命名一致性",
                    "重命名不符合规范的元素"
                ],
                estimated_impact=0.4,
                confidence=0.7
            )
            recommendations.append(rec)
        
        return recommendations
    
    def _generate_performance_recommendations(self, document: ET.Element, 
                                            context: DocumentContext) -> List[ImprovementRecommendation]:
        """生成性能改进建议"""
        recommendations = []
        
        # 检查文档大小
        total_elements = context.size_metrics.get('total_elements', 0)
        if total_elements > 1000:
            rec = ImprovementRecommendation(
                recommendation_id=f"performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                title="优化文档大小",
                description=f"文档包含{total_elements}个元素，可能影响处理性能",
                recommendation_type=RecommendationType.OPTIMIZATION,
                priority=Priority.MEDIUM,
                effort_level=EffortLevel.MEDIUM,
                affected_elements=["大型文档"],
                benefits=["提高处理速度", "减少内存使用", "改善用户体验"],
                implementation_steps=[
                    "分析文档结构",
                    "拆分大型文档",
                    "实现延迟加载机制"
                ],
                estimated_impact=0.7,
                confidence=0.8
            )
            recommendations.append(rec)
        
        return recommendations
    
    def _generate_standardization_recommendations(self, document: ET.Element, 
                                                context: DocumentContext) -> List[ImprovementRecommendation]:
        """生成标准化建议"""
        recommendations = []
        
        # 检查命名空间使用
        namespaces = set()
        for elem in document.iter():
            if ':' in elem.tag:
                ns = elem.tag.split(':')[0]
                namespaces.add(ns)
        
        if len(namespaces) > 5:
            rec = ImprovementRecommendation(
                recommendation_id=f"namespace_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                title="简化命名空间使用",
                description=f"文档使用了{len(namespaces)}个命名空间，建议简化",
                recommendation_type=RecommendationType.STANDARDIZATION,
                priority=Priority.LOW,
                effort_level=EffortLevel.LOW,
                affected_elements=["命名空间"],
                benefits=["减少复杂度", "提高可读性", "便于理解"],
                implementation_steps=[
                    "审查命名空间必要性",
                    "合并相似的命名空间",
                    "更新文档说明"
                ],
                estimated_impact=0.3,
                confidence=0.6
            )
            recommendations.append(rec)
        
        return recommendations
    
    def _rank_recommendations(self, recommendations: List[ImprovementRecommendation]) -> List[ImprovementRecommendation]:
        """排序推荐建议"""
        # 简化的排序逻辑：优先级 + 影响 + 置信度
        def score(rec):
            priority_score = {
                Priority.CRITICAL: 1.0,
                Priority.HIGH: 0.8,
                Priority.MEDIUM: 0.6,
                Priority.LOW: 0.4
            }.get(rec.priority, 0.5)
            
            return priority_score * 0.4 + rec.estimated_impact * 0.3 + rec.confidence * 0.3
        
        recommendations.sort(key=score, reverse=True)
        return recommendations
    
    def _check_naming_consistency(self, document: ET.Element) -> bool:
        """检查命名一致性"""
        patterns = defaultdict(int)
        
        for elem in document.iter():
            tag = elem.tag.split(':')[-1] if ':' in elem.tag else elem.tag
            if re.match(r'^[a-z][a-zA-Z0-9]*$', tag):
                patterns['camelCase'] += 1
            elif re.match(r'^[A-Z][a-zA-Z0-9]*$', tag):
                patterns['PascalCase'] += 1
            elif '_' in tag:
                patterns['snake_case'] += 1
        
        return len([p for p in patterns.values() if p > 0]) > 1
    
    def _calculate_max_depth(self, element: ET.Element, current_depth: int = 0) -> int:
        """计算最大深度"""
        if not list(element):
            return current_depth
        
        return max(self._calculate_max_depth(child, current_depth + 1) for child in element)
    
    def _record_recommendations(self, recommendations: List[ImprovementRecommendation]):
        """记录推荐历史"""
        for rec in recommendations:
            self.recommendation_history.append({
                'timestamp': datetime.now().isoformat(),
                'recommendation': rec,
                'status': 'generated'
            })
    
    def _load_best_practices(self):
        """加载最佳实践"""
        # 这里可以从文件或数据库加载最佳实践
        pass
    
    def _load_improvement_patterns(self):
        """加载改进模式"""
        # 这里可以加载预定义的改进模式
        pass

# 工厂函数
def create_recommendation_engine(config: Dict[str, Any] = None) -> RecommendationEngine:
    """创建推荐引擎实例
    
    Args:
        config: 配置参数
        
    Returns:
        RecommendationEngine实例
    """
    if config is None:
        config = {}
    
    logger.info("创建推荐引擎实例")
    return RecommendationEngine(config)

# 预设配置
DEFAULT_CONFIG = {
    'max_recommendations': 10,
    'personalization_enabled': True,
    'learning_rate': 0.1,
    'confidence_threshold': 0.5
} 