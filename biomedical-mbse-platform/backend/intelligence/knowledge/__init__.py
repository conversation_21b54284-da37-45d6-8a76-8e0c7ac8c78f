#!/usr/bin/env python3
"""
智能分析模块 - 知识管理层
提供本体管理、术语数据库、模式库、最佳实践等知识管理功能
"""

from .ontology_manager import (
    OntologyManager,
    OntologyConcept,
    ConceptMapping,
    ReasoningResult,
    OntologyType,
    ConceptRelationType,
    create_ontology_manager
)
from .terminology_db import (
    TerminologyDB,
    create_terminology_db,
    Term,
    Language,
    TermStatus,
    TermQuery
)
from .pattern_library import (
    PatternLibrary,
    create_pattern_library,
    DesignPattern,
    AntiPattern,
    PatternMatch,
    PatternCategory,
    PatternScope,
    PatternComplexity
)
from .best_practices import (
    BestPractices,
    create_best_practices,
    BestPractice,
    PracticeAssessment,
    ComplianceReport,
    ImprovementSuggestion,
    PracticeDomain,
    PracticeCategory,
    MaturityLevel
)

__all__ = [
    'OntologyManager',
    'OntologyConcept',
    'ConceptMapping',
    'ReasoningResult',
    'OntologyType',
    'ConceptRelationType',
    'create_ontology_manager',
    'TerminologyDB',
    'create_terminology_db',
    'Term',
    'Language',
    'TermStatus',
    'TermQuery',
    'PatternLibrary',
    'create_pattern_library',
    'DesignPattern',
    'AntiPattern',
    'PatternMatch',
    'PatternCategory',
    'PatternScope',
    'PatternComplexity',
    'BestPractices',
    'create_best_practices',
    'BestPractice',
    'PracticeAssessment',
    'ComplianceReport',
    'ImprovementSuggestion',
    'PracticeDomain',
    'PracticeCategory',
    'MaturityLevel'
]

__version__ = '1.0.0' 