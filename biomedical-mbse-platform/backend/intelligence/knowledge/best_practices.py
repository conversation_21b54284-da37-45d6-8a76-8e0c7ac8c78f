#!/usr/bin/env python3
"""
最佳实践库 - 行业最佳实践和标准规范
包含MBSE、软件工程、企业架构、质量保证等领域的最佳实践知识库
"""

import xml.etree.ElementTree as ET
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Set, Tuple
from enum import Enum
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class PracticeDomain(Enum):
    """实践领域"""
    MBSE = "mbse"                           # 基于模型的系统工程
    SOFTWARE_ENGINEERING = "software_eng"   # 软件工程
    ENTERPRISE_ARCHITECTURE = "enterprise_arch"  # 企业架构
    QUALITY_ASSURANCE = "quality_assurance"  # 质量保证
    PROJECT_MANAGEMENT = "project_mgmt"      # 项目管理
    SYSTEMS_ENGINEERING = "systems_eng"     # 系统工程
    DATA_MANAGEMENT = "data_mgmt"           # 数据管理
    SECURITY = "security"                   # 安全

class PracticeCategory(Enum):
    """实践类别"""
    PROCESS = "process"                     # 流程
    METHODOLOGY = "methodology"             # 方法论
    TOOL_USAGE = "tool_usage"              # 工具使用
    DESIGN_PRINCIPLE = "design_principle"   # 设计原则
    CODING_STANDARD = "coding_standard"     # 编码标准
    DOCUMENTATION = "documentation"         # 文档
    TESTING = "testing"                     # 测试
    DEPLOYMENT = "deployment"               # 部署
    MAINTENANCE = "maintenance"             # 维护

class MaturityLevel(Enum):
    """成熟度级别"""
    INITIAL = 1          # 初始级
    MANAGED = 2          # 管理级
    DEFINED = 3          # 已定义级
    QUANTITATIVELY_MANAGED = 4  # 量化管理级
    OPTIMIZING = 5       # 优化级

class PriorityLevel(Enum):
    """优先级"""
    CRITICAL = "critical"    # 关键
    HIGH = "high"           # 高
    MEDIUM = "medium"       # 中
    LOW = "low"             # 低

@dataclass
class PracticeStandard:
    """实践标准"""
    standard_id: str
    name: str
    organization: str                    # 发布组织
    version: str
    description: str
    domain: PracticeDomain
    website_url: Optional[str] = None
    publication_date: Optional[datetime] = None

@dataclass
class PracticeEvidence:
    """实践证据"""
    evidence_type: str                   # 证据类型
    description: str                     # 描述
    source: str                         # 来源
    confidence_level: float             # 置信度 0-1
    supporting_data: List[str] = field(default_factory=list)

@dataclass
class BestPractice:
    """最佳实践"""
    practice_id: str
    name: str
    domain: PracticeDomain
    category: PracticeCategory
    description: str
    objective: str                       # 目标
    benefits: List[str]                  # 益处
    implementation_steps: List[str]      # 实施步骤
    success_criteria: List[str]          # 成功标准
    common_pitfalls: List[str]           # 常见陷阱
    related_practices: List[str] = field(default_factory=list)
    standards: List[PracticeStandard] = field(default_factory=list)
    evidence: List[PracticeEvidence] = field(default_factory=list)
    maturity_level: MaturityLevel = MaturityLevel.DEFINED
    priority: PriorityLevel = PriorityLevel.MEDIUM
    effort_estimate: str = "medium"      # 实施工作量估计
    tags: Set[str] = field(default_factory=set)
    last_updated: Optional[datetime] = None

@dataclass
class PracticeAssessment:
    """实践评估"""
    practice: BestPractice
    current_maturity: MaturityLevel
    target_maturity: MaturityLevel
    compliance_score: float              # 合规性评分 0-1
    gap_analysis: List[str]              # 差距分析
    improvement_recommendations: List[str]  # 改进建议
    assessment_date: datetime = field(default_factory=datetime.now)

@dataclass
class ComplianceReport:
    """合规性报告"""
    document_id: str
    domain: PracticeDomain
    assessed_practices: List[PracticeAssessment]
    overall_compliance: float            # 总体合规性 0-1
    critical_gaps: List[str]             # 关键差距
    priority_improvements: List[str]     # 优先改进项
    maturity_distribution: Dict[MaturityLevel, int]  # 成熟度分布
    generated_date: datetime = field(default_factory=datetime.now)

@dataclass
class ImprovementSuggestion:
    """改进建议"""
    suggestion_id: str
    title: str
    description: str
    related_practice: str
    impact: str                          # 影响程度
    effort: str                          # 实施难度
    timeline: str                        # 时间线
    prerequisites: List[str] = field(default_factory=list)
    expected_benefits: List[str] = field(default_factory=list)

class BestPractices:
    """最佳实践库"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化最佳实践库"""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 实践知识库
        self.practices: Dict[str, BestPractice] = {}
        self.standards: Dict[str, PracticeStandard] = {}
        self.practice_by_domain: Dict[PracticeDomain, List[BestPractice]] = {}
        
        # 初始化实践库
        self._initialize_mbse_practices()
        self._initialize_software_engineering_practices()
        self._initialize_enterprise_architecture_practices()
        self._initialize_quality_assurance_practices()
        self._initialize_standards()
        
        # 建立索引
        self._build_domain_index()
        
        self.logger.info("最佳实践库初始化完成")
    
    def _initialize_mbse_practices(self):
        """初始化MBSE最佳实践"""
        
        # MBSE核心实践
        self.practices["mbse_model_architecture"] = BestPractice(
            practice_id="mbse_model_architecture",
            name="模型架构设计",
            domain=PracticeDomain.MBSE,
            category=PracticeCategory.DESIGN_PRINCIPLE,
            description="建立清晰、分层的模型架构，确保模型的可理解性和可维护性",
            objective="创建结构化、可扩展的系统模型",
            benefits=[
                "提高模型的可理解性",
                "促进团队协作",
                "支持模型重用",
                "简化维护工作",
                "提高系统质量"
            ],
            implementation_steps=[
                "定义模型架构框架",
                "建立分层结构",
                "定义视图和视角",
                "建立模型元素命名规范",
                "创建模型导航机制",
                "建立模型验证规则"
            ],
            success_criteria=[
                "模型层次结构清晰",
                "模型元素命名一致",
                "视图之间关系明确",
                "模型验证通过率>95%",
                "团队成员理解度>80%"
            ],
            common_pitfalls=[
                "过度复杂的层次结构",
                "缺乏命名规范",
                "视图之间不一致",
                "缺乏验证机制",
                "文档不充分"
            ],
            maturity_level=MaturityLevel.DEFINED,
            priority=PriorityLevel.HIGH,
            tags={"architecture", "modeling", "structure", "framework"}
        )
        
        self.practices["requirements_traceability"] = BestPractice(
            practice_id="requirements_traceability",
            name="需求可追踪性管理",
            domain=PracticeDomain.MBSE,
            category=PracticeCategory.PROCESS,
            description="建立从需求到设计、实现和测试的完整追踪链",
            objective="确保所有需求都被正确实现和验证",
            benefits=[
                "确保需求完整性",
                "支持变更影响分析",
                "提高系统质量",
                "简化审计过程",
                "降低风险"
            ],
            implementation_steps=[
                "定义追踪策略",
                "建立追踪矩阵",
                "实施自动化追踪",
                "定期验证追踪关系",
                "生成追踪报告"
            ],
            success_criteria=[
                "需求覆盖率100%",
                "追踪关系准确率>98%",
                "变更影响分析时间<1天",
                "审计通过率100%"
            ],
            common_pitfalls=[
                "追踪关系不完整",
                "手工维护追踪信息",
                "缺乏自动化工具",
                "追踪粒度不当"
            ],
            maturity_level=MaturityLevel.QUANTITATIVELY_MANAGED,
            priority=PriorityLevel.CRITICAL,
            tags={"requirements", "traceability", "verification", "compliance"}
        )
        
        self.practices["model_verification"] = BestPractice(
            practice_id="model_verification",
            name="模型验证与确认",
            domain=PracticeDomain.MBSE,
            category=PracticeCategory.TESTING,
            description="系统性验证模型的正确性、完整性和一致性",
            objective="确保模型准确反映系统需求和设计意图",
            benefits=[
                "早期发现错误",
                "提高模型质量",
                "增强置信度",
                "减少返工",
                "支持决策"
            ],
            implementation_steps=[
                "定义验证标准",
                "实施语法检查",
                "执行语义验证",
                "进行一致性检查",
                "开展同行评审",
                "生成验证报告"
            ],
            success_criteria=[
                "语法错误率<1%",
                "语义错误率<2%",
                "一致性检查通过率>95%",
                "评审发现问题解决率100%"
            ],
            common_pitfalls=[
                "验证标准不明确",
                "缺乏自动化验证",
                "验证覆盖不全",
                "忽视语义验证"
            ],
            maturity_level=MaturityLevel.DEFINED,
            priority=PriorityLevel.HIGH,
            tags={"verification", "validation", "quality", "consistency"}
        )
    
    def _initialize_software_engineering_practices(self):
        """初始化软件工程最佳实践"""
        
        # SOLID原则
        self.practices["solid_principles"] = BestPractice(
            practice_id="solid_principles",
            name="SOLID设计原则",
            domain=PracticeDomain.SOFTWARE_ENGINEERING,
            category=PracticeCategory.DESIGN_PRINCIPLE,
            description="应用SOLID五大设计原则，提高代码质量和可维护性",
            objective="创建松耦合、高内聚、易扩展的软件设计",
            benefits=[
                "提高代码可维护性",
                "增强代码可扩展性",
                "降低耦合度",
                "提高代码重用性",
                "简化测试"
            ],
            implementation_steps=[
                "理解SOLID原则",
                "识别违反原则的代码",
                "重构现有代码",
                "在新开发中应用原则",
                "建立代码审查机制",
                "度量代码质量"
            ],
            success_criteria=[
                "代码审查通过率>90%",
                "代码重用率>30%",
                "缺陷密度<0.1/KLOC",
                "维护时间减少50%"
            ],
            common_pitfalls=[
                "过度设计",
                "原则应用不当",
                "缺乏重构意识",
                "忽视性能影响"
            ],
            maturity_level=MaturityLevel.DEFINED,
            priority=PriorityLevel.HIGH,
            tags={"solid", "design_principles", "oop", "refactoring"}
        )
        
        # Clean Code
        self.practices["clean_code"] = BestPractice(
            practice_id="clean_code",
            name="整洁代码实践",
            domain=PracticeDomain.SOFTWARE_ENGINEERING,
            category=PracticeCategory.CODING_STANDARD,
            description="编写清晰、简洁、可读的代码",
            objective="提高代码质量和团队开发效率",
            benefits=[
                "提高代码可读性",
                "降低维护成本",
                "减少错误",
                "促进团队协作",
                "加快开发速度"
            ],
            implementation_steps=[
                "制定编码规范",
                "使用有意义的命名",
                "编写小而专注的函数",
                "添加适当注释",
                "定期重构代码",
                "进行代码审查"
            ],
            success_criteria=[
                "代码审查评分>4.0/5.0",
                "函数平均长度<20行",
                "变量命名规范性>95%",
                "注释覆盖率适中(10-20%)"
            ],
            common_pitfalls=[
                "过度注释",
                "函数过长",
                "命名不规范",
                "缺乏重构"
            ],
            maturity_level=MaturityLevel.MANAGED,
            priority=PriorityLevel.MEDIUM,
            tags={"clean_code", "readability", "naming", "refactoring"}
        )
    
    def _initialize_enterprise_architecture_practices(self):
        """初始化企业架构最佳实践"""
        
        # TOGAF实践
        self.practices["togaf_adm"] = BestPractice(
            practice_id="togaf_adm",
            name="TOGAF架构开发方法",
            domain=PracticeDomain.ENTERPRISE_ARCHITECTURE,
            category=PracticeCategory.METHODOLOGY,
            description="使用TOGAF ADM进行企业架构开发",
            objective="系统性地开发和管理企业架构",
            benefits=[
                "标准化架构过程",
                "提高架构质量",
                "促进stakeholder沟通",
                "支持架构治理",
                "降低风险"
            ],
            implementation_steps=[
                "建立架构能力",
                "执行预备阶段",
                "开展架构愿景",
                "开发业务架构",
                "开发信息系统架构",
                "开发技术架构",
                "制定机会与解决方案",
                "进行迁移规划",
                "实施治理",
                "管理架构变更"
            ],
            success_criteria=[
                "架构文档完整性>90%",
                "stakeholder满意度>80%",
                "架构决策追踪率100%",
                "合规性检查通过率>95%"
            ],
            common_pitfalls=[
                "过度文档化",
                "忽视业务需求",
                "缺乏stakeholder参与",
                "治理机制不足"
            ],
            maturity_level=MaturityLevel.DEFINED,
            priority=PriorityLevel.HIGH,
            tags={"togaf", "adm", "enterprise_architecture", "methodology"}
        )
    
    def _initialize_quality_assurance_practices(self):
        """初始化质量保证最佳实践"""
        
        # ISO 9001质量管理
        self.practices["iso9001_quality"] = BestPractice(
            practice_id="iso9001_quality",
            name="ISO 9001质量管理体系",
            domain=PracticeDomain.QUALITY_ASSURANCE,
            category=PracticeCategory.PROCESS,
            description="建立符合ISO 9001标准的质量管理体系",
            objective="确保产品和服务持续满足客户和法规要求",
            benefits=[
                "提高客户满意度",
                "改进产品质量",
                "降低运营成本",
                "增强市场竞争力",
                "提高组织效率"
            ],
            implementation_steps=[
                "理解标准要求",
                "进行现状评估",
                "制定质量政策",
                "建立质量目标",
                "设计质量流程",
                "实施质量活动",
                "监控质量绩效",
                "持续改进"
            ],
            success_criteria=[
                "认证审核通过",
                "客户满意度>85%",
                "产品合格率>98%",
                "过程绩效达标率>90%"
            ],
            common_pitfalls=[
                "流于形式",
                "缺乏高层支持",
                "员工培训不足",
                "文档管理混乱"
            ],
            maturity_level=MaturityLevel.QUANTITATIVELY_MANAGED,
            priority=PriorityLevel.HIGH,
            tags={"iso9001", "quality_management", "certification", "compliance"}
        )
    
    def _initialize_standards(self):
        """初始化标准库"""
        
        # MBSE标准
        self.standards["iso15288"] = PracticeStandard(
            standard_id="iso15288",
            name="ISO/IEC/IEEE 15288:2015 Systems and software engineering",
            organization="ISO/IEC/IEEE",
            version="2015",
            description="系统和软件工程生命周期过程",
            domain=PracticeDomain.SYSTEMS_ENGINEERING,
            website_url="https://www.iso.org/standard/63711.html"
        )
        
        self.standards["incose_se_handbook"] = PracticeStandard(
            standard_id="incose_se_handbook",
            name="INCOSE Systems Engineering Handbook",
            organization="INCOSE",
            version="4.0",
            description="系统工程手册",
            domain=PracticeDomain.SYSTEMS_ENGINEERING,
            website_url="https://www.incose.org/"
        )
        
        # 软件工程标准
        self.standards["iso25010"] = PracticeStandard(
            standard_id="iso25010",
            name="ISO/IEC 25010:2011 Software Quality Model",
            organization="ISO/IEC",
            version="2011",
            description="软件质量模型",
            domain=PracticeDomain.SOFTWARE_ENGINEERING,
            website_url="https://www.iso.org/standard/35733.html"
        )
        
        # 质量标准
        self.standards["iso9001"] = PracticeStandard(
            standard_id="iso9001",
            name="ISO 9001:2015 Quality Management Systems",
            organization="ISO",
            version="2015",
            description="质量管理体系要求",
            domain=PracticeDomain.QUALITY_ASSURANCE,
            website_url="https://www.iso.org/standard/62085.html"
        )
    
    def _build_domain_index(self):
        """建立领域索引"""
        for practice in self.practices.values():
            if practice.domain not in self.practice_by_domain:
                self.practice_by_domain[practice.domain] = []
            self.practice_by_domain[practice.domain].append(practice)
    
    def search_practices(self, 
                        domain: Optional[PracticeDomain] = None, 
                        category: Optional[PracticeCategory] = None,
                        keyword: Optional[str] = None) -> List[BestPractice]:
        """搜索最佳实践"""
        results = []
        
        practices_to_search = self.practices.values()
        
        # 领域过滤
        if domain:
            practices_to_search = self.practice_by_domain.get(domain, [])
        
        for practice in practices_to_search:
            # 类别过滤
            if category and practice.category != category:
                continue
            
            # 关键词过滤
            if keyword and not self._matches_keyword(practice, keyword):
                continue
            
            results.append(practice)
        
        return results
    
    def _matches_keyword(self, practice: BestPractice, keyword: str) -> bool:
        """检查实践是否匹配关键词"""
        keyword_lower = keyword.lower()
        
        # 检查名称
        if keyword_lower in practice.name.lower():
            return True
        
        # 检查描述
        if keyword_lower in practice.description.lower():
            return True
        
        # 检查标签
        for tag in practice.tags:
            if keyword_lower in tag.lower():
                return True
        
        return False
    
    def assess_practice_compliance(self, 
                                 document: ET.Element, 
                                 practices: List[BestPractice]) -> ComplianceReport:
        """评估实践合规性"""
        
        # 分析文档结构
        doc_analysis = self._analyze_document_for_practices(document)
        
        assessed_practices = []
        total_score = 0.0
        
        for practice in practices:
            assessment = self._assess_single_practice(practice, doc_analysis)
            assessed_practices.append(assessment)
            total_score += assessment.compliance_score
        
        overall_compliance = total_score / len(practices) if practices else 0.0
        
        # 识别关键差距
        critical_gaps = []
        priority_improvements = []
        
        for assessment in assessed_practices:
            if (assessment.compliance_score < 0.5 and 
                assessment.practice.priority in [PriorityLevel.CRITICAL, PriorityLevel.HIGH]):
                critical_gaps.extend(assessment.gap_analysis)
                priority_improvements.extend(assessment.improvement_recommendations)
        
        # 计算成熟度分布
        maturity_distribution = {}
        for assessment in assessed_practices:
            level = assessment.current_maturity
            maturity_distribution[level] = maturity_distribution.get(level, 0) + 1
        
        return ComplianceReport(
            document_id=document.get('id', 'unknown'),
            domain=practices[0].domain if practices else PracticeDomain.MBSE,
            assessed_practices=assessed_practices,
            overall_compliance=overall_compliance,
            critical_gaps=list(set(critical_gaps)),
            priority_improvements=list(set(priority_improvements)),
            maturity_distribution=maturity_distribution
        )
    
    def _analyze_document_for_practices(self, document: ET.Element) -> Dict[str, Any]:
        """分析文档以支持实践评估"""
        analysis = {
            'elements': [],
            'structure': {},
            'naming_patterns': [],
            'documentation_level': 0.0,
            'traceability_links': [],
            'validation_artifacts': [],
            'stereotypes': set(),
            'packages': [],
            'dependencies': []
        }
        
        # 遍历文档
        for element in document.iter():
            element_info = {
                'tag': element.tag,
                'name': element.get('name', ''),
                'id': element.get('id', ''),
                'stereotype': element.get('stereotype', ''),
                'element': element
            }
            analysis['elements'].append(element_info)
            
            # 收集模型构造型
            if element_info['stereotype']:
                analysis['stereotypes'].add(element_info['stereotype'])
            
            # 分析包结构
            if 'package' in element.tag.lower():
                analysis['packages'].append(element_info)
            
            # 分析命名模式
            if element_info['name']:
                analysis['naming_patterns'].append(element_info['name'])
        
        # 计算文档化程度
        documented_elements = sum(1 for elem in analysis['elements'] 
                                if elem['name'] and len(elem['name']) > 0)
        total_elements = len(analysis['elements'])
        analysis['documentation_level'] = documented_elements / total_elements if total_elements > 0 else 0.0
        
        return analysis
    
    def _assess_single_practice(self, 
                              practice: BestPractice, 
                              doc_analysis: Dict[str, Any]) -> PracticeAssessment:
        """评估单个实践的合规性"""
        
        compliance_score = 0.0
        current_maturity = MaturityLevel.INITIAL
        gap_analysis = []
        improvement_recommendations = []
        
        # 基于实践类型的特定评估
        if practice.practice_id == "mbse_model_architecture":
            compliance_score, current_maturity, gaps, recommendations = self._assess_model_architecture(doc_analysis)
            gap_analysis.extend(gaps)
            improvement_recommendations.extend(recommendations)
            
        elif practice.practice_id == "requirements_traceability":
            compliance_score, current_maturity, gaps, recommendations = self._assess_requirements_traceability(doc_analysis)
            gap_analysis.extend(gaps)
            improvement_recommendations.extend(recommendations)
            
        elif practice.practice_id == "solid_principles":
            compliance_score, current_maturity, gaps, recommendations = self._assess_solid_principles(doc_analysis)
            gap_analysis.extend(gaps)
            improvement_recommendations.extend(recommendations)
            
        else:
            # 通用评估
            compliance_score, current_maturity, gaps, recommendations = self._generic_practice_assessment(practice, doc_analysis)
            gap_analysis.extend(gaps)
            improvement_recommendations.extend(recommendations)
        
        return PracticeAssessment(
            practice=practice,
            current_maturity=current_maturity,
            target_maturity=practice.maturity_level,
            compliance_score=compliance_score,
            gap_analysis=gap_analysis,
            improvement_recommendations=improvement_recommendations
        )
    
    def _assess_model_architecture(self, doc_analysis: Dict[str, Any]) -> Tuple[float, MaturityLevel, List[str], List[str]]:
        """评估模型架构实践"""
        score = 0.0
        gaps = []
        recommendations = []
        
        # 检查包结构
        if len(doc_analysis['packages']) > 0:
            score += 0.3
        else:
            gaps.append("缺乏明确的包结构")
            recommendations.append("建立清晰的包层次结构")
        
        # 检查命名规范
        if doc_analysis['documentation_level'] > 0.8:
            score += 0.3
        else:
            gaps.append("命名规范性不足")
            recommendations.append("制定并执行命名规范")
        
        # 检查模型元素的多样性
        if len(doc_analysis['stereotypes']) > 3:
            score += 0.2
        else:
            gaps.append("模型元素类型单一")
            recommendations.append("丰富模型元素类型，使用适当的构造型")
        
        # 检查文档化程度
        if doc_analysis['documentation_level'] > 0.9:
            score += 0.2
        else:
            gaps.append("文档化程度不足")
            recommendations.append("完善模型元素的文档和描述")
        
        # 确定成熟度级别
        if score >= 0.8:
            maturity = MaturityLevel.QUANTITATIVELY_MANAGED
        elif score >= 0.6:
            maturity = MaturityLevel.DEFINED
        elif score >= 0.4:
            maturity = MaturityLevel.MANAGED
        else:
            maturity = MaturityLevel.INITIAL
        
        return score, maturity, gaps, recommendations
    
    def _assess_requirements_traceability(self, doc_analysis: Dict[str, Any]) -> Tuple[float, MaturityLevel, List[str], List[str]]:
        """评估需求可追踪性实践"""
        score = 0.5  # 基础分数，因为难以从静态模型分析追踪性
        gaps = ["需要建立显式的追踪关系"]
        recommendations = ["实施需求追踪机制", "使用工具支持自动化追踪"]
        maturity = MaturityLevel.MANAGED
        
        return score, maturity, gaps, recommendations
    
    def _assess_solid_principles(self, doc_analysis: Dict[str, Any]) -> Tuple[float, MaturityLevel, List[str], List[str]]:
        """评估SOLID原则实践"""
        score = 0.6  # 基于模型结构的估算
        gaps = ["需要详细的代码分析以准确评估SOLID原则遵循情况"]
        recommendations = ["进行代码审查", "使用静态分析工具", "重构违反SOLID原则的代码"]
        maturity = MaturityLevel.DEFINED
        
        return score, maturity, gaps, recommendations
    
    def _generic_practice_assessment(self, practice: BestPractice, doc_analysis: Dict[str, Any]) -> Tuple[float, MaturityLevel, List[str], List[str]]:
        """通用实践评估"""
        score = 0.0
        gaps = []
        recommendations = []
        
        # 基于文档完整性评估
        if doc_analysis['documentation_level'] > 0.7:
            score += 0.4
        else:
            gaps.append("文档化程度不足")
            recommendations.append("完善模型文档")
        
        # 基于结构复杂性评估
        if len(doc_analysis['elements']) > 10:
            score += 0.3
        else:
            gaps.append("模型结构相对简单")
            recommendations.append("考虑扩展模型复杂度")
        
        # 基于组织结构评估
        if len(doc_analysis['packages']) > 0:
            score += 0.3
        else:
            gaps.append("缺乏组织结构")
            recommendations.append("建立层次化组织结构")
        
        # 确保最小合规性
        if score < 0.3:
            score = 0.35  # 给予基础评分
        
        # 确定成熟度级别
        if score >= 0.8:
            maturity = MaturityLevel.QUANTITATIVELY_MANAGED
        elif score >= 0.6:
            maturity = MaturityLevel.DEFINED
        elif score >= 0.4:
            maturity = MaturityLevel.MANAGED
        else:
            maturity = MaturityLevel.INITIAL
        
        return score, maturity, gaps, recommendations
    
    def generate_improvement_roadmap(self, compliance_report: ComplianceReport) -> List[ImprovementSuggestion]:
        """生成改进路线图"""
        suggestions = []
        
        # 基于合规性报告生成建议
        for assessment in compliance_report.assessed_practices:
            if assessment.compliance_score < 0.7:  # 需要改进的实践
                
                # 确定影响和难度
                impact = "high" if assessment.practice.priority in [PriorityLevel.CRITICAL, PriorityLevel.HIGH] else "medium"
                effort = "low" if assessment.compliance_score > 0.5 else "medium"
                
                suggestion = ImprovementSuggestion(
                    suggestion_id=f"improve_{assessment.practice.practice_id}",
                    title=f"改进{assessment.practice.name}",
                    description=f"提升{assessment.practice.name}的实施水平",
                    related_practice=assessment.practice.practice_id,
                    impact=impact,
                    effort=effort,
                    timeline="1-3个月" if effort == "low" else "3-6个月",
                    prerequisites=[],
                    expected_benefits=assessment.practice.benefits[:3]  # 取前3个主要益处
                )
                
                suggestions.append(suggestion)
        
        # 按影响和难度排序
        suggestions.sort(key=lambda x: (x.impact == "high", x.effort == "low"), reverse=True)
        
        return suggestions

# 工厂函数
def create_best_practices(config: Dict[str, Any] = None) -> BestPractices:
    """创建最佳实践库实例"""
    return BestPractices(config) 