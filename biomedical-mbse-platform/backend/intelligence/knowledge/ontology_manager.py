"""
XML元数据系统 - 本体管理器

实现领域本体管理功能：
- 多种标准本体加载
- 概念映射和对齐
- 本体推理和验证
- 知识图谱构建
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import xml.etree.ElementTree as ET
import json

logger = logging.getLogger(__name__)

class OntologyType(Enum):
    """本体类型"""
    UML = "uml"
    SYSML = "sysml"
    BPMN = "bpmn"
    ARCHIMATE = "archimate"
    TOGAF = "togaf"
    CUSTOM = "custom"

class ConceptRelationType(Enum):
    """概念关系类型"""
    IS_A = "is_a"                    # 是一个（继承）
    PART_OF = "part_of"              # 部分（聚合）
    DEPENDS_ON = "depends_on"        # 依赖
    SIMILAR_TO = "similar_to"        # 相似
    OPPOSITE_TO = "opposite_to"      # 相对
    IMPLEMENTS = "implements"        # 实现
    USES = "uses"                   # 使用

@dataclass
class OntologyConcept:
    """本体概念"""
    concept_id: str
    name: str
    namespace: str
    
    # 概念描述
    description: str = ""
    definition: str = ""
    synonyms: List[str] = field(default_factory=list)
    
    # 概念属性
    attributes: Dict[str, Any] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)
    
    # 层次信息
    parent_concepts: List[str] = field(default_factory=list)
    child_concepts: List[str] = field(default_factory=list)
    
    # 关系信息
    relationships: Dict[str, List[str]] = field(default_factory=dict)
    
    # 元数据
    ontology_source: str = ""
    created_timestamp: datetime = field(default_factory=datetime.now)
    confidence: float = 1.0

@dataclass
class ConceptMapping:
    """概念映射"""
    mapping_id: str
    source_element: str
    target_concepts: List[OntologyConcept]
    
    # 映射质量
    mapping_confidence: float = 0.0
    mapping_method: str = ""
    
    # 映射依据
    matching_features: List[str] = field(default_factory=list)
    similarity_scores: Dict[str, float] = field(default_factory=dict)
    
    # 验证信息
    is_validated: bool = False
    validation_notes: str = ""
    
    # 元数据
    mapped_timestamp: datetime = field(default_factory=datetime.now)
    mapper_version: str = "1.0"

    # 兼容性字段 - 为了与测试代码兼容
    element_id: str = ""
    concept_uri: str = ""
    concept_name: str = ""
    confidence: float = 0.0
    mapping_type: str = ""
    properties: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理，设置兼容性字段"""
        if not self.element_id:
            self.element_id = self.source_element
        
        if self.target_concepts and not self.concept_uri:
            primary_concept = self.target_concepts[0]
            self.concept_uri = primary_concept.concept_id
            self.concept_name = primary_concept.name
        
        if not self.confidence:
            self.confidence = self.mapping_confidence
        
        if not self.mapping_type:
            self.mapping_type = self.mapping_method or "semantic_mapping"
        
        # 获取概念类型
        if self.target_concepts:
            concept_types = set()
            for concept in self.target_concepts:
                if concept.namespace:
                    concept_types.add(concept.namespace)
            self.concept_type = ','.join(concept_types) if concept_types else "unknown"
        else:
            self.concept_type = "unknown"

@dataclass
class ReasoningResult:
    """推理结果"""
    # 推理发现
    inferred_concepts: List[OntologyConcept] = field(default_factory=list)
    inferred_relationships: List[Dict[str, Any]] = field(default_factory=list)
    
    # 一致性检查
    consistency_violations: List[str] = field(default_factory=list)
    completeness_gaps: List[str] = field(default_factory=list)
    
    # 推理统计
    reasoning_time: float = 0.0
    rules_applied: int = 0
    new_facts_derived: int = 0
    
    # 推理质量
    confidence_level: float = 0.0
    certainty_score: float = 0.0

class OntologyManager:
    """本体管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化本体管理器"""
        self.config = config or {}
        
        # 本体存储
        self.loaded_ontologies: Dict[str, Dict[str, OntologyConcept]] = {}
        self.concept_index: Dict[str, OntologyConcept] = {}
        self.namespace_registry: Dict[str, str] = {}
        
        # 映射存储
        self.concept_mappings: Dict[str, ConceptMapping] = {}
        self.mapping_history: List[ConceptMapping] = []
        
        # 推理引擎配置
        self.reasoning_rules: List[Dict[str, Any]] = []
        self.inference_cache: Dict[str, Any] = {}
        
        # 管理统计
        self.management_stats = {
            'loaded_ontologies': 0,
            'total_concepts': 0,
            'successful_mappings': 0,
            'reasoning_operations': 0
        }
        
        # 初始化标准本体
        self._initialize_standard_ontologies()
        
        logger.info("本体管理器初始化完成")
    
    def _initialize_standard_ontologies(self):
        """初始化标准本体"""
        # UML本体
        self._load_uml_ontology()
        
        # SysML本体
        self._load_sysml_ontology()
        
        # BPMN本体
        self._load_bpmn_ontology()
        
        # 初始化推理规则
        self._initialize_reasoning_rules()
    
    def _load_uml_ontology(self):
        """加载UML本体"""
        uml_concepts = {
            'Class': OntologyConcept(
                concept_id='uml:Class',
                name='Class',
                namespace='uml',
                description='UML类元素',
                definition='表示具有相同属性、操作、关系和语义的对象集合',
                synonyms=['类', 'Class'],
                attributes={
                    'name': 'string',
                    'visibility': 'enum',
                    'isAbstract': 'boolean',
                    'isFinal': 'boolean'
                },
                constraints=[
                    'Class必须有唯一的名称',
                    'Abstract类不能被实例化'
                ],
                ontology_source='UML 2.5 Specification'
            ),
            'Interface': OntologyConcept(
                concept_id='uml:Interface',
                name='Interface',
                namespace='uml',
                description='UML接口元素',
                definition='定义一组操作规范的抽象类型',
                synonyms=['接口', 'Interface'],
                attributes={
                    'name': 'string',
                    'visibility': 'enum'
                },
                constraints=[
                    'Interface只能包含抽象操作',
                    'Interface不能有实现'
                ],
                parent_concepts=['uml:Class'],
                ontology_source='UML 2.5 Specification'
            ),
            'Association': OntologyConcept(
                concept_id='uml:Association',
                name='Association',
                namespace='uml',
                description='UML关联关系',
                definition='表示两个或多个类之间的语义关系',
                synonyms=['关联', 'Association'],
                attributes={
                    'name': 'string',
                    'navigability': 'enum',
                    'multiplicity': 'string'
                },
                ontology_source='UML 2.5 Specification'
            ),
            'Generalization': OntologyConcept(
                concept_id='uml:Generalization',
                name='Generalization',
                namespace='uml',
                description='UML泛化关系',
                definition='表示一般化/特殊化的分类关系',
                synonyms=['泛化', '继承', 'Generalization', 'Inheritance'],
                relationships={
                    'is_a': ['uml:Association']
                },
                ontology_source='UML 2.5 Specification'
            )
        }
        
        self.loaded_ontologies['uml'] = uml_concepts
        self.namespace_registry['uml'] = 'http://www.eclipse.org/uml2/5.0.0/UML'
        
        # 更新概念索引
        for concept in uml_concepts.values():
            self.concept_index[concept.concept_id] = concept
        
        logger.info("UML本体加载完成")
    
    def _load_sysml_ontology(self):
        """加载SysML本体"""
        sysml_concepts = {
            'Block': OntologyConcept(
                concept_id='sysml:Block',
                name='Block',
                namespace='sysml',
                description='SysML块元素',
                definition='表示系统结构的基本构建单元',
                synonyms=['块', 'Block'],
                attributes={
                    'name': 'string',
                    'isEncapsulated': 'boolean'
                },
                parent_concepts=['uml:Class'],
                ontology_source='SysML 1.6 Specification'
            ),
            'Requirement': OntologyConcept(
                concept_id='sysml:Requirement',
                name='Requirement',
                namespace='sysml',
                description='SysML需求元素',
                definition='表示系统必须满足的条件或能力',
                synonyms=['需求', 'Requirement'],
                attributes={
                    'id': 'string',
                    'text': 'string',
                    'risk': 'enum',
                    'verifyMethod': 'enum'
                },
                constraints=[
                    'Requirement必须有唯一标识',
                    'Requirement必须有明确的文本描述'
                ],
                ontology_source='SysML 1.6 Specification'
            ),
            'Activity': OntologyConcept(
                concept_id='sysml:Activity',
                name='Activity',
                namespace='sysml',
                description='SysML活动元素',
                definition='表示系统的行为和功能',
                synonyms=['活动', 'Activity'],
                attributes={
                    'name': 'string',
                    'isReentrant': 'boolean'
                },
                parent_concepts=['uml:Activity'],
                ontology_source='SysML 1.6 Specification'
            )
        }
        
        self.loaded_ontologies['sysml'] = sysml_concepts
        self.namespace_registry['sysml'] = 'http://www.eclipse.org/papyrus/0.7.0/SysML'
        
        # 更新概念索引
        for concept in sysml_concepts.values():
            self.concept_index[concept.concept_id] = concept
        
        logger.info("SysML本体加载完成")
    
    def _load_bpmn_ontology(self):
        """加载BPMN本体"""
        bpmn_concepts = {
            'Process': OntologyConcept(
                concept_id='bpmn:Process',
                name='Process',
                namespace='bpmn',
                description='BPMN流程元素',
                definition='表示一系列业务活动的集合',
                synonyms=['流程', 'Process'],
                attributes={
                    'name': 'string',
                    'processType': 'enum',
                    'isExecutable': 'boolean'
                },
                ontology_source='BPMN 2.0 Specification'
            ),
            'Task': OntologyConcept(
                concept_id='bpmn:Task',
                name='Task',
                namespace='bpmn',
                description='BPMN任务元素',
                definition='表示原子级别的工作单元',
                synonyms=['任务', 'Task'],
                attributes={
                    'name': 'string',
                    'taskType': 'enum'
                },
                ontology_source='BPMN 2.0 Specification'
            ),
            'Gateway': OntologyConcept(
                concept_id='bpmn:Gateway',
                name='Gateway',
                namespace='bpmn',
                description='BPMN网关元素',
                definition='控制流程路径的分岔和合并',
                synonyms=['网关', 'Gateway'],
                attributes={
                    'name': 'string',
                    'gatewayDirection': 'enum'
                },
                ontology_source='BPMN 2.0 Specification'
            )
        }
        
        self.loaded_ontologies['bpmn'] = bpmn_concepts
        self.namespace_registry['bpmn'] = 'http://www.omg.org/spec/BPMN/20100524/MODEL'
        
        # 更新概念索引
        for concept in bpmn_concepts.values():
            self.concept_index[concept.concept_id] = concept
        
        logger.info("BPMN本体加载完成")
    
    def _initialize_reasoning_rules(self):
        """初始化推理规则"""
        self.reasoning_rules = [
            {
                'rule_id': 'inheritance_transitivity',
                'description': '继承关系的传递性',
                'pattern': 'if A is_a B and B is_a C then A is_a C',
                'confidence': 0.9
            },
            {
                'rule_id': 'composition_implies_dependency',
                'description': '组合关系暗示依赖关系',
                'pattern': 'if A part_of B then A depends_on B',
                'confidence': 0.8
            },
            {
                'rule_id': 'interface_realization',
                'description': '接口实现关系',
                'pattern': 'if A implements B and B is Interface then A is Class',
                'confidence': 0.9
            },
            {
                'rule_id': 'abstract_class_constraint',
                'description': '抽象类约束',
                'pattern': 'if A is AbstractClass then A cannot be instantiated',
                'confidence': 1.0
            }
        ]
    
    def load_domain_ontology(self, ontology_type: OntologyType, 
                           ontology_data: Dict[str, Any]) -> bool:
        """加载领域本体"""
        try:
            ontology_name = ontology_type.value
            concepts = {}
            
            # 解析本体数据
            for concept_data in ontology_data.get('concepts', []):
                concept = self._parse_concept_data(concept_data, ontology_name)
                concepts[concept.name] = concept
                self.concept_index[concept.concept_id] = concept
            
            self.loaded_ontologies[ontology_name] = concepts
            
            # 注册命名空间
            namespace = ontology_data.get('namespace', f'http://example.org/{ontology_name}')
            self.namespace_registry[ontology_name] = namespace
            
            self.management_stats['loaded_ontologies'] += 1
            self.management_stats['total_concepts'] += len(concepts)
            
            logger.info(f"成功加载{ontology_name}本体，包含{len(concepts)}个概念")
            return True
            
        except Exception as e:
            logger.error(f"加载本体失败: {e}")
            return False
    
    def _parse_concept_data(self, concept_data: Dict[str, Any], ontology_name: str) -> OntologyConcept:
        """解析概念数据"""
        return OntologyConcept(
            concept_id=f"{ontology_name}:{concept_data['name']}",
            name=concept_data['name'],
            namespace=ontology_name,
            description=concept_data.get('description', ''),
            definition=concept_data.get('definition', ''),
            synonyms=concept_data.get('synonyms', []),
            attributes=concept_data.get('attributes', {}),
            constraints=concept_data.get('constraints', []),
            parent_concepts=concept_data.get('parent_concepts', []),
            child_concepts=concept_data.get('child_concepts', []),
            relationships=concept_data.get('relationships', {}),
            ontology_source=concept_data.get('source', 'Custom Ontology')
        )
    
    def map_concepts(self, elements: List[ET.Element]) -> List[ConceptMapping]:
        """将XML元素映射到本体概念"""
        mappings = []
        
        for element in elements:
            try:
                # 基本概念识别
                primary_mapping = self._identify_primary_concept(element)
                if primary_mapping:
                    mappings.append(primary_mapping)
                
                # 扩展概念识别 - 增加更多识别逻辑
                extended_mappings = self._identify_extended_concepts(element)
                mappings.extend(extended_mappings)
                
                # 属性概念识别
                attribute_mappings = self._identify_attribute_concepts(element)
                mappings.extend(attribute_mappings)
                
                # 关系概念识别
                relationship_mappings = self._identify_relationship_concepts(element)
                mappings.extend(relationship_mappings)
                
            except Exception as e:
                logger.warning(f"概念映射失败 {element.tag}: {e}")
                continue
                
        # 去重并排序
        unique_mappings = self._deduplicate_mappings(mappings)
        return sorted(unique_mappings, key=lambda x: x.confidence, reverse=True)
    
    def _identify_primary_concept(self, element: ET.Element) -> Optional[ConceptMapping]:
        """识别元素的主要概念"""
        tag = element.tag.lower()
        element_name = element.get('name', element.tag)
        element_id = element.get('xmi:id', f"elem_{id(element)}")
        
        # 基本概念识别规则
        concept_rules = {
            'class': ('uml:Class', 0.9),
            'package': ('uml:Package', 0.9),
            'interface': ('uml:Interface', 0.9),
            'association': ('uml:Association', 0.9),
            'dependency': ('uml:Dependency', 0.8),
            'property': ('uml:Property', 0.8),
            'operation': ('uml:Operation', 0.8),
            'attribute': ('uml:Attribute', 0.8),
            'model': ('uml:Model', 0.9),
            'diagram': ('uml:Diagram', 0.7)
        }
        
        # 查找匹配的概念
        for keyword, (concept_uri, confidence) in concept_rules.items():
            if keyword in tag or keyword in element_name.lower():
                # 查找对应的本体概念
                concept = self.concept_index.get(concept_uri)
                if not concept:
                    # 如果找不到，创建一个简单的概念
                    concept = OntologyConcept(
                        concept_id=concept_uri,
                        name=concept_uri.split(':')[-1],
                        namespace=concept_uri.split(':')[0],
                        description=f"概念: {concept_uri}",
                        definition=f"自动识别的概念: {concept_uri}"
                    )
                
                return ConceptMapping(
                    mapping_id=f"mapping_{element_id}_{concept_uri}",
                    source_element=element_id,
                    target_concepts=[concept],
                    mapping_confidence=confidence,
                    mapping_method='primary_rule',
                    matching_features=[keyword],
                    properties={'source': 'primary_rule', 'keyword': keyword}
                )
        
        return None
    
    def _identify_extended_concepts(self, element: ET.Element) -> List[ConceptMapping]:
        """识别扩展概念"""
        mappings = []
        element_name = element.get('name', element.tag)
        element_id = element.get('xmi:id', f"elem_{id(element)}")
        
        # 更多UML概念识别
        uml_concepts = {
            'actor': ('uml:Actor', 0.9),
            'usecase': ('uml:UseCase', 0.9),
            'boundary': ('uml:Boundary', 0.8),
            'control': ('uml:Control', 0.8),
            'entity': ('uml:Entity', 0.8),
            'lifeline': ('uml:Lifeline', 0.8),
            'message': ('uml:Message', 0.8),
            'state': ('uml:State', 0.8),
            'transition': ('uml:Transition', 0.8),
            'activity': ('uml:Activity', 0.8),
            'node': ('uml:Node', 0.7),
            'artifact': ('uml:Artifact', 0.7),
            'deployment': ('uml:Deployment', 0.7)
        }
        
        for keyword, (concept_uri, confidence) in uml_concepts.items():
            if keyword in element.tag.lower() or keyword in element_name.lower():
                concept = self.concept_index.get(concept_uri)
                if not concept:
                    concept = OntologyConcept(
                        concept_id=concept_uri,
                        name=concept_uri.split(':')[-1],
                        namespace=concept_uri.split(':')[0],
                        description=f"扩展UML概念: {concept_uri}",
                        definition=f"自动识别的扩展概念: {concept_uri}"
                    )
                
                mappings.append(ConceptMapping(
                    mapping_id=f"mapping_{element_id}_{concept_uri}",
                    source_element=element_id,
                    target_concepts=[concept],
                    mapping_confidence=confidence,
                    mapping_method='extended_keyword',
                    matching_features=[keyword],
                    properties={'source': 'extended_uml', 'keyword': keyword}
                ))
        
        # SysML概念识别
        sysml_concepts = {
            'requirement': ('sysml:Requirement', 0.9),
            'block': ('sysml:Block', 0.9),
            'port': ('sysml:Port', 0.8),
            'constraint': ('sysml:Constraint', 0.8),
            'parametric': ('sysml:Parametric', 0.8),
            'allocation': ('sysml:Allocation', 0.7),
            'satisfy': ('sysml:Satisfy', 0.7),
            'verify': ('sysml:Verify', 0.7),
            'refine': ('sysml:Refine', 0.7)
        }
        
        for keyword, (concept_uri, confidence) in sysml_concepts.items():
            if keyword in element.tag.lower() or keyword in element_name.lower():
                concept = self.concept_index.get(concept_uri)
                if not concept:
                    concept = OntologyConcept(
                        concept_id=concept_uri,
                        name=concept_uri.split(':')[-1],
                        namespace=concept_uri.split(':')[0],
                        description=f"SysML概念: {concept_uri}",
                        definition=f"自动识别的SysML概念: {concept_uri}"
                    )
                
                mappings.append(ConceptMapping(
                    mapping_id=f"mapping_{element_id}_{concept_uri}",
                    source_element=element_id,
                    target_concepts=[concept],
                    mapping_confidence=confidence,
                    mapping_method='extended_keyword',
                    matching_features=[keyword],
                    properties={'source': 'sysml', 'keyword': keyword}
                ))
        
        return mappings
    
    def _identify_attribute_concepts(self, element: ET.Element) -> List[ConceptMapping]:
        """识别属性相关概念"""
        mappings = []
        element_id = element.get('xmi:id', f"elem_{id(element)}")
        
        # 检查元素属性
        for attr_name, attr_value in element.attrib.items():
            if attr_name in ['type', 'xmi:type']:
                concept = OntologyConcept(
                    concept_id=f"Type::{attr_value}",
                    name=f"Type_{attr_value}",
                    namespace="Type",
                    description=f"类型概念: {attr_value}",
                    definition=f"元素类型: {attr_value}"
                )
                
                mappings.append(ConceptMapping(
                    mapping_id=f"mapping_{element_id}_type_{attr_value}",
                    source_element=element_id,
                    target_concepts=[concept],
                    mapping_confidence=0.7,
                    mapping_method='type_attribute',
                    matching_features=[attr_name],
                    properties={'attribute': attr_name, 'value': attr_value}
                ))
            
            if attr_name == 'stereotype':
                concept = OntologyConcept(
                    concept_id=f"Stereotype::{attr_value}",
                    name=f"Stereotype_{attr_value}",
                    namespace="Stereotype",
                    description=f"构造型概念: {attr_value}",
                    definition=f"UML构造型: {attr_value}"
                )
                
                mappings.append(ConceptMapping(
                    mapping_id=f"mapping_{element_id}_stereotype_{attr_value}",
                    source_element=element_id,
                    target_concepts=[concept],
                    mapping_confidence=0.8,
                    mapping_method='stereotype',
                    matching_features=[attr_value],
                    properties={'stereotype': attr_value}
                ))
        
        return mappings
    
    def _identify_relationship_concepts(self, element: ET.Element) -> List[ConceptMapping]:
        """识别关系概念"""
        mappings = []
        element_id = element.get('xmi:id', f"elem_{id(element)}")
        
        # 检查引用关系
        for attr_name, attr_value in element.attrib.items():
            if 'ref' in attr_name.lower() or attr_name in ['target', 'source', 'supplier', 'client']:
                concept = OntologyConcept(
                    concept_id=f"Relationship::{attr_name}",
                    name=f"Relation_{attr_name}",
                    namespace="Relationship",
                    description=f"关系概念: {attr_name}",
                    definition=f"元素关系: {attr_name}"
                )
                
                mappings.append(ConceptMapping(
                    mapping_id=f"mapping_{element_id}_rel_{attr_name}",
                    source_element=element_id,
                    target_concepts=[concept],
                    mapping_confidence=0.6,
                    mapping_method='relationship',
                    matching_features=[attr_name],
                    properties={'relation_type': attr_name, 'target': attr_value}
                ))
        
        return mappings
    
    def _deduplicate_mappings(self, mappings: List[ConceptMapping]) -> List[ConceptMapping]:
        """去重映射结果"""
        seen = set()
        unique_mappings = []
        
        for mapping in mappings:
            key = (mapping.element_id, mapping.concept_uri)
            if key not in seen:
                seen.add(key)
                unique_mappings.append(mapping)
        
        return unique_mappings
    
    def reason_about_concepts(self, concepts: List[OntologyConcept],
                            apply_rules: bool = True) -> ReasoningResult:
        """对概念进行推理"""
        start_time = datetime.now()
        result = ReasoningResult()
        
        if apply_rules:
            # 应用推理规则
            for rule in self.reasoning_rules:
                self._apply_reasoning_rule(rule, concepts, result)
        
        # 一致性检查
        result.consistency_violations = self._check_consistency(concepts)
        
        # 完整性检查
        result.completeness_gaps = self._check_completeness(concepts)
        
        # 计算推理时间
        end_time = datetime.now()
        result.reasoning_time = (end_time - start_time).total_seconds()
        
        # 更新统计
        self.management_stats['reasoning_operations'] += 1
        
        return result
    
    def _apply_reasoning_rule(self, rule: Dict[str, Any], 
                            concepts: List[OntologyConcept],
                            result: ReasoningResult):
        """应用推理规则"""
        rule_id = rule['rule_id']
        
        if rule_id == 'inheritance_transitivity':
            self._apply_inheritance_transitivity(concepts, result)
        elif rule_id == 'composition_implies_dependency':
            self._apply_composition_dependency(concepts, result)
        elif rule_id == 'interface_realization':
            self._apply_interface_realization(concepts, result)
        elif rule_id == 'abstract_class_constraint':
            self._apply_abstract_class_constraint(concepts, result)
        
        result.rules_applied += 1
    
    def _apply_inheritance_transitivity(self, concepts: List[OntologyConcept], result: ReasoningResult):
        """应用继承传递性规则"""
        concept_dict = {c.concept_id: c for c in concepts}
        
        for concept in concepts:
            for parent_id in concept.parent_concepts:
                if parent_id in concept_dict:
                    parent = concept_dict[parent_id]
                    
                    # 传递继承关系
                    for grandparent_id in parent.parent_concepts:
                        if grandparent_id not in concept.parent_concepts:
                            # 推断新的继承关系
                            result.inferred_relationships.append({
                                'type': ConceptRelationType.IS_A.value,
                                'source': concept.concept_id,
                                'target': grandparent_id,
                                'confidence': 0.8,
                                'inferred_by': 'inheritance_transitivity'
                            })
                            result.new_facts_derived += 1
    
    def _apply_composition_dependency(self, concepts: List[OntologyConcept], result: ReasoningResult):
        """应用组合暗示依赖规则"""
        for concept in concepts:
            part_of_relations = concept.relationships.get('part_of', [])
            for target_id in part_of_relations:
                # 推断依赖关系
                if 'depends_on' not in concept.relationships:
                    concept.relationships['depends_on'] = []
                
                if target_id not in concept.relationships['depends_on']:
                    result.inferred_relationships.append({
                        'type': ConceptRelationType.DEPENDS_ON.value,
                        'source': concept.concept_id,
                        'target': target_id,
                        'confidence': 0.7,
                        'inferred_by': 'composition_implies_dependency'
                    })
                    result.new_facts_derived += 1
    
    def _apply_interface_realization(self, concepts: List[OntologyConcept], result: ReasoningResult):
        """应用接口实现规则"""
        for concept in concepts:
            implements_relations = concept.relationships.get('implements', [])
            for interface_id in implements_relations:
                # 验证接口实现的一致性
                if 'Interface' not in interface_id:
                    result.consistency_violations.append(
                        f"概念 {concept.concept_id} 实现了非接口概念 {interface_id}"
                    )
    
    def _apply_abstract_class_constraint(self, concepts: List[OntologyConcept], result: ReasoningResult):
        """应用抽象类约束规则"""
        for concept in concepts:
            if concept.attributes.get('isAbstract', False):
                # 检查抽象类是否被错误实例化
                if 'can_be_instantiated' in concept.attributes:
                    if concept.attributes['can_be_instantiated']:
                        result.consistency_violations.append(
                            f"抽象概念 {concept.concept_id} 不能被实例化"
                        )
    
    def _check_consistency(self, concepts: List[OntologyConcept]) -> List[str]:
        """检查一致性"""
        violations = []
        concept_dict = {c.concept_id: c for c in concepts}
        
        for concept in concepts:
            # 检查循环继承
            if self._has_circular_inheritance(concept, concept_dict):
                violations.append(f"概念 {concept.concept_id} 存在循环继承")
            
            # 检查约束违反
            for constraint in concept.constraints:
                if not self._validate_constraint(concept, constraint, concept_dict):
                    violations.append(f"概念 {concept.concept_id} 违反约束: {constraint}")
        
        return violations
    
    def _check_completeness(self, concepts: List[OntologyConcept]) -> List[str]:
        """检查完整性"""
        gaps = []
        
        # 检查必需属性
        for concept in concepts:
            if concept.namespace in ['uml', 'sysml']:
                if 'name' not in concept.attributes:
                    gaps.append(f"概念 {concept.concept_id} 缺少必需的 name 属性")
        
        # 检查关系完整性
        for concept in concepts:
            for relation_type, targets in concept.relationships.items():
                for target_id in targets:
                    if target_id not in [c.concept_id for c in concepts]:
                        gaps.append(f"概念 {concept.concept_id} 引用了不存在的目标 {target_id}")
        
        return gaps
    
    def _has_circular_inheritance(self, concept: OntologyConcept, 
                                 concept_dict: Dict[str, OntologyConcept],
                                 visited: Set[str] = None) -> bool:
        """检查循环继承"""
        if visited is None:
            visited = set()
        
        if concept.concept_id in visited:
            return True
        
        visited.add(concept.concept_id)
        
        for parent_id in concept.parent_concepts:
            if parent_id in concept_dict:
                parent = concept_dict[parent_id]
                if self._has_circular_inheritance(parent, concept_dict, visited.copy()):
                    return True
        
        return False
    
    def _validate_constraint(self, concept: OntologyConcept, 
                           constraint: str,
                           concept_dict: Dict[str, OntologyConcept]) -> bool:
        """验证约束"""
        # 简化的约束验证
        if "必须有唯一的名称" in constraint:
            return bool(concept.attributes.get('name'))
        
        if "不能被实例化" in constraint:
            return not concept.attributes.get('can_be_instantiated', False)
        
        return True  # 默认通过
    
    def get_concept_by_id(self, concept_id: str) -> Optional[OntologyConcept]:
        """根据ID获取概念"""
        return self.concept_index.get(concept_id)
    
    def search_concepts(self, query: str, 
                       ontologies: List[str] = None) -> List[OntologyConcept]:
        """搜索概念"""
        results = []
        target_ontologies = ontologies or list(self.loaded_ontologies.keys())
        
        query = query.lower()
        
        for ontology_name in target_ontologies:
            if ontology_name not in self.loaded_ontologies:
                continue
            
            ontology = self.loaded_ontologies[ontology_name]
            for concept in ontology.values():
                # 名称匹配
                if query in concept.name.lower():
                    results.append(concept)
                    continue
                
                # 描述匹配
                if query in concept.description.lower():
                    results.append(concept)
                    continue
                
                # 同义词匹配
                if any(query in synonym.lower() for synonym in concept.synonyms):
                    results.append(concept)
        
        return results
    
    def export_ontology(self, ontology_name: str) -> Dict[str, Any]:
        """导出本体"""
        if ontology_name not in self.loaded_ontologies:
            return {}
        
        ontology = self.loaded_ontologies[ontology_name]
        
        export_data = {
            'ontology_name': ontology_name,
            'namespace': self.namespace_registry.get(ontology_name, ''),
            'export_timestamp': datetime.now().isoformat(),
            'concept_count': len(ontology),
            'concepts': []
        }
        
        for concept in ontology.values():
            concept_data = {
                'concept_id': concept.concept_id,
                'name': concept.name,
                'description': concept.description,
                'definition': concept.definition,
                'synonyms': concept.synonyms,
                'attributes': concept.attributes,
                'constraints': concept.constraints,
                'parent_concepts': concept.parent_concepts,
                'child_concepts': concept.child_concepts,
                'relationships': concept.relationships,
                'ontology_source': concept.ontology_source,
                'confidence': concept.confidence
            }
            export_data['concepts'].append(concept_data)
        
        return export_data
    
    def get_management_statistics(self) -> Dict[str, Any]:
        """获取管理统计信息"""
        stats = self.management_stats.copy()
        stats.update({
            'loaded_ontology_names': list(self.loaded_ontologies.keys()),
            'total_mappings': len(self.concept_mappings),
            'reasoning_rules_count': len(self.reasoning_rules),
            'namespace_count': len(self.namespace_registry)
        })
        return stats

# 工厂函数
def create_ontology_manager(config: Dict[str, Any] = None) -> OntologyManager:
    """创建本体管理器实例"""
    return OntologyManager(config)

# 导出主要类
__all__ = [
    'OntologyManager',
    'OntologyConcept',
    'ConceptMapping',
    'ReasoningResult',
    'OntologyType',
    'ConceptRelationType',
    'create_ontology_manager'
] 