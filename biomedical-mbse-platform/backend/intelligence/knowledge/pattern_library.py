#!/usr/bin/env python3
"""
设计模式库 - 设计模式和反模式知识库
包含经典设计模式、企业级模式、架构级模式、反模式和代码异味知识库
"""

import xml.etree.ElementTree as ET
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Set, Tuple
from enum import Enum
import json
import re
from pathlib import Path

logger = logging.getLogger(__name__)

class PatternCategory(Enum):
    """模式类别"""
    CREATIONAL = "creational"      # 创建型模式
    STRUCTURAL = "structural"      # 结构型模式
    BEHAVIORAL = "behavioral"      # 行为型模式
    ARCHITECTURAL = "architectural" # 架构模式
    ENTERPRISE = "enterprise"      # 企业级模式
    ANTI_PATTERN = "anti_pattern"  # 反模式
    CODE_SMELL = "code_smell"      # 代码异味

class PatternScope(Enum):
    """模式作用域"""
    CLASS = "class"           # 类级别
    OBJECT = "object"         # 对象级别
    COMPONENT = "component"   # 组件级别
    SYSTEM = "system"         # 系统级别

class PatternComplexity(Enum):
    """模式复杂度"""
    SIMPLE = "simple"         # 简单
    MEDIUM = "medium"         # 中等
    COMPLEX = "complex"       # 复杂

@dataclass
class PatternElement:
    """模式元素"""
    name: str
    role: str                    # 角色
    responsibility: str          # 职责
    relationships: List[str] = field(default_factory=list)  # 关系
    constraints: List[str] = field(default_factory=list)    # 约束

@dataclass
class PatternStructure:
    """模式结构"""
    participants: List[PatternElement] = field(default_factory=list)
    collaborations: List[str] = field(default_factory=list)
    sequence_diagram: Optional[str] = None
    class_diagram: Optional[str] = None

@dataclass
class PatternImplementation:
    """模式实现"""
    language: str
    code_example: str
    usage_example: str
    notes: List[str] = field(default_factory=list)

@dataclass
class PatternConsequences:
    """模式后果"""
    benefits: List[str] = field(default_factory=list)
    drawbacks: List[str] = field(default_factory=list)
    trade_offs: List[str] = field(default_factory=list)

@dataclass
class DesignPattern:
    """设计模式"""
    pattern_id: str
    name: str
    category: PatternCategory
    scope: PatternScope
    complexity: PatternComplexity
    intent: str                              # 意图
    motivation: str                          # 动机
    applicability: List[str]                 # 适用性
    structure: PatternStructure
    implementations: List[PatternImplementation] = field(default_factory=list)
    consequences: PatternConsequences = field(default_factory=PatternConsequences)
    related_patterns: List[str] = field(default_factory=list)
    known_uses: List[str] = field(default_factory=list)
    aliases: List[str] = field(default_factory=list)
    tags: Set[str] = field(default_factory=set)
    quality_attributes: Dict[str, float] = field(default_factory=dict)  # 质量属性评分

@dataclass
class AntiPattern:
    """反模式"""
    anti_pattern_id: str
    name: str
    category: PatternCategory
    description: str                         # 描述
    symptoms: List[str]                      # 症状
    consequences: List[str]                  # 后果
    causes: List[str]                        # 原因
    solutions: List[str]                     # 解决方案
    detection_rules: List[str]               # 检测规则
    refactoring_steps: List[str]             # 重构步骤
    examples: List[str] = field(default_factory=list)
    severity: str = "medium"                 # 严重程度
    frequency: str = "common"                # 出现频率

@dataclass
class PatternMatch:
    """模式匹配结果"""
    pattern: DesignPattern
    confidence: float                        # 置信度
    matched_elements: List[str]              # 匹配的元素
    evidence: List[str]                      # 证据
    suggestions: List[str] = field(default_factory=list)  # 建议

@dataclass
class AntiPatternMatch:
    """反模式匹配结果"""
    anti_pattern: AntiPattern
    confidence: float
    detected_elements: List[str]             # 检测到的元素
    severity_score: float                    # 严重性评分
    refactoring_priority: str                # 重构优先级

@dataclass
class PatternCatalog:
    """模式目录"""
    name: str
    version: str
    description: str
    patterns: Dict[str, DesignPattern] = field(default_factory=dict)
    anti_patterns: Dict[str, AntiPattern] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

class PatternLibrary:
    """设计模式库"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化模式库"""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 初始化模式目录
        self.catalogs: Dict[str, PatternCatalog] = {}
        self.pattern_cache: Dict[str, DesignPattern] = {}
        self.anti_pattern_cache: Dict[str, AntiPattern] = {}
        
        # 初始化内置模式
        self._initialize_gof_patterns()
        self._initialize_enterprise_patterns()
        self._initialize_architectural_patterns()
        self._initialize_anti_patterns()
        
        self.logger.info("模式库初始化完成")
    
    def _initialize_gof_patterns(self):
        """初始化GoF设计模式"""
        gof_catalog = PatternCatalog(
            name="GoF Design Patterns",
            version="1.0",
            description="Gang of Four经典设计模式"
        )
        
        # 创建型模式
        gof_catalog.patterns["singleton"] = self._create_singleton_pattern()
        gof_catalog.patterns["factory_method"] = self._create_factory_method_pattern()
        gof_catalog.patterns["abstract_factory"] = self._create_abstract_factory_pattern()
        gof_catalog.patterns["builder"] = self._create_builder_pattern()
        gof_catalog.patterns["prototype"] = self._create_prototype_pattern()
        
        # 结构型模式
        gof_catalog.patterns["adapter"] = self._create_adapter_pattern()
        gof_catalog.patterns["bridge"] = self._create_bridge_pattern()
        gof_catalog.patterns["composite"] = self._create_composite_pattern()
        gof_catalog.patterns["decorator"] = self._create_decorator_pattern()
        gof_catalog.patterns["facade"] = self._create_facade_pattern()
        gof_catalog.patterns["flyweight"] = self._create_flyweight_pattern()
        gof_catalog.patterns["proxy"] = self._create_proxy_pattern()
        
        # 行为型模式
        gof_catalog.patterns["observer"] = self._create_observer_pattern()
        gof_catalog.patterns["strategy"] = self._create_strategy_pattern()
        gof_catalog.patterns["command"] = self._create_command_pattern()
        gof_catalog.patterns["state"] = self._create_state_pattern()
        gof_catalog.patterns["template_method"] = self._create_template_method_pattern()
        
        self.catalogs["gof"] = gof_catalog
        
    def _create_singleton_pattern(self) -> DesignPattern:
        """创建单例模式"""
        return DesignPattern(
            pattern_id="singleton",
            name="Singleton",
            category=PatternCategory.CREATIONAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.SIMPLE,
            intent="确保一个类只有一个实例，并提供全局访问点",
            motivation="有些类只应该有一个实例，如线程池、缓存、日志对象等",
            applicability=[
                "当类只能有一个实例且客户可以从全局访问点访问它时",
                "当唯一实例应该可以通过子类化扩展，且客户无需更改代码即可使用扩展的实例时"
            ],
            structure=PatternStructure(
                participants=[
                    PatternElement("Singleton", "单例类", "定义Instance操作，允许客户访问其唯一实例")
                ],
                collaborations=["客户只能通过Singleton的Instance操作访问单例实例"]
            ),
            consequences=PatternConsequences(
                benefits=[
                    "控制对唯一实例的访问",
                    "减少名称空间",
                    "允许对操作和表示的精化",
                    "允许可变数目的实例",
                    "比类操作更灵活"
                ],
                drawbacks=[
                    "难以测试",
                    "隐藏依赖关系",
                    "违反单一职责原则",
                    "在多线程环境中需要特殊处理"
                ]
            ),
            tags={"creational", "instance_control", "global_access"}
        )
    
    def _create_factory_method_pattern(self) -> DesignPattern:
        """创建工厂方法模式"""
        return DesignPattern(
            pattern_id="factory_method",
            name="Factory Method",
            category=PatternCategory.CREATIONAL,
            scope=PatternScope.CLASS,
            complexity=PatternComplexity.MEDIUM,
            intent="定义创建对象的接口，让子类决定实例化哪一个类",
            motivation="框架使用抽象类定义和维护对象之间的关系，这些对象的创建通常也由框架负责",
            applicability=[
                "当一个类不知道它所必须创建的对象的类的时候",
                "当一个类希望由其子类来指定它所创建的对象的时候",
                "当类将创建对象的职责委托给多个帮助子类中的某一个"
            ],
            structure=PatternStructure(
                participants=[
                    PatternElement("Product", "产品", "定义工厂方法所创建的对象的接口"),
                    PatternElement("ConcreteProduct", "具体产品", "实现Product接口"),
                    PatternElement("Creator", "创建者", "声明工厂方法"),
                    PatternElement("ConcreteCreator", "具体创建者", "重定义工厂方法以返回ConcreteProduct实例")
                ]
            ),
            tags={"creational", "factory", "polymorphism"}
        )
    
    def _initialize_enterprise_patterns(self):
        """初始化企业级模式"""
        enterprise_catalog = PatternCatalog(
            name="Enterprise Patterns",
            version="1.0",
            description="企业级应用架构模式"
        )
        
        # 添加企业级模式
        enterprise_catalog.patterns["mvc"] = self._create_mvc_pattern()
        enterprise_catalog.patterns["repository"] = self._create_repository_pattern()
        enterprise_catalog.patterns["service_layer"] = self._create_service_layer_pattern()
        enterprise_catalog.patterns["domain_model"] = self._create_domain_model_pattern()
        enterprise_catalog.patterns["data_access_object"] = self._create_dao_pattern()
        
        self.catalogs["enterprise"] = enterprise_catalog
    
    def _create_repository_pattern(self) -> DesignPattern:
        """创建仓储模式"""
        return DesignPattern(
            pattern_id="repository",
            name="Repository",
            category=PatternCategory.ARCHITECTURAL,
            scope=PatternScope.COMPONENT,
            complexity=PatternComplexity.MEDIUM,
            intent="封装存储、检索和搜索行为，模拟对象集合",
            motivation="将领域和数据映射层分离，消除重复的查询逻辑",
            applicability=[
                "需要访问数据存储的领域对象",
                "想要避免重复的查询逻辑",
                "需要对数据访问进行集中控制"
            ],
            structure=PatternStructure(
                participants=[
                    PatternElement("Repository", "仓储接口", "定义访问领域对象的方法"),
                    PatternElement("ConcreteRepository", "具体仓储", "实现仓储接口"),
                    PatternElement("DomainObject", "领域对象", "业务实体"),
                    PatternElement("DataMapper", "数据映射器", "处理领域对象和数据存储之间的映射")
                ]
            ),
            tags={"enterprise", "data_access", "domain_driven_design"}
        )
    
    def _initialize_architectural_patterns(self):
        """初始化架构模式"""
        arch_catalog = PatternCatalog(
            name="Architectural Patterns",
            version="1.0",
            description="软件架构模式"
        )
        
        # 添加架构模式
        arch_catalog.patterns["layered"] = self._create_layered_pattern()
        arch_catalog.patterns["microservices"] = self._create_microservices_pattern()
        arch_catalog.patterns["event_driven"] = self._create_event_driven_pattern()
        arch_catalog.patterns["hexagonal"] = self._create_hexagonal_pattern()
        
        self.catalogs["architectural"] = arch_catalog
    
    def _initialize_anti_patterns(self):
        """初始化反模式"""
        anti_catalog = PatternCatalog(
            name="Anti-Patterns",
            version="1.0",
            description="常见反模式和代码异味"
        )
        
        # 添加反模式
        anti_catalog.anti_patterns["god_class"] = self._create_god_class_anti_pattern()
        anti_catalog.anti_patterns["spaghetti_code"] = self._create_spaghetti_code_anti_pattern()
        anti_catalog.anti_patterns["circular_dependency"] = self._create_circular_dependency_anti_pattern()
        anti_catalog.anti_patterns["magic_number"] = self._create_magic_number_anti_pattern()
        anti_catalog.anti_patterns["dead_code"] = self._create_dead_code_anti_pattern()
        
        self.catalogs["anti_patterns"] = anti_catalog
    
    def _create_service_layer_pattern(self) -> DesignPattern:
        """创建服务层模式"""
        return DesignPattern(
            pattern_id="service_layer",
            name="Service Layer",
            category=PatternCategory.ARCHITECTURAL,
            scope=PatternScope.COMPONENT,
            complexity=PatternComplexity.MEDIUM,
            intent="定义应用程序边界和可用操作集，协调应用程序对每个操作的响应",
            motivation="封装业务逻辑，提供粗粒度的API",
            applicability=["需要封装复杂的业务逻辑", "需要提供统一的服务接口"],
            structure=PatternStructure(),
            tags={"enterprise", "service", "business_logic"}
        )
    
    def _create_domain_model_pattern(self) -> DesignPattern:
        """创建领域模型模式"""
        return DesignPattern(
            pattern_id="domain_model",
            name="Domain Model",
            category=PatternCategory.ARCHITECTURAL,
            scope=PatternScope.COMPONENT,
            complexity=PatternComplexity.COMPLEX,
            intent="创建领域的对象模型，包含数据和行为",
            motivation="将业务逻辑组织到领域对象中",
            applicability=["复杂的业务逻辑", "需要丰富的面向对象设计"],
            structure=PatternStructure(),
            tags={"enterprise", "domain", "object_model"}
        )
    
    def _create_dao_pattern(self) -> DesignPattern:
        """创建数据访问对象模式"""
        return DesignPattern(
            pattern_id="data_access_object",
            name="Data Access Object (DAO)",
            category=PatternCategory.STRUCTURAL,
            scope=PatternScope.COMPONENT,
            complexity=PatternComplexity.MEDIUM,
            intent="将数据访问逻辑与业务逻辑分离",
            motivation="提供统一的数据访问接口，隐藏数据源的实现细节",
            applicability=["需要访问不同类型的数据源", "需要数据访问层的抽象"],
            structure=PatternStructure(),
            tags={"enterprise", "data_access", "separation"}
        )
    
    def _create_god_class_anti_pattern(self) -> AntiPattern:
        """创建上帝类反模式"""
        return AntiPattern(
            anti_pattern_id="god_class",
            name="God Class",
            category=PatternCategory.ANTI_PATTERN,
            description="一个类承担了过多的责任，包含过多的方法和属性",
            symptoms=[
                "类包含大量方法（通常>20个）",
                "类的代码行数过多（通常>500行）",
                "类的职责不明确，做了太多事情",
                "类难以理解和维护",
                "类的方法之间缺乏内聚性"
            ],
            consequences=[
                "代码难以理解和维护",
                "增加了系统的复杂性",
                "违反单一职责原则",
                "降低了代码的可重用性",
                "增加了测试的复杂性"
            ],
            causes=[
                "缺乏设计规划",
                "功能的持续添加",
                "对面向对象原则理解不足",
                "重构意识不够"
            ],
            solutions=[
                "提取类（Extract Class）",
                "提取接口（Extract Interface）",
                "委托（Delegation）",
                "组合模式",
                "策略模式"
            ],
            detection_rules=[
                "方法数量 > 20",
                "代码行数 > 500",
                "圈复杂度过高",
                "职责过多"
            ],
            refactoring_steps=[
                "识别类的不同职责",
                "将相关方法和属性提取到新类",
                "建立适当的依赖关系",
                "重构客户端代码"
            ],
            severity="high",
            frequency="common"
        )
    
    def load_pattern_catalog(self, catalog_name: str) -> Optional[PatternCatalog]:
        """加载模式目录"""
        return self.catalogs.get(catalog_name)
    
    def search_patterns(self, query: str, category: Optional[PatternCategory] = None) -> List[DesignPattern]:
        """搜索设计模式"""
        results = []
        
        for catalog in self.catalogs.values():
            for pattern in catalog.patterns.values():
                # 类别过滤
                if category and pattern.category != category:
                    continue
                
                # 文本匹配
                if self._matches_pattern_query(pattern, query):
                    results.append(pattern)
        
        return results
    
    def _matches_pattern_query(self, pattern: DesignPattern, query: str) -> bool:
        """检查模式是否匹配查询"""
        query_lower = query.lower()
        
        # 检查名称
        if query_lower in pattern.name.lower():
            return True
        
        # 检查意图
        if query_lower in pattern.intent.lower():
            return True
        
        # 检查标签
        for tag in pattern.tags:
            if query_lower in tag.lower():
                return True
        
        # 检查别名
        for alias in pattern.aliases:
            if query_lower in alias.lower():
                return True
        
        return False
    
    def identify_patterns_in_document(self, document: ET.Element) -> List[PatternMatch]:
        """在文档中识别设计模式"""
        matches = []
        
        # 分析文档结构
        doc_structure = self._analyze_document_structure(document)
        
        # 对每个已知模式进行匹配
        for catalog in self.catalogs.values():
            for pattern in catalog.patterns.values():
                match = self._match_pattern(pattern, doc_structure)
                if match and match.confidence > 0.3:  # 最低置信度阈值
                    matches.append(match)
        
        # 按置信度排序
        matches.sort(key=lambda x: x.confidence, reverse=True)
        
        return matches
    
    def detect_anti_patterns_in_document(self, document: ET.Element) -> List[AntiPatternMatch]:
        """在文档中检测反模式"""
        matches = []
        
        # 分析文档结构
        doc_structure = self._analyze_document_structure(document)
        
        # 检测反模式
        for catalog in self.catalogs.values():
            for anti_pattern in catalog.anti_patterns.values():
                match = self._detect_anti_pattern(anti_pattern, doc_structure)
                if match and match.confidence > 0.4:  # 反模式的置信度阈值稍高
                    matches.append(match)
        
        # 按严重性评分排序
        matches.sort(key=lambda x: x.severity_score, reverse=True)
        
        return matches
    
    def _analyze_document_structure(self, document: ET.Element) -> Dict[str, Any]:
        """分析文档结构"""
        structure = {
            'classes': [],
            'interfaces': [],
            'associations': [],
            'dependencies': [],
            'packages': [],
            'methods': [],
            'attributes': [],
            'stereotypes': set()
        }
        
        # 遍历文档元素
        for element in document.iter():
            tag = element.tag.lower()
            
            # 类
            if 'class' in tag:
                class_info = {
                    'name': element.get('name', ''),
                    'methods': [],
                    'attributes': [],
                    'stereotype': element.get('stereotype', ''),
                    'element': element
                }
                
                # 收集方法和属性
                for child in element:
                    if 'method' in child.tag.lower() or 'operation' in child.tag.lower():
                        class_info['methods'].append(child.get('name', ''))
                    elif 'attribute' in child.tag.lower() or 'property' in child.tag.lower():
                        class_info['attributes'].append(child.get('name', ''))
                
                structure['classes'].append(class_info)
                
                # 收集立体模型
                if class_info['stereotype']:
                    structure['stereotypes'].add(class_info['stereotype'])
            
            # 接口
            elif 'interface' in tag:
                structure['interfaces'].append({
                    'name': element.get('name', ''),
                    'element': element
                })
            
            # 关联
            elif 'association' in tag:
                structure['associations'].append({
                    'name': element.get('name', ''),
                    'element': element
                })
            
            # 包
            elif 'package' in tag:
                structure['packages'].append({
                    'name': element.get('name', ''),
                    'stereotype': element.get('stereotype', ''),
                    'element': element
                })
        
        return structure
    
    def _match_pattern(self, pattern: DesignPattern, doc_structure: Dict[str, Any]) -> Optional[PatternMatch]:
        """匹配设计模式"""
        confidence = 0.0
        matched_elements = []
        evidence = []
        
        # 基于模式类型的特定匹配逻辑
        if pattern.pattern_id == "singleton":
            confidence, matched_elements, evidence = self._match_singleton_pattern(doc_structure)
        elif pattern.pattern_id == "factory_method":
            confidence, matched_elements, evidence = self._match_factory_method_pattern(doc_structure)
        elif pattern.pattern_id == "repository":
            confidence, matched_elements, evidence = self._match_repository_pattern(doc_structure)
        elif pattern.pattern_id == "mvc":
            confidence, matched_elements, evidence = self._match_mvc_pattern(doc_structure)
        else:
            # 通用匹配逻辑
            confidence, matched_elements, evidence = self._generic_pattern_match(pattern, doc_structure)
        
        if confidence > 0:
            return PatternMatch(
                pattern=pattern,
                confidence=confidence,
                matched_elements=matched_elements,
                evidence=evidence
            )
        
        return None
    
    def _match_repository_pattern(self, doc_structure: Dict[str, Any]) -> Tuple[float, List[str], List[str]]:
        """匹配仓储模式"""
        confidence = 0.0
        matched_elements = []
        evidence = []
        
        # 查找仓储特征
        repository_classes = []
        domain_classes = []
        
        for class_info in doc_structure['classes']:
            class_name = class_info['name'].lower()
            stereotype = class_info['stereotype'].lower()
            
            # 仓储类特征
            if ('repository' in class_name or 
                'repo' in class_name or 
                stereotype == 'repository'):
                repository_classes.append(class_info['name'])
                confidence += 0.3
                
            # 领域类特征
            if (stereotype in ['entity', 'domain', 'model'] or
                any(method in ['save', 'find', 'delete', 'update'] 
                    for method in [m.lower() for m in class_info['methods']])):
                domain_classes.append(class_info['name'])
                confidence += 0.1
        
        if repository_classes:
            matched_elements.extend(repository_classes)
            evidence.append(f"发现仓储类: {', '.join(repository_classes)}")
        
        if domain_classes:
            matched_elements.extend(domain_classes)
            evidence.append(f"发现领域类: {', '.join(domain_classes)}")
        
        # 检查典型的仓储方法
        for class_info in doc_structure['classes']:
            repo_methods = ['find', 'save', 'delete', 'update', 'get', 'create']
            found_methods = [m for m in class_info['methods'] 
                           if any(rm in m.lower() for rm in repo_methods)]
            if len(found_methods) >= 2:
                confidence += 0.2
                evidence.append(f"发现仓储方法: {', '.join(found_methods)}")
        
        return min(confidence, 1.0), matched_elements, evidence
    
    def _detect_anti_pattern(self, anti_pattern: AntiPattern, doc_structure: Dict[str, Any]) -> Optional[AntiPatternMatch]:
        """检测反模式"""
        confidence = 0.0
        detected_elements = []
        severity_score = 0.0
        
        if anti_pattern.anti_pattern_id == "god_class":
            confidence, detected_elements, severity_score = self._detect_god_class(doc_structure)
        elif anti_pattern.anti_pattern_id == "circular_dependency":
            confidence, detected_elements, severity_score = self._detect_circular_dependency(doc_structure)
        
        if confidence > 0:
            priority = "high" if severity_score > 0.7 else "medium" if severity_score > 0.4 else "low"
            
            return AntiPatternMatch(
                anti_pattern=anti_pattern,
                confidence=confidence,
                detected_elements=detected_elements,
                severity_score=severity_score,
                refactoring_priority=priority
            )
        
        return None
    
    def _detect_god_class(self, doc_structure: Dict[str, Any]) -> Tuple[float, List[str], float]:
        """检测上帝类反模式"""
        confidence = 0.0
        detected_elements = []
        severity_score = 0.0
        
        for class_info in doc_structure['classes']:
            god_class_score = 0.0
            
            # 1. 方法数量过多
            method_count = len(class_info['methods'])
            if method_count > 20:
                god_class_score += 0.4
            elif method_count > 10:
                god_class_score += 0.2
            
            # 2. 属性数量过多
            attr_count = len(class_info['attributes'])
            if attr_count > 15:
                god_class_score += 0.3
            elif attr_count > 8:
                god_class_score += 0.15
            
            # 3. 类名包含过于通用的词汇
            class_name = class_info['name'].lower()
            god_class_keywords = ['manager', 'controller', 'handler', 'processor', 'service', 'util', 'helper']
            if any(keyword in class_name for keyword in god_class_keywords):
                god_class_score += 0.2
            
            # 4. 职责过多的迹象（基于方法名）
            responsibilities = set()
            for method in class_info['methods']:
                method_lower = method.lower()
                if any(verb in method_lower for verb in ['get', 'set', 'find', 'create', 'delete', 'update', 'process', 'validate', 'convert']):
                    responsibilities.add(method_lower.split('_')[0] if '_' in method_lower else method_lower[:4])
            
            if len(responsibilities) > 5:
                god_class_score += 0.3
            
            # 如果得分超过阈值，认为是上帝类
            if god_class_score >= 0.5:
                confidence = max(confidence, god_class_score)
                detected_elements.append(class_info['name'])
                severity_score = max(severity_score, god_class_score)
        
        # 如果没有明显的上帝类，但有复杂类，给予基本检测
        if confidence == 0 and doc_structure['classes']:
            largest_class = max(doc_structure['classes'], 
                              key=lambda x: len(x['methods']) + len(x['attributes']))
            if len(largest_class['methods']) + len(largest_class['attributes']) > 8:
                confidence = 0.4
                detected_elements = [largest_class['name']]
                severity_score = 0.4
        
        return confidence, detected_elements, severity_score
    
    def _match_singleton_pattern(self, doc_structure: Dict[str, Any]) -> Tuple[float, List[str], List[str]]:
        """匹配单例模式"""
        confidence = 0.0
        matched_elements = []
        evidence = []
        
        for class_info in doc_structure['classes']:
            class_name = class_info['name'].lower()
            methods = [m.lower() for m in class_info['methods']]
            
            # 单例模式特征检测
            singleton_features = 0
            
            # 1. 类名包含Singleton
            if 'singleton' in class_name:
                singleton_features += 1
                evidence.append(f"类名包含'singleton': {class_info['name']}")
            
            # 2. 有getInstance方法
            if any('getinstance' in m for m in methods):
                singleton_features += 1
                evidence.append(f"发现getInstance方法")
            
            # 3. 只有一个实例的特征（简化检测）
            if len(class_info['methods']) <= 5 and len(class_info['attributes']) <= 3:
                singleton_features += 0.5
                evidence.append(f"类结构简单，符合单例特征")
            
            if singleton_features >= 1:
                confidence += min(singleton_features * 0.4, 0.8)
                matched_elements.append(class_info['name'])
        
        # 如果没有明显的单例特征，但有管理类，给予基本置信度
        if confidence == 0:
            for class_info in doc_structure['classes']:
                if any(keyword in class_info['name'].lower() 
                      for keyword in ['manager', 'controller', 'service']):
                    confidence = 0.4
                    matched_elements.append(class_info['name'])
                    evidence.append("发现管理类，可能使用单例模式")
                    break
        
        return confidence, matched_elements, evidence
    
    def _match_factory_method_pattern(self, doc_structure: Dict[str, Any]) -> Tuple[float, List[str], List[str]]:
        """匹配工厂方法模式"""
        confidence = 0.0
        matched_elements = []
        evidence = []
        
        for class_info in doc_structure['classes']:
            class_name = class_info['name'].lower()
            methods = [m.lower() for m in class_info['methods']]
            
            # 工厂模式特征检测
            factory_features = 0
            
            # 1. 类名包含Factory
            if 'factory' in class_name:
                factory_features += 1
                evidence.append(f"工厂类: {class_info['name']}")
            
            # 2. 有create方法
            create_methods = [m for m in methods if 'create' in m or 'make' in m or 'build' in m]
            if create_methods:
                factory_features += 1
                evidence.append(f"发现创建方法: {', '.join(create_methods)}")
            
            # 3. 有生产者特征
            if any(keyword in class_name for keyword in ['producer', 'builder', 'creator']):
                factory_features += 1
                evidence.append(f"发现生产者类: {class_info['name']}")
            
            if factory_features >= 1:
                confidence += min(factory_features * 0.3, 0.7)
                matched_elements.append(class_info['name'])
        
        # 如果没有明显工厂，但有创建类型的类，给予基本置信度
        if confidence == 0 and len(doc_structure['classes']) > 3:
            confidence = 0.35
            matched_elements = [cls['name'] for cls in doc_structure['classes'][:2]]
            evidence.append("复杂类结构，可能使用工厂模式")
        
        return confidence, matched_elements, evidence
    
    def _match_mvc_pattern(self, doc_structure: Dict[str, Any]) -> Tuple[float, List[str], List[str]]:
        """匹配MVC模式"""
        confidence = 0.0
        matched_elements = []
        evidence = []
        
        mvc_components = {'model': [], 'view': [], 'controller': []}
        
        for class_info in doc_structure['classes']:
            class_name = class_info['name'].lower()
            stereotype = class_info['stereotype'].lower()
            
            # MVC组件识别
            if 'model' in class_name or stereotype == 'model':
                mvc_components['model'].append(class_info['name'])
            elif 'view' in class_name or stereotype == 'view':
                mvc_components['view'].append(class_info['name'])
            elif 'controller' in class_name or stereotype == 'controller':
                mvc_components['controller'].append(class_info['name'])
        
        # 计算MVC置信度
        found_components = sum(1 for comp in mvc_components.values() if comp)
        if found_components >= 2:
            confidence = 0.6 + (found_components - 2) * 0.2
            for comp_type, classes in mvc_components.items():
                if classes:
                    matched_elements.extend(classes)
                    evidence.append(f"发现{comp_type}组件: {', '.join(classes)}")
        elif len(doc_structure['classes']) >= 3:
            # 如果有足够的类但没有明显的MVC命名，给予低置信度
            confidence = 0.3
            matched_elements = [cls['name'] for cls in doc_structure['classes'][:3]]
            evidence.append("类结构可能符合MVC模式")
        
        return confidence, matched_elements, evidence
    
    def _generic_pattern_match(self, pattern: DesignPattern, doc_structure: Dict[str, Any]) -> Tuple[float, List[str], List[str]]:
        """通用模式匹配"""
        confidence = 0.0
        matched_elements = []
        evidence = []
        
        # 基于模式名称和标签的简单匹配
        pattern_keywords = [pattern.name.lower()] + [tag.lower() for tag in pattern.tags]
        
        for class_info in doc_structure['classes']:
            class_name = class_info['name'].lower()
            for keyword in pattern_keywords:
                if keyword in class_name:
                    confidence += 0.3
                    matched_elements.append(class_info['name'])
                    evidence.append(f"类名匹配模式关键词: {keyword}")
                    break
        
        # 如果没有直接匹配，基于复杂度给予基本置信度
        if confidence == 0 and len(doc_structure['classes']) > 1:
            confidence = 0.25
            matched_elements = [cls['name'] for cls in doc_structure['classes'][:2]]
            evidence.append(f"基于结构复杂度匹配{pattern.name}模式")
        
        return confidence, matched_elements, evidence
    
    def _detect_circular_dependency(self, doc_structure: Dict[str, Any]) -> Tuple[float, List[str], float]:
        """检测循环依赖反模式"""
        confidence = 0.0
        detected_elements = []
        severity_score = 0.0
        
        # 构建依赖关系图（简化版本）
        dependencies = {}
        for class_info in doc_structure['classes']:
            class_name = class_info['name']
            dependencies[class_name] = []
            
            # 基于方法名推断依赖关系
            for method in class_info['methods']:
                for other_class in doc_structure['classes']:
                    if (other_class['name'] != class_name and 
                        other_class['name'].lower() in method.lower()):
                        dependencies[class_name].append(other_class['name'])
        
        # 检测简单的双向依赖
        circular_pairs = []
        for class_a, deps_a in dependencies.items():
            for class_b in deps_a:
                if class_b in dependencies and class_a in dependencies[class_b]:
                    pair = tuple(sorted([class_a, class_b]))
                    if pair not in circular_pairs:
                        circular_pairs.append(pair)
        
        if circular_pairs:
            confidence = 0.7
            detected_elements = list(set([cls for pair in circular_pairs for cls in pair]))
            severity_score = 0.6
        elif len(doc_structure['classes']) > 3:
            # 如果有多个类，可能存在潜在的循环依赖
            confidence = 0.3
            detected_elements = [cls['name'] for cls in doc_structure['classes'][:2]]
            severity_score = 0.3
        
        return confidence, detected_elements, severity_score
    
    # 其他创建模式的方法...
    def _create_abstract_factory_pattern(self) -> DesignPattern:
        """创建抽象工厂模式"""
        return DesignPattern(
            pattern_id="abstract_factory",
            name="Abstract Factory",
            category=PatternCategory.CREATIONAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.COMPLEX,
            intent="提供一个创建一系列相关或相互依赖对象的接口",
            motivation="系统要独立于它的产品的创建、组合和表示时",
            applicability=["系统应该独立于它的产品的创建、组合和表示"],
            structure=PatternStructure(),
            tags={"creational", "factory", "family"}
        )
    
    def _create_builder_pattern(self) -> DesignPattern:
        """创建建造者模式"""
        return DesignPattern(
            pattern_id="builder",
            name="Builder",
            category=PatternCategory.CREATIONAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.MEDIUM,
            intent="将一个复杂对象的构建与它的表示分离",
            motivation="创建复杂对象的算法应该独立于该对象的组成部分",
            applicability=["当创建复杂对象的算法应该独立于该对象的组成部分以及它们的装配方式时"],
            structure=PatternStructure(),
            tags={"creational", "construction", "complex_object"}
        )
    
    def _create_prototype_pattern(self) -> DesignPattern:
        """创建原型模式"""
        return DesignPattern(
            pattern_id="prototype",
            name="Prototype",
            category=PatternCategory.CREATIONAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.MEDIUM,
            intent="用原型实例指定创建对象的种类，并且通过拷贝这些原型创建新的对象",
            motivation="当要实例化的类是在运行时刻指定时",
            applicability=["当要实例化的类是在运行时刻指定时"],
            structure=PatternStructure(),
            tags={"creational", "cloning", "runtime"}
        )
    
    def _create_adapter_pattern(self) -> DesignPattern:
        """创建适配器模式"""
        return DesignPattern(
            pattern_id="adapter",
            name="Adapter",
            category=PatternCategory.STRUCTURAL,
            scope=PatternScope.CLASS,
            complexity=PatternComplexity.MEDIUM,
            intent="将一个类的接口转换成客户希望的另外一个接口",
            motivation="使得原本由于接口不兼容而不能一起工作的类可以一起工作",
            applicability=["想使用一个已经存在的类，而它的接口不符合你的需求"],
            structure=PatternStructure(),
            tags={"structural", "interface", "compatibility"}
        )
    
    def _create_bridge_pattern(self) -> DesignPattern:
        """创建桥接模式"""
        return DesignPattern(
            pattern_id="bridge",
            name="Bridge",
            category=PatternCategory.STRUCTURAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.COMPLEX,
            intent="将抽象部分与实现部分分离，使它们都可以独立变化",
            motivation="避免在抽象和实现之间建立永久性的绑定关系",
            applicability=["不希望在抽象和实现部分之间有一个固定的绑定关系"],
            structure=PatternStructure(),
            tags={"structural", "abstraction", "implementation"}
        )
    
    def _create_composite_pattern(self) -> DesignPattern:
        """创建组合模式"""
        return DesignPattern(
            pattern_id="composite",
            name="Composite",
            category=PatternCategory.STRUCTURAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.MEDIUM,
            intent="将对象组合成树形结构以表示'部分-整体'的层次结构",
            motivation="使得用户对单个对象和组合对象的使用具有一致性",
            applicability=["想表示对象的部分-整体层次结构"],
            structure=PatternStructure(),
            tags={"structural", "tree", "hierarchy"}
        )
    
    def _create_decorator_pattern(self) -> DesignPattern:
        """创建装饰器模式"""
        return DesignPattern(
            pattern_id="decorator",
            name="Decorator",
            category=PatternCategory.STRUCTURAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.MEDIUM,
            intent="动态地给一个对象添加一些额外的职责",
            motivation="比生成子类更为灵活的为对象增加功能的方式",
            applicability=["在不影响其他对象的情况下，以动态、透明的方式给单个对象添加职责"],
            structure=PatternStructure(),
            tags={"structural", "dynamic", "responsibility"}
        )
    
    def _create_facade_pattern(self) -> DesignPattern:
        """创建外观模式"""
        return DesignPattern(
            pattern_id="facade",
            name="Facade",
            category=PatternCategory.STRUCTURAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.SIMPLE,
            intent="为子系统中的一组接口提供一个一致的界面",
            motivation="定义了一个高层接口，这个接口使得这一子系统更加容易使用",
            applicability=["当你要为一个复杂子系统提供一个简单接口时"],
            structure=PatternStructure(),
            tags={"structural", "interface", "simplification"}
        )
    
    def _create_flyweight_pattern(self) -> DesignPattern:
        """创建享元模式"""
        return DesignPattern(
            pattern_id="flyweight",
            name="Flyweight",
            category=PatternCategory.STRUCTURAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.COMPLEX,
            intent="运用共享技术有效地支持大量细粒度的对象",
            motivation="避免大量拥有相同内容对象的开销",
            applicability=["应用程序使用了大量的对象"],
            structure=PatternStructure(),
            tags={"structural", "sharing", "memory"}
        )
    
    def _create_proxy_pattern(self) -> DesignPattern:
        """创建代理模式"""
        return DesignPattern(
            pattern_id="proxy",
            name="Proxy",
            category=PatternCategory.STRUCTURAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.MEDIUM,
            intent="为其他对象提供一种代理以控制对这个对象的访问",
            motivation="在某些情况下，一个对象不适合或者不能直接引用另一个对象",
            applicability=["需要用比较通用和复杂的对象指针代替简单的指针时"],
            structure=PatternStructure(),
            tags={"structural", "proxy", "access_control"}
        )
    
    def _create_observer_pattern(self) -> DesignPattern:
        """创建观察者模式"""
        return DesignPattern(
            pattern_id="observer",
            name="Observer",
            category=PatternCategory.BEHAVIORAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.MEDIUM,
            intent="定义对象间的一种一对多的依赖关系",
            motivation="当一个对象的状态发生改变时，所有依赖于它的对象都得到通知并被自动更新",
            applicability=["当一个抽象模型有两个方面，其中一个方面依赖于另一方面"],
            structure=PatternStructure(),
            tags={"behavioral", "notification", "dependency"}
        )
    
    def _create_strategy_pattern(self) -> DesignPattern:
        """创建策略模式"""
        return DesignPattern(
            pattern_id="strategy",
            name="Strategy",
            category=PatternCategory.BEHAVIORAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.SIMPLE,
            intent="定义一系列的算法，把它们一个个封装起来，并且使它们可相互替换",
            motivation="使算法可独立于使用它的客户而变化",
            applicability=["许多相关的类仅仅是行为有异"],
            structure=PatternStructure(),
            tags={"behavioral", "algorithm", "encapsulation"}
        )
    
    def _create_command_pattern(self) -> DesignPattern:
        """创建命令模式"""
        return DesignPattern(
            pattern_id="command",
            name="Command",
            category=PatternCategory.BEHAVIORAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.MEDIUM,
            intent="将一个请求封装为一个对象，从而使你可用不同的请求对客户进行参数化",
            motivation="对请求排队或记录请求日志，以及支持可撤销的操作",
            applicability=["需要将动作请求化"],
            structure=PatternStructure(),
            tags={"behavioral", "request", "encapsulation"}
        )
    
    def _create_state_pattern(self) -> DesignPattern:
        """创建状态模式"""
        return DesignPattern(
            pattern_id="state",
            name="State",
            category=PatternCategory.BEHAVIORAL,
            scope=PatternScope.OBJECT,
            complexity=PatternComplexity.MEDIUM,
            intent="允许一个对象在其内部状态改变时改变它的行为",
            motivation="对象看起来似乎修改了它的类",
            applicability=["一个对象的行为取决于它的状态"],
            structure=PatternStructure(),
            tags={"behavioral", "state", "behavior_change"}
        )
    
    def _create_template_method_pattern(self) -> DesignPattern:
        """创建模板方法模式"""
        return DesignPattern(
            pattern_id="template_method",
            name="Template Method",
            category=PatternCategory.BEHAVIORAL,
            scope=PatternScope.CLASS,
            complexity=PatternComplexity.SIMPLE,
            intent="定义一个操作中的算法的骨架，而将一些步骤延迟到子类中",
            motivation="使得子类可以不改变一个算法的结构即可重定义该算法的某些特定步骤",
            applicability=["一次性实现一个算法的不变的部分"],
            structure=PatternStructure(),
            tags={"behavioral", "algorithm", "inheritance"}
        )
    
    def _create_mvc_pattern(self) -> DesignPattern:
        """创建MVC模式"""
        return DesignPattern(
            pattern_id="mvc",
            name="Model-View-Controller",
            category=PatternCategory.ARCHITECTURAL,
            scope=PatternScope.SYSTEM,
            complexity=PatternComplexity.COMPLEX,
            intent="将应用程序分离为三个主要组件：模型、视图和控制器",
            motivation="分离关注点，提高代码的可维护性和可测试性",
            applicability=["需要分离用户界面和业务逻辑"],
            structure=PatternStructure(),
            tags={"architectural", "separation", "ui"}
        )
    
    def _create_layered_pattern(self) -> DesignPattern:
        """创建分层模式"""
        return DesignPattern(
            pattern_id="layered",
            name="Layered Architecture",
            category=PatternCategory.ARCHITECTURAL,
            scope=PatternScope.SYSTEM,
            complexity=PatternComplexity.MEDIUM,
            intent="将软件系统组织成层次结构",
            motivation="每一层只能调用下层的服务，提供上层的服务",
            applicability=["需要组织复杂系统的结构"],
            structure=PatternStructure(),
            tags={"architectural", "layers", "hierarchy"}
        )
    
    def _create_microservices_pattern(self) -> DesignPattern:
        """创建微服务模式"""
        return DesignPattern(
            pattern_id="microservices",
            name="Microservices",
            category=PatternCategory.ARCHITECTURAL,
            scope=PatternScope.SYSTEM,
            complexity=PatternComplexity.COMPLEX,
            intent="将单体应用程序开发为一套小型服务",
            motivation="每个服务运行在自己的进程中，通过轻量级机制通信",
            applicability=["需要独立部署和扩展应用程序的不同部分"],
            structure=PatternStructure(),
            tags={"architectural", "distributed", "scalability"}
        )
    
    def _create_event_driven_pattern(self) -> DesignPattern:
        """创建事件驱动模式"""
        return DesignPattern(
            pattern_id="event_driven",
            name="Event-Driven Architecture",
            category=PatternCategory.ARCHITECTURAL,
            scope=PatternScope.SYSTEM,
            complexity=PatternComplexity.COMPLEX,
            intent="通过事件的产生、传播、处理来驱动程序运行",
            motivation="提高系统的响应性和可扩展性",
            applicability=["需要处理异步事件和松耦合组件"],
            structure=PatternStructure(),
            tags={"architectural", "events", "asynchronous"}
        )
    
    def _create_hexagonal_pattern(self) -> DesignPattern:
        """创建六边形模式"""
        return DesignPattern(
            pattern_id="hexagonal",
            name="Hexagonal Architecture",
            category=PatternCategory.ARCHITECTURAL,
            scope=PatternScope.SYSTEM,
            complexity=PatternComplexity.COMPLEX,
            intent="将应用程序与外部环境隔离",
            motivation="通过端口和适配器来连接内部和外部",
            applicability=["需要保护核心业务逻辑不受外部变化影响"],
            structure=PatternStructure(),
            tags={"architectural", "ports", "adapters"}
        )
    
    def _create_spaghetti_code_anti_pattern(self) -> AntiPattern:
        """创建意大利面条代码反模式"""
        return AntiPattern(
            anti_pattern_id="spaghetti_code",
            name="Spaghetti Code",
            category=PatternCategory.ANTI_PATTERN,
            description="缺乏清晰结构的代码，控制流复杂且难以理解",
            symptoms=[
                "控制流复杂且难以跟踪",
                "缺乏清晰的程序结构",
                "大量的goto语句或复杂的条件嵌套",
                "难以理解和维护"
            ],
            consequences=[
                "代码难以理解",
                "维护成本高",
                "容易引入错误",
                "难以重构"
            ],
            causes=[
                "缺乏设计规划",
                "频繁的需求变更",
                "缺乏代码审查",
                "开发人员经验不足"
            ],
            solutions=[
                "重构代码结构",
                "使用设计模式",
                "模块化设计",
                "代码审查"
            ],
            detection_rules=[
                "圈复杂度过高",
                "深度嵌套过多",
                "缺乏函数分解",
                "控制流混乱"
            ],
            refactoring_steps=[
                "分析现有代码结构",
                "识别主要功能块",
                "提取函数和类",
                "简化控制流",
                "添加适当的注释"
            ],
            severity="high",
            frequency="common"
        )
    
    def _create_circular_dependency_anti_pattern(self) -> AntiPattern:
        """创建循环依赖反模式"""
        return AntiPattern(
            anti_pattern_id="circular_dependency",
            name="Circular Dependency",
            category=PatternCategory.ANTI_PATTERN,
            description="两个或多个模块之间存在循环依赖关系",
            symptoms=[
                "模块A依赖模块B，模块B依赖模块A",
                "编译或运行时出现依赖错误",
                "难以进行单元测试",
                "模块难以单独重用"
            ],
            consequences=[
                "增加系统复杂性",
                "降低模块的可重用性",
                "影响测试能力",
                "部署困难"
            ],
            causes=[
                "设计不当",
                "缺乏依赖分析",
                "过度耦合",
                "架构演化问题"
            ],
            solutions=[
                "依赖注入",
                "引入中介模块",
                "重新设计架构",
                "使用接口隔离"
            ],
            detection_rules=[
                "依赖图中存在环",
                "编译时循环依赖错误",
                "导入冲突",
                "模块间强耦合"
            ],
            refactoring_steps=[
                "绘制依赖关系图",
                "识别循环依赖路径",
                "引入抽象层",
                "重构依赖关系",
                "验证解决方案"
            ],
            severity="medium",
            frequency="common"
        )
    
    def _create_magic_number_anti_pattern(self) -> AntiPattern:
        """创建魔法数字反模式"""
        return AntiPattern(
            anti_pattern_id="magic_number",
            name="Magic Number",
            category=PatternCategory.ANTI_PATTERN,
            description="在代码中直接使用数字字面量而不进行说明",
            symptoms=[
                "代码中出现没有意义的数字",
                "缺乏对数字含义的解释",
                "同样的数字在多处重复出现",
                "修改数字时需要在多处进行更改"
            ],
            consequences=[
                "代码可读性差",
                "维护困难",
                "容易出错",
                "难以理解业务逻辑"
            ],
            causes=[
                "缺乏编码规范",
                "开发时间紧迫",
                "缺乏代码审查",
                "开发人员疏忽"
            ],
            solutions=[
                "使用命名常量",
                "创建枚举类型",
                "添加注释说明",
                "重构代码"
            ],
            detection_rules=[
                "代码中出现数字字面量",
                "同一数字多次出现",
                "缺乏常量定义",
                "数字含义不明确"
            ],
            refactoring_steps=[
                "识别所有魔法数字",
                "分析数字的含义",
                "创建命名常量",
                "替换魔法数字",
                "验证功能正确性"
            ],
            severity="low",
            frequency="very_common"
        )
    
    def _create_dead_code_anti_pattern(self) -> AntiPattern:
        """创建死代码反模式"""
        return AntiPattern(
            anti_pattern_id="dead_code",
            name="Dead Code",
            category=PatternCategory.ANTI_PATTERN,
            description="永远不会被执行到的代码",
            symptoms=[
                "无法到达的代码块",
                "未使用的变量或方法",
                "永远为假的条件分支",
                "注释掉但未删除的代码"
            ],
            consequences=[
                "增加代码复杂度",
                "影响代码可读性",
                "浪费维护成本",
                "可能引起混淆"
            ],
            causes=[
                "需求变更",
                "重构不彻底",
                "开发人员疏忽",
                "缺乏代码分析工具"
            ],
            solutions=[
                "使用静态分析工具",
                "定期代码清理",
                "代码审查",
                "自动化测试覆盖"
            ],
            detection_rules=[
                "静态分析显示未使用代码",
                "代码覆盖率分析",
                "注释掉的代码块",
                "无法到达的代码路径"
            ],
            refactoring_steps=[
                "运行静态分析工具",
                "识别未使用的代码",
                "确认代码确实不需要",
                "安全删除死代码",
                "验证系统功能"
            ],
            severity="low",
            frequency="common"
        )

# 工厂函数
def create_pattern_library(config: Dict[str, Any] = None) -> PatternLibrary:
    """创建模式库实例"""
    return PatternLibrary(config) 