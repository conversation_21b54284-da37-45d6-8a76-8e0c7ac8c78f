"""
术语数据库 (Terminology DB)
多语言术语管理和标准化

主要功能：
- 多领域术语库（UML、SysML、BPMN等）
- 术语翻译和多语言支持
- 术语关系管理（同义词、反义词、上下位关系）
- 术语标准化和一致性检查
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple, Union, Set
import logging
from dataclasses import dataclass, field
from enum import Enum
import json
import re
from collections import defaultdict, Counter
from datetime import datetime
import sqlite3
import difflib

# 配置日志
logger = logging.getLogger(__name__)

class RelationType(Enum):
    """关系类型"""
    SYNONYM = "synonym"
    ANTONYM = "antonym"
    HYPERNYM = "hypernym"  # 上位词
    HYPONYM = "hyponym"    # 下位词
    MERONYM = "meronym"    # 部分词
    HOLONYM = "holonym"    # 整体词
    RELATED = "related"
    EQUIVALENT = "equivalent"

class TermStatus(Enum):
    """术语状态"""
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    CANDIDATE = "candidate"
    REJECTED = "rejected"

class Language(Enum):
    """支持的语言"""
    EN = "en"  # 英语
    ZH = "zh"  # 中文
    JA = "ja"  # 日语
    KO = "ko"  # 韩语
    DE = "de"  # 德语
    FR = "fr"  # 法语
    ES = "es"  # 西班牙语
    PT = "pt"  # 葡萄牙语
    RU = "ru"  # 俄语
    AR = "ar"  # 阿拉伯语

class TermType(Enum):
    """术语类型"""
    CONCEPT = "concept"           # 概念术语
    PROPERTY = "property"         # 属性术语
    RELATIONSHIP = "relationship" # 关系术语
    CONSTRAINT = "constraint"     # 约束术语
    UNIT = "unit"                # 单位术语
    VALUE = "value"              # 值术语

@dataclass
class Term:
    """术语"""
    term_id: str
    text: str
    language: Language
    domain: str
    category: str
    definition: str
    status: TermStatus = TermStatus.ACTIVE
    confidence: float = 1.0
    source: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TermRelationship:
    """术语关系"""
    relationship_id: str
    source_term_id: str
    target_term_id: str
    relation_type: RelationType
    confidence: float = 1.0
    bidirectional: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Translation:
    """翻译"""
    translation_id: str
    source_term_id: str
    target_text: str
    target_language: Language
    confidence: float
    translation_method: str  # 'human', 'machine', 'hybrid'
    verified: bool = False

@dataclass
class MultilingualTermSet:
    """多语言术语集"""
    concept_id: str
    terms: Dict[Language, List[Term]]
    primary_language: Language
    definition: str

@dataclass
class LanguageDetectionResult:
    """语言检测结果"""
    detected_language: Language
    confidence: float
    alternative_languages: List[Tuple[Language, float]]

@dataclass
class TermQuery:
    """术语查询"""
    text: Optional[str] = None
    language: Optional[Language] = None
    domain: Optional[str] = None
    category: Optional[str] = None
    status: Optional[TermStatus] = None
    fuzzy_match: bool = False
    similarity_threshold: float = 0.8

@dataclass
class TermUpdates:
    """术语更新"""
    definition: Optional[str] = None
    category: Optional[str] = None
    status: Optional[TermStatus] = None
    confidence: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class RelatedTerm:
    """相关术语"""
    term: Term
    relation_type: RelationType
    confidence: float
    path_length: int  # 关系路径长度

@dataclass
class TermHierarchy:
    """术语层次结构"""
    root_terms: List[Term]
    hierarchy_tree: Dict[str, List[str]]
    depth_levels: Dict[str, int]

@dataclass
class StandardizationResult:
    """标准化结果"""
    original_terms: List[str]
    standardized_terms: List[str]
    replacements_made: int
    suggestions: List[Dict[str, str]]
    consistency_score: float

@dataclass
class ConsistencyReport:
    """一致性报告"""
    total_terms: int
    inconsistent_terms: List[str]
    consistency_issues: List[Dict[str, Any]]
    consistency_score: float
    recommendations: List[str]

@dataclass
class ReplacementSuggestions:
    """替换建议"""
    deprecated_terms: List[str]
    replacement_mapping: Dict[str, str]
    confidence_scores: Dict[str, float]
    migration_plan: List[str]

class TerminologyDB:
    """术语数据库"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化术语数据库"""
        self.config = config or {}
        self.db_path = self.config.get('db_path', ':memory:')
        
        # 数据库连接
        self.conn = None
        
        # 内存缓存
        self.term_cache = {}
        self.relationship_cache = {}
        self.translation_cache = {}
        
        # 领域特定词汇表
        self.domain_vocabularies = {}
        
        # 语言检测器
        self.language_patterns = {}
        
        # 术语标准化规则
        self.standardization_rules = {}
        
        # 初始化组件
        self._initialize_database()
        self._load_domain_vocabularies()
        self._load_language_patterns()
        self._load_standardization_rules()
        
    def _initialize_database(self):
        """初始化数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            
            # 创建表结构
            self._create_tables()
            
            logger.info(f"术语数据库初始化成功: {self.db_path}")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_tables(self):
        """创建数据库表"""
        cursor = self.conn.cursor()
        
        # 术语表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS terms (
                term_id TEXT PRIMARY KEY,
                text TEXT NOT NULL,
                language TEXT NOT NULL,
                domain TEXT,
                category TEXT,
                definition TEXT,
                status TEXT DEFAULT 'active',
                confidence REAL DEFAULT 1.0,
                source TEXT,
                created_at TEXT,
                updated_at TEXT,
                metadata TEXT
            )
        ''')
        
        # 术语关系表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS term_relationships (
                relationship_id TEXT PRIMARY KEY,
                source_term_id TEXT,
                target_term_id TEXT,
                relation_type TEXT,
                confidence REAL DEFAULT 1.0,
                bidirectional INTEGER DEFAULT 0,
                metadata TEXT,
                FOREIGN KEY (source_term_id) REFERENCES terms (term_id),
                FOREIGN KEY (target_term_id) REFERENCES terms (term_id)
            )
        ''')
        
        # 翻译表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS translations (
                translation_id TEXT PRIMARY KEY,
                source_term_id TEXT,
                target_text TEXT,
                target_language TEXT,
                confidence REAL,
                translation_method TEXT,
                verified INTEGER DEFAULT 0,
                FOREIGN KEY (source_term_id) REFERENCES terms (term_id)
            )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_terms_text ON terms (text)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_terms_language ON terms (language)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_terms_domain ON terms (domain)')
        
        self.conn.commit()
    
    def add_term(self, term: Term) -> bool:
        """添加术语"""
        try:
            cursor = self.conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO terms 
                (term_id, text, language, domain, category, definition, status, 
                 confidence, source, created_at, updated_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                term.term_id, term.text, term.language.value, term.domain,
                term.category, term.definition, term.status.value,
                term.confidence, term.source, term.created_at.isoformat(),
                term.updated_at.isoformat(), json.dumps(term.metadata)
            ))
            
            self.conn.commit()
            
            # 更新缓存
            self.term_cache[term.term_id] = term
            
            logger.info(f"术语添加成功: {term.text} ({term.term_id})")
            return True
            
        except Exception as e:
            logger.error(f"术语添加失败: {e}")
            return False
    
    def update_term(self, term_id: str, updates: TermUpdates) -> bool:
        """更新术语"""
        try:
            # 获取现有术语
            term = self._get_term_by_id(term_id)
            if not term:
                return False
            
            # 应用更新
            if updates.definition is not None:
                term.definition = updates.definition
            if updates.category is not None:
                term.category = updates.category
            if updates.status is not None:
                term.status = updates.status
            if updates.confidence is not None:
                term.confidence = updates.confidence
            if updates.metadata is not None:
                term.metadata.update(updates.metadata)
            
            term.updated_at = datetime.now()
            
            # 保存到数据库
            cursor = self.conn.cursor()
            cursor.execute('''
                UPDATE terms 
                SET definition=?, category=?, status=?, confidence=?, 
                    updated_at=?, metadata=?
                WHERE term_id=?
            ''', (
                term.definition, term.category, term.status.value,
                term.confidence, term.updated_at.isoformat(),
                json.dumps(term.metadata), term_id
            ))
            
            self.conn.commit()
            
            # 更新缓存
            self.term_cache[term_id] = term
            
            logger.info(f"术语更新成功: {term_id}")
            return True
            
        except Exception as e:
            logger.error(f"术语更新失败: {e}")
            return False
    
    def delete_term(self, term_id: str) -> bool:
        """删除术语"""
        try:
            cursor = self.conn.cursor()
            
            # 删除相关关系
            cursor.execute('''
                DELETE FROM term_relationships 
                WHERE source_term_id=? OR target_term_id=?
            ''', (term_id, term_id))
            
            # 删除翻译
            cursor.execute('DELETE FROM translations WHERE source_term_id=?', (term_id,))
            
            # 删除术语
            cursor.execute('DELETE FROM terms WHERE term_id=?', (term_id,))
            
            self.conn.commit()
            
            # 清除缓存
            if term_id in self.term_cache:
                del self.term_cache[term_id]
            
            logger.info(f"术语删除成功: {term_id}")
            return True
            
        except Exception as e:
            logger.error(f"术语删除失败: {e}")
            return False
    
    def search_terms(self, query: TermQuery) -> List[Term]:
        """搜索术语"""
        try:
            cursor = self.conn.cursor()
            
            # 构建查询条件
            conditions = []
            params = []
            
            if query.text:
                if query.fuzzy_match:
                    conditions.append("text LIKE ?")
                    params.append(f"%{query.text}%")
                else:
                    conditions.append("text = ?")
                    params.append(query.text)
            
            if query.language:
                conditions.append("language = ?")
                params.append(query.language.value)
            
            if query.domain:
                conditions.append("domain = ?")
                params.append(query.domain)
            
            if query.category:
                conditions.append("category = ?")
                params.append(query.category)
            
            if query.status:
                conditions.append("status = ?")
                params.append(query.status.value)
            
            # 构建SQL
            sql = "SELECT * FROM terms"
            if conditions:
                sql += " WHERE " + " AND ".join(conditions)
            sql += " ORDER BY confidence DESC, text ASC"
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            
            # 转换为Term对象
            terms = []
            for row in rows:
                term = self._row_to_term(row)
                
                # 如果启用模糊匹配，计算相似度
                if query.fuzzy_match and query.text:
                    similarity = self._calculate_similarity(query.text, term.text)
                    if similarity >= query.similarity_threshold:
                        term.metadata['similarity'] = similarity
                        terms.append(term)
                else:
                    terms.append(term)
            
            logger.info(f"搜索到 {len(terms)} 个术语")
            return terms
            
        except Exception as e:
            logger.error(f"术语搜索失败: {e}")
            return []
    
    def translate_term(self, term: str, source_lang: str, target_lang: str) -> Translation:
        """翻译术语"""
        try:
            source_language = Language(source_lang)
            target_language = Language(target_lang)
            
            # 首先查找现有翻译
            existing_translation = self._find_existing_translation(term, source_language, target_language)
            if existing_translation:
                return existing_translation
            
            # 如果没有现有翻译，尝试生成新翻译
            translated_text = self._generate_translation(term, source_language, target_language)
            
            # 评估翻译质量
            confidence = self._evaluate_translation_quality(term, translated_text, source_language, target_language)
            
            translation = Translation(
                translation_id=f"trans_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(term+target_lang)}",
                source_term_id=self._get_or_create_term_id(term, source_language),
                target_text=translated_text,
                target_language=target_language,
                confidence=confidence,
                translation_method='hybrid',
                verified=False
            )
            
            # 保存翻译
            self._save_translation(translation)
            
            logger.info(f"术语翻译完成: {term} -> {translated_text}")
            return translation
            
        except Exception as e:
            logger.error(f"术语翻译失败: {e}")
            return Translation("", "", "", Language.EN, 0.0, "", False)
    
    def get_multilingual_terms(self, concept_id: str) -> MultilingualTermSet:
        """获取多语言术语集"""
        try:
            # 查找所有相关术语
            related_terms = self._find_terms_by_concept(concept_id)
            
            # 按语言分组
            terms_by_language = defaultdict(list)
            for term in related_terms:
                terms_by_language[term.language].append(term)
            
            # 确定主要语言（术语最多的语言）
            primary_language = max(terms_by_language.keys(), key=lambda lang: len(terms_by_language[lang]))
            
            # 获取概念定义
            definition = self._get_concept_definition(concept_id)
            
            return MultilingualTermSet(
                concept_id=concept_id,
                terms=dict(terms_by_language),
                primary_language=primary_language,
                definition=definition
            )
            
        except Exception as e:
            logger.error(f"多语言术语集获取失败: {e}")
            return MultilingualTermSet("", {}, Language.EN, "")
    
    def detect_term_language(self, text: str) -> LanguageDetectionResult:
        """检测术语语言"""
        try:
            language_scores = {}
            
            # 基于字符模式检测
            for language, patterns in self.language_patterns.items():
                score = 0.0
                for pattern in patterns:
                    if re.search(pattern, text):
                        score += 1.0
                
                # 标准化分数
                if patterns:
                    score = score / len(patterns)
                
                language_scores[language] = score
            
            # 找到最高分数的语言
            if language_scores:
                detected_language = max(language_scores.keys(), key=lambda lang: language_scores[lang])
                confidence = language_scores[detected_language]
                
                # 获取替代语言（分数前3的）
                sorted_langs = sorted(language_scores.items(), key=lambda x: x[1], reverse=True)
                alternatives = [(lang, score) for lang, score in sorted_langs[1:4]]
                
                return LanguageDetectionResult(
                    detected_language=Language(detected_language),
                    confidence=confidence,
                    alternative_languages=[(Language(lang), score) for lang, score in alternatives]
                )
            
            # 默认返回英语
            return LanguageDetectionResult(
                detected_language=Language.EN,
                confidence=0.5,
                alternative_languages=[]
            )
            
        except Exception as e:
            logger.error(f"语言检测失败: {e}")
            return LanguageDetectionResult(Language.EN, 0.0, [])
    
    def add_term_relationship(self, source_term: str, target_term: str, relation_type: RelationType):
        """添加术语关系"""
        try:
            # 查找术语ID
            source_id = self._find_term_id(source_term)
            target_id = self._find_term_id(target_term)
            
            if not source_id or not target_id:
                logger.warning(f"术语关系添加失败: 找不到术语 {source_term} 或 {target_term}")
                return False
            
            relationship = TermRelationship(
                relationship_id=f"rel_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(source_term+target_term)}",
                source_term_id=source_id,
                target_term_id=target_id,
                relation_type=relation_type,
                confidence=1.0,
                bidirectional=relation_type in [RelationType.SYNONYM, RelationType.EQUIVALENT]
            )
            
            # 保存到数据库
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO term_relationships 
                (relationship_id, source_term_id, target_term_id, relation_type, 
                 confidence, bidirectional, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                relationship.relationship_id, relationship.source_term_id,
                relationship.target_term_id, relationship.relation_type.value,
                relationship.confidence, int(relationship.bidirectional),
                json.dumps(relationship.metadata)
            ))
            
            self.conn.commit()
            
            logger.info(f"术语关系添加成功: {source_term} -> {target_term} ({relation_type.value})")
            return True
            
        except Exception as e:
            logger.error(f"术语关系添加失败: {e}")
            return False
    
    def get_related_terms(self, term: str, relation_types: List[RelationType]) -> List[RelatedTerm]:
        """获取相关术语"""
        try:
            term_id = self._find_term_id(term)
            if not term_id:
                return []
            
            cursor = self.conn.cursor()
            
            # 构建查询条件
            relation_placeholders = ','.join(['?' for _ in relation_types])
            
            # 查找直接关系（source_term_id = term_id）
            cursor.execute(f'''
                SELECT tr.*, t.* FROM term_relationships tr
                JOIN terms t ON tr.target_term_id = t.term_id
                WHERE tr.source_term_id = ? AND tr.relation_type IN ({relation_placeholders})
            ''', [term_id] + [rt.value for rt in relation_types])
            
            forward_relations = cursor.fetchall()
            
            # 查找反向关系（target_term_id = term_id 且双向）
            cursor.execute(f'''
                SELECT tr.*, t.* FROM term_relationships tr
                JOIN terms t ON tr.source_term_id = t.term_id
                WHERE tr.target_term_id = ? AND tr.bidirectional = 1 
                AND tr.relation_type IN ({relation_placeholders})
            ''', [term_id] + [rt.value for rt in relation_types])
            
            backward_relations = cursor.fetchall()
            
            # 组合结果
            related_terms = []
            
            for row in forward_relations:
                related_term = self._row_to_term(row)
                related_terms.append(RelatedTerm(
                    term=related_term,
                    relation_type=RelationType(row['relation_type']),
                    confidence=row['confidence'],
                    path_length=1
                ))
            
            for row in backward_relations:
                related_term = self._row_to_term(row)
                related_terms.append(RelatedTerm(
                    term=related_term,
                    relation_type=RelationType(row['relation_type']),
                    confidence=row['confidence'],
                    path_length=1
                ))
            
            logger.info(f"找到 {len(related_terms)} 个相关术语")
            return related_terms
            
        except Exception as e:
            logger.error(f"相关术语查找失败: {e}")
            return []
    
    def build_term_hierarchy(self, domain: str) -> TermHierarchy:
        """构建术语层次结构"""
        try:
            # 查找域内所有术语
            domain_terms = self.search_terms(TermQuery(domain=domain))
            
            # 构建层次关系
            hierarchy_tree = defaultdict(list)
            depth_levels = {}
            root_terms = []
            
            # 查找上下位关系
            for term in domain_terms:
                related = self.get_related_terms(term.text, [RelationType.HYPERNYM, RelationType.HYPONYM])
                
                has_parent = False
                for related_term in related:
                    if related_term.relation_type == RelationType.HYPERNYM:
                        # term是related_term的下位词
                        hierarchy_tree[related_term.term.term_id].append(term.term_id)
                        has_parent = True
                    elif related_term.relation_type == RelationType.HYPONYM:
                        # related_term是term的下位词
                        hierarchy_tree[term.term_id].append(related_term.term.term_id)
                
                if not has_parent:
                    root_terms.append(term)
            
            # 计算深度层级
            def calculate_depth(term_id, current_depth=0):
                depth_levels[term_id] = current_depth
                for child_id in hierarchy_tree.get(term_id, []):
                    calculate_depth(child_id, current_depth + 1)
            
            for root_term in root_terms:
                calculate_depth(root_term.term_id)
            
            return TermHierarchy(
                root_terms=root_terms,
                hierarchy_tree=dict(hierarchy_tree),
                depth_levels=depth_levels
            )
            
        except Exception as e:
            logger.error(f"术语层次结构构建失败: {e}")
            return TermHierarchy([], {}, {})
    
    def standardize_terminology(self, document: ET.Element) -> StandardizationResult:
        """标准化术语"""
        try:
            # 提取文档中的术语
            document_terms = self._extract_terms_from_document(document)
            
            original_terms = []
            standardized_terms = []
            replacements_made = 0
            suggestions = []
            
            for term_text in document_terms:
                original_terms.append(term_text)
                
                # 查找标准化替换
                standard_term = self._find_standard_term(term_text)
                
                if standard_term and standard_term != term_text:
                    standardized_terms.append(standard_term)
                    replacements_made += 1
                    suggestions.append({
                        'original': term_text,
                        'suggested': standard_term,
                        'confidence': 0.9
                    })
                else:
                    standardized_terms.append(term_text)
            
            # 计算一致性分数
            consistency_score = 1.0 - (replacements_made / max(len(original_terms), 1))
            
            return StandardizationResult(
                original_terms=original_terms,
                standardized_terms=standardized_terms,
                replacements_made=replacements_made,
                suggestions=suggestions,
                consistency_score=consistency_score
            )
            
        except Exception as e:
            logger.error(f"术语标准化失败: {e}")
            return StandardizationResult([], [], 0, [], 0.0)
    
    def check_term_consistency(self, document: ET.Element) -> ConsistencyReport:
        """检查术语一致性"""
        try:
            document_terms = self._extract_terms_from_document(document)
            
            # 统计术语使用
            term_counts = Counter(document_terms)
            total_terms = len(document_terms)
            
            # 查找不一致的术语
            inconsistent_terms = []
            consistency_issues = []
            
            for term, count in term_counts.items():
                # 查找同义词
                synonyms = self.get_related_terms(term, [RelationType.SYNONYM])
                
                if synonyms:
                    # 检查是否同时使用了同义词
                    for synonym_rel in synonyms:
                        synonym_term = synonym_rel.term.text
                        if synonym_term in term_counts:
                            inconsistent_terms.extend([term, synonym_term])
                            consistency_issues.append({
                                'type': 'synonym_conflict',
                                'terms': [term, synonym_term],
                                'counts': [count, term_counts[synonym_term]],
                                'suggestion': f"统一使用 '{term}' 或 '{synonym_term}'"
                            })
            
            # 去重
            inconsistent_terms = list(set(inconsistent_terms))
            
            # 计算一致性分数
            inconsistent_count = len(inconsistent_terms)
            consistency_score = 1.0 - (inconsistent_count / max(total_terms, 1))
            
            # 生成建议
            recommendations = [
                "建立术语规范文档",
                "使用术语检查工具",
                "定期进行术语审查"
            ]
            
            return ConsistencyReport(
                total_terms=total_terms,
                inconsistent_terms=inconsistent_terms,
                consistency_issues=consistency_issues,
                consistency_score=consistency_score,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"术语一致性检查失败: {e}")
            return ConsistencyReport(0, [], [], 0.0, [])
    
    def suggest_term_replacements(self, deprecated_terms: List[str]) -> ReplacementSuggestions:
        """建议术语替换"""
        try:
            replacement_mapping = {}
            confidence_scores = {}
            migration_plan = []
            
            for deprecated_term in deprecated_terms:
                # 查找推荐的替换术语
                replacement = self._find_replacement_term(deprecated_term)
                
                if replacement:
                    replacement_mapping[deprecated_term] = replacement
                    confidence_scores[deprecated_term] = 0.9
                    migration_plan.append(f"将 '{deprecated_term}' 替换为 '{replacement}'")
                else:
                    migration_plan.append(f"审查术语 '{deprecated_term}' 的使用")
            
            return ReplacementSuggestions(
                deprecated_terms=deprecated_terms,
                replacement_mapping=replacement_mapping,
                confidence_scores=confidence_scores,
                migration_plan=migration_plan
            )
            
        except Exception as e:
            logger.error(f"术语替换建议失败: {e}")
            return ReplacementSuggestions([], {}, {}, [])
    
    # 私有方法
    def _get_term_by_id(self, term_id: str) -> Optional[Term]:
        """通过ID获取术语"""
        if term_id in self.term_cache:
            return self.term_cache[term_id]
        
        cursor = self.conn.cursor()
        cursor.execute('SELECT * FROM terms WHERE term_id = ?', (term_id,))
        row = cursor.fetchone()
        
        if row:
            term = self._row_to_term(row)
            self.term_cache[term_id] = term
            return term
        
        return None
    
    def _row_to_term(self, row) -> Term:
        """将数据库行转换为Term对象"""
        metadata = json.loads(row['metadata']) if row['metadata'] else {}
        
        return Term(
            term_id=row['term_id'],
            text=row['text'],
            language=Language(row['language']),
            domain=row['domain'],
            category=row['category'],
            definition=row['definition'],
            status=TermStatus(row['status']),
            confidence=row['confidence'],
            source=row['source'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else datetime.now(),
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else datetime.now(),
            metadata=metadata
        )
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        return difflib.SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def _find_existing_translation(self, term: str, source_lang: Language, target_lang: Language) -> Optional[Translation]:
        """查找现有翻译"""
        # 简化实现
        return None
    
    def _generate_translation(self, term: str, source_lang: Language, target_lang: Language) -> str:
        """生成翻译"""
        # 简化实现 - 在实际应用中，这里会调用翻译API
        translation_dict = {
            ('class', 'zh'): '类',
            ('interface', 'zh'): '接口',
            ('package', 'zh'): '包',
            ('association', 'zh'): '关联',
            ('generalization', 'zh'): '泛化'
        }
        
        return translation_dict.get((term.lower(), target_lang.value), term)
    
    def _evaluate_translation_quality(self, original: str, translated: str, source_lang: Language, target_lang: Language) -> float:
        """评估翻译质量"""
        # 简化的质量评估
        if original == translated:
            return 0.5  # 没有翻译
        return 0.8  # 假设翻译质量较好
    
    def _get_or_create_term_id(self, term: str, language: Language) -> str:
        """获取或创建术语ID"""
        # 查找现有术语
        terms = self.search_terms(TermQuery(text=term, language=language))
        if terms:
            return terms[0].term_id
        
        # 创建新术语
        term_id = f"term_{hash(term + language.value)}"
        new_term = Term(
            term_id=term_id,
            text=term,
            language=language,
            domain="general",
            category="unknown",
            definition=""
        )
        
        self.add_term(new_term)
        return term_id
    
    def _save_translation(self, translation: Translation):
        """保存翻译"""
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO translations 
            (translation_id, source_term_id, target_text, target_language,
             confidence, translation_method, verified)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            translation.translation_id, translation.source_term_id,
            translation.target_text, translation.target_language.value,
            translation.confidence, translation.translation_method,
            int(translation.verified)
        ))
        
        self.conn.commit()
    
    def _find_terms_by_concept(self, concept_id: str) -> List[Term]:
        """根据概念ID查找术语"""
        # 简化实现
        return []
    
    def _get_concept_definition(self, concept_id: str) -> str:
        """获取概念定义"""
        return f"概念 {concept_id} 的定义"
    
    def _find_term_id(self, term: str) -> Optional[str]:
        """查找术语ID"""
        terms = self.search_terms(TermQuery(text=term))
        return terms[0].term_id if terms else None
    
    def _extract_terms_from_document(self, document: ET.Element) -> List[str]:
        """从文档中提取术语"""
        terms = []
        
        # 提取元素标签
        for elem in document.iter():
            if elem.tag:
                tag_name = elem.tag.split(':')[-1] if ':' in elem.tag else elem.tag
                terms.append(tag_name)
        
        # 提取属性名
        for elem in document.iter():
            for attr_name in elem.attrib.keys():
                attr_name = attr_name.split(':')[-1] if ':' in attr_name else attr_name
                terms.append(attr_name)
        
        return terms
    
    def _find_standard_term(self, term: str) -> str:
        """查找标准术语"""
        # 查找标准化规则
        for pattern, replacement in self.standardization_rules.items():
            if re.match(pattern, term, re.IGNORECASE):
                return replacement
        
        return term
    
    def _find_replacement_term(self, deprecated_term: str) -> Optional[str]:
        """查找替换术语"""
        # 查找同义词
        synonyms = self.get_related_terms(deprecated_term, [RelationType.SYNONYM])
        
        for synonym_rel in synonyms:
            if synonym_rel.term.status == TermStatus.ACTIVE:
                return synonym_rel.term.text
        
        return None
    
    def _load_domain_vocabularies(self):
        """加载领域词汇表"""
        # 这里可以从文件或外部数据源加载
        self.domain_vocabularies = {
            'uml': ['class', 'interface', 'package', 'association'],
            'sysml': ['block', 'requirement', 'constraint', 'allocation'],
            'bpmn': ['process', 'task', 'gateway', 'event']
        }
    
    def _load_language_patterns(self):
        """加载语言检测模式"""
        self.language_patterns = {
            'en': [r'[a-zA-Z]+'],
            'zh': [r'[\u4e00-\u9fff]+'],
            'ja': [r'[\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff]+'],
            'ko': [r'[\uac00-\ud7af]+']
        }
    
    def _load_standardization_rules(self):
        """加载标准化规则"""
        self.standardization_rules = {
            r'^[Cc]lass$': 'Class',
            r'^[Ii]nterface$': 'Interface',
            r'^[Pp]ackage$': 'Package'
        }

    def extract_terminology_from_elements(self, elements: List[ET.Element]) -> List[Term]:
        """从XML元素列表中提取术语"""
        extracted_terms = []
        processed_terms = set()  # 防止重复
        
        for element in elements:
            try:
                # 1. 从元素标签提取术语
                if element.tag and element.tag not in processed_terms:
                    tag_term = self._create_term_from_tag(element.tag)
                    if tag_term:
                        extracted_terms.append(tag_term)
                        processed_terms.add(element.tag)
                
                # 2. 从元素name属性提取术语
                name = element.get('name', '')
                if name and name not in processed_terms:
                    name_term = self._create_term_from_name(name, element)
                    if name_term:
                        extracted_terms.append(name_term)
                        processed_terms.add(name)
                
                # 3. 从元素属性提取术语
                for attr_name, attr_value in element.attrib.items():
                    if attr_value and len(attr_value) > 2 and attr_value not in processed_terms:
                        # 过滤掉ID、引用等技术性属性
                        if not self._is_technical_attribute(attr_name, attr_value):
                            attr_term = self._create_term_from_attribute(attr_name, attr_value, element)
                            if attr_term:
                                extracted_terms.append(attr_term)
                                processed_terms.add(attr_value)
                
                # 4. 从元素文本内容提取术语
                text_content = element.text
                if text_content and text_content.strip():
                    text_terms = self._extract_terms_from_text(text_content, element)
                    for term in text_terms:
                        if term.text not in processed_terms:
                            extracted_terms.append(term)
                            processed_terms.add(term.text)
                
                # 5. 从stereotype提取术语
                stereotype = element.get('stereotype', '')
                if stereotype and stereotype not in processed_terms:
                    stereotype_term = self._create_term_from_stereotype(stereotype, element)
                    if stereotype_term:
                        extracted_terms.append(stereotype_term)
                        processed_terms.add(stereotype)
                
                # 6. 从xmi:type提取术语
                xmi_type = element.get('xmi:type', '')
                if xmi_type and xmi_type not in processed_terms:
                    type_term = self._create_term_from_type(xmi_type, element)
                    if type_term:
                        extracted_terms.append(type_term)
                        processed_terms.add(xmi_type)
                        
            except Exception as e:
                logger.warning(f"术语提取失败 {element.tag}: {e}")
                continue
        
        # 按置信度排序
        extracted_terms.sort(key=lambda x: x.confidence, reverse=True)
        
        logger.info(f"从 {len(elements)} 个元素中提取了 {len(extracted_terms)} 个术语")
        return extracted_terms
    
    def _create_term_from_tag(self, tag: str) -> Optional[Term]:
        """从元素标签创建术语"""
        # 清理命名空间
        clean_tag = tag.split(':')[-1] if ':' in tag else tag
        clean_tag = clean_tag.split('}')[-1] if '}' in clean_tag else clean_tag
        
        if len(clean_tag) < 2:
            return None
        
        # 识别领域
        domain = self._classify_term_domain(clean_tag)
        
        return Term(
            term_id=f"tag_{clean_tag}_{int(datetime.now().timestamp())}",
            text=clean_tag,
            language=Language.EN,
            domain=domain,
            category="element_tag",
            definition=f"XML元素类型: {clean_tag}",
            confidence=0.8,
            source='element_tag',
            metadata={
                'original_tag': tag,
                'usage_frequency': 1
            }
        )
    
    def _create_term_from_name(self, name: str, element: ET.Element) -> Optional[Term]:
        """从元素名称创建术语"""
        if len(name) < 2:
            return None
        
        # 检测语言
        language = self._detect_language(name)
        domain = self._classify_term_domain(name, element.tag)
        
        return Term(
            term_id=f"name_{name}_{int(datetime.now().timestamp())}",
            text=name,
            language=Language(language),
            domain=domain,
            category="element_name",
            definition=f"元素名称: {name} (类型: {element.tag})",
            confidence=0.9,
            source='element_name',
            metadata={
                'element_type': element.tag,
                'has_attributes': len(element.attrib) > 0,
                'has_children': len(list(element)) > 0,
                'usage_frequency': 1
            }
        )
    
    def _create_term_from_attribute(self, attr_name: str, attr_value: str, element: ET.Element) -> Optional[Term]:
        """从属性值创建术语"""
        if len(attr_value) < 2 or len(attr_value) > 100:  # 长度限制
            return None
        
        language = self._detect_language(attr_value)
        domain = self._classify_term_domain(attr_value, element.tag)
        
        return Term(
            term_id=f"attr_{attr_name}_{attr_value}_{int(datetime.now().timestamp())}",
            text=attr_value,
            language=Language(language),
            domain=domain,
            category="attribute_value",
            definition=f"属性值: {attr_name}={attr_value}",
            confidence=0.7,
            source='element_attribute',
            metadata={
                'attribute_name': attr_name,
                'element_type': element.tag,
                'usage_frequency': 1
            }
        )
    
    def _extract_terms_from_text(self, text: str, element: ET.Element) -> List[Term]:
        """从文本内容提取术语"""
        terms = []
        
        # 分词处理
        words = text.strip().split()
        significant_words = [word for word in words if len(word) > 2 and word.isalpha()]
        
        for word in significant_words:
            language = self._detect_language(word)
            domain = self._classify_term_domain(word, element.tag)
            
            term = Term(
                term_id=f"text_{word}_{int(datetime.now().timestamp())}",
                text=word,
                language=Language(language),
                domain=domain,
                category="text_content",
                definition=f"文本内容词汇: {word}",
                confidence=0.6,
                source='element_text',
                metadata={
                    'element_type': element.tag,
                    'context': text[:50],  # 保留前50个字符作为上下文
                    'usage_frequency': 1
                }
            )
            terms.append(term)
        
        return terms
    
    def _create_term_from_stereotype(self, stereotype: str, element: ET.Element) -> Optional[Term]:
        """从stereotype创建术语"""
        if len(stereotype) < 2:
            return None
        
        return Term(
            term_id=f"stereotype_{stereotype}_{int(datetime.now().timestamp())}",
            text=stereotype,
            language=Language.EN,
            domain='UML',
            category="stereotype",
            definition=f"UML stereotype: {stereotype}",
            confidence=0.9,
            source='stereotype',
            metadata={
                'element_type': element.tag,
                'is_stereotype': True,
                'usage_frequency': 1
            }
        )
    
    def _create_term_from_type(self, xmi_type: str, element: ET.Element) -> Optional[Term]:
        """从xmi:type创建术语"""
        if len(xmi_type) < 2:
            return None
        
        # 清理类型名称
        clean_type = xmi_type.split(':')[-1] if ':' in xmi_type else xmi_type
        
        return Term(
            term_id=f"type_{clean_type}_{int(datetime.now().timestamp())}",
            text=clean_type,
            language=Language.EN,
            domain=self._classify_term_domain(clean_type),
            category="type_definition",
            definition=f"XMI类型: {clean_type}",
            confidence=0.8,
            source='xmi_type',
            metadata={
                'original_type': xmi_type,
                'element_type': element.tag,
                'usage_frequency': 1
            }
        )
    
    def _is_technical_attribute(self, attr_name: str, attr_value: str) -> bool:
        """判断是否为技术性属性（应该跳过）"""
        technical_attrs = {
            'id', 'xmi:id', 'href', 'ref', 'uuid', 'guid',
            'base_Class', 'base_Element', 'base_Property'
        }
        
        # 属性名是技术性的
        if attr_name.lower() in technical_attrs:
            return True
        
        # 值看起来像ID或UUID
        if len(attr_value) > 20 and (attr_value.startswith('_') or '-' in attr_value):
            return True
        
        # 值是纯数字
        if attr_value.isdigit():
            return True
        
        return False
    
    def _classify_term_domain(self, term: str, context: str = '') -> str:
        """分类术语领域"""
        term_lower = term.lower()
        context_lower = context.lower()
        
        # UML领域
        uml_keywords = [
            'class', 'package', 'interface', 'association', 'generalization',
            'dependency', 'realization', 'aggregation', 'composition',
            'actor', 'usecase', 'sequence', 'activity', 'state', 'object'
        ]
        
        if any(keyword in term_lower for keyword in uml_keywords):
            return 'UML'
        
        # SysML领域
        sysml_keywords = [
            'block', 'requirement', 'parametric', 'constraint', 'port',
            'allocation', 'satisfy', 'verify', 'derive', 'refine'
        ]
        
        if any(keyword in term_lower for keyword in sysml_keywords):
            return 'SysML'
        
        # 业务领域
        business_keywords = [
            'business', 'process', 'service', 'customer', 'product',
            'order', 'account', 'payment', 'workflow', 'rule'
        ]
        
        if any(keyword in term_lower for keyword in business_keywords):
            return 'Business'
        
        # 技术领域
        technical_keywords = [
            'system', 'component', 'module', 'interface', 'service',
            'data', 'database', 'network', 'security', 'performance'
        ]
        
        if any(keyword in term_lower for keyword in technical_keywords):
            return 'Technical'
        
        # 基于上下文推断
        if 'uml' in context_lower:
            return 'UML'
        elif 'sysml' in context_lower:
            return 'SysML'
        
        return 'General'
    
    def _detect_language(self, text: str) -> str:
        """检测文本语言"""
        # 简单的中文检测
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        if chinese_chars > 0:
            return 'zh'
        
        # 检测其他常见语言特征
        if any(char in text for char in 'äöüßáéíóúñ'):
            return 'de'  # 德语
        
        return 'en'  # 默认英语

# 工厂函数
def create_terminology_db(config: Dict[str, Any] = None) -> TerminologyDB:
    """创建术语数据库实例
    
    Args:
        config: 配置参数
        
    Returns:
        TerminologyDB实例
    """
    if config is None:
        config = {}
    
    logger.info("创建术语数据库实例")
    return TerminologyDB(config)

# 预设配置
DEFAULT_CONFIG = {
    'db_path': 'terminology.db',
    'cache_size': 1000,
    'similarity_threshold': 0.8
} 