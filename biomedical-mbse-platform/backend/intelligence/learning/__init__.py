"""
智能学习优化模块

提供机器学习和特征提取功能：
- 多维特征提取器
- 模型训练器
- 预测模型
- 自适应优化器
"""

from .feature_extractor import FeatureExtractor, FeatureSet, Feature, FeatureType
from .model_trainer import (
    ModelTrainer,
    TrainingConfig,
    TrainingData,
    TrainingResult,
    BaseModel,
    RandomForestModel,
    SVMModel,
    ModelType,
    TrainingMode,
    create_model_trainer,
    create_training_config
)
from .prediction_model import PredictionModel, create_prediction_model
from .adaptive_optimizer import AdaptiveOptimizer, create_adaptive_optimizer

__all__ = [
    'FeatureExtractor',
    'FeatureSet', 
    'Feature',
    'FeatureType',
    'ModelTrainer',
    'TrainingConfig',
    'TrainingData',
    'TrainingResult',
    'BaseModel',
    'RandomForestModel',
    'SVMModel',
    'ModelType',
    'TrainingMode',
    'create_model_trainer',
    'create_training_config',
    'PredictionModel',
    'create_prediction_model',
    'AdaptiveOptimizer',
    'create_adaptive_optimizer'
]

__version__ = '1.0.0' 