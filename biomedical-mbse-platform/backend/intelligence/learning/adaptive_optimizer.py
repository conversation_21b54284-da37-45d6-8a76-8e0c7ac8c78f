"""
自适应优化器 (Adaptive Optimizer)
系统性能自动优化

主要功能：
- 基于运行时数据的参数自调优
- 机器学习驱动的优化策略
- 多目标优化和帕累托前沿
- A/B测试和实验设计
"""

import numpy as np
from typing import Dict, List, Any, Optional, Callable, Tuple
import logging
from dataclasses import dataclass, field
from enum import Enum
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern, RBF, ConstantKernel as C
from sklearn.ensemble import RandomForestRegressor
from scipy.optimize import minimize, differential_evolution
import time
import json
from datetime import datetime
import statistics
from sklearn.preprocessing import StandardScaler
import warnings
import random

# 配置日志
logger = logging.getLogger(__name__)

class OptimizationType(Enum):
    """优化类型"""
    SINGLE_OBJECTIVE = "single_objective"
    MULTI_OBJECTIVE = "multi_objective"
    CONSTRAINED = "constrained"
    BAYESIAN = "bayesian"

class ExperimentStatus(Enum):
    """实验状态"""
    PLANNED = "planned"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ObjectiveFunction:
    """目标函数"""
    name: str
    function: Callable[[Dict[str, Any]], float]
    minimize: bool = True
    weight: float = 1.0
    constraints: List[Callable] = field(default_factory=list)

@dataclass
class SearchSpace:
    """搜索空间"""
    parameter_ranges: Dict[str, Tuple[float, float]]
    parameter_types: Dict[str, str]  # 'continuous', 'discrete', 'categorical'
    constraints: List[Callable] = field(default_factory=list)

@dataclass
class OptimizationResult:
    """优化结果"""
    best_parameters: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]]
    convergence_info: Dict[str, Any]
    total_evaluations: int
    computation_time: float

@dataclass
class ParetoFront:
    """帕累托前沿"""
    solutions: List[Dict[str, Any]]
    objectives: List[List[float]]
    dominated_solutions: List[Dict[str, Any]]
    hypervolume: float

@dataclass
class BayesianResult:
    """贝叶斯优化结果"""
    best_parameters: Dict[str, Any]
    best_score: float
    acquisition_history: List[float]
    gp_model: Any
    uncertainty_estimates: List[float]

@dataclass
class OptimizationFeedback:
    """优化反馈"""
    parameter_set: Dict[str, Any]
    actual_performance: float
    expected_performance: float
    feedback_quality: float
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    latency: float
    throughput: float
    error_rate: float
    resource_utilization: float
    custom_metrics: Dict[str, float] = field(default_factory=dict)

@dataclass
class MLModel:
    """机器学习模型"""
    model_type: str
    parameters: Dict[str, Any]
    hyperparameters: Dict[str, Any]
    performance_metrics: Dict[str, float]

@dataclass
class HyperparameterConfig:
    """超参数配置"""
    optimized_parameters: Dict[str, Any]
    cross_validation_score: float
    optimization_method: str
    search_iterations: int

@dataclass
class Hypothesis:
    """假设"""
    hypothesis_id: str
    description: str
    expected_outcome: str
    success_criteria: List[str]
    variables: Dict[str, Any]

@dataclass
class ExperimentDesign:
    """实验设计"""
    experiment_id: str
    hypothesis: Hypothesis
    variants: List[Dict[str, Any]]
    sample_size: int
    duration: str
    success_metrics: List[str]
    statistical_power: float

@dataclass
class Variant:
    """实验变体"""
    variant_id: str
    name: str
    parameters: Dict[str, Any]
    allocation_percentage: float
    description: str

@dataclass
class ABTestResult:
    """A/B测试结果"""
    experiment_id: str
    results_by_variant: Dict[str, Dict[str, float]]
    statistical_significance: Dict[str, float]
    confidence_intervals: Dict[str, Tuple[float, float]]
    recommendations: List[str]

@dataclass
class ExperimentResults:
    """实验结果"""
    experiment_id: str
    hypothesis_validated: bool
    observed_effects: Dict[str, float]
    statistical_tests: Dict[str, Any]
    raw_data: List[Dict[str, Any]]

@dataclass
class StatisticalAnalysis:
    """统计分析"""
    p_values: Dict[str, float]
    effect_sizes: Dict[str, float]
    confidence_intervals: Dict[str, Tuple[float, float]]
    power_analysis: Dict[str, float]
    recommendations: List[str]

class BayesianOptimizer:
    """贝叶斯优化器"""
    
    def __init__(self, search_space: SearchSpace, n_initial_points: int = 5):
        self.search_space = search_space
        self.n_initial_points = n_initial_points
        self.X_sample = []
        self.y_sample = []
        self.gp = GaussianProcessRegressor(
            kernel=Matern(length_scale=1.0, nu=2.5),
            alpha=1e-6,
            normalize_y=True,
            n_restarts_optimizer=5,
            random_state=42
        )
        
    def optimize(self, objective_function: ObjectiveFunction, 
                n_iterations: int = 50) -> BayesianResult:
        """贝叶斯优化"""
        try:
            # 初始化采样
            self._initialize_samples(objective_function, self.n_initial_points)
            
            acquisition_history = []
            
            # 优化循环
            for i in range(n_iterations):
                # 拟合高斯过程
                self.gp.fit(self.X_sample, self.y_sample)
                
                # 寻找下一个采样点
                next_point = self._acquire_next_point()
                
                # 评估目标函数
                next_value = objective_function.function(
                    self._array_to_params(next_point)
                )
                
                # 更新样本
                self.X_sample.append(next_point)
                self.y_sample.append(next_value)
                
                # 记录采集函数值
                acquisition_value = self._expected_improvement(next_point)
                acquisition_history.append(acquisition_value)
                
                logger.info(f"贝叶斯优化第{i+1}轮: {next_value}")
            
            # 找到最佳结果
            best_idx = np.argmin(self.y_sample) if objective_function.minimize else np.argmax(self.y_sample)
            best_params = self._array_to_params(self.X_sample[best_idx])
            best_score = self.y_sample[best_idx]
            
            # 计算不确定性估计
            uncertainty_estimates = self._calculate_uncertainty_estimates()
            
            return BayesianResult(
                best_parameters=best_params,
                best_score=best_score,
                acquisition_history=acquisition_history,
                gp_model=self.gp,
                uncertainty_estimates=uncertainty_estimates
            )
            
        except Exception as e:
            logger.error(f"贝叶斯优化失败: {e}")
            return BayesianResult({}, float('inf'), [], None, [])
    
    def _initialize_samples(self, objective_function: ObjectiveFunction, n_points: int):
        """初始化采样点"""
        for _ in range(n_points):
            # 随机采样
            sample_point = []
            for param_name, (min_val, max_val) in self.search_space.parameter_ranges.items():
                sample_point.append(np.random.uniform(min_val, max_val))
            
            # 评估目标函数
            params = self._array_to_params(sample_point)
            value = objective_function.function(params)
            
            self.X_sample.append(sample_point)
            self.y_sample.append(value)
    
    def _acquire_next_point(self) -> List[float]:
        """获取下一个采样点"""
        # 使用期望改善作为采集函数
        def acquisition_function(x):
            return -self._expected_improvement(x)  # 负号因为minimize
        
        # 多次随机初始化优化
        best_x = None
        best_acquisition = float('inf')
        
        for _ in range(10):
            # 随机初始化
            x0 = []
            for param_name, (min_val, max_val) in self.search_space.parameter_ranges.items():
                x0.append(np.random.uniform(min_val, max_val))
            
            # 优化采集函数
            bounds = list(self.search_space.parameter_ranges.values())
            result = minimize(acquisition_function, x0, bounds=bounds, method='L-BFGS-B')
            
            if result.fun < best_acquisition:
                best_acquisition = result.fun
                best_x = result.x
        
        return best_x.tolist()
    
    def _expected_improvement(self, x: List[float]) -> float:
        """期望改善采集函数"""
        if len(self.X_sample) == 0:
            return 0.0
        
        x = np.array(x).reshape(1, -1)
        mu, sigma = self.gp.predict(x, return_std=True)
        
        if sigma[0] == 0:
            return 0.0
        
        # 当前最佳值
        current_best = min(self.y_sample)
        
        # 计算期望改善
        improvement = current_best - mu[0]
        z = improvement / sigma[0]
        
        from scipy.stats import norm
        ei = improvement * norm.cdf(z) + sigma[0] * norm.pdf(z)
        
        return ei
    
    def _array_to_params(self, x: List[float]) -> Dict[str, Any]:
        """将数组转换为参数字典"""
        params = {}
        for i, (param_name, _) in enumerate(self.search_space.parameter_ranges.items()):
            params[param_name] = x[i]
        return params
    
    def _calculate_uncertainty_estimates(self) -> List[float]:
        """计算不确定性估计"""
        if len(self.X_sample) == 0:
            return []
        
        X = np.array(self.X_sample)
        _, sigma = self.gp.predict(X, return_std=True)
        return sigma.tolist()

class MultiObjectiveOptimizer:
    """多目标优化器"""
    
    def __init__(self):
        self.pareto_solutions = []
        
    def optimize(self, objectives: List[ObjectiveFunction], 
                search_space: SearchSpace,
                population_size: int = 50,
                generations: int = 100) -> ParetoFront:
        """多目标优化"""
        try:
            # 使用NSGA-II算法的简化版本
            population = self._initialize_population(search_space, population_size)
            
            for generation in range(generations):
                # 评估目标函数
                objectives_values = self._evaluate_population(population, objectives)
                
                # 非支配排序
                fronts = self._non_dominated_sorting(objectives_values)
                
                # 选择下一代
                population = self._select_next_generation(population, fronts, objectives_values)
                
                logger.info(f"多目标优化第{generation+1}代完成")
            
            # 提取帕累托前沿
            final_objectives = self._evaluate_population(population, objectives)
            pareto_front = self._extract_pareto_front(population, final_objectives)
            
            return pareto_front
            
        except Exception as e:
            logger.error(f"多目标优化失败: {e}")
            return ParetoFront([], [], [], 0.0)
    
    def _initialize_population(self, search_space: SearchSpace, size: int) -> List[Dict[str, Any]]:
        """初始化种群"""
        population = []
        for _ in range(size):
            individual = {}
            for param_name, (min_val, max_val) in search_space.parameter_ranges.items():
                individual[param_name] = np.random.uniform(min_val, max_val)
            population.append(individual)
        return population
    
    def _evaluate_population(self, population: List[Dict[str, Any]], 
                           objectives: List[ObjectiveFunction]) -> List[List[float]]:
        """评估种群"""
        results = []
        for individual in population:
            obj_values = []
            for obj in objectives:
                value = obj.function(individual)
                if not obj.minimize:
                    value = -value  # 转换为最小化问题
                obj_values.append(value)
            results.append(obj_values)
        return results
    
    def _non_dominated_sorting(self, objectives_values: List[List[float]]) -> List[List[int]]:
        """非支配排序"""
        n = len(objectives_values)
        domination_count = [0] * n
        dominated_solutions = [[] for _ in range(n)]
        fronts = [[]]
        
        # 计算支配关系
        for i in range(n):
            for j in range(n):
                if i != j:
                    if self._dominates(objectives_values[i], objectives_values[j]):
                        dominated_solutions[i].append(j)
                    elif self._dominates(objectives_values[j], objectives_values[i]):
                        domination_count[i] += 1
            
            if domination_count[i] == 0:
                fronts[0].append(i)
        
        # 构建其他前沿
        current_front = 0
        while len(fronts[current_front]) > 0:
            next_front = []
            for i in fronts[current_front]:
                for j in dominated_solutions[i]:
                    domination_count[j] -= 1
                    if domination_count[j] == 0:
                        next_front.append(j)
            
            if next_front:
                fronts.append(next_front)
            current_front += 1
        
        return fronts[:-1]  # 移除空的最后一层
    
    def _dominates(self, solution1: List[float], solution2: List[float]) -> bool:
        """判断solution1是否支配solution2"""
        better_in_at_least_one = False
        for val1, val2 in zip(solution1, solution2):
            if val1 > val2:
                return False
            elif val1 < val2:
                better_in_at_least_one = True
        return better_in_at_least_one
    
    def _select_next_generation(self, population: List[Dict[str, Any]], 
                              fronts: List[List[int]], 
                              objectives_values: List[List[float]]) -> List[Dict[str, Any]]:
        """选择下一代"""
        next_generation = []
        
        for front in fronts:
            if len(next_generation) + len(front) <= len(population):
                for idx in front:
                    next_generation.append(population[idx])
            else:
                # 需要从当前前沿中选择一部分
                remaining_slots = len(population) - len(next_generation)
                selected_indices = self._crowding_distance_selection(front, objectives_values, remaining_slots)
                for idx in selected_indices:
                    next_generation.append(population[idx])
                break
        
        return next_generation
    
    def _crowding_distance_selection(self, front: List[int], 
                                   objectives_values: List[List[float]], 
                                   n_select: int) -> List[int]:
        """基于拥挤距离的选择"""
        if n_select >= len(front):
            return front
        
        # 计算拥挤距离
        distances = [0.0] * len(front)
        n_objectives = len(objectives_values[0])
        
        for obj_idx in range(n_objectives):
            # 按当前目标排序
            sorted_indices = sorted(range(len(front)), 
                                  key=lambda i: objectives_values[front[i]][obj_idx])
            
            # 边界点设置无穷大距离
            distances[sorted_indices[0]] = float('inf')
            distances[sorted_indices[-1]] = float('inf')
            
            # 计算中间点的距离
            obj_range = (objectives_values[front[sorted_indices[-1]]][obj_idx] - 
                        objectives_values[front[sorted_indices[0]]][obj_idx])
            
            if obj_range > 0:
                for i in range(1, len(sorted_indices) - 1):
                    distances[sorted_indices[i]] += (
                        objectives_values[front[sorted_indices[i+1]]][obj_idx] - 
                        objectives_values[front[sorted_indices[i-1]]][obj_idx]
                    ) / obj_range
        
        # 选择距离最大的n_select个
        selected_indices = sorted(range(len(front)), key=lambda i: distances[i], reverse=True)[:n_select]
        return [front[i] for i in selected_indices]
    
    def _extract_pareto_front(self, population: List[Dict[str, Any]], 
                            objectives_values: List[List[float]]) -> ParetoFront:
        """提取帕累托前沿"""
        fronts = self._non_dominated_sorting(objectives_values)
        
        if not fronts:
            return ParetoFront([], [], [], 0.0)
        
        pareto_solutions = [population[i] for i in fronts[0]]
        pareto_objectives = [objectives_values[i] for i in fronts[0]]
        
        # 计算支配解
        dominated_solutions = []
        for i, solution in enumerate(population):
            if i not in fronts[0]:
                dominated_solutions.append(solution)
        
        # 计算超体积（简化版）
        hypervolume = self._calculate_hypervolume(pareto_objectives)
        
        return ParetoFront(
            solutions=pareto_solutions,
            objectives=pareto_objectives,
            dominated_solutions=dominated_solutions,
            hypervolume=hypervolume
        )
    
    def _calculate_hypervolume(self, objectives: List[List[float]]) -> float:
        """计算超体积（简化实现）"""
        if not objectives:
            return 0.0
        
        # 这里使用简化的2D超体积计算
        if len(objectives[0]) == 2:
            # 按第一个目标排序
            sorted_objectives = sorted(objectives, key=lambda x: x[0])
            
            hypervolume = 0.0
            for i, (x, y) in enumerate(sorted_objectives):
                if i == 0:
                    width = x
                else:
                    width = x - sorted_objectives[i-1][0]
                hypervolume += width * y
            
            return hypervolume
        
        return 1.0  # 对于高维目标，返回固定值

class AdaptiveOptimizer:
    """自适应优化器主类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化自适应优化器"""
        self.config = config or {}
        
        # 优化器组件
        self.bayesian_optimizer = None
        self.multi_objective_optimizer = MultiObjectiveOptimizer()
        
        # 历史记录
        self.optimization_history = []
        self.feedback_history = []
        self.experiment_history = []
        
        # 学习参数
        self.learning_rate = self.config.get('learning_rate', 0.1)
        self.adaptation_threshold = self.config.get('adaptation_threshold', 0.05)
        
    def optimize_parameters(self, objective: ObjectiveFunction, 
                          search_space: SearchSpace,
                          method: str = 'bayesian') -> OptimizationResult:
        """优化参数"""
        try:
            start_time = time.time()
            
            if method == 'bayesian':
                result = self._bayesian_optimization(objective, search_space)
            elif method == 'differential_evolution':
                result = self._differential_evolution_optimization(objective, search_space)
            else:
                raise ValueError(f"不支持的优化方法: {method}")
            
            computation_time = time.time() - start_time
            
            # 构建优化结果
            optimization_result = OptimizationResult(
                best_parameters=result['best_parameters'],
                best_score=result['best_score'],
                optimization_history=result.get('history', []),
                convergence_info=result.get('convergence', {}),
                total_evaluations=result.get('evaluations', 0),
                computation_time=computation_time
            )
            
            # 记录历史
            self.optimization_history.append({
                'timestamp': datetime.now().isoformat(),
                'method': method,
                'result': optimization_result
            })
            
            logger.info(f"参数优化完成，耗时{computation_time:.2f}秒")
            return optimization_result
            
        except Exception as e:
            logger.error(f"参数优化失败: {e}")
            return OptimizationResult({}, float('inf'), [], {}, 0, 0.0)
    
    def multi_objective_optimization(self, objectives: List[ObjectiveFunction],
                                   search_space: SearchSpace) -> ParetoFront:
        """多目标优化"""
        try:
            pareto_front = self.multi_objective_optimizer.optimize(objectives, search_space)
            
            logger.info(f"多目标优化完成，找到{len(pareto_front.solutions)}个帕累托解")
            return pareto_front
            
        except Exception as e:
            logger.error(f"多目标优化失败: {e}")
            return ParetoFront([], [], [], 0.0)
    
    def bayesian_optimization(self, search_space: SearchSpace, 
                             n_iterations: int = 10,
                             acquisition_function: str = 'expected_improvement') -> BayesianResult:
        """贝叶斯优化"""
        try:
            # 忽略收敛警告
            warnings.filterwarnings('ignore', category=UserWarning, module='sklearn')
            
            # 初始化数据存储
            X_sample = []
            y_sample = []
            
            # 生成初始样本点
            n_initial = min(5, n_iterations // 2)
            for _ in range(n_initial):
                params = self._sample_from_space(search_space)
                X_sample.append(list(params.values()))
                y = self.objective_function.function(params)
                y_sample.append(y)
            
            # 数据标准化
            scaler_x = StandardScaler()
            scaler_y = StandardScaler()
            
            X_scaled = scaler_x.fit_transform(X_sample)
            y_scaled = scaler_y.fit_transform([[y] for y in y_sample])
            y_scaled = y_scaled.flatten()
            
            # 配置高斯过程
            kernel = C(1.0, (1e-3, 1e3)) * RBF(1.0, (1e-2, 1e2))
            gp = GaussianProcessRegressor(
                kernel=kernel,
                alpha=1e-6,
                normalize_y=True,
                n_restarts_optimizer=2,  # 减少重启次数
                optimizer='fmin_l_bfgs_b'
            )
            
            # 迭代优化
            for i in range(n_initial, n_iterations):
                try:
                    # 拟合高斯过程
                    gp.fit(X_scaled, y_scaled)
                    
                    # 生成候选点
                    candidates = []
                    for _ in range(100):  # 减少候选点数量
                        candidate = self._sample_from_space(search_space)
                        candidates.append(list(candidate.values()))
                    
                    # 标准化候选点
                    candidates_scaled = scaler_x.transform(candidates)
                    
                    # 选择最优候选点
                    best_candidate_idx = self._select_best_candidate(
                        gp, candidates_scaled, acquisition_function
                    )
                    
                    # 评估选定的点
                    best_candidate = candidates[best_candidate_idx]
                    param_dict = dict(zip(search_space.parameter_ranges.keys(), best_candidate))
                    y_new = self.objective_function.function(param_dict)
                    
                    # 添加到样本集
                    X_sample.append(best_candidate)
                    y_sample.append(y_new)
                    
                    # 重新标准化
                    X_scaled = scaler_x.fit_transform(X_sample)
                    y_scaled = scaler_y.fit_transform([[y] for y in y_sample])
                    y_scaled = y_scaled.flatten()
                    
                except Exception as e:
                    logger.warning(f"贝叶斯优化第{i}次迭代失败: {e}")
                    # 使用随机采样作为备选
                    params = self._sample_from_space(search_space)
                    X_sample.append(list(params.values()))
                    y = self.objective_function.function(params)
                    y_sample.append(y)
                    continue
            
            # 找到最佳结果
            best_idx = np.argmin(y_sample) if self.objective_function.minimize else np.argmax(y_sample)
            best_params = dict(zip(search_space.parameter_ranges.keys(), X_sample[best_idx]))
            best_value = y_sample[best_idx]
            
            return BayesianResult(
                best_parameters=best_params,
                best_score=best_value,
                acquisition_history=y_sample,
                gp_model=gp,
                uncertainty_estimates=self._calculate_uncertainty_estimates(gp)
            )
            
        except Exception as e:
            logger.error(f"贝叶斯优化失败: {e}")
            # 返回随机优化的结果
            return self._fallback_random_optimization(search_space, n_iterations)
    
    def learn_from_feedback(self, feedback: OptimizationFeedback):
        """从反馈中学习"""
        try:
            # 记录反馈
            self.feedback_history.append({
                'timestamp': datetime.now().isoformat(),
                'feedback': feedback
            })
            
            # 分析反馈质量
            feedback_quality = self._analyze_feedback_quality(feedback)
            
            # 如果反馈质量高，调整优化策略
            if feedback_quality > 0.7:
                self._adapt_optimization_strategy(feedback)
            
            logger.info(f"已学习反馈，质量分数: {feedback_quality:.3f}")
            
        except Exception as e:
            logger.error(f"反馈学习失败: {e}")
    
    def adapt_optimization_strategy(self, performance_metrics: PerformanceMetrics):
        """适应优化策略"""
        try:
            # 分析性能指标
            performance_score = self._calculate_performance_score(performance_metrics)
            
            # 如果性能下降，调整策略
            if performance_score < 0.7:
                self._adjust_search_strategy()
                self._update_objective_weights()
            
            logger.info(f"优化策略已适应，性能分数: {performance_score:.3f}")
            
        except Exception as e:
            logger.error(f"策略适应失败: {e}")
    
    def auto_tune_hyperparameters(self, model: MLModel) -> HyperparameterConfig:
        """自动调优超参数"""
        try:
            # 定义超参数搜索空间
            search_space = self._define_hyperparameter_space(model)
            
            # 定义优化目标
            def objective(params):
                # 这里应该训练模型并返回验证分数
                return self._evaluate_hyperparameters(model, params)
            
            objective_func = ObjectiveFunction(
                name="cross_validation_score",
                function=objective,
                minimize=False  # 最大化分数
            )
            
            # 执行优化
            result = self.optimize_parameters(objective_func, search_space, method='bayesian')
            
            config = HyperparameterConfig(
                optimized_parameters=result.best_parameters,
                cross_validation_score=result.best_score,
                optimization_method='bayesian',
                search_iterations=result.total_evaluations
            )
            
            logger.info(f"超参数调优完成，CV分数: {result.best_score:.3f}")
            return config
            
        except Exception as e:
            logger.error(f"超参数调优失败: {e}")
            return HyperparameterConfig({}, 0.0, "", 0)
    
    def design_experiment(self, hypothesis: Hypothesis) -> ExperimentDesign:
        """设计实验"""
        try:
            # 生成实验变体
            variants = self._generate_experiment_variants(hypothesis)
            
            # 计算样本大小
            sample_size = self._calculate_sample_size(hypothesis)
            
            # 估算实验时长
            duration = self._estimate_experiment_duration(sample_size, variants)
            
            # 计算统计功效
            statistical_power = self._calculate_statistical_power(sample_size)
            
            design = ExperimentDesign(
                experiment_id=f"exp_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                hypothesis=hypothesis,
                variants=variants,
                sample_size=sample_size,
                duration=duration,
                success_metrics=hypothesis.success_criteria,
                statistical_power=statistical_power
            )
            
            logger.info(f"实验设计完成: {design.experiment_id}")
            return design
            
        except Exception as e:
            logger.error(f"实验设计失败: {e}")
            return ExperimentDesign("", hypothesis, [], 0, "", [], 0.0)
    
    def run_ab_test(self, variants: List[Variant], duration_days: int = 7) -> ABTestResult:
        """运行A/B测试"""
        try:
            # 模拟A/B测试执行
            # 在实际应用中，这里会启动真实的A/B测试
            
            results_by_variant = {}
            statistical_significance = {}
            confidence_intervals = {}
            
            for variant in variants:
                # 模拟结果数据
                conversion_rate = np.random.normal(0.1, 0.02)
                results_by_variant[variant.variant_id] = {
                    'conversion_rate': conversion_rate,
                    'sample_size': 1000,
                    'confidence_interval': (conversion_rate - 0.01, conversion_rate + 0.01)
                }
                
                # 计算统计显著性
                statistical_significance[variant.variant_id] = np.random.uniform(0.01, 0.1)
                confidence_intervals[variant.variant_id] = (conversion_rate - 0.01, conversion_rate + 0.01)
            
            # 生成建议
            recommendations = self._generate_ab_test_recommendations(results_by_variant)
            
            result = ABTestResult(
                experiment_id=f"ab_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                results_by_variant=results_by_variant,
                statistical_significance=statistical_significance,
                confidence_intervals=confidence_intervals,
                recommendations=recommendations
            )
            
            logger.info(f"A/B测试完成: {result.experiment_id}")
            return result
            
        except Exception as e:
            logger.error(f"A/B测试失败: {e}")
            return ABTestResult("", {}, {}, {}, [])
    
    def analyze_experiment_results(self, results: ExperimentResults) -> StatisticalAnalysis:
        """分析实验结果"""
        try:
            # 计算统计指标
            p_values = self._calculate_p_values(results)
            effect_sizes = self._calculate_effect_sizes(results)
            confidence_intervals = self._calculate_confidence_intervals(results)
            power_analysis = self._perform_power_analysis(results)
            
            # 生成建议
            recommendations = self._generate_statistical_recommendations(
                p_values, effect_sizes, confidence_intervals
            )
            
            analysis = StatisticalAnalysis(
                p_values=p_values,
                effect_sizes=effect_sizes,
                confidence_intervals=confidence_intervals,
                power_analysis=power_analysis,
                recommendations=recommendations
            )
            
            logger.info(f"实验结果分析完成")
            return analysis
            
        except Exception as e:
            logger.error(f"实验结果分析失败: {e}")
            return StatisticalAnalysis({}, {}, {}, {}, [])
    
    # 私有方法
    def _bayesian_optimization(self, objective: ObjectiveFunction, 
                             search_space: SearchSpace) -> Dict[str, Any]:
        """贝叶斯优化实现"""
        optimizer = BayesianOptimizer(search_space)
        result = optimizer.optimize(objective)
        
        return {
            'best_parameters': result.best_parameters,
            'best_score': result.best_score,
            'history': [{'score': score} for score in optimizer.y_sample],
            'evaluations': len(optimizer.y_sample)
        }
    
    def _differential_evolution_optimization(self, objective: ObjectiveFunction,
                                           search_space: SearchSpace) -> Dict[str, Any]:
        """差分进化优化"""
        bounds = list(search_space.parameter_ranges.values())
        
        def objective_wrapper(x):
            params = {}
            for i, (param_name, _) in enumerate(search_space.parameter_ranges.items()):
                params[param_name] = x[i]
            return objective.function(params)
        
        result = differential_evolution(
            objective_wrapper,
            bounds,
            maxiter=100,
            popsize=15,
            seed=42
        )
        
        best_params = {}
        for i, (param_name, _) in enumerate(search_space.parameter_ranges.items()):
            best_params[param_name] = result.x[i]
        
        return {
            'best_parameters': best_params,
            'best_score': result.fun,
            'evaluations': result.nfev,
            'convergence': {'success': result.success, 'message': result.message}
        }
    
    def _analyze_feedback_quality(self, feedback: OptimizationFeedback) -> float:
        """分析反馈质量"""
        # 简化的反馈质量评估
        prediction_error = abs(feedback.actual_performance - feedback.expected_performance)
        max_error = max(abs(feedback.actual_performance), abs(feedback.expected_performance), 1.0)
        
        quality = 1.0 - (prediction_error / max_error)
        return max(0.0, min(1.0, quality))
    
    def _adapt_optimization_strategy(self, feedback: OptimizationFeedback):
        """适应优化策略"""
        # 根据反馈调整学习率
        if feedback.actual_performance > feedback.expected_performance:
            self.learning_rate *= 1.1  # 增加学习率
        else:
            self.learning_rate *= 0.9  # 减少学习率
        
        # 限制学习率范围
        self.learning_rate = max(0.01, min(0.5, self.learning_rate))
    
    def _calculate_performance_score(self, metrics: PerformanceMetrics) -> float:
        """计算性能分数"""
        # 简化的性能分数计算
        latency_score = max(0, 1 - metrics.latency / 1000)  # 1秒为基准
        throughput_score = min(1, metrics.throughput / 100)  # 100为基准
        error_score = max(0, 1 - metrics.error_rate)
        resource_score = max(0, 1 - metrics.resource_utilization)
        
        return (latency_score + throughput_score + error_score + resource_score) / 4
    
    def _adjust_search_strategy(self):
        """调整搜索策略"""
        # 简化的策略调整
        logger.info("调整搜索策略以改善性能")
    
    def _update_objective_weights(self):
        """更新目标权重"""
        # 简化的权重更新
        logger.info("更新目标函数权重")

    def _sample_from_space(self, search_space: SearchSpace) -> Dict[str, float]:
        """从搜索空间采样"""
        sample = {}
        for param, (min_val, max_val) in search_space.parameter_ranges.items():
            param_type = search_space.parameter_types.get(param, 'continuous')
            
            if param_type == 'continuous':
                sample[param] = random.uniform(min_val, max_val)
            elif param_type == 'integer':
                sample[param] = random.randint(int(min_val), int(max_val))
            elif param_type == 'categorical':
                # 假设类别在constraints中定义
                categories = search_space.constraints.get(param, [min_val, max_val])
                sample[param] = random.choice(categories)
                
        return sample
    
    def _select_best_candidate(self, gp, candidates_scaled, acquisition_function):
        """选择最佳候选点"""
        try:
            if acquisition_function == 'expected_improvement':
                # 计算期望改进
                mu, sigma = gp.predict(candidates_scaled, return_std=True)
                best_f = np.min(gp.y_train_) if self.objective_function.minimize else np.max(gp.y_train_)
                
                if self.objective_function.minimize:
                    improvement = best_f - mu
                else:
                    improvement = mu - best_f
                
                # 避免除零
                sigma = np.maximum(sigma, 1e-9)
                
                # 计算期望改进
                from scipy.stats import norm
                z = improvement / sigma
                ei = improvement * norm.cdf(z) + sigma * norm.pdf(z)
                return np.argmax(ei)
            else:
                # 默认使用不确定性采样
                _, sigma = gp.predict(candidates_scaled, return_std=True)
                return np.argmax(sigma)
                
        except Exception as e:
            logger.warning(f"候选点选择失败: {e}")
            return 0  # 返回第一个候选点
    
    def _calculate_uncertainty_estimates(self, gp) -> List[float]:
        """计算不确定性估计"""
        try:
            if hasattr(gp, 'X_train_') and len(gp.X_train_) > 0:
                _, sigma = gp.predict(gp.X_train_, return_std=True)
                return sigma.tolist()
            return []
        except Exception:
            return []
    
    def _fallback_random_optimization(self, search_space: SearchSpace, n_iterations: int) -> BayesianResult:
        """随机优化作为备选方案"""
        try:
            best_params = None
            best_value = float('inf') if self.objective_function.minimize else float('-inf')
            history = []
            
            for _ in range(n_iterations):
                params = self._sample_from_space(search_space)
                value = self.objective_function.function(params)
                history.append(value)
                
                if self.objective_function.minimize:
                    if value < best_value:
                        best_value = value
                        best_params = params
                else:
                    if value > best_value:
                        best_value = value
                        best_params = params
            
            return BayesianResult(
                best_parameters=best_params or {},
                best_value=best_value,
                convergence_history=history,
                n_evaluations=len(history),
                acquisition_function='random'
            )
            
        except Exception as e:
            logger.error(f"随机优化备选方案失败: {e}")
            return BayesianResult(
                best_parameters={},
                best_value=0.0,
                convergence_history=[],
                n_evaluations=0,
                acquisition_function='failed'
            )

# 工厂函数
def create_adaptive_optimizer(config: Dict[str, Any] = None) -> AdaptiveOptimizer:
    """创建自适应优化器实例
    
    Args:
        config: 配置参数
        
    Returns:
        AdaptiveOptimizer实例
    """
    if config is None:
        config = {}
    
    logger.info("创建自适应优化器实例")
    return AdaptiveOptimizer(config)

# 预设配置
DEFAULT_CONFIG = {
    'learning_rate': 0.1,
    'adaptation_threshold': 0.05,
    'max_iterations': 100,
    'population_size': 50
} 