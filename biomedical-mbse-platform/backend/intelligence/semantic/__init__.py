"""
智能语义分析模块

提供完整的语义分析功能：
- 智能语义引擎
- 上下文分析器  
- 机器学习分类器
- 智能语义分析器
"""

from .semantic_engine import IntelligentSemanticEngine, SemanticAnalysisResult
from .context_analyzer import <PERSON><PERSON><PERSON><PERSON>y<PERSON>, ContextResult, ContextType, ContextScope
from .ml_classifier import MLClassifier, ClassificationResult, TrainingData, ModelType
from .intelligent_analyzer import (
    IntelligentAnalyzer, 
    DeepSemanticResult, 
    SemanticConcept, 
    SemanticDepth, 
    ConceptType,
    create_intelligent_analyzer
)
from .domain_classifier import (
    DomainClassifier, 
    DomainType, 
    DomainFeatures,
    DomainClassificationResult,
    create_domain_classifier
)
from .rule_engine import (
    RuleEngine,
    Rule,
    RuleSet,
    Fact,
    RuleType,
    FactType,
    ValidationResult,
    RuleExecutionResult,
    create_rule_engine,
    create_validation_rules,
    create_inference_rules
)

__all__ = [
    'IntelligentSemanticEngine',
    'SemanticAnalysisResult', 
    'ContextAnalyzer',
    'ContextResult',
    'ContextType',
    'ContextScope',
    'MLClassifier',
    'ClassificationResult',
    'TrainingData',
    'ModelType',
    'IntelligentAnalyzer',
    'DeepSemanticResult',
    'SemanticConcept',
    'SemanticDepth',
    'ConceptType',
    'create_intelligent_analyzer',
    'DomainClassifier',
    'DomainType',
    'DomainFeatures',
    'DomainClassificationResult',
    'create_domain_classifier',
    'RuleEngine',
    'Rule',
    'RuleSet',
    'Fact',
    'RuleType',
    'FactType',
    'ValidationResult',
    'RuleExecutionResult',
    'create_rule_engine',
    'create_validation_rules',
    'create_inference_rules'
]

__version__ = '2.0.0' 