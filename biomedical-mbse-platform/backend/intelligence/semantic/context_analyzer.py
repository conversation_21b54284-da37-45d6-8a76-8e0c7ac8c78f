"""
XML元数据系统 - 上下文分析器

实现上下文感知的语义分析：
- 元素上下文环境分析
- 依赖关系上下文
- 作用域上下文分析
- 语义上下文推理
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import xml.etree.ElementTree as ET

logger = logging.getLogger(__name__)

class ContextType(Enum):
    """上下文类型"""
    STRUCTURAL = "structural"     # 结构上下文
    SEMANTIC = "semantic"        # 语义上下文
    FUNCTIONAL = "functional"    # 功能上下文
    HIERARCHICAL = "hierarchical" # 层次上下文
    RELATIONSHIP = "relationship" # 关系上下文
    SCOPE = "scope"             # 作用域上下文

class ContextScope(Enum):
    """上下文作用域"""
    LOCAL = "local"             # 局部上下文 (直接邻居)
    REGIONAL = "regional"       # 区域上下文 (同级元素)
    GLOBAL = "global"           # 全局上下文 (整个模型)
    DOMAIN = "domain"           # 领域上下文 (特定业务域)

@dataclass
class ContextFeature:
    """上下文特征"""
    name: str
    value: float
    confidence: float
    context_type: ContextType
    scope: ContextScope
    source_elements: List[str] = field(default_factory=list)
    description: str = ""

@dataclass 
class ContextResult:
    """上下文分析结果"""
    # 主要上下文特征
    primary_context: Dict[str, float] = field(default_factory=dict)
    
    # 分类上下文特征
    structural_context: Dict[str, float] = field(default_factory=dict)
    semantic_context: Dict[str, float] = field(default_factory=dict)
    functional_context: Dict[str, float] = field(default_factory=dict)
    hierarchical_context: Dict[str, float] = field(default_factory=dict)
    
    # 上下文模式
    context_patterns: List[str] = field(default_factory=list)
    
    # 依赖关系
    dependencies: List[Dict[str, Any]] = field(default_factory=list)
    influences: List[Dict[str, Any]] = field(default_factory=list)
    
    # 作用域信息
    scope_analysis: Dict[str, Any] = field(default_factory=dict)
    
    # 上下文质量
    context_completeness: float = 0.0
    context_consistency: float = 0.0
    analysis_confidence: float = 0.0
    
    # 元数据
    analysis_timestamp: datetime = field(default_factory=datetime.now)
    analyzed_scope: ContextScope = ContextScope.LOCAL

class ContextAnalyzer:
    """上下文分析器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化上下文分析器"""
        self.config = config or {}
        
        # 上下文权重配置
        self.context_weights = {
            ContextType.STRUCTURAL: self.config.get('structural_weight', 0.25),
            ContextType.SEMANTIC: self.config.get('semantic_weight', 0.3),
            ContextType.FUNCTIONAL: self.config.get('functional_weight', 0.2),
            ContextType.HIERARCHICAL: self.config.get('hierarchical_weight', 0.15),
            ContextType.RELATIONSHIP: self.config.get('relationship_weight', 0.1)
        }
        
        # 作用域权重配置
        self.scope_weights = {
            ContextScope.LOCAL: self.config.get('local_weight', 0.4),
            ContextScope.REGIONAL: self.config.get('regional_weight', 0.3),
            ContextScope.GLOBAL: self.config.get('global_weight', 0.2),
            ContextScope.DOMAIN: self.config.get('domain_weight', 0.1)
        }
        
        # 上下文模式库
        self.context_patterns = self._initialize_context_patterns()
        
        # 分析统计
        self.analysis_stats = {
            'total_analyses': 0,
            'pattern_matches': 0,
            'average_completeness': 0.0,
            'average_confidence': 0.0
        }
        
        logger.info("上下文分析器初始化完成")
    
    def _initialize_context_patterns(self) -> Dict[str, Dict[str, Any]]:
        """初始化上下文模式库"""
        return {
            "容器模式": {
                "description": "包含其他元素的容器结构",
                "indicators": ["package", "component", "subsystem"],
                "context_features": ["has_children", "hierarchical_depth"],
                "weight": 0.8
            },
            "叶子模式": {
                "description": "没有子元素的叶子节点",
                "indicators": ["attribute", "parameter", "literal"],
                "context_features": ["no_children", "simple_type"],
                "weight": 0.7
            },
            "连接模式": {
                "description": "连接其他元素的关系",
                "indicators": ["association", "dependency", "connector"],
                "context_features": ["connects_elements", "relationship_type"],
                "weight": 0.75
            },
            "行为模式": {
                "description": "表示系统行为的元素",
                "indicators": ["operation", "activity", "interaction"],
                "context_features": ["behavioral_element", "has_behavior"],
                "weight": 0.8
            },
            "约束模式": {
                "description": "表示约束和限制的元素",
                "indicators": ["constraint", "invariant", "precondition"],
                "context_features": ["constrains_elements", "validation_rule"],
                "weight": 0.6
            }
        }
    
    def analyze_element_context(self, element: ET.Element, 
                               neighbors: List[ET.Element] = None,
                               global_context: Dict[str, Any] = None) -> ContextResult:
        """分析元素的上下文"""
        try:
            # 构建分析上下文
            analysis_context = self._build_analysis_context(element, neighbors, global_context)
            
            # 多层次上下文分析
            structural_context = self._analyze_structural_context(element, analysis_context)
            semantic_context = self._analyze_semantic_context(element, analysis_context)
            functional_context = self._analyze_functional_context(element, analysis_context)
            hierarchical_context = self._analyze_hierarchical_context(element, analysis_context)
            
            # 依赖关系分析
            dependencies = self._analyze_dependencies(element, analysis_context)
            influences = self._analyze_influences(element, analysis_context)
            
            # 上下文模式识别
            patterns = self._identify_context_patterns(element, analysis_context)
            
            # 作用域分析
            scope_analysis = self._analyze_scope(element, analysis_context)
            
            # 整合分析结果
            result = ContextResult(
                structural_context=structural_context,
                semantic_context=semantic_context,
                functional_context=functional_context,
                hierarchical_context=hierarchical_context,
                context_patterns=patterns,
                dependencies=dependencies,
                influences=influences,
                scope_analysis=scope_analysis
            )
            
            # 计算主要上下文和质量指标
            result.primary_context = self._calculate_primary_context(result)
            result.context_completeness = self._assess_completeness(result)
            result.context_consistency = self._assess_consistency(result)
            result.analysis_confidence = self._calculate_confidence(result)
            
            # 更新统计
            self._update_analysis_stats(result)
            
            return result
            
        except Exception as e:
            logger.error(f"上下文分析失败: {e}")
            return self._create_fallback_result(element)
    
    def _build_analysis_context(self, element: ET.Element, 
                               neighbors: List[ET.Element],
                               global_context: Dict[str, Any]) -> Dict[str, Any]:
        """构建分析上下文"""
        context = {
            'element': element,
            'neighbors': neighbors or [],
            'global_context': global_context or {},
            'element_info': {
                'tag': element.tag,
                'attributes': dict(element.attrib),
                'text_content': element.text or "",
                'children_count': len(list(element)),
                'has_parent': element.getparent() is not None
            }
        }
        
        # 添加邻居信息
        if neighbors:
            context['neighbor_info'] = [
                {
                    'tag': neighbor.tag,
                    'attributes': dict(neighbor.attrib),
                    'distance': self._calculate_element_distance(element, neighbor)
                }
                for neighbor in neighbors
            ]
        
        return context
    
    def _analyze_structural_context(self, element: ET.Element, 
                                  context: Dict[str, Any]) -> Dict[str, float]:
        """分析结构上下文"""
        features = {}
        
        # 层次结构特征
        features['depth_level'] = self._calculate_depth_level(element)
        features['children_count'] = len(list(element))
        features['siblings_count'] = self._count_siblings(element)
        features['is_root'] = 1.0 if element.getparent() is None else 0.0
        features['is_leaf'] = 1.0 if len(list(element)) == 0 else 0.0
        
        # 结构复杂性
        features['structural_complexity'] = self._calculate_structural_complexity(element)
        
        # 嵌套程度
        features['nesting_level'] = self._calculate_nesting_level(element)
        
        # 结构模式
        features['container_pattern'] = self._detect_container_pattern(element)
        features['composition_pattern'] = self._detect_composition_pattern(element)
        
        return features
    
    def _analyze_semantic_context(self, element: ET.Element,
                                context: Dict[str, Any]) -> Dict[str, float]:
        """分析语义上下文"""
        features = {}
        
        # 语义相似性
        features['semantic_similarity'] = self._calculate_semantic_similarity(element, context)
        
        # 领域相关性
        features['domain_relevance'] = self._assess_domain_relevance(element)
        
        # 概念一致性
        features['concept_consistency'] = self._assess_concept_consistency(element, context)
        
        # 术语上下文
        features['terminology_context'] = self._analyze_terminology_context(element)
        
        # 语义角色
        features['semantic_role'] = self._identify_semantic_role(element)
        
        return features
    
    def _analyze_functional_context(self, element: ET.Element,
                                  context: Dict[str, Any]) -> Dict[str, float]:
        """分析功能上下文"""
        features = {}
        
        # 功能角色
        features['functional_role'] = self._identify_functional_role(element)
        
        # 输入输出特征
        features['has_inputs'] = self._detect_inputs(element)
        features['has_outputs'] = self._detect_outputs(element)
        features['io_complexity'] = self._calculate_io_complexity(element)
        
        # 处理能力
        features['processing_capability'] = self._assess_processing_capability(element)
        
        # 功能依赖
        features['functional_dependencies'] = self._count_functional_dependencies(element)
        
        return features
    
    def _analyze_hierarchical_context(self, element: ET.Element,
                                    context: Dict[str, Any]) -> Dict[str, float]:
        """分析层次上下文"""
        features = {}
        
        # 层次位置
        features['hierarchy_position'] = self._calculate_hierarchy_position(element)
        
        # 继承关系
        features['inheritance_level'] = self._calculate_inheritance_level(element)
        
        # 抽象程度
        features['abstraction_level'] = self._assess_abstraction_level(element)
        
        # 范围影响
        features['scope_influence'] = self._calculate_scope_influence(element)
        
        return features
    
    def _analyze_dependencies(self, element: ET.Element,
                            context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析依赖关系"""
        dependencies = []
        
        # 分析父子依赖
        parent = element.getparent()
        if parent is not None:
            dependencies.append({
                'type': 'parent_child',
                'target': parent.tag,
                'strength': 0.9,
                'direction': 'depends_on'
            })
        
        # 分析兄弟依赖
        siblings = self._get_siblings(element)
        for sibling in siblings:
            similarity = self._calculate_element_similarity(element, sibling)
            if similarity > 0.5:
                dependencies.append({
                    'type': 'sibling',
                    'target': sibling.tag,
                    'strength': similarity,
                    'direction': 'correlates_with'
                })
        
        # 分析引用依赖
        references = self._find_references(element, context)
        for ref in references:
            dependencies.append({
                'type': 'reference',
                'target': ref['target'],
                'strength': ref['strength'],
                'direction': 'references'
            })
        
        return dependencies
    
    def _analyze_influences(self, element: ET.Element,
                          context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析影响关系"""
        influences = []
        
        # 子元素影响
        for child in element:
            influences.append({
                'type': 'child_influence',
                'target': child.tag,
                'strength': 0.7,
                'direction': 'influences'
            })
        
        # 属性影响
        for attr_name, attr_value in element.attrib.items():
            influences.append({
                'type': 'attribute_influence',
                'target': attr_name,
                'strength': 0.5,
                'direction': 'configures'
            })
        
        return influences
    
    def _identify_context_patterns(self, element: ET.Element,
                                 context: Dict[str, Any]) -> List[str]:
        """识别上下文模式"""
        patterns = []
        
        for pattern_name, pattern_info in self.context_patterns.items():
            match_score = self._calculate_pattern_match(element, pattern_info, context)
            if match_score > 0.6:
                patterns.append(pattern_name)
        
        return patterns
    
    def _analyze_scope(self, element: ET.Element,
                      context: Dict[str, Any]) -> Dict[str, Any]:
        """分析作用域"""
        scope_info = {
            'local_scope': self._analyze_local_scope(element),
            'regional_scope': self._analyze_regional_scope(element, context),
            'global_scope': self._analyze_global_scope(element, context),
            'scope_boundaries': self._identify_scope_boundaries(element),
            'scope_visibility': self._assess_scope_visibility(element)
        }
        
        return scope_info
    
    def _calculate_primary_context(self, result: ContextResult) -> Dict[str, float]:
        """计算主要上下文特征"""
        primary = {}
        
        # 合并所有上下文特征
        all_contexts = [
            result.structural_context,
            result.semantic_context, 
            result.functional_context,
            result.hierarchical_context
        ]
        
        # 加权合并
        for context_dict in all_contexts:
            for key, value in context_dict.items():
                if key not in primary:
                    primary[key] = 0.0
                primary[key] += value
        
        # 归一化
        if primary:
            max_value = max(primary.values())
            if max_value > 0:
                primary = {k: v / max_value for k, v in primary.items()}
        
        return primary
    
    def _assess_completeness(self, result: ContextResult) -> float:
        """评估上下文完整性"""
        total_features = 0
        available_features = 0
        
        contexts = [
            result.structural_context,
            result.semantic_context,
            result.functional_context, 
            result.hierarchical_context
        ]
        
        for context in contexts:
            for value in context.values():
                total_features += 1
                if value > 0:
                    available_features += 1
        
        return available_features / total_features if total_features > 0 else 0.0
    
    def _assess_consistency(self, result: ContextResult) -> float:
        """评估上下文一致性"""
        # 简化一致性评估
        pattern_consistency = len(result.context_patterns) / 5.0  # 假设最多5个模式
        dependency_consistency = min(len(result.dependencies) / 10.0, 1.0)  # 适中的依赖数量
        
        return (pattern_consistency + dependency_consistency) / 2.0
    
    def _calculate_confidence(self, result: ContextResult) -> float:
        """计算分析置信度"""
        completeness_weight = 0.4
        consistency_weight = 0.3
        pattern_weight = 0.3
        
        pattern_confidence = min(len(result.context_patterns) / 3.0, 1.0)
        
        confidence = (
            result.context_completeness * completeness_weight +
            result.context_consistency * consistency_weight +
            pattern_confidence * pattern_weight
        )
        
        return min(confidence, 1.0)
    
    def _update_analysis_stats(self, result: ContextResult):
        """更新分析统计"""
        self.analysis_stats['total_analyses'] += 1
        
        if result.context_patterns:
            self.analysis_stats['pattern_matches'] += 1
        
        # 更新平均值
        total = self.analysis_stats['total_analyses']
        current_avg_completeness = self.analysis_stats['average_completeness']
        current_avg_confidence = self.analysis_stats['average_confidence']
        
        self.analysis_stats['average_completeness'] = (
            (current_avg_completeness * (total - 1) + result.context_completeness) / total
        )
        
        self.analysis_stats['average_confidence'] = (
            (current_avg_confidence * (total - 1) + result.analysis_confidence) / total
        )
    
    def _create_fallback_result(self, element: ET.Element) -> ContextResult:
        """创建后备分析结果"""
        return ContextResult(
            primary_context={'basic_element': 1.0},
            context_completeness=0.3,
            context_consistency=0.3,
            analysis_confidence=0.3
        )
    
    # 辅助方法 (简化实现)
    def _calculate_depth_level(self, element: ET.Element) -> float:
        """计算元素深度级别"""
        depth = 0
        current = element.getparent()
        while current is not None:
            depth += 1
            current = current.getparent()
        return min(depth / 10.0, 1.0)  # 归一化到0-1
    
    def _count_siblings(self, element: ET.Element) -> float:
        """计算兄弟元素数量"""
        parent = element.getparent()
        if parent is None:
            return 0.0
        siblings = len(list(parent)) - 1  # 减去自己
        return min(siblings / 20.0, 1.0)  # 归一化
    
    def _calculate_structural_complexity(self, element: ET.Element) -> float:
        """计算结构复杂性"""
        children_count = len(list(element))
        attr_count = len(element.attrib)
        return min((children_count + attr_count) / 15.0, 1.0)
    
    def _calculate_nesting_level(self, element: ET.Element) -> float:
        """计算嵌套级别"""
        return self._calculate_depth_level(element)
    
    def _detect_container_pattern(self, element: ET.Element) -> float:
        """检测容器模式"""
        return 1.0 if len(list(element)) > 0 else 0.0
    
    def _detect_composition_pattern(self, element: ET.Element) -> float:
        """检测组合模式"""
        return 1.0 if len(list(element)) > 1 else 0.0
    
    def _calculate_semantic_similarity(self, element: ET.Element, context: Dict) -> float:
        """计算语义相似性"""
        # 简化实现
        return 0.7
    
    def _assess_domain_relevance(self, element: ET.Element) -> float:
        """评估领域相关性"""
        # 简化实现
        return 0.6
    
    def _assess_concept_consistency(self, element: ET.Element, context: Dict) -> float:
        """评估概念一致性"""
        # 简化实现
        return 0.8
    
    def _analyze_terminology_context(self, element: ET.Element) -> float:
        """分析术语上下文"""
        # 简化实现
        return 0.7
    
    def _identify_semantic_role(self, element: ET.Element) -> float:
        """识别语义角色"""
        # 简化实现
        return 0.6
    
    def _identify_functional_role(self, element: ET.Element) -> float:
        """识别功能角色"""
        # 简化实现
        return 0.5
    
    def _detect_inputs(self, element: ET.Element) -> float:
        """检测输入"""
        return 1.0 if 'input' in element.tag.lower() else 0.0
    
    def _detect_outputs(self, element: ET.Element) -> float:
        """检测输出"""
        return 1.0 if 'output' in element.tag.lower() else 0.0
    
    def _calculate_io_complexity(self, element: ET.Element) -> float:
        """计算输入输出复杂性"""
        return (self._detect_inputs(element) + self._detect_outputs(element)) / 2.0
    
    def _assess_processing_capability(self, element: ET.Element) -> float:
        """评估处理能力"""
        return 0.5
    
    def _count_functional_dependencies(self, element: ET.Element) -> float:
        """计算功能依赖数量"""
        return min(len(list(element)) / 5.0, 1.0)
    
    def _calculate_hierarchy_position(self, element: ET.Element) -> float:
        """计算层次位置"""
        return self._calculate_depth_level(element)
    
    def _calculate_inheritance_level(self, element: ET.Element) -> float:
        """计算继承级别"""
        return 0.5
    
    def _assess_abstraction_level(self, element: ET.Element) -> float:
        """评估抽象程度"""
        return 0.6
    
    def _calculate_scope_influence(self, element: ET.Element) -> float:
        """计算范围影响"""
        return min(len(list(element)) / 10.0, 1.0)
    
    def _get_siblings(self, element: ET.Element) -> List[ET.Element]:
        """获取兄弟元素"""
        parent = element.getparent()
        if parent is None:
            return []
        return [child for child in parent if child != element]
    
    def _calculate_element_similarity(self, elem1: ET.Element, elem2: ET.Element) -> float:
        """计算元素相似性"""
        if elem1.tag == elem2.tag:
            return 0.8
        return 0.3
    
    def _find_references(self, element: ET.Element, context: Dict) -> List[Dict]:
        """查找引用关系"""
        # 简化实现
        return []
    
    def _calculate_pattern_match(self, element: ET.Element, pattern_info: Dict, context: Dict) -> float:
        """计算模式匹配度"""
        # 简化实现
        indicators = pattern_info.get('indicators', [])
        for indicator in indicators:
            if indicator in element.tag.lower():
                return 0.8
        return 0.2
    
    def _analyze_local_scope(self, element: ET.Element) -> Dict:
        """分析局部作用域"""
        return {'scope_type': 'local', 'elements_count': len(list(element))}
    
    def _analyze_regional_scope(self, element: ET.Element, context: Dict) -> Dict:
        """分析区域作用域"""
        return {'scope_type': 'regional', 'region_size': 5}
    
    def _analyze_global_scope(self, element: ET.Element, context: Dict) -> Dict:
        """分析全局作用域"""
        return {'scope_type': 'global', 'global_visibility': True}
    
    def _identify_scope_boundaries(self, element: ET.Element) -> List[str]:
        """识别作用域边界"""
        return ['package_boundary', 'component_boundary']
    
    def _assess_scope_visibility(self, element: ET.Element) -> float:
        """评估作用域可见性"""
        return 0.7
    
    def _calculate_element_distance(self, elem1: ET.Element, elem2: ET.Element) -> int:
        """计算元素距离"""
        # 简化实现
        return 1
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        return self.analysis_stats.copy()
    
    def batch_analyze_context(self, elements_with_context: List[Tuple[ET.Element, List[ET.Element], Dict]]) -> List[ContextResult]:
        """批量上下文分析"""
        results = []
        for element, neighbors, global_context in elements_with_context:
            result = self.analyze_element_context(element, neighbors, global_context)
            results.append(result)
        return results

# 创建实例的工厂函数
def create_context_analyzer(config: Dict[str, Any] = None) -> ContextAnalyzer:
    """创建上下文分析器实例"""
    return ContextAnalyzer(config)

# 导出主要类
__all__ = [
    'ContextAnalyzer',
    'ContextResult',
    'ContextFeature', 
    'ContextType',
    'ContextScope',
    'create_context_analyzer'
] 