"""
领域分类器 (Domain Classifier)
自动识别XML文档的专业领域，支持多领域混合文档处理

主要功能：
- 自动识别UML、SysML、BPMN、ArchiMate等专业领域
- 基于机器学习的智能分类
- 领域特定术语和模式识别
- 多领域融合文档处理
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
import logging
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import joblib
import re
from collections import defaultdict, Counter
from dataclasses import dataclass
from enum import Enum

# 配置日志
logger = logging.getLogger(__name__)

class DomainType(Enum):
    """支持的领域类型"""
    UML = "uml"
    SYSML = "sysml"
    BPMN = "bpmn"
    ARCHIMATE = "archimate"
    TOGAF = "togaf"
    UNKNOWN = "unknown"
    MIXED = "mixed"

@dataclass
class DomainFeatures:
    """领域特征数据"""
    vocabulary_features: Dict[str, float]
    structural_features: Dict[str, float]
    semantic_features: Dict[str, float]
    element_distribution: Dict[str, int]
    namespace_features: Dict[str, float]
    
@dataclass
class DomainInfo:
    """领域信息"""
    domain_type: DomainType
    confidence: float
    evidence: List[str]
    coverage: float
    
@dataclass
class DomainClassificationResult:
    """领域分类结果"""
    primary_domain: DomainType
    secondary_domains: List[DomainInfo]
    confidence: float
    mixed_domain: bool
    classification_details: Dict[str, Any]

@dataclass
class DomainVocabulary:
    """领域词汇表"""
    domain_type: DomainType
    core_terms: List[str]
    specialized_terms: List[str]
    element_types: List[str]
    attribute_patterns: List[str]

@dataclass
class DomainTrainingExample:
    """领域训练样例"""
    document: ET.Element
    domain_label: DomainType
    confidence: float
    metadata: Dict[str, Any]

@dataclass
class DomainFeedback:
    """领域分类反馈"""
    document_id: str
    predicted_domain: DomainType
    actual_domain: DomainType
    confidence: float
    feedback_type: str

class DomainClassifier:
    """领域分类器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化领域分类器"""
        self.config = config or {}
        self.model = None
        self.vectorizer = None
        self.domain_vocabularies = {}
        self.classification_history = []
        
        # 领域特征权重
        self.feature_weights = {
            'vocabulary': 0.4,
            'structural': 0.3,
            'semantic': 0.2,
            'namespace': 0.1
        }
        
        # 初始化领域词汇表
        self._initialize_domain_vocabularies()
        
        # 初始化分类器
        self._initialize_classifier()
        
    def _initialize_domain_vocabularies(self):
        """初始化各领域的词汇表"""
        
        # UML领域词汇
        self.domain_vocabularies[DomainType.UML] = DomainVocabulary(
            domain_type=DomainType.UML,
            core_terms=[
                "class", "interface", "package", "association", "generalization",
                "realization", "dependency", "abstraction", "usage", "substitution",
                "actor", "usecase", "extend", "include", "stereotype",
                "profile", "metaclass", "property", "operation", "parameter"
            ],
            specialized_terms=[
                "multiplicity", "aggregation", "composition", "navigability",
                "visibility", "abstract", "final", "static", "derived",
                "ordered", "unique", "readonly", "query", "classifier"
            ],
            element_types=[
                "uml:Class", "uml:Interface", "uml:Package", "uml:Association",
                "uml:Generalization", "uml:Realization", "uml:UseCase", "uml:Actor"
            ],
            attribute_patterns=[
                r"xmi:type.*uml:", r"uml:.*", r".*multiplicity.*",
                r".*visibility.*", r".*isAbstract.*"
            ]
        )
        
        # SysML领域词汇
        self.domain_vocabularies[DomainType.SYSML] = DomainVocabulary(
            domain_type=DomainType.SYSML,
            core_terms=[
                "block", "requirement", "parametric", "activity", "sequence",
                "allocation", "verify", "satisfy", "derive", "refine",
                "trace", "containment", "reference", "value", "unit",
                "constraint", "equation", "flowport", "itemflow", "connector"
            ],
            specialized_terms=[
                "viewpoint", "stakeholder", "concern", "rationale", "problem",
                "context", "operational", "logical", "physical", "quantifyKind",
                "dimension", "measurementUnit", "valueType", "bindingConnector"
            ],
            element_types=[
                "sysml:Block", "sysml:Requirement", "sysml:ConstraintBlock",
                "sysml:ValueType", "sysml:FlowPort", "sysml:ItemFlow"
            ],
            attribute_patterns=[
                r"xmi:type.*sysml:", r"sysml:.*", r".*requirement.*",
                r".*block.*", r".*constraint.*"
            ]
        )
        
        # BPMN领域词汇
        self.domain_vocabularies[DomainType.BPMN] = DomainVocabulary(
            domain_type=DomainType.BPMN,
            core_terms=[
                "process", "task", "activity", "gateway", "event", "flow",
                "sequence", "message", "timer", "conditional", "parallel",
                "exclusive", "inclusive", "complex", "subprocess", "call",
                "lane", "pool", "participant", "collaboration", "conversation"
            ],
            specialized_terms=[
                "choreography", "correlation", "dataObject", "dataStore",
                "escalation", "compensation", "signal", "error", "cancel",
                "terminate", "multiple", "instantiate", "adhoc", "loopback"
            ],
            element_types=[
                "bpmn:Process", "bpmn:Task", "bpmn:Gateway", "bpmn:Event",
                "bpmn:SequenceFlow", "bpmn:MessageFlow", "bpmn:Pool"
            ],
            attribute_patterns=[
                r"bpmn:.*", r".*process.*", r".*task.*",
                r".*gateway.*", r".*flow.*"
            ]
        )
        
        # ArchiMate领域词汇
        self.domain_vocabularies[DomainType.ARCHIMATE] = DomainVocabulary(
            domain_type=DomainType.ARCHIMATE,
            core_terms=[
                "businessActor", "businessRole", "businessInterface", "businessProcess",
                "businessFunction", "businessInteraction", "businessEvent", "businessService",
                "applicationComponent", "applicationInterface", "applicationFunction",
                "applicationProcess", "applicationEvent", "applicationService",
                "technologyInterface", "technologyFunction", "technologyProcess",
                "technologyInteraction", "technologyEvent", "technologyService"
            ],
            specialized_terms=[
                "motivation", "strategy", "capability", "resource", "course",
                "stakeholder", "driver", "assessment", "goal", "outcome",
                "principle", "requirement", "constraint", "meaning", "value"
            ],
            element_types=[
                "archimate:BusinessActor", "archimate:BusinessProcess",
                "archimate:ApplicationComponent", "archimate:TechnologyService"
            ],
            attribute_patterns=[
                r"archimate:.*", r".*business.*", r".*application.*",
                r".*technology.*", r".*strategy.*"
            ]
        )
        
    def _initialize_classifier(self):
        """初始化机器学习分类器"""
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            class_weight='balanced'
        )
        
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            ngram_range=(1, 2),
            stop_words='english'
        )
        
    def classify_domain(self, document: ET.Element) -> DomainClassificationResult:
        """分类文档的专业领域"""
        try:
            # 提取特征
            features = self.extract_domain_features(document)
            
            # 计算各领域得分
            domain_scores = self._calculate_domain_scores(features)
            
            # 判断主要领域
            primary_domain = max(domain_scores.items(), key=lambda x: x[1])
            
            # 判断是否为混合领域
            mixed_domain = self._is_mixed_domain(domain_scores)
            
            # 获取次要领域
            secondary_domains = self._get_secondary_domains(domain_scores, primary_domain[0])
            
            # 计算置信度
            confidence = self._calculate_confidence(domain_scores, mixed_domain)
            
            result = DomainClassificationResult(
                primary_domain=primary_domain[0],
                secondary_domains=secondary_domains,
                confidence=confidence,
                mixed_domain=mixed_domain,
                classification_details={
                    'domain_scores': domain_scores,
                    'feature_analysis': self._analyze_features(features),
                    'evidence_summary': self._collect_evidence(document, features)
                }
            )
            
            # 记录分类历史
            self.classification_history.append({
                'timestamp': self._get_timestamp(),
                'result': result,
                'features': features
            })
            
            logger.info(f"领域分类完成: {primary_domain[0].value}, 置信度: {confidence:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"领域分类失败: {e}")
            return DomainClassificationResult(
                primary_domain=DomainType.UNKNOWN,
                secondary_domains=[],
                confidence=0.0,
                mixed_domain=False,
                classification_details={'error': str(e)}
            )
    
    def detect_mixed_domains(self, document: ET.Element) -> List[DomainInfo]:
        """检测混合领域文档"""
        try:
            features = self.extract_domain_features(document)
            domain_scores = self._calculate_domain_scores(features)
            
            mixed_domains = []
            for domain_type, score in domain_scores.items():
                if score > 0.3:  # 阈值可配置
                    evidence = self._collect_domain_evidence(document, domain_type)
                    coverage = self._calculate_domain_coverage(document, domain_type)
                    
                    domain_info = DomainInfo(
                        domain_type=domain_type,
                        confidence=score,
                        evidence=evidence,
                        coverage=coverage
                    )
                    mixed_domains.append(domain_info)
            
            # 按置信度排序
            mixed_domains.sort(key=lambda x: x.confidence, reverse=True)
            
            logger.info(f"检测到 {len(mixed_domains)} 个领域")
            return mixed_domains
            
        except Exception as e:
            logger.error(f"混合领域检测失败: {e}")
            return []
    
    def get_domain_confidence(self, classification: DomainClassificationResult) -> float:
        """获取领域分类的置信度"""
        return classification.confidence
    
    def extract_domain_features(self, document: ET.Element) -> DomainFeatures:
        """提取领域特征"""
        try:
            # 词汇特征
            vocabulary_features = self._extract_vocabulary_features(document)
            
            # 结构特征
            structural_features = self._extract_structural_features(document)
            
            # 语义特征
            semantic_features = self._extract_semantic_features(document)
            
            # 元素分布
            element_distribution = self._extract_element_distribution(document)
            
            # 命名空间特征
            namespace_features = self._extract_namespace_features(document)
            
            return DomainFeatures(
                vocabulary_features=vocabulary_features,
                structural_features=structural_features,
                semantic_features=semantic_features,
                element_distribution=element_distribution,
                namespace_features=namespace_features
            )
            
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return DomainFeatures({}, {}, {}, {}, {})
    
    def build_domain_vocabulary(self, domain: str) -> DomainVocabulary:
        """构建领域词汇表"""
        try:
            domain_type = DomainType(domain.lower())
            return self.domain_vocabularies.get(domain_type, 
                DomainVocabulary(domain_type, [], [], [], []))
        except ValueError:
            logger.warning(f"未知领域类型: {domain}")
            return DomainVocabulary(DomainType.UNKNOWN, [], [], [], [])
    
    def classify_elements_by_domain(self, elements: List[ET.Element]) -> Dict[str, List[ET.Element]]:
        """按领域分类元素"""
        domain_elements = defaultdict(list)
        
        for element in elements:
            # 为每个元素进行领域分类
            element_doc = ET.Element("root")
            element_doc.append(element)
            
            classification = self.classify_domain(element_doc)
            domain_name = classification.primary_domain.value
            domain_elements[domain_name].append(element)
        
        return dict(domain_elements)
    
    def train_domain_model(self, training_data: List[DomainTrainingExample]):
        """训练领域分类模型"""
        try:
            if not training_data:
                logger.warning("没有训练数据")
                return
            
            # 准备训练数据
            X = []
            y = []
            
            for example in training_data:
                features = self.extract_domain_features(example.document)
                feature_vector = self._features_to_vector(features)
                X.append(feature_vector)
                y.append(example.domain_label.value)
            
            X = np.array(X)
            y = np.array(y)
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # 训练模型
            self.model.fit(X_train, y_train)
            
            # 评估模型
            y_pred = self.model.predict(X_test)
            report = classification_report(y_test, y_pred)
            
            logger.info(f"模型训练完成:\n{report}")
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
    
    def update_domain_rules(self, feedback: DomainFeedback):
        """更新领域分类规则"""
        try:
            # 记录反馈
            self.classification_history.append({
                'timestamp': self._get_timestamp(),
                'feedback': feedback,
                'type': 'user_feedback'
            })
            
            # 根据反馈调整分类策略
            if feedback.predicted_domain != feedback.actual_domain:
                logger.info(f"分类错误反馈: {feedback.predicted_domain.value} -> {feedback.actual_domain.value}")
                # 这里可以实现在线学习逻辑
                
        except Exception as e:
            logger.error(f"规则更新失败: {e}")
    
    def export_domain_model(self, domain: str) -> Dict[str, Any]:
        """导出领域模型"""
        try:
            domain_type = DomainType(domain.lower())
            vocabulary = self.domain_vocabularies.get(domain_type)
            
            model_data = {
                'domain_type': domain_type.value,
                'vocabulary': vocabulary.__dict__ if vocabulary else {},
                'model_parameters': self._get_model_parameters(),
                'classification_history': self.classification_history[-100:],  # 最近100条记录
                'feature_weights': self.feature_weights
            }
            
            return model_data
            
        except Exception as e:
            logger.error(f"模型导出失败: {e}")
            return {}
    
    # 私有方法
    def _extract_vocabulary_features(self, document: ET.Element) -> Dict[str, float]:
        """提取词汇特征"""
        text_content = self._extract_text_content(document)
        features = {}
        
        for domain_type, vocab in self.domain_vocabularies.items():
            domain_score = 0.0
            total_terms = len(vocab.core_terms) + len(vocab.specialized_terms)
            
            if total_terms > 0:
                for term in vocab.core_terms:
                    if term.lower() in text_content.lower():
                        domain_score += 2.0  # 核心术语权重更高
                
                for term in vocab.specialized_terms:
                    if term.lower() in text_content.lower():
                        domain_score += 1.0
                
                # 标准化得分
                domain_score = domain_score / total_terms
            
            features[f"vocab_{domain_type.value}"] = domain_score
        
        return features
    
    def _extract_structural_features(self, document: ET.Element) -> Dict[str, float]:
        """提取结构特征"""
        features = {}
        
        # 元素深度分布
        depths = []
        for elem in document.iter():
            depth = self._get_element_depth(elem, document)
            depths.append(depth)
        
        if depths:
            features['avg_depth'] = np.mean(depths)
            features['max_depth'] = max(depths)
            features['depth_variance'] = np.var(depths)
        
        # 元素类型多样性
        element_types = set()
        for elem in document.iter():
            element_types.add(elem.tag)
        
        features['type_diversity'] = len(element_types)
        features['total_elements'] = len(list(document.iter()))
        
        # 属性密度
        total_attrs = sum(len(elem.attrib) for elem in document.iter())
        features['attr_density'] = total_attrs / max(len(list(document.iter())), 1)
        
        return features
    
    def _extract_semantic_features(self, document: ET.Element) -> Dict[str, float]:
        """提取语义特征"""
        features = {}
        
        # 关系类型分析
        relationships = self._extract_relationships(document)
        features['relationship_count'] = len(relationships)
        features['relationship_diversity'] = len(set(relationships))
        
        # 命名模式分析
        naming_patterns = self._analyze_naming_patterns(document)
        features.update(naming_patterns)
        
        # 继承深度分析
        inheritance_depth = self._analyze_inheritance_depth(document)
        features['inheritance_depth'] = inheritance_depth
        
        return features
    
    def _extract_element_distribution(self, document: ET.Element) -> Dict[str, int]:
        """提取元素分布"""
        distribution = Counter()
        
        for elem in document.iter():
            # 标准化元素名称
            tag = elem.tag
            if ':' in tag:
                tag = tag.split(':')[-1]
            distribution[tag] += 1
        
        return dict(distribution)
    
    def _extract_namespace_features(self, document: ET.Element) -> Dict[str, float]:
        """提取命名空间特征"""
        features = {}
        namespaces = set()
        
        for elem in document.iter():
            if ':' in elem.tag:
                namespace = elem.tag.split(':')[0]
                namespaces.add(namespace)
        
        features['namespace_count'] = len(namespaces)
        
        # 分析各领域的命名空间出现频率
        for domain_type in DomainType:
            if domain_type in [DomainType.UNKNOWN, DomainType.MIXED]:
                continue
                
            domain_ns_count = sum(1 for ns in namespaces 
                                if domain_type.value in ns.lower())
            features[f"ns_{domain_type.value}"] = domain_ns_count
        
        return features
    
    def _calculate_domain_scores(self, features: DomainFeatures) -> Dict[DomainType, float]:
        """计算各领域得分"""
        scores = {}
        
        for domain_type in DomainType:
            if domain_type in [DomainType.UNKNOWN, DomainType.MIXED]:
                continue
            
            score = 0.0
            
            # 词汇特征得分
            vocab_key = f"vocab_{domain_type.value}"
            if vocab_key in features.vocabulary_features:
                score += features.vocabulary_features[vocab_key] * self.feature_weights['vocabulary']
            
            # 命名空间特征得分
            ns_key = f"ns_{domain_type.value}"
            if ns_key in features.namespace_features:
                score += features.namespace_features[ns_key] * self.feature_weights['namespace']
            
            # 结构特征得分
            structure_score = self._calculate_structure_score(features.structural_features, domain_type)
            score += structure_score * self.feature_weights['structural']
            
            # 语义特征得分
            semantic_score = self._calculate_semantic_score(features.semantic_features, domain_type)
            score += semantic_score * self.feature_weights['semantic']
            
            scores[domain_type] = score
        
        # 标准化得分
        max_score = max(scores.values()) if scores else 1.0
        if max_score > 0:
            scores = {k: v/max_score for k, v in scores.items()}
        
        return scores
    
    def _is_mixed_domain(self, domain_scores: Dict[DomainType, float], threshold: float = 0.3) -> bool:
        """判断是否为混合领域"""
        high_score_domains = [score for score in domain_scores.values() if score > threshold]
        return len(high_score_domains) > 1
    
    def _get_secondary_domains(self, domain_scores: Dict[DomainType, float], 
                             primary_domain: DomainType) -> List[DomainInfo]:
        """获取次要领域"""
        secondary = []
        
        for domain_type, score in domain_scores.items():
            if domain_type != primary_domain and score > 0.2:  # 阈值可配置
                domain_info = DomainInfo(
                    domain_type=domain_type,
                    confidence=score,
                    evidence=[],  # 这里可以添加具体证据
                    coverage=score  # 简化处理
                )
                secondary.append(domain_info)
        
        # 按置信度排序
        secondary.sort(key=lambda x: x.confidence, reverse=True)
        return secondary
    
    def _calculate_confidence(self, domain_scores: Dict[DomainType, float], 
                            mixed_domain: bool) -> float:
        """计算分类置信度"""
        if not domain_scores:
            return 0.0
        
        scores = list(domain_scores.values())
        scores.sort(reverse=True)
        
        if len(scores) < 2:
            return scores[0] if scores else 0.0
        
        # 计算最高分与第二高分的差距
        confidence = scores[0] - scores[1]
        
        # 混合领域的置信度需要调整
        if mixed_domain:
            confidence *= 0.8
        
        return min(confidence, 1.0)
    
    def _analyze_features(self, features: DomainFeatures) -> Dict[str, Any]:
        """分析特征"""
        return {
            'vocabulary_summary': self._summarize_vocabulary_features(features.vocabulary_features),
            'structural_summary': self._summarize_structural_features(features.structural_features),
            'semantic_summary': self._summarize_semantic_features(features.semantic_features),
            'top_elements': sorted(features.element_distribution.items(), 
                                 key=lambda x: x[1], reverse=True)[:10]
        }
    
    def _collect_evidence(self, document: ET.Element, features: DomainFeatures) -> List[str]:
        """收集分类证据"""
        evidence = []
        
        # 添加词汇证据
        for domain_type, vocab in self.domain_vocabularies.items():
            found_terms = []
            text_content = self._extract_text_content(document)
            
            for term in vocab.core_terms:
                if term.lower() in text_content.lower():
                    found_terms.append(term)
            
            if found_terms:
                evidence.append(f"{domain_type.value}领域术语: {', '.join(found_terms[:5])}")
        
        return evidence
    
    def _extract_text_content(self, element: ET.Element) -> str:
        """提取元素的文本内容"""
        text_parts = []
        
        # 元素名称
        if element.tag:
            text_parts.append(element.tag)
        
        # 属性值
        for key, value in element.attrib.items():
            text_parts.extend([key, value])
        
        # 文本内容
        if element.text:
            text_parts.append(element.text)
        
        # 递归处理子元素
        for child in element:
            text_parts.append(self._extract_text_content(child))
        
        return ' '.join(text_parts)
    
    def _get_element_depth(self, element: ET.Element, root: ET.Element) -> int:
        """获取元素深度"""
        depth = 0
        current = element
        
        # 这是一个简化实现，实际需要更复杂的父节点查找
        for elem in root.iter():
            for child in elem:
                if child == element:
                    return self._calculate_depth_recursive(elem, root, 0)
        
        return depth
    
    def _calculate_depth_recursive(self, element: ET.Element, root: ET.Element, current_depth: int) -> int:
        """递归计算深度"""
        if element == root:
            return current_depth
        
        for elem in root.iter():
            for child in elem:
                if child == element:
                    return current_depth + 1
        
        return current_depth
    
    def _extract_relationships(self, document: ET.Element) -> List[str]:
        """提取关系信息"""
        relationships = []
        
        for elem in document.iter():
            # 查找关系相关的属性和元素
            for attr_name, attr_value in elem.attrib.items():
                if any(rel_word in attr_name.lower() 
                      for rel_word in ['ref', 'id', 'href', 'target', 'source']):
                    relationships.append(f"{attr_name}:{attr_value}")
        
        return relationships
    
    def _analyze_naming_patterns(self, document: ET.Element) -> Dict[str, float]:
        """分析命名模式"""
        patterns = {
            'camelCase': 0,
            'PascalCase': 0,
            'snake_case': 0,
            'kebab-case': 0
        }
        
        total_names = 0
        
        for elem in document.iter():
            names = [elem.tag]
            names.extend(elem.attrib.keys())
            names.extend(elem.attrib.values())
            
            for name in names:
                if isinstance(name, str) and name:
                    total_names += 1
                    
                    if re.match(r'^[a-z][a-zA-Z0-9]*$', name):
                        patterns['camelCase'] += 1
                    elif re.match(r'^[A-Z][a-zA-Z0-9]*$', name):
                        patterns['PascalCase'] += 1
                    elif '_' in name:
                        patterns['snake_case'] += 1
                    elif '-' in name:
                        patterns['kebab-case'] += 1
        
        # 标准化
        if total_names > 0:
            patterns = {k: v/total_names for k, v in patterns.items()}
        
        return patterns
    
    def _analyze_inheritance_depth(self, document: ET.Element) -> int:
        """分析继承深度"""
        max_depth = 0
        
        # 简化的继承分析，实际需要根据具体的元模型实现
        for elem in document.iter():
            if 'generalization' in elem.tag.lower() or 'extends' in str(elem.attrib):
                # 这里需要更复杂的继承链分析
                max_depth = max(max_depth, 1)
        
        return max_depth
    
    def _calculate_structure_score(self, structural_features: Dict[str, float], 
                                 domain_type: DomainType) -> float:
        """计算结构特征得分"""
        # 根据不同领域的典型结构特征计算得分
        score = 0.0
        
        if domain_type == DomainType.UML:
            # UML通常有较深的继承层次和复杂的关系
            if 'avg_depth' in structural_features:
                score += min(structural_features['avg_depth'] / 10.0, 1.0)
        elif domain_type == DomainType.BPMN:
            # BPMN通常有相对扁平的结构
            if 'avg_depth' in structural_features:
                score += max(1.0 - structural_features['avg_depth'] / 5.0, 0.0)
        
        return score
    
    def _calculate_semantic_score(self, semantic_features: Dict[str, float], 
                                domain_type: DomainType) -> float:
        """计算语义特征得分"""
        score = 0.0
        
        # 根据关系数量和多样性计算得分
        if 'relationship_count' in semantic_features:
            score += min(semantic_features['relationship_count'] / 20.0, 1.0)
        
        return score
    
    def _collect_domain_evidence(self, document: ET.Element, domain_type: DomainType) -> List[str]:
        """收集特定领域的证据"""
        evidence = []
        
        if domain_type in self.domain_vocabularies:
            vocab = self.domain_vocabularies[domain_type]
            text_content = self._extract_text_content(document)
            
            found_terms = []
            for term in vocab.core_terms:
                if term.lower() in text_content.lower():
                    found_terms.append(term)
            
            if found_terms:
                evidence.append(f"核心术语: {', '.join(found_terms[:3])}")
        
        return evidence
    
    def _calculate_domain_coverage(self, document: ET.Element, domain_type: DomainType) -> float:
        """计算领域覆盖率"""
        if domain_type not in self.domain_vocabularies:
            return 0.0
        
        vocab = self.domain_vocabularies[domain_type]
        text_content = self._extract_text_content(document).lower()
        
        total_terms = len(vocab.core_terms) + len(vocab.specialized_terms)
        found_terms = 0
        
        for term in vocab.core_terms + vocab.specialized_terms:
            if term.lower() in text_content:
                found_terms += 1
        
        return found_terms / max(total_terms, 1)
    
    def _features_to_vector(self, features: DomainFeatures) -> np.ndarray:
        """将特征转换为向量"""
        # 这是一个简化实现，实际需要更复杂的特征向量化
        vector = []
        
        # 添加词汇特征
        vector.extend(features.vocabulary_features.values())
        
        # 添加结构特征
        vector.extend(features.structural_features.values())
        
        # 添加语义特征
        vector.extend(features.semantic_features.values())
        
        # 填充或截断到固定长度
        target_length = 50
        if len(vector) < target_length:
            vector.extend([0.0] * (target_length - len(vector)))
        else:
            vector = vector[:target_length]
        
        return np.array(vector)
    
    def _get_model_parameters(self) -> Dict[str, Any]:
        """获取模型参数"""
        if self.model:
            return {
                'n_estimators': self.model.n_estimators,
                'max_depth': self.model.max_depth,
                'random_state': self.model.random_state
            }
        return {}
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _summarize_vocabulary_features(self, vocab_features: Dict[str, float]) -> Dict[str, float]:
        """总结词汇特征"""
        return {k: round(v, 3) for k, v in vocab_features.items()}
    
    def _summarize_structural_features(self, struct_features: Dict[str, float]) -> Dict[str, float]:
        """总结结构特征"""
        return {k: round(v, 3) for k, v in struct_features.items()}
    
    def _summarize_semantic_features(self, semantic_features: Dict[str, float]) -> Dict[str, float]:
        """总结语义特征"""
        return {k: round(v, 3) for k, v in semantic_features.items()}

# 工厂函数
def create_domain_classifier(config: Dict[str, Any] = None) -> DomainClassifier:
    """创建领域分类器实例"""
    return DomainClassifier(config)

# 预设配置
DEFAULT_CONFIG = {
    'feature_weights': {
        'vocabulary': 0.4,
        'structural': 0.3,
        'semantic': 0.2,
        'namespace': 0.1
    },
    'confidence_threshold': 0.7,
    'mixed_domain_threshold': 0.3
} 