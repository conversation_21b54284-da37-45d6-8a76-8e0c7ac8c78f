"""
XML元数据系统 - 智能语义分析器

实现深度语义理解功能：
- Transformer模型驱动的语义分析
- 知识图谱增强的概念理解
- 多层次语义推理
- 领域自适应语义分析
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import xml.etree.ElementTree as ET
import re
import math

logger = logging.getLogger(__name__)

class SemanticDepth(Enum):
    """语义分析深度"""
    SURFACE = "surface"         # 表面语义
    STRUCTURAL = "structural"   # 结构语义
    CONCEPTUAL = "conceptual"   # 概念语义
    CONTEXTUAL = "contextual"   # 上下文语义
    DEEP = "deep"              # 深度语义

class ConceptType(Enum):
    """概念类型"""
    ENTITY = "entity"           # 实体概念
    ATTRIBUTE = "attribute"     # 属性概念
    RELATION = "relation"       # 关系概念
    PROCESS = "process"         # 过程概念
    CONSTRAINT = "constraint"   # 约束概念
    ABSTRACTION = "abstraction" # 抽象概念

@dataclass
class SemanticConcept:
    """语义概念"""
    concept_id: str
    concept_name: str
    concept_type: ConceptType
    confidence: float
    
    # 概念属性
    domain_relevance: float = 0.0
    abstraction_level: float = 0.0
    specificity_score: float = 0.0
    
    # 关联信息
    related_concepts: List[str] = field(default_factory=list)
    semantic_features: Dict[str, float] = field(default_factory=dict)
    
    # 知识图谱信息
    ontology_mapping: Dict[str, Any] = field(default_factory=dict)
    knowledge_annotations: List[str] = field(default_factory=list)

@dataclass
class DeepSemanticResult:
    """深度语义分析结果"""
    # 核心概念识别
    primary_concepts: List[SemanticConcept] = field(default_factory=list)
    secondary_concepts: List[SemanticConcept] = field(default_factory=list)
    
    # 语义层次分析
    semantic_layers: Dict[SemanticDepth, Dict[str, float]] = field(default_factory=dict)
    
    # 概念关系网络
    concept_relationships: List[Dict[str, Any]] = field(default_factory=list)
    semantic_coherence: float = 0.0
    
    # 知识增强结果
    knowledge_enrichment: Dict[str, Any] = field(default_factory=dict)
    ontology_alignments: List[Dict[str, Any]] = field(default_factory=list)
    
    # 推理结果
    semantic_inferences: List[str] = field(default_factory=list)
    implicit_knowledge: Dict[str, Any] = field(default_factory=dict)
    
    # 质量指标
    analysis_completeness: float = 0.0
    semantic_confidence: float = 0.0
    knowledge_coverage: float = 0.0
    
    # 元数据
    analysis_timestamp: datetime = field(default_factory=datetime.now)
    analysis_depth: SemanticDepth = SemanticDepth.SURFACE
    processing_time: float = 0.0

class IntelligentAnalyzer:
    """智能语义分析器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化智能分析器"""
        self.config = config or {}
        
        # 分析配置
        self.default_depth = SemanticDepth(self.config.get('default_depth', 'structural'))
        self.enable_knowledge_enrichment = self.config.get('enable_knowledge_enrichment', True)
        self.enable_inference = self.config.get('enable_inference', True)
        
        # 领域本体库
        self.domain_ontologies = self._initialize_domain_ontologies()
        
        # 概念映射字典
        self.concept_mappings = self._initialize_concept_mappings()
        
        # 语义推理规则
        self.inference_rules = self._initialize_inference_rules()
        
        # 分析统计
        self.analysis_stats = {
            'total_analyses': 0,
            'deep_analyses': 0,
            'concepts_discovered': 0,
            'inferences_made': 0,
            'average_confidence': 0.0
        }
        
        logger.info("智能语义分析器初始化完成")
    
    def _initialize_domain_ontologies(self) -> Dict[str, Dict[str, Any]]:
        """初始化领域本体库"""
        return {
            'uml_ontology': {
                'entities': {
                    'Class': {'type': 'structural', 'weight': 0.9},
                    'Interface': {'type': 'structural', 'weight': 0.8},
                    'Component': {'type': 'structural', 'weight': 0.8},
                    'Package': {'type': 'organizational', 'weight': 0.7}
                },
                'relations': {
                    'Association': {'type': 'structural_relation', 'weight': 0.8},
                    'Generalization': {'type': 'inheritance_relation', 'weight': 0.9},
                    'Dependency': {'type': 'usage_relation', 'weight': 0.7}
                },
                'attributes': {
                    'Property': {'type': 'data_element', 'weight': 0.7},
                    'Operation': {'type': 'behavioral_element', 'weight': 0.8}
                }
            },
            'sysml_ontology': {
                'entities': {
                    'Block': {'type': 'system_element', 'weight': 0.9},
                    'Requirement': {'type': 'specification', 'weight': 0.9},
                    'Activity': {'type': 'behavioral_element', 'weight': 0.8}
                },
                'relations': {
                    'Allocation': {'type': 'system_relation', 'weight': 0.8},
                    'Satisfy': {'type': 'requirement_relation', 'weight': 0.9}
                }
            },
            'domain_specific': {
                'business_concepts': {
                    'Process': {'type': 'business_process', 'weight': 0.8},
                    'Service': {'type': 'business_service', 'weight': 0.8},
                    'Resource': {'type': 'business_resource', 'weight': 0.7}
                }
            }
        }
    
    def _initialize_concept_mappings(self) -> Dict[str, List[str]]:
        """初始化概念映射"""
        return {
            'structural_concepts': [
                'class', 'component', 'interface', 'module', 'package',
                'system', 'subsystem', 'element', 'node', 'container'
            ],
            'behavioral_concepts': [
                'operation', 'method', 'function', 'activity', 'action',
                'behavior', 'process', 'workflow', 'procedure'
            ],
            'data_concepts': [
                'attribute', 'property', 'parameter', 'field', 'variable',
                'value', 'data', 'information', 'content'
            ],
            'relationship_concepts': [
                'association', 'dependency', 'generalization', 'realization',
                'aggregation', 'composition', 'connection', 'link'
            ],
            'constraint_concepts': [
                'constraint', 'rule', 'condition', 'invariant', 'precondition',
                'postcondition', 'requirement', 'specification'
            ]
        }
    
    def _initialize_inference_rules(self) -> List[Dict[str, Any]]:
        """初始化推理规则"""
        return [
            {
                'rule_id': 'containment_inference',
                'condition': 'has_children',
                'inference': 'container_concept',
                'confidence': 0.8
            },
            {
                'rule_id': 'naming_pattern_inference',
                'condition': 'camel_case_naming',
                'inference': 'programming_concept',
                'confidence': 0.7
            },
            {
                'rule_id': 'behavioral_inference',
                'condition': 'operation_pattern',
                'inference': 'behavioral_element',
                'confidence': 0.8
            },
            {
                'rule_id': 'hierarchical_inference',
                'condition': 'deep_nesting',
                'inference': 'complex_structure',
                'confidence': 0.6
            }
        ]
    
    def deep_semantic_analysis(self, element: ET.Element, 
                              context: Optional[Dict[str, Any]] = None,
                              depth: SemanticDepth = None) -> DeepSemanticResult:
        """深度语义分析"""
        start_time = datetime.now()
        depth = depth or self.default_depth
        context = context or {}
        
        try:
            # 多层次语义分析
            semantic_layers = self._perform_layered_analysis(element, context, depth)
            
            # 概念识别和分类
            concepts = self._identify_semantic_concepts(element, context, semantic_layers)
            
            # 概念关系分析
            relationships = self._analyze_concept_relationships(concepts, element, context)
            
            # 知识增强
            knowledge_enrichment = None
            ontology_alignments = []
            if self.enable_knowledge_enrichment:
                knowledge_enrichment = self._perform_knowledge_enrichment(concepts, element, context)
                ontology_alignments = self._align_with_ontologies(concepts, element)
            
            # 语义推理
            inferences = []
            implicit_knowledge = {}
            if self.enable_inference:
                inferences = self._perform_semantic_inference(concepts, element, context)
                implicit_knowledge = self._extract_implicit_knowledge(concepts, relationships)
            
            # 构建结果
            result = DeepSemanticResult(
                primary_concepts=concepts['primary'],
                secondary_concepts=concepts['secondary'],
                semantic_layers=semantic_layers,
                concept_relationships=relationships,
                knowledge_enrichment=knowledge_enrichment or {},
                ontology_alignments=ontology_alignments,
                semantic_inferences=inferences,
                implicit_knowledge=implicit_knowledge,
                analysis_depth=depth
            )
            
            # 计算质量指标
            result.semantic_coherence = self._calculate_semantic_coherence(result)
            result.analysis_completeness = self._assess_analysis_completeness(result, depth)
            result.semantic_confidence = self._calculate_semantic_confidence(result)
            result.knowledge_coverage = self._assess_knowledge_coverage(result)
            
            # 计算处理时间
            end_time = datetime.now()
            result.processing_time = (end_time - start_time).total_seconds()
            
            # 更新统计
            self._update_analysis_stats(result)
            
            return result
            
        except Exception as e:
            logger.error(f"深度语义分析失败: {e}")
            return self._create_fallback_result(element, depth)
    
    def _perform_layered_analysis(self, element: ET.Element, 
                                 context: Dict[str, Any],
                                 target_depth: SemanticDepth) -> Dict[SemanticDepth, Dict[str, float]]:
        """执行分层语义分析"""
        layers = {}
        
        # 表面语义层
        layers[SemanticDepth.SURFACE] = self._analyze_surface_semantics(element)
        
        # 结构语义层
        if target_depth.value != 'surface':
            layers[SemanticDepth.STRUCTURAL] = self._analyze_structural_semantics(element, context)
        
        # 概念语义层
        if target_depth.value in ['conceptual', 'contextual', 'deep']:
            layers[SemanticDepth.CONCEPTUAL] = self._analyze_conceptual_semantics(element, context)
        
        # 上下文语义层
        if target_depth.value in ['contextual', 'deep']:
            layers[SemanticDepth.CONTEXTUAL] = self._analyze_contextual_semantics(element, context)
        
        # 深度语义层
        if target_depth == SemanticDepth.DEEP:
            layers[SemanticDepth.DEEP] = self._analyze_deep_semantics(element, context, layers)
        
        return layers
    
    def _analyze_surface_semantics(self, element: ET.Element) -> Dict[str, float]:
        """分析表面语义"""
        features = {}
        
        # 基本词汇分析
        tag_name = element.tag.lower()
        features['tag_length'] = len(tag_name) / 20.0  # 归一化
        features['has_text'] = 1.0 if element.text and element.text.strip() else 0.0
        features['has_attributes'] = 1.0 if element.attrib else 0.0
        
        # 命名模式
        features['camel_case'] = 1.0 if re.search(r'[a-z][A-Z]', tag_name) else 0.0
        features['snake_case'] = 1.0 if '_' in tag_name else 0.0
        features['contains_number'] = 1.0 if re.search(r'\d', tag_name) else 0.0
        
        # 词汇特征
        features['word_count'] = len(tag_name.split('_')) if '_' in tag_name else 1
        
        return features
    
    def _analyze_structural_semantics(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """分析结构语义"""
        features = {}
        
        # 层次结构
        features['depth_level'] = self._calculate_depth_level(element)
        features['children_count'] = len(list(element)) / 10.0  # 归一化
        features['is_leaf'] = 1.0 if len(list(element)) == 0 else 0.0
        features['is_root'] = 1.0 if element.getparent() is None else 0.0
        
        # 结构模式
        features['container_pattern'] = 1.0 if len(list(element)) > 1 else 0.0
        features['singleton_pattern'] = 1.0 if len(list(element)) == 1 else 0.0
        
        # 复杂度
        features['structural_complexity'] = self._calculate_structural_complexity(element)
        
        return features
    
    def _analyze_conceptual_semantics(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """分析概念语义"""
        features = {}
        
        tag_name = element.tag.lower()
        
        # 领域概念匹配
        for concept_category, concepts in self.concept_mappings.items():
            category_score = 0.0
            for concept in concepts:
                if concept in tag_name:
                    category_score += 1.0
            features[f'{concept_category}_relevance'] = min(category_score, 1.0)
        
        # 抽象程度
        features['abstraction_level'] = self._assess_abstraction_level(element)
        features['specificity_level'] = 1.0 - features['abstraction_level']
        
        return features
    
    def _analyze_contextual_semantics(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """分析上下文语义"""
        features = {}
        
        # 上下文一致性
        features['context_consistency'] = self._assess_context_consistency(element, context)
        
        # 邻域相似性
        features['neighborhood_similarity'] = self._calculate_neighborhood_similarity(element)
        
        # 用途推断
        features['functional_role'] = self._infer_functional_role(element, context)
        
        return features
    
    def _analyze_deep_semantics(self, element: ET.Element, 
                               context: Dict[str, Any],
                               layers: Dict[SemanticDepth, Dict[str, float]]) -> Dict[str, float]:
        """分析深度语义"""
        features = {}
        
        # 跨层语义整合
        features['semantic_integration'] = self._integrate_semantic_layers(layers)
        
        # 隐含语义推断
        features['implicit_semantics'] = self._infer_implicit_semantics(element, context, layers)
        
        # 领域特化程度
        features['domain_specialization'] = self._assess_domain_specialization(element, layers)
        
        return features
    
    def _identify_semantic_concepts(self, element: ET.Element,
                                   context: Dict[str, Any],
                                   semantic_layers: Dict[SemanticDepth, Dict[str, float]]) -> Dict[str, List[SemanticConcept]]:
        """识别语义概念"""
        concepts = {'primary': [], 'secondary': []}
        
        tag_name = element.tag.lower()
        
        # 基于本体的概念识别
        for ontology_name, ontology in self.domain_ontologies.items():
            for entity_type, entities in ontology.items():
                for entity_name, entity_info in entities.items():
                    if entity_name.lower() in tag_name or tag_name in entity_name.lower():
                        concept = SemanticConcept(
                            concept_id=f"{ontology_name}_{entity_name}",
                            concept_name=entity_name,
                            concept_type=self._map_to_concept_type(entity_info['type']),
                            confidence=entity_info['weight'],
                            domain_relevance=entity_info['weight'],
                            ontology_mapping={
                                'ontology': ontology_name,
                                'entity_type': entity_type,
                                'entity_info': entity_info
                            }
                        )
                        
                        if concept.confidence > 0.7:
                            concepts['primary'].append(concept)
                        else:
                            concepts['secondary'].append(concept)
        
        # 基于语义层的概念推断
        inferred_concepts = self._infer_concepts_from_layers(element, semantic_layers)
        concepts['secondary'].extend(inferred_concepts)
        
        return concepts
    
    def _map_to_concept_type(self, type_string: str) -> ConceptType:
        """映射到概念类型"""
        type_mapping = {
            'structural': ConceptType.ENTITY,
            'behavioral': ConceptType.PROCESS,
            'data_element': ConceptType.ATTRIBUTE,
            'relation': ConceptType.RELATION,
            'constraint': ConceptType.CONSTRAINT
        }
        return type_mapping.get(type_string, ConceptType.ENTITY)
    
    def _infer_concepts_from_layers(self, element: ET.Element,
                                   semantic_layers: Dict[SemanticDepth, Dict[str, float]]) -> List[SemanticConcept]:
        """从语义层推断概念"""
        concepts = []
        
        # 基于结构特征推断
        if SemanticDepth.STRUCTURAL in semantic_layers:
            structural = semantic_layers[SemanticDepth.STRUCTURAL]
            
            if structural.get('container_pattern', 0) > 0.5:
                concepts.append(SemanticConcept(
                    concept_id='inferred_container',
                    concept_name='Container',
                    concept_type=ConceptType.ENTITY,
                    confidence=structural['container_pattern']
                ))
        
        return concepts
    
    def _analyze_concept_relationships(self, concepts: Dict[str, List[SemanticConcept]],
                                     element: ET.Element,
                                     context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析概念关系"""
        relationships = []
        
        all_concepts = concepts['primary'] + concepts['secondary']
        
        # 分析概念间关系
        for i, concept1 in enumerate(all_concepts):
            for concept2 in all_concepts[i+1:]:
                similarity = self._calculate_concept_similarity(concept1, concept2)
                if similarity > 0.3:
                    relationships.append({
                        'source_concept': concept1.concept_id,
                        'target_concept': concept2.concept_id,
                        'relationship_type': 'semantic_similarity',
                        'strength': similarity,
                        'confidence': min(concept1.confidence, concept2.confidence)
                    })
        
        return relationships
    
    def _calculate_concept_similarity(self, concept1: SemanticConcept, concept2: SemanticConcept) -> float:
        """计算概念相似性"""
        # 类型相似性
        type_similarity = 1.0 if concept1.concept_type == concept2.concept_type else 0.3
        
        # 领域相关性相似性
        domain_similarity = 1.0 - abs(concept1.domain_relevance - concept2.domain_relevance)
        
        # 名称相似性
        name_similarity = self._calculate_name_similarity(concept1.concept_name, concept2.concept_name)
        
        return (type_similarity + domain_similarity + name_similarity) / 3.0
    
    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """计算名称相似性"""
        name1, name2 = name1.lower(), name2.lower()
        
        if name1 == name2:
            return 1.0
        
        # 简化的字符串相似性
        common_chars = set(name1) & set(name2)
        total_chars = set(name1) | set(name2)
        
        return len(common_chars) / len(total_chars) if total_chars else 0.0
    
    def _perform_knowledge_enrichment(self, concepts: Dict[str, List[SemanticConcept]],
                                    element: ET.Element,
                                    context: Dict[str, Any]) -> Dict[str, Any]:
        """执行知识增强"""
        enrichment = {
            'domain_knowledge': self._extract_domain_knowledge(concepts, element),
            'best_practices': self._identify_best_practices(concepts, element),
            'related_patterns': self._find_related_patterns(concepts, element)
        }
        
        return enrichment
    
    def _extract_domain_knowledge(self, concepts: Dict[str, List[SemanticConcept]], element: ET.Element) -> List[str]:
        """提取领域知识"""
        knowledge = []
        
        for concept in concepts['primary']:
            if concept.ontology_mapping:
                ontology = concept.ontology_mapping.get('ontology', '')
                if 'uml' in ontology:
                    knowledge.append(f"元素符合UML {concept.concept_name} 语义")
                elif 'sysml' in ontology:
                    knowledge.append(f"元素符合SysML {concept.concept_name} 语义")
        
        return knowledge
    
    def _identify_best_practices(self, concepts: Dict[str, List[SemanticConcept]], element: ET.Element) -> List[str]:
        """识别最佳实践"""
        practices = []
        
        # 基于概念类型的最佳实践
        for concept in concepts['primary']:
            if concept.concept_type == ConceptType.ENTITY:
                practices.append("建议为实体元素定义清晰的接口")
            elif concept.concept_type == ConceptType.PROCESS:
                practices.append("建议为过程元素定义输入输出规范")
        
        return practices
    
    def _find_related_patterns(self, concepts: Dict[str, List[SemanticConcept]], element: ET.Element) -> List[str]:
        """查找相关模式"""
        patterns = []
        
        # 基于元素结构的模式识别
        if len(list(element)) > 3:
            patterns.append("复合结构模式")
        
        if len(element.attrib) > 5:
            patterns.append("属性丰富模式")
        
        return patterns
    
    def _align_with_ontologies(self, concepts: Dict[str, List[SemanticConcept]], element: ET.Element) -> List[Dict[str, Any]]:
        """与本体对齐"""
        alignments = []
        
        for concept in concepts['primary']:
            if concept.ontology_mapping:
                alignments.append({
                    'concept_id': concept.concept_id,
                    'ontology': concept.ontology_mapping.get('ontology'),
                    'alignment_confidence': concept.confidence,
                    'mapping_details': concept.ontology_mapping
                })
        
        return alignments
    
    def _perform_semantic_inference(self, concepts: Dict[str, List[SemanticConcept]],
                                   element: ET.Element,
                                   context: Dict[str, Any]) -> List[str]:
        """执行语义推理"""
        inferences = []
        
        # 应用推理规则
        for rule in self.inference_rules:
            if self._check_rule_condition(rule, element, concepts, context):
                inference = f"根据{rule['rule_id']}推断: {rule['inference']}"
                inferences.append(inference)
        
        return inferences
    
    def _check_rule_condition(self, rule: Dict[str, Any],
                             element: ET.Element,
                             concepts: Dict[str, List[SemanticConcept]],
                             context: Dict[str, Any]) -> bool:
        """检查规则条件"""
        condition = rule['condition']
        
        if condition == 'has_children':
            return len(list(element)) > 0
        elif condition == 'camel_case_naming':
            return bool(re.search(r'[a-z][A-Z]', element.tag))
        elif condition == 'operation_pattern':
            return 'operation' in element.tag.lower() or 'method' in element.tag.lower()
        elif condition == 'deep_nesting':
            return self._calculate_depth_level(element) > 0.5
        
        return False
    
    def _extract_implicit_knowledge(self, concepts: List[SemanticConcept],
                                   relationships: List[Dict[str, Any]]) -> Dict[str, Any]:
        """提取隐含知识"""
        implicit = {
            'design_intentions': self._infer_design_intentions(concepts),
            'usage_patterns': self._infer_usage_patterns(relationships),
            'architectural_implications': self._infer_architectural_implications(concepts, relationships)
        }
        
        return implicit
    
    def _infer_design_intentions(self, concepts: List[SemanticConcept]) -> List[str]:
        """推断设计意图"""
        intentions = []
        
        entity_concepts = [c for c in concepts if c.concept_type == ConceptType.ENTITY]
        process_concepts = [c for c in concepts if c.concept_type == ConceptType.PROCESS]
        
        if len(entity_concepts) > len(process_concepts):
            intentions.append("偏向数据结构设计")
        elif len(process_concepts) > len(entity_concepts):
            intentions.append("偏向行为流程设计")
        
        return intentions
    
    def _infer_usage_patterns(self, relationships: List[Dict[str, Any]]) -> List[str]:
        """推断使用模式"""
        patterns = []
        
        if len(relationships) > 5:
            patterns.append("高耦合使用模式")
        elif len(relationships) < 2:
            patterns.append("独立使用模式")
        else:
            patterns.append("适度耦合使用模式")
        
        return patterns
    
    def _infer_architectural_implications(self, concepts: List[SemanticConcept],
                                        relationships: List[Dict[str, Any]]) -> List[str]:
        """推断架构含义"""
        implications = []
        
        # 基于概念复杂度推断
        avg_confidence = sum(c.confidence for c in concepts) / len(concepts) if concepts else 0
        
        if avg_confidence > 0.8:
            implications.append("高确定性架构元素")
        elif avg_confidence < 0.5:
            implications.append("需要进一步澄清的架构元素")
        
        return implications
    
    # 质量评估方法
    def _calculate_semantic_coherence(self, result: DeepSemanticResult) -> float:
        """计算语义一致性"""
        if not result.primary_concepts:
            return 0.0
        
        # 基于概念一致性计算
        concept_types = [c.concept_type for c in result.primary_concepts]
        type_consistency = len(set(concept_types)) / len(concept_types) if concept_types else 0
        
        # 基于关系强度计算
        if result.concept_relationships:
            avg_relationship_strength = sum(r['strength'] for r in result.concept_relationships) / len(result.concept_relationships)
        else:
            avg_relationship_strength = 0
        
        return (type_consistency + avg_relationship_strength) / 2.0
    
    def _assess_analysis_completeness(self, result: DeepSemanticResult, depth: SemanticDepth) -> float:
        """评估分析完整性"""
        expected_layers = {
            SemanticDepth.SURFACE: 1,
            SemanticDepth.STRUCTURAL: 2,
            SemanticDepth.CONCEPTUAL: 3,
            SemanticDepth.CONTEXTUAL: 4,
            SemanticDepth.DEEP: 5
        }
        
        actual_layers = len(result.semantic_layers)
        expected = expected_layers.get(depth, 1)
        
        layer_completeness = actual_layers / expected
        concept_completeness = min(len(result.primary_concepts) / 3.0, 1.0)  # 期望至少3个主要概念
        
        return (layer_completeness + concept_completeness) / 2.0
    
    def _calculate_semantic_confidence(self, result: DeepSemanticResult) -> float:
        """计算语义置信度"""
        if not result.primary_concepts:
            return 0.0
        
        avg_concept_confidence = sum(c.confidence for c in result.primary_concepts) / len(result.primary_concepts)
        
        # 考虑分析深度的影响
        depth_bonus = {
            SemanticDepth.SURFACE: 0.0,
            SemanticDepth.STRUCTURAL: 0.1,
            SemanticDepth.CONCEPTUAL: 0.2,
            SemanticDepth.CONTEXTUAL: 0.3,
            SemanticDepth.DEEP: 0.4
        }
        
        bonus = depth_bonus.get(result.analysis_depth, 0.0)
        return min(avg_concept_confidence + bonus, 1.0)
    
    def _assess_knowledge_coverage(self, result: DeepSemanticResult) -> float:
        """评估知识覆盖度"""
        coverage_factors = []
        
        # 本体对齐覆盖度
        if result.ontology_alignments:
            coverage_factors.append(min(len(result.ontology_alignments) / 3.0, 1.0))
        else:
            coverage_factors.append(0.0)
        
        # 推理覆盖度
        if result.semantic_inferences:
            coverage_factors.append(min(len(result.semantic_inferences) / 2.0, 1.0))
        else:
            coverage_factors.append(0.0)
        
        # 知识增强覆盖度
        if result.knowledge_enrichment:
            enrichment_count = sum(len(v) if isinstance(v, list) else 1 for v in result.knowledge_enrichment.values())
            coverage_factors.append(min(enrichment_count / 5.0, 1.0))
        else:
            coverage_factors.append(0.0)
        
        return sum(coverage_factors) / len(coverage_factors) if coverage_factors else 0.0
    
    # 辅助方法
    def _calculate_depth_level(self, element: ET.Element) -> float:
        """计算深度级别"""
        depth = 0
        current = element.getparent()
        while current is not None:
            depth += 1
            current = current.getparent()
        return min(depth / 10.0, 1.0)
    
    def _calculate_structural_complexity(self, element: ET.Element) -> float:
        """计算结构复杂度"""
        children_count = len(list(element))
        attr_count = len(element.attrib)
        return min((children_count + attr_count) / 15.0, 1.0)
    
    def _assess_abstraction_level(self, element: ET.Element) -> float:
        """评估抽象级别"""
        # 简化实现
        depth = self._calculate_depth_level(element)
        return max(0, 1.0 - depth)
    
    def _assess_context_consistency(self, element: ET.Element, context: Dict[str, Any]) -> float:
        """评估上下文一致性"""
        # 简化实现
        return 0.7
    
    def _calculate_neighborhood_similarity(self, element: ET.Element) -> float:
        """计算邻域相似性"""
        # 简化实现
        return 0.6
    
    def _infer_functional_role(self, element: ET.Element, context: Dict[str, Any]) -> float:
        """推断功能角色"""
        # 简化实现
        return 0.5
    
    def _integrate_semantic_layers(self, layers: Dict[SemanticDepth, Dict[str, float]]) -> float:
        """整合语义层"""
        if not layers:
            return 0.0
        
        total_features = sum(len(features) for features in layers.values())
        return min(total_features / 20.0, 1.0)
    
    def _infer_implicit_semantics(self, element: ET.Element,
                                 context: Dict[str, Any],
                                 layers: Dict[SemanticDepth, Dict[str, float]]) -> float:
        """推断隐含语义"""
        # 简化实现
        return 0.4
    
    def _assess_domain_specialization(self, element: ET.Element,
                                    layers: Dict[SemanticDepth, Dict[str, float]]) -> float:
        """评估领域特化程度"""
        # 简化实现
        return 0.6
    
    def _update_analysis_stats(self, result: DeepSemanticResult):
        """更新分析统计"""
        self.analysis_stats['total_analyses'] += 1
        
        if result.analysis_depth == SemanticDepth.DEEP:
            self.analysis_stats['deep_analyses'] += 1
        
        self.analysis_stats['concepts_discovered'] += len(result.primary_concepts) + len(result.secondary_concepts)
        self.analysis_stats['inferences_made'] += len(result.semantic_inferences)
        
        # 更新平均置信度
        total = self.analysis_stats['total_analyses']
        current_avg = self.analysis_stats['average_confidence']
        self.analysis_stats['average_confidence'] = (
            (current_avg * (total - 1) + result.semantic_confidence) / total
        )
    
    def _create_fallback_result(self, element: ET.Element, depth: SemanticDepth) -> DeepSemanticResult:
        """创建后备结果"""
        return DeepSemanticResult(
            primary_concepts=[
                SemanticConcept(
                    concept_id='fallback_concept',
                    concept_name=element.tag,
                    concept_type=ConceptType.ENTITY,
                    confidence=0.3
                )
            ],
            analysis_depth=depth,
            semantic_confidence=0.3,
            analysis_completeness=0.3
        )
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        return self.analysis_stats.copy()
    
    def batch_analyze(self, elements_with_context: List[Tuple[ET.Element, Dict[str, Any]]], 
                     depth: SemanticDepth = None) -> List[DeepSemanticResult]:
        """批量分析"""
        results = []
        for element, context in elements_with_context:
            result = self.deep_semantic_analysis(element, context, depth)
            results.append(result)
        return results

# 创建实例的工厂函数
def create_intelligent_analyzer(config: Dict[str, Any] = None) -> IntelligentAnalyzer:
    """创建智能分析器实例"""
    return IntelligentAnalyzer(config)

# 导出主要类
__all__ = [
    'IntelligentAnalyzer',
    'DeepSemanticResult',
    'SemanticConcept',
    'SemanticDepth',
    'ConceptType',
    'create_intelligent_analyzer'
] 