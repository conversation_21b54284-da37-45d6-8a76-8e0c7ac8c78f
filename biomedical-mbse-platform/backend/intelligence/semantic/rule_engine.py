"""
规则引擎 (Rule Engine)
基于规则的推理和验证系统

主要功能：
- 支持多种规则类型（OCL、自定义规则等）
- 实时规则验证和冲突检测
- 规则链推理和前向/后向推理
- 可视化规则编辑和调试
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple, Union, Set
import re
import logging
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import json
from collections import defaultdict, deque
import traceback

# 配置日志
logger = logging.getLogger(__name__)

class RuleType(Enum):
    """规则类型"""
    OCL = "ocl"
    CUSTOM = "custom"
    CONSTRAINT = "constraint"
    VALIDATION = "validation"
    INFERENCE = "inference"
    BUSINESS = "business"

class FactType(Enum):
    """事实类型"""
    ELEMENT = "element"
    ATTRIBUTE = "attribute"
    RELATIONSHIP = "relationship"
    CONSTRAINT = "constraint"
    DERIVED = "derived"

class ConflictType(Enum):
    """冲突类型"""
    CONTRADICTION = "contradiction"
    REDUNDANCY = "redundancy"
    SUBSUMPTION = "subsumption"
    CIRCULAR = "circular"

@dataclass
class Fact:
    """事实"""
    id: str
    fact_type: FactType
    subject: str
    predicate: str
    object: Any
    confidence: float = 1.0
    source: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Rule:
    """规则"""
    id: str
    name: str
    rule_type: RuleType
    condition: str
    action: str
    priority: int = 0
    enabled: bool = True
    confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RuleSet:
    """规则集"""
    name: str
    rules: List[Rule]
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ValidationResult:
    """验证结果"""
    valid: bool
    errors: List[str]
    warnings: List[str]
    rule_id: str = ""

@dataclass
class RuleConflict:
    """规则冲突"""
    conflict_type: ConflictType
    rule1: Rule
    rule2: Rule
    description: str
    severity: float

@dataclass
class ConflictResolution:
    """冲突解决方案"""
    conflicts: List[RuleConflict]
    resolution_strategy: str
    resolved_rules: List[Rule]
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RuleExecutionResult:
    """规则执行结果"""
    executed_rules: List[str]
    derived_facts: List[Fact]
    conflicts: List[RuleConflict]
    execution_time: float
    success: bool
    error_message: str = ""

@dataclass
class InferenceTrace:
    """推理轨迹"""
    goal: Fact
    steps: List[Dict[str, Any]]
    success: bool
    final_confidence: float

@dataclass
class OptimizedRuleSet:
    """优化后的规则集"""
    original_size: int
    optimized_size: int
    removed_rules: List[str]
    optimization_metrics: Dict[str, float]

@dataclass
class RulePerformanceReport:
    """规则性能报告"""
    total_executions: int
    average_execution_time: float
    rule_performance: Dict[str, Dict[str, float]]
    bottlenecks: List[str]

class RuleConditionParser:
    """规则条件解析器"""
    
    def __init__(self):
        self.operators = {
            'and': lambda a, b: a and b,
            'or': lambda a, b: a or b,
            'not': lambda a: not a,
            'eq': lambda a, b: a == b,
            'ne': lambda a, b: a != b,
            'gt': lambda a, b: a > b,
            'lt': lambda a, b: a < b,
            'ge': lambda a, b: a >= b,
            'le': lambda a, b: a <= b,
            'in': lambda a, b: a in b,
            'contains': lambda a, b: b in a,
            'exists': lambda a: a is not None,
            'empty': lambda a: len(a) == 0 if hasattr(a, '__len__') else False
        }
    
    def parse_condition(self, condition: str) -> Dict[str, Any]:
        """解析规则条件"""
        try:
            # 简化的条件解析，实际需要更复杂的语法分析
            tokens = self._tokenize(condition)
            ast = self._parse_tokens(tokens)
            return {
                'ast': ast,
                'variables': self._extract_variables(tokens),
                'operators': self._extract_operators(tokens)
            }
        except Exception as e:
            logger.error(f"条件解析失败: {e}")
            return {'ast': None, 'variables': [], 'operators': []}
    
    def evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """评估条件"""
        try:
            parsed = self.parse_condition(condition)
            if parsed['ast']:
                return self._evaluate_ast(parsed['ast'], context)
            return False
        except Exception as e:
            logger.error(f"条件评估失败: {e}")
            return False
    
    def _tokenize(self, condition: str) -> List[str]:
        """标记化"""
        # 简化的标记化实现
        tokens = re.findall(r'\w+|[()=<>!&|]', condition.lower())
        return tokens
    
    def _parse_tokens(self, tokens: List[str]) -> Dict[str, Any]:
        """解析标记"""
        # 简化的AST构建
        return {'type': 'expression', 'tokens': tokens}
    
    def _extract_variables(self, tokens: List[str]) -> List[str]:
        """提取变量"""
        variables = []
        for token in tokens:
            if token.isalpha() and token not in self.operators:
                variables.append(token)
        return list(set(variables))
    
    def _extract_operators(self, tokens: List[str]) -> List[str]:
        """提取操作符"""
        operators = []
        for token in tokens:
            if token in self.operators:
                operators.append(token)
        return operators
    
    def _evaluate_ast(self, ast: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """评估AST"""
        # 简化的AST评估实现
        try:
            tokens = ast.get('tokens', [])
            if not tokens:
                return False
            
            # 替换变量值
            for i, token in enumerate(tokens):
                if token in context:
                    tokens[i] = context[token]
            
            # 简单的表达式评估
            result = self._evaluate_simple_expression(tokens)
            return bool(result)
        except Exception as e:
            logger.error(f"AST评估失败: {e}")
            return False
    
    def _evaluate_simple_expression(self, tokens: List[Any]) -> bool:
        """评估简单表达式"""
        # 这是一个非常简化的实现
        if len(tokens) == 1:
            return bool(tokens[0])
        elif len(tokens) == 3:
            left, op, right = tokens
            if op == '==' or op == 'eq':
                return left == right
            elif op == '!=' or op == 'ne':
                return left != right
            elif op == '>' or op == 'gt':
                return left > right
            elif op == '<' or op == 'lt':
                return left < right
        return False

class RuleEngine:
    """规则引擎"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化规则引擎"""
        self.config = config or {}
        self.rules: Dict[str, Rule] = {}
        self.facts: Dict[str, Fact] = {}
        self.rule_sets: Dict[str, RuleSet] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, Any] = defaultdict(dict)
        
        # 初始化组件
        self.condition_parser = RuleConditionParser()
        
        # 推理引擎状态
        self.inference_depth = 0
        self.max_inference_depth = 10
        
        logger.info("规则引擎初始化完成")
    
    # 规则管理
    def load_rules(self, rule_set: Union[str, RuleSet]) -> bool:
        """加载规则集"""
        try:
            if isinstance(rule_set, str):
                # 从文件加载
                with open(rule_set, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    rule_set = self._parse_rule_set_data(data)
            
            if isinstance(rule_set, RuleSet):
                self.rule_sets[rule_set.name] = rule_set
                
                # 添加规则到引擎
                for rule in rule_set.rules:
                    self.rules[rule.id] = rule
                
                logger.info(f"成功加载规则集: {rule_set.name}, 包含 {len(rule_set.rules)} 条规则")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"规则集加载失败: {e}")
            return False
    
    def add_rule(self, rule: Rule) -> bool:
        """添加规则"""
        try:
            # 验证规则语法
            validation = self.validate_rule_syntax(rule)
            if not validation.valid:
                logger.error(f"规则语法验证失败: {validation.errors}")
                return False
            
            self.rules[rule.id] = rule
            logger.info(f"成功添加规则: {rule.id}")
            return True
            
        except Exception as e:
            logger.error(f"添加规则失败: {e}")
            return False
    
    def remove_rule(self, rule_id: str) -> bool:
        """移除规则"""
        try:
            if rule_id in self.rules:
                del self.rules[rule_id]
                logger.info(f"成功移除规则: {rule_id}")
                return True
            else:
                logger.warning(f"规则不存在: {rule_id}")
                return False
                
        except Exception as e:
            logger.error(f"移除规则失败: {e}")
            return False
    
    def validate_rule_syntax(self, rule: Rule) -> ValidationResult:
        """验证规则语法"""
        errors = []
        warnings = []
        
        try:
            # 检查基本字段
            if not rule.id:
                errors.append("规则ID不能为空")
            
            if not rule.condition:
                errors.append("规则条件不能为空")
            
            if not rule.action:
                errors.append("规则动作不能为空")
            
            # 验证条件语法
            if rule.condition:
                parsed = self.condition_parser.parse_condition(rule.condition)
                if not parsed['ast']:
                    errors.append("条件语法解析失败")
            
            # 验证规则类型
            if rule.rule_type not in RuleType:
                errors.append(f"无效的规则类型: {rule.rule_type}")
            
            # OCL规则特殊验证
            if rule.rule_type == RuleType.OCL:
                if not self._validate_ocl_syntax(rule.condition):
                    errors.append("OCL语法验证失败")
            
            return ValidationResult(
                valid=len(errors) == 0,
                errors=errors,
                warnings=warnings,
                rule_id=rule.id
            )
            
        except Exception as e:
            logger.error(f"规则语法验证异常: {e}")
            return ValidationResult(
                valid=False,
                errors=[f"验证异常: {str(e)}"],
                warnings=[],
                rule_id=rule.id
            )
    
    # 推理执行
    def execute_rules(self, facts: List[Fact]) -> RuleExecutionResult:
        """执行规则"""
        import time
        start_time = time.time()
        
        try:
            # 更新事实库
            for fact in facts:
                self.facts[fact.id] = fact
            
            executed_rules = []
            derived_facts = []
            conflicts = []
            
            # 按优先级排序规则
            sorted_rules = sorted(
                [rule for rule in self.rules.values() if rule.enabled],
                key=lambda r: r.priority,
                reverse=True
            )
            
            # 执行规则
            for rule in sorted_rules:
                try:
                    if self._should_execute_rule(rule):
                        result = self._execute_single_rule(rule)
                        if result['success']:
                            executed_rules.append(rule.id)
                            if result['derived_facts']:
                                derived_facts.extend(result['derived_facts'])
                        else:
                            logger.warning(f"规则执行失败: {rule.id}")
                
                except Exception as e:
                    logger.error(f"规则执行异常: {rule.id}, {e}")
            
            # 检测冲突
            conflicts = self.detect_rule_conflicts(list(self.rules.values()))
            
            execution_time = time.time() - start_time
            
            # 记录执行历史
            execution_record = {
                'timestamp': self._get_timestamp(),
                'executed_rules': executed_rules,
                'derived_facts_count': len(derived_facts),
                'conflicts_count': len(conflicts),
                'execution_time': execution_time
            }
            self.execution_history.append(execution_record)
            
            return RuleExecutionResult(
                executed_rules=executed_rules,
                derived_facts=derived_facts,
                conflicts=conflicts,
                execution_time=execution_time,
                success=True
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"规则执行失败: {e}")
            return RuleExecutionResult(
                executed_rules=[],
                derived_facts=[],
                conflicts=[],
                execution_time=execution_time,
                success=False,
                error_message=str(e)
            )
    
    def forward_chaining(self, initial_facts: List[Fact]) -> List[Fact]:
        """前向推理"""
        try:
            # 初始化事实库
            for fact in initial_facts:
                self.facts[fact.id] = fact
            
            derived_facts = []
            iteration = 0
            max_iterations = 100
            
            while iteration < max_iterations:
                new_facts = []
                
                # 尝试应用所有规则
                for rule in self.rules.values():
                    if not rule.enabled or rule.rule_type != RuleType.INFERENCE:
                        continue
                    
                    # 检查规则条件
                    if self._check_rule_conditions(rule):
                        # 执行规则动作
                        generated_facts = self._execute_rule_action(rule)
                        new_facts.extend(generated_facts)
                
                # 如果没有新事实产生，停止推理
                if not new_facts:
                    break
                
                # 添加新事实到事实库
                for fact in new_facts:
                    if fact.id not in self.facts:
                        self.facts[fact.id] = fact
                        derived_facts.append(fact)
                
                iteration += 1
            
            logger.info(f"前向推理完成，推导出 {len(derived_facts)} 个新事实")
            return derived_facts
            
        except Exception as e:
            logger.error(f"前向推理失败: {e}")
            return []
    
    def backward_chaining(self, goal: Fact) -> InferenceTrace:
        """后向推理"""
        try:
            trace = InferenceTrace(
                goal=goal,
                steps=[],
                success=False,
                final_confidence=0.0
            )
            
            # 检查目标是否已在事实库中
            if goal.id in self.facts:
                trace.success = True
                trace.final_confidence = self.facts[goal.id].confidence
                trace.steps.append({
                    'type': 'found_fact',
                    'fact': goal,
                    'confidence': trace.final_confidence
                })
                return trace
            
            # 寻找可以推导目标的规则
            applicable_rules = self._find_rules_for_goal(goal)
            
            for rule in applicable_rules:
                step = {
                    'type': 'try_rule',
                    'rule_id': rule.id,
                    'rule_name': rule.name
                }
                trace.steps.append(step)
                
                # 递归检查规则的前提条件
                if self._verify_rule_premises(rule, trace):
                    # 执行规则
                    derived_facts = self._execute_rule_action(rule)
                    for fact in derived_facts:
                        if self._facts_match(fact, goal):
                            trace.success = True
                            trace.final_confidence = fact.confidence
                            trace.steps.append({
                                'type': 'goal_achieved',
                                'fact': fact,
                                'confidence': fact.confidence
                            })
                            return trace
            
            logger.info(f"后向推理完成，目标 {goal.id} {'成功' if trace.success else '失败'}")
            return trace
            
        except Exception as e:
            logger.error(f"后向推理失败: {e}")
            return InferenceTrace(goal, [], False, 0.0)
    
    # 冲突解决
    def detect_rule_conflicts(self, rules: List[Rule]) -> List[RuleConflict]:
        """检测规则冲突"""
        conflicts = []
        
        try:
            for i, rule1 in enumerate(rules):
                for j, rule2 in enumerate(rules[i+1:], i+1):
                    conflict = self._check_conflict_between_rules(rule1, rule2)
                    if conflict:
                        conflicts.append(conflict)
            
            logger.info(f"检测到 {len(conflicts)} 个规则冲突")
            return conflicts
            
        except Exception as e:
            logger.error(f"冲突检测失败: {e}")
            return []
    
    def resolve_conflicts(self, conflicts: List[RuleConflict]) -> ConflictResolution:
        """解决冲突"""
        try:
            resolved_rules = []
            resolution_strategy = "priority_based"
            
            # 按冲突严重程度排序
            sorted_conflicts = sorted(conflicts, key=lambda c: c.severity, reverse=True)
            
            for conflict in sorted_conflicts:
                # 根据冲突类型选择解决策略
                if conflict.conflict_type == ConflictType.CONTRADICTION:
                    # 保留优先级更高的规则
                    if conflict.rule1.priority > conflict.rule2.priority:
                        resolved_rules.append(conflict.rule1)
                    else:
                        resolved_rules.append(conflict.rule2)
                
                elif conflict.conflict_type == ConflictType.REDUNDANCY:
                    # 保留置信度更高的规则
                    if conflict.rule1.confidence > conflict.rule2.confidence:
                        resolved_rules.append(conflict.rule1)
                    else:
                        resolved_rules.append(conflict.rule2)
                
                elif conflict.conflict_type == ConflictType.CIRCULAR:
                    # 禁用循环中的一个规则
                    rule_to_disable = conflict.rule2
                    rule_to_disable.enabled = False
                    resolved_rules.append(rule_to_disable)
            
            return ConflictResolution(
                conflicts=conflicts,
                resolution_strategy=resolution_strategy,
                resolved_rules=resolved_rules,
                metadata={'resolution_time': self._get_timestamp()}
            )
            
        except Exception as e:
            logger.error(f"冲突解决失败: {e}")
            return ConflictResolution(conflicts, "failed", [], {'error': str(e)})
    
    # 规则优化
    def optimize_rule_set(self, rules: List[Rule]) -> OptimizedRuleSet:
        """优化规则集"""
        try:
            original_size = len(rules)
            optimized_rules = rules.copy()
            removed_rules = []
            
            # 移除冗余规则
            redundant_rules = self._find_redundant_rules(optimized_rules)
            for rule_id in redundant_rules:
                optimized_rules = [r for r in optimized_rules if r.id != rule_id]
                removed_rules.append(rule_id)
            
            # 合并相似规则
            merged_rules = self._merge_similar_rules(optimized_rules)
            optimized_rules = merged_rules
            
            # 重新排序规则
            optimized_rules = self._reorder_rules_for_performance(optimized_rules)
            
            optimization_metrics = {
                'redundancy_reduction': len(redundant_rules) / max(original_size, 1),
                'size_reduction': (original_size - len(optimized_rules)) / max(original_size, 1),
                'performance_gain': 0.0  # 需要实际测试确定
            }
            
            # 更新规则引擎
            for rule in optimized_rules:
                self.rules[rule.id] = rule
            
            logger.info(f"规则集优化完成: {original_size} -> {len(optimized_rules)}")
            
            return OptimizedRuleSet(
                original_size=original_size,
                optimized_size=len(optimized_rules),
                removed_rules=removed_rules,
                optimization_metrics=optimization_metrics
            )
            
        except Exception as e:
            logger.error(f"规则集优化失败: {e}")
            return OptimizedRuleSet(len(rules), len(rules), [], {})
    
    def analyze_rule_performance(self) -> RulePerformanceReport:
        """分析规则性能"""
        try:
            total_executions = len(self.execution_history)
            
            if total_executions == 0:
                return RulePerformanceReport(0, 0.0, {}, [])
            
            # 计算平均执行时间
            total_time = sum(record['execution_time'] for record in self.execution_history)
            average_execution_time = total_time / total_executions
            
            # 分析每个规则的性能
            rule_performance = {}
            rule_execution_counts = defaultdict(int)
            rule_execution_times = defaultdict(list)
            
            for record in self.execution_history:
                for rule_id in record['executed_rules']:
                    rule_execution_counts[rule_id] += 1
                    # 这里简化处理，实际需要更精确的时间测量
                    estimated_time = record['execution_time'] / len(record['executed_rules'])
                    rule_execution_times[rule_id].append(estimated_time)
            
            for rule_id in rule_execution_counts:
                times = rule_execution_times[rule_id]
                rule_performance[rule_id] = {
                    'execution_count': rule_execution_counts[rule_id],
                    'average_time': sum(times) / len(times),
                    'max_time': max(times),
                    'min_time': min(times)
                }
            
            # 识别性能瓶颈
            bottlenecks = [
                rule_id for rule_id, perf in rule_performance.items()
                if perf['average_time'] > average_execution_time * 2
            ]
            
            return RulePerformanceReport(
                total_executions=total_executions,
                average_execution_time=average_execution_time,
                rule_performance=rule_performance,
                bottlenecks=bottlenecks
            )
            
        except Exception as e:
            logger.error(f"性能分析失败: {e}")
            return RulePerformanceReport(0, 0.0, {}, [])
    
    # 私有方法
    def _parse_rule_set_data(self, data: Dict[str, Any]) -> RuleSet:
        """解析规则集数据"""
        rules = []
        for rule_data in data.get('rules', []):
            rule = Rule(
                id=rule_data['id'],
                name=rule_data.get('name', ''),
                rule_type=RuleType(rule_data.get('type', 'custom')),
                condition=rule_data.get('condition', ''),
                action=rule_data.get('action', ''),
                priority=rule_data.get('priority', 0),
                enabled=rule_data.get('enabled', True),
                confidence=rule_data.get('confidence', 1.0),
                metadata=rule_data.get('metadata', {})
            )
            rules.append(rule)
        
        return RuleSet(
            name=data.get('name', 'unnamed'),
            rules=rules,
            metadata=data.get('metadata', {})
        )
    
    def _validate_ocl_syntax(self, condition: str) -> bool:
        """验证OCL语法"""
        # 简化的OCL语法验证
        try:
            # 检查基本的OCL关键字和结构
            ocl_keywords = ['self', 'context', 'inv', 'pre', 'post', 'forAll', 'exists', 'select', 'collect']
            has_ocl_structure = any(keyword in condition for keyword in ocl_keywords)
            
            # 检查括号匹配
            open_count = condition.count('(')
            close_count = condition.count(')')
            brackets_match = open_count == close_count
            
            return has_ocl_structure and brackets_match
            
        except Exception:
            return False
    
    def _should_execute_rule(self, rule: Rule) -> bool:
        """判断是否应该执行规则"""
        if not rule.enabled:
            return False
        
        # 检查规则条件
        return self._check_rule_conditions(rule)
    
    def _execute_single_rule(self, rule: Rule) -> Dict[str, Any]:
        """执行单个规则"""
        try:
            # 检查条件
            if not self._check_rule_conditions(rule):
                return {'success': False, 'reason': 'conditions_not_met'}
            
            # 执行动作
            derived_facts = self._execute_rule_action(rule)
            
            # 更新性能指标
            self._update_rule_performance(rule.id)
            
            return {
                'success': True,
                'derived_facts': derived_facts
            }
            
        except Exception as e:
            logger.error(f"规则执行异常: {rule.id}, {e}")
            return {'success': False, 'reason': str(e)}
    
    def _check_rule_conditions(self, rule: Rule) -> bool:
        """检查规则条件"""
        try:
            # 构建上下文
            context = self._build_context_for_rule(rule)
            
            # 评估条件
            return self.condition_parser.evaluate_condition(rule.condition, context)
            
        except Exception as e:
            logger.error(f"条件检查失败: {rule.id}, {e}")
            return False
    
    def _execute_rule_action(self, rule: Rule) -> List[Fact]:
        """执行规则动作"""
        try:
            derived_facts = []
            
            # 解析动作
            actions = self._parse_rule_action(rule.action)
            
            for action in actions:
                if action['type'] == 'create_fact':
                    fact = self._create_fact_from_action(action, rule)
                    if fact:
                        derived_facts.append(fact)
                        self.facts[fact.id] = fact
                
                elif action['type'] == 'update_fact':
                    self._update_fact_from_action(action, rule)
                
                elif action['type'] == 'delete_fact':
                    self._delete_fact_from_action(action, rule)
            
            return derived_facts
            
        except Exception as e:
            logger.error(f"动作执行失败: {rule.id}, {e}")
            return []
    
    def _build_context_for_rule(self, rule: Rule) -> Dict[str, Any]:
        """为规则构建上下文"""
        context = {}
        
        # 添加所有事实到上下文
        for fact_id, fact in self.facts.items():
            context[fact.subject] = fact.object
            context[f"fact_{fact_id}"] = fact
        
        # 添加规则特定的上下文
        context['rule_id'] = rule.id
        context['rule_priority'] = rule.priority
        
        return context
    
    def _parse_rule_action(self, action: str) -> List[Dict[str, Any]]:
        """解析规则动作"""
        # 简化的动作解析
        actions = []
        
        # 分割多个动作
        action_parts = action.split(';')
        
        for part in action_parts:
            part = part.strip()
            if not part:
                continue
            
            # 解析动作类型
            if part.startswith('create'):
                actions.append({'type': 'create_fact', 'content': part})
            elif part.startswith('update'):
                actions.append({'type': 'update_fact', 'content': part})
            elif part.startswith('delete'):
                actions.append({'type': 'delete_fact', 'content': part})
            else:
                actions.append({'type': 'custom', 'content': part})
        
        return actions
    
    def _create_fact_from_action(self, action: Dict[str, Any], rule: Rule) -> Optional[Fact]:
        """从动作创建事实"""
        try:
            # 简化的事实创建逻辑
            content = action['content']
            
            # 提取事实信息（简化实现）
            match = re.search(r'create\s+(\w+)\s*\(([^)]+)\)', content)
            if match:
                fact_type = match.group(1)
                params = match.group(2)
                
                # 解析参数
                param_dict = {}
                for param in params.split(','):
                    if '=' in param:
                        key, value = param.split('=', 1)
                        param_dict[key.strip()] = value.strip().strip('"\'')
                
                fact_id = f"derived_{len(self.facts)}_{rule.id}"
                fact = Fact(
                    id=fact_id,
                    fact_type=FactType.DERIVED,
                    subject=param_dict.get('subject', fact_type),
                    predicate=param_dict.get('predicate', 'derived_from'),
                    object=param_dict.get('object', rule.id),
                    confidence=rule.confidence,
                    source=rule.id,
                    metadata={'created_by_rule': rule.id}
                )
                
                return fact
            
            return None
            
        except Exception as e:
            logger.error(f"创建事实失败: {e}")
            return None
    
    def _update_fact_from_action(self, action: Dict[str, Any], rule: Rule):
        """从动作更新事实"""
        # 简化实现
        pass
    
    def _delete_fact_from_action(self, action: Dict[str, Any], rule: Rule):
        """从动作删除事实"""
        # 简化实现
        pass
    
    def _find_rules_for_goal(self, goal: Fact) -> List[Rule]:
        """寻找可以推导目标的规则"""
        applicable_rules = []
        
        for rule in self.rules.values():
            if not rule.enabled or rule.rule_type != RuleType.INFERENCE:
                continue
            
            # 检查规则是否可能产生目标事实
            if self._rule_can_derive_goal(rule, goal):
                applicable_rules.append(rule)
        
        return applicable_rules
    
    def _rule_can_derive_goal(self, rule: Rule, goal: Fact) -> bool:
        """检查规则是否可能推导目标"""
        # 简化实现：检查动作中是否包含目标相关信息
        return goal.subject in rule.action or goal.predicate in rule.action
    
    def _verify_rule_premises(self, rule: Rule, trace: InferenceTrace) -> bool:
        """验证规则前提"""
        # 简化实现：检查条件是否满足
        return self._check_rule_conditions(rule)
    
    def _facts_match(self, fact1: Fact, fact2: Fact) -> bool:
        """检查事实是否匹配"""
        return (fact1.subject == fact2.subject and 
                fact1.predicate == fact2.predicate)
    
    def _check_conflict_between_rules(self, rule1: Rule, rule2: Rule) -> Optional[RuleConflict]:
        """检查两个规则之间的冲突"""
        try:
            # 检查矛盾冲突
            if self._rules_contradict(rule1, rule2):
                return RuleConflict(
                    conflict_type=ConflictType.CONTRADICTION,
                    rule1=rule1,
                    rule2=rule2,
                    description=f"规则 {rule1.id} 和 {rule2.id} 产生矛盾结果",
                    severity=0.9
                )
            
            # 检查冗余
            if self._rules_redundant(rule1, rule2):
                return RuleConflict(
                    conflict_type=ConflictType.REDUNDANCY,
                    rule1=rule1,
                    rule2=rule2,
                    description=f"规则 {rule1.id} 和 {rule2.id} 功能重复",
                    severity=0.3
                )
            
            # 检查循环依赖
            if self._rules_circular(rule1, rule2):
                return RuleConflict(
                    conflict_type=ConflictType.CIRCULAR,
                    rule1=rule1,
                    rule2=rule2,
                    description=f"规则 {rule1.id} 和 {rule2.id} 存在循环依赖",
                    severity=0.8
                )
            
            return None
            
        except Exception as e:
            logger.error(f"冲突检查失败: {e}")
            return None
    
    def _rules_contradict(self, rule1: Rule, rule2: Rule) -> bool:
        """检查规则是否矛盾"""
        # 简化实现：检查动作是否相反
        return ('create' in rule1.action and 'delete' in rule2.action) or \
               ('delete' in rule1.action and 'create' in rule2.action)
    
    def _rules_redundant(self, rule1: Rule, rule2: Rule) -> bool:
        """检查规则是否冗余"""
        # 简化实现：检查条件和动作是否相似
        condition_similarity = self._calculate_similarity(rule1.condition, rule2.condition)
        action_similarity = self._calculate_similarity(rule1.action, rule2.action)
        
        return condition_similarity > 0.8 and action_similarity > 0.8
    
    def _rules_circular(self, rule1: Rule, rule2: Rule) -> bool:
        """检查规则是否存在循环依赖"""
        # 简化实现：检查互相依赖
        rule1_affects_rule2 = self._rule_affects_rule(rule1, rule2)
        rule2_affects_rule1 = self._rule_affects_rule(rule2, rule1)
        
        return rule1_affects_rule2 and rule2_affects_rule1
    
    def _rule_affects_rule(self, rule1: Rule, rule2: Rule) -> bool:
        """检查规则1是否影响规则2"""
        # 简化实现：检查rule1的输出是否是rule2的输入
        rule1_outputs = self._extract_rule_outputs(rule1)
        rule2_inputs = self._extract_rule_inputs(rule2)
        
        return bool(set(rule1_outputs) & set(rule2_inputs))
    
    def _extract_rule_outputs(self, rule: Rule) -> List[str]:
        """提取规则输出"""
        # 简化实现
        outputs = []
        if 'create' in rule.action:
            # 提取创建的实体
            matches = re.findall(r'create\s+(\w+)', rule.action)
            outputs.extend(matches)
        return outputs
    
    def _extract_rule_inputs(self, rule: Rule) -> List[str]:
        """提取规则输入"""
        # 简化实现
        inputs = []
        # 从条件中提取变量
        words = re.findall(r'\w+', rule.condition)
        inputs.extend(words)
        return inputs
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        # 简化的相似度计算
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        
        intersection = words1 & words2
        union = words1 | words2
        
        return len(intersection) / len(union) if union else 0.0
    
    def _find_redundant_rules(self, rules: List[Rule]) -> List[str]:
        """查找冗余规则"""
        redundant = []
        
        for i, rule1 in enumerate(rules):
            for j, rule2 in enumerate(rules[i+1:], i+1):
                if self._rules_redundant(rule1, rule2):
                    # 保留优先级更高或置信度更高的规则
                    if rule1.priority < rule2.priority or \
                       (rule1.priority == rule2.priority and rule1.confidence < rule2.confidence):
                        redundant.append(rule1.id)
                    else:
                        redundant.append(rule2.id)
        
        return list(set(redundant))
    
    def _merge_similar_rules(self, rules: List[Rule]) -> List[Rule]:
        """合并相似规则"""
        # 简化实现：暂时不合并，直接返回
        return rules
    
    def _reorder_rules_for_performance(self, rules: List[Rule]) -> List[Rule]:
        """为性能重新排序规则"""
        # 按执行频率和执行时间排序
        return sorted(rules, key=lambda r: (r.priority, -r.confidence), reverse=True)
    
    def _update_rule_performance(self, rule_id: str):
        """更新规则性能指标"""
        if rule_id not in self.performance_metrics:
            self.performance_metrics[rule_id] = {
                'execution_count': 0,
                'total_time': 0.0
            }
        
        self.performance_metrics[rule_id]['execution_count'] += 1
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

# 工厂函数
def create_rule_engine(config: Dict[str, Any] = None) -> RuleEngine:
    """创建规则引擎实例"""
    return RuleEngine(config)

# 预设规则集
def create_validation_rules() -> RuleSet:
    """创建验证规则集"""
    rules = [
        Rule(
            id="val_001",
            name="元素ID唯一性验证",
            rule_type=RuleType.VALIDATION,
            condition="element.id exists and element.id != ''",
            action="validate uniqueness of element.id",
            priority=10
        ),
        Rule(
            id="val_002", 
            name="必填属性验证",
            rule_type=RuleType.VALIDATION,
            condition="element.required_attributes exists",
            action="validate all required_attributes are present",
            priority=9
        )
    ]
    
    return RuleSet(name="validation_rules", rules=rules)

def create_inference_rules() -> RuleSet:
    """创建推理规则集"""
    rules = [
        Rule(
            id="inf_001",
            name="继承关系推导",
            rule_type=RuleType.INFERENCE,
            condition="relationship.type == 'generalization'",
            action="create inheritance(child=relationship.source, parent=relationship.target)",
            priority=8
        ),
        Rule(
            id="inf_002",
            name="组合关系推导",
            rule_type=RuleType.INFERENCE,
            condition="relationship.type == 'composition'",
            action="create contains(container=relationship.source, contained=relationship.target)",
            priority=7
        )
    ]
    
    return RuleSet(name="inference_rules", rules=rules) 