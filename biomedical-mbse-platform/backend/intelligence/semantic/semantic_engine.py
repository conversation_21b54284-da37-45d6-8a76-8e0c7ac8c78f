"""
XML元数据系统 - 智能语义分析引擎

基于AI驱动的语义分析实现：
- 多维特征提取
- 机器学习分类
- 上下文感知分析
- 语义关系推理
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import xml.etree.ElementTree as ET

logger = logging.getLogger(__name__)

class AnalysisConfidence(Enum):
    """分析置信度级别"""
    HIGH = "high"       # >0.8
    MEDIUM = "medium"   # 0.5-0.8
    LOW = "low"        # <0.5
    UNCERTAIN = "uncertain"  # 无法确定

@dataclass
class SemanticFeatures:
    """语义特征集合"""
    # 词汇特征
    lexical_features: Dict[str, float] = field(default_factory=dict)
    
    # 结构特征
    structural_features: Dict[str, float] = field(default_factory=dict)
    
    # 语义特征
    semantic_features: Dict[str, float] = field(default_factory=dict)
    
    # 关系特征
    relationship_features: Dict[str, float] = field(default_factory=dict)
    
    # 上下文特征
    context_features: Dict[str, float] = field(default_factory=dict)
    
    # 图谱特征
    graph_features: Dict[str, float] = field(default_factory=dict)
    
    def get_feature_vector(self) -> List[float]:
        """获取特征向量"""
        all_features = []
        all_features.extend(self.lexical_features.values())
        all_features.extend(self.structural_features.values())
        all_features.extend(self.semantic_features.values())
        all_features.extend(self.relationship_features.values())
        all_features.extend(self.context_features.values())
        all_features.extend(self.graph_features.values())
        return all_features
    
    def get_feature_count(self) -> int:
        """获取特征总数"""
        return len(self.get_feature_vector())

@dataclass
class SemanticAnalysisResult:
    """语义分析结果"""
    # 主要分类
    primary_category: str
    confidence: float
    
    # 次要分类候选
    secondary_categories: List[Tuple[str, float]] = field(default_factory=list)
    
    # 分析特征
    features: SemanticFeatures = field(default_factory=SemanticFeatures)
    
    # 决策路径
    decision_path: List[str] = field(default_factory=list)
    
    # 业务语义
    business_domain: str = ""
    business_purpose: str = ""
    business_value: float = 0.0
    
    # 技术语义
    technical_stereotype: str = ""
    technical_pattern: str = ""
    complexity_score: float = 0.0
    
    # 关系语义
    relationship_roles: List[str] = field(default_factory=list)
    semantic_connections: List[Dict[str, Any]] = field(default_factory=list)
    
    # 质量指标
    analysis_quality: float = 0.0
    uncertainty_factors: List[str] = field(default_factory=list)
    
    # 元数据
    analysis_timestamp: datetime = field(default_factory=datetime.now)
    analysis_method: str = "ml_enhanced"
    model_version: str = "2.0"

class IntelligentSemanticEngine:
    """智能语义分析引擎"""
    
    def __init__(self):
        """初始化语义分析引擎"""
        # MBSE领域分类系统
        self.mbse_categories = self._initialize_mbse_categories()
        
        # 特征提取器
        self.feature_extractor = self._initialize_feature_extractor()
        
        # 机器学习分类器 (稍后与ml_classifier集成)
        self.ml_classifier = None
        
        # 上下文分析器
        self.context_analyzer = self._initialize_context_analyzer()
        
        # 语义规则库
        self.semantic_rules = self._initialize_semantic_rules()
        
        # 分析统计
        self.analysis_stats = {
            'total_analyses': 0,
            'high_confidence_count': 0,
            'medium_confidence_count': 0,
            'low_confidence_count': 0,
            'average_confidence': 0.0
        }
        
        logger.info("智能语义分析引擎初始化完成")
    
    def _initialize_mbse_categories(self) -> Dict[str, Dict[str, Any]]:
        """初始化MBSE领域分类系统"""
        return {
            "结构域": {
                "elements": ["class", "component", "package", "model"],
                "keywords": ["structure", "component", "module", "system"],
                "priority": 9,
                "description": "系统结构和组件定义"
            },
            "行为域": {
                "elements": ["operation", "activity", "interaction", "statemachine"],
                "keywords": ["behavior", "action", "process", "function"],
                "priority": 8,
                "description": "系统行为和过程定义"
            },
            "需求域": {
                "elements": ["requirement", "constraint", "testcase"],
                "keywords": ["requirement", "need", "constraint", "verify"],
                "priority": 10,
                "description": "需求和约束定义"
            },
            "关系域": {
                "elements": ["association", "dependency", "generalization"],
                "keywords": ["relationship", "dependency", "connection"],
                "priority": 7,
                "description": "元素间关系定义"
            },
            "属性域": {
                "elements": ["property", "attribute", "parameter"],
                "keywords": ["property", "attribute", "parameter", "value"],
                "priority": 6,
                "description": "属性和参数定义"
            },
            "视图域": {
                "elements": ["diagram", "view", "viewpoint"],
                "keywords": ["view", "diagram", "visualization"],
                "priority": 5,
                "description": "视图和图表定义"
            },
            "验证域": {
                "elements": ["testcase", "verification", "validation"],
                "keywords": ["test", "verify", "validate", "check"],
                "priority": 8,
                "description": "验证和测试定义"
            }
        }
    
    def _initialize_feature_extractor(self):
        """初始化特征提取器"""
        # 这里将与feature_extractor.py集成
        return {
            'lexical_extractor': self._extract_lexical_features,
            'structural_extractor': self._extract_structural_features,
            'semantic_extractor': self._extract_semantic_features,
            'relationship_extractor': self._extract_relationship_features,
            'context_extractor': self._extract_context_features,
            'graph_extractor': self._extract_graph_features
        }
    
    def _initialize_context_analyzer(self):
        """初始化上下文分析器"""
        # 这里将与context_analyzer.py集成
        return {
            'parent_context_weight': 0.3,
            'sibling_context_weight': 0.2,
            'global_context_weight': 0.1,
            'semantic_context_weight': 0.4
        }
    
    def _initialize_semantic_rules(self) -> Dict[str, List[Dict[str, Any]]]:
        """初始化语义规则库"""
        return {
            "元素类型规则": [
                {
                    "condition": "tag == 'class' and 'Controller' in name",
                    "category": "行为域",
                    "confidence": 0.8,
                    "reason": "控制器类属于行为域"
                },
                {
                    "condition": "tag == 'association' and type == 'Aggregation'",
                    "category": "关系域", 
                    "confidence": 0.9,
                    "reason": "聚合关系属于关系域"
                }
            ],
            "命名约定规则": [
                {
                    "pattern": r".*Interface$",
                    "category": "结构域",
                    "confidence": 0.7,
                    "reason": "接口类型属于结构域"
                },
                {
                    "pattern": r".*Test.*",
                    "category": "验证域",
                    "confidence": 0.8,
                    "reason": "测试相关元素属于验证域"
                }
            ],
            "上下文规则": [
                {
                    "parent_category": "结构域",
                    "child_bias": {"结构域": 0.3, "属性域": 0.2},
                    "reason": "结构域元素的子元素倾向于结构或属性域"
                }
            ]
        }
    
    def analyze_element_semantics(self, element: ET.Element, context: Optional[Dict[str, Any]] = None) -> SemanticAnalysisResult:
        """
        分析XML元素的语义特征（优化版本）
        
        Args:
            element: 要分析的XML元素
            context: 分析上下文（包含父级信息、模型信息等）
            
        Returns:
            语义分析结果
        """
        try:
            start_time = datetime.now()
            context = context or {}
            
            # 性能优化：限制分析深度
            max_depth = context.get('max_analysis_depth', 3)
            current_depth = context.get('current_depth', 0)
            
            if current_depth > max_depth:
                return self._create_fast_analysis_result(element, "depth_limit")
            
            # 性能优化：采样分析（对于大型文档）
            total_elements = context.get('total_elements', 0)
            if total_elements > 10000:
                # 大型文档使用快速分析模式
                return self._perform_fast_analysis(element, context)
            
            # 提取关键特征（轻量级）
            features = self._extract_essential_features(element, context)
            
            # 执行快速分析
            result = self._perform_optimized_analysis(element, features, context)
            
            # 简化质量评估
            result.analysis_quality = min(0.8, result.confidence)
            
            # 更新统计信息
            self._update_analysis_stats(result)
            
            # 记录处理时间
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            # 性能警告
            if processing_time > 0.1:  # 100ms警告阈值
                logger.warning(f"语义分析耗时过长: {processing_time:.3f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"语义分析失败: {str(e)}")
            return self._create_fallback_result(element)
    
    def analyze_element(self, element: ET.Element, context: Optional[Dict[str, Any]] = None) -> SemanticAnalysisResult:
        """分析元素语义（兼容性方法）"""
        return self.analyze_element_semantics(element, context)
    
    def _perform_fast_analysis(self, element: ET.Element, context: Dict[str, Any]) -> SemanticAnalysisResult:
        """快速分析模式（用于大型文档）"""
        # 基于标签的快速分类
        tag = element.tag.lower()
        
        # 快速分类规则
        if 'class' in tag:
            category = "结构域"
            confidence = 0.8
        elif 'association' in tag or 'relation' in tag:
            category = "关系域"
            confidence = 0.8
        elif 'property' in tag or 'attribute' in tag:
            category = "属性域"
            confidence = 0.8
        elif 'requirement' in tag:
            category = "需求域"
            confidence = 0.9
        elif 'activity' in tag or 'operation' in tag:
            category = "行为域"
            confidence = 0.8
        elif 'diagram' in tag or 'view' in tag:
            category = "视图域"
            confidence = 0.8
        else:
            category = "结构域"
            confidence = 0.6
        
        return SemanticAnalysisResult(
            primary_category=category,
            confidence=confidence,
            decision_path=[f"快速分析: {tag} -> {category}"],
            analysis_method="fast_mode",
            model_version="2.0_fast"
        )
    
    def _extract_essential_features(self, element: ET.Element, context: Dict[str, Any]) -> SemanticFeatures:
        """提取核心特征（轻量级版本）"""
        features = SemanticFeatures()
        
        # 只提取最关键的特征
        features.lexical_features = self._extract_basic_lexical_features(element)
        features.structural_features = self._extract_basic_structural_features(element)
        features.semantic_features = self._extract_basic_semantic_features(element)
        
        return features
    
    def _extract_basic_lexical_features(self, element: ET.Element) -> Dict[str, float]:
        """提取基础词汇特征"""
        features = {}
        
        name = element.get('name', '')
        tag = element.tag
        
        # 基础特征
        features['name_length'] = min(len(name) / 50.0, 1.0)
        features['has_name'] = 1.0 if name else 0.0
        
        # 标签类型特征（限制数量）
        features['is_class'] = 1.0 if 'class' in tag.lower() else 0.0
        features['is_association'] = 1.0 if 'association' in tag.lower() else 0.0
        features['is_property'] = 1.0 if 'property' in tag.lower() else 0.0
        
        return features
    
    def _extract_basic_structural_features(self, element: ET.Element) -> Dict[str, float]:
        """提取基础结构特征"""
        features = {}
        
        # 基础计数（限制计算复杂度）
        children_count = len(list(element))
        attr_count = len(element.attrib)
        
        features['child_count'] = min(children_count / 20.0, 1.0)
        features['attribute_count'] = min(attr_count / 10.0, 1.0)
        features['has_children'] = 1.0 if children_count > 0 else 0.0
        features['has_attributes'] = 1.0 if attr_count > 0 else 0.0
        
        return features
    
    def _extract_basic_semantic_features(self, element: ET.Element) -> Dict[str, float]:
        """提取基础语义特征"""
        features = {}
        
        name = element.get('name', '').lower()
        tag = element.tag.lower()
        
        # 领域关键词匹配（简化版）
        domain_keywords = {
            '结构域': ['class', 'component', 'package', 'interface'],
            '行为域': ['operation', 'activity', 'behavior', 'method'],
            '需求域': ['requirement', 'constraint', 'test'],
            '关系域': ['association', 'dependency', 'relation'],
            '属性域': ['property', 'attribute', 'parameter']
        }
        
        for domain, keywords in domain_keywords.items():
            score = 0.0
            for keyword in keywords:
                if keyword in tag or keyword in name:
                    score = 1.0
                    break
            features[f'domain_{domain}'] = score
        
        return features
    
    def _perform_optimized_analysis(self, element: ET.Element, features: SemanticFeatures, context: Dict[str, Any]) -> SemanticAnalysisResult:
        """执行优化的分析"""
        # 简化的分析流程
        category_scores = {}
        
        # 基于语义特征快速评分
        for domain in self.mbse_categories.keys():
            score = features.semantic_features.get(f'domain_{domain}', 0.0) * 0.6
            
            # 加入结构特征权重
            if domain == "结构域":
                score += features.structural_features.get('has_children', 0.0) * 0.2
            elif domain == "属性域":
                score += features.structural_features.get('has_attributes', 0.0) * 0.3
            
            category_scores[domain] = score
        
        # 选择最佳分类
        best_category = max(category_scores, key=category_scores.get)
        best_score = category_scores[best_category]
        
        # 如果所有得分都很低，使用默认分类
        if best_score < 0.3:
            best_category = "结构域"
            best_score = 0.5
        
        # 构建次要候选
        sorted_categories = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)
        secondary_categories = [(cat, score) for cat, score in sorted_categories[1:3]]
        
        return SemanticAnalysisResult(
            primary_category=best_category,
            confidence=min(0.9, best_score + 0.2),
            secondary_categories=secondary_categories,
            features=features,
            decision_path=[f"优化分析: 特征评分 -> {best_category}"],
            analysis_method="optimized",
            model_version="2.0_opt"
        )
    
    def _create_fast_analysis_result(self, element: ET.Element, reason: str) -> SemanticAnalysisResult:
        """创建快速分析结果"""
        tag = element.tag.lower()
        
        # 基于标签的简单分类
        if any(keyword in tag for keyword in ['class', 'component', 'package']):
            category = "结构域"
        elif any(keyword in tag for keyword in ['association', 'dependency']):
            category = "关系域"
        elif any(keyword in tag for keyword in ['property', 'attribute']):
            category = "属性域"
        else:
            category = "结构域"
        
        return SemanticAnalysisResult(
            primary_category=category,
            confidence=0.6,
            decision_path=[f"快速分析: {reason} -> {category}"],
            analysis_method="fast_analysis",
            model_version="2.0_fast"
        )
    
    def _extract_comprehensive_features(self, element: ET.Element, context: Dict[str, Any]) -> SemanticFeatures:
        """提取综合特征"""
        features = SemanticFeatures()
        
        # 提取各维度特征
        features.lexical_features = self._extract_lexical_features(element, context)
        features.structural_features = self._extract_structural_features(element, context)
        features.semantic_features = self._extract_semantic_features(element, context)
        features.relationship_features = self._extract_relationship_features(element, context)
        features.context_features = self._extract_context_features(element, context)
        features.graph_features = self._extract_graph_features(element, context)
        
        return features
    
    def _extract_lexical_features(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """提取词汇特征"""
        features = {}
        
        # 基础词汇特征
        name = element.get('name', '')
        tag = element.tag
        
        # 名称长度特征
        features['name_length'] = len(name) / 50.0  # 归一化
        features['name_word_count'] = len(name.split()) / 10.0
        
        # 命名风格特征
        features['is_camel_case'] = 1.0 if self._is_camel_case(name) else 0.0
        features['is_snake_case'] = 1.0 if '_' in name else 0.0
        features['has_numbers'] = 1.0 if any(c.isdigit() for c in name) else 0.0
        
        # 关键词匹配特征
        for category, info in self.mbse_categories.items():
            keyword_score = 0.0
            for keyword in info['keywords']:
                if keyword.lower() in name.lower():
                    keyword_score += 1.0
            features[f'keyword_match_{category}'] = keyword_score / len(info['keywords'])
        
        # 标签类型特征
        features['is_class'] = 1.0 if tag == 'class' else 0.0
        features['is_association'] = 1.0 if tag == 'association' else 0.0
        features['is_property'] = 1.0 if tag == 'property' else 0.0
        
        return features
    
    def _extract_structural_features(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """提取结构特征"""
        features = {}
        
        # 元素结构特征
        features['attribute_count'] = len(element.attrib) / 20.0  # 归一化
        features['child_count'] = len(list(element)) / 50.0
        features['text_length'] = len(element.text or '') / 1000.0
        
        # 命名空间特征
        namespace = element.tag.split('}')[0] if '}' in element.tag else ''
        features['has_namespace'] = 1.0 if namespace else 0.0
        features['is_uml_namespace'] = 1.0 if 'uml' in namespace.lower() else 0.0
        
        # 属性特征
        features['has_id'] = 1.0 if element.get('xmi:id') or element.get('id') else 0.0
        features['has_type'] = 1.0 if element.get('type') or element.get('xmi:type') else 0.0
        features['has_stereotype'] = 1.0 if element.get('stereotype') else 0.0
        
        # 层次深度特征 (基于context)
        depth = context.get('element_depth', 0)
        features['depth_normalized'] = depth / 10.0
        
        return features
    
    def _extract_semantic_features(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """提取语义特征"""
        features = {}
        
        name = element.get('name', '')
        tag = element.tag
        
        # 业务语义特征
        business_keywords = [
            'business', 'service', 'process', 'workflow', 'rule',
            'customer', 'user', 'account', 'order', 'product'
        ]
        business_score = sum(1 for kw in business_keywords if kw in name.lower())
        features['business_semantic_score'] = business_score / len(business_keywords)
        
        # 技术语义特征
        technical_keywords = [
            'interface', 'implementation', 'abstract', 'concrete',
            'factory', 'builder', 'observer', 'strategy', 'adapter'
        ]
        technical_score = sum(1 for kw in technical_keywords if kw in name.lower())
        features['technical_semantic_score'] = technical_score / len(technical_keywords)
        
        # 领域语义特征
        for category, info in self.mbse_categories.items():
            # 元素类型匹配
            element_match = 1.0 if tag in info['elements'] else 0.0
            features[f'domain_element_match_{category}'] = element_match
            
            # 优先级权重
            features[f'domain_priority_{category}'] = info['priority'] / 10.0
        
        # 模式识别特征
        patterns = {
            'controller_pattern': r'.*[Cc]ontroller.*',
            'service_pattern': r'.*[Ss]ervice.*',
            'entity_pattern': r'.*[Ee]ntity.*',
            'util_pattern': r'.*[Uu]til.*',
            'manager_pattern': r'.*[Mm]anager.*'
        }
        
        import re
        for pattern_name, pattern_regex in patterns.items():
            features[pattern_name] = 1.0 if re.match(pattern_regex, name) else 0.0
        
        return features
    
    def _extract_relationship_features(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """提取关系特征"""
        features = {}
        
        # 引用关系特征
        ref_count = 0
        for attr_name, attr_value in element.attrib.items():
            if 'ref' in attr_name.lower() or 'href' in attr_name.lower():
                ref_count += 1
        
        features['reference_count'] = ref_count / 10.0  # 归一化
        
        # 被引用特征 (需要全局上下文)
        referenced_count = context.get('referenced_count', 0)
        features['referenced_count'] = referenced_count / 20.0
        
        # 父子关系特征
        parent_category = context.get('parent_category', '')
        for category in self.mbse_categories.keys():
            features[f'parent_is_{category}'] = 1.0 if parent_category == category else 0.0
        
        # 兄弟关系特征
        sibling_categories = context.get('sibling_categories', [])
        for category in self.mbse_categories.keys():
            features[f'siblings_include_{category}'] = 1.0 if category in sibling_categories else 0.0
        
        return features
    
    def _extract_context_features(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """提取上下文特征"""
        features = {}
        
        # 全局上下文特征
        total_elements = context.get('total_elements', 1)
        features['global_element_ratio'] = 1.0 / total_elements if total_elements > 0 else 0.0
        
        # 局部上下文特征
        local_element_count = context.get('local_element_count', 0)
        features['local_density'] = local_element_count / 100.0
        
        # 文档类型上下文
        document_type = context.get('document_type', '')
        doc_type_features = {
            'is_class_diagram': 1.0 if 'class' in document_type.lower() else 0.0,
            'is_sequence_diagram': 1.0 if 'sequence' in document_type.lower() else 0.0,
            'is_component_diagram': 1.0 if 'component' in document_type.lower() else 0.0
        }
        features.update(doc_type_features)
        
        # 版本上下文特征
        version_info = context.get('version_info', {})
        features['has_version_info'] = 1.0 if version_info else 0.0
        features['is_latest_version'] = version_info.get('is_latest', 0.0)
        
        return features
    
    def _extract_graph_features(self, element: ET.Element, context: Dict[str, Any]) -> Dict[str, float]:
        """提取图谱特征"""
        features = {}
        
        # 节点重要性特征 (基于度中心性)
        in_degree = context.get('in_degree', 0)
        out_degree = context.get('out_degree', 0)
        total_degree = in_degree + out_degree
        
        features['in_degree_normalized'] = in_degree / 50.0
        features['out_degree_normalized'] = out_degree / 50.0
        features['total_degree_normalized'] = total_degree / 100.0
        
        # 聚类系数特征
        clustering_coefficient = context.get('clustering_coefficient', 0.0)
        features['clustering_coefficient'] = clustering_coefficient
        
        # 路径特征
        avg_path_length = context.get('avg_path_length', 0.0)
        features['avg_path_length_normalized'] = avg_path_length / 10.0
        
        # 社区检测特征
        community_id = context.get('community_id', -1)
        features['has_community'] = 1.0 if community_id >= 0 else 0.0
        features['community_size_normalized'] = context.get('community_size', 0) / 100.0
        
        return features
    
    def _perform_multi_level_analysis(self, element: ET.Element, features: SemanticFeatures, context: Dict[str, Any]) -> SemanticAnalysisResult:
        """执行多层次分析"""
        # 1. 规则基础分析
        rule_result = self._rule_based_analysis(element, features, context)
        
        # 2. 特征基础分析 
        feature_result = self._feature_based_analysis(element, features, context)
        
        # 3. 上下文增强分析
        context_result = self._context_enhanced_analysis(element, features, context)
        
        # 4. 集成分析结果
        integrated_result = self._integrate_analysis_results(
            [rule_result, feature_result, context_result],
            element, features, context
        )
        
        return integrated_result
    
    def _rule_based_analysis(self, element: ET.Element, features: SemanticFeatures, context: Dict[str, Any]) -> SemanticAnalysisResult:
        """基于规则的分析"""
        name = element.get('name', '')
        tag = element.tag
        
        # 元素类型规则匹配
        for category, info in self.mbse_categories.items():
            if tag in info['elements']:
                return SemanticAnalysisResult(
                    primary_category=category,
                    confidence=0.8,
                    decision_path=[f"元素类型规则: {tag} -> {category}"],
                    analysis_method="rule_based",
                    features=features
                )
        
        # 命名约定规则匹配
        import re
        for rule in self.semantic_rules.get("命名约定规则", []):
            if re.search(rule['pattern'], name):
                return SemanticAnalysisResult(
                    primary_category=rule['category'],
                    confidence=rule['confidence'],
                    decision_path=[f"命名规则: {rule['pattern']} -> {rule['category']}"],
                    analysis_method="rule_based",
                    features=features
                )
        
        # 默认分类
        return SemanticAnalysisResult(
            primary_category="结构域",
            confidence=0.5,
            decision_path=["默认规则: 未知元素 -> 结构域"],
            analysis_method="rule_based",
            features=features
        )
    
    def _feature_based_analysis(self, element: ET.Element, features: SemanticFeatures, context: Dict[str, Any]) -> SemanticAnalysisResult:
        """基于特征的分析"""
        # 计算各类别的特征匹配分数
        category_scores = {}
        
        for category in self.mbse_categories.keys():
            score = 0.0
            
            # 词汇特征贡献
            score += features.lexical_features.get(f'keyword_match_{category}', 0.0) * 0.3
            
            # 语义特征贡献
            score += features.semantic_features.get(f'domain_element_match_{category}', 0.0) * 0.4
            score += features.semantic_features.get(f'domain_priority_{category}', 0.0) * 0.2
            
            # 结构特征贡献
            if category == "结构域":
                score += features.structural_features.get('child_count', 0.0) * 0.1
            elif category == "关系域":
                score += features.relationship_features.get('reference_count', 0.0) * 0.2
            
            category_scores[category] = score
        
        # 选择最高分类别
        best_category = max(category_scores, key=category_scores.get)
        best_score = category_scores[best_category]
        
        # 计算次要候选
        sorted_categories = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)
        secondary_categories = [(cat, score) for cat, score in sorted_categories[1:4]]
        
        return SemanticAnalysisResult(
            primary_category=best_category,
            confidence=min(0.9, best_score),
            secondary_categories=secondary_categories,
            decision_path=[f"特征分析: 最高分数 {best_score:.3f} -> {best_category}"],
            analysis_method="feature_based",
            features=features
        )
    
    def _context_enhanced_analysis(self, element: ET.Element, features: SemanticFeatures, context: Dict[str, Any]) -> SemanticAnalysisResult:
        """上下文增强分析"""
        # 获取父元素上下文
        parent_category = context.get('parent_category', '')
        
        # 基于父元素上下文调整分类倾向
        context_bias = {}
        for rule in self.semantic_rules.get("上下文规则", []):
            if rule.get('parent_category') == parent_category:
                context_bias.update(rule.get('child_bias', {}))
        
        # 计算上下文增强分数
        base_scores = {cat: 0.5 for cat in self.mbse_categories.keys()}
        
        # 应用上下文偏置
        for category, bias in context_bias.items():
            if category in base_scores:
                base_scores[category] += bias
        
        # 兄弟元素上下文
        sibling_categories = context.get('sibling_categories', [])
        for category in sibling_categories:
            if category in base_scores:
                base_scores[category] += 0.1  # 兄弟元素相似性偏置
        
        # 选择最佳类别
        best_category = max(base_scores, key=base_scores.get)
        best_score = base_scores[best_category]
        
        return SemanticAnalysisResult(
            primary_category=best_category,
            confidence=min(0.85, best_score),
            decision_path=[f"上下文分析: 父元素={parent_category} -> {best_category}"],
            analysis_method="context_enhanced",
            features=features
        )
    
    def _integrate_analysis_results(self, results: List[SemanticAnalysisResult], 
                                  element: ET.Element, features: SemanticFeatures, 
                                  context: Dict[str, Any]) -> SemanticAnalysisResult:
        """集成多种分析结果"""
        # 权重设置
        weights = {
            'rule_based': 0.4,
            'feature_based': 0.4,
            'context_enhanced': 0.2
        }
        
        # 收集所有候选分类
        category_votes = {}
        total_weight = 0.0
        decision_paths = []
        
        for result in results:
            method = result.analysis_method
            weight = weights.get(method, 0.0)
            
            if weight > 0:
                category = result.primary_category
                if category not in category_votes:
                    category_votes[category] = 0.0
                
                category_votes[category] += result.confidence * weight
                total_weight += weight
                decision_paths.extend(result.decision_path)
        
        # 归一化投票结果
        if total_weight > 0:
            for category in category_votes:
                category_votes[category] /= total_weight
        
        # 选择最终分类
        if category_votes:
            final_category = max(category_votes, key=category_votes.get)
            final_confidence = category_votes[final_category]
        else:
            final_category = "结构域"
            final_confidence = 0.5
        
        # 构建次要候选
        sorted_votes = sorted(category_votes.items(), key=lambda x: x[1], reverse=True)
        secondary_categories = sorted_votes[1:4]
        
        # 构建最终结果
        final_result = SemanticAnalysisResult(
            primary_category=final_category,
            confidence=final_confidence,
            secondary_categories=secondary_categories,
            features=features,
            decision_path=decision_paths,
            analysis_method="integrated",
            model_version="2.0"
        )
        
        # 添加业务和技术语义信息
        self._enrich_semantic_information(final_result, element, context)
        
        return final_result
    
    def _enrich_semantic_information(self, result: SemanticAnalysisResult, element: ET.Element, context: Dict[str, Any]):
        """丰富语义信息"""
        name = element.get('name', '')
        
        # 业务语义推导
        if result.primary_category in ["需求域", "验证域"]:
            result.business_domain = "业务逻辑"
            result.business_purpose = "需求管理和验证"
            result.business_value = 0.9
        elif result.primary_category == "结构域":
            result.business_domain = "系统架构"
            result.business_purpose = "结构定义和组织"
            result.business_value = 0.8
        elif result.primary_category == "行为域":
            result.business_domain = "业务流程"
            result.business_purpose = "行为建模和控制"
            result.business_value = 0.85
        
        # 技术语义推导
        if 'interface' in name.lower():
            result.technical_stereotype = "Interface"
            result.technical_pattern = "Interface Pattern"
        elif 'abstract' in name.lower():
            result.technical_stereotype = "Abstract"
            result.technical_pattern = "Abstract Pattern"
        elif 'controller' in name.lower():
            result.technical_stereotype = "Controller"
            result.technical_pattern = "MVC Pattern"
        
        # 复杂度评估
        result.complexity_score = self._calculate_complexity_score(element, result.features)
    
    def _calculate_complexity_score(self, element: ET.Element, features: SemanticFeatures) -> float:
        """计算复杂度分数"""
        complexity = 0.0
        
        # 基于结构特征
        complexity += features.structural_features.get('child_count', 0.0) * 2.0
        complexity += features.structural_features.get('attribute_count', 0.0) * 1.0
        
        # 基于关系特征
        complexity += features.relationship_features.get('reference_count', 0.0) * 1.5
        complexity += features.relationship_features.get('referenced_count', 0.0) * 1.0
        
        # 基于图谱特征
        complexity += features.graph_features.get('total_degree_normalized', 0.0) * 3.0
        
        return min(10.0, complexity)
    
    def _assess_analysis_quality(self, result: SemanticAnalysisResult, features: SemanticFeatures):
        """评估分析质量"""
        quality_score = 0.0
        
        # 置信度贡献
        quality_score += result.confidence * 0.4
        
        # 特征丰富度贡献
        feature_count = features.get_feature_count()
        quality_score += min(1.0, feature_count / 100.0) * 0.3
        
        # 决策路径完整性贡献
        path_completeness = len(result.decision_path) / 3.0  # 期望3个决策步骤
        quality_score += min(1.0, path_completeness) * 0.2
        
        # 次要候选丰富度贡献
        secondary_richness = len(result.secondary_categories) / 3.0
        quality_score += min(1.0, secondary_richness) * 0.1
        
        result.analysis_quality = min(1.0, quality_score)
        
        # 识别不确定因素
        if result.confidence < 0.6:
            result.uncertainty_factors.append("低置信度分类")
        if len(result.secondary_categories) > 0 and result.secondary_categories[0][1] > 0.7:
            result.uncertainty_factors.append("次要分类置信度过高")
        if feature_count < 20:
            result.uncertainty_factors.append("特征数量不足")
    
    def _update_analysis_stats(self, result: SemanticAnalysisResult):
        """更新分析统计"""
        self.analysis_stats['total_analyses'] += 1
        
        # 置信度分布统计
        if result.confidence > 0.8:
            self.analysis_stats['high_confidence_count'] += 1
        elif result.confidence > 0.5:
            self.analysis_stats['medium_confidence_count'] += 1
        else:
            self.analysis_stats['low_confidence_count'] += 1
        
        # 更新平均置信度
        total = self.analysis_stats['total_analyses']
        current_avg = self.analysis_stats['average_confidence']
        self.analysis_stats['average_confidence'] = (
            (current_avg * (total - 1) + result.confidence) / total
        )
    
    def _create_fallback_result(self, element: ET.Element) -> SemanticAnalysisResult:
        """创建备用分析结果"""
        return SemanticAnalysisResult(
            primary_category="结构域",
            confidence=0.3,
            decision_path=["错误回退: 使用默认分类"],
            analysis_method="fallback",
            uncertainty_factors=["分析过程出错"],
            analysis_quality=0.2
        )
    
    def _is_camel_case(self, name: str) -> bool:
        """检查是否为驼峰命名"""
        if not name:
            return False
        return name[0].islower() and any(c.isupper() for c in name[1:])
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        stats = self.analysis_stats.copy()
        
        # 计算置信度分布比例
        total = stats['total_analyses']
        if total > 0:
            stats['high_confidence_ratio'] = stats['high_confidence_count'] / total
            stats['medium_confidence_ratio'] = stats['medium_confidence_count'] / total
            stats['low_confidence_ratio'] = stats['low_confidence_count'] / total
        
        # 添加模型信息
        stats['model_info'] = {
            'version': '2.0',
            'feature_dimensions': 6,
            'mbse_categories': len(self.mbse_categories),
            'analysis_methods': ['rule_based', 'feature_based', 'context_enhanced', 'integrated']
        }
        
        return stats
    
    def batch_analyze_elements(self, elements: List[Tuple[ET.Element, Dict[str, Any]]]) -> List[SemanticAnalysisResult]:
        """批量分析元素"""
        results = []
        
        for i, (element, context) in enumerate(elements):
            try:
                result = self.analyze_element_semantics(element, context)
                result.batch_index = i
                results.append(result)
            except Exception as e:
                logger.error(f"批量分析失败 (索引 {i}): {str(e)}")
                fallback_result = self._create_fallback_result(element)
                fallback_result.batch_index = i
                results.append(fallback_result)
        
        logger.info(f"批量语义分析完成: {len(results)}个元素")
        return results 