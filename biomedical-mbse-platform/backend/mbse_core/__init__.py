"""
生物医学MBSE核心引擎模块
=======================

整合XML元数据系统的AI能力，为生物医学建模提供专业化的MBSE引擎。

核心组件：
- engine.py: 主引擎，协调各组件工作
- model_converter.py: 多格式模型转换器
- workflow_orchestrator.py: 工作流编排器
"""

__version__ = "1.0.0"
__author__ = "生物医学MBSE团队"

from .engine import BiomedicalMBSEEngine
from .model_converter import BiomedicalModelConverter
from .workflow_orchestrator import WorkflowOrchestrator

__all__ = [
    "BiomedicalMBSEEngine",
    "BiomedicalModelConverter", 
    "WorkflowOrchestrator"
] 