"""
生物医学MBSE核心引擎
==================

整合UML/SysML标准建模系统，提供生物医学专业化的MBSE功能。
"""

from typing import Dict, Any, List, Optional, Union
import uuid
import asyncio
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 导入标准建模引擎
from .uml_engine import UMLEngine, UMLDiagram, UMLElement
from .sysml_engine import SysMLEngine, SysMLBlock, SysMLRequirement

# 导入现有组件
from xml_metadata_integration.metadata_bridge import XMLMetadataBridge
from biomedical_extensions.biomedical_recommendation_engine import BiomedicalRecommendationEngine
from biomedical_extensions.biomedical_preloader import BiomedicalPreloader
from biomedical_extensions.biomedical_cache_manager import BiomedicalCacheManager
from biomedical_extensions.tool_chain_manager import ToolChainManager

class IntegratedBiomedicalMBSEEngine:
    """集成生物医学MBSE核心引擎"""
    
    def __init__(self):
        # 核心建模引擎
        self.uml_engine = UMLEngine()
        self.sysml_engine = SysMLEngine(self.uml_engine)
        
        # 保持与现有XML系统的兼容性
        self.metadata_bridge = XMLMetadataBridge()
        
        # 获取生物医学专用组件
        self.recommendation_engine = BiomedicalRecommendationEngine()
        self.preloader = BiomedicalPreloader()
        self.cache_manager = BiomedicalCacheManager()
        self.tool_chain_manager = ToolChainManager()
        
        # 知识管理集成（新增）
        self.knowledge_integrator = KnowledgeModelIntegrator()
        
        # 状态管理
        self.active_projects = {}
        self.running_workflows = {}
        self.engine_status = "initialized"
        
        print("集成生物医学MBSE引擎初始化完成")
    
    async def create_biomedical_project(self, project_config: Dict[str, Any]) -> str:
        """创建新的生物医学MBSE项目"""
        project_id = self._generate_project_id()
        
        # 利用推荐引擎选择建模策略
        modeling_strategy = self.recommendation_engine.recommend_workflow(
            project_config.get('project_type', 'general_biomedical')
        )
        
        # 预加载相关资源
        await self._preload_project_resources(project_config, modeling_strategy)
        
        # 创建项目实例
        project = BiomedicalMBSEProject(
            id=project_id,
            config=project_config,
            strategy=modeling_strategy,
            engine=self
        )
        
        self.active_projects[project_id] = project
        
        return project_id
    
    async def create_uml_diagram(self, project_id: str, diagram_type: str, name: str, 
                               domain: str = 'biomedical') -> Dict[str, Any]:
        """创建UML图形"""
        project = self.active_projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        # 创建基础UML图形
        diagram = self.uml_engine.create_diagram(diagram_type, name)
        
        # 应用生物医学专业化
        if domain == 'biomedical':
            await self._apply_biomedical_specialization(diagram, project.config)
        
        # 存储到项目中
        diagram_id = await project.add_diagram(diagram)
        
        return {
            'diagram_id': diagram_id,
            'diagram': diagram.to_dict(),
            'recommendations': await self._get_diagram_recommendations(diagram, domain)
        }
    
    async def create_sysml_diagram(self, project_id: str, diagram_type: str, name: str,
                                 domain: str = 'biomedical') -> Dict[str, Any]:
        """创建SysML图形"""
        project = self.active_projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        # 创建SysML图形
        diagram = self.sysml_engine.create_diagram(diagram_type, name)
        
        # 应用生物医学专业化
        if domain == 'biomedical':
            await self._apply_biomedical_specialization(diagram, project.config)
        
        # 存储到项目中
        diagram_id = await project.add_diagram(diagram)
        
        return {
            'diagram_id': diagram_id,
            'diagram': diagram.to_dict(),
            'recommendations': await self._get_diagram_recommendations(diagram, domain)
        }
    
    async def create_biomedical_block(self, project_id: str, diagram_id: str,
                                    block_config: Dict[str, Any]) -> Dict[str, Any]:
        """创建生物医学SysML块"""
        project = self.active_projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        diagram = project.get_diagram(diagram_id)
        if not diagram:
            raise ValueError(f"图形不存在: {diagram_id}")
        
        # 创建SysML块
        biological_type = block_config.get('biological_type')
        block = self.sysml_engine.create_block(
            block_config['name'], 
            biological_type
        )
        
        # 应用额外配置
        if block_config.get('auto_configure', True):
            await self._auto_configure_block(block, block_config)
        
        # 添加到图形
        diagram.add_element(block)
        
        # 推荐相关工具
        tool_recommendations = self.tool_chain_manager.get_tools_by_category(None)
        recommended_tools = [
            tool for tool in tool_recommendations 
            if biological_type in tool.get('description', '').lower()
        ]
        
        return {
            'block_id': block.id,
            'block': block.to_dict(),
            'recommended_tools': recommended_tools[:5]  # 限制推荐数量
        }
    
    async def execute_integrated_workflow(self, project_id: str, 
                                        workflow_config: Dict[str, Any]) -> str:
        """执行集成生物医学分析工作流"""
        project = self.active_projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        # 利用推荐引擎选择工具链
        analysis_type = workflow_config.get('analysis_type', 'general')
        data_format = workflow_config.get('data_format', 'unknown')
        
        recommended_tools = self.recommendation_engine.recommend_tools_for_analysis(
            analysis_type, data_format
        )
        
        # 执行工具链
        workflow_id = self._generate_workflow_id()
        
        # 异步执行工作流
        asyncio.create_task(self._execute_workflow_async(
            workflow_id, project_id, workflow_config, recommended_tools
        ))
        
        return workflow_id
    
    async def _execute_workflow_async(self, workflow_id: str, project_id: str,
                                    workflow_config: Dict[str, Any], 
                                    recommended_tools: List[Dict]):
        """异步执行工作流"""
        try:
            # 执行工具链
            result = self.tool_chain_manager.execute_tool_chain(
                [tool['name'] for tool in recommended_tools],
                workflow_config.get('input_data')
            )
            
            # 更新项目模型（如果有图形输出）
            if workflow_config.get('update_models', True):
                await self._update_models_from_workflow_results(
                    project_id, workflow_config, result
                )
            
            self.running_workflows[workflow_id] = {
                'project_id': project_id,
                'config': workflow_config,
                'result': result,
                'status': 'completed',
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            self.running_workflows[workflow_id] = {
                'project_id': project_id,
                'config': workflow_config,
                'error': str(e),
                'status': 'failed',
                'timestamp': datetime.now()
            }
    
    async def integrate_knowledge_context(self, project_id: str, diagram_id: str,
                                        document_ids: List[str]) -> Dict[str, Any]:
        """集成知识上下文到图形"""
        project = self.active_projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        diagram = project.get_diagram(diagram_id)
        if not diagram:
            raise ValueError(f"图形不存在: {diagram_id}")
        
        # 创建知识链接
        knowledge_links = await self.knowledge_integrator.create_knowledge_links(
            diagram, document_ids
        )
        
        # 更新图形元数据
        diagram.metadata['knowledge_links'] = knowledge_links
        
        return {
            'knowledge_links_count': len(knowledge_links),
            'knowledge_links': knowledge_links
        }
    
    async def get_project_status(self, project_id: str) -> Dict[str, Any]:
        """获取项目状态"""
        project = self.active_projects.get(project_id)
        if not project:
            return {"error": "项目不存在"}
        
        return {
            "project_id": project.id,
            "status": "active",
            "config": project.config,
            "diagrams_count": len(project.diagrams),
            "models_count": len(project.models),
            "workflows_count": len([w for w in self.running_workflows.values() 
                                   if w['project_id'] == project_id]),
            "knowledge_links_count": project.get_knowledge_links_count()
        }
    
    async def _apply_biomedical_specialization(self, diagram: UMLDiagram, 
                                             project_config: Dict[str, Any]):
        """应用生物医学专业化"""
        domain = project_config.get('domain', 'general_biomedical')
        
        # 设置生物医学元数据
        diagram.metadata.update({
            'biomedical_domain': domain,
            'specialized': True,
            'specialization_version': '1.0'
        })
        
        # 根据领域添加推荐元素模板
        if domain == 'molecular_biology':
            diagram.metadata['recommended_elements'] = [
                'protein', 'gene', 'enzyme', 'metabolite'
            ]
        elif domain == 'systems_biology':
            diagram.metadata['recommended_elements'] = [
                'pathway', 'network', 'regulation', 'flux'
            ]
        elif domain == 'cell_biology':
            diagram.metadata['recommended_elements'] = [
                'cell', 'organelle', 'membrane', 'transport'
            ]
    
    async def _auto_configure_block(self, block: SysMLBlock, config: Dict[str, Any]):
        """自动配置块"""
        biological_type = config.get('biological_type')
        
        # 根据配置添加额外属性
        if config.get('add_standard_properties', True):
            # 添加通用生物医学属性
            block.add_value_property('confidence_score', 'Real', 'normalized')
            block.add_value_property('validation_status', 'String', 'categorical')
        
        if config.get('add_experimental_data', False):
            block.add_value_property('experimental_evidence', 'String', 'text')
            block.add_value_property('literature_references', 'String', 'list')
        
        # 应用知识注释
        if config.get('auto_annotate', True):
            annotations = await self.knowledge_integrator.generate_auto_annotations(
                block.name, biological_type
            )
            block.biological_annotations.update(annotations)
    
    async def _get_diagram_recommendations(self, diagram: UMLDiagram, 
                                         domain: str) -> List[Dict[str, Any]]:
        """获取图形推荐"""
        recommendations = []
        
        # 推荐建模元素
        if diagram.diagram_type == 'ClassDiagram' and domain == 'biomedical':
            recommendations.append({
                'type': 'element',
                'title': '添加蛋白质类',
                'description': '为生物医学模型添加蛋白质类元素',
                'action': 'create_element',
                'params': {'element_type': 'Class', 'biological_type': 'protein'}
            })
        
        # 推荐工具
        recommended_tools = self.tool_chain_manager.get_tools_by_category(None)
        for tool in recommended_tools[:3]:  # 限制推荐数量
            recommendations.append({
                'type': 'tool',
                'title': f'使用 {tool["name"]}',
                'description': tool['description'],
                'action': 'integrate_tool',
                'params': {'tool_name': tool['name']}
            })
        
        return recommendations
    
    async def _update_models_from_workflow_results(self, project_id: str, 
                                                 workflow_config: Dict[str, Any],
                                                 results: Dict[str, Any]):
        """从工作流结果更新模型"""
        project = self.active_projects.get(project_id)
        if not project:
            return
        
        # 根据结果类型更新不同的模型元素
        target_diagram_id = workflow_config.get('target_diagram_id')
        if target_diagram_id:
            diagram = project.get_diagram(target_diagram_id)
            if diagram:
                # 添加分析结果到图形元数据
                if 'analysis_results' not in diagram.metadata:
                    diagram.metadata['analysis_results'] = []
                
                diagram.metadata['analysis_results'].append({
                    'workflow_type': workflow_config.get('analysis_type'),
                    'results': results,
                    'timestamp': datetime.now().isoformat()
                })
    
    def _generate_project_id(self) -> str:
        """生成项目ID"""
        return f"proj_{uuid.uuid4().hex[:8]}"
    
    def _generate_workflow_id(self) -> str:
        """生成工作流ID"""
        return f"wf_{uuid.uuid4().hex[:8]}"
    
    async def list_available_tools(self) -> List[Dict[str, Any]]:
        """列出可用工具"""
        return self.tool_chain_manager.get_tools_by_category(None)
    
    async def get_recommendations(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取AI推荐"""
        analysis_type = context.get('analysis_type', 'general')
        data_format = context.get('data_format', 'unknown')
        
        return self.recommendation_engine.recommend_tools_for_analysis(
            analysis_type, data_format
        )
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        return {
            "status": self.engine_status,
            "uml_engine": "active",
            "sysml_engine": "active",
            "xml_bridge_connected": self.metadata_bridge.test_complete_integration(),
            "active_projects": len(self.active_projects),
            "running_workflows": len(self.running_workflows),
            "available_tools": len(self.tool_chain_manager.available_tools)
        }

class BiomedicalMBSEProject:
    """生物医学MBSE项目"""
    
    def __init__(self, id: str, config: Dict, strategy: Dict, engine):
        self.id = id
        self.config = config
        self.strategy = strategy
        self.engine = engine
        self.diagrams = {}  # 新增：图形管理
        self.models = {}    # 保持兼容
        self.workflows = {}
        self.results = {}
        self.created_at = datetime.now()
    
    async def add_diagram(self, diagram: UMLDiagram) -> str:
        """添加图形到项目"""
        self.diagrams[diagram.id] = diagram
        
        # 缓存图形数据
        cache_key = f"diagram:{self.id}:{diagram.id}"
        self.engine.cache_manager.set_biomedical_cache(
            cache_key, diagram.to_dict(), 'uml_diagram'
        )
        
        return diagram.id
    
    def get_diagram(self, diagram_id: str) -> Optional[UMLDiagram]:
        """获取图形"""
        return self.diagrams.get(diagram_id)
    
    def list_diagrams(self) -> List[Dict[str, Any]]:
        """列出项目中的所有图形"""
        return [
            {
                'diagram_id': did,
                'name': diagram.name,
                'type': diagram.diagram_type,
                'elements_count': len(diagram.elements),
                'created_at': diagram.created_at.isoformat()
            }
            for did, diagram in self.diagrams.items()
        ]
    
    def get_knowledge_links_count(self) -> int:
        """获取知识链接数量"""
        total_links = 0
        for diagram in self.diagrams.values():
            knowledge_links = diagram.metadata.get('knowledge_links', [])
            total_links += len(knowledge_links)
        return total_links
    
    # 保持与现有代码的兼容性
    async def add_model(self, model_data: Any, model_format: str) -> str:
        """添加生物医学模型"""
        model_id = f"model_{uuid.uuid4().hex[:8]}"
        
        # 缓存模型数据
        cache_key = f"model:{self.id}:{model_id}"
        self.engine.cache_manager.set_biomedical_cache(
            cache_key, model_data, model_format
        )
        
        self.models[model_id] = {
            'format': model_format,
            'cache_key': cache_key,
            'added_at': datetime.now()
        }
        
        return model_id

class KnowledgeModelIntegrator:
    """知识模型集成器"""
    
    async def create_knowledge_links(self, diagram: UMLDiagram, 
                                   document_ids: List[str]) -> List[Dict[str, Any]]:
        """创建知识链接"""
        # 简化实现，实际需要NLP和语义分析
        knowledge_links = []
        
        for doc_id in document_ids:
            # 模拟文档分析
            link = {
                'document_id': doc_id,
                'relevance_score': 0.8,  # 模拟相关性分数
                'matched_elements': [elem.id for elem in diagram.elements[:2]],
                'relationship_type': 'describes',
                'created_at': datetime.now().isoformat()
            }
            knowledge_links.append(link)
        
        return knowledge_links
    
    async def generate_auto_annotations(self, element_name: str, 
                                      biological_type: str) -> Dict[str, Any]:
        """生成自动注释"""
        # 简化实现
        annotations = {
            'auto_generated': True,
            'biological_context': f'Element {element_name} is a {biological_type}',
            'confidence': 0.7,
            'last_updated': datetime.now().isoformat()
        }
        
        return annotations

# 保持向后兼容性的别名
BiomedicalMBSEEngine = IntegratedBiomedicalMBSEEngine 