"""
增强版UML建模引擎
=================

集成XML解析、生物医学专业化和MBSE标准建模的完整UML引擎。
基于现有的UML引擎，增加XML导入导出、模型转换、验证等高级功能。
"""

from typing import Dict, List, Any, Optional, Union, Tuple
import uuid
import asyncio
from datetime import datetime
import logging
import json

# 导入基础引擎
from .uml_engine import UMLEngine, UMLElement, UMLDiagram, UMLClass
from .sysml_engine import SysMLEngine, SysMLBlock, SysMLRequirement
from .uml_xml_integration import UMLXMLIntegration

# 导入XML元数据桥接
from ..xml_metadata_integration.metadata_bridge import XMLMetadataBridge

logger = logging.getLogger(__name__)

class EnhancedUMLEngine:
    """增强版UML建模引擎"""
    
    def __init__(self):
        # 初始化核心引擎
        self.uml_engine = UMLEngine()
        self.sysml_engine = SysMLEngine(self.uml_engine)
        
        # 初始化XML集成
        self.xml_integration = UMLXMLIntegration()
        self.xml_metadata_bridge = XMLMetadataBridge()
        
        # 模型存储
        self.projects = {}  # project_id -> ProjectModel
        self.model_cache = {}  # 模型缓存
        self.transformation_history = []  # 变换历史
        
        # 生物医学专业化配置
        self.biomedical_profiles = self._initialize_biomedical_profiles()
        
        # 验证器和转换器
        self.model_validator = UMLModelValidator()
        self.model_transformer = UMLModelTransformer()
        
        logger.info("增强版UML引擎初始化完成")
    
    def _initialize_biomedical_profiles(self) -> Dict[str, Any]:
        """初始化生物医学配置文件"""
        return {
            'molecular_biology': {
                'stereotypes': ['«protein»', '«gene»', '«enzyme»', '«metabolite»'],
                'default_attributes': {
                    'protein': ['sequence', 'molecular_weight', 'isoelectric_point'],
                    'gene': ['sequence', 'length', 'gc_content', 'expression_level'],
                    'enzyme': ['activity', 'km_value', 'vmax', 'substrate'],
                    'metabolite': ['formula', 'mass', 'charge', 'solubility']
                },
                'constraints': {
                    'protein': ['michaelis_menten', 'binding_affinity'],
                    'enzyme': ['enzyme_kinetics', 'inhibition_constants'],
                    'gene': ['expression_regulation', 'transcription_factors']
                }
            },
            'systems_biology': {
                'stereotypes': ['«pathway»', '«network»', '«regulation»', '«flux»'],
                'default_attributes': {
                    'pathway': ['pathway_id', 'activity_level', 'flux_rate'],
                    'network': ['connectivity', 'centrality', 'clustering'],
                    'regulation': ['regulation_type', 'strength', 'direction']
                },
                'constraints': {
                    'pathway': ['flux_balance', 'thermodynamic_constraints'],
                    'network': ['connectivity_constraints', 'scale_free_topology']
                }
            },
            'cell_biology': {
                'stereotypes': ['«cell»', '«organelle»', '«membrane»', '«transport»'],
                'default_attributes': {
                    'cell': ['cell_type', 'volume', 'membrane_potential'],
                    'organelle': ['organelle_type', 'location', 'function'],
                    'membrane': ['lipid_composition', 'permeability', 'proteins']
                }
            }
        }
    
    async def create_biomedical_project(self, project_config: Dict[str, Any]) -> str:
        """创建生物医学MBSE项目"""
        project_id = f"proj_{uuid.uuid4().hex[:8]}"
        
        # 创建项目模型
        project = ProjectModel(
            id=project_id,
            config=project_config,
            engine=self
        )
        
        # 应用生物医学配置文件
        domain = project_config.get('domain', 'molecular_biology')
        if domain in self.biomedical_profiles:
            project.apply_biomedical_profile(self.biomedical_profiles[domain])
        
        # 集成XML元数据系统能力
        if self.xml_metadata_bridge.is_connected:
            project.enable_xml_metadata_integration(self.xml_metadata_bridge)
        
        self.projects[project_id] = project
        logger.info(f"生物医学项目创建成功: {project_id}")
        
        return project_id
    
    async def import_model_from_xml(self, project_id: str, xml_source: str, 
                                   import_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """从XML源导入模型"""
        project = self.projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        options = import_options or {}
        
        try:
            # 使用XML集成模块导入
            import_result = await self.xml_integration.import_xmi_file(xml_source, project_id)
            
            if import_result['success']:
                # 将导入的图形添加到项目
                for diagram in import_result['diagrams']:
                    project.add_diagram(diagram)
                
                # 应用生物医学后处理
                await self._post_process_imported_model(project, import_result, options)
                
                # 记录导入历史
                self.transformation_history.append({
                    'type': 'import',
                    'project_id': project_id,
                    'source': xml_source,
                    'timestamp': datetime.now().isoformat(),
                    'result': import_result
                })
            
            return import_result
            
        except Exception as e:
            logger.error(f"XML模型导入失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _post_process_imported_model(self, project: 'ProjectModel', 
                                         import_result: Dict[str, Any], 
                                         options: Dict[str, Any]):
        """后处理导入的模型"""
        # 1. 应用生物医学增强
        if options.get('apply_biomedical_enhancement', True):
            await self._enhance_with_biomedical_context(project, import_result['diagrams'])
        
        # 2. 标准化命名
        if options.get('standardize_naming', True):
            await self._standardize_element_naming(project)
        
        # 3. 验证模型一致性
        if options.get('validate_model', True):
            validation_result = await self.validate_project_model(project.id)
            project.last_validation = validation_result
        
        # 4. 生成建议
        if options.get('generate_suggestions', True):
            suggestions = await self._generate_modeling_suggestions(project)
            project.modeling_suggestions = suggestions
    
    async def _enhance_with_biomedical_context(self, project: 'ProjectModel', 
                                             diagrams: List[UMLDiagram]):
        """使用生物医学上下文增强模型"""
        for diagram in diagrams:
            for element in diagram.elements:
                # 检测生物医学元素
                bio_type = self._detect_biomedical_type(element)
                if bio_type:
                    # 应用生物医学专业化
                    await self._apply_biomedical_specialization(element, bio_type, project.biomedical_profile)
                    
                    # 添加标准属性
                    self._add_standard_biomedical_attributes(element, bio_type)
                    
                    # 设置约束
                    self._set_biomedical_constraints(element, bio_type)
    
    def _detect_biomedical_type(self, element: UMLElement) -> Optional[str]:
        """检测元素的生物医学类型"""
        name = element.name.lower()
        
        # 基于名称的启发式检测
        bio_keywords = {
            'protein': ['protein', 'enzyme', 'antibody', 'receptor'],
            'gene': ['gene', 'dna', 'chromosome', 'allele'],
            'pathway': ['pathway', 'cascade', 'signaling', 'metabolic'],
            'cell': ['cell', 'neuron', 'hepatocyte', 'fibroblast'],
            'metabolite': ['metabolite', 'compound', 'molecule', 'substrate']
        }
        
        for bio_type, keywords in bio_keywords.items():
            for keyword in keywords:
                if keyword in name:
                    return bio_type
        
        # 检查立体类型
        if element.stereotype:
            stereotype = element.stereotype.lower()
            for bio_type in bio_keywords.keys():
                if bio_type in stereotype:
                    return bio_type
        
        return None
    
    async def _apply_biomedical_specialization(self, element: UMLElement, 
                                             bio_type: str, profile: Dict[str, Any]):
        """应用生物医学专业化"""
        # 设置生物医学类型
        element.biological_type = {
            'category': bio_type,
            'profile': profile.get('name', 'general'),
            'specialized_at': datetime.now().isoformat()
        }
        
        # 应用专业化立体类型
        if not element.stereotype and bio_type in profile.get('stereotypes', []):
            element.stereotype = f'«{bio_type}»'
        
        # 添加生物医学注释
        element.biological_annotations = {
            'auto_detected': True,
            'detection_confidence': 0.8,  # 简化的置信度
            'specialization_applied': bio_type
        }
    
    def _add_standard_biomedical_attributes(self, element: UMLElement, bio_type: str):
        """添加标准生物医学属性"""
        # 根据生物医学类型添加标准属性
        standard_attrs = {
            'protein': [
                {'name': 'sequence', 'type': 'String', 'default': ''},
                {'name': 'molecular_weight', 'type': 'Real', 'unit': 'kDa'},
                {'name': 'isoelectric_point', 'type': 'Real', 'unit': 'pH'}
            ],
            'gene': [
                {'name': 'sequence', 'type': 'String', 'default': ''},
                {'name': 'length', 'type': 'Integer', 'unit': 'bp'},
                {'name': 'expression_level', 'type': 'Real', 'unit': 'FPKM'}
            ],
            'pathway': [
                {'name': 'pathway_id', 'type': 'String', 'default': ''},
                {'name': 'activity_level', 'type': 'Real', 'unit': 'normalized'},
                {'name': 'flux_rate', 'type': 'Real', 'unit': 'mmol/h'}
            ]
        }
        
        if bio_type in standard_attrs:
            # 如果是SysML块，添加值属性
            if hasattr(element, 'add_value_property'):
                for attr in standard_attrs[bio_type]:
                    element.add_value_property(
                        attr['name'], 
                        attr['type'], 
                        attr.get('unit')
                    )
            # 如果是UML类，添加属性
            elif hasattr(element, 'add_attribute'):
                for attr in standard_attrs[bio_type]:
                    element.add_attribute(
                        attr['name'],
                        attr['type'],
                        'private'
                    )
    
    def _set_biomedical_constraints(self, element: UMLElement, bio_type: str):
        """设置生物医学约束"""
        constraint_templates = {
            'protein': [
                {
                    'name': 'sequence_length_constraint',
                    'expression': 'length(sequence) > 0',
                    'description': '蛋白质序列不能为空'
                },
                {
                    'name': 'molecular_weight_range',
                    'expression': 'molecular_weight > 0 and molecular_weight < 1000',
                    'description': '分子量应在合理范围内'
                }
            ],
            'gene': [
                {
                    'name': 'expression_range',
                    'expression': 'expression_level >= 0',
                    'description': '表达水平不能为负数'
                }
            ]
        }
        
        if bio_type in constraint_templates and hasattr(element, 'add_constraint'):
            for constraint in constraint_templates[bio_type]:
                element.add_constraint(
                    constraint['name'],
                    constraint['expression'],
                    []  # 参数列表
                )
    
    async def export_model_to_xml(self, project_id: str, export_format: str = 'xmi', 
                                 export_options: Dict[str, Any] = None) -> str:
        """导出模型到XML格式"""
        project = self.projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        options = export_options or {}
        
        try:
            if export_format.lower() == 'xmi':
                # 导出为XMI格式
                xml_content = await self.xml_integration.export_to_xmi(
                    project_id, 
                    project.diagrams
                )
                
                # 添加生物医学扩展信息
                if options.get('include_biomedical_extensions', True):
                    xml_content = self._add_biomedical_extensions_to_xml(xml_content, project)
                
                # 记录导出历史
                self.transformation_history.append({
                    'type': 'export',
                    'project_id': project_id,
                    'format': export_format,
                    'timestamp': datetime.now().isoformat()
                })
                
                return xml_content
            
            else:
                raise ValueError(f"不支持的导出格式: {export_format}")
                
        except Exception as e:
            logger.error(f"XML模型导出失败: {e}")
            raise
    
    def _add_biomedical_extensions_to_xml(self, xml_content: str, project: 'ProjectModel') -> str:
        """向XML添加生物医学扩展信息"""
        # 解析XML
        import xml.etree.ElementTree as ET
        root = ET.fromstring(xml_content)
        
        # 添加生物医学命名空间
        root.set('xmlns:biomedical', 'http://biomedical-mbse.org/extensions/1.0')
        
        # 为每个元素添加生物医学注释
        for elem in root.iter():
            if elem.get('xmi:type') and elem.get('name'):
                # 查找对应的UML元素
                element_name = elem.get('name')
                uml_element = self._find_element_by_name(project, element_name)
                
                if uml_element and hasattr(uml_element, 'biological_type'):
                    # 添加生物医学扩展元素
                    bio_ext = ET.SubElement(elem, 'biomedical:extension')
                    bio_ext.set('type', uml_element.biological_type.get('category', 'unknown'))
                    bio_ext.set('profile', uml_element.biological_type.get('profile', 'general'))
                    
                    # 添加生物医学注释
                    if hasattr(uml_element, 'biological_annotations'):
                        for key, value in uml_element.biological_annotations.items():
                            annotation = ET.SubElement(bio_ext, 'biomedical:annotation')
                            annotation.set('key', key)
                            annotation.set('value', str(value))
        
        # 返回修改后的XML
        ET.indent(root, space="  ", level=0)
        return ET.tostring(root, encoding='unicode')
    
    def _find_element_by_name(self, project: 'ProjectModel', name: str) -> Optional[UMLElement]:
        """在项目中查找指定名称的元素"""
        for diagram in project.diagrams:
            for element in diagram.elements:
                if element.name == name:
                    return element
        return None
    
    async def validate_project_model(self, project_id: str) -> Dict[str, Any]:
        """验证项目模型"""
        project = self.projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        return await self.model_validator.validate_project(project)
    
    async def transform_model(self, project_id: str, transformation_type: str, 
                            transformation_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行模型变换"""
        project = self.projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        return await self.model_transformer.transform(project, transformation_type, transformation_params)
    
    async def _generate_modeling_suggestions(self, project: 'ProjectModel') -> List[Dict[str, Any]]:
        """生成建模建议"""
        suggestions = []
        
        # 基于XML元数据系统的推荐引擎
        if self.xml_metadata_bridge.is_connected:
            recommender = self.xml_metadata_bridge.get_biomedical_recommendation_engine()
            if recommender:
                # 获取AI推荐
                ai_suggestions = recommender.recommend_modeling_strategy(
                    project.config.get('project_type', 'general'),
                    {'domain': project.config.get('domain', 'molecular_biology')}
                )
                suggestions.extend(ai_suggestions.get('suggestions', []))
        
        # 基于模型分析的建议
        analysis_suggestions = await self._analyze_model_for_suggestions(project)
        suggestions.extend(analysis_suggestions)
        
        return suggestions
    
    async def _analyze_model_for_suggestions(self, project: 'ProjectModel') -> List[Dict[str, Any]]:
        """分析模型并生成建议"""
        suggestions = []
        
        # 分析图形复杂度
        for diagram in project.diagrams:
            if len(diagram.elements) > 20:
                suggestions.append({
                    'type': 'complexity_warning',
                    'title': '图形过于复杂',
                    'description': f'图形 {diagram.name} 包含 {len(diagram.elements)} 个元素，建议分解为多个子图',
                    'priority': 'medium',
                    'action': 'decompose_diagram'
                })
            
            # 检查缺失的关系
            if len(diagram.relationships) < len(diagram.elements) * 0.3:
                suggestions.append({
                    'type': 'relationship_suggestion',
                    'title': '添加更多关系',
                    'description': f'图形 {diagram.name} 的关系密度较低，考虑添加更多关系以完善模型',
                    'priority': 'low',
                    'action': 'add_relationships'
                })
        
        # 检查生物医学专业化
        bio_elements = 0
        total_elements = 0
        
        for diagram in project.diagrams:
            for element in diagram.elements:
                total_elements += 1
                if hasattr(element, 'biological_type') and element.biological_type:
                    bio_elements += 1
        
        if total_elements > 0 and bio_elements / total_elements < 0.5:
            suggestions.append({
                'type': 'biomedical_enhancement',
                'title': '增强生物医学特性',
                'description': f'只有 {bio_elements}/{total_elements} 个元素具有生物医学特性，建议增强专业化',
                'priority': 'medium',
                'action': 'enhance_biomedical_features'
            })
        
        return suggestions
    
    def get_project_status(self, project_id: str) -> Dict[str, Any]:
        """获取项目状态"""
        project = self.projects.get(project_id)
        if not project:
            return {'error': '项目不存在'}
        
        return project.get_status()

class ProjectModel:
    """项目模型类"""
    
    def __init__(self, id: str, config: Dict[str, Any], engine: EnhancedUMLEngine):
        self.id = id
        self.config = config
        self.engine = engine
        self.diagrams: List[UMLDiagram] = []
        self.biomedical_profile = {}
        self.xml_metadata_integration = None
        self.last_validation = None
        self.modeling_suggestions = []
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def apply_biomedical_profile(self, profile: Dict[str, Any]):
        """应用生物医学配置文件"""
        self.biomedical_profile = profile
        self.updated_at = datetime.now()
    
    def enable_xml_metadata_integration(self, xml_bridge: XMLMetadataBridge):
        """启用XML元数据集成"""
        self.xml_metadata_integration = xml_bridge
    
    def add_diagram(self, diagram: UMLDiagram):
        """添加图形"""
        self.diagrams.append(diagram)
        self.updated_at = datetime.now()
    
    def get_status(self) -> Dict[str, Any]:
        """获取项目状态"""
        total_elements = sum(len(d.elements) for d in self.diagrams)
        total_relationships = sum(len(d.relationships) for d in self.diagrams)
        
        bio_elements = 0
        for diagram in self.diagrams:
            for element in diagram.elements:
                if hasattr(element, 'biological_type') and element.biological_type:
                    bio_elements += 1
        
        return {
            'project_id': self.id,
            'config': self.config,
            'diagrams_count': len(self.diagrams),
            'total_elements': total_elements,
            'total_relationships': total_relationships,
            'biomedical_elements': bio_elements,
            'biomedical_profile': self.biomedical_profile.get('name', 'none'),
            'xml_integration_enabled': self.xml_metadata_integration is not None,
            'last_validation': self.last_validation,
            'suggestions_count': len(self.modeling_suggestions),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class UMLModelValidator:
    """UML模型验证器"""
    
    async def validate_project(self, project: ProjectModel) -> Dict[str, Any]:
        """验证项目模型"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'suggestions': [],
            'validation_timestamp': datetime.now().isoformat()
        }
        
        # 验证每个图形
        for diagram in project.diagrams:
            diagram_validation = await self._validate_diagram(diagram)
            
            validation_result['errors'].extend(diagram_validation['errors'])
            validation_result['warnings'].extend(diagram_validation['warnings'])
            validation_result['suggestions'].extend(diagram_validation['suggestions'])
        
        # 验证项目级别的一致性
        project_validation = await self._validate_project_consistency(project)
        validation_result['errors'].extend(project_validation['errors'])
        validation_result['warnings'].extend(project_validation['warnings'])
        
        # 设置总体验证状态
        validation_result['is_valid'] = len(validation_result['errors']) == 0
        
        return validation_result
    
    async def _validate_diagram(self, diagram: UMLDiagram) -> Dict[str, Any]:
        """验证单个图形"""
        result = {'errors': [], 'warnings': [], 'suggestions': []}
        
        # 验证元素
        for element in diagram.elements:
            if not element.name or element.name.strip() == '':
                result['errors'].append({
                    'type': 'missing_name',
                    'element_id': element.id,
                    'message': '元素缺少名称'
                })
        
        # 验证关系
        element_ids = {e.id for e in diagram.elements}
        for relationship in diagram.relationships:
            if relationship.get('source_id') not in element_ids:
                result['errors'].append({
                    'type': 'invalid_relationship',
                    'relationship_id': relationship.get('id'),
                    'message': '关系源元素不存在'
                })
            
            if relationship.get('target_id') not in element_ids:
                result['errors'].append({
                    'type': 'invalid_relationship',
                    'relationship_id': relationship.get('id'),
                    'message': '关系目标元素不存在'
                })
        
        return result
    
    async def _validate_project_consistency(self, project: ProjectModel) -> Dict[str, Any]:
        """验证项目一致性"""
        result = {'errors': [], 'warnings': []}
        
        # 检查重复元素名称
        all_names = []
        for diagram in project.diagrams:
            for element in diagram.elements:
                all_names.append(element.name)
        
        name_counts = {}
        for name in all_names:
            name_counts[name] = name_counts.get(name, 0) + 1
        
        for name, count in name_counts.items():
            if count > 1:
                result['warnings'].append({
                    'type': 'duplicate_name',
                    'message': f'元素名称 "{name}" 重复 {count} 次'
                })
        
        return result

class UMLModelTransformer:
    """UML模型变换器"""
    
    async def transform(self, project: ProjectModel, transformation_type: str, 
                       params: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行模型变换"""
        params = params or {}
        
        transformations = {
            'extract_interfaces': self._extract_interfaces,
            'merge_diagrams': self._merge_diagrams,
            'split_diagram': self._split_diagram,
            'apply_pattern': self._apply_design_pattern,
            'refactor_naming': self._refactor_naming
        }
        
        if transformation_type not in transformations:
            raise ValueError(f"不支持的变换类型: {transformation_type}")
        
        return await transformations[transformation_type](project, params)
    
    async def _extract_interfaces(self, project: ProjectModel, params: Dict[str, Any]) -> Dict[str, Any]:
        """提取接口变换"""
        extracted_interfaces = []
        
        for diagram in project.diagrams:
            for element in diagram.elements:
                if hasattr(element, 'operations') and element.operations:
                    # 创建接口
                    interface_name = f"I{element.name}"
                    # 实现接口提取逻辑
                    extracted_interfaces.append(interface_name)
        
        return {
            'transformation_type': 'extract_interfaces',
            'extracted_interfaces': extracted_interfaces,
            'success': True
        }
    
    async def _merge_diagrams(self, project: ProjectModel, params: Dict[str, Any]) -> Dict[str, Any]:
        """合并图形变换"""
        # 实现图形合并逻辑
        return {'transformation_type': 'merge_diagrams', 'success': True}
    
    async def _split_diagram(self, project: ProjectModel, params: Dict[str, Any]) -> Dict[str, Any]:
        """分割图形变换"""
        # 实现图形分割逻辑
        return {'transformation_type': 'split_diagram', 'success': True}
    
    async def _apply_design_pattern(self, project: ProjectModel, params: Dict[str, Any]) -> Dict[str, Any]:
        """应用设计模式变换"""
        # 实现设计模式应用逻辑
        return {'transformation_type': 'apply_pattern', 'success': True}
    
    async def _refactor_naming(self, project: ProjectModel, params: Dict[str, Any]) -> Dict[str, Any]:
        """重构命名变换"""
        # 实现命名重构逻辑
        return {'transformation_type': 'refactor_naming', 'success': True} 