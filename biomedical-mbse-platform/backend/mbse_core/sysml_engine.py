"""
SysML扩展建模引擎
===============

基于UML引擎实现SysML 1.6标准扩展
"""

from typing import Dict, List, Any, Optional
import uuid
from datetime import datetime
from enum import Enum
from .uml_engine import UMLEngine, UMLElement, UMLDiagram

class SysMLDiagramType(Enum):
    """SysML图形类型"""
    BLOCK_DEFINITION_DIAGRAM = "BlockDefinitionDiagram"
    INTERNAL_BLOCK_DIAGRAM = "InternalBlockDiagram"
    REQUIREMENTS_DIAGRAM = "RequirementsDiagram"
    PARAMETRIC_DIAGRAM = "ParametricDiagram"
    PACKAGE_DIAGRAM = "PackageDiagram"
    # Activity和Sequence继承自UML
    ACTIVITY_DIAGRAM = "ActivityDiagram"
    SEQUENCE_DIAGRAM = "SequenceDiagram"
    STATE_MACHINE_DIAGRAM = "StateMachineDiagram"
    USE_CASE_DIAGRAM = "UseCaseDiagram"

class ValueProperty:
    """SysML值属性"""
    
    def __init__(self, name: str, type_name: str, unit: Optional[str] = None):
        self.name = name
        self.type = type_name
        self.unit = unit
        self.default_value = None
        self.constraints = []
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'type': self.type,
            'unit': self.unit,
            'default_value': self.default_value,
            'constraints': self.constraints
        }

class FlowProperty:
    """SysML流属性"""
    
    def __init__(self, name: str, type_name: str, direction: str):
        self.name = name
        self.type = type_name
        self.direction = direction  # 'in', 'out', 'inout'
        self.multiplicity = '1'
        self.protocol = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'type': self.type,
            'direction': self.direction,
            'multiplicity': self.multiplicity,
            'protocol': self.protocol
        }

class ConstraintProperty:
    """SysML约束属性"""
    
    def __init__(self, name: str, expression: str, parameters: List[str]):
        self.name = name
        self.expression = expression
        self.parameters = parameters
        self.constraint_type = 'equality'  # 'equality', 'inequality', 'optimization'
        self.biological_significance = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'expression': self.expression,
            'parameters': self.parameters,
            'constraint_type': self.constraint_type,
            'biological_significance': self.biological_significance
        }

class SysMLBlock(UMLElement):
    """SysML块元素"""
    
    def __init__(self, properties: Dict[str, Any]):
        super().__init__('Block', properties)
        self.value_properties = []
        self.flow_properties = []
        self.constraint_properties = []
        self.part_properties = []
        self.reference_properties = []
        
        # 生物医学专用属性
        self.biological_functions = properties.get('biological_functions', [])
        self.interaction_interfaces = properties.get('interaction_interfaces', [])
        self.regulatory_mechanisms = properties.get('regulatory_mechanisms', [])
        
        # 设置SysML构造型
        if not self.stereotype:
            self.stereotype = '«block»'
    
    def add_value_property(self, name: str, type_name: str, unit: Optional[str] = None):
        """添加值属性"""
        value_prop = ValueProperty(name, type_name, unit)
        self.value_properties.append(value_prop)
        self.updated_at = datetime.now()
    
    def add_flow_property(self, name: str, type_name: str, direction: str):
        """添加流属性"""
        flow_prop = FlowProperty(name, type_name, direction)
        self.flow_properties.append(flow_prop)
        self.updated_at = datetime.now()
    
    def add_constraint(self, name: str, expression: str, parameters: List[str]):
        """添加约束"""
        constraint = ConstraintProperty(name, expression, parameters)
        self.constraint_properties.append(constraint)
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        base_dict = super().to_dict()
        base_dict.update({
            'value_properties': [vp.to_dict() for vp in self.value_properties],
            'flow_properties': [fp.to_dict() for fp in self.flow_properties],
            'constraint_properties': [cp.to_dict() for cp in self.constraint_properties],
            'biological_functions': self.biological_functions,
            'interaction_interfaces': self.interaction_interfaces,
            'regulatory_mechanisms': self.regulatory_mechanisms
        })
        return base_dict

class SysMLRequirement(UMLElement):
    """SysML需求元素"""
    
    def __init__(self, properties: Dict[str, Any]):
        super().__init__('Requirement', properties)
        self.text = properties.get('text', '')
        self.id_text = properties.get('id_text', '')
        self.requirement_type = properties.get('requirement_type', 'functional')
        self.priority = properties.get('priority', 'medium')
        self.status = properties.get('status', 'draft')
        self.verification_method = properties.get('verification_method', 'analysis')
        
        # 生物医学需求特化
        self.biological_context = properties.get('biological_context', {})
        self.regulatory_compliance = properties.get('regulatory_compliance', [])
        
        if not self.stereotype:
            self.stereotype = '«requirement»'
    
    def to_dict(self) -> Dict[str, Any]:
        base_dict = super().to_dict()
        base_dict.update({
            'text': self.text,
            'id_text': self.id_text,
            'requirement_type': self.requirement_type,
            'priority': self.priority,
            'status': self.status,
            'verification_method': self.verification_method,
            'biological_context': self.biological_context,
            'regulatory_compliance': self.regulatory_compliance
        })
        return base_dict

class SysMLEngine:
    """SysML扩展引擎"""
    
    def __init__(self, uml_engine: UMLEngine):
        self.uml_engine = uml_engine
        self.constraint_solver = SysMLConstraintSolver()
        self.block_factory = SysMLBlockFactory()
        self.requirement_manager = SysMLRequirementManager()
    
    def create_diagram(self, diagram_type: str, name: str) -> UMLDiagram:
        """创建SysML图形"""
        diagram = self.uml_engine.create_diagram(diagram_type, name)
        
        # 为SysML图形添加特殊配置
        if diagram_type in [dt.value for dt in SysMLDiagramType]:
            diagram.metadata['is_sysml'] = True
            diagram.metadata['sysml_version'] = '1.6'
        
        return diagram
    
    def create_block(self, name: str, biological_type: Optional[str] = None) -> SysMLBlock:
        """创建SysML块"""
        properties = {
            'name': name,
            'biological_type': biological_type
        }
        
        block = SysMLBlock(properties)
        
        # 如果是生物医学块，应用专业化配置
        if biological_type:
            self._apply_biological_specialization(block, biological_type)
        
        return block
    
    def create_requirement(self, name: str, text: str, **kwargs) -> SysMLRequirement:
        """创建SysML需求"""
        properties = {
            'name': name,
            'text': text,
            **kwargs
        }
        
        return SysMLRequirement(properties)
    
    def solve_constraints(self, block: SysMLBlock, constraint_values: Dict[str, Any]) -> Dict[str, Any]:
        """求解块约束"""
        return self.constraint_solver.solve_block_constraints(block, constraint_values)
    
    def _apply_biological_specialization(self, block: SysMLBlock, biological_type: str):
        """应用生物学专业化"""
        if biological_type == 'protein':
            self._configure_protein_block(block)
        elif biological_type == 'gene':
            self._configure_gene_block(block)
        elif biological_type == 'pathway':
            self._configure_pathway_block(block)
        elif biological_type == 'cell':
            self._configure_cell_block(block)
    
    def _configure_protein_block(self, block: SysMLBlock):
        """配置蛋白质块"""
        # 标准蛋白质值属性
        block.add_value_property('sequence', 'String', 'amino_acids')
        block.add_value_property('molecular_weight', 'Real', 'kDa')
        block.add_value_property('isoelectric_point', 'Real', 'pH')
        block.add_value_property('stability_score', 'Real', 'kcal_mol')
        
        # 流属性（蛋白质相互作用）
        block.add_flow_property('substrate_binding', 'BiomoleculeFlow', 'in')
        block.add_flow_property('product_release', 'BiomoleculeFlow', 'out')
        block.add_flow_property('allosteric_regulation', 'RegulatoryFlow', 'inout')
        
        # 约束（Michaelis-Menten动力学）
        block.add_constraint(
            'michaelis_menten',
            'reaction_rate = (Vmax * substrate) / (Km + substrate)',
            ['Vmax', 'Km', 'substrate', 'reaction_rate']
        )
        
        # 生物学功能
        block.biological_functions = [{
            'function_id': 'catalytic_activity',
            'function_name': 'Catalytic Activity',
            'mechanism': 'enzyme_kinetics',
            'regulation_level': 'post_translational'
        }]
        
        block.stereotype = '«protein»'
    
    def _configure_gene_block(self, block: SysMLBlock):
        """配置基因块"""
        # 基因特有属性
        block.add_value_property('sequence', 'String', 'nucleotides')
        block.add_value_property('length', 'Integer', 'bp')
        block.add_value_property('gc_content', 'Real', 'percentage')
        block.add_value_property('expression_level', 'Real', 'FPKM')
        
        # 流属性
        block.add_flow_property('transcription_input', 'TranscriptionFactorFlow', 'in')
        block.add_flow_property('mrna_output', 'RNAFlow', 'out')
        
        # 约束
        block.add_constraint(
            'expression_regulation',
            'expression_level = basal_rate * regulation_factor',
            ['basal_rate', 'regulation_factor', 'expression_level']
        )
        
        block.stereotype = '«gene»'
    
    def _configure_pathway_block(self, block: SysMLBlock):
        """配置通路块"""
        # 通路级别属性
        block.add_value_property('pathway_activity', 'Real', 'normalized')
        block.add_value_property('flux_rate', 'Real', 'mmol_h')
        block.add_value_property('regulation_state', 'Enumeration', 'categorical')
        
        # 流属性
        block.add_flow_property('substrate_input', 'MetaboliteFlow', 'in')
        block.add_flow_property('product_output', 'MetaboliteFlow', 'out')
        block.add_flow_property('energy_requirement', 'EnergyFlow', 'in')
        
        # 通路平衡约束
        block.add_constraint(
            'flux_balance',
            'sum(input_fluxes) = sum(output_fluxes)',
            ['input_fluxes', 'output_fluxes']
        )
        
        block.stereotype = '«pathway»'
    
    def _configure_cell_block(self, block: SysMLBlock):
        """配置细胞块"""
        # 细胞级别属性
        block.add_value_property('cell_volume', 'Real', 'um3')
        block.add_value_property('membrane_potential', 'Real', 'mV')
        block.add_value_property('viability', 'Real', 'percentage')
        
        # 流属性
        block.add_flow_property('nutrient_uptake', 'NutrientFlow', 'in')
        block.add_flow_property('waste_excretion', 'WasteFlow', 'out')
        block.add_flow_property('signal_transduction', 'SignalFlow', 'inout')
        
        block.stereotype = '«cell»'

class SysMLConstraintSolver:
    """SysML约束求解器"""
    
    def solve_block_constraints(self, block: SysMLBlock, values: Dict[str, Any]) -> Dict[str, Any]:
        """求解块约束"""
        results = {
            'solved_parameters': {},
            'constraint_violations': [],
            'optimization_suggestions': []
        }
        
        for constraint in block.constraint_properties:
            try:
                # 简化的约束求解逻辑
                if constraint.constraint_type == 'equality':
                    solved_values = self._solve_equality_constraint(constraint, values)
                    results['solved_parameters'].update(solved_values)
                elif constraint.constraint_type == 'inequality':
                    violations = self._check_inequality_constraint(constraint, values)
                    results['constraint_violations'].extend(violations)
                
            except Exception as e:
                results['constraint_violations'].append({
                    'constraint': constraint.name,
                    'error': str(e)
                })
        
        return results
    
    def _solve_equality_constraint(self, constraint: ConstraintProperty, values: Dict[str, Any]) -> Dict[str, Any]:
        """求解等式约束"""
        # 简化实现，实际应该使用符号计算
        solved = {}
        
        if constraint.name == 'michaelis_menten':
            # 求解Michaelis-Menten方程
            if 'Vmax' in values and 'Km' in values and 'substrate' in values:
                Vmax = values['Vmax']
                Km = values['Km']
                substrate = values['substrate']
                reaction_rate = (Vmax * substrate) / (Km + substrate)
                solved['reaction_rate'] = reaction_rate
        
        return solved
    
    def _check_inequality_constraint(self, constraint: ConstraintProperty, values: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查不等式约束"""
        violations = []
        # 约束检查逻辑
        return violations

class SysMLBlockFactory:
    """SysML块工厂"""
    
    def create_specialized_block(self, block_type: str, properties: Dict[str, Any]) -> SysMLBlock:
        """创建专业化块"""
        block = SysMLBlock(properties)
        
        # 根据类型应用专业化
        specialization_configs = {
            'protein': self._protein_specialization,
            'gene': self._gene_specialization,
            'pathway': self._pathway_specialization,
            'cell': self._cell_specialization
        }
        
        if block_type in specialization_configs:
            specialization_configs[block_type](block)
        
        return block

class SysMLRequirementManager:
    """SysML需求管理器"""
    
    def __init__(self):
        self.requirements = {}
        self.requirement_relationships = []
    
    def create_biomedical_requirement(self, req_type: str, properties: Dict[str, Any]) -> SysMLRequirement:
        """创建生物医学需求"""
        
        # 生物医学需求模板
        biomedical_templates = {
            'regulatory': {
                'requirement_type': 'regulatory',
                'verification_method': 'inspection',
                'regulatory_compliance': ['FDA', 'EMA']
            },
            'performance': {
                'requirement_type': 'performance', 
                'verification_method': 'test',
                'biological_context': {'domain': 'efficacy'}
            },
            'safety': {
                'requirement_type': 'safety',
                'verification_method': 'analysis',
                'biological_context': {'domain': 'toxicity'}
            }
        }
        
        template = biomedical_templates.get(req_type, {})
        merged_properties = {**template, **properties}
        
        requirement = SysMLRequirement(merged_properties)
        self.requirements[requirement.id] = requirement
        
        return requirement 