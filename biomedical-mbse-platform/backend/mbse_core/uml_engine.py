"""
UML核心建模引擎
=============

基于UML 2.5标准的核心建模引擎实现
"""

from typing import Dict, List, Any, Optional, Union
import uuid
from datetime import datetime
from enum import Enum
import json

class UMLElementType(Enum):
    """UML元素类型"""
    CLASS = "Class"
    INTERFACE = "Interface"
    COMPONENT = "Component"
    PACKAGE = "Package"
    ACTOR = "Actor"
    USECASE = "UseCase"
    STATE = "State"
    ACTIVITY = "Activity"

class UMLDiagramType(Enum):
    """UML图形类型"""
    CLASS_DIAGRAM = "ClassDiagram"
    COMPONENT_DIAGRAM = "ComponentDiagram"
    PACKAGE_DIAGRAM = "PackageDiagram"
    USECASE_DIAGRAM = "UseCaseDiagram"
    SEQUENCE_DIAGRAM = "SequenceDiagram"
    ACTIVITY_DIAGRAM = "ActivityDiagram"
    STATE_DIAGRAM = "StateDiagram"
    DEPLOYMENT_DIAGRAM = "DeploymentDiagram"

class UMLElement:
    """UML元素基类"""
    
    def __init__(self, element_type: str, properties: Dict[str, Any]):
        self.id = properties.get('id', str(uuid.uuid4()))
        self.name = properties.get('name', '')
        self.element_type = element_type
        self.stereotype = properties.get('stereotype')
        self.tagged_values = properties.get('tagged_values', {})
        self.documentation = properties.get('documentation', '')
        
        # 视觉属性
        self.visual_properties = {
            'x': properties.get('x', 0),
            'y': properties.get('y', 0),
            'width': properties.get('width', 100),
            'height': properties.get('height', 50),
            'fill_color': properties.get('fill_color', '#ffffff'),
            'stroke_color': properties.get('stroke_color', '#000000'),
            'stroke_width': properties.get('stroke_width', 1)
        }
        
        # 生物医学扩展属性
        self.biological_type = properties.get('biological_type')
        self.biological_annotations = properties.get('biological_annotations', {})
        
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'element_type': self.element_type,
            'stereotype': self.stereotype,
            'tagged_values': self.tagged_values,
            'documentation': self.documentation,
            'visual_properties': self.visual_properties,
            'biological_type': self.biological_type,
            'biological_annotations': self.biological_annotations,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def validate(self) -> List[str]:
        """验证元素"""
        violations = []
        if not self.name:
            violations.append(f"Element {self.id} missing name")
        return violations

class UMLClass(UMLElement):
    """UML类元素"""
    
    def __init__(self, properties: Dict[str, Any]):
        super().__init__(UMLElementType.CLASS.value, properties)
        self.attributes = properties.get('attributes', [])
        self.operations = properties.get('operations', [])
        self.is_abstract = properties.get('is_abstract', False)
        self.visibility = properties.get('visibility', 'public')
    
    def add_attribute(self, name: str, type_name: str, visibility: str = 'private'):
        """添加属性"""
        attribute = {
            'name': name,
            'type': type_name,
            'visibility': visibility,
            'multiplicity': '1',
            'default_value': None
        }
        self.attributes.append(attribute)
        self.updated_at = datetime.now()
    
    def add_operation(self, name: str, return_type: str = 'void', parameters: List[Dict] = None):
        """添加操作"""
        operation = {
            'name': name,
            'return_type': return_type,
            'parameters': parameters or [],
            'visibility': 'public',
            'is_static': False,
            'is_abstract': False
        }
        self.operations.append(operation)
        self.updated_at = datetime.now()

class UMLDiagram:
    """UML图形"""
    
    def __init__(self, diagram_type: str, name: str):
        self.id = str(uuid.uuid4())
        self.name = name
        self.diagram_type = diagram_type
        self.elements = []
        self.relationships = []
        self.documentation = ''
        self.metadata = {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def add_element(self, element: UMLElement):
        """添加元素"""
        self.elements.append(element)
        self.updated_at = datetime.now()
    
    def remove_element(self, element_id: str):
        """移除元素"""
        self.elements = [e for e in self.elements if e.id != element_id]
        self.updated_at = datetime.now()
    
    def get_element(self, element_id: str) -> Optional[UMLElement]:
        """获取元素"""
        for element in self.elements:
            if element.id == element_id:
                return element
        return None
    
    def validate(self) -> List[str]:
        """验证图形"""
        violations = []
        
        # 验证图形名称
        if not self.name:
            violations.append("Diagram missing name")
        
        # 验证元素
        for element in self.elements:
            element_violations = element.validate()
            violations.extend(element_violations)
        
        return violations
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'diagram_type': self.diagram_type,
            'elements': [e.to_dict() for e in self.elements],
            'relationships': self.relationships,
            'documentation': self.documentation,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class UMLEngine:
    """UML核心引擎"""
    
    def __init__(self):
        self.diagrams = {}
        self.element_factory = UMLElementFactory()
        self.diagram_factory = UMLDiagramFactory()
        self.validator = UMLValidator()
    
    def create_diagram(self, diagram_type: str, name: str) -> UMLDiagram:
        """创建图形"""
        diagram = self.diagram_factory.create_diagram(diagram_type, name)
        self.diagrams[diagram.id] = diagram
        return diagram
    
    def create_element(self, element_type: str, properties: Dict[str, Any]) -> UMLElement:
        """创建元素"""
        return self.element_factory.create_element(element_type, properties)
    
    def get_diagram(self, diagram_id: str) -> Optional[UMLDiagram]:
        """获取图形"""
        return self.diagrams.get(diagram_id)
    
    def validate_diagram(self, diagram: UMLDiagram) -> Dict[str, Any]:
        """验证图形"""
        violations = self.validator.validate_diagram(diagram)
        return {
            'is_valid': len(violations) == 0,
            'violations': violations
        }

class UMLElementFactory:
    """UML元素工厂"""
    
    def create_element(self, element_type: str, properties: Dict[str, Any]) -> UMLElement:
        """创建元素"""
        element_classes = {
            UMLElementType.CLASS.value: UMLClass,
            # 其他元素类型可以继续添加
        }
        
        element_class = element_classes.get(element_type, UMLElement)
        return element_class(properties)

class UMLDiagramFactory:
    """UML图形工厂"""
    
    def create_diagram(self, diagram_type: str, name: str) -> UMLDiagram:
        """创建图形"""
        return UMLDiagram(diagram_type, name)

class UMLValidator:
    """UML验证器"""
    
    def validate_diagram(self, diagram: UMLDiagram) -> List[str]:
        """验证图形"""
        return diagram.validate() 