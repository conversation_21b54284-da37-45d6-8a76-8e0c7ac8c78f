"""
UML建模系统与XML解析集成模块
=================================

将现有的XML解析能力与UML/SysML建模系统深度集成，
支持XMI、EA项目文件、ArchiMate等多种UML建模格式的导入导出。
"""

from typing import Dict, List, Any, Optional, Union
import xml.etree.ElementTree as ET
import uuid
import asyncio
from datetime import datetime
import logging

# 导入现有的XML解析器
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core', 'parsing'))
from xml_parser import UnifiedXMLParser

# 导入UML引擎
from .uml_engine import UMLEngine, UMLElement, UMLDiagram, UMLClass
from .sysml_engine import SysMLEngine, SysMLBlock, SysMLRequirement

logger = logging.getLogger(__name__)

class UMLXMLIntegration:
    """UML建模系统与XML解析集成器"""
    
    def __init__(self):
        # 初始化组件
        self.xml_parser = UnifiedXMLParser({
            'enable_semantic_analysis': True,
            'enable_reference_analysis': True,
            'enable_parallel_processing': True
        })
        self.uml_engine = UMLEngine()
        self.sysml_engine = SysMLEngine(self.uml_engine)
        
        # XMI命名空间映射
        self.xmi_namespaces = {
            'xmi': 'http://www.omg.org/XMI',
            'uml': 'http://www.eclipse.org/uml2/5.0.0/UML',
            'sysml': 'http://www.eclipse.org/papyrus/sysml/1.6',
            'notation': 'http://www.eclipse.org/gmf/runtime/1.0.2/notation'
        }
        
        # 元素类型映射
        self.uml_type_mapping = {
            'uml:Class': 'Class',
            'uml:Interface': 'Interface',
            'uml:Package': 'Package',
            'uml:Component': 'Component',
            'uml:Actor': 'Actor',
            'uml:UseCase': 'UseCase',
            'uml:Activity': 'Activity',
            'uml:State': 'State',
            'sysml:Block': 'Block',
            'sysml:Requirement': 'Requirement',
            'sysml:ConstraintBlock': 'ConstraintBlock'
        }
        
        logger.info("UML-XML集成模块初始化完成")
    
    async def import_xmi_file(self, xmi_file_path: str, project_id: str = None) -> Dict[str, Any]:
        """
        导入XMI文件到UML模型
        
        Args:
            xmi_file_path: XMI文件路径
            project_id: 目标项目ID
            
        Returns:
            导入结果
        """
        try:
            # 1. 使用XML解析器解析XMI文件
            parse_result = await self.xml_parser.parse(xmi_file_path)
            
            if not parse_result['success']:
                raise ValueError(f"XMI文件解析失败: {parse_result.get('error')}")
            
            # 2. 提取解析数据
            xml_data = parse_result['data']
            metadata_store = xml_data['metadata_store']
            
            # 3. 转换为UML模型
            conversion_result = await self._convert_xml_to_uml_model(metadata_store, project_id)
            
            # 4. 生成导入报告
            import_report = self._generate_import_report(parse_result, conversion_result)
            
            return {
                'success': True,
                'project_id': project_id or conversion_result.get('project_id'),
                'diagrams': conversion_result['diagrams'],
                'elements_imported': conversion_result['elements_count'],
                'relationships_imported': conversion_result['relationships_count'],
                'import_report': import_report,
                'xml_parsing_metrics': parse_result['metrics']
            }
            
        except Exception as e:
            logger.error(f"XMI导入失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _convert_xml_to_uml_model(self, metadata_store: Dict[str, Any], 
                                       project_id: str = None) -> Dict[str, Any]:
        """将XML元数据转换为UML模型"""
        
        # 创建项目
        if not project_id:
            project_id = f"imported_project_{uuid.uuid4().hex[:8]}"
        
        # 转换结果统计
        diagrams = []
        elements_count = 0
        relationships_count = 0
        
        # 1. 分析XML结构，识别图形和元素
        diagram_groups = self._group_elements_by_diagram(metadata_store)
        
        # 2. 为每个图形组创建UML图形
        for diagram_info in diagram_groups:
            diagram = await self._create_uml_diagram_from_group(diagram_info, metadata_store)
            if diagram:
                diagrams.append(diagram)
                elements_count += len(diagram.elements)
                relationships_count += len(diagram.relationships)
        
        return {
            'project_id': project_id,
            'diagrams': diagrams,
            'elements_count': elements_count,
            'relationships_count': relationships_count
        }
    
    def _group_elements_by_diagram(self, metadata_store: Dict[str, Any]) -> List[Dict[str, Any]]:
        """将XML元素按图形分组"""
        diagram_groups = []
        current_diagram = None
        
        for element_id, metadata in metadata_store.items():
            element_type = metadata.get('tag', '')
            
            # 检查是否为图形定义
            if self._is_diagram_element(element_type, metadata):
                # 保存之前的图形
                if current_diagram and current_diagram['elements']:
                    diagram_groups.append(current_diagram)
                
                # 开始新图形
                current_diagram = {
                    'diagram_id': element_id,
                    'diagram_type': self._infer_diagram_type(element_type, metadata),
                    'diagram_name': metadata.get('attributes', {}).get('name', f'Diagram_{len(diagram_groups) + 1}'),
                    'elements': [],
                    'relationships': []
                }
            
            # 检查是否为UML元素
            elif self._is_uml_element(element_type, metadata):
                if not current_diagram:
                    # 创建默认图形
                    current_diagram = {
                        'diagram_id': f"default_diagram_{uuid.uuid4().hex[:8]}",
                        'diagram_type': 'ClassDiagram',
                        'diagram_name': 'Imported Elements',
                        'elements': [],
                        'relationships': []
                    }
                
                current_diagram['elements'].append({
                    'element_id': element_id,
                    'metadata': metadata
                })
            
            # 检查是否为关系
            elif self._is_relationship_element(element_type, metadata):
                if current_diagram:
                    current_diagram['relationships'].append({
                        'relationship_id': element_id,
                        'metadata': metadata
                    })
        
        # 添加最后一个图形
        if current_diagram and current_diagram['elements']:
            diagram_groups.append(current_diagram)
        
        return diagram_groups
    
    def _is_diagram_element(self, element_type: str, metadata: Dict[str, Any]) -> bool:
        """判断是否为图形元素"""
        diagram_keywords = [
            'diagram', 'view', 'model', 'package',
            'Diagram', 'View', 'Model', 'Package'
        ]
        
        # 检查元素类型
        for keyword in diagram_keywords:
            if keyword.lower() in element_type.lower():
                return True
        
        # 检查语义分析结果
        semantic_info = metadata.get('semantic_analysis', {})
        if semantic_info.get('domain_classification') in ['package', 'model']:
            return True
        
        return False
    
    def _is_uml_element(self, element_type: str, metadata: Dict[str, Any]) -> bool:
        """判断是否为UML元素"""
        # 检查已知的UML类型
        if element_type in self.uml_type_mapping:
            return True
        
        # 检查语义分析结果
        semantic_info = metadata.get('semantic_analysis', {})
        domain = semantic_info.get('domain_classification', '')
        
        uml_domains = ['block', 'requirement', 'constraint', 'parameter', 'interface', 'state']
        return domain in uml_domains
    
    def _is_relationship_element(self, element_type: str, metadata: Dict[str, Any]) -> bool:
        """判断是否为关系元素"""
        relationship_keywords = [
            'association', 'generalization', 'dependency', 'realization',
            'connector', 'link', 'edge', 'relationship'
        ]
        
        for keyword in relationship_keywords:
            if keyword.lower() in element_type.lower():
                return True
        
        return False
    
    def _infer_diagram_type(self, element_type: str, metadata: Dict[str, Any]) -> str:
        """推断图形类型"""
        # 基于元素类型推断
        type_mapping = {
            'class': 'ClassDiagram',
            'component': 'ComponentDiagram', 
            'package': 'PackageDiagram',
            'usecase': 'UseCaseDiagram',
            'activity': 'ActivityDiagram',
            'state': 'StateDiagram',
            'sequence': 'SequenceDiagram',
            'block': 'BlockDefinitionDiagram',
            'requirement': 'RequirementsDiagram'
        }
        
        element_type_lower = element_type.lower()
        for keyword, diagram_type in type_mapping.items():
            if keyword in element_type_lower:
                return diagram_type
        
        # 检查语义分析
        semantic_info = metadata.get('semantic_analysis', {})
        domain = semantic_info.get('domain_classification', '')
        
        if domain in ['block', 'requirement']:
            return 'BlockDefinitionDiagram'
        elif domain in ['activity', 'behavior']:
            return 'ActivityDiagram'
        elif domain in ['state']:
            return 'StateDiagram'
        
        return 'ClassDiagram'  # 默认
    
    async def _create_uml_diagram_from_group(self, diagram_info: Dict[str, Any], 
                                           metadata_store: Dict[str, Any]) -> Optional[UMLDiagram]:
        """从元素组创建UML图形"""
        try:
            diagram_type = diagram_info['diagram_type']
            diagram_name = diagram_info['diagram_name']
            
            # 创建图形
            if diagram_type.startswith('Block') or diagram_type == 'RequirementsDiagram':
                # SysML图形
                diagram = self.sysml_engine.create_diagram(diagram_type, diagram_name)
            else:
                # UML图形
                diagram = self.uml_engine.create_diagram(diagram_type, diagram_name)
            
            # 添加元素
            for element_info in diagram_info['elements']:
                element = await self._create_uml_element_from_metadata(
                    element_info['metadata'], 
                    diagram_type
                )
                if element:
                    diagram.add_element(element)
            
            # 添加关系
            for relationship_info in diagram_info['relationships']:
                relationship = self._create_uml_relationship_from_metadata(
                    relationship_info['metadata'],
                    diagram.elements
                )
                if relationship:
                    diagram.relationships.append(relationship)
            
            return diagram
            
        except Exception as e:
            logger.error(f"创建UML图形失败: {e}")
            return None
    
    async def _create_uml_element_from_metadata(self, metadata: Dict[str, Any], 
                                              diagram_type: str) -> Optional[UMLElement]:
        """从XML元数据创建UML元素"""
        try:
            element_type = metadata.get('tag', '')
            attributes = metadata.get('attributes', {})
            semantic_info = metadata.get('semantic_analysis', {})
            
            # 确定元素类型
            uml_type = self._map_xml_to_uml_type(element_type, semantic_info)
            
            # 创建基础属性
            properties = {
                'name': attributes.get('name', f'Element_{uuid.uuid4().hex[:8]}'),
                'id': metadata.get('unified_id', str(uuid.uuid4())),
                'documentation': metadata.get('text_content', ''),
                'stereotype': attributes.get('stereotype'),
                'tagged_values': self._extract_tagged_values(attributes)
            }
            
            # 添加视觉属性（如果有）
            properties.update(self._extract_visual_properties(attributes))
            
            # 添加生物医学属性（如果适用）
            properties.update(self._extract_biomedical_properties(attributes, semantic_info))
            
            # 根据类型创建元素
            if uml_type == 'Class':
                element = UMLClass(properties)
                # 添加属性和操作
                self._add_class_members(element, metadata)
                
            elif uml_type == 'Block':
                element = self.sysml_engine.create_block(
                    properties['name'],
                    properties.get('biological_type')
                )
                # 更新元素属性
                for key, value in properties.items():
                    if hasattr(element, key):
                        setattr(element, key, value)
                        
            elif uml_type == 'Requirement':
                element = self.sysml_engine.create_requirement(
                    properties['name'],
                    properties.get('documentation', ''),
                    **properties
                )
                
            else:
                # 创建通用UML元素
                element = self.uml_engine.create_element(uml_type, properties)
            
            return element
            
        except Exception as e:
            logger.error(f"创建UML元素失败: {e}")
            return None
    
    def _map_xml_to_uml_type(self, xml_type: str, semantic_info: Dict[str, Any]) -> str:
        """将XML类型映射到UML类型"""
        # 直接映射
        if xml_type in self.uml_type_mapping:
            return self.uml_type_mapping[xml_type]
        
        # 基于语义分析
        domain = semantic_info.get('domain_classification', '')
        domain_to_uml = {
            'block': 'Block',
            'requirement': 'Requirement',
            'constraint': 'ConstraintBlock',
            'parameter': 'Block',
            'interface': 'Interface',
            'state': 'State',
            'activity': 'Activity',
            'use_case': 'UseCase'
        }
        
        if domain in domain_to_uml:
            return domain_to_uml[domain]
        
        # 默认为Class
        return 'Class'
    
    def _extract_tagged_values(self, attributes: Dict[str, Any]) -> Dict[str, Any]:
        """提取标记值"""
        tagged_values = {}
        
        # 查找特定的标记值属性
        for key, value in attributes.items():
            if key.startswith('tag_') or key.startswith('property_'):
                clean_key = key.replace('tag_', '').replace('property_', '')
                tagged_values[clean_key] = value
        
        return tagged_values
    
    def _extract_visual_properties(self, attributes: Dict[str, Any]) -> Dict[str, Any]:
        """提取视觉属性"""
        visual_props = {
            'x': 0,
            'y': 0, 
            'width': 100,
            'height': 50,
            'fill_color': '#ffffff',
            'stroke_color': '#000000',
            'stroke_width': 1
        }
        
        # 查找位置和尺寸信息
        if 'x' in attributes:
            visual_props['x'] = float(attributes['x'])
        if 'y' in attributes:
            visual_props['y'] = float(attributes['y'])
        if 'width' in attributes:
            visual_props['width'] = float(attributes['width'])
        if 'height' in attributes:
            visual_props['height'] = float(attributes['height'])
        
        # 查找颜色信息
        if 'color' in attributes:
            visual_props['fill_color'] = attributes['color']
        if 'strokeColor' in attributes:
            visual_props['stroke_color'] = attributes['strokeColor']
        if 'strokeWidth' in attributes:
            visual_props['stroke_width'] = float(attributes['strokeWidth'])
        
        return {'visual_properties': visual_props}
    
    def _extract_biomedical_properties(self, attributes: Dict[str, Any], 
                                     semantic_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取生物医学属性"""
        biomedical_props = {}
        
        # 检查是否为生物医学元素
        bio_keywords = ['protein', 'gene', 'pathway', 'cell', 'enzyme', 'metabolite']
        element_name = attributes.get('name', '').lower()
        
        for keyword in bio_keywords:
            if keyword in element_name:
                biomedical_props['biological_type'] = keyword
                break
        
        # 从语义分析中提取
        if semantic_info.get('biological_context'):
            biomedical_props['biological_annotations'] = semantic_info['biological_context']
        
        return biomedical_props
    
    def _add_class_members(self, uml_class: UMLClass, metadata: Dict[str, Any]):
        """为UML类添加属性和操作"""
        # 从子元素中查找属性和操作
        child_elements = metadata.get('children', [])
        
        for child_id in child_elements:
            # 这里需要访问完整的metadata_store来获取子元素信息
            # 简化处理：基于属性名称推断
            pass
    
    def _create_uml_relationship_from_metadata(self, metadata: Dict[str, Any], 
                                             elements: List[UMLElement]) -> Optional[Dict[str, Any]]:
        """从XML元数据创建UML关系"""
        try:
            attributes = metadata.get('attributes', {})
            
            # 查找源和目标元素
            source_id = attributes.get('source') or attributes.get('from')
            target_id = attributes.get('target') or attributes.get('to')
            
            if not source_id or not target_id:
                return None
            
            # 查找对应的元素
            source_element = None
            target_element = None
            
            for element in elements:
                if element.id == source_id:
                    source_element = element
                elif element.id == target_id:
                    target_element = element
            
            if not source_element or not target_element:
                return None
            
            # 创建关系
            relationship = {
                'id': metadata.get('unified_id', str(uuid.uuid4())),
                'name': attributes.get('name', ''),
                'relationship_type': self._infer_relationship_type(metadata),
                'source_id': source_element.id,
                'target_id': target_element.id,
                'properties': attributes
            }
            
            return relationship
            
        except Exception as e:
            logger.error(f"创建UML关系失败: {e}")
            return None
    
    def _infer_relationship_type(self, metadata: Dict[str, Any]) -> str:
        """推断关系类型"""
        element_type = metadata.get('tag', '').lower()
        
        type_mapping = {
            'association': 'Association',
            'generalization': 'Generalization',
            'dependency': 'Dependency',
            'realization': 'Realization',
            'composition': 'Composition',
            'aggregation': 'Aggregation'
        }
        
        for keyword, relationship_type in type_mapping.items():
            if keyword in element_type:
                return relationship_type
        
        return 'Association'  # 默认
    
    def _generate_import_report(self, parse_result: Dict[str, Any], 
                               conversion_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成导入报告"""
        return {
            'import_timestamp': datetime.now().isoformat(),
            'xml_parsing': {
                'elements_parsed': parse_result['data']['analysis_summary']['total_elements'],
                'processing_time': parse_result['metrics']['total_parsing_time'],
                'processing_speed': parse_result['metrics']['processing_speed']
            },
            'uml_conversion': {
                'diagrams_created': len(conversion_result['diagrams']),
                'elements_created': conversion_result['elements_count'],
                'relationships_created': conversion_result['relationships_count']
            },
            'success_rate': {
                'elements': conversion_result['elements_count'] / max(1, parse_result['data']['analysis_summary']['total_elements']),
                'overall': 'successful'
            }
        }
    
    async def export_to_xmi(self, project_id: str, diagrams: List[UMLDiagram]) -> str:
        """导出UML模型到XMI格式"""
        try:
            # 创建XMI根元素
            root = ET.Element('xmi:XMI')
            root.set('xmi:version', '2.0')
            root.set('xmlns:xmi', self.xmi_namespaces['xmi'])
            root.set('xmlns:uml', self.xmi_namespaces['uml'])
            root.set('xmlns:sysml', self.xmi_namespaces['sysml'])
            
            # 创建模型元素
            model = ET.SubElement(root, 'uml:Model')
            model.set('xmi:id', project_id)
            model.set('name', f'Project_{project_id}')
            
            # 导出每个图形
            for diagram in diagrams:
                await self._export_diagram_to_xmi(diagram, model)
            
            # 生成XML字符串
            ET.indent(root, space="  ", level=0)
            return ET.tostring(root, encoding='unicode')
            
        except Exception as e:
            logger.error(f"XMI导出失败: {e}")
            raise
    
    async def _export_diagram_to_xmi(self, diagram: UMLDiagram, parent: ET.Element):
        """将图形导出到XMI"""
        # 创建图形包
        package = ET.SubElement(parent, 'packagedElement')
        package.set('xmi:type', 'uml:Package')
        package.set('xmi:id', diagram.id)
        package.set('name', diagram.name)
        
        # 导出元素
        for element in diagram.elements:
            await self._export_element_to_xmi(element, package)
        
        # 导出关系
        for relationship in diagram.relationships:
            self._export_relationship_to_xmi(relationship, package)
    
    async def _export_element_to_xmi(self, element: UMLElement, parent: ET.Element):
        """将元素导出到XMI"""
        # 确定XMI类型
        xmi_type = self._get_xmi_type_for_element(element)
        
        # 创建元素
        elem = ET.SubElement(parent, 'packagedElement')
        elem.set('xmi:type', xmi_type)
        elem.set('xmi:id', element.id)
        elem.set('name', element.name)
        
        # 添加文档
        if element.documentation:
            comment = ET.SubElement(elem, 'ownedComment')
            comment.set('xmi:type', 'uml:Comment')
            comment.set('body', element.documentation)
        
        # 添加标记值
        if element.tagged_values:
            for key, value in element.tagged_values.items():
                tag = ET.SubElement(elem, 'ownedAttribute')
                tag.set('name', key)
                tag.set('value', str(value))
    
    def _get_xmi_type_for_element(self, element: UMLElement) -> str:
        """获取元素的XMI类型"""
        type_mapping = {
            'Class': 'uml:Class',
            'Interface': 'uml:Interface',
            'Package': 'uml:Package',
            'Component': 'uml:Component',
            'Actor': 'uml:Actor',
            'UseCase': 'uml:UseCase',
            'Activity': 'uml:Activity',
            'State': 'uml:State',
            'Block': 'sysml:Block',
            'Requirement': 'sysml:Requirement'
        }
        
        return type_mapping.get(element.element_type, 'uml:Class')
    
    def _export_relationship_to_xmi(self, relationship: Dict[str, Any], parent: ET.Element):
        """将关系导出到XMI"""
        rel_elem = ET.SubElement(parent, 'packagedElement')
        rel_elem.set('xmi:type', f'uml:{relationship["relationship_type"]}')
        rel_elem.set('xmi:id', relationship['id'])
        rel_elem.set('name', relationship.get('name', ''))
        
        # 添加端点
        source_end = ET.SubElement(rel_elem, 'memberEnd')
        source_end.set('xmi:idref', relationship['source_id'])
        
        target_end = ET.SubElement(rel_elem, 'memberEnd')
        target_end.set('xmi:idref', relationship['target_id']) 