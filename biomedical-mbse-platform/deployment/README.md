# 本地部署指南

## 🎯 概述

本目录包含了在Windows本地环境部署生物医学MBSE平台的完整配置文件和脚本。

## 📋 前提条件

### 必需软件
1. **PostgreSQL 15+** - 数据库服务器
2. **Python 3.11+** - 后端运行环境
3. **Node.js 18+** - 前端构建环境（可选）
4. **Git** - 版本控制

### 推荐工具
- **pgAdmin 4** - PostgreSQL数据库管理工具
- **VS Code** - 代码编辑器
- **Windows Terminal** - 终端工具

## 🚀 快速开始

### 1. 一键部署（推荐）
```batch
# 在deployment目录下运行
deploy.bat
```

### 2. 手动部署
```batch
# 1. 安装PostgreSQL（如未安装）
install-postgresql.bat

# 2. 创建数据库和用户
setup-database.bat

# 3. 初始化数据库结构
init-database.bat

# 4. 设置Python环境
setup-python.bat

# 5. 启动服务
start-services.bat
```

## 📁 文件结构

```
deployment/
├── README.md                  # 本文档
├── deploy.bat                 # 一键部署脚本
├── config/                    # 配置文件
│   ├── database.yaml         # 数据库配置
│   ├── app.yaml              # 应用配置
│   └── .env.local            # 环境变量模板
├── scripts/                   # 部署脚本
│   ├── install-postgresql.bat # PostgreSQL安装
│   ├── setup-database.bat    # 数据库设置
│   ├── init-database.bat     # 数据库初始化
│   ├── setup-python.bat      # Python环境设置
│   └── start-services.bat    # 启动服务
├── sql/                       # SQL脚本
│   ├── create-database.sql   # 创建数据库
│   └── init-core-schema.sql  # 核心Schema初始化
└── logs/                      # 部署日志（自动生成）
```

## 🔧 配置说明

### 数据库配置
- **数据库名称**: `biomedical_mbse_platform`
- **用户**: `mbse_user`
- **端口**: `5432`（默认）
- **编码**: `UTF8`

### 应用配置
- **后端端口**: `8000`
- **前端端口**: `3000`（开发模式）
- **API地址**: `http://localhost:8000`

## 🐛 故障排除

### 常见问题

#### 1. PostgreSQL连接失败
```batch
# 检查服务状态
net start postgresql-x64-15

# 重启PostgreSQL服务
net stop postgresql-x64-15
net start postgresql-x64-15
```

#### 2. 端口被占用
```batch
# 查看端口占用情况
netstat -ano | findstr :5432
netstat -ano | findstr :8000

# 结束占用进程
taskkill /PID <进程ID> /F
```

#### 3. Python依赖安装失败
```batch
# 升级pip
python -m pip install --upgrade pip

# 清理缓存重新安装
pip cache purge
pip install -r requirements.txt
```

#### 4. 数据库权限问题
- 确保使用管理员权限运行脚本
- 检查PostgreSQL用户权限配置
- 验证pg_hba.conf配置

## 📈 验证部署

### 1. 数据库连接测试
```batch
cd scripts
test-database.bat
```

### 2. API服务测试
```batch
curl http://localhost:8000/health
```

### 3. 动态架构测试
```batch
cd scripts
test-dynamic-architecture.bat
```

## 🔄 日常维护

### 备份数据库
```batch
scripts\backup-database.bat
```

### 更新应用
```batch
scripts\update-application.bat
```

### 清理环境
```batch
scripts\cleanup.bat
```

## 📞 技术支持

如果遇到问题，请检查：
1. `logs/` 目录下的日志文件
2. PostgreSQL错误日志
3. Python应用日志

---

💡 **提示**: 首次部署建议使用`deploy.bat`一键部署脚本，它会自动检测环境并安装所需依赖。 