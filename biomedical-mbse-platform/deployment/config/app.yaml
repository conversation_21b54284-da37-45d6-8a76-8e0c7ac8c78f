# 应用配置文件
# 生物医学MBSE平台配置

application:
  name: "Biomedical MBSE Platform"
  version: "1.0.0"
  description: "基于动态Element架构的生物医学MBSE建模平台"
  
  # 服务配置
  host: "0.0.0.0"
  port: 8000
  debug: true
  reload: true
  
  # API配置
  api:
    prefix: "/api/v1"
    docs_url: "/docs"
    redoc_url: "/redoc"
    openapi_url: "/openapi.json"
    
  # 安全配置
  security:
    secret_key: "biomedical-mbse-platform-secret-key-2024"
    algorithm: "HS256"
    access_token_expire_minutes: 30
    refresh_token_expire_days: 7
    
  # CORS配置
  cors:
    allow_origins: 
      - "http://localhost:3000"
      - "http://127.0.0.1:3000"
      - "http://localhost:8080"
    allow_credentials: true
    allow_methods: ["*"]
    allow_headers: ["*"]

# 元素架构配置
element_architecture:
  # 核心配置
  core:
    default_namespace: "core"
    auto_register_metadata: true
    enable_semantic_tags: true
    
  # 动态Schema配置
  dynamic_schema:
    enabled: true
    auto_create_indexes: true
    optimize_queries: true
    
  # 跨域配置
  cross_domain:
    enabled: true
    auto_discover_relationships: true
    index_strategy: "adaptive"
    
# 领域配置
domains:
  # 安全领域
  security:
    enabled: true
    auto_create: true
    default_roles: ["user_default", "system_admin"]
    
  # 建模领域
  modeling:
    enabled: false
    auto_create: false
    
  # 生物医学领域
  biomedical:
    enabled: false
    auto_create: false

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 文件日志
  file:
    enabled: true
    path: "./logs/app.log"
    max_size: "10MB"
    backup_count: 5
    
  # 控制台日志
  console:
    enabled: true
    colored: true

# 性能配置
performance:
  # 数据库连接池
  db_pool_size: 20
  
  # 查询超时
  query_timeout: 30
  
  # 缓存配置
  cache:
    enabled: true
    ttl: 300
    max_size: 1000
    
# 开发工具
development:
  # 自动重载
  auto_reload: true
  
  # 调试模式
  debug_mode: true
  
  # 测试数据
  load_test_data: true
  
  # API文档
  enable_docs: true 