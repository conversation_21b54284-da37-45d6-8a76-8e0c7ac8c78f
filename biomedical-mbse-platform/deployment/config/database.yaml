# 数据库配置文件
# 用于本地开发和部署

database:
  # 主数据库配置
  host: localhost
  port: 5432
  name: biomedical_mbse_platform
  user: mbse_user
  password: mbse_pass_2024
  
  # 连接池配置
  pool:
    min_size: 5
    max_size: 20
    command_timeout: 60
    
  # SSL配置（本地开发可以禁用）
  ssl:
    enabled: false
    require: false
    
  # 字符编码
  encoding: UTF8
  timezone: Asia/Shanghai
  
  # 管理员账户（用于初始化）
  admin:
    user: postgres
    password: postgres
    
# 开发环境配置
development:
  debug: true
  log_level: DEBUG
  auto_migrate: true
  
# 测试环境配置  
test:
  database_name: biomedical_mbse_platform_test
  drop_on_start: true
  
# 备份配置
backup:
  enabled: true
  schedule: "0 2 * * *"  # 每天凌晨2点
  retention_days: 30
  path: "./backups" 