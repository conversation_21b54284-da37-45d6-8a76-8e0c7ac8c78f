# 本地开发环境变量配置模板
# 复制此文件为 .env 并根据需要修改配置

# ==============================================
# 数据库配置
# ==============================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=biomedical_mbse_platform
DB_USER=mbse_user
DB_PASSWORD=mbse_pass_2024
DB_POOL_MIN_SIZE=5
DB_POOL_MAX_SIZE=20

# PostgreSQL管理员账户（仅用于初始化）
DB_ADMIN_USER=postgres
DB_ADMIN_PASSWORD=postgres

# ==============================================
# 应用配置
# ==============================================
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=true
APP_RELOAD=true

# API配置
API_PREFIX=/api/v1
API_DOCS_URL=/docs
API_REDOC_URL=/redoc

# ==============================================
# 安全配置
# ==============================================
SECRET_KEY=biomedical-mbse-platform-secret-key-2024
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# ==============================================
# Element架构配置
# ==============================================
# 核心配置
ELEMENT_AUTO_REGISTER_METADATA=true
ELEMENT_ENABLE_SEMANTIC_TAGS=true

# 动态Schema配置
DYNAMIC_SCHEMA_ENABLED=true
DYNAMIC_SCHEMA_AUTO_CREATE_INDEXES=true
DYNAMIC_SCHEMA_OPTIMIZE_QUERIES=true

# 跨域配置
CROSS_DOMAIN_ENABLED=true
CROSS_DOMAIN_AUTO_DISCOVER=true
CROSS_DOMAIN_INDEX_STRATEGY=adaptive

# ==============================================
# 领域配置
# ==============================================
# 安全领域
SECURITY_DOMAIN_ENABLED=true
SECURITY_DOMAIN_AUTO_CREATE=true

# 建模领域
MODELING_DOMAIN_ENABLED=false
MODELING_DOMAIN_AUTO_CREATE=false

# 生物医学领域
BIOMEDICAL_DOMAIN_ENABLED=false
BIOMEDICAL_DOMAIN_AUTO_CREATE=false

# ==============================================
# 日志配置
# ==============================================
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/app.log
LOG_FILE_MAX_SIZE=10MB
LOG_FILE_BACKUP_COUNT=5
LOG_CONSOLE_ENABLED=true
LOG_CONSOLE_COLORED=true

# ==============================================
# 性能配置
# ==============================================
PERFORMANCE_DB_POOL_SIZE=20
PERFORMANCE_QUERY_TIMEOUT=30
PERFORMANCE_CACHE_ENABLED=true
PERFORMANCE_CACHE_TTL=300
PERFORMANCE_CACHE_MAX_SIZE=1000

# ==============================================
# 开发工具配置
# ==============================================
DEV_AUTO_RELOAD=true
DEV_DEBUG_MODE=true
DEV_LOAD_TEST_DATA=true
DEV_ENABLE_DOCS=true

# ==============================================
# CORS配置
# ==============================================
CORS_ALLOW_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=*
CORS_ALLOW_HEADERS=*

# ==============================================
# 备份配置
# ==============================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups

# ==============================================
# 部署环境标识
# ==============================================
ENVIRONMENT=local
DEPLOYMENT_MODE=development 