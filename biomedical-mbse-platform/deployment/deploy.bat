@echo off
REM =====================================================
REM 生物医学MBSE平台一键部署脚本
REM 版本 1.0.0
REM 创建时间 2024-12-09
REM =====================================================

setlocal EnableDelayedExpansion

REM 设置控制台编码为936(GBK)以正确显示中文
chcp 936 >nul 2>&1

echo ================================================
echo 生物医学MBSE平台一键部署脚本
echo ================================================
echo.
echo 本脚本将自动完成以下步骤:
echo 1. 检查和安装PostgreSQL数据库
echo 2. 创建数据库和用户
echo 3. 初始化核心Schema和安全领域
echo 4. 设置Python环境和依赖
echo 5. 启动后端API服务
echo.
echo 注意:
echo - 需要管理员权限
echo - 需要网络连接
echo - 整个过程可能需要10-20分钟
echo.

set /p CONFIRM="确认开始部署?(y/N): "
if /i not "%CONFIRM%"=="y" (
    echo [INFO] 部署取消
    pause
    exit /b 0
)

REM 检查管理员权限
echo [INFO] 检查管理员权限...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] 此脚本需要管理员权限运行！
    echo [INFO] 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 设置变量
set SCRIPT_DIR=%~dp0
set SCRIPTS_DIR=%SCRIPT_DIR%scripts
set LOG_DIR=%SCRIPT_DIR%logs
set START_TIME=%time%

REM 创建日志目录
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM 设置日志文件
set LOG_FILE=%LOG_DIR%\deploy_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=%LOG_FILE: =0%

echo [INFO] 部署日志将保存到: %LOG_FILE%
echo ================================================ > "%LOG_FILE%"
echo 生物医学MBSE平台部署日志 >> "%LOG_FILE%"
echo 开始时间: %date% %time% >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"

echo.
echo ================================================
echo 步骤 1/5: 检查和安装PostgreSQL
echo ================================================

echo [INFO] 步骤 1/5: 检查和安装PostgreSQL >> "%LOG_FILE%"

call "%SCRIPTS_DIR%\install-postgresql.bat" >> "%LOG_FILE%" 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] PostgreSQL安装失败！
    echo [ERROR] PostgreSQL安装失败！ >> "%LOG_FILE%"
    goto :error_exit
)

echo [SUCCESS] PostgreSQL安装完成
echo [SUCCESS] PostgreSQL安装完成 >> "%LOG_FILE%"

echo.
echo ================================================
echo 步骤 2/5: 创建数据库和用户
echo ================================================

echo [INFO] 步骤 2/5: 创建数据库和用户 >> "%LOG_FILE%"

REM 注意：此步骤需要用户交互输入密码，不能重定向到日志
echo [INFO] 注意：此步骤需要输入PostgreSQL管理员密码
call "%SCRIPTS_DIR%\setup-database.bat"
if %errorLevel% neq 0 (
    echo [ERROR] 数据库设置失败！
    echo [ERROR] 数据库设置失败！ >> "%LOG_FILE%"
    goto :error_exit
)

echo [SUCCESS] 数据库设置完成
echo [SUCCESS] 数据库设置完成 >> "%LOG_FILE%"

echo.
echo ================================================
echo 步骤 3/5: 初始化数据库Schema
echo ================================================

echo [INFO] 步骤 3/5: 初始化数据库Schema >> "%LOG_FILE%"

call "%SCRIPTS_DIR%\init-database.bat" >> "%LOG_FILE%" 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] 数据库初始化失败！
    echo [ERROR] 数据库初始化失败！ >> "%LOG_FILE%"
    goto :error_exit
)

echo [SUCCESS] 数据库初始化完成
echo [SUCCESS] 数据库初始化完成 >> "%LOG_FILE%"

echo.
echo ================================================
echo 步骤 4/5: 设置Python环境
echo ================================================

echo [INFO] 步骤 4/5: 设置Python环境 >> "%LOG_FILE%"

call "%SCRIPTS_DIR%\setup-python.bat" >> "%LOG_FILE%" 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python环境设置失败！
    echo [ERROR] Python环境设置失败！ >> "%LOG_FILE%"
    goto :error_exit
)

echo [SUCCESS] Python环境设置完成
echo [SUCCESS] Python环境设置完成 >> "%LOG_FILE%"

echo.
echo ================================================
echo 步骤 5/5: 部署验证
echo ================================================

echo [INFO] 步骤 5/5: 部署验证 >> "%LOG_FILE%"

REM 验证数据库连接
echo [INFO] 验证数据库连接...
set PSQL_PATH="C:\Program Files\PostgreSQL\17\bin\psql.exe"
set PGPASSWORD=mbse_pass_2024

%PSQL_PATH% -h localhost -p 5432 -U mbse_user -d biomedical_mbse_platform -c "SELECT 'Deployment verification successful' as result;" >> "%LOG_FILE%" 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] 数据库连接验证失败！
    echo [ERROR] 数据库连接验证失败！ >> "%LOG_FILE%"
    goto :error_exit
)

REM 验证Python环境
echo [INFO] 验证Python环境...
set PROJECT_DIR=%SCRIPT_DIR%..
call "%PROJECT_DIR%\venv\Scripts\activate.bat" >> "%LOG_FILE%" 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python环境验证失败！
    echo [ERROR] Python环境验证失败！ >> "%LOG_FILE%"
    goto :error_exit
)

python -c "import fastapi, uvicorn, asyncpg; print('Python environment OK')" >> "%LOG_FILE%" 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python依赖验证失败！
    echo [ERROR] Python依赖验证失败！ >> "%LOG_FILE%"
    goto :error_exit
)

set END_TIME=%time%
echo ================================================ >> "%LOG_FILE%"
echo 部署完成时间: %date% %time% >> "%LOG_FILE%"
echo 部署耗时: 从 %START_TIME% 到 %END_TIME% >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"

echo [SUCCESS] 部署验证完成
echo [SUCCESS] 部署验证完成 >> "%LOG_FILE%"

echo.
echo ================================================
echo 🎉 部署成功完成！
echo ================================================
echo.
echo ✅ 已完成的组件：
echo   - PostgreSQL 17 数据库服务器
echo   - biomedical_mbse_platform 数据库
echo   - 核心Schema (Element架构)
echo   - 安全领域Schema (用户认证)
echo   - Python 虚拟环境
echo   - FastAPI + AsyncPG + SQLAlchemy
echo.
echo 🔑 默认账户信息：
echo   - 管理员用户: admin
echo   - 管理员密码: admin123
echo   - 数据库用户: mbse_user
echo   - 数据库密码: mbse_pass_2024
echo.
echo 🚀 启动服务：
echo   - 运行: scripts\start-services.bat
echo   - 或者: cd .. ^&^& start_backend.bat
echo.
echo 📊 服务地址：
echo   - API服务: http://localhost:8000
echo   - API文档: http://localhost:8000/docs
echo   - 健康检查: http://localhost:8000/health
echo.
echo 📁 重要文件位置：
echo   - 项目根目录: %PROJECT_DIR%
echo   - 虚拟环境: %PROJECT_DIR%\venv
echo   - 环境配置: %PROJECT_DIR%\backend\.env
echo   - 部署日志: %LOG_FILE%
echo.

set /p START_NOW="是否立即启动服务？(y/N): "
if /i "%START_NOW%"=="y" (
    echo [INFO] 启动服务...
    call "%SCRIPTS_DIR%\start-services.bat"
) else (
    echo [INFO] 可以稍后运行 scripts\start-services.bat 启动服务
)

goto :end

:error_exit
echo.
echo ================================================
echo ❌ 部署失败！
echo ================================================
echo.
echo 请检查以下内容：
echo 1. 是否以管理员权限运行脚本
echo 2. 网络连接是否正常
echo 3. 磁盘空间是否充足
echo 4. 杀毒软件是否阻止安装
echo.
echo 详细错误信息请查看日志文件：
echo %LOG_FILE%
echo.
echo 如需技术支持，请提供完整的日志文件。
echo.

echo ================================================ >> "%LOG_FILE%"
echo 部署失败时间: %date% %time% >> "%LOG_FILE%"
echo 失败位置: 请查看上方的错误信息 >> "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"

pause
exit /b 1

:end
echo.
echo 感谢使用生物医学MBSE平台！
echo.
pause 