@echo off
chcp 65001 >nul
echo ================================================
echo 生物医学MBSE平台 - 修复和重新部署脚本
echo ================================================
echo.

echo [INFO] 清理之前的虚拟环境...
if exist "..\..\venv" (
    rmdir /s /q "..\..\venv" 2>nul
    echo [SUCCESS] 旧虚拟环境已清理
)

echo.
echo [INFO] 开始重新部署 Python 环境...
echo.

REM 切换到脚本目录
cd /d scripts

REM 重新运行 Python 环境设置
call setup-python.bat

if %errorLevel% neq 0 (
    echo [ERROR] Python环境设置失败！
    pause
    exit /b 1
)

echo.
echo [SUCCESS] 修复完成！Python环境已重新设置。
echo.
echo 接下来可以运行：
echo - setup-frontend.bat  （设置前端环境）
echo - start-services.bat  （启动所有服务）
echo.
pause 