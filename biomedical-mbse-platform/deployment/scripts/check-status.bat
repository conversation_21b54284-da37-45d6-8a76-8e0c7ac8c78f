@echo off
REM =====================================================
REM 系统状态检查脚本
REM 检查生物医学MBSE平台的完整状态
REM =====================================================

setlocal EnableDelayedExpansion

echo ================================================
echo 🧬 生物医学MBSE平台状态检查
echo ================================================
echo.

REM 设置变量
set POSTGRESQL_VERSION=17
set PSQL_PATH="C:\Program Files\PostgreSQL\%POSTGRESQL_VERSION%\bin\psql.exe"
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=biomedical_mbse_platform
set DB_USER=mbse_user
set DB_PASSWORD=mbse_pass_2024

REM 获取脚本目录
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..\..
set BACKEND_DIR=%PROJECT_DIR%\backend
set VENV_DIR=%PROJECT_DIR%\venv

REM 设置状态变量
set STATUS_POSTGRESQL=❌
set STATUS_DATABASE=❌
set STATUS_PYTHON=❌
set STATUS_VENV=❌
set STATUS_DEPENDENCIES=❌
set STATUS_CONFIG=❌
set STATUS_CORE_SCHEMA=❌
set STATUS_SECURITY_SCHEMA=❌
set STATUS_API=❌

echo [INFO] 开始系统状态检查...
echo.

REM 1. 检查PostgreSQL安装和服务
echo ================================================
echo 1. PostgreSQL数据库检查
echo ================================================

if exist %PSQL_PATH% (
    echo [✓] PostgreSQL已安装: %PSQL_PATH%
    
    REM 检查服务状态
    sc query postgresql-x64-%POSTGRESQL_VERSION% | find "RUNNING" >nul 2>&1
    if !errorLevel! == 0 (
        echo [✓] PostgreSQL服务运行正常
        set STATUS_POSTGRESQL=✅
        
        REM 检查版本
        for /f "tokens=*" %%v in ('%PSQL_PATH% --version 2^>nul') do (
            echo [INFO] 版本: %%v
        )
    ) else (
        echo [⚠] PostgreSQL服务未运行
        echo [INFO] 尝试启动服务...
        net start postgresql-x64-%POSTGRESQL_VERSION% >nul 2>&1
        if !errorLevel! == 0 (
            echo [✓] PostgreSQL服务启动成功
            set STATUS_POSTGRESQL=✅
        ) else (
            echo [❌] PostgreSQL服务启动失败
        )
    )
) else (
    echo [❌] PostgreSQL未安装
    echo [INFO] 请运行: scripts\install-postgresql.bat
)

echo.

REM 2. 检查数据库连接和结构
echo ================================================
echo 2. 数据库连接和结构检查
echo ================================================

if "%STATUS_POSTGRESQL%"=="✅" (
    set PGPASSWORD=%DB_PASSWORD%
    
    REM 测试数据库连接
    %PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT current_database();" >nul 2>&1
    if !errorLevel! == 0 (
        echo [✓] 数据库连接正常
        set STATUS_DATABASE=✅
        
        REM 检查核心Schema
        %PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 1 FROM information_schema.schemata WHERE schema_name = 'core_schema';" >nul 2>&1
        if !errorLevel! == 0 (
            echo [✓] 核心Schema已创建
            set STATUS_CORE_SCHEMA=✅
        ) else (
            echo [❌] 核心Schema未创建
            echo [INFO] 请运行: scripts\setup-database.bat
        )
        
        REM 检查安全Schema
        %PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 1 FROM information_schema.schemata WHERE schema_name = 'security_schema';" >nul 2>&1
        if !errorLevel! == 0 (
            echo [✓] 安全Schema已创建
            set STATUS_SECURITY_SCHEMA=✅
            
            REM 检查用户数据
            for /f %%c in ('%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -t -c "SELECT count(*) FROM security_schema.user_elements;" 2^>nul') do (
                echo [INFO] 用户数量: %%c
            )
        ) else (
            echo [❌] 安全Schema未创建
            echo [INFO] 请运行: scripts\init-database.bat
        )
        
        REM 检查领域注册
        for /f %%c in ('%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -t -c "SELECT count(*) FROM core_schema.domain_registry;" 2^>nul') do (
            echo [INFO] 注册领域数量: %%c
        )
        
    ) else (
        echo [❌] 数据库连接失败
        echo [INFO] 请检查数据库配置或运行: scripts\setup-database.bat
    )
) else (
    echo [⚠] 跳过数据库检查（PostgreSQL未运行）
)

echo.

REM 3. 检查Python环境
echo ================================================
echo 3. Python环境检查
echo ================================================

python --version >nul 2>&1
if %errorLevel% == 0 (
    for /f "tokens=*" %%v in ('python --version 2^>^&1') do (
        echo [✓] Python已安装: %%v
        set STATUS_PYTHON=✅
    )
    
    REM 检查pip
    python -m pip --version >nul 2>&1
    if !errorLevel! == 0 (
        echo [✓] pip可用
    ) else (
        echo [❌] pip不可用
    )
) else (
    echo [❌] Python未安装
    echo [INFO] 请安装Python 3.11+并添加到PATH
)

echo.

REM 4. 检查虚拟环境
echo ================================================
echo 4. Python虚拟环境检查
echo ================================================

if exist "%VENV_DIR%\Scripts\activate.bat" (
    echo [✓] 虚拟环境已创建: %VENV_DIR%
    set STATUS_VENV=✅
    
    REM 激活虚拟环境并检查依赖
    call "%VENV_DIR%\Scripts\activate.bat" >nul 2>&1
    
    REM 检查关键依赖
    python -c "import fastapi; print('FastAPI:', fastapi.__version__)" >nul 2>&1
    if !errorLevel! == 0 (
        echo [✓] FastAPI已安装
        set DEP_FASTAPI=✅
    ) else (
        echo [❌] FastAPI未安装
        set DEP_FASTAPI=❌
    )
    
    python -c "import asyncpg; print('AsyncPG:', asyncpg.__version__)" >nul 2>&1
    if !errorLevel! == 0 (
        echo [✓] AsyncPG已安装
        set DEP_ASYNCPG=✅
    ) else (
        echo [❌] AsyncPG未安装
        set DEP_ASYNCPG=❌
    )
    
    python -c "import sqlalchemy; print('SQLAlchemy:', sqlalchemy.__version__)" >nul 2>&1
    if !errorLevel! == 0 (
        echo [✓] SQLAlchemy已安装
        set DEP_SQLALCHEMY=✅
    ) else (
        echo [❌] SQLAlchemy未安装
        set DEP_SQLALCHEMY=❌
    )
    
    if "!DEP_FASTAPI!"=="✅" if "!DEP_ASYNCPG!"=="✅" if "!DEP_SQLALCHEMY!"=="✅" (
        set STATUS_DEPENDENCIES=✅
        echo [✓] 核心依赖完整
    ) else (
        echo [❌] 缺少核心依赖
        echo [INFO] 请运行: scripts\setup-python.bat
    )
    
) else (
    echo [❌] 虚拟环境未创建
    echo [INFO] 请运行: scripts\setup-python.bat
)

echo.

REM 5. 检查项目配置
echo ================================================
echo 5. 项目配置检查
echo ================================================

if exist "%BACKEND_DIR%" (
    echo [✓] 后端目录存在: %BACKEND_DIR%
    
    if exist "%BACKEND_DIR%\.env" (
        echo [✓] 环境变量文件存在
        set STATUS_CONFIG=✅
    ) else (
        echo [❌] 环境变量文件不存在
        echo [INFO] 请运行: scripts\setup-python.bat
    )
    
    if exist "%BACKEND_DIR%\main.py" (
        echo [✓] 主应用文件存在
    ) else (
        echo [⚠] 主应用文件不存在
        echo [INFO] 将在启动时自动创建
    )
    
    if exist "%BACKEND_DIR%\requirements.txt" (
        echo [✓] 依赖文件存在
    ) else (
        echo [⚠] 依赖文件不存在
        echo [INFO] 将在Python环境设置时创建
    )
) else (
    echo [❌] 后端目录不存在
)

echo.

REM 6. 检查API服务
echo ================================================
echo 6. API服务检查
echo ================================================

REM 检查端口8000是否被占用
netstat -ano | findstr :8000 >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 端口8000有服务运行
    
    REM 尝试访问健康检查端点
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 5; if ($response.StatusCode -eq 200) { Write-Host '[✓] API健康检查通过' } else { Write-Host '[❌] API响应异常' } } catch { Write-Host '[❌] API服务不响应' }" 2>nul
    
    REM 如果健康检查通过，则设置API状态为正常
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
    if !errorLevel! == 0 (
        set STATUS_API=✅
    )
) else (
    echo [❌] 端口8000无服务运行
    echo [INFO] 请运行: scripts\start-services.bat
)

echo.

REM 系统状态总结
echo ================================================
echo 📊 系统状态总结
echo ================================================
echo.
echo 核心组件状态：
echo   PostgreSQL数据库    %STATUS_POSTGRESQL%
echo   数据库连接          %STATUS_DATABASE%
echo   Python环境          %STATUS_PYTHON%
echo   虚拟环境            %STATUS_VENV%
echo   Python依赖          %STATUS_DEPENDENCIES%
echo   项目配置            %STATUS_CONFIG%
echo   核心Schema          %STATUS_CORE_SCHEMA%
echo   安全Schema          %STATUS_SECURITY_SCHEMA%
echo   API服务             %STATUS_API%
echo.

REM 计算完成度
set COMPONENT_COUNT=0
set COMPLETED_COUNT=0

if "%STATUS_POSTGRESQL%"=="✅" set /a COMPLETED_COUNT+=1
if "%STATUS_DATABASE%"=="✅" set /a COMPLETED_COUNT+=1
if "%STATUS_PYTHON%"=="✅" set /a COMPLETED_COUNT+=1
if "%STATUS_VENV%"=="✅" set /a COMPLETED_COUNT+=1
if "%STATUS_DEPENDENCIES%"=="✅" set /a COMPLETED_COUNT+=1
if "%STATUS_CONFIG%"=="✅" set /a COMPLETED_COUNT+=1
if "%STATUS_CORE_SCHEMA%"=="✅" set /a COMPLETED_COUNT+=1
if "%STATUS_SECURITY_SCHEMA%"=="✅" set /a COMPLETED_COUNT+=1
if "%STATUS_API%"=="✅" set /a COMPLETED_COUNT+=1

set /a COMPONENT_COUNT=9
set /a COMPLETION_PERCENT=COMPLETED_COUNT*100/COMPONENT_COUNT

echo 完成度: %COMPLETED_COUNT%/%COMPONENT_COUNT% (%COMPLETION_PERCENT%%)
echo.

REM 提供操作建议
if %COMPLETION_PERCENT% LSS 50 (
    echo 🚀 建议操作：
    echo   1. 运行一键部署: deploy.bat
    echo   2. 或者按步骤手动部署
) else if %COMPLETION_PERCENT% LSS 90 (
    echo 🔧 需要完善：
    if "%STATUS_POSTGRESQL%"=="❌" echo   - 安装PostgreSQL: scripts\install-postgresql.bat
    if "%STATUS_DATABASE%"=="❌" echo   - 设置数据库: scripts\setup-database.bat
    if "%STATUS_CORE_SCHEMA%"=="❌" echo   - 初始化数据库: scripts\init-database.bat
    if "%STATUS_PYTHON%"=="❌" echo   - 安装Python 3.11+
    if "%STATUS_VENV%"=="❌" echo   - 设置Python环境: scripts\setup-python.bat
    if "%STATUS_DEPENDENCIES%"=="❌" echo   - 安装Python依赖: scripts\setup-python.bat
    if "%STATUS_CONFIG%"=="❌" echo   - 创建配置文件: scripts\setup-python.bat
    if "%STATUS_API%"=="❌" echo   - 启动API服务: scripts\start-services.bat
) else (
    echo ✅ 系统状态良好！
    if "%STATUS_API%"=="❌" (
        echo 💡 可以启动API服务: scripts\start-services.bat
    ) else (
        echo 🎉 所有组件运行正常！
        echo.
        echo 📱 服务地址：
        echo   - API服务: http://localhost:8000
        echo   - API文档: http://localhost:8000/docs
        echo   - 健康检查: http://localhost:8000/health
    )
)

echo.
echo 🛠️ 可用工具：
echo   - 数据库测试: scripts\test-database.bat
echo   - 架构测试: scripts\test-dynamic-architecture.bat
echo   - 状态检查: scripts\check-status.bat
echo.

REM 清理环境变量
set PGPASSWORD=

pause 