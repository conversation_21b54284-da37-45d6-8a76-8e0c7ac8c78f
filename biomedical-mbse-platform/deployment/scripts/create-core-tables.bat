@echo off
REM =====================================================
REM 核心表创建脚本
REM 执行Element架构核心表的创建
REM =====================================================

echo ================================================
echo 创建Element架构核心表
echo ================================================

REM 设置变量
set POSTGRESQL_VERSION=17
set PSQL_PATH="C:\Program Files\PostgreSQL\%POSTGRESQL_VERSION%\bin\psql.exe"
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=biomedical_mbse_platform
set DB_USER=mbse_user
set DB_PASSWORD=mbse_pass_2024

REM 获取脚本目录
set SCRIPT_DIR=%~dp0
set SQL_DIR=%SCRIPT_DIR%..\sql

echo [INFO] 检查PostgreSQL连接...

REM 设置密码环境变量
set PGPASSWORD=%DB_PASSWORD%

echo [INFO] 执行核心表创建脚本...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f "%SQL_DIR%\create-core-tables.sql"

if %errorLevel% neq 0 (
    echo [ERROR] 核心表创建失败！
    pause
    exit /b 1
)

echo [SUCCESS] 核心表创建完成

echo [INFO] 验证核心表创建...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "\dt core_schema.*"

echo ================================================
echo 核心表创建完成！
echo ================================================

pause 