@echo off
REM =====================================================
REM 数据库初始化脚本 - 安全领域
REM 创建安全领域Schema和初始数据
REM =====================================================

setlocal EnableDelayedExpansion

REM 设置控制台编码为936(GBK)以正确显示中文
chcp 936 >nul 2>&1

echo ================================================
echo 数据库初始化脚本 - 安全领域
echo ================================================

REM 设置变量
set POSTGRESQL_VERSION=17
set PSQL_PATH="C:\Program Files\PostgreSQL\%POSTGRESQL_VERSION%\bin\psql.exe"
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=biomedical_mbse_platform
set DB_USER=mbse_user
set DB_PASSWORD=mbse_pass_2024

REM 获取脚本目录
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..\..\backend

echo [INFO] 验证数据库连接...

REM 设置密码环境变量
set PGPASSWORD=%DB_PASSWORD%

%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 'Connected to ' || current_database() as status;"

if %errorLevel% neq 0 (
    echo [ERROR] 无法连接到数据库！请先运行 setup-database.bat
    pause
    exit /b 1
)

echo [SUCCESS] 数据库连接正常

echo [INFO] 检查Python环境...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python未安装或未添加到PATH！
    echo [INFO] 请安装Python 3.11+并添加到系统PATH
    pause
    exit /b 1
)

echo [INFO] 检查项目目录...
if not exist "%PROJECT_DIR%" (
    echo [ERROR] 项目目录未找到: %PROJECT_DIR%
    echo [INFO] 请确保在正确的项目目录下运行此脚本
    pause
    exit /b 1
)

echo [INFO] 创建安全领域Schema...

REM 创建安全领域SQL脚本
set TEMP_SQL=%TEMP%\init_security_schema.sql

REM 强制删除可能存在的临时文件并重新创建
if exist "%TEMP_SQL%" del "%TEMP_SQL%" /f /q

echo -- 创建安全领域Schema > "%TEMP_SQL%"
echo \c %DB_NAME%; >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo -- 创建安全领域Schema >> "%TEMP_SQL%"
echo CREATE SCHEMA IF NOT EXISTS security_schema; >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo -- 1. 用户Element表 >> "%TEMP_SQL%"
echo CREATE TABLE IF NOT EXISTS security_schema.user_elements ( >> "%TEMP_SQL%"
echo     element_id TEXT PRIMARY KEY, >> "%TEMP_SQL%"
echo     username TEXT UNIQUE NOT NULL, >> "%TEMP_SQL%"
echo     email TEXT UNIQUE NOT NULL, >> "%TEMP_SQL%"
echo     password_hash TEXT NOT NULL, >> "%TEMP_SQL%"
echo     full_name TEXT, >> "%TEMP_SQL%"
echo     is_active BOOLEAN DEFAULT true, >> "%TEMP_SQL%"
echo     is_superuser BOOLEAN DEFAULT false, >> "%TEMP_SQL%"
echo     last_login TIMESTAMP WITH TIME ZONE, >> "%TEMP_SQL%"
echo     failed_login_attempts INTEGER DEFAULT 0, >> "%TEMP_SQL%"
echo     account_locked_until TIMESTAMP WITH TIME ZONE, >> "%TEMP_SQL%"
echo     element_data JSONB NOT NULL DEFAULT '{}', >> "%TEMP_SQL%"
echo     created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, >> "%TEMP_SQL%"
echo     updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP >> "%TEMP_SQL%"
echo ); >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo -- 2. 角色Element表 >> "%TEMP_SQL%"
echo CREATE TABLE IF NOT EXISTS security_schema.role_elements ( >> "%TEMP_SQL%"
echo     element_id TEXT PRIMARY KEY, >> "%TEMP_SQL%"
echo     role_name TEXT UNIQUE NOT NULL, >> "%TEMP_SQL%"
echo     description TEXT, >> "%TEMP_SQL%"
echo     is_system_role BOOLEAN DEFAULT false, >> "%TEMP_SQL%"
echo     element_data JSONB NOT NULL DEFAULT '{}', >> "%TEMP_SQL%"
echo     created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, >> "%TEMP_SQL%"
echo     updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP >> "%TEMP_SQL%"
echo ); >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo -- 3. 权限Element表 >> "%TEMP_SQL%"
echo CREATE TABLE IF NOT EXISTS security_schema.permission_elements ( >> "%TEMP_SQL%"
echo     element_id TEXT PRIMARY KEY, >> "%TEMP_SQL%"
echo     permission_name TEXT UNIQUE NOT NULL, >> "%TEMP_SQL%"
echo     resource TEXT NOT NULL, >> "%TEMP_SQL%"
echo     action TEXT NOT NULL, >> "%TEMP_SQL%"
echo     conditions JSONB DEFAULT '{}', >> "%TEMP_SQL%"
echo     description TEXT, >> "%TEMP_SQL%"
echo     element_data JSONB NOT NULL DEFAULT '{}', >> "%TEMP_SQL%"
echo     created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, >> "%TEMP_SQL%"
echo     updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP >> "%TEMP_SQL%"
echo ); >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo -- 4. 用户会话表 >> "%TEMP_SQL%"
echo CREATE TABLE IF NOT EXISTS security_schema.user_sessions ( >> "%TEMP_SQL%"
echo     session_id TEXT PRIMARY KEY, >> "%TEMP_SQL%"
echo     user_element_id TEXT NOT NULL, >> "%TEMP_SQL%"
echo     access_token TEXT NOT NULL, >> "%TEMP_SQL%"
echo     refresh_token TEXT, >> "%TEMP_SQL%"
echo     expires_at TIMESTAMP WITH TIME ZONE NOT NULL, >> "%TEMP_SQL%"
echo     created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, >> "%TEMP_SQL%"
echo     user_agent TEXT, >> "%TEMP_SQL%"
echo     ip_address TEXT, >> "%TEMP_SQL%"
echo     is_active BOOLEAN DEFAULT true, >> "%TEMP_SQL%"
echo     FOREIGN KEY (user_element_id) REFERENCES security_schema.user_elements(element_id) ON DELETE CASCADE >> "%TEMP_SQL%"
echo ); >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo -- 创建索引 >> "%TEMP_SQL%"
echo CREATE INDEX IF NOT EXISTS idx_user_elements_username ON security_schema.user_elements(username); >> "%TEMP_SQL%"
echo CREATE INDEX IF NOT EXISTS idx_user_elements_email ON security_schema.user_elements(email); >> "%TEMP_SQL%"
echo CREATE INDEX IF NOT EXISTS idx_user_elements_active ON security_schema.user_elements(is_active) WHERE is_active = true; >> "%TEMP_SQL%"
echo CREATE INDEX IF NOT EXISTS idx_role_elements_name ON security_schema.role_elements(role_name); >> "%TEMP_SQL%"
echo CREATE INDEX IF NOT EXISTS idx_permission_elements_resource_action ON security_schema.permission_elements(resource, action); >> "%TEMP_SQL%"
echo CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON security_schema.user_sessions(user_element_id); >> "%TEMP_SQL%"
echo CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON security_schema.user_sessions(access_token); >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo -- 注册安全领域到核心注册表 >> "%TEMP_SQL%"
echo INSERT INTO core_schema.domain_registry ( >> "%TEMP_SQL%"
echo     domain_name, schema_name, display_name, description, >> "%TEMP_SQL%"
echo     domain_config, element_types, namespace_prefix >> "%TEMP_SQL%"
echo ) VALUES ( >> "%TEMP_SQL%"
echo     'security', 'security_schema', 'Security Domain', 'User authentication and authorization management', >> "%TEMP_SQL%"
echo     '{"supports_authentication": true, "supports_authorization": true, "supports_rbac": true}', >> "%TEMP_SQL%"
echo     '["user_element", "role_element", "permission_element"]', >> "%TEMP_SQL%"
echo     'sec' >> "%TEMP_SQL%"
echo ) ON CONFLICT (domain_name) DO NOTHING; >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo -- 设置权限 >> "%TEMP_SQL%"
echo GRANT USAGE ON SCHEMA security_schema TO %DB_USER%; >> "%TEMP_SQL%"
echo GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA security_schema TO %DB_USER%; >> "%TEMP_SQL%"
echo GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA security_schema TO %DB_USER%; >> "%TEMP_SQL%"
echo. >> "%TEMP_SQL%"
echo SELECT 'Security schema initialized successfully' as result; >> "%TEMP_SQL%"

echo [INFO] 执行安全领域Schema创建...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f "%TEMP_SQL%" --set client_encoding=UTF8

if %errorLevel% neq 0 (
    echo [ERROR] 安全领域Schema创建失败！
    del "%TEMP_SQL%" 2>nul
    pause
    exit /b 1
)

echo [SUCCESS] 安全领域Schema创建完成

echo [INFO] 插入初始数据...

REM 创建初始数据SQL
set INIT_DATA_SQL=%TEMP%\init_data.sql

REM 强制删除可能存在的临时文件并重新创建
if exist "%INIT_DATA_SQL%" del "%INIT_DATA_SQL%" /f /q

echo \c %DB_NAME%; > "%INIT_DATA_SQL%"
echo. >> "%INIT_DATA_SQL%"
echo -- 插入默认角色 >> "%INIT_DATA_SQL%"
echo INSERT INTO security_schema.role_elements (element_id, role_name, description, is_system_role, element_data) VALUES >> "%INIT_DATA_SQL%"
echo     ('role_system_admin', 'system_admin', 'System Administrator', true, '{"level": "system", "privileges": ["all"]}'), >> "%INIT_DATA_SQL%"
echo     ('role_user_default', 'user_default', 'Default User', true, '{"level": "user", "privileges": ["read", "create_own"]}') >> "%INIT_DATA_SQL%"
echo ON CONFLICT (element_id) DO NOTHING; >> "%INIT_DATA_SQL%"
echo. >> "%INIT_DATA_SQL%"
echo -- 插入默认权限 >> "%INIT_DATA_SQL%"
echo INSERT INTO security_schema.permission_elements (element_id, permission_name, resource, action, description, element_data) VALUES >> "%INIT_DATA_SQL%"
echo     ('perm_user_read', 'user:read', 'user', 'read', 'Read user information', '{}'), >> "%INIT_DATA_SQL%"
echo     ('perm_user_create', 'user:create', 'user', 'create', 'Create user', '{}'), >> "%INIT_DATA_SQL%"
echo     ('perm_user_update', 'user:update', 'user', 'update', 'Update user', '{}'), >> "%INIT_DATA_SQL%"
echo     ('perm_user_delete', 'user:delete', 'user', 'delete', 'Delete user', '{}'), >> "%INIT_DATA_SQL%"
echo     ('perm_element_read', 'element:read', 'element', 'read', 'Read Element', '{}'), >> "%INIT_DATA_SQL%"
echo     ('perm_element_create', 'element:create', 'element', 'create', 'Create Element', '{}'), >> "%INIT_DATA_SQL%"
echo     ('perm_system_admin', 'system:admin', 'system', '*', 'System admin permission', '{}') >> "%INIT_DATA_SQL%"
echo ON CONFLICT (element_id) DO NOTHING; >> "%INIT_DATA_SQL%"
echo. >> "%INIT_DATA_SQL%"
echo -- 创建测试管理员用户（密码: admin123） >> "%INIT_DATA_SQL%"
echo INSERT INTO security_schema.user_elements (element_id, username, email, password_hash, full_name, is_superuser, element_data) VALUES >> "%INIT_DATA_SQL%"
echo     ('user_admin', 'admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LeEVgEEg3ZPr3vZD6', 'System Administrator', true, '{"role": "system_admin", "created_by": "system"}') >> "%INIT_DATA_SQL%"
echo ON CONFLICT (element_id) DO NOTHING; >> "%INIT_DATA_SQL%"
echo. >> "%INIT_DATA_SQL%"
echo -- 注册Element元数据 >> "%INIT_DATA_SQL%"
echo INSERT INTO core_schema.element_metadata (element_id, domain_schema, element_type, tag, local_name, description) VALUES >> "%INIT_DATA_SQL%"
echo     ('role_system_admin', 'security_schema', 'role_element', 'role', 'system_admin', 'System Administrator Role'), >> "%INIT_DATA_SQL%"
echo     ('role_user_default', 'security_schema', 'role_element', 'role', 'user_default', 'Default User Role'), >> "%INIT_DATA_SQL%"
echo     ('user_admin', 'security_schema', 'user_element', 'user', 'admin', 'System Administrator User') >> "%INIT_DATA_SQL%"
echo ON CONFLICT (element_id) DO NOTHING; >> "%INIT_DATA_SQL%"
echo. >> "%INIT_DATA_SQL%"
echo SELECT 'Initial data inserted successfully' as result; >> "%INIT_DATA_SQL%"

echo [INFO] 插入初始数据...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f "%INIT_DATA_SQL%" --set client_encoding=UTF8

if %errorLevel% neq 0 (
    echo [ERROR] 初始数据插入失败！
    del "%TEMP_SQL%" 2>nul
    del "%INIT_DATA_SQL%" 2>nul
    pause
    exit /b 1
)

echo [SUCCESS] 初始数据插入完成

echo [INFO] 验证数据库初始化...

REM 验证安全领域表
echo [INFO] 检查安全领域表...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT schemaname, tablename FROM pg_tables WHERE schemaname = 'security_schema';"

REM 验证初始数据
echo [INFO] 检查初始数据...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 'Users: ' || count(*) as count FROM security_schema.user_elements UNION ALL SELECT 'Roles: ' || count(*) FROM security_schema.role_elements UNION ALL SELECT 'Permissions: ' || count(*) FROM security_schema.permission_elements;"

REM 验证领域注册
echo [INFO] 检查领域注册...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT domain_name, schema_name, display_name FROM core_schema.domain_registry WHERE domain_name IN ('core', 'security');"

echo ================================================
echo 数据库初始化完成！
echo ================================================
echo.
echo 已创建以下组件：
echo - security_schema（安全领域Schema）
echo - user_elements（用户Element表）
echo - role_elements（角色Element表） 
echo - permission_elements（权限Element表）
echo - user_sessions（用户会话表）
echo.
echo 初始数据：
echo - 管理员用户: admin / admin123
echo - 默认角色: system_admin, user_default
echo - 基础权限: 用户和Element管理权限
echo.
echo 下一步：运行 setup-python.bat 设置Python环境
echo.

REM 清理临时文件
del "%TEMP_SQL%" 2>nul
del "%INIT_DATA_SQL%" 2>nul

REM 清理环境变量
set PGPASSWORD=

pause 