@echo off
REM =====================================================
REM PostgreSQL 自动安装脚本
REM 版本：1.0.0
REM =====================================================

setlocal EnableDelayedExpansion

REM 设置控制台编码为936(GBK)以正确显示中文
chcp 936 >nul 2>&1

echo ================================================
echo PostgreSQL 安装脚本
echo ================================================

REM 检查是否以管理员权限运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] 检测到管理员权限，继续安装...
) else (
    echo [ERROR] 此脚本需要管理员权限运行！
    echo [INFO] 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 设置变量
set POSTGRESQL_VERSION=17
set INSTALL_DIR=C:\Program Files\PostgreSQL\%POSTGRESQL_VERSION%
set DATA_DIR=C:\Program Files\PostgreSQL\%POSTGRESQL_VERSION%\data
set DOWNLOAD_URL=https://get.enterprisedb.com/postgresql/postgresql-17.0-1-windows-x64.exe

echo [INFO] 检查PostgreSQL是否已安装...

REM 检查PostgreSQL是否已安装
if exist "%INSTALL_DIR%\bin\psql.exe" (
    echo [INFO] 发现PostgreSQL已安装在: %INSTALL_DIR%
    echo [INFO] 检查服务状态...
    
    sc query postgresql-x64-%POSTGRESQL_VERSION% >nul 2>&1
    if !errorLevel! == 0 (
        echo [INFO] PostgreSQL服务已存在
        sc query postgresql-x64-%POSTGRESQL_VERSION% | find "RUNNING" >nul
        if !errorLevel! == 0 (
            echo [SUCCESS] PostgreSQL服务正在运行
        ) else (
            echo [INFO] 启动PostgreSQL服务...
            net start postgresql-x64-%POSTGRESQL_VERSION%
        )
    ) else (
        echo [WARNING] PostgreSQL已安装但服务未找到
    )
    
    echo [INFO] 测试PostgreSQL连接...
    "%INSTALL_DIR%\bin\psql.exe" --version
    if !errorLevel! == 0 (
        echo [SUCCESS] PostgreSQL安装正常
        goto :end
    )
)

echo [INFO] PostgreSQL未安装，开始下载和安装...

REM 创建临时目录
set TEMP_DIR=%TEMP%\postgresql_install
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

REM 设置安装文件路径
set INSTALLER_FILE=%TEMP_DIR%\postgresql-installer.exe

echo [INFO] 下载PostgreSQL安装包...
echo [INFO] 下载地址: %DOWNLOAD_URL%

REM 使用PowerShell下载文件
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri '%DOWNLOAD_URL%' -OutFile '%INSTALLER_FILE%'}"

if not exist "%INSTALLER_FILE%" (
    echo [ERROR] 下载失败！请检查网络连接或手动下载PostgreSQL
    echo [INFO] 手动下载地址: https://www.postgresql.org/download/windows/
    pause
    exit /b 1
)

echo [INFO] 开始安装PostgreSQL...
echo [INFO] 安装过程中会提示设置postgres用户密码，建议设置为: postgres

REM 静默安装PostgreSQL
"%INSTALLER_FILE%" --mode unattended ^
    --superpassword "postgres" ^
    --unattendedmodeui none ^
    --install_runtimes 0 ^
    --enable-components server,pgAdmin,stackbuilder,commandlinetools ^
    --disable-stackbuilder 1

if %errorLevel% neq 0 (
    echo [ERROR] PostgreSQL安装失败！
    echo [INFO] 请手动安装PostgreSQL或检查系统要求
    pause
    exit /b 1
)

echo [INFO] 等待安装完成...
timeout /t 10

echo [INFO] 验证安装...
if exist "%INSTALL_DIR%\bin\psql.exe" (
    echo [SUCCESS] PostgreSQL安装成功！
    
    REM 添加到系统PATH
    echo [INFO] 添加PostgreSQL到系统PATH...
    setx PATH "%PATH%;%INSTALL_DIR%\bin" /M
    
    REM 启动服务
    echo [INFO] 启动PostgreSQL服务...
    net start postgresql-x64-%POSTGRESQL_VERSION%
    
    REM 测试连接
    echo [INFO] 测试数据库连接...
    "%INSTALL_DIR%\bin\psql.exe" --version
    
    echo [SUCCESS] PostgreSQL安装和配置完成！
    echo [INFO] 数据库服务器地址: localhost:5432
    echo [INFO] 管理员用户: postgres
    echo [INFO] 管理员密码: postgres
    
) else (
    echo [ERROR] 安装验证失败！PostgreSQL可能未正确安装
    exit /b 1
)

REM 清理临时文件
echo [INFO] 清理临时文件...
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"

:end
echo ================================================
echo PostgreSQL安装完成！
echo ================================================
echo.
echo 下一步：运行 setup-database.bat 创建项目数据库
echo.
pause 