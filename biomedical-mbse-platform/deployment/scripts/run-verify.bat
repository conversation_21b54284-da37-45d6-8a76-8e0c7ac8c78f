@echo off
chcp 65001 >nul

echo 正在验证动态Element架构完整性...
echo.

REM 设置数据库连接参数
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=biomedical_mbse_platform
set DB_USER=mbse_user
set DB_PASSWORD=mbse_pass_2024

REM 查找PostgreSQL安装目录
set PSQL_PATH=
for /d %%i in ("C:\Program Files\PostgreSQL\*") do (
    if exist "%%i\bin\psql.exe" (
        set PSQL_PATH=%%i\bin\psql.exe
        goto found
    )
)

:found
if "%PSQL_PATH%"=="" (
    echo [ERROR] 未找到PostgreSQL psql工具
    pause
    exit /b 1
)

echo 使用PostgreSQL: %PSQL_PATH%
echo.

REM 设置密码环境变量
set PGPASSWORD=%DB_PASSWORD%

REM 运行验证脚本
"%PSQL_PATH%" -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f verify-dynamic-schema.sql

REM 清理环境变量
set PGPASSWORD=

echo.
echo 验证完成！
pause 