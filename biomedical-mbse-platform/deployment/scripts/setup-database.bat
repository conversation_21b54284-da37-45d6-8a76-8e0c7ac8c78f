@echo off
REM =====================================================
REM 数据库设置脚本
REM 创建数据库和用户
REM =====================================================

setlocal EnableDelayedExpansion

REM 设置控制台编码为936(GBK)以正确显示中文
chcp 936 >nul 2>&1

echo ================================================
echo 数据库设置脚本
echo ================================================

REM 设置变量
set POSTGRESQL_VERSION=17
set PSQL_PATH="C:\Program Files\PostgreSQL\%POSTGRESQL_VERSION%\bin\psql.exe"
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=biomedical_mbse_platform
set DB_USER=mbse_user
set DB_PASSWORD=mbse_pass_2024
set ADMIN_USER=postgres

REM 获取脚本目录
set SCRIPT_DIR=%~dp0
set SQL_DIR=%SCRIPT_DIR%..\sql

echo [INFO] 检查PostgreSQL安装...
if not exist %PSQL_PATH% (
    echo [ERROR] PostgreSQL未找到！请先运行 install-postgresql.bat
    pause
    exit /b 1
)

echo [INFO] 检查PostgreSQL服务状态...
sc query postgresql-x64-%POSTGRESQL_VERSION% | find "RUNNING" >nul
if %errorLevel% neq 0 (
    echo [INFO] 启动PostgreSQL服务...
    net start postgresql-x64-%POSTGRESQL_VERSION%
    timeout /t 5
)

echo [INFO] 测试PostgreSQL连接...

echo [INFO] 请输入PostgreSQL管理员密码（通常在安装时设置）
set /p ADMIN_PASSWORD="请输入postgres用户密码: "

if "%ADMIN_PASSWORD%"=="" (
    echo [WARNING] 密码为空，使用默认密码 'postgres'
    set ADMIN_PASSWORD=postgres
)

set PGPASSWORD=%ADMIN_PASSWORD%
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %ADMIN_USER% -c "SELECT version();" 2>nul
if %errorLevel% neq 0 (
    echo [ERROR] 无法连接到PostgreSQL！
    echo [INFO] 请检查：
    echo [INFO] 1. PostgreSQL服务是否运行
    echo [INFO] 2. postgres用户密码是否为 'postgres'
    echo [INFO] 3. 防火墙设置
    pause
    exit /b 1
)

echo [SUCCESS] PostgreSQL连接正常

echo [INFO] 检查数据库是否已存在...

REM 使用psql查询数据库是否存在，而不是使用cut命令
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %ADMIN_USER% -c "SELECT 1 FROM pg_database WHERE datname = '%DB_NAME%';" -t | findstr /C:"1" >nul
if %errorLevel% == 0 (
    echo [WARNING] 数据库 %DB_NAME% 已存在
    set /p RECREATE="是否重新创建数据库？(y/N): "
    if /i "!RECREATE!"=="y" (
        echo [INFO] 删除现有数据库...
        %PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %ADMIN_USER% -c "DROP DATABASE IF EXISTS %DB_NAME%;"
        %PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %ADMIN_USER% -c "DROP USER IF EXISTS %DB_USER%;"
    ) else (
        echo [INFO] 跳过数据库创建，使用现有数据库
        goto :init_schema
    )
)

echo [INFO] 创建数据库和用户...

REM 设置PGPASSWORD环境变量
set PGPASSWORD=%ADMIN_PASSWORD%

echo [INFO] 执行数据库创建脚本...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %ADMIN_USER% -f "%SQL_DIR%\create-database.sql" --set client_encoding=UTF8

if %errorLevel% neq 0 (
    echo [ERROR] 数据库创建失败！
    pause
    exit /b 1
)

echo [SUCCESS] 数据库创建完成

:init_schema
echo [INFO] 初始化核心Schema...

REM 执行核心Schema初始化
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %ADMIN_USER% -f "%SQL_DIR%\create-core-tables.sql" --set client_encoding=UTF8

if %errorLevel% neq 0 (
    echo [ERROR] 核心Schema初始化失败！
    pause
    exit /b 1
)

echo [SUCCESS] 核心Schema初始化完成

echo [INFO] 验证数据库设置...

REM 使用新创建的用户测试连接
set PGPASSWORD=%DB_PASSWORD%
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 'Database connection test successful' as result;"

if %errorLevel% neq 0 (
    echo [ERROR] 用户连接测试失败！
    pause
    exit /b 1
)

echo [INFO] 检查核心表是否创建成功...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT count(*) as core_tables FROM information_schema.tables WHERE table_schema = 'core_schema';"

echo [INFO] 检查系统配置...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT config_key, config_value FROM public.system_config WHERE config_key IN ('platform.initialized', 'element_architecture.enabled');"

echo ================================================
echo 数据库设置完成！
echo ================================================
echo.
echo 数据库信息：
echo - 主机: %DB_HOST%:%DB_PORT%
echo - 数据库: %DB_NAME%
echo - 用户: %DB_USER%
echo - 密码: %DB_PASSWORD%
echo.
echo 管理员信息：
echo - 用户: %ADMIN_USER%
echo - 密码: %ADMIN_PASSWORD%
echo.
echo 下一步：运行 init-database.bat 初始化安全领域
echo.

REM 清理环境变量
set PGPASSWORD=

pause 