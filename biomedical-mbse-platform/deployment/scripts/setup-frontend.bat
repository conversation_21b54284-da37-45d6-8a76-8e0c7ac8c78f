@echo off
chcp 65001 >nul
echo ================================================
echo 生物医学MBSE平台 - 前端环境设置
echo ================================================
echo.

REM 检查Node.js是否安装
echo [INFO] 检查Node.js环境...
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Node.js未安装！
    echo [INFO] 请先安装Node.js: https://nodejs.org/
    echo [INFO] 建议版本：Node.js 18.x 或更高版本
    pause
    exit /b 1
)

echo [SUCCESS] 检测到Node.js版本：
node --version

REM 检查npm版本
echo [INFO] 检查npm版本...
npm --version
echo.

REM 切换到前端目录
echo [INFO] 切换到前端目录...
cd /d "%~dp0..\..\frontend"
if %errorLevel% neq 0 (
    echo [ERROR] 前端目录不存在！
    pause
    exit /b 1
)

echo [INFO] 当前目录: %CD%
echo.

REM 清理旧的node_modules（如果存在问题时）
if exist "node_modules" (
    echo [INFO] 检测到已存在的node_modules目录
    set /p clean_install="是否重新安装所有依赖？(y/N): "
    if /i "!clean_install!"=="y" (
        echo [INFO] 清理旧的node_modules...
        rmdir /s /q node_modules 2>nul
    )
)

REM 设置npm镜像源（加速国内下载）
echo [INFO] 设置npm镜像源...
npm config set registry https://registry.npmmirror.com/
if %errorLevel% neq 0 (
    echo [WARNING] 设置镜像源失败，使用默认源
)

REM 安装依赖包
echo [INFO] 安装前端依赖包...
echo [INFO] 这可能需要几分钟时间，请稍候...
echo.

npm install
if %errorLevel% neq 0 (
    echo [ERROR] 依赖包安装失败！
    echo [INFO] 尝试解决方案：
    echo [INFO] 1. 检查网络连接
    echo [INFO] 2. 清理npm缓存: npm cache clean --force
    echo [INFO] 3. 删除node_modules和package-lock.json后重试
    pause
    exit /b 1
)

echo.
echo [SUCCESS] 依赖包安装完成！

REM 验证关键依赖
echo [INFO] 验证关键依赖安装...
echo [INFO] 检查Vue版本...
npm list vue --depth=0 2>nul

echo [INFO] 检查Vite版本...
npm list vite --depth=0 2>nul

echo [INFO] 检查Element Plus版本...
npm list element-plus --depth=0 2>nul

REM 创建开发启动脚本
echo [INFO] 创建前端启动脚本...
set FRONTEND_START_SCRIPT=%~dp0..\..\start_frontend.bat
(
echo @echo off
echo chcp 65001 ^>nul
echo echo 启动前端开发服务器...
echo cd /d "%%~dp0frontend"
echo npm run dev
echo pause
) > "%FRONTEND_START_SCRIPT%"

echo [SUCCESS] 已创建前端启动脚本: %FRONTEND_START_SCRIPT%

REM 创建前端构建脚本
echo [INFO] 创建前端构建脚本...
set FRONTEND_BUILD_SCRIPT=%~dp0..\..\build_frontend.bat
(
echo @echo off
echo chcp 65001 ^>nul
echo echo 构建前端生产版本...
echo cd /d "%%~dp0frontend"
echo npm run build
echo if %%errorLevel%% equ 0 ^(
echo     echo [SUCCESS] 前端构建完成！输出目录: dist/
echo ^) else ^(
echo     echo [ERROR] 前端构建失败！
echo ^)
echo pause
) > "%FRONTEND_BUILD_SCRIPT%"

echo [SUCCESS] 已创建前端构建脚本: %FRONTEND_BUILD_SCRIPT%

REM 测试开发服务器启动
echo [INFO] 测试Vite开发服务器...
echo [INFO] 这将快速测试配置是否正确...
timeout /t 2 >nul
npm run build --dry-run >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Vite配置验证通过！
) else (
    echo [WARNING] Vite配置可能有问题，但基本安装完成
)

echo.
echo ================================================
echo 前端环境设置完成！
echo ================================================
echo.
echo 项目信息：
echo - 技术栈：Vue 3 + TypeScript + Vite
echo - UI组件库：Element Plus  
echo - 图形组件：AntV G6/X6
echo - 3D组件：Three.js
echo.
echo 可用脚本：
echo - start_frontend.bat    (启动开发服务器)
echo - build_frontend.bat    (构建生产版本)
echo.
echo 开发服务器地址：http://localhost:3000 (启动后)
echo.
echo 下一步：运行 start-services.bat 启动完整系统
echo.
pause 