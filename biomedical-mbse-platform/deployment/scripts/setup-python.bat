@echo off
REM =====================================================
REM Python环境设置脚本
REM 创建虚拟环境并安装依赖
REM =====================================================

setlocal EnableDelayedExpansion

REM 设置控制台编码为936(GBK)以正确显示中文
chcp 936 >nul 2>&1

echo ================================================
echo Python环境设置脚本
echo ================================================

REM 获取脚本目录
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..\..
set BACKEND_DIR=%PROJECT_DIR%\backend
set VENV_DIR=%PROJECT_DIR%\venv

echo [INFO] 检查Python安装...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Python未安装或未添加到PATH！
    echo [INFO] 请从以下地址下载并安装Python 3.11+:
    echo [INFO] https://www.python.org/downloads/
    echo [INFO] 安装时请勾选"Add Python to PATH"
    pause
    exit /b 1
)

REM 检查Python版本
echo [INFO] 检查Python版本...
for /f "tokens=2" %%v in ('python --version 2^>^&1') do set PYTHON_VERSION=%%v
echo [INFO] 当前Python版本: %PYTHON_VERSION%

REM 检查Python版本（简化处理，对于3.x版本都接受）
echo %PYTHON_VERSION% | findstr "^3\." >nul
if %errorLevel% neq 0 (
    echo [WARNING] 建议使用Python 3.11+，当前版本可能不兼容
    set /p CONTINUE="是否继续安装？(y/N): "
    if /i not "!CONTINUE!"=="y" (
        echo [INFO] 安装取消
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] Python版本兼容 (Python 3.x)
)

echo [INFO] 检查pip安装...
python -m pip --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] pip未安装！请重新安装Python
    pause
    exit /b 1
)

echo [INFO] 升级pip到最新版本...
python -m pip install --upgrade pip

echo [INFO] 检查项目目录...
if not exist "%BACKEND_DIR%" (
    echo [ERROR] 后端项目目录未找到: %BACKEND_DIR%
    echo [INFO] 请确保在正确的项目目录下运行此脚本
    pause
    exit /b 1
)

echo [INFO] 检查虚拟环境...
if exist "%VENV_DIR%" (
    echo [WARNING] 虚拟环境已存在: %VENV_DIR%
    set /p RECREATE="是否重新创建虚拟环境？(y/N): "
    if /i "!RECREATE!"=="y" (
        echo [INFO] 删除现有虚拟环境...
        rmdir /s /q "%VENV_DIR%"
    ) else (
        echo [INFO] 使用现有虚拟环境
        goto :activate_venv
    )
)

echo [INFO] 创建Python虚拟环境...
python -m venv "%VENV_DIR%"

if %errorLevel% neq 0 (
    echo [ERROR] 虚拟环境创建失败！
    pause
    exit /b 1
)

echo [SUCCESS] 虚拟环境创建完成

:activate_venv
echo [INFO] 激活虚拟环境...
call "%VENV_DIR%\Scripts\activate.bat"

if %errorLevel% neq 0 (
    echo [ERROR] 虚拟环境激活失败！
    pause
    exit /b 1
)

echo [SUCCESS] 虚拟环境已激活

echo [INFO] 升级虚拟环境中的pip...
python -m pip install --upgrade pip

echo [INFO] 检查requirements.txt文件...
if not exist "%BACKEND_DIR%\requirements.txt" (
    echo [WARNING] requirements.txt未找到，创建基础依赖文件...
    
    echo # 生物医学MBSE平台依赖包 > "%BACKEND_DIR%\requirements.txt"
    echo # 版本：1.0.0 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # Web框架 >> "%BACKEND_DIR%\requirements.txt"
    echo fastapi==0.104.1 >> "%BACKEND_DIR%\requirements.txt"
    echo uvicorn[standard]==0.24.0 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # 数据库 >> "%BACKEND_DIR%\requirements.txt"
    echo asyncpg==0.29.0 >> "%BACKEND_DIR%\requirements.txt"
    echo sqlalchemy[asyncio]==2.0.23 >> "%BACKEND_DIR%\requirements.txt"
    echo alembic==1.12.1 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # 认证和安全 >> "%BACKEND_DIR%\requirements.txt"
    echo python-jose[cryptography]==3.3.0 >> "%BACKEND_DIR%\requirements.txt"
    echo passlib[bcrypt]==1.7.4 >> "%BACKEND_DIR%\requirements.txt"
    echo python-multipart==0.0.6 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # 配置管理 >> "%BACKEND_DIR%\requirements.txt"
    echo pydantic==2.5.0 >> "%BACKEND_DIR%\requirements.txt"
    echo pydantic-settings==2.1.0 >> "%BACKEND_DIR%\requirements.txt"
    echo python-dotenv==1.0.0 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # 数据处理 >> "%BACKEND_DIR%\requirements.txt"
    echo pydantic==2.5.0 >> "%BACKEND_DIR%\requirements.txt"
    echo orjson==3.9.10 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # 日志和监控 >> "%BACKEND_DIR%\requirements.txt"
    echo loguru==0.7.2 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # 开发工具 >> "%BACKEND_DIR%\requirements.txt"
    echo pytest==7.4.3 >> "%BACKEND_DIR%\requirements.txt"
    echo pytest-asyncio==0.21.1 >> "%BACKEND_DIR%\requirements.txt"
    echo httpx==0.25.2 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # YAML配置支持 >> "%BACKEND_DIR%\requirements.txt"
    echo PyYAML==6.0.1 >> "%BACKEND_DIR%\requirements.txt"
    echo. >> "%BACKEND_DIR%\requirements.txt"
    echo # 时间处理 >> "%BACKEND_DIR%\requirements.txt"
    echo python-dateutil==2.8.2 >> "%BACKEND_DIR%\requirements.txt"
    
    echo [INFO] 已创建基础requirements.txt文件
)

echo [INFO] 安装Python依赖包...
echo [INFO] 这可能需要几分钟时间，请稍候...

python -m pip install -r "%BACKEND_DIR%\requirements.txt"

if %errorLevel% neq 0 (
    echo [ERROR] 依赖包安装失败！
    echo [INFO] 请检查网络连接和requirements.txt文件
    pause
    exit /b 1
)

echo [SUCCESS] 依赖包安装完成

echo [INFO] 验证关键包安装...
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')"
python -c "import asyncpg; print(f'AsyncPG: {asyncpg.__version__}')"
python -c "import sqlalchemy; print(f'SQLAlchemy: {sqlalchemy.__version__}')"
python -c "import pydantic; print(f'Pydantic: {pydantic.__version__}')"

if %errorLevel% neq 0 (
    echo [ERROR] 关键包验证失败！
    pause
    exit /b 1
)

echo [INFO] 创建环境变量文件...
set ENV_FILE=%BACKEND_DIR%\.env

if not exist "%ENV_FILE%" (
    echo [INFO] 复制环境变量模板...
    copy "%SCRIPT_DIR%..\config\env.local.template" "%ENV_FILE%"
    
    if %errorLevel% neq 0 (
        echo [WARNING] 环境变量模板复制失败，手动创建基础.env文件...
        echo # 数据库配置 > "%ENV_FILE%"
        echo DB_HOST=localhost >> "%ENV_FILE%"
        echo DB_PORT=5432 >> "%ENV_FILE%"
        echo DB_NAME=biomedical_mbse_platform >> "%ENV_FILE%"
        echo DB_USER=mbse_user >> "%ENV_FILE%"
        echo DB_PASSWORD=mbse_pass_2024 >> "%ENV_FILE%"
        echo. >> "%ENV_FILE%"
        echo # 应用配置 >> "%ENV_FILE%"
        echo APP_HOST=0.0.0.0 >> "%ENV_FILE%"
        echo APP_PORT=8000 >> "%ENV_FILE%"
        echo APP_DEBUG=true >> "%ENV_FILE%"
        echo. >> "%ENV_FILE%"
        echo # 安全配置 >> "%ENV_FILE%"
        echo SECRET_KEY=biomedical-mbse-platform-secret-key-2024 >> "%ENV_FILE%"
    )
    
    echo [INFO] 已创建环境变量文件: %ENV_FILE%
) else (
    echo [INFO] 环境变量文件已存在，跳过创建
)

echo [INFO] 创建启动脚本...
set START_SCRIPT=%PROJECT_DIR%\start_backend.bat

echo @echo off > "%START_SCRIPT%"
echo REM 启动后端服务脚本 >> "%START_SCRIPT%"
echo. >> "%START_SCRIPT%"
echo cd /d "%BACKEND_DIR%" >> "%START_SCRIPT%"
echo call "%VENV_DIR%\Scripts\activate.bat" >> "%START_SCRIPT%"
echo. >> "%START_SCRIPT%"
echo echo 启动生物医学MBSE平台后端服务... >> "%START_SCRIPT%"
echo echo 服务地址: http://localhost:8000 >> "%START_SCRIPT%"
echo echo API文档: http://localhost:8000/docs >> "%START_SCRIPT%"
echo echo. >> "%START_SCRIPT%"
echo. >> "%START_SCRIPT%"
echo python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload >> "%START_SCRIPT%"
echo. >> "%START_SCRIPT%"
echo pause >> "%START_SCRIPT%"

echo [INFO] 已创建启动脚本: %START_SCRIPT%

echo [INFO] 创建开发工具脚本...
set DEV_SCRIPT=%PROJECT_DIR%\dev_tools.bat

echo @echo off > "%DEV_SCRIPT%"
echo REM 开发工具脚本 >> "%DEV_SCRIPT%"
echo. >> "%DEV_SCRIPT%"
echo cd /d "%BACKEND_DIR%" >> "%DEV_SCRIPT%"
echo call "%VENV_DIR%\Scripts\activate.bat" >> "%DEV_SCRIPT%"
echo. >> "%DEV_SCRIPT%"
echo echo 生物医学MBSE平台开发工具 >> "%DEV_SCRIPT%"
echo echo ========================== >> "%DEV_SCRIPT%"
echo echo 1. 运行测试 >> "%DEV_SCRIPT%"
echo echo 2. 格式化代码 >> "%DEV_SCRIPT%"
echo echo 3. 类型检查 >> "%DEV_SCRIPT%"
echo echo 4. 安装新依赖 >> "%DEV_SCRIPT%"
echo echo 5. 数据库迁移 >> "%DEV_SCRIPT%"
echo echo. >> "%DEV_SCRIPT%"
echo. >> "%DEV_SCRIPT%"
echo set /p choice="请选择操作 (1-5): " >> "%DEV_SCRIPT%"
echo. >> "%DEV_SCRIPT%"
echo if "%%choice%%"=="1" python -m pytest >> "%DEV_SCRIPT%"
echo if "%%choice%%"=="2" python -m black . >> "%DEV_SCRIPT%"
echo if "%%choice%%"=="3" python -m mypy . >> "%DEV_SCRIPT%"
echo if "%%choice%%"=="4" ( >> "%DEV_SCRIPT%"
echo     set /p package="请输入包名: " >> "%DEV_SCRIPT%"
echo     python -m pip install %%package%% >> "%DEV_SCRIPT%"
echo     python -m pip freeze ^> requirements.txt >> "%DEV_SCRIPT%"
echo ) >> "%DEV_SCRIPT%"
echo if "%%choice%%"=="5" python -m alembic upgrade head >> "%DEV_SCRIPT%"
echo. >> "%DEV_SCRIPT%"
echo pause >> "%DEV_SCRIPT%"

echo [INFO] 已创建开发工具脚本: %DEV_SCRIPT%

echo ================================================
echo Python环境设置完成！
echo ================================================
echo.
echo 虚拟环境位置: %VENV_DIR%
echo 环境变量文件: %ENV_FILE%
echo 启动脚本: %START_SCRIPT%
echo 开发工具: %DEV_SCRIPT%
echo.
echo 下一步：运行 start-services.bat 启动服务
echo.
echo 手动激活虚拟环境：
echo call %VENV_DIR%\Scripts\activate.bat
echo.

pause 