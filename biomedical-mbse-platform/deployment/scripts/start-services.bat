@echo off
REM =====================================================
REM 启动服务脚本
REM 启动后端API服务
REM =====================================================

setlocal EnableDelayedExpansion

echo ================================================
echo 启动生物医学MBSE平台服务
echo ================================================

REM 获取脚本目录
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..\..
set BACKEND_DIR=%PROJECT_DIR%\backend
set VENV_DIR=%PROJECT_DIR%\venv

REM 数据库配置
set POSTGRESQL_VERSION=17
set PSQL_PATH="C:\Program Files\PostgreSQL\%POSTGRESQL_VERSION%\bin\psql.exe"
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=biomedical_mbse_platform
set DB_USER=mbse_user
set DB_PASSWORD=mbse_pass_2024

echo [INFO] 启动前环境检查...

REM 检查PostgreSQL服务
echo [INFO] 检查PostgreSQL服务状态...
sc query postgresql-x64-%POSTGRESQL_VERSION% | find "RUNNING" >nul
if %errorLevel% neq 0 (
    echo [INFO] 启动PostgreSQL服务...
    net start postgresql-x64-%POSTGRESQL_VERSION%
    if %errorLevel% neq 0 (
        echo [ERROR] PostgreSQL服务启动失败！
        echo [INFO] 请手动启动PostgreSQL服务或检查安装
        pause
        exit /b 1
    )
    timeout /t 3
)

echo [SUCCESS] PostgreSQL服务运行正常

REM 检查数据库连接
echo [INFO] 测试数据库连接...
set PGPASSWORD=%DB_PASSWORD%
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 'Database connected successfully' as status;" 2>nul
if %errorLevel% neq 0 (
    echo [ERROR] 数据库连接失败！
    echo [INFO] 请确保：
    echo [INFO] 1. 数据库已正确创建 (运行 setup-database.bat)
    echo [INFO] 2. 用户权限配置正确
    echo [INFO] 3. 网络连接正常
    pause
    exit /b 1
)

echo [SUCCESS] 数据库连接正常

REM 检查Python虚拟环境
echo [INFO] 检查Python虚拟环境...
if not exist "%VENV_DIR%\Scripts\activate.bat" (
    echo [ERROR] Python虚拟环境未找到！
    echo [INFO] 请先运行 setup-python.bat 创建Python环境
    pause
    exit /b 1
)

echo [INFO] 检查项目文件...
if not exist "%BACKEND_DIR%" (
    echo [ERROR] 后端项目目录未找到: %BACKEND_DIR%
    pause
    exit /b 1
)

if not exist "%BACKEND_DIR%\.env" (
    echo [WARNING] 环境变量文件未找到，使用默认配置
    echo [INFO] 建议运行 setup-python.bat 创建完整配置
)

echo [INFO] 激活Python虚拟环境...
call "%VENV_DIR%\Scripts\activate.bat"
if %errorLevel% neq 0 (
    echo [ERROR] 虚拟环境激活失败！
    pause
    exit /b 1
)

echo [INFO] 切换到后端目录...
cd /d "%BACKEND_DIR%"

echo [INFO] 检查Python依赖...
python -c "import fastapi, uvicorn, asyncpg" 2>nul
if %errorLevel% neq 0 (
    echo [ERROR] 关键Python包未安装！
    echo [INFO] 请运行 setup-python.bat 安装依赖
    pause
    exit /b 1
)

echo [SUCCESS] 环境检查完成

REM 检查是否有现有的main.py文件
if not exist "main.py" (
    echo [WARNING] main.py未找到，创建基础应用文件...
    
    echo # 生物医学MBSE平台主应用 > main.py
    echo from fastapi import FastAPI >> main.py
    echo from fastapi.middleware.cors import CORSMiddleware >> main.py
    echo import uvicorn >> main.py
    echo. >> main.py
    echo app = FastAPI( >> main.py
    echo     title="生物医学MBSE平台", >> main.py
    echo     description="基于动态Element架构的生物医学建模平台", >> main.py
    echo     version="1.0.0", >> main.py
    echo     docs_url="/docs", >> main.py
    echo     redoc_url="/redoc" >> main.py
    echo ) >> main.py
    echo. >> main.py
    echo # CORS配置 >> main.py
    echo app.add_middleware( >> main.py
    echo     CORSMiddleware, >> main.py
    echo     allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"], >> main.py
    echo     allow_credentials=True, >> main.py
    echo     allow_methods=["*"], >> main.py
    echo     allow_headers=["*"], >> main.py
    echo ) >> main.py
    echo. >> main.py
    echo @app.get("/") >> main.py
    echo async def root(): >> main.py
    echo     return {"message": "生物医学MBSE平台API", "version": "1.0.0", "status": "running"} >> main.py
    echo. >> main.py
    echo @app.get("/health") >> main.py
    echo async def health_check(): >> main.py
    echo     return {"status": "healthy", "service": "biomedical-mbse-platform"} >> main.py
    echo. >> main.py
    echo @app.get("/api/v1/info") >> main.py
    echo async def api_info(): >> main.py
    echo     return { >> main.py
    echo         "api_version": "v1", >> main.py
    echo         "features": [ >> main.py
    echo             "Element架构", >> main.py
    echo             "动态Schema", >> main.py
    echo             "跨域关系", >> main.py
    echo             "用户认证" >> main.py
    echo         ], >> main.py
    echo         "database": "PostgreSQL", >> main.py
    echo         "architecture": "Element-based" >> main.py
    echo     } >> main.py
    echo. >> main.py
    echo if __name__ == "__main__": >> main.py
    echo     uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) >> main.py
    
    echo [INFO] 已创建基础main.py文件
)

echo ================================================
echo 启动生物医学MBSE平台后端服务
echo ================================================
echo.
echo 🚀 服务信息：
echo - 服务地址: http://localhost:8000
echo - API文档: http://localhost:8000/docs  
echo - ReDoc文档: http://localhost:8000/redoc
echo - 健康检查: http://localhost:8000/health
echo.
echo 🗄️ 数据库信息：
echo - 数据库: %DB_NAME%
echo - 地址: %DB_HOST%:%DB_PORT%
echo - 用户: %DB_USER%
echo.
echo 📁 项目目录: %BACKEND_DIR%
echo 🐍 虚拟环境: %VENV_DIR%
echo.
echo ⏹️ 按 Ctrl+C 停止服务
echo ================================================
echo.

REM 启动服务
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload

REM 如果服务异常退出
if %errorLevel% neq 0 (
    echo.
    echo [ERROR] 服务启动失败！
    echo [INFO] 可能的原因：
    echo [INFO] 1. 端口8000已被占用
    echo [INFO] 2. 数据库连接问题
    echo [INFO] 3. Python代码语法错误
    echo [INFO] 4. 依赖包缺失
    echo.
    echo [INFO] 诊断建议：
    echo [INFO] 1. 检查端口占用: netstat -ano ^| findstr :8000
    echo [INFO] 2. 检查数据库连接: %SCRIPT_DIR%\test-database.bat
    echo [INFO] 3. 检查Python环境: python --version
    echo [INFO] 4. 查看详细错误日志
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================
echo 服务已停止
echo ================================================
pause 