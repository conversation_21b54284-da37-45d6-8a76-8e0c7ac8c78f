@echo off
REM =====================================================
REM 数据库测试脚本
REM 测试数据库连接和功能
REM =====================================================

setlocal EnableDelayedExpansion

echo ================================================
echo 数据库连接和功能测试
echo ================================================

REM 设置变量
set POSTGRESQL_VERSION=17
set PSQL_PATH="C:\Program Files\PostgreSQL\%POSTGRESQL_VERSION%\bin\psql.exe"
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=biomedical_mbse_platform
set DB_USER=mbse_user
set DB_PASSWORD=mbse_pass_2024

echo [INFO] 测试配置：
echo - 数据库主机: %DB_HOST%:%DB_PORT%
echo - 数据库名称: %DB_NAME%
echo - 用户名: %DB_USER%
echo.

echo [INFO] 1. 检查PostgreSQL服务状态...
sc query postgresql-x64-%POSTGRESQL_VERSION% | find "RUNNING" >nul
if %errorLevel% neq 0 (
    echo [ERROR] PostgreSQL服务未运行！
    echo [INFO] 尝试启动服务...
    net start postgresql-x64-%POSTGRESQL_VERSION%
    if %errorLevel% neq 0 (
        echo [ERROR] 无法启动PostgreSQL服务
        pause
        exit /b 1
    )
    echo [SUCCESS] PostgreSQL服务已启动
) else (
    echo [SUCCESS] PostgreSQL服务运行正常
)

echo.
echo [INFO] 2. 测试基本连接...
set PGPASSWORD=%DB_PASSWORD%

%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 'Connection successful' as result;"
if %errorLevel% neq 0 (
    echo [ERROR] 数据库连接失败！
    echo [INFO] 请检查：
    echo [INFO] - 数据库是否已创建
    echo [INFO] - 用户名和密码是否正确
    echo [INFO] - 网络连接是否正常
    pause
    exit /b 1
)

echo [SUCCESS] 数据库连接正常

echo.
echo [INFO] 3. 检查数据库信息...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT current_database() as database, current_user as user, version() as version;"

echo.
echo [INFO] 4. 检查Schema结构...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name IN ('core_schema', 'security_schema', 'public') ORDER BY schema_name;"

echo.
echo [INFO] 5. 检查核心表...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT schemaname, tablename FROM pg_tables WHERE schemaname IN ('core_schema', 'security_schema') ORDER BY schemaname, tablename;"

echo.
echo [INFO] 6. 检查数据统计...
echo [INFO] 6.1 系统配置：
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT config_key, config_value FROM public.system_config ORDER BY config_key;"

echo.
echo [INFO] 6.2 领域注册：
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT domain_name, schema_name, display_name, is_active FROM core_schema.domain_registry ORDER BY domain_name;"

echo.
echo [INFO] 6.3 用户数据：
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT 'Users: ' || count(*) as count FROM security_schema.user_elements UNION ALL SELECT 'Roles: ' || count(*) FROM security_schema.role_elements UNION ALL SELECT 'Permissions: ' || count(*) FROM security_schema.permission_elements;"

echo.
echo [INFO] 7. 测试Element元数据查询...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT element_id, domain_schema, element_type, tag FROM core_schema.element_metadata ORDER BY domain_schema, element_type;"

echo.
echo [INFO] 8. 测试数据库写入（创建测试Element）...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "INSERT INTO core_schema.element_metadata (element_id, domain_schema, element_type, tag, local_name, description) VALUES ('test_element_' || floor(extract(epoch from now()))::text, 'core_schema', 'test_element', 'test', 'test_element', '数据库测试Element') ON CONFLICT DO NOTHING; SELECT 'Test element created' as result;"

echo.
echo [INFO] 9. 测试复杂查询（跨Schema查询）...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT d.domain_name, d.display_name, count(e.element_id) as element_count FROM core_schema.domain_registry d LEFT JOIN core_schema.element_metadata e ON d.schema_name = e.domain_schema GROUP BY d.domain_name, d.display_name ORDER BY d.domain_name;"

echo.
echo [INFO] 10. 测试索引性能...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM core_schema.element_metadata WHERE domain_schema = 'security_schema' AND element_type = 'user_element';"

echo.
echo [INFO] 11. 检查数据库大小...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT pg_database.datname as database_name, pg_size_pretty(pg_database_size(pg_database.datname)) as size FROM pg_database WHERE datname = current_database();"

echo.
echo [INFO] 12. 检查连接信息...
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT application_name, client_addr, state, query_start FROM pg_stat_activity WHERE datname = current_database() AND pid != pg_backend_pid();"

echo.
echo ================================================
echo 测试完成总结
echo ================================================

REM 最终验证
%PSQL_PATH% -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT CASE WHEN EXISTS (SELECT 1 FROM core_schema.domain_registry WHERE domain_name = 'core') AND EXISTS (SELECT 1 FROM core_schema.domain_registry WHERE domain_name = 'security') AND EXISTS (SELECT 1 FROM security_schema.user_elements WHERE username = 'admin') THEN '✅ 数据库测试通过' ELSE '❌ 数据库测试失败' END as test_result;"

if %errorLevel% neq 0 (
    echo [ERROR] 最终验证失败！
    pause
    exit /b 1
)

echo.
echo [SUCCESS] 所有测试完成！
echo.
echo 数据库状态：正常
echo 核心Schema：已创建
echo 安全领域：已初始化
echo 测试数据：已验证
echo.
echo 如果需要重置数据库，请运行：
echo scripts\setup-database.bat
echo.

REM 清理环境变量
set PGPASSWORD=

pause 