-- =====================================================
-- 动态Element架构完整性验证脚本
-- =====================================================

\echo '========================================='
\echo '动态Element架构完整性验证'
\echo '========================================='

-- 1. 检查核心表结构
\echo ''
\echo '1. 核心表结构验证:'
\echo '-------------------'

-- 检查element_type_definitions表结构
\echo '1.1 element_type_definitions表字段:'
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'core_schema' 
  AND table_name = 'element_type_definitions' 
ORDER BY ordinal_position;

\echo ''
\echo '1.2 dynamic_schemas表字段:'
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'core_schema' 
  AND table_name = 'dynamic_schemas' 
ORDER BY ordinal_position;

\echo ''
\echo '1.3 cross_domain_relationships表字段:'
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'core_schema' 
  AND table_name = 'cross_domain_relationships' 
ORDER BY ordinal_position;

-- 2. 检查索引
\echo ''
\echo '2. 动态架构相关索引:'
\echo '-------------------'
SELECT schemaname, tablename, indexname, indexdef 
FROM pg_indexes 
WHERE schemaname = 'core_schema' 
  AND tablename IN ('element_type_definitions', 'dynamic_schemas', 'cross_domain_relationships')
ORDER BY tablename, indexname;

-- 3. 检查触发器
\echo ''
\echo '3. 动态架构触发器:'
\echo '-------------------'
SELECT event_object_schema, event_object_table, trigger_name, action_timing, event_manipulation
FROM information_schema.triggers 
WHERE event_object_schema = 'core_schema'
ORDER BY event_object_table, trigger_name;

-- 4. 检查视图
\echo ''
\echo '4. 动态架构视图:'
\echo '-------------------'
SELECT table_schema, table_name, view_definition 
FROM information_schema.views 
WHERE table_schema = 'core_schema'
ORDER BY table_name;

-- 5. 检查外键约束
\echo ''
\echo '5. 外键约束:'
\echo '-------------------'
SELECT 
    tc.table_schema, 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_schema = 'core_schema'
ORDER BY tc.table_name, tc.constraint_name;

-- 6. 检查数据状态
\echo ''
\echo '6. 数据状态验证:'
\echo '-------------------'

\echo '6.1 领域注册状态:'
SELECT domain_name, schema_name, display_name, is_active, 
       domain_config->>'element_types' as element_types_count,
       enable_cross_domain_refs, auto_create_indexes
FROM core_schema.domain_registry 
ORDER BY domain_name;

\echo ''
\echo '6.2 Element类型定义状态:'
SELECT type_id, type_name, domain_schema, is_active, is_system_type,
       jsonb_array_length(COALESCE(field_definitions->'fields', '[]'::jsonb)) as field_count
FROM core_schema.element_type_definitions 
ORDER BY domain_schema, type_id;

\echo ''
\echo '6.3 动态Schema状态:'
SELECT schema_name, domain_name, creation_status,
       jsonb_array_length(COALESCE(created_tables, '[]'::jsonb)) as table_count,
       jsonb_array_length(COALESCE(created_indexes, '[]'::jsonb)) as index_count
FROM core_schema.dynamic_schemas
ORDER BY schema_name;

\echo ''
\echo '6.4 跨域关系统计:'
SELECT source_domain, target_domain, relationship_type, count(*) as count
FROM core_schema.cross_domain_relationships
GROUP BY source_domain, target_domain, relationship_type
ORDER BY source_domain, target_domain;

\echo ''
\echo '========================================='
\echo '验证完成'
\echo '=========================================' 