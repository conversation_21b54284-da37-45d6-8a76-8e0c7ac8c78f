-- Create core tables for Element architecture
-- Version: 1.0.0
-- No Chinese characters to avoid encoding issues

\c biomedical_mbse_platform;

-- Create core schema
CREATE SCHEMA IF NOT EXISTS core_schema;

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION core_schema.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 1. Element metadata table
CREATE TABLE IF NOT EXISTS core_schema.element_metadata (
    element_id TEXT PRIMARY KEY,
    domain_schema TEXT NOT NULL,
    element_type TEXT NOT NULL,
    tag TEXT NOT NULL,
    local_name TEXT NOT NULL,
    
    -- Element properties
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deprecated', 'deleted', 'pending')),
    version TEXT DEFAULT '1.0',
    
    -- Hierarchy
    parent_element_id TEXT,
    depth_level INTEGER DEFAULT 0 CHECK (depth_level >= 0),
    hierarchy_path TEXT,
    
    -- Semantics
    semantic_tags JSONB DEFAULT '[]',
    ontology_mappings JSONB DEFAULT '{}',
    domain_category TEXT,
    domain_code TEXT,
    
    -- Metadata
    description TEXT,
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    updated_by TEXT,
    
    -- Constraints
    FOREIGN KEY (parent_element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE SET NULL,
    CONSTRAINT valid_element_id CHECK (element_id ~ '^[a-zA-Z][a-zA-Z0-9_-]*$'),
    CONSTRAINT valid_hierarchy_path CHECK (hierarchy_path IS NULL OR hierarchy_path ~ '^(/[^/]+)+/$')
);

-- 2. Domain registry table
CREATE TABLE IF NOT EXISTS core_schema.domain_registry (
    domain_name TEXT PRIMARY KEY,
    schema_name TEXT UNIQUE NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    
    -- Domain config
    domain_config JSONB NOT NULL DEFAULT '{}',
    element_types JSONB NOT NULL DEFAULT '[]',
    api_endpoints JSONB DEFAULT '{}',
    
    -- Dynamic schema config
    schema_template JSONB DEFAULT '{}',
    auto_create_indexes BOOLEAN DEFAULT true,
    enable_cross_domain_refs BOOLEAN DEFAULT true,
    
    -- Domain metadata
    namespace_prefix TEXT,
    ontology_uri TEXT,
    version TEXT DEFAULT '1.0',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_core_domain BOOLEAN DEFAULT false,
    is_auto_generated BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT valid_schema_name CHECK (schema_name ~ '^[a-z][a-z0-9_]*_schema$'),
    CONSTRAINT valid_namespace_prefix CHECK (namespace_prefix IS NULL OR namespace_prefix ~ '^[a-z][a-z0-9]*$')
);

-- 3. Element type definitions table
CREATE TABLE IF NOT EXISTS core_schema.element_type_definitions (
    type_id TEXT PRIMARY KEY,
    type_name TEXT NOT NULL,
    domain_schema TEXT NOT NULL,
    
    -- Field definitions (JSON Schema format)
    field_definitions JSONB NOT NULL,
    table_name TEXT NOT NULL,
    
    -- Index definitions
    index_definitions JSONB DEFAULT '[]',
    constraint_definitions JSONB DEFAULT '[]',
    
    -- Relationship definitions
    relationship_definitions JSONB DEFAULT '{}',
    cross_domain_refs JSONB DEFAULT '[]',
    
    -- Type config
    type_config JSONB DEFAULT '{
        "auto_create_table": true,
        "supports_versioning": false,
        "enable_audit": true,
        "cache_policy": "default"
    }',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    is_system_type BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (domain_schema) REFERENCES core_schema.domain_registry(schema_name),
    
    -- Constraints
    CONSTRAINT valid_type_id CHECK (type_id ~ '^[a-z][a-z0-9_]*_element$'),
    CONSTRAINT valid_table_name CHECK (table_name ~ '^[a-z][a-z0-9_]*_elements$')
);

-- 4. Dynamic schemas table
CREATE TABLE IF NOT EXISTS core_schema.dynamic_schemas (
    schema_name TEXT PRIMARY KEY,
    domain_name TEXT NOT NULL,
    
    -- Schema status
    creation_status TEXT DEFAULT 'pending' CHECK (creation_status IN ('pending', 'creating', 'active', 'error')),
    creation_log JSONB DEFAULT '[]',
    
    -- Schema info
    created_tables JSONB DEFAULT '[]',
    created_indexes JSONB DEFAULT '[]',
    created_triggers JSONB DEFAULT '[]',
    
    -- Dependencies
    dependencies JSONB DEFAULT '[]',
    dependents JSONB DEFAULT '[]',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (domain_name) REFERENCES core_schema.domain_registry(domain_name)
);

-- 5. Cross-domain relationships table
CREATE TABLE IF NOT EXISTS core_schema.cross_domain_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    relationship_id TEXT UNIQUE NOT NULL,
    
    -- Relationship endpoints
    source_element_id TEXT NOT NULL,
    target_element_id TEXT NOT NULL,
    source_domain TEXT NOT NULL,
    target_domain TEXT NOT NULL,
    
    -- Relationship type
    relationship_type TEXT NOT NULL,
    relationship_direction TEXT DEFAULT 'directed' CHECK (relationship_direction IN ('directed', 'undirected', 'bidirectional')),
    
    -- Relationship properties
    relationship_data JSONB DEFAULT '{}',
    strength DECIMAL(3,2) DEFAULT 1.0 CHECK (strength >= 0.0 AND strength <= 1.0),
    is_direct BOOLEAN DEFAULT true,
    
    -- Index optimization fields
    source_type TEXT,
    target_type TEXT,
    
    -- Relationship semantics
    semantic_meaning TEXT,
    ontology_mapping JSONB DEFAULT '{}',
    
    -- Timestamps and status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deprecated', 'deleted')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT,
    
    -- Foreign key constraints
    FOREIGN KEY (source_element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
    FOREIGN KEY (target_element_id) REFERENCES core_schema.element_metadata(element_id) ON DELETE CASCADE,
    FOREIGN KEY (source_domain) REFERENCES core_schema.domain_registry(domain_name),
    FOREIGN KEY (target_domain) REFERENCES core_schema.domain_registry(domain_name),
    
    -- Unique constraints
    UNIQUE(source_element_id, target_element_id, relationship_type),
    
    -- Constraints
    CONSTRAINT no_self_reference CHECK (source_element_id != target_element_id),
    CONSTRAINT valid_relationship_id CHECK (relationship_id ~ '^rel_[a-zA-Z0-9_-]+$')
);

-- 6. Cross-domain indexes table
CREATE TABLE IF NOT EXISTS core_schema.cross_domain_indexes (
    index_id TEXT PRIMARY KEY,
    index_name TEXT UNIQUE NOT NULL,
    
    -- Index definition
    source_schema TEXT NOT NULL,
    target_schema TEXT NOT NULL,
    index_type TEXT NOT NULL,
    
    -- Index fields
    indexed_fields JSONB NOT NULL,
    index_expression TEXT,
    
    -- Index config
    is_unique BOOLEAN DEFAULT false,
    is_partial BOOLEAN DEFAULT false,
    partial_condition TEXT,
    
    -- Usage statistics
    usage_count INTEGER DEFAULT 0,
    last_used TIMESTAMP WITH TIME ZONE,
    performance_impact JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    auto_created BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_element_metadata_domain_type ON core_schema.element_metadata(domain_schema, element_type);
CREATE INDEX IF NOT EXISTS idx_element_metadata_status ON core_schema.element_metadata(status) WHERE status != 'deleted';
CREATE INDEX IF NOT EXISTS idx_element_metadata_semantic_tags ON core_schema.element_metadata USING GIN(semantic_tags);
CREATE INDEX IF NOT EXISTS idx_domain_registry_active ON core_schema.domain_registry(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_cross_relationships_source ON core_schema.cross_domain_relationships(source_element_id, source_domain);
CREATE INDEX IF NOT EXISTS idx_cross_relationships_target ON core_schema.cross_domain_relationships(target_element_id, target_domain);

-- Create triggers
CREATE TRIGGER trigger_element_metadata_updated_at 
    BEFORE UPDATE ON core_schema.element_metadata 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

CREATE TRIGGER trigger_domain_registry_updated_at 
    BEFORE UPDATE ON core_schema.domain_registry 
    FOR EACH ROW EXECUTE FUNCTION core_schema.update_updated_at_column();

-- Insert initial data
INSERT INTO core_schema.domain_registry (
    domain_name, schema_name, display_name, description,
    domain_config, element_types, is_core_domain, namespace_prefix
) VALUES 
    ('core', 'core_schema', 'Core Architecture Domain', 'Provides Element abstraction and cross-domain services',
     '{"provides_abstraction": true, "manages_cross_domain": true, "supports_dynamic_creation": true}',
     '["abstract_element", "domain_element", "relationship_element"]',
     true, 'core')
ON CONFLICT (domain_name) DO NOTHING;

-- Set permissions
GRANT USAGE ON SCHEMA core_schema TO mbse_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA core_schema TO mbse_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA core_schema TO mbse_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA core_schema TO mbse_user;

-- Update system config
UPDATE public.system_config 
SET config_value = 'true'
WHERE config_key = 'platform.initialized';

SELECT 'Core schema tables created successfully' as result; 