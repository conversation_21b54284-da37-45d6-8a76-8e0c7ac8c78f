-- =====================================================
-- Biomedical MBSE Platform Database Initialization
-- Version: 1.0.0
-- Created: 2024-12-09
-- Description: Create database, user and basic configuration
-- Note: No Chinese characters to avoid encoding issues
-- =====================================================

-- Connect to default database (usually postgres)
\c postgres;

-- Create database user (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'mbse_user') THEN
        CREATE USER mbse_user WITH PASSWORD 'mbse_pass_2024';
    END IF;
END
$$;

-- Drop existing database if exists
DROP DATABASE IF EXISTS biomedical_mbse_platform;

-- Create database
CREATE DATABASE biomedical_mbse_platform
    WITH 
    OWNER = mbse_user
    ENCODING = 'UTF8'
    TEMPLATE = template0
    CONNECTION LIMIT = -1;

-- Grant database permissions
GRANT ALL PRIVILEGES ON DATABASE biomedical_mbse_platform TO mbse_user;
GRANT CONNECT ON DATABASE biomedical_mbse_platform TO mbse_user;

-- Switch to target database
\c biomedical_mbse_platform;

-- Create necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Grant schema permissions
GRANT USAGE ON SCHEMA public TO mbse_user;
GRANT CREATE ON SCHEMA public TO mbse_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO mbse_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO mbse_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO mbse_user;

-- Set default permissions (for future objects)
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO mbse_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO mbse_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO mbse_user;

-- Create basic roles
DO $$
BEGIN
    -- Create readonly role
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'mbse_reader') THEN
        CREATE ROLE mbse_reader;
        GRANT CONNECT ON DATABASE biomedical_mbse_platform TO mbse_reader;
        GRANT USAGE ON SCHEMA public TO mbse_reader;
        GRANT SELECT ON ALL TABLES IN SCHEMA public TO mbse_reader;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO mbse_reader;
    END IF;
    
    -- Create application role
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'mbse_app') THEN
        CREATE ROLE mbse_app;
        GRANT CONNECT ON DATABASE biomedical_mbse_platform TO mbse_app;
        GRANT USAGE ON SCHEMA public TO mbse_app;
        GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO mbse_app;
        GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO mbse_app;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO mbse_app;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO mbse_app;
    END IF;
    
    -- Assign roles to main user
    GRANT mbse_reader TO mbse_user;
    GRANT mbse_app TO mbse_user;
END
$$;

-- Set database parameters
ALTER DATABASE biomedical_mbse_platform SET timezone TO 'Asia/Shanghai';

-- Create application logs table
CREATE TABLE IF NOT EXISTS public.application_logs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    level VARCHAR(20) NOT NULL,
    logger VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    module VARCHAR(100),
    function_name VARCHAR(100),
    line_number INTEGER,
    extra_data JSONB DEFAULT '{}'
);

-- Create indexes for logs table
CREATE INDEX IF NOT EXISTS idx_app_logs_timestamp ON public.application_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_app_logs_level ON public.application_logs(level);
CREATE INDEX IF NOT EXISTS idx_app_logs_logger ON public.application_logs(logger);
CREATE INDEX IF NOT EXISTS idx_app_logs_module ON public.application_logs(module);

-- Create system configuration table
CREATE TABLE IF NOT EXISTS public.system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for config table
CREATE INDEX IF NOT EXISTS idx_system_config_key ON public.system_config(config_key);
CREATE INDEX IF NOT EXISTS idx_system_config_active ON public.system_config(is_active);

-- Insert default system configuration (using English descriptions)
INSERT INTO public.system_config (config_key, config_value, description) VALUES 
    ('database.version', '"1.0.0"', 'Database version'),
    ('platform.initialized', 'false', 'Platform initialization status'),
    ('element_architecture.enabled', 'true', 'Element architecture enabled'),
    ('dynamic_schema.enabled', 'true', 'Dynamic schema enabled'),
    ('cross_domain.enabled', 'true', 'Cross domain features enabled')
ON CONFLICT (config_key) DO NOTHING;

-- Create auto-update updated_at function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add update trigger for system_config table
CREATE TRIGGER trigger_system_config_updated_at 
    BEFORE UPDATE ON public.system_config 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Display completion information (English only)
SELECT 'Database initialization completed successfully' as status;
SELECT 'Database: ' || current_database() as info
UNION ALL
SELECT 'User: ' || current_user as info
UNION ALL  
SELECT 'Extensions: ' || string_agg(extname, ', ') as info
FROM pg_extension
WHERE extname IN ('uuid-ossp', 'pgcrypto', 'btree_gin', 'pg_trgm'); 