# 🧬 生物医学MBSE平台快速启动指南

## 📖 概述

本指南将帮助您快速在Windows本地环境部署和运行生物医学MBSE平台。平台基于动态Element架构设计，支持跨领域建模和管理。

## 🎯 系统要求

### 硬件要求
- CPU: Intel/AMD 双核 2.0GHz+
- 内存: 8GB RAM+
- 硬盘: 10GB 可用空间
- 网络: 稳定的互联网连接

### 软件要求
- Windows 10/11 (64位)
- 管理员权限
- 现代浏览器 (Chrome/Firefox/Edge)

## 🚀 一键部署

### 方法1：完全自动部署 ⭐️推荐

1. **以管理员身份**打开命令提示符或PowerShell
2. 导航到部署目录：
   ```batch
   cd biomedical-mbse-platform\deployment
   ```
3. 运行一键部署脚本：
   ```batch
   deploy.bat
   ```
4. 按照提示确认安装
5. 等待自动完成（约10-20分钟）

### 方法2：分步手动部署

如果自动部署遇到问题，可以分步执行：

```batch
# 1. 安装PostgreSQL数据库
scripts\install-postgresql.bat

# 2. 创建数据库和用户
scripts\setup-database.bat

# 3. 初始化数据库结构
scripts\init-database.bat

# 4. 设置Python环境
scripts\setup-python.bat

# 5. 启动服务
scripts\start-services.bat
```

## 🔧 系统验证

### 检查部署状态
```batch
scripts\check-status.bat
```

### 测试数据库
```batch
scripts\test-database.bat
```

### 测试Element架构
```batch
scripts\test-dynamic-architecture.bat
```

## 🌟 首次使用

### 1. 访问API文档
打开浏览器访问：http://localhost:8000/docs

### 2. 健康检查
访问：http://localhost:8000/health

### 3. 默认账户
- **管理员用户名**: `admin`
- **管理员密码**: `admin123`
- **数据库用户**: `mbse_user`
- **数据库密码**: `mbse_pass_2024`

## 📁 重要文件位置

```
biomedical-mbse-platform/
├── deployment/              # 部署配置
│   ├── deploy.bat           # 一键部署脚本
│   ├── config/              # 配置文件
│   ├── scripts/             # 部署脚本
│   └── sql/                 # 数据库脚本
├── backend/                 # 后端代码
│   ├── main.py             # 主应用文件
│   ├── .env                # 环境变量
│   └── requirements.txt    # Python依赖
├── venv/                   # Python虚拟环境
├── start_backend.bat       # 后端启动脚本
└── logs/                   # 部署日志
```

## 🛠️ 常用命令

### 启动服务
```batch
# 启动后端API服务
scripts\start-services.bat

# 或使用快捷方式
cd .. && start_backend.bat
```

### 停止服务
- 在服务窗口中按 `Ctrl+C`

### 重启PostgreSQL服务
```batch
net stop postgresql-x64-15
net start postgresql-x64-15
```

### 重新初始化数据库
```batch
scripts\setup-database.bat
scripts\init-database.bat
```

## 📊 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| API服务 | http://localhost:8000 | 主要API端点 |
| API文档 | http://localhost:8000/docs | Swagger文档 |
| ReDoc文档 | http://localhost:8000/redoc | ReDoc文档 |
| 健康检查 | http://localhost:8000/health | 服务状态检查 |

## 🏗️ 架构特性

### Element架构
- **统一抽象**: 所有数据都基于Element模型
- **领域隔离**: 不同业务领域独立Schema
- **跨域关系**: 支持领域间的复杂关联
- **动态扩展**: 运行时创建新的Element类型

### 已实现领域
- **核心领域** (core_schema): Element抽象和跨域服务
- **安全领域** (security_schema): 用户认证和权限管理
- **实验室领域** (laboratory_schema): 实验室设备管理 [测试]

### 数据库结构
```
biomedical_mbse_platform 数据库
├── core_schema                 # 核心架构
│   ├── element_metadata       # Element元数据
│   ├── domain_registry        # 领域注册
│   └── cross_domain_relationships  # 跨域关系
├── security_schema            # 安全领域
│   ├── user_elements         # 用户管理
│   ├── role_elements         # 角色管理
│   └── permission_elements   # 权限管理
└── [其他领域Schema]           # 可动态扩展
```

## 🐛 故障排除

### 常见问题

#### 1. PostgreSQL安装失败
- 检查是否有管理员权限
- 关闭杀毒软件临时保护
- 确保端口5432未被占用

#### 2. 数据库连接失败
```batch
# 检查PostgreSQL服务
sc query postgresql-x64-15

# 重启服务
net restart postgresql-x64-15

# 测试连接
scripts\test-database.bat
```

#### 3. Python依赖安装失败
```batch
# 升级pip
python -m pip install --upgrade pip

# 清理缓存
pip cache purge

# 重新安装
scripts\setup-python.bat
```

#### 4. 端口冲突
```batch
# 查看端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :5432

# 结束占用进程
taskkill /PID <进程ID> /F
```

#### 5. 权限问题
- 确保以管理员权限运行脚本
- 检查防火墙设置
- 验证用户账户控制(UAC)设置

### 日志查看
- 部署日志: `deployment\logs\`
- 应用日志: `backend\logs\`
- PostgreSQL日志: `C:\Program Files\PostgreSQL\15\data\log\`

## 🔄 日常维护

### 备份数据库
```batch
# 手动备份
pg_dump -h localhost -U mbse_user -d biomedical_mbse_platform > backup.sql

# 定期备份（建议设置计划任务）
```

### 更新依赖
```batch
# 激活虚拟环境
call venv\Scripts\activate.bat

# 更新依赖
pip install --upgrade -r backend\requirements.txt

# 保存新版本
pip freeze > backend\requirements.txt
```

### 性能监控
```batch
# 系统状态检查
scripts\check-status.bat

# 数据库性能测试
scripts\test-database.bat
```

## 🚀 下一步

### 开发环境
1. 安装开发工具（可选）
   ```batch
   pip install black mypy pytest
   ```

2. 启用开发模式
   - 编辑 `backend\.env`
   - 设置 `APP_DEBUG=true`

### 生产部署
1. 修改安全配置
2. 配置HTTPS
3. 设置防火墙规则
4. 配置监控和日志

### 扩展功能
1. 创建新的业务领域
2. 定义Element类型
3. 建立跨域关系
4. 开发API端点

## 📞 技术支持

### 获取帮助
1. 查看日志文件
2. 运行诊断脚本
3. 检查GitHub文档
4. 联系技术支持团队

### 反馈渠道
- GitHub Issues
- 技术文档Wiki
- 社区论坛

---

## 📋 快速检查清单

**部署完成后，确认以下项目：**

- [ ] PostgreSQL服务运行正常
- [ ] 数据库连接测试通过
- [ ] Python虚拟环境创建成功
- [ ] 关键依赖包安装完整
- [ ] 环境变量文件配置正确
- [ ] 核心Schema初始化完成
- [ ] 安全Schema创建成功
- [ ] API服务启动正常
- [ ] 健康检查端点响应正常
- [ ] API文档可以访问

**如果所有项目都勾选，恭喜您！生物医学MBSE平台已成功部署！** 🎉

---

*更新时间：2024-12-09*
*版本：1.0.0* 