@echo off
REM =====================================================
REM 🧬 生物医学MBSE平台 - 快速部署
REM 解决密码输入和编码问题
REM =====================================================

setlocal EnableDelayedExpansion

REM 设置控制台编码为936(GBK)以正确显示中文
chcp 936 >nul 2>&1

echo.
echo ================================================
echo 🧬 生物医学MBSE平台 快速部署
echo ================================================
echo 📋 根据 config/database.yaml 配置部署
echo 📋 解决密码和编码问题
echo.

REM 从配置读取信息
set DB_NAME=biomedical_mbse_platform
set DB_USER=mbse_user
set DB_PASSWORD=mbse_pass_2024
set PSQL_PATH="C:\Program Files\PostgreSQL\17\bin\psql.exe"

echo [1/4] 📦 检查PostgreSQL...
sc query postgresql-x64-17 | find "RUNNING" >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ PostgreSQL未运行，请先启动PostgreSQL 17
    pause
    exit /b 1
)
echo ✅ PostgreSQL运行正常

echo.
echo [2/4] 🔑 数据库管理员认证...
echo 💡 请输入PostgreSQL安装时设置的postgres用户密码
echo 💡 如果不确定，常见密码有：postgres, admin, 123456

:retry_password
set /p ADMIN_PASSWORD="请输入postgres密码: "
if "%ADMIN_PASSWORD%"=="" (
    echo ❌ 密码不能为空
    goto retry_password
)

echo [INFO] 测试管理员连接...
set PGPASSWORD=%ADMIN_PASSWORD%
%PSQL_PATH% -h localhost -p 5432 -U postgres -c "SELECT 'Connected' as status;" 2>nul
if %errorLevel% neq 0 (
    echo ❌ 密码错误或连接失败
    echo 💡 请重新输入正确的postgres密码
    goto retry_password
)
echo ✅ 管理员认证成功

echo.
echo [3/4] 🗄️ 创建项目数据库...
%PSQL_PATH% -h localhost -p 5432 -U postgres -c "DROP DATABASE IF EXISTS %DB_NAME%;" 2>nul
%PSQL_PATH% -h localhost -p 5432 -U postgres -c "DROP USER IF EXISTS %DB_USER%;" 2>nul
%PSQL_PATH% -h localhost -p 5432 -U postgres -c "CREATE USER %DB_USER% WITH PASSWORD '%DB_PASSWORD%';"
%PSQL_PATH% -h localhost -p 5432 -U postgres -c "CREATE DATABASE %DB_NAME% OWNER %DB_USER%;"
%PSQL_PATH% -h localhost -p 5432 -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE %DB_NAME% TO %DB_USER%;"

if %errorLevel% neq 0 (
    echo ❌ 数据库创建失败
    pause
    exit /b 1
)
echo ✅ 数据库创建完成

echo.
echo [4/4] 📊 初始化数据库结构...
set PGPASSWORD=%DB_PASSWORD%
%PSQL_PATH% -h localhost -p 5432 -U %DB_USER% -d %DB_NAME% -f "sql\create-database.sql"
%PSQL_PATH% -h localhost -p 5432 -U %DB_USER% -d %DB_NAME% -f "sql\create-core-tables.sql"

if %errorLevel% neq 0 (
    echo ❌ 数据库结构初始化失败
    pause
    exit /b 1
)
echo ✅ 数据库结构初始化完成

echo.
echo ================================================
echo 🎉 部署完成！
echo ================================================
echo.
echo 📊 数据库信息：
echo   数据库: %DB_NAME%
echo   用户: %DB_USER%
echo   密码: %DB_PASSWORD%
echo   地址: localhost:5432
echo.
echo 🚀 下一步：
echo   运行 scripts\setup-python.bat 设置Python环境
echo   然后运行 scripts\start-services.bat 启动服务
echo.

pause 