# 生物医学MBSE平台后端架构设计详细说明

> **文档版本**: v1.0  
> **创建时间**: 2024年12月  
> **适用范围**: FastAPI + Python后端服务  
> **关联项目**: biomedical-mbse-platform

## 📋 概述

本文档详细描述了生物医学MBSE建模平台后端架构的设计与实现。后端采用FastAPI + Python技术栈，集成XML解析、UML/SysML建模引擎、生物医学专业化和AI智能辅助功能。

## 🏗️ 技术架构

### 核心技术栈

```
后端技术架构
├── 核心框架
│   ├── FastAPI 0.104+           # 异步Web框架
│   ├── Python 3.11+             # 编程语言
│   └── Uvicorn 0.24+            # ASGI服务器
├── 数据层
│   ├── SQLAlchemy 2.0+          # ORM框架
│   ├── Alembic 1.12+            # 数据库迁移
│   ├── PostgreSQL 15+           # 主数据库
│   └── Redis 7.0+               # 缓存/消息队列
├── 建模引擎
│   ├── lxml 4.9+                # XML解析
│   ├── networkx 3.2+            # 图形算法
│   └── pydantic 2.5+            # 数据验证
├── AI/ML
│   ├── scikit-learn 1.3+        # 机器学习
│   ├── spacy 3.7+               # NLP处理
│   └── sentence-transformers    # 语义向量
└── 工具库
    ├── celery 5.3+              # 异步任务
    ├── httpx 0.25+              # HTTP客户端
    └── pytest 7.4+              # 测试框架
```

### 目录结构

```
backend/
├── api/                         # API路由层
│   ├── routers/                # 路由模块
│   │   ├── enhanced_modeling.py # 增强建模API
│   │   ├── xml_integration.py  # XML集成API
│   │   └── knowledge.py        # 知识管理API
│   └── main.py                 # FastAPI应用入口
├── mbse_core/                  # 核心建模引擎
│   ├── enhanced_uml_engine.py  # 增强UML引擎
│   ├── uml_xml_integration.py  # XML集成模块
│   ├── uml_engine.py           # 基础UML引擎
│   └── sysml_engine.py         # SysML扩展引擎
├── core/                       # 核心功能模块
│   ├── parsing/                # XML解析
│   ├── validation/             # 模型验证
│   └── transformation/         # 模型转换
├── ai_integration/             # AI集成模块
├── database/                   # 数据库模块
├── models/                     # 数据模型
├── services/                   # 业务服务层
├── utils/                      # 工具函数
└── tests/                      # 测试文件
```

## 🔧 核心模块设计

### 1. Enhanced UML Engine - 增强建模引擎

**文件路径**: `mbse_core/enhanced_uml_engine.py`

**核心功能**:
- 🎯 生物医学项目管理
- 🔄 XML模型导入导出
- 🧬 生物医学专业化
- ✅ 模型验证转换
- 🤖 AI辅助建模

**类设计**:
```python
class EnhancedUMLEngine:
    """增强版UML建模引擎"""
    
    def __init__(self):
        self.uml_engine = UMLEngine()
        self.sysml_engine = SysMLEngine(self.uml_engine)
        self.xml_integration = UMLXMLIntegration()
        self.xml_metadata_bridge = XMLMetadataBridge()
        
        # 项目存储和缓存
        self.projects = {}
        self.model_cache = {}
        self.transformation_history = []
        
        # 生物医学配置
        self.biomedical_profiles = self._initialize_biomedical_profiles()
    
    async def create_biomedical_project(self, config: dict) -> str:
        """创建生物医学MBSE项目"""
        project_id = f"proj_{uuid.uuid4().hex[:8]}"
        project = ProjectModel(id=project_id, config=config, engine=self)
        
        # 应用生物医学配置
        domain = config.get('domain', 'molecular_biology')
        if domain in self.biomedical_profiles:
            project.apply_biomedical_profile(self.biomedical_profiles[domain])
        
        self.projects[project_id] = project
        return project_id
    
    async def import_model_from_xml(self, project_id: str, xml_source: str, 
                                   options: dict = None) -> dict:
        """从XML导入模型"""
        project = self.projects.get(project_id)
        if not project:
            raise ValueError(f"项目不存在: {project_id}")
        
        # 使用XML集成模块导入
        import_result = await self.xml_integration.import_xmi_file(xml_source, project_id)
        
        if import_result['success']:
            # 添加导入的图形到项目
            for diagram in import_result['diagrams']:
                project.add_diagram(diagram)
            
            # 生物医学后处理
            await self._post_process_imported_model(project, import_result, options)
        
        return import_result
```

### 2. XML Integration - XML集成模块

**文件路径**: `mbse_core/uml_xml_integration.py`

**核心功能**:
- 📄 XMI文件解析
- 🔍 语义分析
- 🧬 生物医学检测
- 📊 关系推断
- 🔄 双向转换

**关键方法**:
```python
class UMLXMLIntegration:
    """UML建模系统与XML解析集成器"""
    
    async def import_xmi_file(self, xmi_file_path: str, project_id: str = None) -> dict:
        """导入XMI文件到UML模型"""
        try:
            # 1. XML解析
            parse_result = await self.xml_parser.parse(xmi_file_path)
            
            # 2. 转换为UML模型
            conversion_result = await self._convert_xml_to_uml_model(
                parse_result['data']['metadata_store'], project_id
            )
            
            # 3. 生成导入报告
            import_report = self._generate_import_report(parse_result, conversion_result)
            
            return {
                'success': True,
                'project_id': project_id or conversion_result.get('project_id'),
                'diagrams': conversion_result['diagrams'],
                'elements_imported': conversion_result['elements_count'],
                'import_report': import_report
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
```

### 3. API Router - 增强建模API

**文件路径**: `api/routers/enhanced_modeling.py`

**API设计**:
```python
@router.post("/enhanced/projects")
async def create_enhanced_project(request: EnhancedProjectCreateRequest):
    """创建增强版生物医学MBSE项目"""
    project_config = {
        'name': request.name,
        'domain': request.domain,
        'project_type': request.project_type,
        'biomedical_profile': request.biomedical_profile
    }
    
    project_id = await enhanced_uml_engine.create_biomedical_project(project_config)
    
    return {
        'success': True,
        'project_id': project_id,
        'features': {
            'xml_integration': request.enable_xml_integration,
            'biomedical_profile': request.biomedical_profile,
            'enhanced_validation': True
        }
    }

@router.post("/enhanced/projects/{project_id}/import/xml")
async def import_xml_model(project_id: str, file: UploadFile = File(...), 
                          import_options: XMLImportRequest = XMLImportRequest()):
    """导入XML模型文件"""
    # 文件验证
    if not file.filename.lower().endswith(('.xmi', '.xml', '.uml')):
        raise HTTPException(status_code=400, detail="不支持的文件格式")
    
    # 保存临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix='.xml') as temp_file:
        content = await file.read()
        temp_file.write(content)
        temp_file_path = temp_file.name
    
    try:
        # 执行导入
        import_result = await enhanced_uml_engine.import_model_from_xml(
            project_id, temp_file_path, import_options.import_options
        )
        
        return {
            'success': import_result['success'],
            'file_name': file.filename,
            'import_result': import_result
        }
    finally:
        os.unlink(temp_file_path)
```

## 💾 数据模型设计

### 1. 数据库模型

```python
# models/project.py
from sqlalchemy import Column, String, JSON, DateTime, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class Project(Base):
    __tablename__ = 'projects'
    
    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    config = Column(JSON)
    domain = Column(String, default='molecular_biology')
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    owner_id = Column(String, ForeignKey('users.id'))
    
    # 关系
    diagrams = relationship("Diagram", back_populates="project")

class Diagram(Base):
    __tablename__ = 'diagrams'
    
    id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey('projects.id'))
    name = Column(String, nullable=False)
    diagram_type = Column(String)
    diagram_data = Column(JSON)  # UML/SysML图形数据
    metadata = Column(JSON)
    created_at = Column(DateTime)
    
    # 关系
    project = relationship("Project", back_populates="diagrams")
    elements = relationship("Element", back_populates="diagram")

class Element(Base):
    __tablename__ = 'elements'
    
    id = Column(String, primary_key=True)
    diagram_id = Column(String, ForeignKey('diagrams.id'))
    element_type = Column(String)
    name = Column(String)
    properties = Column(JSON)
    biological_type = Column(JSON)  # 生物医学类型信息
    visual_properties = Column(JSON)
    
    # 关系
    diagram = relationship("Diagram", back_populates="elements")
```

### 2. Pydantic模型

```python
# models/schemas.py
from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from datetime import datetime

class ProjectConfig(BaseModel):
    name: str = Field(..., description="项目名称")
    description: Optional[str] = ""
    project_type: str = "biomedical_mbse"
    domain: str = Field("molecular_biology", description="生物医学领域")
    biomedical_profile: Optional[str] = "molecular_biology"
    enable_xml_integration: bool = True

class UMLElement(BaseModel):
    id: str
    name: str
    element_type: str
    biological_type: Optional[Dict[str, Any]] = None
    visual_properties: Dict[str, Any]
    properties: Dict[str, Any] = {}

class BiomedicalType(BaseModel):
    category: str = Field(..., description="生物医学分类")
    profile: str = Field(..., description="应用的配置文件")
    specialized_at: datetime = Field(..., description="专业化时间")
    confidence: float = Field(0.8, description="检测置信度")

class ImportResult(BaseModel):
    success: bool
    project_id: Optional[str] = None
    elements_imported: int = 0
    relationships_imported: int = 0
    diagrams: List[Dict[str, Any]] = []
    import_report: Optional[Dict[str, Any]] = None
```

## 🔄 服务层设计

### 1. 项目服务

```python
# services/project_service.py
from typing import List, Optional
from models.schemas import ProjectConfig, Project
from database.repositories import ProjectRepository

class ProjectService:
    def __init__(self, project_repo: ProjectRepository):
        self.project_repo = project_repo
        
    async def create_project(self, config: ProjectConfig, owner_id: str) -> Project:
        """创建新项目"""
        project_data = {
            'id': f"proj_{uuid.uuid4().hex[:8]}",
            'name': config.name,
            'description': config.description,
            'config': config.dict(),
            'domain': config.domain,
            'owner_id': owner_id,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        return await self.project_repo.create(project_data)
    
    async def get_project(self, project_id: str) -> Optional[Project]:
        """获取项目"""
        return await self.project_repo.get_by_id(project_id)
    
    async def list_projects(self, owner_id: str) -> List[Project]:
        """列出用户项目"""
        return await self.project_repo.list_by_owner(owner_id)
```

### 2. XML处理服务

```python
# services/xml_service.py
class XMLProcessingService:
    def __init__(self):
        self.xml_parser = UnifiedXMLParser()
        self.xml_integration = UMLXMLIntegration()
    
    async def process_xml_import(self, file_path: str, project_id: str, 
                               options: dict) -> dict:
        """处理XML导入"""
        try:
            # 1. 解析XML文件
            parse_result = await self.xml_parser.parse(file_path)
            
            # 2. 转换为UML模型
            conversion_result = await self.xml_integration.convert_to_uml(
                parse_result, project_id
            )
            
            # 3. 应用生物医学增强
            if options.get('apply_biomedical_enhancement', True):
                enhanced_result = await self._apply_biomedical_enhancement(
                    conversion_result
                )
                conversion_result.update(enhanced_result)
            
            return {
                'success': True,
                'result': conversion_result
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
```

## 🤖 AI集成架构

### 1. 生物医学推荐引擎

```python
# ai_integration/biomedical_recommender.py
class BiomedicalRecommendationEngine:
    def __init__(self):
        self.nlp_model = spacy.load("en_core_web_sm")
        self.sentence_transformer = SentenceTransformer('all-MiniLM-L6-v2')
        
    async def recommend_modeling_strategy(self, project_type: str, 
                                        context: dict) -> dict:
        """推荐建模策略"""
        domain = context.get('domain', 'general')
        
        # 基于领域的建模策略
        strategies = {
            'molecular_biology': [
                {
                    'strategy': 'protein_pathway_modeling',
                    'description': '蛋白质通路建模',
                    'confidence': 0.9,
                    'steps': [
                        '识别关键蛋白质',
                        '建立相互作用网络',
                        '添加调控关系',
                        '验证通路完整性'
                    ]
                }
            ],
            'systems_biology': [
                {
                    'strategy': 'multi_scale_modeling',
                    'description': '多尺度系统建模',
                    'confidence': 0.85,
                    'steps': [
                        '分子层次建模',
                        '细胞层次建模',
                        '组织层次建模',
                        '系统整合'
                    ]
                }
            ]
        }
        
        return {
            'recommendations': strategies.get(domain, []),
            'context_analysis': await self._analyze_context(context)
        }
```

### 2. 智能元素检测

```python
# ai_integration/element_detector.py
class BiologicalElementDetector:
    def __init__(self):
        self.bio_keywords = {
            'protein': ['protein', 'enzyme', 'antibody', 'receptor'],
            'gene': ['gene', 'dna', 'chromosome', 'allele'],
            'pathway': ['pathway', 'cascade', 'signaling'],
            'cell': ['cell', 'neuron', 'hepatocyte']
        }
    
    def detect_biological_type(self, element_name: str, context: str = "") -> dict:
        """检测生物医学元素类型"""
        name_lower = element_name.lower()
        context_lower = context.lower()
        
        scores = {}
        for bio_type, keywords in self.bio_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in name_lower:
                    score += 0.8
                if keyword in context_lower:
                    score += 0.3
            scores[bio_type] = score
        
        if scores:
            best_type = max(scores, key=scores.get)
            confidence = min(scores[best_type], 1.0)
            
            if confidence > 0.5:
                return {
                    'detected_type': best_type,
                    'confidence': confidence,
                    'suggestions': self._get_type_suggestions(best_type)
                }
        
        return {'detected_type': None, 'confidence': 0.0}
```

## ⚡ 异步任务处理

### 1. Celery任务配置

```python
# tasks/celery_app.py
from celery import Celery

celery_app = Celery(
    "biomedical_mbse",
    broker="redis://localhost:6379/0",
    backend="redis://localhost:6379/0"
)

celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
)

@celery_app.task
async def process_large_xml_file(file_path: str, project_id: str) -> dict:
    """异步处理大型XML文件"""
    try:
        xml_service = XMLProcessingService()
        result = await xml_service.process_xml_import(file_path, project_id, {})
        return result
    except Exception as e:
        return {'success': False, 'error': str(e)}

@celery_app.task
async def generate_model_suggestions(project_id: str) -> dict:
    """异步生成建模建议"""
    recommender = BiomedicalRecommendationEngine()
    suggestions = await recommender.recommend_modeling_strategy(
        'biomedical', {'project_id': project_id}
    )
    return suggestions
```

## 🔒 安全与认证

### 1. JWT认证

```python
# auth/jwt_handler.py
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from jose import JWTError, jwt

security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="无效的认证令牌")
        return user_id
    except JWTError:
        raise HTTPException(status_code=401, detail="无效的认证令牌")
```

## 🧪 测试架构

### 1. 单元测试

```python
# tests/test_enhanced_uml_engine.py
import pytest
from mbse_core.enhanced_uml_engine import EnhancedUMLEngine

@pytest.fixture
def uml_engine():
    return EnhancedUMLEngine()

@pytest.mark.asyncio
async def test_create_biomedical_project(uml_engine):
    """测试创建生物医学项目"""
    config = {
        'name': '测试项目',
        'domain': 'molecular_biology',
        'project_type': 'biomedical_mbse'
    }
    
    project_id = await uml_engine.create_biomedical_project(config)
    
    assert project_id.startswith('proj_')
    assert project_id in uml_engine.projects
    
    project = uml_engine.projects[project_id]
    assert project.config['name'] == '测试项目'
    assert project.config['domain'] == 'molecular_biology'
```

### 2. 集成测试

```python
# tests/test_api_integration.py
from fastapi.testclient import TestClient
from api.main import app

client = TestClient(app)

def test_create_project_api():
    """测试项目创建API"""
    response = client.post("/enhanced/projects", json={
        "name": "测试项目",
        "domain": "molecular_biology",
        "project_type": "biomedical_mbse"
    })
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "project_id" in data
```

## 📊 监控与日志

### 1. 日志配置

```python
# utils/logging_config.py
import logging
from datetime import datetime

def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'logs/app_{datetime.now().strftime("%Y%m%d")}.log'),
            logging.StreamHandler()
        ]
    )
    
    # 设置特定模块的日志级别
    logging.getLogger('mbse_core').setLevel(logging.DEBUG)
    logging.getLogger('xml_integration').setLevel(logging.INFO)
```

### 2. 性能监控

```python
# utils/performance_monitor.py
import time
from functools import wraps

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 记录性能指标
            logger.info(f"{func.__name__} 执行时间: {execution_time:.2f}秒")
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} 执行失败 (耗时: {execution_time:.2f}秒): {e}")
            raise
    
    return wrapper
```

---

**总结**: 后端架构采用现代化的FastAPI框架，结合强大的建模引擎和AI集成能力，为生物医学MBSE平台提供了robust、scalable和extensible的服务端解决方案。通过合理的分层设计、异步处理和性能优化，确保系统的高性能和可维护性。 