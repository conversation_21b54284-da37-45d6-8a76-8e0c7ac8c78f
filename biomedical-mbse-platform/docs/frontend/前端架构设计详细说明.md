# 生物医学MBSE平台前端架构设计详细说明

> **文档版本**: v1.0  
> **创建时间**: 2024年12月  
> **适用范围**: Vue 3 + TypeScript前端应用  
> **关联项目**: biomedical-mbse-platform

## 📋 概述

本文档详细描述了生物医学MBSE建模平台前端架构的设计与实现。前端采用Vue 3 + TypeScript + Vite技术栈，结合Konva.js图形引擎和Element Plus UI组件库，为用户提供专业的建模界面和丰富的交互体验。

## 🏗️ 技术架构

### 核心技术栈

```
前端技术架构
├── 核心框架
│   ├── Vue 3.3+              # 组合式API + 响应式系统
│   ├── TypeScript 5.0+       # 类型安全
│   └── Vite 4.0+             # 构建工具
├── UI框架
│   ├── Element Plus 2.4+     # UI组件库
│   ├── Tailwind CSS 3.3+     # 样式框架
│   └── @element-plus/icons-vue # 图标库
├── 图形渲染
│   ├── Konva.js 9.2+         # 2D图形渲染
│   ├── D3.js 7.8+            # 数据可视化
│   └── vis-network 9.1+      # 网络图
├── 状态管理
│   ├── Pinia 2.1+            # 状态管理
│   └── VueUse 10.5+          # 组合式工具库
└── 工具库
    ├── Axios 1.6+            # HTTP客户端
    ├── dayjs 1.11+           # 日期处理
    └── lodash-es 4.17+       # 工具函数
```

### 目录结构

```
frontend/
├── src/
│   ├── components/           # 组件目录
│   │   ├── modeling/        # 建模相关组件
│   │   ├── knowledge/       # 知识管理组件
│   │   ├── common/          # 通用组件
│   │   └── ui/              # UI基础组件
│   ├── views/               # 页面组件
│   │   ├── modeling/        # 建模页面
│   │   ├── knowledge/       # 知识管理页面
│   │   └── dashboard/       # 仪表盘页面
│   ├── composables/         # 组合式函数
│   ├── stores/              # Pinia状态管理
│   ├── types/               # TypeScript类型定义
│   ├── utils/               # 工具函数
│   ├── api/                 # API接口
│   ├── assets/              # 静态资源
│   └── styles/              # 样式文件
├── public/                  # 公共资源
├── docs/                    # 文档
└── tests/                   # 测试文件
```

## 🎨 组件架构设计

### 1. 建模组件体系

#### 1.1 DiagramEditor - 核心图形编辑器

**文件路径**: `src/components/modeling/DiagramEditor.vue`

**功能描述**:
- 🎨 基于Konva.js的专业建模界面
- 🔧 支持拖拽创建UML/SysML元素
- ⚡ 实时图形渲染和交互
- 📐 智能网格对齐和布局

**组件结构**:
```vue
<template>
  <div class="diagram-editor" ref="editorContainer">
    <!-- 工具栏 -->
    <DiagramToolbar
      :tools="availableTools"
      :active-tool="activeTool"
      @tool-select="handleToolSelect"
    />
    
    <!-- Konva画布容器 -->
    <div class="canvas-container" ref="canvasContainer">
      <v-stage
        ref="stage"
        :config="stageConfig"
        @mousedown="handleStageClick"
        @mousemove="handleMouseMove"
      >
        <v-layer ref="backgroundLayer">
          <Grid :config="gridConfig" />
        </v-layer>
        
        <v-layer ref="elementsLayer">
          <DiagramElement
            v-for="element in elements"
            :key="element.id"
            :element="element"
            @select="handleElementSelect"
            @update="handleElementUpdate"
          />
        </v-layer>
        
        <v-layer ref="connectionsLayer">
          <DiagramConnection
            v-for="connection in connections"
            :key="connection.id"
            :connection="connection"
            @select="handleConnectionSelect"
          />
        </v-layer>
      </v-stage>
    </div>
    
    <!-- 右键菜单 -->
    <ContextMenu
      v-if="contextMenu.visible"
      :x="contextMenu.x"
      :y="contextMenu.y"
      :items="contextMenuItems"
      @action="handleContextAction"
    />
  </div>
</template>
```

**核心逻辑**:
```typescript
// src/components/modeling/DiagramEditor.vue
import { defineComponent, ref, computed, onMounted } from 'vue'
import { useDiagramStore } from '@/stores/diagram'
import { useModelingEngine } from '@/composables/useModelingEngine'

export default defineComponent({
  name: 'DiagramEditor',
  setup() {
    const diagramStore = useDiagramStore()
    const { createElement, updateElement, deleteElement } = useModelingEngine()
    
    // 响应式状态
    const stage = ref()
    const activeTool = ref('select')
    const selectedElements = ref<string[]>([])
    
    // 计算属性
    const elements = computed(() => diagramStore.elements)
    const connections = computed(() => diagramStore.connections)
    
    // 画布配置
    const stageConfig = computed(() => ({
      width: 1200,
      height: 800,
      draggable: activeTool.value === 'pan'
    }))
    
    // 事件处理
    const handleToolSelect = (tool: string) => {
      activeTool.value = tool
    }
    
    const handleStageClick = (e: any) => {
      if (activeTool.value === 'select') {
        // 处理选择逻辑
        handleSelection(e)
      } else if (activeTool.value.startsWith('create-')) {
        // 处理元素创建
        handleElementCreation(e)
      }
    }
    
    return {
      stage,
      activeTool,
      elements,
      connections,
      stageConfig,
      handleToolSelect,
      handleStageClick
    }
  }
})
```

#### 1.2 DiagramElement - 图形元素组件

**功能描述**:
- 🔷 渲染各种UML/SysML元素
- 🧬 支持生物医学专业化显示
- 🎯 响应式尺寸和位置
- 🖱️ 交互操作支持

**组件结构**:
```vue
<template>
  <v-group
    :config="groupConfig"
    @transformend="handleTransform"
    @dragend="handleDragEnd"
  >
    <!-- 基础形状 -->
    <v-rect
      v-if="element.type === 'class'"
      :config="rectConfig"
    />
    
    <!-- 生物医学特殊形状 -->
    <v-circle
      v-if="element.biological_type === 'protein'"
      :config="proteinConfig"
    />
    
    <!-- 文本标签 -->
    <v-text
      :config="textConfig"
      :text="displayText"
    />
    
    <!-- 生物医学注释 -->
    <v-text
      v-if="element.biological_type"
      :config="annotationConfig"
      :text="biologicalAnnotation"
    />
  </v-group>
</template>
```

#### 1.3 ElementPalette - 元素调色板

**功能描述**:
- 🎨 分类显示可用元素
- 🧬 生物医学元素专区
- 🔍 搜索和过滤功能
- 📱 响应式布局

**组件结构**:
```vue
<template>
  <div class="element-palette">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- UML标准元素 -->
      <el-tab-pane label="UML元素" name="uml">
        <ElementCategory
          v-for="category in umlCategories"
          :key="category.name"
          :category="category"
          @element-drag="handleElementDrag"
        />
      </el-tab-pane>
      
      <!-- SysML扩展元素 -->
      <el-tab-pane label="SysML元素" name="sysml">
        <ElementCategory
          v-for="category in sysmlCategories"
          :key="category.name"
          :category="category"
          @element-drag="handleElementDrag"
        />
      </el-tab-pane>
      
      <!-- 生物医学专业元素 -->
      <el-tab-pane label="生物医学" name="biomedical">
        <BiologicalElementGroup
          v-for="group in biologicalGroups"
          :key="group.name"
          :group="group"
          @element-drag="handleElementDrag"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
```

### 2. 知识管理组件

#### 2.1 KnowledgePanel - 知识侧面板

**功能描述**:
- 📚 显示相关文档和知识
- 🔗 文档与模型元素的智能关联
- 🏷️ 标签和分类管理
- 🔍 搜索和筛选功能

#### 2.2 DocumentBrowser - 文档浏览器

**功能描述**:
- 📖 多格式文档预览
- 📝 文档编辑和注释
- 🔗 与建模元素的双向链接
- 📊 文档统计和分析

## 🔧 状态管理架构

### Pinia Store设计

#### 1. DiagramStore - 图形状态管理

```typescript
// src/stores/diagram.ts
import { defineStore } from 'pinia'
import type { UMLElement, UMLConnection, DiagramState } from '@/types/modeling'

export const useDiagramStore = defineStore('diagram', {
  state: (): DiagramState => ({
    elements: new Map(),
    connections: new Map(),
    selectedElements: [],
    currentDiagram: null,
    diagramMetadata: {
      name: '',
      type: 'ClassDiagram',
      created: new Date(),
      modified: new Date()
    },
    history: [],
    historyIndex: -1
  }),
  
  getters: {
    elementsList: (state) => Array.from(state.elements.values()),
    connectionsList: (state) => Array.from(state.connections.values()),
    hasSelection: (state) => state.selectedElements.length > 0,
    canUndo: (state) => state.historyIndex > 0,
    canRedo: (state) => state.historyIndex < state.history.length - 1
  },
  
  actions: {
    // 元素操作
    addElement(element: UMLElement) {
      this.elements.set(element.id, element)
      this.addToHistory('add_element', { element })
    },
    
    updateElement(id: string, updates: Partial<UMLElement>) {
      const element = this.elements.get(id)
      if (element) {
        const updated = { ...element, ...updates }
        this.elements.set(id, updated)
        this.addToHistory('update_element', { id, updates })
      }
    },
    
    deleteElement(id: string) {
      const element = this.elements.get(id)
      if (element) {
        this.elements.delete(id)
        // 删除相关连接
        this.deleteElementConnections(id)
        this.addToHistory('delete_element', { element })
      }
    },
    
    // 历史操作
    addToHistory(action: string, data: any) {
      // 实现撤销/重做逻辑
      this.history.splice(this.historyIndex + 1)
      this.history.push({ action, data, timestamp: Date.now() })
      this.historyIndex = this.history.length - 1
    },
    
    undo() {
      if (this.canUndo) {
        // 实现撤销逻辑
        this.historyIndex--
      }
    },
    
    redo() {
      if (this.canRedo) {
        // 实现重做逻辑
        this.historyIndex++
      }
    }
  }
})
```

#### 2. ProjectStore - 项目状态管理

```typescript
// src/stores/project.ts
export const useProjectStore = defineStore('project', {
  state: () => ({
    currentProject: null as Project | null,
    projects: [] as Project[],
    isLoading: false,
    error: null as string | null
  }),
  
  actions: {
    async createProject(config: ProjectConfig) {
      this.isLoading = true
      try {
        const project = await projectAPI.create(config)
        this.projects.push(project)
        this.currentProject = project
        return project
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.isLoading = false
      }
    },
    
    async loadProject(id: string) {
      this.isLoading = true
      try {
        const project = await projectAPI.load(id)
        this.currentProject = project
        return project
      } catch (error) {
        this.error = error.message
        throw error
      } finally {
        this.isLoading = false
      }
    }
  }
})
```

## 🎯 组合式函数设计

### 1. useModelingEngine - 建模引擎接口

```typescript
// src/composables/useModelingEngine.ts
import { ref, computed } from 'vue'
import { enhancedModelingAPI } from '@/api/enhanced-modeling'

export function useModelingEngine() {
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // 项目管理
  const createEnhancedProject = async (config: ProjectConfig) => {
    isLoading.value = true
    try {
      const response = await enhancedModelingAPI.createProject(config)
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  // XML导入导出
  const importXMLModel = async (projectId: string, file: File) => {
    isLoading.value = true
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await enhancedModelingAPI.importXML(projectId, formData)
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const exportXMLModel = async (projectId: string, format: string = 'xmi') => {
    try {
      const response = await enhancedModelingAPI.exportXML(projectId, { 
        export_format: format 
      })
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    }
  }
  
  // 模型验证
  const validateModel = async (projectId: string) => {
    try {
      const response = await enhancedModelingAPI.validateModel(projectId)
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    }
  }
  
  return {
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),
    createEnhancedProject,
    importXMLModel,
    exportXMLModel,
    validateModel
  }
}
```

### 2. useDiagramEditor - 图形编辑器逻辑

```typescript
// src/composables/useDiagramEditor.ts
export function useDiagramEditor() {
  const stage = ref<Konva.Stage>()
  const selectedTool = ref('select')
  const isDrawing = ref(false)
  
  // 工具切换
  const selectTool = (tool: string) => {
    selectedTool.value = tool
    updateCursor()
  }
  
  // 元素创建
  const createElement = (type: string, position: { x: number, y: number }) => {
    const elementFactory = new ElementFactory()
    const element = elementFactory.create(type, position)
    
    // 添加到画布
    addElementToStage(element)
    
    return element
  }
  
  // 拖拽处理
  const handleDragStart = (e: Konva.KonvaEventObject<DragEvent>) => {
    const target = e.target
    target.moveToTop()
  }
  
  const handleDragEnd = (e: Konva.KonvaEventObject<DragEvent>) => {
    const target = e.target
    const element = findElementById(target.id())
    
    if (element) {
      // 更新元素位置
      updateElementPosition(element.id, {
        x: target.x(),
        y: target.y()
      })
    }
  }
  
  return {
    stage,
    selectedTool,
    selectTool,
    createElement,
    handleDragStart,
    handleDragEnd
  }
}
```

## 📡 API接口设计

### 1. Enhanced Modeling API

```typescript
// src/api/enhanced-modeling.ts
import { http } from '@/utils/http'

export const enhancedModelingAPI = {
  // 项目管理
  createProject: (config: ProjectConfig) =>
    http.post('/enhanced/projects', config),
  
  getProjectStatus: (projectId: string) =>
    http.get(`/enhanced/projects/${projectId}/status`),
  
  // XML导入导出
  importXML: (projectId: string, formData: FormData) =>
    http.post(`/enhanced/projects/${projectId}/import/xml`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
  
  exportXML: (projectId: string, options: ExportOptions) =>
    http.post(`/enhanced/projects/${projectId}/export/xml`, options),
  
  // 模型验证和转换
  validateModel: (projectId: string, options?: ValidationOptions) =>
    http.post(`/enhanced/projects/${projectId}/validate`, options),
  
  transformModel: (projectId: string, transformation: TransformationRequest) =>
    http.post(`/enhanced/projects/${projectId}/transform`, transformation),
  
  // 生物医学增强
  enhanceBiomedical: (projectId: string, options?: EnhancementOptions) =>
    http.post(`/enhanced/projects/${projectId}/biomedical/enhance`, options),
  
  // AI建议
  getSuggestions: (projectId: string) =>
    http.get(`/enhanced/projects/${projectId}/suggestions`),
  
  // 系统能力
  getCapabilities: () =>
    http.get('/enhanced/capabilities')
}
```

## 🎨 样式设计规范

### 1. 主题配置

```scss
// src/styles/themes/biomedical.scss
:root {
  // 主色调 - 生物医学蓝
  --primary-color: #1976d2;
  --primary-light: #42a5f5;
  --primary-dark: #1565c0;
  
  // 生物医学专用色彩
  --protein-color: #e3f2fd;
  --gene-color: #f3e5f5;
  --pathway-color: #e8f5e8;
  --cell-color: #fff3e0;
  
  // 图形元素颜色
  --element-border: #424242;
  --element-selected: #ff9800;
  --connection-color: #666666;
  
  // 界面色彩
  --background-color: #fafafa;
  --surface-color: #ffffff;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --success-color: #4caf50;
}
```

### 2. 组件样式

```scss
// src/styles/components/diagram-editor.scss
.diagram-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .canvas-container {
    flex: 1;
    position: relative;
    background: var(--background-color);
    overflow: hidden;
    
    .konvajs-content {
      border: 1px solid #e0e0e0;
    }
  }
  
  .toolbar {
    height: 48px;
    background: var(--surface-color);
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    padding: 0 16px;
    gap: 8px;
  }
}

// 生物医学元素样式
.biological-element {
  &.protein {
    fill: var(--protein-color);
    stroke: var(--primary-color);
  }
  
  &.gene {
    fill: var(--gene-color);
    stroke: #9c27b0;
  }
  
  &.pathway {
    fill: var(--pathway-color);
    stroke: #4caf50;
  }
}
```

## 🧪 测试架构

### 1. 单元测试

```typescript
// tests/unit/components/DiagramEditor.spec.ts
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import DiagramEditor from '@/components/modeling/DiagramEditor.vue'

describe('DiagramEditor', () => {
  let wrapper: any
  
  beforeEach(() => {
    const pinia = createPinia()
    wrapper = mount(DiagramEditor, {
      global: {
        plugins: [pinia]
      }
    })
  })
  
  it('应该正确渲染图形编辑器', () => {
    expect(wrapper.find('.diagram-editor').exists()).toBe(true)
    expect(wrapper.find('.canvas-container').exists()).toBe(true)
  })
  
  it('应该响应工具选择', async () => {
    await wrapper.vm.selectTool('create-class')
    expect(wrapper.vm.selectedTool).toBe('create-class')
  })
})
```

### 2. 集成测试

```typescript
// tests/integration/modeling-workflow.spec.ts
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { useModelingEngine } from '@/composables/useModelingEngine'

describe('建模工作流集成测试', () => {
  it('应该完成完整的建模流程', async () => {
    const { createEnhancedProject, importXMLModel } = useModelingEngine()
    
    // 1. 创建项目
    const project = await createEnhancedProject({
      name: '测试项目',
      domain: 'molecular_biology'
    })
    
    // 2. 导入XML模型
    const file = new File(['<?xml version="1.0"?>'], 'test.xmi')
    const importResult = await importXMLModel(project.project_id, file)
    
    expect(importResult.success).toBe(true)
  })
})
```

## 🚀 性能优化策略

### 1. 图形渲染优化

```typescript
// src/utils/performance/canvas-optimizer.ts
export class CanvasOptimizer {
  private stage: Konva.Stage
  private viewportElements = new Set<string>()
  
  constructor(stage: Konva.Stage) {
    this.stage = stage
  }
  
  // 视口剔除优化
  optimizeViewport() {
    const visibleRect = this.getVisibleRect()
    
    this.stage.find('Group').forEach(group => {
      const bounds = group.getClientRect()
      const isVisible = this.intersects(visibleRect, bounds)
      
      if (isVisible && !this.viewportElements.has(group.id())) {
        group.show()
        this.viewportElements.add(group.id())
      } else if (!isVisible && this.viewportElements.has(group.id())) {
        group.hide()
        this.viewportElements.delete(group.id())
      }
    })
  }
  
  // 批量更新优化
  batchUpdate(updates: Array<() => void>) {
    this.stage.batchDraw(() => {
      updates.forEach(update => update())
    })
  }
}
```

### 2. 内存管理

```typescript
// src/utils/performance/memory-manager.ts
export class MemoryManager {
  private elementPool = new Map<string, any[]>()
  
  // 对象池模式
  getElement(type: string) {
    const pool = this.elementPool.get(type) || []
    return pool.pop() || this.createElement(type)
  }
  
  releaseElement(type: string, element: any) {
    const pool = this.elementPool.get(type) || []
    pool.push(element)
    this.elementPool.set(type, pool)
  }
  
  // 清理未使用的对象
  cleanup() {
    for (const [type, pool] of this.elementPool) {
      if (pool.length > 10) {
        this.elementPool.set(type, pool.slice(0, 10))
      }
    }
  }
}
```

## 📱 响应式设计

### 1. 断点配置

```typescript
// src/utils/responsive/breakpoints.ts
export const breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1400
}

export function useBreakpoint() {
  const width = ref(window.innerWidth)
  
  const currentBreakpoint = computed(() => {
    if (width.value >= breakpoints.xxl) return 'xxl'
    if (width.value >= breakpoints.xl) return 'xl'
    if (width.value >= breakpoints.lg) return 'lg'
    if (width.value >= breakpoints.md) return 'md'
    if (width.value >= breakpoints.sm) return 'sm'
    return 'xs'
  })
  
  return { currentBreakpoint }
}
```

## 🔧 构建配置

### 1. Vite配置优化

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@utils': resolve(__dirname, 'src/utils')
    }
  },
  
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['vue', 'vue-router', 'pinia'],
          'graphics': ['konva', 'd3', 'vis-network'],
          'ui': ['element-plus', '@element-plus/icons-vue']
        }
      }
    },
    
    // 代码分割优化
    chunkSizeWarningLimit: 1000
  },
  
  // 开发服务器配置
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

---

**总结**: 前端架构采用现代化的Vue 3生态系统，结合专业的图形渲染引擎，为生物医学建模提供了强大而灵活的用户界面。通过合理的组件设计、状态管理和性能优化，确保系统的可维护性和用户体验。 