# 生物医学MBSE建模平台 - 环境配置示例

# 应用配置
APP_NAME=生物医学MBSE建模平台
APP_VERSION=1.0.0
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=true

# XML元数据系统路径
XML_METADATA_SYSTEM_PATH=D:/xmltest/xml_metadata_system

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/biomedical_mbse
DATABASE_ECHO=false

# Redis缓存配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# InfluxDB时序数据库
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your_influxdb_token
INFLUXDB_ORG=biomedical_org
INFLUXDB_BUCKET=biomedical_metrics

# Neo4j图数据库
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# JWT认证
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 文件存储
UPLOAD_PATH=./uploads
MAX_UPLOAD_SIZE=100MB

# 生物医学工具路径（可选）
PYMOL_PATH=
SPSS_PATH=
ORIGIN_PATH=

# API配置
API_V1_STR=/api/v1
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# 监控配置
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# 开发配置
RELOAD=true
WORKERS=1 