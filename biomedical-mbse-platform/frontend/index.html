<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="生物医学MBSE建模平台 - 基于XML元数据系统v3.0构建的专业生物医学建模工具" />
    <meta name="keywords" content="生物医学,MBSE,建模,XML元数据,系统工程,分子建模" />
    <title>生物医学MBSE建模平台</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 引入字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
      /* 加载动画样式 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: 'Inter', sans-serif;
      }
      
      .loading-content {
        text-align: center;
      }
      
      .loading-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
      }
      
      /* 隐藏加载动画 */
      .loading-hidden {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.5s ease;
      }
    </style>
  </head>
  <body>
    <!-- 加载动画 -->
    <div id="loading">
      <div class="loading-content">
        <div class="loading-icon"></div>
        <div class="loading-text">生物医学MBSE建模平台</div>
        <div class="loading-subtitle">正在启动AI引擎...</div>
      </div>
    </div>
    
    <!-- Vue应用挂载点 -->
    <div id="app"></div>
    
    <!-- 启动脚本 -->
    <script type="module" src="/src/main.js"></script>
    
    <script>
      // 页面加载完成后隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('loading-hidden');
            setTimeout(() => {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000); // 延迟1秒显示加载效果
      });
      
      // 错误处理
      window.addEventListener('error', function(event) {
        console.error('应用错误:', event.error);
      });
      
      // Vue应用错误处理
      window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
      });
    </script>
  </body>
</html> 