<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 顶部导航栏 -->
      <el-header class="app-header">
        <div class="header-left">
          <h1 class="app-title">
            <el-icon><DataAnalysis /></el-icon>
            生物医学MBSE建模平台
          </h1>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="$router.push('/workspace')">
            <el-icon><House /></el-icon>
            工作区
          </el-button>
          <el-button @click="$router.push('/visualization-demo')">
            <el-icon><View /></el-icon>
            可视化演示
          </el-button>
          <el-button @click="$router.push('/projects')">
            <el-icon><Document /></el-icon>
            项目管理
          </el-button>
          <el-button @click="$router.push('/biomedical')">
            <el-icon><Monitor /></el-icon>
            生物医学
          </el-button>
          <el-button @click="$router.push('/tools')">
            <el-icon><Tools /></el-icon>
            工具管理
          </el-button>
        </div>
      </el-header>

      <!-- 主内容区域 -->
      <el-main class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>

      <!-- 底部状态栏 -->
      <el-footer class="app-footer" height="40px">
        <div class="footer-content">
          <span>生物医学MBSE建模平台 v1.0.0</span>
          <span>基于专业可视化组件库构建</span>
          <div class="status-indicators">
            <el-tag size="small" type="success">系统正常</el-tag>
            <el-tag size="small" type="info">可视化引擎已连接</el-tag>
          </div>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { House, Tools, Document, DataAnalysis, View, Monitor } from '@element-plus/icons-vue'

const router = useRouter()

onMounted(() => {
  // 应用初始化逻辑
  console.log('生物医学MBSE建模平台已启动')
  
  // 默认跳转到工作区
  if (router.currentRoute.value.path === '/') {
    router.push('/workspace')
  }
})
</script>

<style scoped>
.app-container {
  height: 100vh;
  width: 100%;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.app-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.app-main {
  padding: 20px;
  background-color: #f5f7fa;
  overflow-y: auto;
}

.app-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 12px;
  color: #666;
}

.status-indicators {
  display: flex;
  gap: 8px;
}

/* 路由动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 全局样式重置 */
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
</style> 