import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token等
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// 生物医学API服务
export const biomedicalApi = {
  // 项目管理
  async getProjects() {
    return api.get('/biomedical/projects')
  },

  async createProject(projectData) {
    return api.post('/biomedical/projects', projectData)
  },

  async getProject(projectId) {
    return api.get(`/biomedical/projects/${projectId}`)
  },

  async updateProject(projectId, projectData) {
    return api.put(`/biomedical/projects/${projectId}`, projectData)
  },

  async deleteProject(projectId) {
    return api.delete(`/biomedical/projects/${projectId}`)
  },

  // AI推荐
  async getAIRecommendations(context = {}) {
    return api.post('/ai/recommendations', context)
  },

  async getBusinessInsights(timeWindow = 7) {
    return api.get(`/ai/insights?time_window=${timeWindow}`)
  },

  // 工具管理
  async getTools() {
    return api.get('/biomedical/tools')
  },

  async getToolStatus(toolId) {
    return api.get(`/biomedical/tools/${toolId}/status`)
  },

  async executeWorkflow(workflowData) {
    return api.post('/biomedical/workflows/execute', workflowData)
  },

  // 系统健康
  async getSystemHealth() {
    return api.get('/system/health')
  },

  async getSystemStats() {
    return api.get('/system/stats')
  },

  // 分子数据
  async getMolecularData(query) {
    return api.get('/biomedical/molecular', { params: query })
  },

  async analyzeMolecule(moleculeData) {
    return api.post('/biomedical/molecular/analyze', moleculeData)
  },

  // 数据标准
  async getDataStandards() {
    return api.get('/biomedical/standards')
  },

  async validateData(data, standard) {
    return api.post('/biomedical/standards/validate', { data, standard })
  }
}

export default api 