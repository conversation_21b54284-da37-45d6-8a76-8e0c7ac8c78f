import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import type { XMLElement, ParsingResult } from '../types/parsing'

// API基础配置
const API_BASE_URL = (typeof process !== 'undefined' && process.env && process.env.VITE_API_BASE_URL) ? process.env.VITE_API_BASE_URL : 'http://localhost:8000'
const API_TIMEOUT = 30000
const API_VERSION = 'v3'

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/${API_VERSION}`,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证token (如果需要)
    const token = localStorage.getItem('auth_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求ID用于追踪
    const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2)
    if (config.headers) {
      config.headers['X-Request-ID'] = requestId
    }
    
    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, config.data)
    return config
  },
  (error) => {
    console.error('[API Request Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`[API Response] ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data)
    return response
  },
  (error) => {
    console.error('[API Response Error]', error.response?.data || error.message)
    
    // 统一错误处理
    if (error.response?.status === 401) {
      // Token过期，重定向到登录页
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    } else if (error.response?.status === 404) {
      console.warn('API endpoint not found:', error.config?.url)
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error.response?.data)
    }
    
    return Promise.reject(error)
  }
)

// API响应通用接口
export interface APIResponse<T = any> {
  success: boolean
  data: T
  message?: string
  error?: string
  requestId?: string
  timestamp?: string
}

// 通用API请求方法
export class APIClient {
  static async get<T>(url: string, config?: any): Promise<APIResponse<T>> {
    const response = await apiClient.get(url, config)
    return response.data
  }

  static async post<T>(url: string, data?: any, config?: any): Promise<APIResponse<T>> {
    const response = await apiClient.post(url, data, config)
    return response.data
  }

  static async put<T>(url: string, data?: any, config?: any): Promise<APIResponse<T>> {
    const response = await apiClient.put(url, data, config)
    return response.data
  }

  static async delete<T>(url: string, config?: any): Promise<APIResponse<T>> {
    const response = await apiClient.delete(url, config)
    return response.data
  }

  static async patch<T>(url: string, data?: any, config?: any): Promise<APIResponse<T>> {
    const response = await apiClient.patch(url, data, config)
    return response.data
  }
}

export default apiClient 