import { APIClient, APIResponse } from '../index'

// Core模块相关接口定义
export interface XMLElement {
  id: string
  name: string
  type: string
  attributes: Record<string, any>
  children?: XMLElement[]
  content?: string
}

export interface ParseResult {
  elements: XMLElement[]
  metadata: {
    totalElements: number
    processingTime: number
    elementsPerSecond: number
    version: string
  }
  structure: {
    depth: number
    breadth: number
    complexity: number
  }
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  schema?: string
  validatedAt: string
}

export interface ValidationError {
  line: number
  column: number
  message: string
  severity: 'error' | 'warning'
  code: string
}

export interface ValidationWarning {
  line: number
  column: number
  message: string
  suggestion?: string
}

export interface TerminologyResult {
  terms: TerminologyTerm[]
  statistics: {
    totalTerms: number
    standardTerms: number
    customTerms: number
    coverage: number
  }
  recommendations: string[]
}

export interface TerminologyTerm {
  term: string
  definition: string
  category: string
  isStandard: boolean
  frequency: number
  context: string[]
}

/**
 * Core模块API接口类
 * 基于后端Core模块的强大处理能力：
 * - 7,699元素/秒处理速度
 * - 统一异步接口
 * - 900+标准术语支持
 */
export class CoreAPI {
  /**
   * 解析XML内容
   * @param content XML内容字符串
   * @param options 解析选项
   */
  static async parseXML(
    content: string, 
    options?: {
      validateSchema?: boolean
      includeMetadata?: boolean
      maxDepth?: number
    }
  ): Promise<APIResponse<ParseResult>> {
    return APIClient.post('/core/parse', {
      content,
      options: {
        validateSchema: true,
        includeMetadata: true,
        maxDepth: 10,
        ...options
      }
    })
  }

  /**
   * 验证XML语法和语义
   * @param content XML内容
   * @param schemaUrl 可选的schema URL
   */
  static async validateXML(
    content: string,
    schemaUrl?: string
  ): Promise<APIResponse<ValidationResult>> {
    return APIClient.post('/core/validate', {
      content,
      schemaUrl
    })
  }

  /**
   * 生成唯一标识符
   * @param element XML元素
   * @param prefix ID前缀
   */
  static async generateID(
    element: Partial<XMLElement>,
    prefix?: string
  ): Promise<APIResponse<{ id: string }>> {
    return APIClient.post('/core/generate-id', {
      element,
      prefix
    })
  }

  /**
   * 术语分析
   * @param content 文本内容
   * @param domain 领域范围
   */
  static async analyzeTerminology(
    content: string,
    domain?: string
  ): Promise<APIResponse<TerminologyResult>> {
    return APIClient.post('/core/terminology', {
      content,
      domain
    })
  }

  /**
   * 批量解析多个XML文件
   * @param files 文件列表
   */
  static async batchParse(
    files: File[]
  ): Promise<APIResponse<ParseResult[]>> {
    const formData = new FormData()
    files.forEach((file, index) => {
      formData.append(`file_${index}`, file)
    })

    return APIClient.post('/core/batch-parse', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取解析进度
   * @param taskId 任务ID
   */
  static async getParseProgress(
    taskId: string
  ): Promise<APIResponse<{
    progress: number
    status: 'pending' | 'processing' | 'completed' | 'failed'
    elementsProcessed: number
    totalElements: number
    estimatedTimeRemaining?: number
  }>> {
    return APIClient.get(`/core/parse-progress/${taskId}`)
  }

  /**
   * 取消解析任务
   * @param taskId 任务ID
   */
  static async cancelParse(taskId: string): Promise<APIResponse<{ cancelled: boolean }>> {
    return APIClient.delete(`/core/parse-task/${taskId}`)
  }
}

// 导出默认实例
export default CoreAPI 