import { APIClient, APIResponse } from '../index'

// 需求相关接口定义
export interface Requirement {
  id: string
  title: string
  description: string
  type: 'functional' | 'non-functional' | 'business' | 'technical'
  priority: 'low' | 'medium' | 'high' | 'critical'
  status: 'draft' | 'reviewed' | 'approved' | 'implemented' | 'tested' | 'verified'
  source: string
  tags: string[]
  acceptanceCriteria: AcceptanceCriterion[]
  dependencies: string[]
  stakeholders: string[]
  estimatedEffort?: number
  businessValue?: number
  riskLevel?: 'low' | 'medium' | 'high'
  createdAt: string
  updatedAt: string
  createdBy: string
  assignedTo?: string
}

export interface AcceptanceCriterion {
  id: string
  description: string
  completed: boolean
  testScenarios?: string[]
}

export interface TraceLink {
  id: string
  sourceId: string
  targetId: string
  type: 'derivedFrom' | 'tracesTo' | 'satisfies' | 'verifies' | 'refines' | 'dependsOn' | 'conflicts'
  strength: 'strong' | 'medium' | 'weak'
  verified: boolean
  createdAt: string
}

export interface RequirementMetadata {
  xmlElementId?: string
  stakeholders: string[]
  riskLevel: 'low' | 'medium' | 'high'
  complexity: number
  estimatedEffort: number
  tags: string[]
}

export interface RequirementScenario {
  id: string
  name: string
  description: string
  preconditions: string[]
  steps: ScenarioStep[]
  postconditions: string[]
  requirementIds: string[]
  verificationStatus: 'pending' | 'in_progress' | 'completed' | 'failed'
}

export interface ScenarioStep {
  step: number
  action: string
  expectedResult: string
  actualResult?: string
  status?: 'passed' | 'failed' | 'pending'
}

export interface TraceabilityMatrix {
  requirements: Requirement[]
  traces: TraceLink[]
  coverage: {
    upstreamCoverage: number
    downstreamCoverage: number
    verificationCoverage: number
    orphanRequirements: string[]
    danglingReferences: string[]
  }
}

export interface RequirementAnalysis {
  quality: {
    completeness: number
    consistency: number
    clarity: number
    testability: number
  }
  risks: RequirementRisk[]
  recommendations: string[]
  duplicates: RequirementDuplicate[]
  gaps: RequirementGap[]
}

export interface RequirementRisk {
  id: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  likelihood: number
  impact: number
  mitigation: string
}

export interface RequirementDuplicate {
  requirementIds: string[]
  similarity: number
  conflictType: 'exact' | 'semantic' | 'contradictory'
}

export interface RequirementGap {
  area: string
  description: string
  severity: 'low' | 'medium' | 'high'
  suggestedRequirements: string[]
}

export interface RequirementFilter {
  type?: string
  priority?: string
  status?: string
  assignedTo?: string
  tags?: string[]
  search?: string
}

/**
 * 需求分析师API接口类
 * 支持需求全生命周期管理和智能分析
 */
export class RequirementsAPI {
  // ============ 需求CRUD操作 ============
  
  /**
   * 创建新需求
   */
  static async createRequirement(requirement: Omit<Requirement, 'id' | 'createdAt' | 'updatedAt'>): Promise<APIResponse<Requirement>> {
    return APIClient.post('/requirements', requirement)
  }

  /**
   * 获取需求列表
   */
  static async getRequirements(filter?: RequirementFilter): Promise<APIResponse<Requirement[]>> {
    return APIClient.get('/requirements', { params: filter })
  }

  /**
   * 获取单个需求详情
   */
  static async getRequirement(id: string): Promise<APIResponse<Requirement>> {
    return APIClient.get(`/requirements/${id}`)
  }

  /**
   * 更新需求
   */
  static async updateRequirement(id: string, requirement: Partial<Requirement>): Promise<APIResponse<Requirement>> {
    return APIClient.put(`/requirements/${id}`, requirement)
  }

  /**
   * 删除需求
   */
  static async deleteRequirement(id: string): Promise<APIResponse<{ deleted: boolean }>> {
    return APIClient.delete(`/requirements/${id}`)
  }

  // ============ 需求导入和识别 ============

  /**
   * 从XML元数据中识别需求
   */
  static async extractRequirementsFromXML(
    xmlContent: string,
    options?: {
      useMLClassification?: boolean
      confidenceThreshold?: number
      includeSemanticAnalysis?: boolean
    }
  ): Promise<APIResponse<{
    requirements: Requirement[]
    confidence: number
    suggestions: string[]
  }>> {
    return APIClient.post('/requirements/extract-from-xml', {
      xmlContent,
      options: {
        useMLClassification: true,
        confidenceThreshold: 0.8,
        includeSemanticAnalysis: true,
        ...options
      }
    })
  }

  /**
   * 智能需求分类
   */
  static async classifyRequirements(requirements: string[]): Promise<APIResponse<{
    classifications: Array<{
      text: string
      type: string
      confidence: number
    }>
  }>> {
    return APIClient.post('/requirements/classify', { requirements })
  }

  // ============ 追溯分析 ============

  /**
   * 构建追溯关系
   */
  static async createTraceLink(trace: Omit<TraceLink, 'id' | 'createdAt'>): Promise<APIResponse<TraceLink>> {
    return APIClient.post('/requirements/traces', trace)
  }

  /**
   * 获取需求的追溯网络
   */
  static async getTraceability(
    requirementId: string,
    options?: {
      depth?: number
      direction?: 'upstream' | 'downstream' | 'both'
      includeMetrics?: boolean
    }
  ): Promise<APIResponse<TraceabilityMatrix>> {
    return APIClient.get(`/requirements/${requirementId}/traceability`, { 
      params: {
        depth: 3,
        direction: 'both',
        includeMetrics: true,
        ...options
      }
    })
  }

  /**
   * 验证追溯完整性
   */
  static async validateTraceability(projectId?: string): Promise<APIResponse<{
    completeness: number
    issues: Array<{
      type: 'orphan' | 'dangling' | 'circular' | 'inconsistent'
      requirementId: string
      description: string
      severity: 'low' | 'medium' | 'high'
    }>
    recommendations: string[]
  }>> {
    return APIClient.post('/requirements/validate-traceability', { projectId })
  }

  // ============ 场景管理 ============

  /**
   * 创建需求验证场景
   */
  static async createScenario(scenario: Omit<RequirementScenario, 'id'>): Promise<APIResponse<RequirementScenario>> {
    return APIClient.post('/requirements/scenarios', scenario)
  }

  /**
   * 获取场景列表
   */
  static async getScenarios(requirementId?: string): Promise<APIResponse<RequirementScenario[]>> {
    return APIClient.get('/requirements/scenarios', { 
      params: requirementId ? { requirementId } : {} 
    })
  }

  /**
   * 执行场景验证
   */
  static async executeScenario(
    scenarioId: string,
    testData?: any
  ): Promise<APIResponse<{
    result: 'passed' | 'failed' | 'inconclusive'
    details: ScenarioStep[]
    coverage: number
    issues: string[]
  }>> {
    return APIClient.post(`/requirements/scenarios/${scenarioId}/execute`, { testData })
  }

  // ============ 需求分析 ============

  /**
   * 分析需求质量
   */
  static async analyzeRequirements(requirementIds: string[]): Promise<APIResponse<RequirementAnalysis>> {
    return APIClient.post('/requirements/analyze', { requirementIds })
  }

  /**
   * 生成覆盖分析报告
   */
  static async generateCoverageReport(projectId?: string): Promise<APIResponse<{
    requirementCoverage: number
    testCoverage: number
    verificationCoverage: number
    gaps: RequirementGap[]
    recommendations: string[]
  }>> {
    return APIClient.post('/requirements/coverage-report', { projectId })
  }

  // ============ 批量操作 ============

  /**
   * 批量更新需求状态
   */
  static async batchUpdateStatus(
    requirementIds: string[],
    status: Requirement['status']
  ): Promise<APIResponse<{ updated: number }>> {
    return APIClient.patch('/requirements/batch-status', {
      requirementIds,
      status
    })
  }

  /**
   * 导出需求数据
   */
  static async exportRequirements(
    format: 'json' | 'csv' | 'excel' | 'pdf',
    filters?: any
  ): Promise<APIResponse<{ downloadUrl: string }>> {
    return APIClient.post('/requirements/export', {
      format,
      filters
    })
  }
}

export default RequirementsAPI 