<template>
  <div class="biomedical-dashboard">
    <!-- 顶部工具栏 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-title">
          <h1>生物医学MBSE建模平台</h1>
          <p>基于XML元数据系统v3.0构建的专业生物医学建模工具平台</p>
        </div>
        <div class="header-status">
          <div class="status-item">
            <span class="status-dot success"></span>
            <span>系统正常运行</span>
          </div>
          <div class="status-item">
            <span class="status-dot info"></span>
            <span>AI引擎已连接</span>
          </div>
        </div>
      </div>
    </div>

    <div class="dashboard-body">
      <!-- 左侧导航 -->
      <div class="sidebar">
        <div class="workspace-info">
          <h3>{{ dashboardTitle }}</h3>
          <p>当前工作区：<span class="workspace-name">{{ workspaceLabels[activeMenu] }}</span></p>
        </div>
        
        <nav class="sidebar-nav">
          <ul class="nav-list">
            <li class="nav-item" :class="{ active: activeMenu === 'molecular' }" @click="handleMenuSelect('molecular')">
              <span class="nav-icon">🧬</span>
              <span>分子建模</span>
            </li>
            <li class="nav-item" :class="{ active: activeMenu === 'pathway' }" @click="handleMenuSelect('pathway')">
              <span class="nav-icon">🔗</span>
              <span>通路分析</span>
            </li>
            <li class="nav-item" :class="{ active: activeMenu === 'statistics' }" @click="handleMenuSelect('statistics')">
              <span class="nav-icon">📊</span>
              <span>统计分析</span>
            </li>
            <li class="nav-item" :class="{ active: activeMenu === 'tools' }" @click="handleMenuSelect('tools')">
              <span class="nav-icon">🔧</span>
              <span>工具管理</span>
            </li>
          </ul>
        </nav>
        
        <div class="quick-actions">
          <h4>快速操作</h4>
          <button class="action-btn primary" @click="createNewProject">新建项目</button>
          <button class="action-btn secondary" @click="importData">导入数据</button>
          <button class="action-btn tertiary" @click="viewHistory">查看历史</button>
        </div>
      </div>

      <!-- 主工作区 -->
      <div class="main-content">
        <!-- 统计概览 -->
        <div class="stats-overview">
          <div class="stat-card">
            <div class="stat-number">42</div>
            <div class="stat-label">分子模型</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">28</div>
            <div class="stat-label">通路图谱</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">156</div>
            <div class="stat-label">分析结果</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">15</div>
            <div class="stat-label">可用工具</div>
          </div>
        </div>

        <!-- 工具链管理 -->
        <div class="section">
          <div class="section-header">
            <h2>工具链管理</h2>
            <div class="header-actions">
              <button class="btn-link" @click="showMolecularViewer">分子可视化</button>
              <button class="btn-link" @click="showToolChain">工具链管理</button>
              <button class="btn-link" @click="showDataStandards">数据标准</button>
            </div>
          </div>
          
          <div class="tool-grid">
            <div class="tool-card" @click="selectModule('molecular')">
              <div class="tool-header">
                <span class="tool-icon">🧬</span>
                <h3>分子建模</h3>
              </div>
              <p>进行分子结构建模和分析，支持3D可视化和结构优化</p>
              <div class="tool-stats">
                <span>活跃项目: 8</span>
                <span>模型数量: 42</span>
              </div>
            </div>

            <div class="tool-card" @click="selectModule('pathway')">
              <div class="tool-header">
                <span class="tool-icon">🔗</span>
                <h3>通路分析</h3>
              </div>
              <p>生物通路和信号传导分析，构建网络图谱和路径预测</p>
              <div class="tool-stats">
                <span>通路数据: 28</span>
                <span>网络节点: 1,245</span>
              </div>
            </div>

            <div class="tool-card" @click="selectModule('statistics')">
              <div class="tool-header">
                <span class="tool-icon">📊</span>
                <h3>统计分析</h3>
              </div>
              <p>生物医学数据统计分析，支持多种算法和可视化展示</p>
              <div class="tool-stats">
                <span>数据集: 156</span>
                <span>分析任务: 89</span>
              </div>
            </div>

            <div class="tool-card" @click="selectModule('tools')">
              <div class="tool-header">
                <span class="tool-icon">🔧</span>
                <h3>工具管理</h3>
              </div>
              <p>管理和配置生物医学建模工具，支持工具链编排和调度</p>
              <div class="tool-stats">
                <span>可用工具: 15</span>
                <span>工具链: 6</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="section">
          <h2>最近活动</h2>
          <div class="activity-feed">
            <div class="activity-item">
              <div class="activity-indicator success"></div>
              <div class="activity-content">
                <div class="activity-title">分子建模任务完成</div>
                <div class="activity-description">蛋白质结构优化 - protein_model_v3.pdb</div>
                <div class="activity-time">2分钟前</div>
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-indicator warning"></div>
              <div class="activity-content">
                <div class="activity-title">通路分析进行中</div>
                <div class="activity-description">信号转导网络构建 - pathway_analysis_job_42</div>
                <div class="activity-time">正在进行</div>
              </div>
            </div>
            <div class="activity-item">
              <div class="activity-indicator info"></div>
              <div class="activity-content">
                <div class="activity-title">数据导入完成</div>
                <div class="activity-description">基因表达数据集 - RNA_seq_data_2024.csv</div>
                <div class="activity-time">1小时前</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const activeMenu = ref('molecular')

// 工作区标签映射
const workspaceLabels = {
  molecular: 'molecular',
  pathway: 'pathway',
  statistics: 'statistics',
  tools: 'tools'
}

// 计算属性
const dashboardTitle = computed(() => {
  const titles = {
    molecular: '分子建模工作区',
    pathway: '通路分析工作区', 
    statistics: '统计分析工作区',
    tools: '工具管理工作区'
  }
  return titles[activeMenu.value] || '生物医学MBSE平台'
})

// 方法
const handleMenuSelect = (index) => {
  activeMenu.value = index
  ElMessage.success(`切换到${dashboardTitle.value}`)
}

const selectModule = (module) => {
  activeMenu.value = module
  ElMessage.info(`进入${workspaceLabels[module]}模块`)
}

const createNewProject = () => {
  ElMessage.success('创建新项目')
}

const importData = () => {
  ElMessage.info('导入数据功能')
}

const viewHistory = () => {
  ElMessage.info('查看历史记录')
}

const showMolecularViewer = () => {
  ElMessage.info('分子可视化功能')
}

const showToolChain = () => {
  ElMessage.info('工具链管理功能')
}

const showDataStandards = () => {
  ElMessage.info('数据标准功能')
}
</script>

<style scoped>
/* 基础样式 */
.biomedical-dashboard {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 顶部工具栏 */
.dashboard-header {
  background: white;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid #e8e8e8;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-title h1 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.header-title p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.header-status {
  display: flex;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.success {
  background-color: #10b981;
}

.status-dot.info {
  background-color: #3b82f6;
}

/* 主体布局 */
.dashboard-body {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 100px);
}

/* 侧边栏 */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e5e7eb;
  padding: 24px;
}

.workspace-info {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.workspace-info h3 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.workspace-info p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.workspace-name {
  font-weight: 500;
  color: #4f46e5;
}

/* 导航菜单 */
.sidebar-nav {
  margin-bottom: 32px;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
}

.nav-item:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

.nav-item.active {
  background-color: #4f46e5;
  color: white;
}

.nav-icon {
  margin-right: 12px;
  font-size: 18px;
}

/* 快速操作 */
.quick-actions {
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
}

.quick-actions h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.action-btn {
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 8px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.action-btn.primary {
  background-color: #4f46e5;
  color: white;
}

.action-btn.primary:hover {
  background-color: #4338ca;
}

.action-btn.secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.action-btn.secondary:hover {
  background-color: #e5e7eb;
}

.action-btn.tertiary {
  background-color: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.action-btn.tertiary:hover {
  background-color: #f9fafb;
  color: #374151;
}

/* 主内容区 */
.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 统计概览 */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

/* 节区样式 */
.section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.btn-link {
  background: none;
  border: none;
  padding: 6px 12px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-link:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* 工具网格 */
.tool-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.tool-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-card:hover {
  border-color: #4f46e5;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.1);
}

.tool-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.tool-icon {
  font-size: 24px;
  margin-right: 12px;
}

.tool-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.tool-card p {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 16px;
  font-size: 14px;
}

.tool-stats {
  display: flex;
  gap: 16px;
  color: #6b7280;
  font-size: 12px;
}

/* 活动流 */
.activity-feed {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.activity-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-top: 6px;
  flex-shrink: 0;
}

.activity-indicator.success {
  background-color: #10b981;
}

.activity-indicator.warning {
  background-color: #f59e0b;
}

.activity-indicator.info {
  background-color: #3b82f6;
}

.activity-content {
  flex: 1;
}

.activity-title {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.activity-description {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.activity-time {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .tool-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-body {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .header-status {
    justify-content: center;
  }
}
</style>