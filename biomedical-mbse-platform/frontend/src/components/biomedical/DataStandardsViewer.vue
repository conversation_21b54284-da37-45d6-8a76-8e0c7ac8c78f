<!-- 数据标准查看器组件 -->
<template>
  <div class="data-standards-viewer">
    <div class="page-header">
      <h1>数据标准管理</h1>
      <p>查看和管理生物医学数据标准，确保数据质量和互操作性</p>
    </div>

    <div class="content-area">
      <!-- 数据标准概览 -->
      <el-row :gutter="20" class="overview-cards">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-number">{{ supportedStandards.length }}</div>
              <div class="card-label">支持的标准</div>
            </div>
            <el-icon class="card-icon"><DocumentChecked /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-number">{{ validatedDatasets }}</div>
              <div class="card-label">已验证数据集</div>
            </div>
            <el-icon class="card-icon"><DataAnalysis /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-number">{{ (complianceRate * 100).toFixed(1) }}%</div>
              <div class="card-label">合规率</div>
            </div>
            <el-icon class="card-icon"><SuccessFilled /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-number">{{ activeValidations }}</div>
              <div class="card-label">活跃验证</div>
            </div>
            <el-icon class="card-icon"><Loading /></el-icon>
          </el-card>
        </el-col>
      </el-row>

      <!-- 标准分类 -->
      <el-card class="standards-categories">
        <template #header>
          <div class="card-header">
            <span>数据标准分类</span>
            <el-button type="primary" @click="showValidationDialog = true">
              <el-icon><DocumentAdd /></el-icon>
              数据验证
            </el-button>
          </div>
        </template>

        <el-tabs v-model="activeCategory" @tab-click="handleCategoryChange">
          <el-tab-pane label="分子数据" name="molecular">
            <div class="standards-grid">
              <div 
                v-for="standard in getMolecularStandards()" 
                :key="standard.id"
                class="standard-card"
                @click="viewStandardDetail(standard)"
              >
                <div class="standard-header">
                  <h3>{{ standard.name }}</h3>
                  <el-tag :type="getStandardTypeColor(standard.type)">{{ standard.type }}</el-tag>
                </div>
                <p class="standard-description">{{ standard.description }}</p>
                <div class="standard-footer">
                  <span class="standard-version">v{{ standard.version }}</span>
                  <el-tag size="small" :type="standard.status === 'active' ? 'success' : 'info'">
                    {{ standard.status === 'active' ? '活跃' : '归档' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="系统建模" name="systems">
            <div class="standards-grid">
              <div 
                v-for="standard in getSystemsStandards()" 
                :key="standard.id"
                class="standard-card"
                @click="viewStandardDetail(standard)"
              >
                <div class="standard-header">
                  <h3>{{ standard.name }}</h3>
                  <el-tag :type="getStandardTypeColor(standard.type)">{{ standard.type }}</el-tag>
                </div>
                <p class="standard-description">{{ standard.description }}</p>
                <div class="standard-footer">
                  <span class="standard-version">v{{ standard.version }}</span>
                  <el-tag size="small" :type="standard.status === 'active' ? 'success' : 'info'">
                    {{ standard.status === 'active' ? '活跃' : '归档' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="医学影像" name="imaging">
            <div class="standards-grid">
              <div 
                v-for="standard in getImagingStandards()" 
                :key="standard.id"
                class="standard-card"
                @click="viewStandardDetail(standard)"
              >
                <div class="standard-header">
                  <h3>{{ standard.name }}</h3>
                  <el-tag :type="getStandardTypeColor(standard.type)">{{ standard.type }}</el-tag>
                </div>
                <p class="standard-description">{{ standard.description }}</p>
                <div class="standard-footer">
                  <span class="standard-version">v{{ standard.version }}</span>
                  <el-tag size="small" :type="standard.status === 'active' ? 'success' : 'info'">
                    {{ standard.status === 'active' ? '活跃' : '归档' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 数据验证对话框 -->
    <el-dialog v-model="showValidationDialog" title="数据验证" width="600px">
      <el-form :model="validationForm" label-width="120px">
        <el-form-item label="选择标准">
          <el-select v-model="validationForm.standard" placeholder="请选择数据标准">
            <el-option 
              v-for="standard in supportedStandards" 
              :key="standard.id"
              :label="standard.name" 
              :value="standard.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据文件">
          <el-upload
            v-model:file-list="validationForm.files"
            :auto-upload="false"
            :limit="1"
            accept=".xml,.json,.csv,.txt"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">支持 XML、JSON、CSV、TXT 格式</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showValidationDialog = false">取消</el-button>
        <el-button type="primary" @click="startValidation" :loading="isValidating">
          开始验证
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const activeCategory = ref('molecular')
const showValidationDialog = ref(false)
const isValidating = ref(false)
const validatedDatasets = ref(156)
const complianceRate = ref(0.947)
const activeValidations = ref(3)

// 表单数据
const validationForm = reactive({
  standard: '',
  files: []
})

// 支持的数据标准
const supportedStandards = ref([
  // 分子数据标准
  {
    id: 'sbml',
    name: 'SBML',
    type: 'XML',
    category: 'molecular',
    description: '系统生物学标记语言，用于生化反应网络建模',
    version: '3.2',
    status: 'active'
  },
  {
    id: 'cellml',
    name: 'CellML',
    type: 'XML',
    category: 'molecular',
    description: '细胞建模语言，用于细胞生物学模型',
    version: '2.0',
    status: 'active'
  },
  {
    id: 'pdb',
    name: 'PDB',
    type: 'Text',
    category: 'molecular',
    description: '蛋白质数据银行格式',
    version: '3.30',
    status: 'active'
  },
  {
    id: 'sdf',
    name: 'SDF',
    type: 'Text',
    category: 'molecular',
    description: '结构数据文件格式',
    version: '2.0',
    status: 'active'
  },
  // 系统建模标准
  {
    id: 'sysml',
    name: 'SysML',
    type: 'UML',
    category: 'systems',
    description: '系统建模语言',
    version: '1.6',
    status: 'active'
  },
  {
    id: 'bpmn',
    name: 'BPMN',
    type: 'XML',
    category: 'systems',
    description: '业务流程建模标记法',
    version: '2.0',
    status: 'active'
  },
  {
    id: 'archimate',
    name: 'ArchiMate',
    type: 'XML',
    category: 'systems',
    description: '企业架构建模语言',
    version: '3.1',
    status: 'active'
  },
  // 医学影像标准
  {
    id: 'dicom',
    name: 'DICOM',
    type: 'Binary',
    category: 'imaging',
    description: '医学数字成像和通信标准',
    version: '2021e',
    status: 'active'
  },
  {
    id: 'nifti',
    name: 'NIfTI',
    type: 'Binary',
    category: 'imaging',
    description: '神经影像信息学技术倡议格式',
    version: '2.0',
    status: 'active'
  },
  {
    id: 'hl7-fhir',
    name: 'HL7 FHIR',
    type: 'JSON',
    category: 'imaging',
    description: '快速健康互操作性资源',
    version: 'R4',
    status: 'active'
  }
])

// 计算属性
const getMolecularStandards = () => {
  return supportedStandards.value.filter(s => s.category === 'molecular')
}

const getSystemsStandards = () => {
  return supportedStandards.value.filter(s => s.category === 'systems')
}

const getImagingStandards = () => {
  return supportedStandards.value.filter(s => s.category === 'imaging')
}

// 方法
const getStandardTypeColor = (type) => {
  const colors = {
    'XML': 'primary',
    'JSON': 'success',
    'Text': 'warning',
    'Binary': 'danger',
    'UML': 'info'
  }
  return colors[type] || 'default'
}

const handleCategoryChange = (tab) => {
  console.log('切换到标准类别:', tab.props.name)
}

const viewStandardDetail = (standard) => {
  console.log('查看标准详情:', standard)
  ElMessage.info(`查看 ${standard.name} 标准详情`)
}

const startValidation = async () => {
  if (!validationForm.standard) {
    ElMessage.warning('请选择数据标准')
    return
  }
  
  if (validationForm.files.length === 0) {
    ElMessage.warning('请选择要验证的文件')
    return
  }

  isValidating.value = true
  
  try {
    // 模拟验证过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('数据验证完成，文件符合标准要求')
    showValidationDialog.value = false
    
    // 重置表单
    validationForm.standard = ''
    validationForm.files = []
    
  } catch (error) {
    ElMessage.error('验证失败: ' + error.message)
  } finally {
    isValidating.value = false
  }
}
</script>

<style scoped>
.data-standards-viewer {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #2c3e50;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
}

.overview-cards {
  margin-bottom: 24px;
}

.overview-card {
  position: relative;
  overflow: hidden;
}

.card-content {
  position: relative;
  z-index: 1;
}

.card-number {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #7f8c8d;
}

.card-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 48px;
  color: #ecf0f1;
  z-index: 0;
}

.standards-categories {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.standards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.standard-card {
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.standard-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.standard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.standard-header h3 {
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
}

.standard-description {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.standard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.standard-version {
  font-size: 12px;
  color: #999;
}

.content-area {
  max-width: 1200px;
}
</style>