<template>
  <div class="molecular-viewer">
    <div class="viewer-controls">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="selectedProtein" placeholder="选择蛋白质">
            <el-option
              v-for="protein in availableProteins"
              :key="protein.id"
              :label="protein.name"
              :value="protein.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="viewMode" placeholder="显示模式">
            <el-option label="线框模式" value="wireframe" />
            <el-option label="球棍模式" value="ball_stick" />
            <el-option label="表面模式" value="surface" />
            <el-option label="卡通模式" value="cartoon" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="loadStructure">
            <el-icon><View /></el-icon>
            加载结构
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="analyzeStructure">
            <el-icon><DataAnalysis /></el-icon>
            结构分析
          </el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 3D可视化区域 -->
    <div class="viewer-3d" ref="viewer3d">
      <!-- PyMOL或其他分子可视化工具的嵌入区域 -->
      <div v-if="!structureLoaded" class="loading-placeholder">
        <el-icon class="loading-icon" size="48"><Loading /></el-icon>
        <p>准备分子可视化环境...</p>
      </div>
      <div v-else class="structure-display">
        <!-- 实际的3D结构显示 -->
        <canvas ref="molecularCanvas" />
      </div>
    </div>
    
    <!-- 分析结果面板 -->
    <div class="analysis-panel" v-if="analysisResults">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="PDB ID">
              {{ analysisResults.pdb_id }}
            </el-descriptions-item>
            <el-descriptions-item label="分子量">
              {{ analysisResults.molecular_weight }}
            </el-descriptions-item>
            <el-descriptions-item label="氨基酸数">
              {{ analysisResults.amino_acid_count }}
            </el-descriptions-item>
            <el-descriptions-item label="二级结构">
              {{ analysisResults.secondary_structure }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="结构特征" name="features">
          <StructureFeatures :features="analysisResults.features" />
        </el-tab-pane>
        <el-tab-pane label="相互作用" name="interactions">
          <InteractionAnalysis :interactions="analysisResults.interactions" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const selectedProtein = ref('')
const viewMode = ref('cartoon')
const structureLoaded = ref(false)
const analysisResults = ref(null)
const activeTab = ref('basic')

const availableProteins = ref([
  { id: '1ABC', name: '血红蛋白 (1ABC)' },
  { id: '2XYZ', name: '胰岛素 (2XYZ)' },
  { id: '3DEF', name: '溶菌酶 (3DEF)' }
])

// 模板引用
const viewer3d = ref(null)
const molecularCanvas = ref(null)

// 方法
const loadStructure = async () => {
  if (!selectedProtein.value) {
    ElMessage.warning('请先选择要加载的蛋白质')
    return
  }
  
  try {
    structureLoaded.value = false
    
    // 调用后端API加载蛋白质结构
    const response = await fetch('/api/v1/biomedical/molecular/load', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        protein_id: selectedProtein.value,
        view_mode: viewMode.value
      })
    })
    
    const data = await response.json()
    
    if (data.status === 'success') {
      await initializeMolecularViewer(data.structure_data)
      structureLoaded.value = true
      ElMessage.success('蛋白质结构加载成功')
    }
  } catch (error) {
    ElMessage.error('加载蛋白质结构失败')
    console.error(error)
  }
}

const initializeMolecularViewer = async (structureData) => {
  // 初始化分子可视化（这里需要集成PyMOL或其他可视化库）
  await nextTick()
  
  if (molecularCanvas.value) {
    // 设置canvas
    const canvas = molecularCanvas.value
    canvas.width = 800
    canvas.height = 600
    
    // 这里应该集成实际的分子可视化库
    // 例如：PyMOL、ChimeraX、或基于WebGL的库
    renderMolecularStructure(canvas, structureData)
  }
}

const renderMolecularStructure = (canvas, data) => {
  // 渲染分子结构的实际实现
  const ctx = canvas.getContext('2d')
  ctx.fillStyle = '#f0f8ff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  // 绘制示例蛋白质结构
  ctx.fillStyle = '#ff6b6b'
  ctx.beginPath()
  ctx.arc(400, 300, 50, 0, Math.PI * 2)
  ctx.fill()
  
  ctx.fillStyle = '#4ecdc4'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('蛋白质结构可视化', 400, 350)
  ctx.fillText(`ID: ${selectedProtein.value}`, 400, 370)
}

const analyzeStructure = async () => {
  if (!structureLoaded.value) {
    ElMessage.warning('请先加载蛋白质结构')
    return
  }
  
  try {
    const response = await fetch('/api/v1/biomedical/molecular/analyze', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        protein_id: selectedProtein.value,
        analysis_type: 'comprehensive'
      })
    })
    
    const data = await response.json()
    analysisResults.value = data.results
    
    ElMessage.success('结构分析完成')
  } catch (error) {
    ElMessage.error('结构分析失败')
    console.error(error)
  }
}

onMounted(() => {
  // 初始化分子可视化环境
  initializeViewer()
})

const initializeViewer = () => {
  // 检查可用的分子可视化工具
  checkAvailableTools()
}

const checkAvailableTools = async () => {
  try {
    const response = await fetch('/api/v1/biomedical/tools')
    const data = await response.json()
    
    const molecularTools = data.tools.filter(
      tool => tool.category === 'molecular_visualization'
    )
    
    if (molecularTools.length === 0) {
      ElMessage.warning('未检测到分子可视化工具，请安装PyMOL或ChimeraX')
    }
  } catch (error) {
    console.error('检查工具失败:', error)
  }
}
</script>

<style scoped>
.molecular-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 20px;
}

.viewer-controls {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.viewer-3d {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-placeholder {
  text-align: center;
  color: #666;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.structure-display {
  width: 100%;
  height: 100%;
  position: relative;
}

.analysis-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
}
</style>