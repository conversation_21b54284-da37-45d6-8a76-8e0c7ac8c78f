<template>
  <div class="performance-panel">
    <el-card>
      <template #header>
        <h4>性能监控</h4>
      </template>
      <div v-if="!metrics || Object.keys(metrics).length === 0" class="no-metrics">
        <p>暂无性能数据</p>
      </div>
      <div v-else class="metrics-list">
        <div class="metric-item">
          <span class="metric-label">CPU使用率</span>
          <span class="metric-value">{{ metrics.cpu || '0' }}%</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">内存使用</span>
          <span class="metric-value">{{ metrics.memory || '0' }}%</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">响应时间</span>
          <span class="metric-value">{{ metrics.responseTime || '0' }}ms</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">吞吐量</span>
          <span class="metric-value">{{ metrics.throughput || '0' }}/s</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  metrics: {
    type: Object,
    default: () => ({})
  }
})
</script>

<style scoped>
.performance-panel {
  margin-bottom: 16px;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  color: #666;
  font-size: 14px;
}

.metric-value {
  color: #333;
  font-weight: 600;
}

.no-metrics {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style> 