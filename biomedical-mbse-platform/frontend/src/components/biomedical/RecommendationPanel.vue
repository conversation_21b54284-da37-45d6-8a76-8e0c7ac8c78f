<template>
  <div class="recommendation-panel">
    <el-card>
      <template #header>
        <h4>AI推荐</h4>
      </template>
      <div v-if="recommendations.length === 0" class="no-recommendations">
        <p>暂无推荐</p>
      </div>
      <div v-else>
        <div 
          v-for="recommendation in recommendations" 
          :key="recommendation.id"
          class="recommendation-item"
        >
          <h5>{{ recommendation.name }}</h5>
          <p>{{ recommendation.description }}</p>
          <el-button 
            type="primary" 
            size="small" 
            @click="handleApply(recommendation)"
          >
            应用
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  recommendations: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['apply'])

const handleApply = (recommendation) => {
  emit('apply', recommendation)
}
</script>

<style scoped>
.recommendation-panel {
  margin-bottom: 16px;
}

.recommendation-item {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.recommendation-item h5 {
  margin: 0 0 8px 0;
  color: #333;
}

.recommendation-item p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.no-recommendations {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style> 