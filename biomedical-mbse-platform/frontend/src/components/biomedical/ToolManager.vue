<template>
  <div class="tool-manager">
    <!-- 工具概览 -->
    <div class="tools-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="tool-stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ availableTools.length }}</div>
              <div class="stat-label">可用工具</div>
            </div>
            <el-icon class="stat-icon"><Tools /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="tool-stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ activeTools.length }}</div>
              <div class="stat-label">运行中</div>
            </div>
            <el-icon class="stat-icon"><Loading /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="tool-stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ toolChains.length }}</div>
              <div class="stat-label">工具链</div>
            </div>
            <el-icon class="stat-icon"><Share /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="tool-stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ completedJobs }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <el-icon class="stat-icon"><SuccessFilled /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 工具列表 -->
    <div class="tools-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>177个生物医学工具</span>
            <el-button type="primary" @click="refreshTools">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        
        <!-- 工具过滤 -->
        <div class="tools-filter">
          <el-row :gutter="15">
            <el-col :span="8">
              <el-select v-model="filterCategory" placeholder="按类别筛选">
                <el-option label="全部" value="all" />
                <el-option label="分子可视化" value="molecular_visualization" />
                <el-option label="统计分析" value="statistical_analysis" />
                <el-option label="序列分析" value="sequence_analysis" />
                <el-option label="药物发现" value="drug_discovery" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="filterStatus" placeholder="按状态筛选">
                <el-option label="全部" value="all" />
                <el-option label="可用" value="available" />
                <el-option label="运行中" value="running" />
                <el-option label="离线" value="offline" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索工具名称"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
          </el-row>
        </div>
        
        <!-- 工具表格 -->
        <el-table
          :data="filteredTools"
          style="width: 100%"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="工具名称" width="150">
            <template #default="scope">
              <div class="tool-name">
                <el-icon class="tool-icon">
                  <component :is="getToolIcon(scope.row.category)" />
                </el-icon>
                {{ scope.row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="类别" width="150">
            <template #default="scope">
              <el-tag :type="getCategoryTagType(scope.row.category)">
                {{ getCategoryLabel(scope.row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 'available' ? 'success' : 
                       scope.row.status === 'running' ? 'warning' : 'danger'"
              >
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button-group>
                <el-button
                  size="small"
                  type="primary"
                  @click="launchTool(scope.row)"
                  :disabled="scope.row.status !== 'available'"
                >
                  启动
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  @click="configTool(scope.row)"
                >
                  配置
                </el-button>
                <el-button
                  size="small"
                  type="success"
                  @click="addToChain(scope.row)"
                >
                  加入链
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 工具链管理 -->
    <div class="toolchain-management">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>工具链管理</span>
            <el-button type="success" @click="createNewChain">
              <el-icon><Plus /></el-icon>
              新建工具链
            </el-button>
          </div>
        </template>
        
        <div class="toolchain-list">
          <el-row :gutter="20">
            <el-col
              :span="8"
              v-for="chain in toolChains"
              :key="chain.id"
            >
              <el-card class="toolchain-card">
                <div class="chain-header">
                  <h4>{{ chain.name }}</h4>
                  <el-tag type="info">{{ chain.tools.length }}个工具</el-tag>
                </div>
                <div class="chain-tools">
                  <div
                    v-for="(tool, index) in chain.tools"
                    :key="tool.id"
                    class="chain-tool"
                  >
                    <span>{{ index + 1 }}. {{ tool.name }}</span>
                    <el-icon v-if="index < chain.tools.length - 1">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </div>
                <div class="chain-actions">
                  <el-button size="small" type="primary" @click="executeChain(chain)">
                    执行
                  </el-button>
                  <el-button size="small" @click="editChain(chain)">
                    编辑
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const availableTools = ref([])
const activeTools = ref([])
const toolChains = ref([])
const completedJobs = ref(0)
const filterCategory = ref('all')
const filterStatus = ref('all')
const searchKeyword = ref('')
const selectedTools = ref([])

// 计算属性
const filteredTools = computed(() => {
  let tools = availableTools.value
  
  // 按类别过滤
  if (filterCategory.value !== 'all') {
    tools = tools.filter(tool => tool.category === filterCategory.value)
  }
  
  // 按状态过滤
  if (filterStatus.value !== 'all') {
    tools = tools.filter(tool => tool.status === filterStatus.value)
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    tools = tools.filter(tool =>
      tool.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  return tools
})

// 方法
const refreshTools = async () => {
  try {
    const response = await fetch('/api/v1/biomedical/tools')
    const data = await response.json()
    // 确保data.tools是数组
    availableTools.value = Array.isArray(data.tools) ? data.tools : []
    
    // 获取活跃工具
    const activeResponse = await fetch('/api/v1/biomedical/tools/active')
    const activeData = await activeResponse.json()
    // 确保activeData.active_tools是数组
    activeTools.value = Array.isArray(activeData.active_tools) ? activeData.active_tools : []
    
    // 获取工具链
    const chainResponse = await fetch('/api/v1/biomedical/toolchains')
    const chainData = await chainResponse.json()
    // 确保chainData.toolchains是数组
    toolChains.value = Array.isArray(chainData.toolchains) ? chainData.toolchains : []
    
    ElMessage.success('工具列表已刷新')
  } catch (error) {
    console.error('刷新工具列表失败:', error)
    // 确保在错误情况下也初始化为空数组
    availableTools.value = []
    activeTools.value = []
    toolChains.value = []
    ElMessage.error('刷新工具列表失败')
  }
}

const launchTool = async (tool) => {
  try {
    const response = await fetch(`/api/v1/biomedical/tools/${tool.id}/launch`, {
      method: 'POST'
    })
    
    if (response.ok) {
      ElMessage.success(`${tool.name} 启动成功`)
      tool.status = 'running'
      activeTools.value.push(tool)
    }
  } catch (error) {
    ElMessage.error(`启动 ${tool.name} 失败`)
  }
}

const configTool = (tool) => {
  ElMessageBox.prompt(`配置 ${tool.name}`, '工具配置', {
    confirmButtonText: '保存',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    ElMessage.success('配置已保存')
  })
}

const addToChain = (tool) => {
  // 添加工具到工具链的逻辑
  ElMessage.success(`${tool.name} 已添加到工具链`)
}

const createNewChain = () => {
  ElMessageBox.prompt('请输入工具链名称', '新建工具链', {
    confirmButtonText: '创建',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    const newChain = {
      id: Date.now(),
      name: value,
      tools: [],
      status: 'draft'
    }
    toolChains.value.push(newChain)
    ElMessage.success('工具链创建成功')
  })
}

const executeChain = async (chain) => {
  try {
    const response = await fetch(`/api/v1/biomedical/toolchains/${chain.id}/execute`, {
      method: 'POST'
    })
    
    if (response.ok) {
      ElMessage.success(`工具链 ${chain.name} 开始执行`)
    }
  } catch (error) {
    ElMessage.error('执行工具链失败')
  }
}

const editChain = (chain) => {
  // 打开工具链编辑器
  ElMessage.info('打开工具链编辑器')
}

const handleSelectionChange = (selection) => {
  selectedTools.value = selection
}

const loadToolChains = async () => {
  try {
    const response = await fetch('/api/v1/biomedical/toolchains')
    const data = await response.json()
    toolChains.value = Array.isArray(data.toolchains) ? data.toolchains : []
  } catch (error) {
    console.error('加载工具链失败:', error)
    toolChains.value = []
  }
}

// 工具函数
const getToolIcon = (category) => {
  const icons = {
    molecular_visualization: 'View',
    statistical_analysis: 'DataAnalysis',
    sequence_analysis: 'List',
    drug_discovery: 'Medicine'
  }
  return icons[category] || 'Tools'
}

const getCategoryTagType = (category) => {
  const types = {
    molecular_visualization: 'primary',
    statistical_analysis: 'success',
    sequence_analysis: 'warning',
    drug_discovery: 'danger'
  }
  return types[category] || 'info'
}

const getCategoryLabel = (category) => {
  const labels = {
    molecular_visualization: '分子可视化',
    statistical_analysis: '统计分析',
    sequence_analysis: '序列分析',
    drug_discovery: '药物发现'
  }
  return labels[category] || category
}

const getStatusLabel = (status) => {
  const labels = {
    available: '可用',
    running: '运行中',
    offline: '离线'
  }
  return labels[status] || status
}

// 生命周期
onMounted(() => {
  refreshTools()
  loadToolChains()
})
</script>

<style scoped>
.tool-manager {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tools-overview {
  margin-bottom: 20px;
}

.tool-stat-card {
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  color: #7f8c8d;
  margin-top: 5px;
}

.stat-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 2em;
  color: #ecf0f1;
  z-index: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tools-filter {
  margin-bottom: 20px;
}

.tool-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-icon {
  color: #3498db;
}

.toolchain-card {
  margin-bottom: 15px;
}

.chain-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chain-header h4 {
  margin: 0;
}

.chain-tools {
  margin-bottom: 15px;
}

.chain-tool {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0;
  font-size: 14px;
}

.chain-actions {
  display: flex;
  gap: 10px;
}
</style>