<template>
  <div class="diagram-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <el-button-group>
        <el-button 
          type="primary" 
          icon="Plus" 
          @click="showCreateElementDialog = true"
        >
          添加元素
        </el-button>
        <el-button 
          icon="Delete" 
          :disabled="!hasSelectedElements"
          @click="deleteSelectedElements"
        >
          删除
        </el-button>
        <el-button 
          icon="Check" 
          @click="validateDiagram"
        >
          验证
        </el-button>
        <el-button 
          icon="Tools" 
          @click="showWorkflowDialog = true"
        >
          执行工作流
        </el-button>
      </el-button-group>
      
      <!-- 图形信息 -->
      <div class="diagram-info">
        <el-tag v-if="activeDiagram" type="info">
          {{ activeDiagram.name }} ({{ diagramElementCount }} 个元素)
        </el-tag>
      </div>
    </div>

    <!-- 主编辑区域 -->
    <div class="editor-main">
      <!-- 画布 -->
      <div ref="canvasContainer" class="canvas-container">
        <svg 
          ref="svgCanvas" 
          class="svg-canvas"
          @click="handleCanvasClick"
          @mousemove="handleCanvasMouseMove"
        >
          <!-- 网格背景 -->
          <defs>
            <pattern 
              id="grid" 
              width="20" 
              height="20" 
              patternUnits="userSpaceOnUse"
            >
              <path 
                d="M 20 0 L 0 0 0 20" 
                fill="none" 
                stroke="#e0e0e0" 
                stroke-width="1"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          <!-- 渲染图形元素 -->
          <g v-if="activeDiagram">
            <DiagramElement
              v-for="element in activeDiagram.elements"
              :key="element.id"
              :element="element"
              :selected="selectedElements.includes(element.id)"
              @click="selectElement(element.id, $event)"
              @update="updateElement(element.id, $event)"
            />
            
            <!-- 渲染关系 -->
            <DiagramRelationship
              v-for="relationship in activeDiagram.relationships"
              :key="relationship.id"
              :relationship="relationship"
              :elements="activeDiagram.elements"
            />
          </g>
          
          <!-- 选择框 -->
          <rect
            v-if="selectionBox"
            :x="selectionBox.x"
            :y="selectionBox.y"
            :width="selectionBox.width"
            :height="selectionBox.height"
            fill="rgba(64, 158, 255, 0.1)"
            stroke="#409eff"
            stroke-width="1"
            stroke-dasharray="5,5"
          />
        </svg>
      </div>

      <!-- 侧边栏 -->
      <div class="editor-sidebar">
        <!-- 元素调色板 -->
        <el-collapse v-model="activeCollapse">
          <el-collapse-item title="UML元素" name="uml">
            <ElementPalette 
              :elements="umlElements"
              @drag-start="handleElementDragStart"
            />
          </el-collapse-item>
          
          <el-collapse-item title="SysML元素" name="sysml">
            <ElementPalette 
              :elements="sysmlElements"
              @drag-start="handleElementDragStart"
            />
          </el-collapse-item>
          
          <el-collapse-item title="生物医学元素" name="biomedical">
            <ElementPalette 
              :elements="biomedicalElements"
              @drag-start="handleElementDragStart"
            />
          </el-collapse-item>
        </el-collapse>

        <!-- 属性面板 -->
        <div class="properties-panel">
          <h4>属性</h4>
          <PropertyPanel 
            v-if="selectedElementsData.length > 0"
            :elements="selectedElementsData"
            @update="updateSelectedElements"
          />
          <div v-else class="no-selection">
            请选择一个元素查看属性
          </div>
        </div>
      </div>
    </div>

    <!-- 创建元素对话框 -->
    <CreateElementDialog
      v-model="showCreateElementDialog"
      :diagram-type="activeDiagram?.diagram_type"
      @create="createNewElement"
    />

    <!-- 工作流执行对话框 -->
    <WorkflowExecuteDialog
      v-model="showWorkflowDialog"
      :project-id="currentProject?.id"
      :diagram-id="activeDiagram?.id"
      @execute="executeWorkflow"
    />

    <!-- 验证结果对话框 -->
    <ValidationResultDialog
      v-model="showValidationDialog"
      :validation-result="validationResult"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useModelingEngine } from '@/composables/useModelingEngine'
import DiagramElement from './DiagramElement.vue'
import DiagramRelationship from './DiagramRelationship.vue'
import ElementPalette from './ElementPalette.vue'
import PropertyPanel from './PropertyPanel.vue'
import CreateElementDialog from './CreateElementDialog.vue'
import WorkflowExecuteDialog from './WorkflowExecuteDialog.vue'
import ValidationResultDialog from './ValidationResultDialog.vue'
import type { 
  UMLElement, 
  ValidationResult,
  WorkflowExecuteRequest 
} from '@/types/modeling'

// 组合式API
const {
  currentProject,
  activeDiagram,
  selectedElements,
  selectedElementsData,
  hasSelectedElements,
  diagramElementCount,
  selectElements,
  updateElement,
  removeElement,
  createBiomedicalBlock,
  validateDiagram: validateDiagramAPI,
  executeWorkflow: executeWorkflowAPI
} = useModelingEngine()

// 响应式数据
const canvasContainer = ref<HTMLDivElement>()
const svgCanvas = ref<SVGSVGElement>()
const showCreateElementDialog = ref(false)
const showWorkflowDialog = ref(false)
const showValidationDialog = ref(false)
const validationResult = ref<ValidationResult | null>(null)
const activeCollapse = ref(['uml', 'sysml', 'biomedical'])
const selectionBox = ref<{ x: number, y: number, width: number, height: number } | null>(null)

// 元素调色板数据
const umlElements = computed(() => [
  { type: 'Class', icon: '📦', label: '类' },
  { type: 'Interface', icon: '🔌', label: '接口' },
  { type: 'Component', icon: '🧩', label: '组件' },
  { type: 'Package', icon: '📁', label: '包' },
  { type: 'Actor', icon: '👤', label: '角色' },
  { type: 'UseCase', icon: '⭕', label: '用例' },
])

const sysmlElements = computed(() => [
  { type: 'Block', icon: '⬜', label: '块' },
  { type: 'Requirement', icon: '📋', label: '需求' },
  { type: 'ConstraintBlock', icon: '🔒', label: '约束块' },
  { type: 'ValueType', icon: '🔢', label: '值类型' },
])

const biomedicalElements = computed(() => [
  { type: 'Protein', icon: '🧬', label: '蛋白质', biological_type: 'protein' },
  { type: 'Gene', icon: '🧪', label: '基因', biological_type: 'gene' },
  { type: 'Pathway', icon: '🔄', label: '通路', biological_type: 'pathway' },
  { type: 'Cell', icon: '🦠', label: '细胞', biological_type: 'cell' },
])

// 方法
const selectElement = (elementId: string, event: MouseEvent) => {
  if (event.ctrlKey || event.metaKey) {
    // 多选模式
    const newSelection = selectedElements.value.includes(elementId)
      ? selectedElements.value.filter(id => id !== elementId)
      : [...selectedElements.value, elementId]
    selectElements(newSelection)
  } else {
    // 单选模式
    selectElements([elementId])
  }
}

const handleCanvasClick = (event: MouseEvent) => {
  // 点击空白区域取消选择
  if (event.target === svgCanvas.value) {
    selectElements([])
  }
}

const handleCanvasMouseMove = (event: MouseEvent) => {
  // 处理鼠标移动事件（用于拖拽等操作）
  // TODO: 实现拖拽逻辑
}

const handleElementDragStart = (elementType: string, biologicalType?: string) => {
  // 处理元素拖拽开始
  // TODO: 实现拖拽逻辑
  console.log('拖拽开始:', elementType, biologicalType)
}

const deleteSelectedElements = async () => {
  if (selectedElements.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定删除选中的 ${selectedElements.value.length} 个元素吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 删除选中的元素
    selectedElements.value.forEach(elementId => {
      removeElement(elementId)
    })
    
    selectElements([])
    ElMessage.success('元素删除成功')
  } catch {
    // 用户取消删除
  }
}

const createNewElement = async (elementData: any) => {
  if (!currentProject.value || !activeDiagram.value) {
    ElMessage.error('请先选择项目和图形')
    return
  }
  
  try {
    // 创建元素的默认位置
    const defaultPosition = {
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200
    }
    
    if (elementData.biological_type) {
      // 创建生物医学块
      await createBiomedicalBlock(
        currentProject.value.id,
        activeDiagram.value.id,
        {
          name: elementData.name,
          biological_type: elementData.biological_type,
          auto_configure: true
        }
      )
    } else {
      // 创建普通UML元素
      // TODO: 实现普通UML元素创建
      console.log('创建UML元素:', elementData)
    }
  } catch (error) {
    console.error('创建元素失败:', error)
  }
}

const updateSelectedElements = (updates: Partial<UMLElement>) => {
  selectedElements.value.forEach(elementId => {
    updateElement(elementId, updates)
  })
}

const validateDiagram = async () => {
  if (!currentProject.value || !activeDiagram.value) {
    ElMessage.error('请先选择项目和图形')
    return
  }
  
  try {
    const result = await validateDiagramAPI(
      activeDiagram.value.id,
      currentProject.value.id
    )
    validationResult.value = result
    showValidationDialog.value = true
  } catch (error) {
    console.error('图形验证失败:', error)
  }
}

const executeWorkflow = async (workflowRequest: WorkflowExecuteRequest) => {
  if (!currentProject.value) {
    ElMessage.error('请先选择项目')
    return
  }
  
  try {
    await executeWorkflowAPI(currentProject.value.id, workflowRequest)
    showWorkflowDialog.value = false
  } catch (error) {
    console.error('执行工作流失败:', error)
  }
}

// 生命周期
onMounted(() => {
  // 初始化画布
  if (canvasContainer.value) {
    // 设置画布大小
    nextTick(() => {
      if (svgCanvas.value && canvasContainer.value) {
        const rect = canvasContainer.value.getBoundingClientRect()
        svgCanvas.value.setAttribute('width', String(rect.width))
        svgCanvas.value.setAttribute('height', String(rect.height))
      }
    })
  }
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped>
.diagram-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.diagram-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #fafafa;
}

.svg-canvas {
  width: 100%;
  height: 100%;
  min-width: 800px;
  min-height: 600px;
  cursor: crosshair;
}

.editor-sidebar {
  width: 300px;
  background: white;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.properties-panel {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.properties-panel h4 {
  margin: 0 0 16px 0;
  color: #333;
}

.no-selection {
  color: #999;
  text-align: center;
  padding: 32px 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .editor-sidebar {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .editor-main {
    flex-direction: column;
  }
  
  .editor-sidebar {
    width: 100%;
    height: 200px;
    border-left: none;
    border-top: 1px solid #e0e0e0;
  }
}
</style> 