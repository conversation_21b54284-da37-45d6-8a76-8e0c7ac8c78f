<template>
  <div class="flow-editor">
    <div class="editor-toolbar">
      <div class="tool-group">
        <el-button-group>
          <el-button size="small" @click="undo" :disabled="!canUndo">撤销</el-button>
          <el-button size="small" @click="redo" :disabled="!canRedo">重做</el-button>
        </el-button-group>
        <el-button size="small" @click="clearCanvas">清空画布</el-button>
        <el-button size="small" @click="saveFlow">保存</el-button>
        <el-button size="small" @click="exportFlow">导出</el-button>
      </div>
      <div class="zoom-controls">
        <el-button size="small" @click="zoomIn">放大</el-button>
        <span class="zoom-display">{{ Math.round(zoomLevel * 100) }}%</span>
        <el-button size="small" @click="zoomOut">缩小</el-button>
        <el-button size="small" @click="fitView">适应</el-button>
      </div>
    </div>
    
    <div class="editor-content">
      <div class="stencil-panel">
        <div class="panel-title">组件库</div>
        <div class="node-categories">
          <div 
            v-for="category in nodeCategories" 
            :key="category.name"
            class="category"
          >
            <div class="category-title" @click="toggleCategory(category.name)">
              <el-icon><component :is="category.expanded ? 'ArrowDown' : 'ArrowRight'" /></el-icon>
              {{ category.title }}
            </div>
            <div v-show="category.expanded" class="category-nodes">
              <div 
                v-for="node in category.nodes"
                :key="node.type"
                class="stencil-node"
                :draggable="true"
                @dragstart="onDragStart($event, node)"
              >
                <div class="node-icon" :style="{ backgroundColor: node.color }">
                  {{ node.icon }}
                </div>
                <div class="node-label">{{ node.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="canvas-panel">
        <div ref="graphContainer" class="graph-container"></div>
      </div>
      
      <div class="property-panel">
        <div class="panel-title">属性面板</div>
        <div v-if="selectedItem" class="property-content">
          <el-form :model="selectedItem" size="small" label-width="80px">
            <el-form-item label="名称">
              <el-input v-model="selectedItem.label" @input="updateProperty" />
            </el-form-item>
            <el-form-item label="描述">
              <el-input 
                v-model="selectedItem.description" 
                type="textarea" 
                :rows="3"
                @input="updateProperty"
              />
            </el-form-item>
            <el-form-item v-if="selectedItem.nodeType" label="类型">
              <el-select v-model="selectedItem.nodeType" @change="updateProperty">
                <el-option 
                  v-for="type in getNodeTypes()" 
                  :key="type.value" 
                  :label="type.label" 
                  :value="type.value" 
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="selectedItem.nodeType === 'decision'" label="条件">
              <el-input v-model="selectedItem.condition" @input="updateProperty" />
            </el-form-item>
            <el-form-item v-if="selectedItem.nodeType === 'data'" label="数据源">
              <el-input v-model="selectedItem.dataSource" @input="updateProperty" />
            </el-form-item>
          </el-form>
        </div>
        <div v-else class="no-selection">
          <el-empty description="请选择一个节点或连线" />
        </div>
      </div>
    </div>
    
    <div class="editor-statusbar">
      <div class="status-info">
        <span>节点: {{ nodeCount }}</span>
        <span>连线: {{ edgeCount }}</span>
        <span>选中: {{ selectedCount }}</span>
      </div>
      <div class="grid-toggle">
        <el-switch 
          v-model="showGrid" 
          size="small" 
          active-text="网格" 
          @change="toggleGrid"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { Graph } from '@antv/x6'
import { Stencil } from '@antv/x6-plugin-stencil'
import { History } from '@antv/x6-plugin-history'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue'

const emit = defineEmits(['save', 'export', 'change'])

// 响应式数据
const graphContainer = ref(null)
const selectedItem = ref(null)
const showGrid = ref(true)
const zoomLevel = ref(1)
const nodeCount = ref(0)
const edgeCount = ref(0)
const selectedCount = ref(0)
const canUndo = ref(false)
const canRedo = ref(false)

// X6 图实例
let graph = null
let stencil = null

// 节点分类
const nodeCategories = ref([
  {
    name: 'basic',
    title: '基础节点',
    expanded: true,
    nodes: [
      { type: 'start', label: '开始', icon: '●', color: '#22c55e' },
      { type: 'end', label: '结束', icon: '●', color: '#ef4444' },
      { type: 'process', label: '处理', icon: '□', color: '#3b82f6' },
      { type: 'decision', label: '判断', icon: '◇', color: '#f59e0b' },
      { type: 'data', label: '数据', icon: '▷', color: '#8b5cf6' }
    ]
  },
  {
    name: 'biomedical',
    title: '生物医学',
    expanded: true,
    nodes: [
      { type: 'sample', label: '样本', icon: '🧪', color: '#06b6d4' },
      { type: 'experiment', label: '实验', icon: '🔬', color: '#10b981' },
      { type: 'analysis', label: '分析', icon: '📊', color: '#f59e0b' },
      { type: 'result', label: '结果', icon: '📋', color: '#8b5cf6' },
      { type: 'protocol', label: '协议', icon: '📄', color: '#6b7280' }
    ]
  },
  {
    name: 'modeling',
    title: '建模组件',
    expanded: false,
    nodes: [
      { type: 'model', label: '模型', icon: '🏗️', color: '#dc2626' },
      { type: 'simulation', label: '仿真', icon: '⚙️', color: '#059669' },
      { type: 'validation', label: '验证', icon: '✓', color: '#0d9488' },
      { type: 'optimization', label: '优化', icon: '📈', color: '#7c3aed' }
    ]
  }
])

// 计算属性
const getNodeTypes = () => [
  { label: '开始节点', value: 'start' },
  { label: '结束节点', value: 'end' },
  { label: '处理节点', value: 'process' },
  { label: '判断节点', value: 'decision' },
  { label: '数据节点', value: 'data' }
]

// 初始化编辑器
const initEditor = () => {
  const container = graphContainer.value
  if (!container) return

  // 创建图实例
  graph = new Graph({
    container,
    width: container.offsetWidth,
    height: container.offsetHeight,
    background: {
      color: '#f8f9fa'
    },
    grid: {
      visible: showGrid.value,
      type: 'dot',
      args: {
        color: '#ddd',
        thickness: 1
      }
    },
    selecting: {
      enabled: true,
      multiple: true,
      rubberband: true,
      movable: true,
      showNodeSelectionBox: true
    },
    connecting: {
      router: 'orthogonal',
      connector: {
        name: 'rounded',
        args: {
          radius: 8
        }
      },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false,
      snap: {
        radius: 20
      },
      createEdge() {
        return new Shape.Edge({
          attrs: {
            line: {
              stroke: '#666',
              strokeWidth: 2,
              targetMarker: {
                name: 'block',
                width: 12,
                height: 8
              }
            }
          },
          zIndex: 0
        })
      },
      validateConnection({ targetMagnet }) {
        return !!targetMagnet
      }
    },
    highlighting: {
      magnetAdsorbed: {
        name: 'stroke',
        args: {
          attrs: {
            fill: '#5F95FF',
            stroke: '#5F95FF'
          }
        }
      }
    },
    resizing: true,
    rotating: true,
    snapline: true,
    keyboard: true,
    clipboard: true
  })

  // 注册插件
  registerPlugins()

  // 注册自定义节点
  registerCustomNodes()

  // 绑定事件
  bindEvents()

  // 创建模板面板
  createStencil()
}

// 注册插件
const registerPlugins = () => {
  // 历史记录
  graph.use(new History({
    enabled: true
  }))

  // 选择
  graph.use(new Selection({
    enabled: true,
    multiple: true,
    rubberband: true,
    movable: true,
    showNodeSelectionBox: true
  }))

  // 对齐线
  graph.use(new Snapline({
    enabled: true
  }))
}

// 注册自定义节点
const registerCustomNodes = () => {
  // 开始节点
  Graph.registerNode('start', {
    inherit: 'circle',
    width: 60,
    height: 60,
    attrs: {
      body: {
        strokeWidth: 2,
        stroke: '#22c55e',
        fill: '#dcfce7'
      },
      text: {
        fontSize: 12,
        fill: '#166534'
      }
    },
    ports: {
      groups: {
        out: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#31d0c6',
              strokeWidth: 2,
              fill: '#fff'
            }
          }
        }
      },
      items: [
        { group: 'out', id: 'out1' }
      ]
    }
  })

  // 结束节点
  Graph.registerNode('end', {
    inherit: 'circle',
    width: 60,
    height: 60,
    attrs: {
      body: {
        strokeWidth: 2,
        stroke: '#ef4444',
        fill: '#fef2f2'
      },
      text: {
        fontSize: 12,
        fill: '#dc2626'
      }
    },
    ports: {
      groups: {
        in: {
          position: 'top',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#31d0c6',
              strokeWidth: 2,
              fill: '#fff'
            }
          }
        }
      },
      items: [
        { group: 'in', id: 'in1' }
      ]
    }
  })

  // 处理节点
  Graph.registerNode('process', {
    inherit: 'rect',
    width: 100,
    height: 60,
    attrs: {
      body: {
        strokeWidth: 2,
        stroke: '#3b82f6',
        fill: '#eff6ff',
        rx: 8,
        ry: 8
      },
      text: {
        fontSize: 12,
        fill: '#1e40af'
      }
    },
    ports: {
      groups: {
        in: {
          position: 'top',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#31d0c6',
              strokeWidth: 2,
              fill: '#fff'
            }
          }
        },
        out: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#31d0c6',
              strokeWidth: 2,
              fill: '#fff'
            }
          }
        }
      },
      items: [
        { group: 'in', id: 'in1' },
        { group: 'out', id: 'out1' }
      ]
    }
  })

  // 判断节点
  Graph.registerNode('decision', {
    inherit: 'polygon',
    width: 80,
    height: 80,
    attrs: {
      body: {
        strokeWidth: 2,
        stroke: '#f59e0b',
        fill: '#fffbeb',
        refPoints: '0,10 10,0 20,10 10,20'
      },
      text: {
        fontSize: 12,
        fill: '#d97706'
      }
    },
    ports: {
      groups: {
        in: {
          position: 'top',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#31d0c6',
              strokeWidth: 2,
              fill: '#fff'
            }
          }
        },
        out: {
          position: 'bottom',
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              stroke: '#31d0c6',
              strokeWidth: 2,
              fill: '#fff'
            }
          }
        }
      },
      items: [
        { group: 'in', id: 'in1' },
        { group: 'out', id: 'out1' },
        { group: 'out', id: 'out2' }
      ]
    }
  })
}

// 创建模板面板
const createStencil = () => {
  const stencilContainer = document.createElement('div')
  stencilContainer.style.width = '100%'
  stencilContainer.style.height = '300px'
  
  stencil = new Stencil({
    title: '组件库',
    target: graph,
    stencilGraphWidth: 200,
    stencilGraphHeight: 300,
    collapsable: true,
    groups: [
      {
        title: '基础节点',
        name: 'basic'
      },
      {
        title: '生物医学',
        name: 'biomedical'
      }
    ],
    layoutOptions: {
      columns: 2,
      columnWidth: 80,
      rowHeight: 55
    }
  })

  // 添加节点到模板
  const basicNodes = [
    graph.createNode({
      shape: 'start',
      label: '开始'
    }),
    graph.createNode({
      shape: 'end',
      label: '结束'
    }),
    graph.createNode({
      shape: 'process',
      label: '处理'
    }),
    graph.createNode({
      shape: 'decision',
      label: '判断'
    })
  ]

  stencil.load(basicNodes, 'basic')
}

// 绑定事件
const bindEvents = () => {
  // 节点/边选中
  graph.on('selection:changed', (args) => {
    const { selected, removed } = args
    selectedCount.value = selected.length
    
    if (selected.length === 1) {
      selectedItem.value = selected[0].toJSON()
    } else {
      selectedItem.value = null
    }
  })

  // 图变化
  graph.on('graph:changed', () => {
    nodeCount.value = graph.getNodes().length
    edgeCount.value = graph.getEdges().length
    emit('change', graph.toJSON())
  })

  // 历史记录变化
  graph.on('history:change', () => {
    canUndo.value = graph.canUndo()
    canRedo.value = graph.canRedo()
  })

  // 缩放变化
  graph.on('scale', ({ sx }) => {
    zoomLevel.value = sx
  })
}

// 切换分类展开状态
const toggleCategory = (categoryName) => {
  const category = nodeCategories.value.find(c => c.name === categoryName)
  if (category) {
    category.expanded = !category.expanded
  }
}

// 拖拽开始
const onDragStart = (event, node) => {
  event.dataTransfer.setData('application/json', JSON.stringify(node))
}

// 更新属性
const updateProperty = () => {
  if (!selectedItem.value || !graph) return
  
  const cell = graph.getCellById(selectedItem.value.id)
  if (cell) {
    if (selectedItem.value.label !== undefined) {
      cell.setAttrs({ text: { text: selectedItem.value.label } })
    }
    emit('change', graph.toJSON())
  }
}

// 工具栏方法
const undo = () => graph?.undo()
const redo = () => graph?.redo()
const clearCanvas = () => graph?.clearCells()
const saveFlow = () => emit('save', graph?.toJSON())
const exportFlow = () => emit('export', graph?.toJSON())
const zoomIn = () => graph?.zoom(0.1)
const zoomOut = () => graph?.zoom(-0.1)
const fitView = () => graph?.zoomToFit()

const toggleGrid = (visible) => {
  if (graph) {
    graph.drawGrid({ visible })
  }
}

// 生命周期
onMounted(() => {
  initEditor()
})

onUnmounted(() => {
  if (graph) {
    graph.dispose()
  }
})
</script>

<style scoped>
.flow-editor {
  display: flex;
  flex-direction: column;
  height: 800px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.tool-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-display {
  font-size: 14px;
  color: #6b7280;
  min-width: 50px;
  text-align: center;
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.stencil-panel {
  width: 200px;
  background: #f8f9fa;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
}

.panel-title {
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
}

.node-categories {
  padding: 8px;
}

.category {
  margin-bottom: 8px;
}

.category-title {
  display: flex;
  align-items: center;
  padding: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  border-radius: 4px;
}

.category-title:hover {
  background: #e5e7eb;
}

.category-nodes {
  padding: 4px 0 4px 20px;
}

.stencil-node {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  cursor: grab;
  transition: background-color 0.2s;
}

.stencil-node:hover {
  background: #e5e7eb;
}

.stencil-node:active {
  cursor: grabbing;
}

.node-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
  margin-right: 8px;
}

.node-label {
  font-size: 12px;
  color: #374151;
}

.canvas-panel {
  flex: 1;
  position: relative;
}

.graph-container {
  width: 100%;
  height: 100%;
}

.property-panel {
  width: 250px;
  background: #f8f9fa;
  border-left: 1px solid #e5e7eb;
  overflow-y: auto;
}

.property-content {
  padding: 16px;
}

.no-selection {
  padding: 40px 20px;
  text-align: center;
}

.editor-statusbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
  font-size: 12px;
  color: #6b7280;
}

.status-info {
  display: flex;
  gap: 16px;
}

@media (max-width: 1024px) {
  .stencil-panel,
  .property-panel {
    width: 180px;
  }
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }
  
  .stencil-panel,
  .property-panel {
    width: 100%;
    height: 200px;
  }
}
</style> 