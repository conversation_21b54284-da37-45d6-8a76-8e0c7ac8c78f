<template>
  <div class="molecular-viewer">
    <div class="viewer-toolbar">
      <div class="view-controls">
        <el-button-group>
          <el-button size="small" @click="resetView">复位视角</el-button>
          <el-button size="small" @click="toggleRotation">
            {{ isRotating ? '停止旋转' : '自动旋转' }}
          </el-button>
          <el-button size="small" @click="toggleFullscreen">全屏</el-button>
        </el-button-group>
      </div>
      <div class="display-options">
        <el-select v-model="renderMode" size="small" style="width: 120px">
          <el-option label="球棍模型" value="ball-stick" />
          <el-option label="空间填充" value="spacefill" />
          <el-option label="线框模型" value="wireframe" />
          <el-option label="表面模型" value="surface" />
        </el-select>
        <el-select v-model="colorScheme" size="small" style="width: 100px">
          <el-option label="元素色" value="element" />
          <el-option label="残基色" value="residue" />
          <el-option label="链色" value="chain" />
        </el-select>
      </div>
    </div>
    <div ref="viewerContainer" class="viewer-container"></div>
    <div class="viewer-info">
      <div class="molecule-info">
        <span>{{ moleculeData.name || '未命名分子' }}</span>
        <span>原子数: {{ moleculeData.atomCount || 0 }}</span>
        <span>分子量: {{ moleculeData.molecularWeight || 'N/A' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as THREE from 'three'

const props = defineProps({
  moleculeData: {
    type: Object,
    default: () => ({})
  },
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 600
  }
})

const emit = defineEmits(['atom-click', 'bond-click', 'selection-change'])

// 响应式数据
const viewerContainer = ref(null)
const isRotating = ref(false)
const renderMode = ref('ball-stick')
const colorScheme = ref('element')

// Three.js 对象
let scene, camera, renderer, controls
let moleculeGroup = new THREE.Group()
let animationId = null

// 初始化3D场景
const initScene = () => {
  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf8f9fa)

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    75,
    props.width / props.height,
    0.1,
    1000
  )
  camera.position.set(0, 0, 50)

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true })
  renderer.setSize(props.width, props.height)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(50, 50, 50)
  directionalLight.castShadow = true
  scene.add(directionalLight)

  // 将渲染器添加到容器
  viewerContainer.value.appendChild(renderer.domElement)

  // 添加分子组到场景
  scene.add(moleculeGroup)
}

// 创建原子
const createAtom = (element, position, radius = 1) => {
  const colors = {
    H: 0xffffff,  // 氢 - 白色
    C: 0x909090,  // 碳 - 灰色
    N: 0x3050f8,  // 氮 - 蓝色
    O: 0xff0d0d,  // 氧 - 红色
    S: 0xffff30,  // 硫 - 黄色
    P: 0xff8000   // 磷 - 橙色
  }

  const geometry = new THREE.SphereGeometry(radius, 32, 32)
  const material = new THREE.MeshPhongMaterial({
    color: colors[element] || 0x808080
  })

  const sphere = new THREE.Mesh(geometry, material)
  sphere.position.set(position.x, position.y, position.z)
  sphere.castShadow = true
  sphere.receiveShadow = true
  sphere.userData = { type: 'atom', element }

  return sphere
}

// 创建化学键
const createBond = (start, end, bondType = 'single') => {
  const direction = new THREE.Vector3().subVectors(end, start)
  const length = direction.length()
  
  const geometry = new THREE.CylinderGeometry(0.1, 0.1, length, 8)
  const material = new THREE.MeshPhongMaterial({ color: 0x666666 })
  
  const cylinder = new THREE.Mesh(geometry, material)
  cylinder.position.copy(start).add(direction.clone().multiplyScalar(0.5))
  cylinder.lookAt(end)
  cylinder.rotateX(Math.PI / 2)
  cylinder.userData = { type: 'bond', bondType }

  return cylinder
}

// 加载分子数据
const loadMolecule = (data) => {
  // 清空现有分子
  moleculeGroup.clear()

  if (!data.atoms) return

  // 创建原子
  data.atoms.forEach(atom => {
    const atomMesh = createAtom(
      atom.element,
      { x: atom.x, y: atom.y, z: atom.z },
      atom.radius || 1
    )
    moleculeGroup.add(atomMesh)
  })

  // 创建化学键
  if (data.bonds) {
    data.bonds.forEach(bond => {
      const startAtom = data.atoms[bond.start]
      const endAtom = data.atoms[bond.end]
      
      const bondMesh = createBond(
        new THREE.Vector3(startAtom.x, startAtom.y, startAtom.z),
        new THREE.Vector3(endAtom.x, endAtom.y, endAtom.z),
        bond.type
      )
      moleculeGroup.add(bondMesh)
    })
  }

  // 居中分子
  const box = new THREE.Box3().setFromObject(moleculeGroup)
  const center = box.getCenter(new THREE.Vector3())
  moleculeGroup.position.sub(center)
}

// 动画循环
const animate = () => {
  animationId = requestAnimationFrame(animate)

  if (isRotating.value) {
    moleculeGroup.rotation.y += 0.01
  }

  renderer.render(scene, camera)
}

// 控制方法
const resetView = () => {
  camera.position.set(0, 0, 50)
  camera.lookAt(0, 0, 0)
}

const toggleRotation = () => {
  isRotating.value = !isRotating.value
}

const toggleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    viewerContainer.value.requestFullscreen()
  }
}

// 监听属性变化
watch(() => props.moleculeData, (newData) => {
  if (newData) {
    loadMolecule(newData)
  }
}, { deep: true })

watch(renderMode, (newMode) => {
  // 根据渲染模式更新显示
  console.log('切换到渲染模式:', newMode)
})

watch(colorScheme, (newScheme) => {
  // 根据配色方案更新颜色
  console.log('切换到配色方案:', newScheme)
})

// 生命周期
onMounted(() => {
  initScene()
  animate()
  
  if (props.moleculeData) {
    loadMolecule(props.moleculeData)
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  if (renderer) {
    renderer.dispose()
  }
})
</script>

<style scoped>
.molecular-viewer {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.viewer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.display-options {
  display: flex;
  gap: 8px;
}

.viewer-container {
  position: relative;
  width: 100%;
  height: 600px;
  overflow: hidden;
}

.viewer-info {
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
}

.molecule-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #6b7280;
}

.molecule-info span:first-child {
  font-weight: 600;
  color: #1f2937;
}
</style> 