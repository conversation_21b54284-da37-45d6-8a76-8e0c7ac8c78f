<template>
  <div class="network-graph">
    <div class="graph-toolbar">
      <div class="layout-controls">
        <el-select v-model="layoutType" size="small" style="width: 120px" @change="updateLayout">
          <el-option label="力导向" value="force" />
          <el-option label="层次布局" value="hierarchy" />
          <el-option label="径向布局" value="radial" />
          <el-option label="网格布局" value="grid" />
          <el-option label="圆形布局" value="circular" />
        </el-select>
        <el-button size="small" @click="resetZoom">适应画布</el-button>
        <el-button size="small" @click="exportGraph">导出图片</el-button>
      </div>
      <div class="filter-controls">
        <el-input
          v-model="searchText"
          size="small"
          placeholder="搜索节点..."
          style="width: 150px"
          @input="onSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-switch
          v-model="showLabels"
          size="small"
          active-text="显示标签"
          @change="toggleLabels"
        />
      </div>
    </div>
    <div ref="graphContainer" class="graph-container"></div>
    <div class="graph-info">
      <div class="network-stats">
        <span>节点: {{ nodeCount }}</span>
        <span>边: {{ edgeCount }}</span>
        <span>选中: {{ selectedNodes.length }}</span>
      </div>
      <div class="legend" v-if="showLegend">
        <div class="legend-item" v-for="type in nodeTypes" :key="type.name">
          <div class="legend-color" :style="{ backgroundColor: type.color }"></div>
          <span>{{ type.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { Graph } from '@antv/g6'
import { Search } from '@element-plus/icons-vue'

// G6 版本检测和兼容性处理
const isG6V5 = typeof Graph.registerNode === 'undefined'

// 安全的图形操作包装器
const safeGraphOperation = (operation, fallback = null) => {
  try {
    return operation()
  } catch (error) {
    console.warn('Graph operation failed:', error)
    return fallback
  }
}

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ nodes: [], edges: [] })
  },
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 600
  },
  nodeTypes: {
    type: Array,
    default: () => [
      { name: '蛋白质', color: '#4F46E5', shape: 'circle' },
      { name: '基因', color: '#059669', shape: 'rect' },
      { name: '化合物', color: '#DC2626', shape: 'diamond' },
      { name: '通路', color: '#7C3AED', shape: 'ellipse' }
    ]
  }
})

const emit = defineEmits(['node-click', 'edge-click', 'selection-change'])

// 响应式数据
const graphContainer = ref(null)
const layoutType = ref('force')
const searchText = ref('')
const showLabels = ref(true)
const showLegend = ref(true)
const selectedNodes = ref([])

// G6 图实例
let graph = null

// 计算属性
const nodeCount = computed(() => props.data.nodes?.length || 0)
const edgeCount = computed(() => props.data.edges?.length || 0)

// 初始化图
const initGraph = () => {
  const container = graphContainer.value
  if (!container) return

  // 创建完整的G6图形配置
  const graphConfig = {
    container,
    width: props.width,
    height: props.height,
    fitView: true,
    fitViewPadding: 50,
    // 直接在配置中设置默认布局
    layout: {
      type: 'force',
      preventOverlap: true,
      nodeSize: 30,
      linkDistance: 100,
      nodeStrength: -300,
      edgeStrength: 0.8,
      alpha: 0.8,
      alphaDecay: 0.028
    },
    defaultNode: {
      size: 30,
      style: {
        fill: '#4F46E5',
        stroke: '#ffffff',
        lineWidth: 2
      },
      labelCfg: {
        style: {
          fill: '#333',
          fontSize: 12
        },
        position: 'bottom',
        offset: 5
      }
    },
    defaultEdge: {
      style: {
        stroke: '#e5e7eb',
        lineWidth: 1,
        endArrow: {
          path: 'M 0,0 L 8,4 L 8,-4 Z',
          fill: '#e5e7eb'
        }
      },
      labelCfg: {
        autoRotate: true,
        style: {
          fill: '#666',
          fontSize: 10
        }
      }
    },
    modes: {
      default: ['drag-canvas', 'zoom-canvas', 'drag-node']
    },
    nodeStateStyles: {
      hover: {
        fill: '#f59e0b',
        stroke: '#d97706',
        lineWidth: 3
      },
      selected: {
        fill: '#ef4444',
        stroke: '#dc2626',
        lineWidth: 3
      },
      highlight: {
        fill: '#10b981',
        stroke: '#059669',
        lineWidth: 3
      }
    },
    edgeStateStyles: {
      hover: {
        stroke: '#f59e0b',
        lineWidth: 2
      },
      selected: {
        stroke: '#ef4444',
        lineWidth: 3
      }
    }
  }

  console.log('Creating G6 graph with config:', graphConfig)
  graph = new Graph(graphConfig)

  // 注册自定义节点（仅在G6 v4中支持）
  if (!isG6V5) {
    registerCustomNodes()
  }

  // 绑定事件
  bindEvents()

  // 图形已在配置中设置了布局，无需额外设置
  console.log('Graph initialized with built-in layout')
  
  // 如果需要，可以在这里进行额外的初始化
  setTimeout(() => {
    if (graph && graph.fitView) {
      graph.fitView()
    }
  }, 100)
}

// 注册自定义节点类型
const registerCustomNodes = () => {
  // G6 v5 兼容性检查
  if (isG6V5) {
    console.warn('G6 v5 detected, custom nodes registration skipped')
    return
  }
  
  // 蛋白质节点
  Graph.registerNode('protein', {
    draw(cfg, group) {
      const { size = 30, label } = cfg
      const radius = size / 2

      // 主圆形
      const circle = group.addShape('circle', {
        attrs: {
          x: 0,
          y: 0,
          r: radius,
          fill: '#4F46E5',
          stroke: '#ffffff',
          lineWidth: 2
        },
        name: 'main-circle'
      })

      // 内部标记
      group.addShape('text', {
        attrs: {
          x: 0,
          y: 0,
          text: 'P',
          fontSize: 12,
          fontWeight: 'bold',
          fill: '#ffffff',
          textAlign: 'center',
          textBaseline: 'middle'
        },
        name: 'type-label'
      })

      // 标签
      if (label) {
        group.addShape('text', {
          attrs: {
            x: 0,
            y: radius + 15,
            text: label,
            fontSize: 11,
            fill: '#333',
            textAlign: 'center',
            textBaseline: 'top'
          },
          name: 'node-label'
        })
      }

      return circle
    }
  })

  // 基因节点
  Graph.registerNode('gene', {
    draw(cfg, group) {
      const { size = 30, label } = cfg
      const width = size
      const height = size * 0.6

      // 矩形
      const rect = group.addShape('rect', {
        attrs: {
          x: -width / 2,
          y: -height / 2,
          width,
          height,
          fill: '#059669',
          stroke: '#ffffff',
          lineWidth: 2,
          radius: 4
        },
        name: 'main-rect'
      })

      // 内部标记
      group.addShape('text', {
        attrs: {
          x: 0,
          y: 0,
          text: 'G',
          fontSize: 12,
          fontWeight: 'bold',
          fill: '#ffffff',
          textAlign: 'center',
          textBaseline: 'middle'
        },
        name: 'type-label'
      })

      // 标签
      if (label) {
        group.addShape('text', {
          attrs: {
            x: 0,
            y: height / 2 + 15,
            text: label,
            fontSize: 11,
            fill: '#333',
            textAlign: 'center',
            textBaseline: 'top'
          },
          name: 'node-label'
        })
      }

      return rect
    }
  })
}

// 绑定事件
const bindEvents = () => {
  try {
    // 节点点击
    graph.on('node:click', (e) => {
      const { item } = e
      const model = item.getModel()
      emit('node-click', model)
    })

    // 边点击
    graph.on('edge:click', (e) => {
      const { item } = e
      const model = item.getModel()
      emit('edge-click', model)
    })

    // 节点选中状态变化（G6 v5兼容性处理）
    if (!isG6V5) {
      graph.on('nodeselectchange', (e) => {
        selectedNodes.value = e.selectedItems.nodes.map(node => node.getModel())
        emit('selection-change', selectedNodes.value)
      })
    }
  } catch (error) {
    console.warn('Event binding failed:', error)
  }

  // 节点悬停（G6 v5兼容性处理）
  graph.on('node:mouseenter', (e) => {
    try {
      const { item } = e
      if (graph.setItemState) {
        graph.setItemState(item, 'hover', true)
        
        // 高亮相邻节点和边
        if (item.getEdges) {
          const edges = item.getEdges()
          edges.forEach(edge => {
            graph.setItemState(edge, 'hover', true)
            const otherNode = edge.getSource() === item ? edge.getTarget() : edge.getSource()
            graph.setItemState(otherNode, 'highlight', true)
          })
        }
      }
    } catch (error) {
      console.warn('Node mouseenter handler failed:', error)
    }
  })

  graph.on('node:mouseleave', (e) => {
    try {
      const { item } = e
      if (graph.setItemState) {
        graph.setItemState(item, 'hover', false)
        
        // 取消高亮
        if (item.getEdges) {
          const edges = item.getEdges()
          edges.forEach(edge => {
            graph.setItemState(edge, 'hover', false)
            const otherNode = edge.getSource() === item ? edge.getTarget() : edge.getSource()
            graph.setItemState(otherNode, 'highlight', false)
          })
        }
      }
    } catch (error) {
      console.warn('Node mouseleave handler failed:', error)
    }
  })
}

// 设置布局
const setLayout = () => {
  // 检查图形是否已准备好
  if (!graph) {
    console.warn('Graph not initialized, skipping layout setting')
    return
  }

  let layout
  
  switch (layoutType.value) {
    case 'force':
      layout = {
        type: 'force',
        preventOverlap: true,
        nodeSize: 30,
        linkDistance: 100,
        nodeStrength: -300,
        edgeStrength: 0.8,
        alpha: 0.8,
        alphaDecay: 0.028,
        // 添加postLayout回调来避免错误
        onLayoutEnd: () => {
          console.log('Force layout completed')
        }
      }
      break
    case 'hierarchy':
      layout = {
        type: 'dagre',
        direction: 'TB',
        align: 'UL',
        nodesep: 50,
        ranksep: 80
      }
      break
    case 'radial':
      layout = {
        type: 'radial',
        center: [props.width / 2, props.height / 2],
        linkDistance: 100,
        preventOverlap: true,
        nodeSize: 30
      }
      break
    case 'grid':
      layout = {
        type: 'grid',
        preventOverlap: true,
        nodeSize: 30
      }
      break
    case 'circular':
      layout = {
        type: 'circular',
        radius: Math.min(props.width, props.height) / 3,
        startAngle: 0,
        endAngle: Math.PI * 2
      }
      break
    default:
      layout = { 
        type: 'force',
        preventOverlap: true,
        onLayoutEnd: () => {
          console.log('Default layout completed')
        }
      }
  }

  safeGraphOperation(() => {
    if (graph && layout) {
      console.log('Setting layout:', layout.type, layout)
      
      // 尝试不同的布局设置方法
      if (typeof graph.updateLayout === 'function') {
        graph.updateLayout(layout)
      } else if (typeof graph.layout === 'function') {
        graph.layout(layout)
      } else if (typeof graph.setLayout === 'function') {
        graph.setLayout(layout)
      } else {
        // 如果没有布局方法，尝试在图形配置中设置布局
        console.warn('No layout method available, trying to recreate graph with layout')
      }
    }
  })
}

// 更新布局
const updateLayout = () => {
  if (!graph) return
  
  // 获取新的布局配置
  const newLayoutConfig = getLayoutConfig(layoutType.value)
  
  safeGraphOperation(() => {
    if (graph && newLayoutConfig) {
      console.log('Updating layout to:', layoutType.value, newLayoutConfig)
      
      if (typeof graph.updateLayout === 'function') {
        graph.updateLayout(newLayoutConfig)
      } else if (typeof graph.layout === 'function') {
        graph.layout(newLayoutConfig)
      } else {
        console.warn('Layout update not supported')
      }
      
      // 更新后重新适应视图
      setTimeout(() => {
        if (graph && graph.fitView) {
          graph.fitView()
        }
      }, 100)
    }
  })
}

// 获取布局配置的辅助函数
const getLayoutConfig = (type) => {
  switch (type) {
    case 'force':
      return {
        type: 'force',
        preventOverlap: true,
        nodeSize: 30,
        linkDistance: 100,
        nodeStrength: -300,
        edgeStrength: 0.8,
        alpha: 0.8,
        alphaDecay: 0.028
      }
    case 'hierarchy':
      return {
        type: 'dagre',
        direction: 'TB',
        align: 'UL',
        nodesep: 50,
        ranksep: 80
      }
    case 'radial':
      return {
        type: 'radial',
        center: [props.width / 2, props.height / 2],
        linkDistance: 100,
        preventOverlap: true,
        nodeSize: 30
      }
    case 'grid':
      return {
        type: 'grid',
        preventOverlap: true,
        nodeSize: 30
      }
    case 'circular':
      return {
        type: 'circular',
        radius: Math.min(props.width, props.height) / 3,
        startAngle: 0,
        endAngle: Math.PI * 2
      }
    default:
      return {
        type: 'force',
        preventOverlap: true
      }
  }
}

// 重置缩放
const resetZoom = () => {
  safeGraphOperation(() => {
    if (graph && graph.fitView) {
      graph.fitView()
    }
  })
}

// 导出图片
const exportGraph = () => {
  try {
    if (graph.downloadFullImage) {
      graph.downloadFullImage('network-graph', 'image/png', {
        backgroundColor: '#ffffff'
      })
    } else {
      console.warn('Export feature not available in current G6 version')
    }
  } catch (error) {
    console.warn('Export failed:', error)
  }
}

// 搜索节点
const onSearch = (value) => {
  safeGraphOperation(() => {
    if (!graph || !graph.getNodes) return
    
    const nodes = graph.getNodes()
    nodes.forEach(node => {
      const model = node.getModel && node.getModel()
      if (model) {
        const isMatch = model.label?.toLowerCase().includes(value.toLowerCase())
        
        if (value && !isMatch) {
          if (node.hide) node.hide()
        } else {
          if (node.show) node.show()
        }
      }
    })
  })
}

// 切换标签显示
const toggleLabels = (show) => {
  safeGraphOperation(() => {
    if (!graph || !graph.getNodes) return
    
    const nodes = graph.getNodes()
    nodes.forEach(node => {
      try {
        const group = node.get && node.get('group')
        if (group && group.find) {
          const labelShape = group.find(shape => shape.get && shape.get('name') === 'node-label')
          if (labelShape && labelShape.attr) {
            labelShape.attr('opacity', show ? 1 : 0)
          }
        }
      } catch (error) {
        // 忽略个别节点的标签切换错误
      }
    })
    
    if (graph.refresh) {
      graph.refresh()
    }
  })
}

// 加载数据
const loadData = (data) => {
  if (!graph || !data) return

  // 处理节点数据
  const processedNodes = data.nodes.map(node => ({
    ...node,
    type: getNodeShape(node.nodeType),
    style: {
      fill: getNodeColor(node.nodeType),
      ...node.style
    }
  }))

  // 处理边数据
  const processedEdges = data.edges.map(edge => ({
    ...edge,
    style: {
      stroke: getEdgeColor(edge.edgeType),
      ...edge.style
    }
  }))

  safeGraphOperation(() => {
    if (graph) {
      if (graph.changeData) {
        graph.changeData({
          nodes: processedNodes,
          edges: processedEdges
        })
      } else if (graph.data) {
        graph.data({
          nodes: processedNodes,
          edges: processedEdges
        })
        if (graph.render) {
          graph.render()
        }
      }
    }
  })
}

// 获取节点形状（兼容G6 v5）
const getNodeShape = (nodeType) => {
  // 映射自定义类型到内置形状
  const shapeMap = {
    protein: 'circle',
    gene: 'rect', 
    compound: 'diamond',
    pathway: 'ellipse'
  }
  return shapeMap[nodeType] || 'circle'
}

// 获取节点颜色
const getNodeColor = (nodeType) => {
  const typeMap = {
    protein: '#4F46E5',
    gene: '#059669',
    compound: '#DC2626',
    pathway: '#7C3AED'
  }
  return typeMap[nodeType] || '#6B7280'
}

// 获取边颜色
const getEdgeColor = (edgeType) => {
  const typeMap = {
    interaction: '#6B7280',
    regulation: '#F59E0B',
    binding: '#EF4444'
  }
  return typeMap[edgeType] || '#E5E7EB'
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData && graph) {
    loadData(newData)
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  try {
    console.log('Mounting NetworkGraph component')
    initGraph()
    
    // 等待图形完全初始化后再加载数据
    setTimeout(() => {
      if (props.data && props.data.nodes && props.data.nodes.length > 0) {
        console.log('Loading initial data:', props.data)
        loadData(props.data)
      } else {
        console.log('No initial data provided, creating empty graph')
        // 即使没有数据，也要确保图形可以正常显示
        safeGraphOperation(() => {
          if (graph) {
            graph.data({ nodes: [], edges: [] })
            if (graph.render) {
              graph.render()
            }
          }
        })
      }
    }, 150)
  } catch (error) {
    console.error('Failed to initialize NetworkGraph:', error)
  }
})

onUnmounted(() => {
  if (graph) {
    graph.destroy()
  }
})
</script>

<style scoped>
.network-graph {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.graph-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
  gap: 12px;
}

.layout-controls,
.filter-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.graph-container {
  width: 100%;
  height: 600px;
  overflow: hidden;
}

.graph-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
}

.network-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #6b7280;
}

.network-stats span:first-child {
  font-weight: 600;
  color: #1f2937;
}

.legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

@media (max-width: 768px) {
  .graph-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .layout-controls,
  .filter-controls {
    justify-content: center;
  }
}
</style> 