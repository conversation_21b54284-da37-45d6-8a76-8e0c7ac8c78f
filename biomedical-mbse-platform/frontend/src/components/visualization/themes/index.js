// 可视化主题配置

/**
 * 基础颜色配置
 */
export const colors = {
  // 主色调
  primary: '#4F46E5',
  secondary: '#7C3AED',
  accent: '#06B6D4',
  
  // 状态颜色
  success: '#10B981',
  warning: '#F59E0B',
  danger: '#EF4444',
  info: '#3B82F6',
  
  // 灰度
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827'
  },
  
  // 生物医学专用色彩
  biomedical: {
    protein: '#4F46E5',
    gene: '#059669',
    compound: '#DC2626',
    pathway: '#7C3AED',
    enzyme: '#F59E0B',
    reaction: '#6B7280',
    metabolite: '#EC4899',
    organism: '#10B981',
    tissue: '#8B5CF6',
    cell: '#06B6D4',
    molecule: '#F97316',
    drug: '#DB2777'
  }
}

/**
 * 生物医学可视化主题
 */
export const biomedicalTheme = {
  name: 'biomedical',
  colors,
  
  // 节点样式
  node: {
    default: {
      size: 30,
      strokeWidth: 2,
      stroke: '#ffffff',
      opacity: 0.9
    },
    protein: {
      fill: colors.biomedical.protein,
      shape: 'circle',
      size: 35
    },
    gene: {
      fill: colors.biomedical.gene,
      shape: 'rect',
      size: 32,
      radius: 4
    },
    compound: {
      fill: colors.biomedical.compound,
      shape: 'diamond',
      size: 28
    },
    pathway: {
      fill: colors.biomedical.pathway,
      shape: 'ellipse',
      size: 40
    },
    enzyme: {
      fill: colors.biomedical.enzyme,
      shape: 'hexagon',
      size: 33
    },
    metabolite: {
      fill: colors.biomedical.metabolite,
      shape: 'triangle',
      size: 30
    }
  },
  
  // 边样式
  edge: {
    default: {
      stroke: colors.gray[400],
      strokeWidth: 1,
      opacity: 0.7
    },
    interaction: {
      stroke: colors.gray[500],
      strokeWidth: 2,
      style: 'solid'
    },
    regulation: {
      stroke: colors.warning,
      strokeWidth: 2,
      style: 'dashed'
    },
    binding: {
      stroke: colors.danger,
      strokeWidth: 3,
      style: 'solid'
    },
    pathway: {
      stroke: colors.secondary,
      strokeWidth: 2,
      style: 'dotted'
    },
    activation: {
      stroke: colors.success,
      strokeWidth: 2,
      endArrow: 'triangle',
      style: 'solid'
    },
    inhibition: {
      stroke: colors.danger,
      strokeWidth: 2,
      endArrow: 'tee',
      style: 'solid'
    }
  },
  
  // 字体配置
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: {
      small: 10,
      normal: 12,
      large: 14,
      xlarge: 16
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      bold: 600
    }
  },
  
  // 布局配置
  layout: {
    padding: 20,
    spacing: 50,
    grid: {
      size: 20,
      color: colors.gray[200],
      opacity: 0.5
    }
  }
}

/**
 * 暗色主题
 */
export const darkTheme = {
  name: 'dark',
  colors: {
    ...colors,
    background: colors.gray[900],
    surface: colors.gray[800],
    text: colors.gray[100],
    textSecondary: colors.gray[400]
  },
  
  node: {
    default: {
      size: 30,
      strokeWidth: 2,
      stroke: colors.gray[700],
      opacity: 0.9
    },
    protein: {
      fill: '#6366F1',
      shape: 'circle'
    },
    gene: {
      fill: '#10B981',
      shape: 'rect'
    },
    compound: {
      fill: '#F87171',
      shape: 'diamond'
    }
  },
  
  edge: {
    default: {
      stroke: colors.gray[600],
      strokeWidth: 1,
      opacity: 0.8
    }
  },
  
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    color: colors.gray[100]
  },
  
  layout: {
    background: colors.gray[900],
    grid: {
      color: colors.gray[700],
      opacity: 0.3
    }
  }
}

/**
 * 简洁主题
 */
export const minimalTheme = {
  name: 'minimal',
  colors,
  
  node: {
    default: {
      size: 25,
      strokeWidth: 1,
      stroke: colors.gray[300],
      fill: colors.gray[50],
      opacity: 1
    }
  },
  
  edge: {
    default: {
      stroke: colors.gray[300],
      strokeWidth: 1,
      opacity: 0.6
    }
  },
  
  typography: {
    fontFamily: 'Helvetica, Arial, sans-serif',
    fontSize: {
      small: 9,
      normal: 11,
      large: 13
    },
    color: colors.gray[700]
  },
  
  layout: {
    padding: 30,
    spacing: 60,
    grid: {
      color: colors.gray[200],
      opacity: 0.3
    }
  }
}

/**
 * 科学期刊主题
 */
export const scientificTheme = {
  name: 'scientific',
  colors,
  
  node: {
    default: {
      size: 28,
      strokeWidth: 1.5,
      stroke: colors.gray[800],
      opacity: 1
    },
    protein: {
      fill: '#000080',  // 深蓝
      shape: 'circle'
    },
    gene: {
      fill: '#008000',  // 绿色
      shape: 'rect'
    },
    compound: {
      fill: '#800000',  // 深红
      shape: 'diamond'
    }
  },
  
  edge: {
    default: {
      stroke: colors.gray[800],
      strokeWidth: 1,
      opacity: 0.8
    }
  },
  
  typography: {
    fontFamily: 'Times, "Times New Roman", serif',
    fontSize: {
      small: 10,
      normal: 12,
      large: 14
    },
    color: colors.gray[900]
  },
  
  layout: {
    background: '#ffffff',
    padding: 25,
    grid: {
      color: colors.gray[300],
      opacity: 0.4
    }
  }
}

/**
 * 高对比度主题（用于可访问性）
 */
export const highContrastTheme = {
  name: 'high-contrast',
  colors,
  
  node: {
    default: {
      size: 32,
      strokeWidth: 3,
      stroke: '#000000',
      opacity: 1
    },
    protein: {
      fill: '#0000FF',
      shape: 'circle'
    },
    gene: {
      fill: '#00FF00',
      shape: 'rect'
    },
    compound: {
      fill: '#FF0000',
      shape: 'diamond'
    }
  },
  
  edge: {
    default: {
      stroke: '#000000',
      strokeWidth: 2,
      opacity: 1
    }
  },
  
  typography: {
    fontFamily: 'Arial, sans-serif',
    fontSize: {
      small: 12,
      normal: 14,
      large: 16
    },
    fontWeight: {
      normal: 600,
      bold: 700
    },
    color: '#000000'
  },
  
  layout: {
    background: '#ffffff',
    padding: 20,
    grid: {
      color: '#000000',
      opacity: 0.6
    }
  }
}

/**
 * 预定义主题集合
 */
export const themes = {
  biomedical: biomedicalTheme,
  dark: darkTheme,
  minimal: minimalTheme,
  scientific: scientificTheme,
  'high-contrast': highContrastTheme
}

/**
 * 主题工具函数
 */
export const ThemeUtils = {
  // 获取主题
  getTheme(themeName) {
    return themes[themeName] || biomedicalTheme
  },
  
  // 应用主题到图形配置
  applyTheme(config, themeName) {
    const theme = this.getTheme(themeName)
    
    return {
      ...config,
      defaultNode: {
        ...config.defaultNode,
        ...theme.node.default
      },
      defaultEdge: {
        ...config.defaultEdge,
        ...theme.edge.default
      },
      background: theme.layout?.background || '#ffffff',
      grid: {
        ...config.grid,
        ...theme.layout?.grid
      }
    }
  },
  
  // 获取节点样式
  getNodeStyle(nodeType, themeName) {
    const theme = this.getTheme(themeName)
    return {
      ...theme.node.default,
      ...theme.node[nodeType]
    }
  },
  
  // 获取边样式
  getEdgeStyle(edgeType, themeName) {
    const theme = this.getTheme(themeName)
    return {
      ...theme.edge.default,
      ...theme.edge[edgeType]
    }
  },
  
  // 生成颜色渐变
  generateGradient(startColor, endColor, steps) {
    const colors = []
    const startRGB = this.hexToRgb(startColor)
    const endRGB = this.hexToRgb(endColor)
    
    for (let i = 0; i < steps; i++) {
      const ratio = i / (steps - 1)
      const r = Math.round(startRGB.r + (endRGB.r - startRGB.r) * ratio)
      const g = Math.round(startRGB.g + (endRGB.g - startRGB.g) * ratio)
      const b = Math.round(startRGB.b + (endRGB.b - startRGB.b) * ratio)
      colors.push(this.rgbToHex(r, g, b))
    }
    
    return colors
  },
  
  // 十六进制转RGB
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  },
  
  // RGB转十六进制
  rgbToHex(r, g, b) {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
  },
  
  // 判断颜色亮度
  isLightColor(hex) {
    const rgb = this.hexToRgb(hex)
    if (!rgb) return true
    
    // 使用相对亮度计算
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000
    return brightness > 128
  },
  
  // 获取对比色
  getContrastColor(backgroundColor) {
    return this.isLightColor(backgroundColor) ? '#000000' : '#ffffff'
  },
  
  // 创建自定义主题
  createCustomTheme(name, baseTheme, overrides) {
    const base = this.getTheme(baseTheme)
    return {
      name,
      ...base,
      ...overrides,
      node: {
        ...base.node,
        ...overrides.node
      },
      edge: {
        ...base.edge,
        ...overrides.edge
      }
    }
  }
}

// 默认导出
export default {
  themes,
  colors,
  ThemeUtils
} 