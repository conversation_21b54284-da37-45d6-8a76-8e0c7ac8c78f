// 可视化工具类库

/**
 * 颜色相关工具
 */
export const ColorUtils = {
  // 生成颜色调色板
  generatePalette(count, hue = 200, saturation = 70, lightness = 50) {
    const colors = []
    for (let i = 0; i < count; i++) {
      const h = (hue + (i * 360 / count)) % 360
      colors.push(`hsl(${h}, ${saturation}%, ${lightness}%)`)
    }
    return colors
  },

  // 根据数值生成颜色
  valueToColor(value, min, max, colorScale = ['#3b82f6', '#ef4444']) {
    const normalized = (value - min) / (max - min)
    const r1 = parseInt(colorScale[0].slice(1, 3), 16)
    const g1 = parseInt(colorScale[0].slice(3, 5), 16)
    const b1 = parseInt(colorScale[0].slice(5, 7), 16)
    const r2 = parseInt(colorScale[1].slice(1, 3), 16)
    const g2 = parseInt(colorScale[1].slice(3, 5), 16)
    const b2 = parseInt(colorScale[1].slice(5, 7), 16)
    
    const r = Math.round(r1 + (r2 - r1) * normalized)
    const g = Math.round(g1 + (g2 - g1) * normalized)
    const b = Math.round(b1 + (b2 - b1) * normalized)
    
    return `rgb(${r}, ${g}, ${b})`
  },

  // 获取生物医学主题颜色
  getBiomedicalColors() {
    return {
      protein: '#4F46E5',
      gene: '#059669',
      compound: '#DC2626',
      pathway: '#7C3AED',
      enzyme: '#F59E0B',
      reaction: '#6B7280',
      metabolite: '#EC4899',
      organism: '#10B981'
    }
  }
}

/**
 * 数据转换工具
 */
export const DataUtils = {
  // 网络图数据格式化
  formatNetworkData(rawData) {
    const nodes = []
    const edges = []
    const nodeMap = new Map()

    // 处理节点
    if (rawData.nodes) {
      rawData.nodes.forEach((node, index) => {
        const formattedNode = {
          id: node.id || `node_${index}`,
          label: node.name || node.label || `节点${index}`,
          type: node.type || 'default',
          size: node.size || 30,
          x: node.x,
          y: node.y,
          style: {
            fill: this.getNodeColor(node.type),
            stroke: '#ffffff',
            lineWidth: 2,
            ...node.style
          },
          ...node
        }
        nodes.push(formattedNode)
        nodeMap.set(formattedNode.id, formattedNode)
      })
    }

    // 处理边
    if (rawData.edges) {
      rawData.edges.forEach((edge, index) => {
        const formattedEdge = {
          id: edge.id || `edge_${index}`,
          source: edge.source || edge.from,
          target: edge.target || edge.to,
          label: edge.label,
          type: edge.type || 'default',
          style: {
            stroke: this.getEdgeColor(edge.type),
            lineWidth: edge.weight || 1,
            ...edge.style
          },
          ...edge
        }
        edges.push(formattedEdge)
      })
    }

    return { nodes, edges }
  },

  // 获取节点颜色
  getNodeColor(type) {
    const colors = ColorUtils.getBiomedicalColors()
    return colors[type] || '#6B7280'
  },

  // 获取边颜色
  getEdgeColor(type) {
    const colors = {
      interaction: '#6B7280',
      regulation: '#F59E0B',
      binding: '#EF4444',
      pathway: '#8B5CF6',
      default: '#E5E7EB'
    }
    return colors[type] || colors.default
  },

  // 分子数据格式化
  formatMolecularData(pdbData) {
    const atoms = []
    const bonds = []
    
    if (typeof pdbData === 'string') {
      // 解析PDB格式
      const lines = pdbData.split('\n')
      
      lines.forEach(line => {
        if (line.startsWith('ATOM') || line.startsWith('HETATM')) {
          const atom = {
            id: parseInt(line.slice(6, 11).trim()),
            element: line.slice(76, 78).trim() || line.slice(12, 16).trim().charAt(0),
            x: parseFloat(line.slice(30, 38).trim()),
            y: parseFloat(line.slice(38, 46).trim()),
            z: parseFloat(line.slice(46, 54).trim()),
            residue: line.slice(17, 20).trim(),
            chain: line.slice(21, 22).trim()
          }
          atoms.push(atom)
        } else if (line.startsWith('CONECT')) {
          const parts = line.split(/\s+/).filter(p => p)
          const sourceId = parseInt(parts[1])
          for (let i = 2; i < parts.length; i++) {
            const targetId = parseInt(parts[i])
            if (!isNaN(targetId)) {
              bonds.push({
                start: sourceId - 1, // 转换为0基索引
                end: targetId - 1,
                type: 'single'
              })
            }
          }
        }
      })
    } else if (pdbData.atoms) {
      // 已经是格式化的数据
      return pdbData
    }

    return { atoms, bonds }
  },

  // 图表数据格式化
  formatChartData(data, xField, yField, seriesField) {
    if (!Array.isArray(data)) return []

    return data.map(item => ({
      x: item[xField],
      y: item[yField],
      series: seriesField ? item[seriesField] : '系列1',
      ...item
    }))
  },

  // 树形数据格式化
  formatTreeData(data, idField = 'id', parentField = 'parentId', childrenField = 'children') {
    const map = new Map()
    const roots = []

    // 第一遍遍历，创建节点映射
    data.forEach(item => {
      map.set(item[idField], { ...item, [childrenField]: [] })
    })

    // 第二遍遍历，建立父子关系
    data.forEach(item => {
      const node = map.get(item[idField])
      const parentId = item[parentField]
      
      if (parentId && map.has(parentId)) {
        map.get(parentId)[childrenField].push(node)
      } else {
        roots.push(node)
      }
    })

    return roots
  }
}

/**
 * 坐标和几何工具
 */
export const GeometryUtils = {
  // 计算两点间距离
  distance(p1, p2) {
    const dx = p2.x - p1.x
    const dy = p2.y - p1.y
    return Math.sqrt(dx * dx + dy * dy)
  },

  // 计算点在圆上的位置
  pointOnCircle(center, radius, angle) {
    return {
      x: center.x + radius * Math.cos(angle),
      y: center.y + radius * Math.sin(angle)
    }
  },

  // 生成圆形布局位置
  circularLayout(nodeCount, center, radius) {
    const positions = []
    const angleStep = (2 * Math.PI) / nodeCount
    
    for (let i = 0; i < nodeCount; i++) {
      const angle = i * angleStep
      positions.push(this.pointOnCircle(center, radius, angle))
    }
    
    return positions
  },

  // 生成网格布局位置
  gridLayout(nodeCount, bounds, columns) {
    const positions = []
    const rows = Math.ceil(nodeCount / columns)
    const cellWidth = bounds.width / columns
    const cellHeight = bounds.height / rows
    
    for (let i = 0; i < nodeCount; i++) {
      const row = Math.floor(i / columns)
      const col = i % columns
      positions.push({
        x: bounds.x + col * cellWidth + cellWidth / 2,
        y: bounds.y + row * cellHeight + cellHeight / 2
      })
    }
    
    return positions
  },

  // 边界框计算
  getBounds(points) {
    if (!points.length) return { x: 0, y: 0, width: 0, height: 0 }
    
    let minX = points[0].x
    let maxX = points[0].x
    let minY = points[0].y
    let maxY = points[0].y
    
    points.forEach(point => {
      minX = Math.min(minX, point.x)
      maxX = Math.max(maxX, point.x)
      minY = Math.min(minY, point.y)
      maxY = Math.max(maxY, point.y)
    })
    
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    }
  }
}

/**
 * 动画和交互工具
 */
export const AnimationUtils = {
  // 缓动函数
  easing: {
    linear: t => t,
    easeInQuad: t => t * t,
    easeOutQuad: t => t * (2 - t),
    easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
    easeInCubic: t => t * t * t,
    easeOutCubic: t => (--t) * t * t + 1,
    easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
  },

  // 线性插值
  lerp(start, end, t) {
    return start + (end - start) * t
  },

  // 颜色插值
  lerpColor(color1, color2, t) {
    const r1 = parseInt(color1.slice(1, 3), 16)
    const g1 = parseInt(color1.slice(3, 5), 16)
    const b1 = parseInt(color1.slice(5, 7), 16)
    const r2 = parseInt(color2.slice(1, 3), 16)
    const g2 = parseInt(color2.slice(3, 5), 16)
    const b2 = parseInt(color2.slice(5, 7), 16)
    
    const r = Math.round(this.lerp(r1, r2, t))
    const g = Math.round(this.lerp(g1, g2, t))
    const b = Math.round(this.lerp(b1, b2, t))
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  },

  // 节点动画
  animateNode(node, targetProps, duration = 1000, easing = 'easeInOutQuad') {
    const startProps = {}
    const easingFunc = this.easing[easing] || this.easing.linear
    
    // 记录起始状态
    Object.keys(targetProps).forEach(key => {
      startProps[key] = node[key]
    })
    
    const startTime = Date.now()
    
    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      const easedProgress = easingFunc(progress)
      
      // 更新属性
      Object.keys(targetProps).forEach(key => {
        const start = startProps[key]
        const end = targetProps[key]
        
        if (typeof start === 'number' && typeof end === 'number') {
          node[key] = this.lerp(start, end, easedProgress)
        } else if (typeof start === 'string' && typeof end === 'string' && 
                   start.startsWith('#') && end.startsWith('#')) {
          node[key] = this.lerpColor(start, end, easedProgress)
        }
      })
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    
    animate()
  }
}

/**
 * 导出工具
 */
export const ExportUtils = {
  // 导出为图片
  exportAsImage(element, filename = 'visualization', format = 'png') {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const rect = element.getBoundingClientRect()
    
    canvas.width = rect.width
    canvas.height = rect.height
    
    // 这里需要根据具体的可视化库实现
    // 以下是通用的HTML2Canvas方式
    import('html2canvas').then(html2canvas => {
      html2canvas(element).then(canvas => {
        const link = document.createElement('a')
        link.download = `${filename}.${format}`
        link.href = canvas.toDataURL(`image/${format}`)
        link.click()
      })
    }).catch(() => {
      console.warn('html2canvas not available, using fallback method')
      this.exportAsSVG(element, filename)
    })
  },

  // 导出为SVG
  exportAsSVG(element, filename = 'visualization') {
    const serializer = new XMLSerializer()
    const svgElements = element.querySelectorAll('svg')
    
    if (svgElements.length > 0) {
      const svgString = serializer.serializeToString(svgElements[0])
      const blob = new Blob([svgString], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.download = `${filename}.svg`
      link.href = url
      link.click()
      
      URL.revokeObjectURL(url)
    }
  },

  // 导出数据为JSON
  exportAsJSON(data, filename = 'data') {
    const jsonString = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.download = `${filename}.json`
    link.href = url
    link.click()
    
    URL.revokeObjectURL(url)
  },

  // 导出数据为CSV
  exportAsCSV(data, filename = 'data') {
    if (!Array.isArray(data) || data.length === 0) return
    
    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => 
        JSON.stringify(row[header] || '')
      ).join(','))
    ].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.download = `${filename}.csv`
    link.href = url
    link.click()
    
    URL.revokeObjectURL(url)
  }
} 