<template>
  <div class="performance-metric">
    <el-card>
      <template #header>
        <div class="metric-header">
          <span>{{ title }}</span>
          <el-tag :type="status">{{ statusText }}</el-tag>
        </div>
      </template>
      <div class="metric-content">
        <div class="metric-value">{{ value }}</div>
        <div class="metric-unit">{{ unit }}</div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  unit: {
    type: String,
    default: ''
  },
  status: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'warning', 'danger', 'info'].includes(value)
  },
  description: {
    type: String,
    default: ''
  },
  trend: {
    type: String,
    default: 'stable',
    validator: (value) => ['up', 'down', 'stable'].includes(value)
  },
  chartData: {
    type: Array,
    default: () => []
  },
  chartType: {
    type: String,
    default: 'line',
    validator: (value) => ['line', 'area', 'bar'].includes(value)
  }
})

const statusText = computed(() => {
  switch (props.status) {
    case 'success': return '正常'
    case 'warning': return '警告' 
    case 'danger': return '错误'
    case 'info': 
    default: return '监控中'
  }
})
</script>

<style scoped>
.performance-metric {
  margin-bottom: 16px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-content {
  text-align: center;
  padding: 20px 0;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--el-color-primary);
}

.metric-unit {
  color: var(--el-text-color-secondary);
  margin-top: 8px;
}
</style> 