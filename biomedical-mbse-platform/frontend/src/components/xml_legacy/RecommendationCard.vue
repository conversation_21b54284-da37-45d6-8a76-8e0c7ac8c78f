<template>
  <div class="recommendation-card">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>{{ recommendation.title }}</span>
          <el-tag :type="tagType">{{ priorityText }}</el-tag>
        </div>
      </template>
      <div class="card-content">
        <p>{{ recommendation.description }}</p>
        <div class="meta-info">
          <span class="confidence">置信度: {{ (recommendation.confidence * 100).toFixed(0) }}%</span>
          <span class="type">类型: {{ typeText }}</span>
        </div>
        <div class="actions">
          <el-button type="primary" size="small" @click="applyRecommendation">
            应用推荐
          </el-button>
          <el-button size="small" @click="dismissRecommendation">
            忽略
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Recommendation {
  id: string
  title: string
  description: string
  type: 'optimization' | 'warning' | 'suggestion' | 'insight'
  priority: 'high' | 'medium' | 'low'
  impact: string
  confidence: number
  action?: string
}

interface Props {
  recommendation: Recommendation
}

const props = defineProps<Props>()

const emit = defineEmits<{
  apply: [id: string]
  dismiss: [id: string]
}>()

const typeConfig = computed(() => {
  switch (props.recommendation.type) {
    case 'optimization':
      return { icon: 'Promotion', color: 'success', text: '优化建议' }
    case 'warning':
      return { icon: 'Warning', color: 'warning', text: '警告' }
    case 'suggestion':
      return { icon: 'Idea', color: 'info', text: '建议' }
    case 'insight':
      return { icon: 'View', color: 'primary', text: '洞察' }
    default:
      return { icon: 'Document', color: 'info', text: '推荐' }
  }
})

const priorityConfig = computed(() => {
  switch (props.recommendation.priority) {
    case 'high':
      return { color: 'danger', text: '高优先级' }
    case 'medium':
      return { color: 'warning', text: '中优先级' }
    case 'low':
      return { color: 'info', text: '低优先级' }
    default:
      return { color: 'info', text: '普通' }
  }
})

const tagType = computed(() => priorityConfig.value.color)
const priorityText = computed(() => priorityConfig.value.text)
const typeText = computed(() => typeConfig.value.text)

const applyRecommendation = () => {
  emit('apply', props.recommendation.id)
}

const dismissRecommendation = () => {
  emit('dismiss', props.recommendation.id)
}
</script>

<style scoped>
.recommendation-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content p {
  margin-bottom: 16px;
  color: var(--el-text-color-regular);
}

.meta-info {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.actions {
  display: flex;
  gap: 8px;
}
</style> 