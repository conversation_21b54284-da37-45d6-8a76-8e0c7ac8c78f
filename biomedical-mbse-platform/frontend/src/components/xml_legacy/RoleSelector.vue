<template>
  <div class="role-selector">
    <el-select v-model="selectedRole" placeholder="选择角色" @change="onRoleChange">
      <el-option
        v-for="role in roles"
        :key="role.value"
        :label="role.label"
        :value="role.value"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'

type UserRole = 'system_architect' | 'business_analyst' | 'data_architect' | 'developer' | 'admin'

interface RoleOption {
  value: UserRole
  label: string
  icon: string
}

interface Props {
  modelValue: UserRole
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: UserRole]
  change: [value: UserRole]
}>()

const roles: RoleOption[] = [
  { value: 'system_architect', label: '系统架构师', icon: 'SetUp' },
  { value: 'business_analyst', label: '业务分析师', icon: 'Document' },
  { value: 'data_architect', label: '数据架构师', icon: 'Files' },
  { value: 'developer', label: '开发人员', icon: 'User' },
  { value: 'admin', label: '管理员', icon: 'Setting' }
]

const selectedRole = computed({
  get: () => props.modelValue,
  set: (value: UserRole) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

const currentRoleLabel = computed(() => {
  let role: RoleOption | undefined
  for (let i = 0; i < roles.length; i++) {
    if (roles[i].value === props.modelValue) {
      role = roles[i]
      break
    }
  }
  return role?.label || '未知角色'
})

const onRoleChange = (value: string) => {
  emit('update:modelValue', value as UserRole)
  emit('change', value as UserRole)
}

watch(() => props.modelValue, (newValue) => {
  // 监听modelValue变化，保持同步
  console.log('Role changed to:', newValue)
})
</script>

<style scoped>
.role-selector {
  min-width: 200px;
}
</style> 