<template>
  <div class="smart-dashboard">
    <!-- 顶部性能指标卡片 -->
    <div class="metrics-grid">
      <PerformanceMetric
        title="缓存命中率"
        :value="performanceStats.cacheHitRate"
        unit="%"
        description="自适应缓存系统效率"
        trend="up"
        :chart-data="cacheHitHistory"
        chart-type="line"
        class="metric-cache"
      />
      
      <PerformanceMetric
        title="预加载效率"
        :value="performanceStats.preloadHitRate"
        unit="%"
        description="智能预加载命中率"
        trend="stable"
        :chart-data="preloadHistory"
        chart-type="area"
        class="metric-preload"
      />
      
      <PerformanceMetric
        title="处理速度"
        :value="performanceStats.throughput"
        unit="/s"
        description="实时处理吞吐量"
        trend="up"
        :chart-data="throughputHistory"
        chart-type="bar"
        class="metric-throughput"
      />
      
      <PerformanceMetric
        title="策略切换"
        :value="performanceStats.strategySwitchTime"
        unit="ms"
        description="缓存策略切换延迟"
        trend="down"
        :chart-data="switchTimeHistory"
        chart-type="line"
        class="metric-switch"
      />
    </div>
    
    <!-- 智能推荐区域 -->
    <div class="recommendations-section">
      <div class="section-header">
        <h3>
          <el-icon><TrendCharts /></el-icon>
          智能推荐与洞察
        </h3>
        <el-button type="primary" size="small" @click="refreshRecommendations">
          <el-icon><Refresh /></el-icon>
          刷新推荐
        </el-button>
      </div>
      
      <div class="recommendations-grid">
        <RecommendationCard
          v-for="recommendation in recommendations"
          :key="recommendation.id"
          :recommendation="recommendation"
          @apply="applyRecommendation"
          @dismiss="dismissRecommendation"
        />
      </div>
    </div>
    
    <!-- 实时监控图表 -->
    <div class="monitoring-section">
      <div class="section-header">
        <h3>
          <el-icon><Monitor /></el-icon>
          实时系统监控
        </h3>
        <RoleSelector v-model="currentRole" @change="onRoleChange" />
      </div>
      
      <div class="charts-grid">
        <!-- 系统性能图表 -->
        <div class="chart-container performance-chart">
          <h4>系统性能趋势</h4>
          <PerformanceChart 
            :data="performanceHistory"
            :height="300"
            @period-change="onPeriodChange"
          />
        </div>
        
        <!-- 连接分析图表 -->
        <div class="chart-container connection-chart">
          <h4>连接分析状态</h4>
          <ConnectionChart
            :connections="connectionStats"
            :role="currentRole"
            :height="300"
            @node-click="onNodeClick"
          />
        </div>
      </div>
    </div>
    
    <!-- 分析进度区域 -->
    <div class="progress-section" v-if="isProcessing">
      <div class="section-header">
        <h3>
          <el-icon><Loading /></el-icon>
          分析进度
        </h3>
      </div>
      
      <div class="progress-details">
        <div class="progress-item">
          <span>整体进度</span>
          <el-progress 
            :percentage="parsingProgress" 
            :status="progressStatus"
            :stroke-width="8"
          />
        </div>
        
        <div class="stage-indicators">
          <div 
            v-for="stage in processingStages"
            :key="stage.name"
            :class="['stage-indicator', { active: stage.active, completed: stage.completed }]"
          >
            <el-icon>
              <Check v-if="stage.completed" />
              <Refresh v-else-if="stage.active" />
              <Timer v-else />
            </el-icon>
            <span>{{ stage.label }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts, Monitor, Refresh, Check, Timer } from '@element-plus/icons-vue'
import { useParsingStore } from '../../stores/parsing'
import { useRecommendationStore } from '../../stores/recommendations'
import { useConnectionStore } from '../../stores/connections'
import type { UserRole, Recommendation } from '../../types/parsing'
import type { ParsingStore, RecommendationStore, ConnectionStore } from '../../types/stores'

// 组件导入
import PerformanceMetric from './PerformanceMetric.vue'
import RecommendationCard from './RecommendationCard.vue'
import RoleSelector from './RoleSelector.vue'
import PerformanceChart from './charts/PerformanceChart.vue'
import ConnectionChart from './charts/ConnectionChart.vue'

// Store实例 - 使用双重类型断言解决类型转换问题
const parsingStore = useParsingStore() as unknown as ParsingStore
const recommendationStore = useRecommendationStore() as unknown as RecommendationStore
const connectionStore = useConnectionStore() as unknown as ConnectionStore

// 响应式数据
const currentRole = ref<UserRole>('system_architect')
const refreshInterval = ref<number | null>(null)

// 计算属性 - 现在有了类型安全
const performanceStats = computed(() => parsingStore.performanceStats)
const recommendations = computed(() => recommendationStore.recommendations.slice(0, 6))
const connectionStats = computed(() => connectionStore.connections)
const isProcessing = computed(() => parsingStore.isProcessing)
const parsingProgress = computed(() => parsingStore.parsingProgress)

// 进度状态
const progressStatus = computed(() => {
  if (parsingProgress.value === 100) return 'success'
  if (parsingProgress.value > 0) return undefined
  return 'exception'
})

// 处理阶段
const processingStages = ref([
  { name: 'parse', label: '解析XML文档', active: false, completed: false },
  { name: 'perspective', label: '视角分析', active: false, completed: false },
  { name: 'semantic', label: '语义聚类', active: false, completed: false },
  { name: 'connection', label: '连接分析', active: false, completed: false },
  { name: 'recommendation', label: '生成推荐', active: false, completed: false },
  { name: 'complete', label: '分析完成', active: false, completed: false }
])

// 历史数据（模拟实时数据）
const cacheHitHistory = ref(generateTimeSeriesData(94.9, 20))
const preloadHistory = ref(generateTimeSeriesData(93, 20))
const throughputHistory = ref(generateTimeSeriesData(18000, 20))
const switchTimeHistory = ref(generateTimeSeriesData(0.78, 20))
const performanceHistory = ref(generatePerformanceHistory())

// 方法
const refreshRecommendations = async () => {
  try {
    await recommendationStore.generateRecommendations({
      role: currentRole.value,
      includePerformance: true,
      includeOptimization: true
    })
  } catch (error) {
    console.error('刷新推荐失败:', error)
  }
}

const applyRecommendation = async (recommendation: Recommendation) => {
  try {
    await recommendationStore.applyRecommendation(recommendation.id)
    ElMessage.success(`已应用推荐: ${recommendation.title}`)
  } catch (error) {
    ElMessage.error('应用推荐失败')
  }
}

const dismissRecommendation = async (recommendation: Recommendation) => {
  try {
    await recommendationStore.dismissRecommendation(recommendation.id)
  } catch (error) {
    console.error('忽略推荐失败:', error)
  }
}

const onRoleChange = async (role: UserRole) => {
  currentRole.value = role
  await Promise.all([
    connectionStore.analyzeConnections(role),
    refreshRecommendations()
  ])
}

const onPeriodChange = (period: string) => {
  // 更新图表时间周期
  performanceHistory.value = generatePerformanceHistory(period)
}

const onNodeClick = (nodeId: string) => {
  // 处理连接图节点点击
  console.log('节点点击:', nodeId)
}

// 数据更新循环
const startDataRefresh = () => {
  refreshInterval.value = window.setInterval(() => {
    // 更新历史数据（模拟实时数据流）
    updateTimeSeriesData(cacheHitHistory.value, Math.random() * 5 + 92)
    updateTimeSeriesData(preloadHistory.value, Math.random() * 4 + 91)
    updateTimeSeriesData(throughputHistory.value, Math.random() * 2000 + 17000)
    updateTimeSeriesData(switchTimeHistory.value, Math.random() * 0.5 + 0.5)
  }, 2000)
}

const stopDataRefresh = () => {
  if (refreshInterval.value) {
    window.clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 辅助函数
function generateTimeSeriesData(baseValue: number, count: number) {
  return Array.from({ length: count }, (_, i) => ({
    time: new Date(Date.now() - (count - i) * 60000).toISOString(),
    value: baseValue + (Math.random() - 0.5) * baseValue * 0.1
  }))
}

function updateTimeSeriesData(data: any[], newValue: number) {
  data.shift()
  data.push({
    time: new Date().toISOString(),
    value: newValue
  })
}

function generatePerformanceHistory(period: string = '1h') {
  const now = Date.now()
  const intervals = period === '1h' ? 60 : period === '6h' ? 360 : 1440
  
  return Array.from({ length: intervals }, (_, i) => ({
    time: new Date(now - (intervals - i) * 60000).toISOString(),
    cpu: 94.9 + (Math.random() - 0.5) * 5,
    memory: 40 + (Math.random() - 0.5) * 10,
    network: 18000 + (Math.random() - 0.5) * 2000
  }))
}

// 生命周期
onMounted(async () => {
  // 初始化数据
  await Promise.all([
    refreshRecommendations(),
    connectionStore.analyzeConnections(currentRole.value)
  ])
  
  // 开始数据刷新
  startDataRefresh()
})

onUnmounted(() => {
  stopDataRefresh()
})
</script>

<style scoped lang="scss">
.smart-dashboard {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
  
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
  }
  
  .recommendations-section,
  .monitoring-section,
  .progress-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }
  }
  
  .recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
  }
  
  .charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .chart-container {
    background: #fafafa;
    border-radius: 8px;
    padding: 20px;
    
    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
      color: #666;
    }
  }
  
  .progress-details {
    .progress-item {
      margin-bottom: 20px;
      
      span {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
      }
    }
    
    .stage-indicators {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      
      .stage-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 6px;
        background: #f0f0f0;
        color: #666;
        transition: all 0.3s ease;
        
        &.active {
          background: #e6f3ff;
          color: #1890ff;
        }
        
        &.completed {
          background: #f6ffed;
          color: #52c41a;
        }
      }
    }
  }
}
</style> 