<template>
  <div class="system-architect-workspace">
    <!-- 架构概览面板 -->
    <div class="architecture-overview-panel">
      <div class="project-context">
        <div class="project-info">
          <el-select v-model="currentProject" placeholder="选择项目" size="small" class="project-selector">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
          <div class="project-details" v-if="currentProjectInfo">
            <span class="project-name">{{ currentProjectInfo.name }}</span>
            <el-tag type="info" size="small">{{ currentProjectInfo.domain }}</el-tag>
          </div>
        </div>
        <div class="architect-title">
          <h2>系统架构师工作台</h2>
          <span class="subtitle">Architecture Design & Analysis</span>
        </div>
      </div>

      <!-- 架构层次视图 -->
      <div class="architecture-layers">
        <div class="layer-card" v-for="layer in architectureLayers" :key="layer.id" 
             :class="{ active: selectedLayer === layer.id }"
             @click="selectLayer(layer.id)">
          <div class="layer-header">
            <el-icon class="layer-icon"><component :is="layer.icon" /></el-icon>
            <span class="layer-name">{{ layer.name }}</span>
          </div>
          <div class="layer-stats">
            <div class="stat-item">
              <span class="stat-value">{{ layer.components }}</span>
              <span class="stat-label">组件</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ layer.interfaces }}</span>
              <span class="stat-label">接口</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主工作区域 -->
    <div class="architect-main-workspace">
      <!-- 左侧架构树 -->
      <div class="architecture-tree-panel">
        <el-card class="tree-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Promotion /></el-icon>
              <span>系统结构</span>
            </div>
          </template>
          <el-tree 
            :data="architectureTree"
            :props="treeProps"
            node-key="id"
            default-expand-all
            @node-click="handleNodeClick"
            class="architecture-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <el-icon class="node-icon"><component :is="data.icon" /></el-icon>
                <span class="node-label">{{ data.label }}</span>
                <el-tag v-if="data.type" :type="getNodeTypeColor(data.type)" size="small">{{ data.type }}</el-tag>
              </div>
            </template>
          </el-tree>
        </el-card>
      </div>

      <!-- 中央架构画布 -->
      <div class="architecture-canvas-area">
        <div class="canvas-toolbar">
          <el-button-group>
            <el-button @click="switchView('logical')" :type="currentView === 'logical' ? 'primary' : 'default'">
              <el-icon><CreditCard /></el-icon>逻辑视图
            </el-button>
            <el-button @click="switchView('physical')" :type="currentView === 'physical' ? 'primary' : 'default'">
              <el-icon><Grid /></el-icon>物理视图
            </el-button>
            <el-button @click="switchView('deployment')" :type="currentView === 'deployment' ? 'primary' : 'default'">
              <el-icon><Monitor /></el-icon>部署视图
            </el-button>
          </el-button-group>
          
          <div class="canvas-actions">
            <el-button icon="Download" @click="exportArchitecture">导出</el-button>
            <el-button icon="Refresh" @click="refreshCanvas">刷新</el-button>
            <el-button type="primary" icon="Plus" @click="addComponent">添加组件</el-button>
          </div>
        </div>

        <div class="architecture-canvas" ref="canvasRef">
          <ArchitectureCanvas 
            :view-type="currentView"
            :components="selectedLayerComponents"
            :connections="componentConnections"
            @component-selected="handleComponentSelected"
            @connection-created="handleConnectionCreated"
          />
        </div>
      </div>

      <!-- 右侧属性与分析面板 -->
      <div class="properties-analysis-panel">
        <!-- 组件属性 -->
        <el-card class="component-properties" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>组件属性</span>
            </div>
          </template>
          <div v-if="selectedComponent" class="properties-content">
            <div class="property-group">
              <h5>基本信息</h5>
              <el-form label-width="70px" size="small">
                <el-form-item label="名称">
                  <el-input v-model="selectedComponent.name" />
                </el-form-item>
                <el-form-item label="类型">
                  <el-select v-model="selectedComponent.type">
                    <el-option label="服务组件" value="service" />
                    <el-option label="数据组件" value="data" />
                    <el-option label="界面组件" value="ui" />
                    <el-option label="基础组件" value="infrastructure" />
                  </el-select>
                </el-form-item>
                <el-form-item label="层次">
                  <el-select v-model="selectedComponent.layer">
                    <el-option label="表示层" value="presentation" />
                    <el-option label="业务层" value="business" />
                    <el-option label="数据层" value="data" />
                    <el-option label="基础层" value="infrastructure" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
            
            <div class="property-group">
              <h5>技术特性</h5>
              <div class="tech-specs">
                <div v-for="spec in selectedComponent.techSpecs" :key="spec.name" class="spec-item">
                  <span class="spec-name">{{ spec.name }}</span>
                  <span class="spec-value">{{ spec.value }}</span>
                </div>
              </div>
            </div>

            <div class="property-group">
              <h5>质量属性</h5>
              <div class="quality-attributes">
                <div v-for="attr in selectedComponent.qualityAttrs" :key="attr.name" class="attr-item">
                  <span class="attr-name">{{ attr.name }}</span>
                  <el-rate v-model="attr.rating" :max="5" size="small" disabled />
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="选择组件查看属性" />
        </el-card>

        <!-- 架构分析 -->
        <el-card class="architecture-analysis" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>架构分析</span>
            </div>
          </template>
          <div class="analysis-content">
            <div class="analysis-metric">
              <span class="metric-label">复杂性评分</span>
              <el-progress :percentage="architectureMetrics.complexity" color="#f56c6c" />
            </div>
            <div class="analysis-metric">
              <span class="metric-label">耦合度</span>
              <el-progress :percentage="architectureMetrics.coupling" color="#e6a23c" />
            </div>
            <div class="analysis-metric">
              <span class="metric-label">内聚性</span>
              <el-progress :percentage="architectureMetrics.cohesion" color="#67c23a" />
            </div>
            <div class="analysis-metric">
              <span class="metric-label">可维护性</span>
              <el-progress :percentage="architectureMetrics.maintainability" color="#409eff" />
            </div>
          </div>
        </el-card>

        <!-- 架构决策记录 -->
        <el-card class="architecture-decisions" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Files /></el-icon>
              <span>架构决策</span>
            </div>
          </template>
          <div class="decisions-list">
            <div v-for="decision in architectureDecisions" :key="decision.id" class="decision-item">
              <div class="decision-header">
                <span class="decision-title">{{ decision.title }}</span>
                <el-tag :type="getDecisionStatusColor(decision.status)" size="small">
                  {{ decision.status }}
                </el-tag>
              </div>
              <div class="decision-content">{{ decision.description }}</div>
              <div class="decision-meta">
                <span class="decision-date">{{ decision.date }}</span>
                <span class="decision-author">{{ decision.author }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="architect-bottom-toolbar">
      <div class="validation-status">
        <el-icon class="status-icon"><CircleCheckFilled /></el-icon>
        <span>架构验证状态：</span>
        <el-tag :type="validationStatus.type" size="small">{{ validationStatus.text }}</el-tag>
      </div>
      
      <div class="toolbar-actions">
        <el-button @click="validateArchitecture" icon="Check">验证架构</el-button>
        <el-button @click="generateCode" icon="Document">生成代码</el-button>
        <el-button @click="exportDocuments" icon="Download">导出文档</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Setting, Menu, FolderOpened, CircleCheckFilled,
  Calendar, Grid, House, Download, Refresh, Plus, Check
} from '@element-plus/icons-vue'
import type { SystemComponent, LayerConfig, ConnectionConfig } from '../../../types/view-system'

// 响应式数据
const currentProject = ref<string>('')
const currentProjectInfo = ref<any>(null)
const selectedLayer = ref<string>('business')
const currentView = ref<string>('logical')
const selectedComponent = ref<any>(null)
const canvasRef = ref<HTMLElement>()

// 项目数据
const projects = ref([
  {
    id: 'proj-001',
    name: '智能交通系统',
    domain: 'SysML',
    status: '进行中'
  },
  {
    id: 'proj-002', 
    name: '企业数据平台',
    domain: 'UML',
    status: '规划中'
  }
])

// 架构层次
const architectureLayers = ref([
  {
    id: 'presentation',
    name: '表示层',
    icon: 'Monitor',
    components: 12,
    interfaces: 8
  },
  {
    id: 'business',
    name: '业务层',
    icon: 'Operation',
    components: 18,
    interfaces: 15
  },
  {
    id: 'data',
    name: '数据层',
    icon: 'DataBoard',
    components: 9,
    interfaces: 6
  },
  {
    id: 'infrastructure',
    name: '基础设施层',
    icon: 'Platform',
    components: 6,
    interfaces: 12
  }
])

// 架构树
const architectureTree = ref([
  {
    id: 'system',
    label: '智能交通系统',
    icon: 'Platform',
    type: 'system',
    children: [
      {
        id: 'traffic-management',
        label: '交通管理子系统',
        icon: 'Operation',
        type: 'subsystem',
        children: [
          {
            id: 'traffic-monitor',
            label: '交通监控服务',
            icon: 'View',
            type: 'service'
          },
          {
            id: 'signal-control',
            label: '信号控制服务',
            icon: 'Switch',
            type: 'service'
          }
        ]
      },
      {
        id: 'data-processing',
        label: '数据处理子系统',
        icon: 'DataAnalysis',
        type: 'subsystem'
      }
    ]
  }
])

const treeProps = {
  children: 'children',
  label: 'label'
}

// 架构指标
const architectureMetrics = reactive({
  complexity: 75,
  coupling: 45,
  cohesion: 85,
  maintainability: 70
})

// 架构决策
const architectureDecisions = ref([
  {
    id: '1',
    title: '选择微服务架构模式',
    description: '基于系统复杂性和团队结构考虑，采用微服务架构',
    status: '已采纳',
    date: '2025-06-15',
    author: '张架构师'
  },
  {
    id: '2',
    title: '数据库选型决策',
    description: '核心业务数据使用PostgreSQL，缓存使用Redis',
    status: '评估中',
    date: '2025-06-16',
    author: '李架构师'
  }
])

// 验证状态
const validationStatus = reactive({
  type: 'success',
  text: '架构设计符合规范'
})

// 方法
const selectLayer = (layerId: string) => {
  selectedLayer.value = layerId
  ElMessage.info(`已切换到${architectureLayers.value.find(l => l.id === layerId)?.name}`)
}

const switchView = (viewType: string) => {
  currentView.value = viewType
  ElMessage.info(`已切换到${viewType === 'logical' ? '逻辑' : viewType === 'physical' ? '物理' : '部署'}视图`)
}

const handleNodeClick = (data: any) => {
  ElMessage.info(`选择了节点: ${data.label}`)
}

const handleComponentSelected = (component: any) => {
  selectedComponent.value = {
    name: component.name,
    type: 'service',
    layer: 'business',
    techSpecs: [
      { name: '技术栈', value: 'Spring Boot + Java 17' },
      { name: '部署方式', value: 'Docker容器' },
      { name: '负载能力', value: '1000 TPS' }
    ],
    qualityAttrs: [
      { name: '性能', rating: 4 },
      { name: '可靠性', rating: 5 },
      { name: '安全性', rating: 4 },
      { name: '可维护性', rating: 3 }
    ]
  }
}

const getNodeTypeColor = (type: string) => {
  switch (type) {
    case 'system': return 'success'
    case 'subsystem': return 'primary'
    case 'service': return 'info'
    default: return 'default'
  }
}

const getDecisionStatusColor = (status: string) => {
  switch (status) {
    case '已采纳': return 'success'
    case '评估中': return 'warning'
    case '已废弃': return 'danger'
    default: return 'info'
  }
}

const validateArchitecture = () => {
  ElMessage.success('架构验证完成，未发现违规设计')
}

const generateCode = () => {
  ElMessage.info('正在生成代码框架...')
}

const exportDocuments = () => {
  ElMessage.success('架构文档导出成功')
}

const handleConnectionCreated = (connection: any) => {
  ElMessage.success('组件连接已创建')
}

const exportArchitecture = () => {
  ElMessage.success('架构图已导出')
}

const refreshCanvas = () => {
  ElMessage.info('画布已刷新')
}

const addComponent = () => {
  ElMessage.info('添加新组件')
}

// 计算属性
const selectedLayerComponents = computed(() => {
  // 根据选中的层次返回对应的组件
  return []
})

const componentConnections = computed(() => {
  // 返回组件间的连接关系
  return []
})

// 初始化
onMounted(() => {
  if (projects.value[0]) {
    currentProject.value = projects.value[0].id
    currentProjectInfo.value = projects.value[0]
  }
})
</script>

<style scoped lang="scss">
.system-architect-workspace {
  display: grid;
  grid-template-rows: auto 1fr auto;
  height: 100vh;
  background: #f5f5f5;

  // 架构概览面板
  .architecture-overview-panel {
    background: white;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;

    .project-context {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .project-info {
        display: flex;
        align-items: center;
        gap: 16px;

        .project-selector {
          width: 200px;
        }

        .project-details {
          display: flex;
          align-items: center;
          gap: 8px;

          .project-name {
            font-weight: 600;
            color: #374151;
          }
        }
      }

      .architect-title {
        text-align: right;

        h2 {
          margin: 0;
          color: #1f2937;
          font-size: 24px;
        }

        .subtitle {
          color: #6b7280;
          font-size: 14px;
          font-style: italic;
        }
      }
    }

    .architecture-layers {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;

      .layer-card {
        background: #f9fafb;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #7c3aed;
          transform: translateY(-2px);
        }

        &.active {
          background: #f3e8ff;
          border-color: #7c3aed;
        }

        .layer-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .layer-icon {
            font-size: 20px;
            color: #7c3aed;
          }

          .layer-name {
            font-weight: 600;
            color: #374151;
          }
        }

        .layer-stats {
          display: flex;
          justify-content: space-between;

          .stat-item {
            text-align: center;

            .stat-value {
              display: block;
              font-size: 18px;
              font-weight: 700;
              color: #1f2937;
            }

            .stat-label {
              font-size: 12px;
              color: #6b7280;
            }
          }
        }
      }
    }
  }

  // 主工作区域
  .architect-main-workspace {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    gap: 1px;
    background: #e5e7eb;

    // 左侧架构树
    .architecture-tree-panel {
      background: white;

      .tree-card {
        height: 100%;
        border: none;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .architecture-tree {
          .tree-node {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;

            .node-icon {
              color: #7c3aed;
            }

            .node-label {
              flex: 1;
            }
          }
        }
      }
    }

    // 中央架构画布
    .architecture-canvas-area {
      background: white;
      display: flex;
      flex-direction: column;

      .canvas-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #e5e7eb;
        background: #f9fafb;

        .canvas-actions {
          display: flex;
          gap: 8px;
        }
      }

      .architecture-canvas {
        flex: 1;
        position: relative;
        background: #fafafa;
        background-image: 
          radial-gradient(circle, #e5e7eb 1px, transparent 1px);
        background-size: 20px 20px;
      }
    }

    // 右侧属性与分析面板
    .properties-analysis-panel {
      background: white;
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .component-properties {
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .properties-content {
          .property-group {
            margin-bottom: 20px;

            h5 {
              margin: 0 0 12px 0;
              font-size: 14px;
              font-weight: 600;
              color: #374151;
            }

            .tech-specs, .quality-attributes {
              .spec-item, .attr-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #f1f5f9;

                .spec-name, .attr-name {
                  font-size: 13px;
                  color: #6b7280;
                }

                .spec-value {
                  font-weight: 500;
                  color: #374151;
                }
              }
            }
          }
        }
      }

      .architecture-analysis {
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .analysis-content {
          .analysis-metric {
            margin-bottom: 16px;

            .metric-label {
              display: block;
              font-size: 13px;
              color: #6b7280;
              margin-bottom: 8px;
            }
          }
        }
      }

      .architecture-decisions {
        flex: 1;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .decisions-list {
          max-height: 300px;
          overflow-y: auto;

          .decision-item {
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;

            .decision-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .decision-title {
                font-weight: 600;
                color: #374151;
              }
            }

            .decision-content {
              font-size: 13px;
              color: #6b7280;
              margin-bottom: 8px;
            }

            .decision-meta {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              color: #9ca3af;
            }
          }
        }
      }
    }
  }

  // 底部工具栏
  .architect-bottom-toolbar {
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .validation-status {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-icon {
        color: #059669;
      }
    }

    .toolbar-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 深度样式覆盖
:deep(.el-card) {
  border: 1px solid #e5e7eb;
  box-shadow: none;

  .el-card__header {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 12px 16px;
  }

  .el-card__body {
    padding: 16px;
  }
}

:deep(.el-tree) {
  background: transparent;

  .el-tree-node__content {
    height: 40px;
  }
}

:deep(.el-progress) {
  .el-progress__text {
    font-size: 12px;
  }
}

// ========== 深色主题支持 ==========
:global(.theme-dark) .architect-workspace {
  background: linear-gradient(135deg, #111827 0%, #0f172a 50%, #111827 100%);
  color: #f9fafb;

  .architect-header {
    background: #1f2937;
    border-bottom: 2px solid #374151;

    .architect-title {
      h2 {
        color: #a855f7;
        text-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
      }

      .subtitle {
        color: #d1d5db;
      }
    }

    .architecture-layers {
      .layer-card {
        background: #374151;
        border: 2px solid #4b5563;
        color: #f9fafb;

        &:hover {
          border-color: #a855f7;
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(168, 85, 247, 0.25);
          background: #475569;
        }

        &.active {
          border-color: #a855f7;
          background: linear-gradient(135deg, #581c87 0%, #6b21a8 100%);
          box-shadow: 0 8px 25px rgba(168, 85, 247, 0.4);
          color: #ffffff;
        }

        .layer-header {
          .layer-name {
            color: #f9fafb;
            font-weight: 700;
          }
        }

        .layer-stats {
          .stat-value {
            color: #c084fc;
            font-weight: 800;
          }

          .stat-label {
            color: #d1d5db;
            font-weight: 500;
          }
        }
      }
    }
  }

  .architect-main-workspace {
    // 左侧架构树
    .architecture-tree-panel {
      background: #1f2937;
      border-right: 2px solid #374151;

      .tree-card {
        background: #1f2937;
        
        .card-header {
          background: #374151;
          border-bottom: 1px solid #4b5563;
          color: #f9fafb;
          font-weight: 700;
        }
      }
    }

    // 中央架构画布
    .architecture-canvas-area {
      background: #1f2937;

      .canvas-toolbar {
        background: #374151;
        border-bottom: 1px solid #4b5563;
        
        .view-switcher .el-button {
          background: #4b5563;
          border-color: #6b7280;
          color: #f9fafb;
          
          &:hover {
            background: #6b7280;
            color: #ffffff;
          }
          
          &.el-button--primary {
            background: #a855f7;
            border-color: #a855f7;
            
            &:hover {
              background: #9333ea;
            }
          }
        }

        .canvas-actions .el-button {
          background: #4b5563;
          border-color: #6b7280;
          color: #f9fafb;
          
          &:hover {
            background: #6b7280;
            color: #ffffff;
          }
        }
      }

      .architecture-canvas {
        background: #111827;
        background-image: radial-gradient(circle, #374151 1px, transparent 1px);
      }
    }

    // 右侧属性分析面板
    .properties-analysis-panel {
      background: #1f2937;

      .component-properties,
      .architecture-analysis,
      .architecture-decisions {
        .card-header {
          color: #f9fafb;
          font-weight: 700;
        }

        .properties-content,
        .analysis-content,
        .decisions-list {
          .property-group h5,
          .metric-label,
          .decision-title {
            color: #f9fafb;
            font-weight: 600;
          }

          .spec-name,
          .attr-name,
          .decision-content {
            color: #d1d5db;
          }

          .spec-value {
            color: #f3f4f6;
            font-weight: 500;
          }

          .spec-item,
          .attr-item,
          .decision-item {
            border-bottom: 1px solid #374151;
          }

          .decision-meta {
            color: #9ca3af;
          }
        }
      }
    }
  }

  // 底部工具栏
  .architect-bottom-toolbar {
    background: #1f2937;
    border-top: 1px solid #374151;

    .validation-status {
      color: #f9fafb;

      .status-icon {
        color: #10b981;
      }
    }
  }
}

// 深度样式覆盖 - 深色主题
:global(.theme-dark) :deep(.el-card) {
  background: #1f2937;
  border: 1px solid #374151;

  .el-card__header {
    background: #374151;
    border-bottom: 1px solid #4b5563;
    color: #f9fafb;
  }

  .el-card__body {
    background: #1f2937;
    color: #f9fafb;
  }
}

:global(.theme-dark) :deep(.el-tree) {
  background: #1f2937;
  color: #f9fafb;

  .el-tree-node__content {
    color: #f3f4f6;
    font-weight: 600;

    &:hover {
      background: #374151;
      color: #ffffff;
    }
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background: #581c87;
    color: #c084fc;
    font-weight: 700;
  }
}

:global(.theme-dark) :deep(.el-progress) {
  .el-progress__text {
    color: #f9fafb;
    font-weight: 600;
  }

  .el-progress-bar__outer {
    background: #374151;
  }

  .el-progress-bar__inner {
    background: linear-gradient(90deg, #a855f7 0%, #9333ea 100%);
  }
}

:global(.theme-dark) :deep(.el-button) {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;

  &:hover {
    background: #6b7280;
    border-color: #9ca3af;
    color: #ffffff;
  }

  &.el-button--primary {
    background: #a855f7;
    border-color: #a855f7;
    color: #ffffff;

    &:hover {
      background: #9333ea;
      border-color: #9333ea;
    }
  }
}
</style> 