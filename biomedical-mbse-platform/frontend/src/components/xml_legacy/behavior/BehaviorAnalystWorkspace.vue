<template>
  <div class="behavior-analyst-workspace">
    <!-- 顶部仿真控制栏 -->
    <div class="simulation-control-panel">
      <div class="project-context">
        <el-select v-model="currentProject" placeholder="选择项目" size="small" class="project-selector">
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
        <div class="behavior-title">
          <h2>行为分析师工作台</h2>
          <span class="subtitle">Dynamic Behavior Analysis & Simulation</span>
        </div>
      </div>

      <!-- 仿真控制器 -->
      <div class="simulation-controls">
        <div class="simulation-info">
          <span class="control-label">当前仿真：</span>
          <el-select v-model="activeSimulation" placeholder="选择仿真场景" class="sim-selector">
            <el-option label="用户登录流程" value="login" />
            <el-option label="交易处理流程" value="transaction" />
            <el-option label="数据同步流程" value="sync" />
          </el-select>
        </div>
        
        <div class="simulation-actions">
          <el-button-group>
            <el-button 
              :type="simulationState === 'playing' ? 'primary' : 'default'" 
              icon="VideoCamera"
              @click="toggleSimulation"
            >
              {{ simulationState === 'playing' ? '暂停' : '播放' }}
            </el-button>
            <el-button icon="Refresh" @click="resetSimulation">重置</el-button>
            <el-button icon="Check" @click="stepSimulation">单步</el-button>
          </el-button-group>
          
          <div class="speed-control">
            <span class="speed-label">速度：</span>
            <el-slider v-model="simulationSpeed" :min="1" :max="10" :step="1" style="width: 80px;" />
            <span class="speed-value">{{ simulationSpeed }}x</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主分析区域 -->
    <div class="behavior-main-workspace">
      <!-- 左侧行为模型面板 -->
      <div class="behavior-models-panel">
        <el-tabs v-model="activeModelTab" tab-position="left" class="model-tabs">
          <el-tab-pane label="状态机" name="stateMachine">
            <div class="state-machine-view">
              <div class="toolbar">
                <el-button size="small" icon="Plus" @click="addState">添加状态</el-button>
                <el-button size="small" icon="ChatRound" @click="addTransition">添加转换</el-button>
              </div>
              <StateMachineCanvas 
                :states="currentStates"
                :transitions="currentTransitions"
                :active-state="currentActiveState"
                @state-selected="handleStateSelected"
                @transition-created="handleTransitionCreated"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="时序图" name="sequence">
            <div class="sequence-diagram-view">
              <div class="toolbar">
                <el-button size="small" icon="Plus" @click="addLifeline">添加生命线</el-button>
                <el-button size="small" icon="ChatRound" @click="addMessage">添加消息</el-button>
              </div>
              <SequenceDiagramCanvas 
                :lifelines="sequenceLifelines"
                :messages="sequenceMessages"
                :current-time="simulationTime"
                @message-selected="handleMessageSelected"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="活动图" name="activity">
            <div class="activity-diagram-view">
              <div class="toolbar">
                <el-button size="small" icon="Plus" @click="addActivity">添加活动</el-button>
                <el-button size="small" icon="Location" @click="addDecision">添加判断</el-button>
              </div>
              <ActivityDiagramCanvas 
                :activities="activityNodes"
                :flows="activityFlows"
                :current-activity="currentActivity"
                @activity-selected="handleActivitySelected"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 中央时间轴面板 -->
      <div class="timeline-analysis-panel">
        <!-- 时间轴控制 -->
        <div class="timeline-controls">
          <div class="timeline-header">
            <h4>行为时间轴分析</h4>
            <div class="timeline-tools">
              <el-button size="small" icon="ZoomIn" @click="zoomIn">放大</el-button>
              <el-button size="small" icon="ZoomOut" @click="zoomOut">缩小</el-button>
              <el-button size="small" icon="Refresh" @click="resetZoom">重置</el-button>
            </div>
          </div>
          <div class="time-range">
            <span>时间范围：0ms - {{ totalSimulationTime }}ms</span>
            <div class="current-time">当前时间：{{ simulationTime }}ms</div>
          </div>
        </div>

        <!-- 多层时间轴 -->
        <div class="timeline-view">
          <div class="timeline-layers">
            <!-- 用户交互层 -->
            <div class="timeline-layer user-layer">
              <div class="layer-label">用户交互</div>
              <div class="layer-track">
                <div 
                  v-for="event in userEvents" 
                  :key="event.id"
                  class="timeline-event user-event"
                  :style="{ left: `${(event.timestamp / totalSimulationTime) * 100}%` }"
                  @click="jumpToTime(event.timestamp)"
                >
                  <el-tooltip :content="event.description">
                    <div class="event-marker">{{ event.type }}</div>
                  </el-tooltip>
                </div>
              </div>
            </div>

            <!-- 系统处理层 -->
            <div class="timeline-layer system-layer">
              <div class="layer-label">系统处理</div>
              <div class="layer-track">
                <div 
                  v-for="event in systemEvents" 
                  :key="event.id"
                  class="timeline-event system-event"
                  :style="{ 
                    left: `${(event.startTime / totalSimulationTime) * 100}%`,
                    width: `${((event.endTime - event.startTime) / totalSimulationTime) * 100}%`
                  }"
                  @click="jumpToTime(event.startTime)"
                >
                  <el-tooltip :content="`${event.description} (${event.duration}ms)`">
                    <div class="event-bar">{{ event.type }}</div>
                  </el-tooltip>
                </div>
              </div>
            </div>

            <!-- 数据流层 -->
            <div class="timeline-layer data-layer">
              <div class="layer-label">数据流</div>
              <div class="layer-track">
                <div 
                  v-for="event in dataEvents" 
                  :key="event.id"
                  class="timeline-event data-event"
                  :style="{ left: `${(event.timestamp / totalSimulationTime) * 100}%` }"
                  @click="jumpToTime(event.timestamp)"
                >
                  <el-tooltip :content="event.description">
                    <div class="event-marker">{{ event.type }}</div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>

          <!-- 时间指示器 -->
          <div 
            class="time-indicator"
            :style="{ left: `${(simulationTime / totalSimulationTime) * 100}%` }"
          ></div>
        </div>

        <!-- 事件详情 -->
        <div class="event-details">
          <el-card v-if="selectedEvent" shadow="never">
            <template #header>
              <div class="event-header">
                <span>事件详情</span>
                <el-tag :type="getEventTypeColor(selectedEvent.type)">{{ selectedEvent.type }}</el-tag>
              </div>
            </template>
            <div class="event-content">
              <div class="detail-item">
                <span class="detail-label">时间戳：</span>
                <span class="detail-value">{{ selectedEvent.timestamp }}ms</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">描述：</span>
                <span class="detail-value">{{ selectedEvent.description }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">参数：</span>
                <pre class="detail-json">{{ JSON.stringify(selectedEvent.params, null, 2) }}</pre>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 右侧性能分析面板 -->
      <div class="performance-analysis-panel">
        <!-- 实时指标 -->
        <el-card class="metrics-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Histogram /></el-icon>
              <span>实时性能指标</span>
            </div>
          </template>
          <div class="metrics-content">
            <div class="metric-group">
              <div class="metric-item">
                <div class="metric-label">平均响应时间</div>
                <div class="metric-value">{{ performanceMetrics.avgResponseTime }}ms</div>
                <div class="metric-trend">
                  <el-icon :class="getTrendClass(performanceMetrics.responseTimeTrend)">
                    <component :is="getTrendIcon(performanceMetrics.responseTimeTrend)" />
                  </el-icon>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">吞吐量</div>
                <div class="metric-value">{{ performanceMetrics.throughput }}/s</div>
                <div class="metric-trend">
                  <el-icon :class="getTrendClass(performanceMetrics.throughputTrend)">
                    <component :is="getTrendIcon(performanceMetrics.throughputTrend)" />
                  </el-icon>
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-label">错误率</div>
                <div class="metric-value">{{ performanceMetrics.errorRate }}%</div>
                <div class="metric-trend">
                  <el-icon :class="getTrendClass(performanceMetrics.errorRateTrend)">
                    <component :is="getTrendIcon(performanceMetrics.errorRateTrend)" />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 状态覆盖率 -->
        <el-card class="coverage-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Histogram /></el-icon>
              <span>状态覆盖率</span>
            </div>
          </template>
          <div class="coverage-content">
            <div class="coverage-chart">
              <div class="chart-placeholder">
                <!-- 这里放置覆盖率饼图 -->
                <div class="coverage-summary">
                  <div class="coverage-percentage">{{ stateCoverage.percentage }}%</div>
                  <div class="coverage-details">
                    <div>已覆盖: {{ stateCoverage.covered }}/{{ stateCoverage.total }}</div>
                    <div>未覆盖: {{ stateCoverage.uncovered }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="coverage-list">
              <div v-for="state in stateCoverage.states" :key="state.id" class="coverage-item">
                <span class="state-name">{{ state.name }}</span>
                <el-progress 
                  :percentage="state.coverage" 
                  :status="state.coverage > 80 ? 'success' : state.coverage > 50 ? 'warning' : 'exception'"
                  :stroke-width="6"
                />
              </div>
            </div>
          </div>
        </el-card>

        <!-- 异常检测 -->
        <el-card class="anomaly-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><WarningFilled /></el-icon>
              <span>异常检测</span>
            </div>
          </template>
          <div class="anomaly-content">
            <div v-if="anomalies.length === 0" class="no-anomaly">
              <el-icon class="success-icon"><CircleCheckFilled /></el-icon>
              <span>未检测到异常</span>
            </div>
            <div v-else class="anomaly-list">
              <div v-for="anomaly in anomalies" :key="anomaly.id" class="anomaly-item">
                <div class="anomaly-header">
                  <el-tag :type="getAnomalyTypeColor(anomaly.severity)" size="small">
                    {{ anomaly.severity }}
                  </el-tag>
                  <span class="anomaly-time">{{ anomaly.timestamp }}ms</span>
                </div>
                <div class="anomaly-description">{{ anomaly.description }}</div>
                <div class="anomaly-suggestion">{{ anomaly.suggestion }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 底部数据监控条 -->
    <div class="data-monitoring-bar">
      <div class="monitoring-metrics">
        <div class="metric-display">
          <span class="metric-title">仿真时长：</span>
          <span class="metric-data">{{ formatTime(simulationTime) }}</span>
        </div>
        <div class="metric-display">
          <span class="metric-title">事件总数：</span>
          <span class="metric-data">{{ totalEvents }}</span>
        </div>
        <div class="metric-display">
          <span class="metric-title">状态转换：</span>
          <span class="metric-data">{{ stateTransitions }}</span>
        </div>
        <div class="metric-display">
          <span class="metric-title">内存使用：</span>
          <span class="metric-data">{{ memoryUsage }}MB</span>
        </div>
      </div>
      
      <div class="monitoring-actions">
        <el-button size="small" @click="exportReport">导出报告</el-button>
        <el-button size="small" @click="saveSnapshot">保存快照</el-button>
        <el-button size="small" type="primary" @click="generateTest">生成测试</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  VideoCamera, Refresh, Check, Plus, ChatRound, Comment,
  Location, ZoomIn, ZoomOut, Setting, Monitor, TrendCharts,
  WarningFilled, CircleCheckFilled, ArrowUp, ArrowDown, Delete
} from '@element-plus/icons-vue'

// 响应式数据
const currentProject = ref<string>('')
const activeSimulation = ref<string>('login')
const simulationState = ref<string>('stopped') // stopped, playing, paused
const simulationSpeed = ref<number>(5)
const simulationTime = ref<number>(0)
const totalSimulationTime = ref<number>(10000)
const activeModelTab = ref<string>('stateMachine')
const selectedEvent = ref<any>(null)
const currentActiveState = ref<string>('')
const currentActivity = ref<string>('')

// 项目数据
const projects = ref([
  {
    id: 'proj-001',
    name: '智能交通系统',
    domain: 'SysML'
  },
  {
    id: 'proj-002', 
    name: '企业数据平台',
    domain: 'UML'
  }
])

// 状态机数据
const currentStates = ref([
  { id: 'idle', name: '空闲', x: 100, y: 100 },
  { id: 'processing', name: '处理中', x: 300, y: 100 },
  { id: 'completed', name: '完成', x: 500, y: 100 }
])

const currentTransitions = ref([
  { id: 't1', from: 'idle', to: 'processing', trigger: 'start' },
  { id: 't2', from: 'processing', to: 'completed', trigger: 'finish' }
])

// 时序图数据
const sequenceLifelines = ref([
  { id: 'user', name: '用户', type: 'actor' },
  { id: 'system', name: '系统', type: 'boundary' },
  { id: 'database', name: '数据库', type: 'entity' }
])

const sequenceMessages = ref([
  { id: 'm1', from: 'user', to: 'system', message: '登录请求', timestamp: 1000 },
  { id: 'm2', from: 'system', to: 'database', message: '验证用户', timestamp: 1500 },
  { id: 'm3', from: 'database', to: 'system', message: '返回结果', timestamp: 2000 }
])

// 活动图数据
const activityNodes = ref([
  { id: 'start', name: '开始', type: 'start', x: 100, y: 50 },
  { id: 'validate', name: '验证输入', type: 'activity', x: 100, y: 150 },
  { id: 'decision', name: '是否有效？', type: 'decision', x: 100, y: 250 }
])

const activityFlows = ref([
  { id: 'f1', from: 'start', to: 'validate' },
  { id: 'f2', from: 'validate', to: 'decision' }
])

// 时间轴事件数据
const userEvents = ref([
  { id: 'u1', type: '点击', timestamp: 500, description: '用户点击登录按钮' },
  { id: 'u2', type: '输入', timestamp: 1200, description: '用户输入用户名' },
  { id: 'u3', type: '提交', timestamp: 2500, description: '用户提交表单' }
])

const systemEvents = ref([
  { id: 's1', type: '验证', startTime: 1000, endTime: 1800, duration: 800, description: '系统验证用户输入' },
  { id: 's2', type: '查询', startTime: 2000, endTime: 3200, duration: 1200, description: '查询用户信息' },
  { id: 's3', type: '响应', startTime: 3500, endTime: 4000, duration: 500, description: '返回登录结果' }
])

const dataEvents = ref([
  { id: 'd1', type: 'SQL', timestamp: 2100, description: 'SELECT用户信息' },
  { id: 'd2', type: '缓存', timestamp: 3000, description: '更新用户缓存' }
])

// 性能指标
const performanceMetrics = reactive({
  avgResponseTime: 1250,
  responseTimeTrend: 'up',
  throughput: 156,
  throughputTrend: 'down',
  errorRate: 0.02,
  errorRateTrend: 'stable'
})

// 状态覆盖率
const stateCoverage = reactive({
  percentage: 85,
  covered: 17,
  total: 20,
  uncovered: 3,
  states: [
    { id: 's1', name: '用户认证', coverage: 95 },
    { id: 's2', name: '数据处理', coverage: 88 },
    { id: 's3', name: '错误处理', coverage: 45 },
    { id: 's4', name: '日志记录', coverage: 92 }
  ]
})

// 异常检测
const anomalies = ref([
  {
    id: 'a1',
    severity: '警告',
    timestamp: 3500,
    description: '响应时间超过阈值(2000ms)',
    suggestion: '检查数据库查询性能'
  }
])

// 计算属性
const totalEvents = computed(() => {
  return userEvents.value.length + systemEvents.value.length + dataEvents.value.length
})

const stateTransitions = computed(() => {
  return Math.floor(simulationTime.value / 1000) * 3
})

const memoryUsage = computed(() => {
  return Math.round(128 + (simulationTime.value / 1000) * 2.5)
})

// 方法
const toggleSimulation = () => {
  if (simulationState.value === 'playing') {
    simulationState.value = 'paused'
    ElMessage.info('仿真已暂停')
  } else {
    simulationState.value = 'playing'
    ElMessage.success('仿真已开始')
    startSimulation()
  }
}

const resetSimulation = () => {
  simulationState.value = 'stopped'
  simulationTime.value = 0
  currentActiveState.value = 'idle'
  ElMessage.info('仿真已重置')
}

const stepSimulation = () => {
  simulationTime.value = Math.min(simulationTime.value + 500, totalSimulationTime.value)
  updateSimulationState()
  ElMessage.info(`仿真前进到 ${simulationTime.value}ms`)
}

const startSimulation = () => {
  if (simulationState.value === 'playing') {
    setTimeout(() => {
      simulationTime.value = Math.min(simulationTime.value + 100 * simulationSpeed.value, totalSimulationTime.value)
      updateSimulationState()
      
      if (simulationTime.value < totalSimulationTime.value && simulationState.value === 'playing') {
        startSimulation()
      } else if (simulationTime.value >= totalSimulationTime.value) {
        simulationState.value = 'stopped'
        ElMessage.success('仿真已完成')
      }
    }, 100)
  }
}

const updateSimulationState = () => {
  // 根据时间更新当前状态
  if (simulationTime.value < 1000) {
    currentActiveState.value = 'idle'
  } else if (simulationTime.value < 3000) {
    currentActiveState.value = 'processing'
  } else {
    currentActiveState.value = 'completed'
  }
}

const jumpToTime = (timestamp: number) => {
  simulationTime.value = timestamp
  updateSimulationState()
  ElMessage.info(`跳转到时间点: ${timestamp}ms`)
}

const getEventTypeColor = (type: string) => {
  switch (type) {
    case '点击': return 'primary'
    case '验证': return 'warning'
    case 'SQL': return 'info'
    default: return 'default'
  }
}

const getTrendClass = (trend: string) => {
  switch (trend) {
    case 'up': return 'trend-up'
    case 'down': return 'trend-down'
    case 'stable': return 'trend-stable'
    default: return ''
  }
}

const getTrendIcon = (trend: string) => {
  switch (trend) {
    case 'up': return 'ArrowUp'
    case 'down': return 'ArrowDown'
    case 'stable': return 'Delete'
    default: return 'Delete'
  }
}

const getAnomalyTypeColor = (severity: string) => {
  switch (severity) {
    case '严重': return 'danger'
    case '警告': return 'warning'
    case '提示': return 'info'
    default: return 'default'
  }
}

const formatTime = (ms: number) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const secondsStr = (seconds % 60).toString()
  const paddedSeconds = secondsStr.length === 1 ? '0' + secondsStr : secondsStr
  return `${minutes}:${paddedSeconds}`
}

// 事件处理
const handleStateSelected = (state: any) => {
  ElMessage.info(`选择状态: ${state.name}`)
}

const handleTransitionCreated = (transition: any) => {
  currentTransitions.value.push(transition)
  ElMessage.success('状态转换已创建')
}

const handleMessageSelected = (message: any) => {
  selectedEvent.value = {
    type: '消息',
    timestamp: message.timestamp,
    description: message.message,
    params: { from: message.from, to: message.to }
  }
}

const handleActivitySelected = (activity: any) => {
  currentActivity.value = activity.id
  ElMessage.info(`选择活动: ${activity.name}`)
}

// 工具栏操作
const addState = () => {
  ElMessage.info('添加新状态')
}

const addTransition = () => {
  ElMessage.info('添加状态转换')
}

const addLifeline = () => {
  ElMessage.info('添加生命线')
}

const addMessage = () => {
  ElMessage.info('添加消息')
}

const addActivity = () => {
  ElMessage.info('添加活动')
}

const addDecision = () => {
  ElMessage.info('添加判断节点')
}

const zoomIn = () => {
  ElMessage.info('放大时间轴')
}

const zoomOut = () => {
  ElMessage.info('缩小时间轴')
}

const resetZoom = () => {
  ElMessage.info('重置时间轴缩放')
}

const exportReport = () => {
  ElMessage.success('行为分析报告已导出')
}

const saveSnapshot = () => {
  ElMessage.success('仿真快照已保存')
}

const generateTest = () => {
  ElMessage.success('测试用例已生成')
}

// 初始化
onMounted(() => {
  if (projects.value[0]) {
    currentProject.value = projects.value[0].id
  }
  currentActiveState.value = 'idle'
})
</script>

<style scoped lang="scss">
.behavior-analyst-workspace {
  display: grid;
  grid-template-rows: auto 1fr auto;
  height: 100vh;
  background: #f0f2f5;

  // 顶部仿真控制栏
  .simulation-control-panel {
    background: white;
    padding: 16px 20px;
    border-bottom: 2px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .project-context {
      display: flex;
      align-items: center;
      gap: 16px;

      .project-selector {
        width: 180px;
      }

      .behavior-title {
        h2 {
          margin: 0;
          color: #1f2937;
          font-size: 22px;
        }

        .subtitle {
          color: #6b7280;
          font-size: 13px;
          font-style: italic;
        }
      }
    }

    .simulation-controls {
      display: flex;
      align-items: center;
      gap: 20px;

      .simulation-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .control-label {
          font-size: 14px;
          color: #6b7280;
        }

        .sim-selector {
          width: 160px;
        }
      }

      .simulation-actions {
        display: flex;
        align-items: center;
        gap: 16px;

        .speed-control {
          display: flex;
          align-items: center;
          gap: 8px;

          .speed-label {
            font-size: 14px;
            color: #6b7280;
          }

          .speed-value {
            font-size: 14px;
            color: #374151;
            min-width: 24px;
          }
        }
      }
    }
  }

  // 主分析区域
  .behavior-main-workspace {
    display: grid;
    grid-template-columns: 300px 1fr 280px;
    gap: 1px;
    background: #e5e7eb;

    // 左侧行为模型面板
    .behavior-models-panel {
      background: white;

      .model-tabs {
        height: 100%;

        .state-machine-view,
        .sequence-diagram-view,
        .activity-diagram-view {
          height: 100%;
          display: flex;
          flex-direction: column;

          .toolbar {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            gap: 8px;
          }
        }
      }
    }

    // 中央时间轴面板
    .timeline-analysis-panel {
      background: white;
      display: flex;
      flex-direction: column;

      .timeline-controls {
        padding: 16px;
        border-bottom: 1px solid #e5e7eb;
        background: #f9fafb;

        .timeline-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          h4 {
            margin: 0;
            color: #374151;
          }

          .timeline-tools {
            display: flex;
            gap: 8px;
          }
        }

        .time-range {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 14px;
          color: #6b7280;

          .current-time {
            font-weight: 600;
            color: #059669;
          }
        }
      }

      .timeline-view {
        flex: 1;
        position: relative;
        overflow-x: auto;
        min-height: 300px;

        .timeline-layers {
          .timeline-layer {
            height: 60px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;

            &.user-layer {
              background: #fef3c7;
            }

            &.system-layer {
              background: #dbeafe;
            }

            &.data-layer {
              background: #dcfce7;
            }

            .layer-label {
              width: 80px;
              padding: 0 12px;
              font-size: 12px;
              font-weight: 600;
              color: #374151;
              border-right: 1px solid #e5e7eb;
            }

            .layer-track {
              flex: 1;
              position: relative;
              height: 40px;

              .timeline-event {
                position: absolute;
                height: 24px;
                top: 8px;
                cursor: pointer;

                &.user-event .event-marker {
                  background: #d97706;
                  color: white;
                  padding: 2px 6px;
                  border-radius: 4px;
                  font-size: 10px;
                }

                &.system-event .event-bar {
                  background: #2563eb;
                  color: white;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  padding: 0 8px;
                  border-radius: 4px;
                  font-size: 10px;
                  white-space: nowrap;
                }

                &.data-event .event-marker {
                  background: #059669;
                  color: white;
                  padding: 2px 6px;
                  border-radius: 4px;
                  font-size: 10px;
                }
              }
            }
          }
        }

        .time-indicator {
          position: absolute;
          top: 0;
          bottom: 0;
          width: 2px;
          background: #dc2626;
          z-index: 10;
          transition: left 0.1s ease;

          &::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 8px solid #dc2626;
          }
        }
      }

      .event-details {
        height: 200px;
        border-top: 1px solid #e5e7eb;
        overflow-y: auto;

        .event-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .event-content {
          .detail-item {
            margin-bottom: 8px;

            .detail-label {
              font-weight: 600;
              color: #374151;
            }

            .detail-value {
              color: #6b7280;
            }

            .detail-json {
              background: #f3f4f6;
              padding: 8px;
              border-radius: 4px;
              font-size: 12px;
              margin-top: 4px;
            }
          }
        }
      }
    }

    // 右侧性能分析面板
    .performance-analysis-panel {
      background: white;
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .metrics-card {
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .metrics-content {
          .metric-group {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .metric-item {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .metric-label {
                font-size: 13px;
                color: #6b7280;
              }

              .metric-value {
                font-weight: 600;
                color: #374151;
              }

              .metric-trend {
                &.trend-up {
                  color: #dc2626;
                }

                &.trend-down {
                  color: #059669;
                }

                &.trend-stable {
                  color: #6b7280;
                }
              }
            }
          }
        }
      }

      .coverage-card {
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .coverage-content {
          .coverage-chart {
            margin-bottom: 16px;

            .chart-placeholder {
              height: 120px;
              display: flex;
              align-items: center;
              justify-content: center;

              .coverage-summary {
                text-align: center;

                .coverage-percentage {
                  font-size: 32px;
                  font-weight: 700;
                  color: #059669;
                }

                .coverage-details {
                  font-size: 12px;
                  color: #6b7280;
                }
              }
            }
          }

          .coverage-list {
            .coverage-item {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;

              .state-name {
                font-size: 12px;
                color: #374151;
                min-width: 80px;
              }
            }
          }
        }
      }

      .anomaly-card {
        flex: 1;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .anomaly-content {
          .no-anomaly {
            text-align: center;
            padding: 20px;
            color: #6b7280;

            .success-icon {
              font-size: 24px;
              color: #059669;
              margin-bottom: 8px;
            }
          }

          .anomaly-list {
            .anomaly-item {
              padding: 12px 0;
              border-bottom: 1px solid #f1f5f9;

              .anomaly-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .anomaly-time {
                  font-size: 12px;
                  color: #9ca3af;
                }
              }

              .anomaly-description {
                font-size: 13px;
                color: #374151;
                margin-bottom: 4px;
              }

              .anomaly-suggestion {
                font-size: 12px;
                color: #6b7280;
                font-style: italic;
              }
            }
          }
        }
      }
    }
  }

  // 底部数据监控条
  .data-monitoring-bar {
    background: #1f2937;
    color: white;
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .monitoring-metrics {
      display: flex;
      gap: 24px;

      .metric-display {
        display: flex;
        align-items: center;
        gap: 4px;

        .metric-title {
          font-size: 12px;
          color: #9ca3af;
        }

        .metric-data {
          font-size: 14px;
          font-weight: 600;
          color: #10b981;
        }
      }
    }

    .monitoring-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 深度样式覆盖
:deep(.el-tabs) {
  height: 100%;

  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__content {
    height: calc(100% - 40px);
    overflow: hidden;
  }

  .el-tab-pane {
    height: 100%;
  }
}

:deep(.el-card) {
  border: 1px solid #e5e7eb;
  box-shadow: none;

  .el-card__header {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 12px 16px;
  }

  .el-card__body {
    padding: 16px;
  }
}

:deep(.el-progress) {
  flex: 1;

  .el-progress__text {
    font-size: 12px;
  }
}

// ========== 深色主题支持 ==========
:global(.theme-dark) .behavior-analyst-workspace {
  background: linear-gradient(135deg, #111827 0%, #0f172a 50%, #111827 100%);
  color: #f9fafb;

  .behavior-header {
    background: #1f2937;
    border-bottom: 2px solid #374151;

    .behavior-title {
      h2 {
        color: #10b981;
        text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
      }

      .subtitle {
        color: #d1d5db;
      }
    }

    .simulation-controls {
      background: #374151;
      border: 1px solid #4b5563;

      .control-group {
        .el-button {
          background: #4b5563;
          border-color: #6b7280;
          color: #f9fafb;

          &:hover {
            background: #6b7280;
            color: #ffffff;
          }

          &.el-button--success {
            background: #10b981;
            border-color: #10b981;

            &:hover {
              background: #059669;
            }
          }

          &.el-button--danger {
            background: #ef4444;
            border-color: #ef4444;

            &:hover {
              background: #dc2626;
            }
          }
        }

        .speed-slider {
          .el-slider__runway {
            background: #4b5563;
          }

          .el-slider__bar {
            background: #10b981;
          }
        }
      }

      .simulation-time {
        color: #d1d5db;
        font-weight: 600;
      }
    }
  }

  .behavior-main-workspace {
    // 左侧行为模型面板
    .behavior-models-panel {
      background: #1f2937;
      border-right: 2px solid #374151;

      .models-card {
        background: #1f2937;

        .card-header {
          background: #374151;
          border-bottom: 1px solid #4b5563;
          color: #f9fafb;
        }
      }
    }

    // 中央时间轴分析
    .timeline-analysis-area {
      background: #1f2937;

      .timeline-header {
        background: #374151;
        border-bottom: 1px solid #4b5563;

        .timeline-controls {
          .el-button {
            background: #4b5563;
            border-color: #6b7280;
            color: #f9fafb;

            &:hover {
              background: #6b7280;
              color: #ffffff;
            }

            &.el-button--primary {
              background: #10b981;
              border-color: #10b981;

              &:hover {
                background: #059669;
              }
            }
          }
        }

        .current-time {
          color: #d1d5db;
          font-weight: 600;
        }
      }

      .timeline-content {
        background: #111827;

        .layer-header {
          background: #374151;
          color: #f9fafb;
          font-weight: 600;
        }

        .layer-content {
          background: #1f2937;

          .interaction-event,
          .system-event,
          .data-event {
            background: #374151;
            border: 1px solid #4b5563;
            color: #f9fafb;

            &:hover {
              background: #475569;
              border-color: #6b7280;
            }
          }
        }
      }
    }

    // 右侧性能分析面板
    .performance-analysis-panel {
      background: #1f2937;

      .metrics-card,
      .coverage-card,
      .anomaly-card {
        background: #1f2937;

        .card-header {
          background: #374151;
          border-bottom: 1px solid #4b5563;
          color: #f9fafb;
          font-weight: 700;
        }

        .metrics-content,
        .coverage-content,
        .anomaly-content {
          .realtime-metrics .metric-item,
          .coverage-item,
          .anomaly-item {
            border-bottom: 1px solid #374151;

            .metric-name,
            .metric-label,
            .state-name,
            .anomaly-description {
              color: #f3f4f6;
              font-weight: 500;
            }

            .metric-value,
            .metric-current {
              color: #10b981;
              font-weight: 700;
            }

            .trend-up {
              color: #10b981;
            }

            .trend-down {
              color: #ef4444;
            }

            .trend-stable {
              color: #9ca3af;
            }
          }

          .coverage-percentage {
            color: #10b981;
          }

          .coverage-details,
          .anomaly-time,
          .anomaly-suggestion {
            color: #9ca3af;
          }

          .no-anomaly {
            color: #d1d5db;

            .success-icon {
              color: #10b981;
            }
          }
        }
      }
    }
  }

  // 底部数据监控条
  .data-monitoring-bar {
    background: #1f2937;
    border-top: 1px solid #374151;

    .monitoring-metrics .metric-display {
      .metric-title {
        color: #d1d5db;
      }

      .metric-data {
        color: #10b981;
        font-weight: 700;
      }
    }
  }
}

// 深度样式覆盖 - 深色主题
:global(.theme-dark) :deep(.el-card) {
  background: #1f2937;
  border: 1px solid #374151;

  .el-card__header {
    background: #374151;
    border-bottom: 1px solid #4b5563;
    color: #f9fafb;
  }

  .el-card__body {
    background: #1f2937;
    color: #f9fafb;
  }
}

:global(.theme-dark) :deep(.el-tabs) {
  .el-tabs__header {
    background: #374151;
  }

  .el-tabs__nav-wrap {
    background: #374151;
  }

  .el-tabs__item {
    color: #d1d5db;
    font-weight: 500;

    &.is-active {
      color: #10b981;
      font-weight: 700;
    }

    &:hover {
      color: #f9fafb;
    }
  }

  .el-tabs__active-bar {
    background-color: #10b981;
  }

  .el-tabs__content {
    background: #1f2937;
  }
}

:global(.theme-dark) :deep(.el-progress) {
  .el-progress__text {
    color: #f9fafb;
    font-weight: 600;
  }

  .el-progress-bar__outer {
    background: #374151;
  }

  .el-progress-bar__inner {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  }
}

:global(.theme-dark) :deep(.el-slider) {
  .el-slider__runway {
    background: #4b5563;
  }

  .el-slider__bar {
    background: #10b981;
  }

  .el-slider__button {
    border-color: #10b981;
  }
}

:global(.theme-dark) :deep(.el-button) {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;

  &:hover {
    background: #6b7280;
    border-color: #9ca3af;
    color: #ffffff;
  }

  &.el-button--success {
    background: #10b981;
    border-color: #10b981;
    color: #ffffff;

    &:hover {
      background: #059669;
      border-color: #059669;
    }
  }

  &.el-button--danger {
    background: #ef4444;
    border-color: #ef4444;
    color: #ffffff;

    &:hover {
      background: #dc2626;
      border-color: #dc2626;
    }
  }
}

// 深度样式覆盖
:deep(.el-tabs) {
  height: 100%;

  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__content {
    height: calc(100% - 40px);
    overflow: hidden;
  }

  .el-tab-pane {
    height: 100%;
  }
}

:deep(.el-card) {
  border: 1px solid #e5e7eb;
  box-shadow: none;

  .el-card__header {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 12px 16px;
  }

  .el-card__body {
    padding: 16px;
  }
}

:deep(.el-progress) {
  flex: 1;

  .el-progress__text {
    font-size: 12px;
  }
}
</style> 