<template>
  <div class="connection-chart">
    <el-card>
      <template #header>
        <div class="chart-header">
          <span>{{ title }}</span>
          <div class="chart-actions">
            <el-button size="small" @click="refreshConnections">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>
      <div ref="chartContainer" class="chart-container"></div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { Refresh } from '@element-plus/icons-vue'

interface Connection {
  id: string
  source: string
  target: string
  type: string
  weight?: number
  status?: 'active' | 'inactive' | 'error'
}

interface Node {
  id: string
  name: string
  type: string
  x?: number
  y?: number
  size?: number
}

interface Props {
  connections: Connection[]
  role?: string
  height?: number
  interactive?: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  interactive: true,
  role: 'viewer',
  title: '连接图表'
})

const emit = defineEmits<{
  'node-click': [node: Node]
  'connection-click': [connection: Connection]
}>()

// 响应式数据
const chartRef = ref()
const loading = ref(false)
const selectedNode = ref<Node | null>(null)

// 计算属性
const chartHeight = computed(() => `${props.height}px`)

const nodes = computed(() => {
  const nodeIds: string[] = []
  props.connections.forEach((conn: Connection) => {
    if (nodeIds.indexOf(conn.source) === -1) {
      nodeIds.push(conn.source)
    }
    if (nodeIds.indexOf(conn.target) === -1) {
      nodeIds.push(conn.target)
    }
  })
  
  return nodeIds.map((id: string) => ({
    id,
    name: id,
    type: 'default',
    size: 10 + Math.random() * 20
  }))
})

const processedConnections = computed(() => {
  return props.connections.map((conn: Connection) => ({
    ...conn,
    value: conn.weight || 1,
    lineStyle: {
      color: getConnectionColor(conn.type),
      width: Math.max(1, (conn.weight || 1) * 2)
    }
  }))
})

// 方法
const getConnectionColor = (type: string) => {
  const colors = {
    'strong': '#67C23A',
    'medium': '#E6A23C', 
    'weak': '#F56C6C',
    'default': '#909399'
  }
  return colors[type as keyof typeof colors] || colors.default
}

const initChart = () => {
  if (!chartRef.value) return
  
  // 这里可以集成图表库（如ECharts、D3等）
  console.log('初始化连接图表', {
    nodes: nodes.value.length,
    connections: processedConnections.value.length
  })
}

const updateChart = () => {
  if (!chartRef.value) return
  
  // 更新图表数据
  console.log('更新连接图表')
}

const handleNodeClick = (node: Node) => {
  selectedNode.value = node
  emit('node-click', node)
}

const handleConnectionClick = (connection: Connection) => {
  emit('connection-click', connection)
}

const refreshChart = () => {
  loading.value = true
  setTimeout(() => {
    updateChart()
    loading.value = false
  }, 1000)
}

const refreshConnections = () => {
  refreshChart()
}

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  // 清理图表资源
  console.log('连接图表已卸载')
})

// 监听数据变化
watch(() => props.connections, updateChart, { deep: true })
watch(() => props.role, updateChart)
</script>

<style scoped>
.connection-chart {
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-container {
  height: 400px;
  width: 100%;
}
</style> 