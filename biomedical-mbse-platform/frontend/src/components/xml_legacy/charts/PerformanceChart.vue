<template>
  <div class="performance-chart">
    <el-card>
      <template #header>
        <div class="chart-header">
          <span>{{ title }}</span>
          <el-button-group>
            <el-button 
              v-for="period in timePeriods"
              :key="period.value"
              :type="selectedPeriod === period.value ? 'primary' : ''"
              size="small"
              @click="selectedPeriod = period.value"
            >
              {{ period.label }}
            </el-button>
          </el-button-group>
        </div>
      </template>
      <div ref="chartContainer" class="chart-container"></div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

interface PerformanceData {
  time: string
  cpu: number
  memory: number
  network: number
  formattedTime?: string
}

interface Props {
  title?: string
  data?: PerformanceData[]
  height?: number
  period?: 'hour' | 'day' | 'week' | 'month'
}

const props = withDefaults(defineProps<Props>(), {
  height: 300,
  period: 'hour'
})

const emit = defineEmits<{
  'period-change': [period: string]
}>()

// 使用computed来提供默认值
const chartData = computed(() => props.data || [])
const chartTitle = computed(() => props.title || '性能监控图表')

const chartContainer = ref<HTMLElement | undefined>(undefined)
const selectedPeriod = ref('1h')
let chartInstance: echarts.ECharts | null = null

const timePeriods = [
  { value: '1h', label: '1小时' },
  { value: '6h', label: '6小时' },
  { value: '24h', label: '24小时' },
  { value: '7d', label: '7天' }
]

const chartHeight = computed(() => `${props.height}px`)

const processedData = computed(() => {
  return props.data?.map((item: PerformanceData) => ({
    ...item,
    formattedTime: formatTime(item.time),
    displayValue: Number(item.cpu).toFixed(2)
  })) || []
})

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  switch (props.period) {
    case 'hour':
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    case 'day':
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    case 'week':
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    case 'month':
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    default:
      return date.toLocaleString('zh-CN')
  }
}

const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['CPU使用率', '内存使用率', '网络I/O']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: processedData.value.map((item: PerformanceData) => item.formattedTime)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        smooth: true,
        data: processedData.value.map((item: PerformanceData) => item.cpu),
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '内存使用率',
        type: 'line',
        smooth: true,
        data: processedData.value.map((item: PerformanceData) => item.memory),
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '网络I/O',
        type: 'line',
        smooth: true,
        data: processedData.value.map((item: PerformanceData) => item.network),
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  }
  
  chartInstance.setOption(option)
}

const updateChart = () => {
  if (chartInstance) {
    // 模拟不同时间段的数据更新
    initChart()
  }
}

const refreshChart = () => {
  // 模拟刷新
  setTimeout(() => {
    updateChart()
  }, 1000)
}

const onPeriodChange = (period: string) => {
  emit('period-change', period)
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})

watch(() => selectedPeriod.value, updateChart)
watch(() => processedData, updateChart, { deep: true })
</script>

<style scoped>
.performance-chart {
  height: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  width: 100%;
}
</style> 