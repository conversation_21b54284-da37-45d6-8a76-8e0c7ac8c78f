<template>
  <div class="data-modeler-workspace">
    <!-- 顶部数据架构概览 -->
    <div class="data-architecture-overview">
      <div class="project-context">
        <el-select v-model="currentProject" placeholder="选择项目" size="small" class="project-selector">
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
        <div class="data-title">
          <h2>数据建模师工作台</h2>
          <span class="subtitle">Enterprise Data Architecture & Governance</span>
        </div>
      </div>

      <!-- 数据域分类视图 -->
      <div class="data-domains">
        <div class="domain-card" v-for="domain in dataDomains" :key="domain.id" 
             :class="{ active: selectedDomain === domain.id }"
             @click="selectDomain(domain.id)">
          <div class="domain-header">
            <el-icon class="domain-icon" :style="{ color: domain.color }">
              <component :is="domain.icon" />
            </el-icon>
            <span class="domain-name">{{ domain.name }}</span>
          </div>
          <div class="domain-stats">
            <div class="stat-item">
              <span class="stat-value">{{ domain.entities }}</span>
              <span class="stat-label">实体</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ domain.quality }}%</span>
              <span class="stat-label">质量</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主数据工作区 -->
    <div class="data-main-workspace">
      <!-- 左侧数据目录树 -->
      <div class="data-catalog-panel">
        <el-card class="catalog-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><FolderOpened /></el-icon>
              <span>数据目录</span>
              <el-button size="small" type="primary" icon="Plus" @click="addDataEntity">新增</el-button>
            </div>
          </template>
          <div class="catalog-filters">
            <el-input v-model="catalogSearch" placeholder="搜索数据实体" size="small" prefix-icon="Search" />
            <el-select v-model="catalogFilter" placeholder="筛选类型" size="small">
              <el-option label="全部" value="all" />
              <el-option label="核心实体" value="core" />
              <el-option label="主数据" value="master" />
              <el-option label="参考数据" value="reference" />
            </el-select>
          </div>
          <el-tree 
            :data="dataCatalogTree"
            :props="treeProps"
            node-key="id"
            default-expand-all
            @node-click="handleEntityClick"
            class="data-catalog-tree"
          >
            <template #default="{ node, data }">
              <div class="catalog-node">
                <el-icon class="node-icon" :style="{ color: getEntityTypeColor(data.type) }">
                  <component :is="getEntityIcon(data.type)" />
                </el-icon>
                <span class="node-label">{{ data.label }}</span>
                <div class="node-badges">
                  <el-tag v-if="data.isPrimaryKey" type="danger" size="small">PK</el-tag>
                  <el-tag v-if="data.hasIssues" type="warning" size="small">!</el-tag>
                </div>
              </div>
            </template>
          </el-tree>
        </el-card>
      </div>

      <!-- 中央数据建模画布 -->
      <div class="data-modeling-canvas">
        <div class="canvas-toolbar">
          <el-button-group>
            <el-button @click="switchView('erd')" :type="currentView === 'erd' ? 'primary' : 'default'">
              <el-icon><ChatRound /></el-icon>实体关系图
            </el-button>
            <el-button @click="switchView('dataflow')" :type="currentView === 'dataflow' ? 'primary' : 'default'">
              <el-icon><Position /></el-icon>数据流图
            </el-button>
            <el-button @click="switchView('lineage')" :type="currentView === 'lineage' ? 'primary' : 'default'">
              <el-icon><Help /></el-icon>血缘图谱
            </el-button>
          </el-button-group>
          
          <div class="canvas-actions">
            <el-button icon="Tools" @click="autoLayout">自动布局</el-button>
            <el-button icon="Download" @click="exportModel">导出模型</el-button>
            <el-button type="primary" icon="Plus" @click="addRelationship">添加关系</el-button>
          </div>
        </div>

        <div class="modeling-canvas-content" ref="canvasRef">
          <DataModelingCanvas 
            :view-type="currentView"
            :entities="selectedDomainEntities"
            :relationships="entityRelationships"
            :data-flows="dataFlowConnections"
            @entity-selected="handleEntitySelected"
            @relationship-created="handleRelationshipCreated"
          />
        </div>

        <!-- 底部数据统计条 -->
        <div class="data-stats-bar">
          <div class="stats-group">
            <div class="stat-display">
              <span class="stat-label">当前域实体：</span>
              <span class="stat-value">{{ selectedDomainEntities.length }}</span>
            </div>
            <div class="stat-display">
              <span class="stat-label">关系数量：</span>
              <span class="stat-value">{{ entityRelationships.length }}</span>
            </div>
            <div class="stat-display">
              <span class="stat-label">平均质量分：</span>
              <span class="stat-value">{{ averageQualityScore }}%</span>
            </div>
          </div>
          <div class="quality-indicator">
            <el-progress 
              :percentage="overallDataQuality" 
              :status="overallDataQuality > 80 ? 'success' : overallDataQuality > 60 ? 'warning' : 'exception'"
              :stroke-width="8"
            />
          </div>
        </div>
      </div>

      <!-- 右侧治理控制面板 -->
      <div class="governance-control-panel">
        <!-- 实体详情 -->
        <el-card class="entity-details-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Files /></el-icon>
              <span>{{ selectedEntity ? '实体详情' : '选择实体' }}</span>
            </div>
          </template>
          <div v-if="selectedEntity" class="entity-content">
            <div class="entity-basic-info">
              <div class="info-row">
                <span class="info-label">实体名称：</span>
                <span class="info-value">{{ selectedEntity.name }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">业务描述：</span>
                <span class="info-value">{{ selectedEntity.description }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">数据域：</span>
                <el-tag :type="getDomainTagType(selectedEntity.domain)" size="small">
                  {{ selectedEntity.domainName }}
                </el-tag>
              </div>
              <div class="info-row">
                <span class="info-label">治理级别：</span>
                <el-rate v-model="selectedEntity.governanceLevel" :max="5" size="small" disabled />
              </div>
            </div>
            
            <div class="fields-section">
              <h5>字段信息 ({{ selectedEntity.fields?.length || 0 }})</h5>
              <div class="fields-list">
                <div v-for="field in selectedEntity.fields" :key="field.id" class="field-row">
                  <div class="field-info">
                    <span class="field-name">{{ field.name }}</span>
                    <span class="field-type">{{ field.type }}</span>
                  </div>
                  <div class="field-badges">
                    <el-icon v-if="field.isPrimaryKey" class="pk-icon" title="主键"><Key /></el-icon>
                    <el-icon v-if="field.isForeignKey" class="fk-icon" title="外键"><Link /></el-icon>
                    <el-icon v-if="field.isRequired" class="required-icon" title="必填"><Lock /></el-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="选择实体查看详情" />
        </el-card>

        <!-- 数据质量监控 -->
        <el-card class="quality-monitoring-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><View /></el-icon>
              <span>质量监控</span>
            </div>
          </template>
          <div class="quality-content">
            <div class="quality-overview">
              <div class="quality-score">
                <div class="score-circle">{{ currentQualityScore }}%</div>
                <div class="score-label">综合质量分</div>
              </div>
              <div class="quality-trend">
                <el-icon :class="getQualityTrendClass(qualityTrend)">
                  <component :is="getQualityTrendIcon(qualityTrend)" />
                </el-icon>
                <span class="trend-text">{{ qualityTrend === 'up' ? '质量提升' : qualityTrend === 'down' ? '质量下降' : '质量稳定' }}</span>
              </div>
            </div>

            <div class="quality-dimensions">
              <div v-for="dimension in qualityDimensions" :key="dimension.name" class="dimension-item">
                <div class="dimension-header">
                  <span class="dimension-name">{{ dimension.name }}</span>
                  <span class="dimension-score">{{ dimension.score }}%</span>
                </div>
                <el-progress 
                  :percentage="dimension.score" 
                  :status="getDimensionStatus(dimension.score)"
                  :stroke-width="6"
                />
              </div>
            </div>

            <div class="quality-issues">
              <h5>质量问题 ({{ qualityIssues.length }})</h5>
              <div class="issues-list">
                <div v-for="issue in qualityIssues" :key="issue.id" class="issue-item">
                  <el-tag :type="getIssueTypeColor(issue.severity)" size="small">
                    {{ issue.severity }}
                  </el-tag>
                  <span class="issue-description">{{ issue.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 治理策略 -->
        <el-card class="governance-policies-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Grid /></el-icon>
              <span>治理策略</span>
            </div>
          </template>
          <div class="governance-content">
            <div class="policy-categories">
              <el-tabs v-model="activePolicyTab" size="small">
                <el-tab-pane label="数据标准" name="standards">
                  <div class="standards-list">
                    <div v-for="standard in dataStandards" :key="standard.id" class="standard-item">
                      <div class="standard-header">
                        <span class="standard-name">{{ standard.name }}</span>
                        <el-switch v-model="standard.enabled" size="small" />
                      </div>
                      <div class="standard-description">{{ standard.description }}</div>
                    </div>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="访问控制" name="access">
                  <div class="access-rules">
                    <div v-for="rule in accessRules" :key="rule.id" class="access-rule">
                      <div class="rule-header">
                        <span class="rule-role">{{ rule.role }}</span>
                        <el-tag :type="getAccessTypeColor(rule.permission)" size="small">
                          {{ rule.permission }}
                        </el-tag>
                      </div>
                      <div class="rule-scope">范围: {{ rule.scope }}</div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="生命周期" name="lifecycle">
                  <div class="lifecycle-stages">
                    <div v-for="stage in lifecycleStages" :key="stage.id" class="lifecycle-stage">
                      <div class="stage-name">{{ stage.name }}</div>
                      <div class="stage-duration">{{ stage.duration }}天</div>
                      <el-progress :percentage="stage.completion" :stroke-width="4" />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  FolderOpened, Plus, Search, ChatRound, Position, Help, 
  Tools, Download, Files, Key, Lock, View, 
  Grid, Setting
} from '@element-plus/icons-vue'
import DataModelingCanvas from './DataModelingCanvas.vue'

// 项目相关
const currentProject = ref('proj-001')
const projects = ref([
  { id: 'proj-001', name: '电商平台系统', domain: '零售电商' },
  { id: 'proj-002', name: '金融风控系统', domain: '金融服务' },
  { id: 'proj-003', name: '物联网平台', domain: '工业物联网' }
])

const currentProjectInfo = computed(() => {
  return projects.value.find(p => p.id === currentProject.value)
})

// 数据域管理
const selectedDomain = ref('customer')
const dataDomains = ref([
  {
    id: 'customer',
    name: '客户域',
    icon: 'User',
    color: '#1890ff',
    entities: 12,
    quality: 92
  },
  {
    id: 'product',
    name: '产品域',
    icon: 'Box',
    color: '#52c41a',
    entities: 18,
    quality: 87
  },
  {
    id: 'order',
    name: '订单域',
    icon: 'Document',
    color: '#faad14',
    entities: 8,
    quality: 95
  },
  {
    id: 'payment',
    name: '支付域',
    icon: 'CreditCard',
    color: '#f5222d',
    entities: 6,
    quality: 88
  }
])

// 数据目录
const catalogSearch = ref('')
const catalogFilter = ref('all')
const dataCatalogTree = ref([
  {
    id: 'customer-domain',
    label: '客户域',
    type: 'domain',
    children: [
      {
        id: 'customer-info',
        label: '客户信息',
        type: 'entity',
        isPrimaryKey: true,
        hasIssues: false
      },
      {
        id: 'customer-profile',
        label: '客户画像',
        type: 'entity',
        isPrimaryKey: false,
        hasIssues: true
      }
    ]
  },
  {
    id: 'product-domain',
    label: '产品域',
    type: 'domain',
    children: [
      {
        id: 'product-info',
        label: '产品信息',
        type: 'entity',
        isPrimaryKey: true,
        hasIssues: false
      },
      {
        id: 'product-category',
        label: '产品分类',
        type: 'entity',
        isPrimaryKey: false,
        hasIssues: false
      },
      {
        id: 'product-inventory',
        label: '产品库存',
        type: 'entity',
        isPrimaryKey: false,
        hasIssues: true
      }
    ]
  },
  {
    id: 'order-domain',
    label: '订单域',
    type: 'domain',
    children: [
      {
        id: 'order-header',
        label: '订单头',
        type: 'entity',
        isPrimaryKey: true,
        hasIssues: false
      },
      {
        id: 'order-item',
        label: '订单明细',
        type: 'entity',
        isPrimaryKey: false,
        hasIssues: false
      }
    ]
  },
  {
    id: 'payment-domain',
    label: '支付域',
    type: 'domain',
    children: [
      {
        id: 'payment-record',
        label: '支付记录',
        type: 'entity',
        isPrimaryKey: true,
        hasIssues: false
      },
      {
        id: 'payment-gateway',
        label: '支付网关',
        type: 'entity',
        isPrimaryKey: false,
        hasIssues: false
      }
    ]
  }
])

const treeProps = {
  children: 'children',
  label: 'label'
}

// 建模画布
const currentView = ref('erd')
const canvasRef = ref()
const selectedEntity = ref<any>(null)
const selectedDomainEntities = ref<any[]>([])
const entityRelationships = ref<any[]>([])
const dataFlowConnections = ref<any[]>([])

// 数据质量
const currentQualityScore = ref(89)
const qualityTrend = ref('up')
const qualityDimensions = ref([
  { name: '完整性', score: 92 },
  { name: '准确性', score: 88 },
  { name: '一致性', score: 85 },
  { name: '及时性', score: 91 }
])

const qualityIssues = ref([
  { id: 1, severity: '高', description: '客户表存在空值字段' },
  { id: 2, severity: '中', description: '产品分类数据不一致' },
  { id: 3, severity: '低', description: '订单状态更新延迟' }
])

// 治理策略
const activePolicyTab = ref('standards')
const dataStandards = ref([
  { id: 1, name: '命名规范', description: '统一的数据命名约定', enabled: true },
  { id: 2, name: '数据类型标准', description: '标准化数据类型定义', enabled: true },
  { id: 3, name: '业务规则', description: '数据业务逻辑规范', enabled: false }
])

const accessRules = ref([
  { id: 1, role: '数据分析师', permission: '只读', scope: '客户域、产品域' },
  { id: 2, role: '系统管理员', permission: '读写', scope: '全部域' },
  { id: 3, role: '业务用户', permission: '受限读', scope: '业务报表' }
])

const lifecycleStages = ref([
  { id: 1, name: '数据采集', duration: 7, completion: 100 },
  { id: 2, name: '数据处理', duration: 3, completion: 75 },
  { id: 3, name: '数据存储', duration: 1, completion: 90 },
  { id: 4, name: '数据归档', duration: 30, completion: 30 }
])

// 计算属性
const averageQualityScore = computed(() => {
  if (qualityDimensions.value.length === 0) return 0
  const total = qualityDimensions.value.reduce((sum, dim) => sum + dim.score, 0)
  return Math.round(total / qualityDimensions.value.length)
})

const overallDataQuality = computed(() => {
  return averageQualityScore.value
})

// 方法
const selectDomain = (domainId: string) => {
  selectedDomain.value = domainId
  loadDomainEntities(domainId)
}

const loadDomainEntities = (domainId: string) => {
  // 根据不同域加载不同的实体数据
  switch (domainId) {
    case 'customer':
      selectedDomainEntities.value = [
        {
          id: 'customer-info',
          name: '客户信息',
          type: 'core',
          x: 150,
          y: 120,
          fields: [
            { id: 1, name: 'customer_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'customer_name', type: 'varchar(100)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 3, name: 'email', type: 'varchar(255)', isPrimaryKey: false, isForeignKey: false, isRequired: false },
            { id: 4, name: 'phone', type: 'varchar(20)', isPrimaryKey: false, isForeignKey: false, isRequired: false },
            { id: 5, name: 'address', type: 'text', isPrimaryKey: false, isForeignKey: false, isRequired: false },
            { id: 6, name: 'created_at', type: 'timestamp', isPrimaryKey: false, isForeignKey: false, isRequired: true }
          ]
        },
        {
          id: 'customer-profile',
          name: '客户画像',
          type: 'derived',
          x: 500,
          y: 120,
          fields: [
            { id: 1, name: 'profile_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'customer_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: true },
            { id: 3, name: 'age_group', type: 'varchar(20)', isPrimaryKey: false, isForeignKey: false, isRequired: false },
            { id: 4, name: 'income_level', type: 'varchar(20)', isPrimaryKey: false, isForeignKey: false, isRequired: false },
            { id: 5, name: 'preference_score', type: 'decimal(5,2)', isPrimaryKey: false, isForeignKey: false, isRequired: false }
          ]
        }
      ]
      // 设置客户域的实体关系
      entityRelationships.value = [
        {
          id: 'rel-1',
          x1: 350,
          y1: 180,
          x2: 500,
          y2: 180,
          fromEntity: 'customer-info',
          toEntity: 'customer-profile',
          type: 'one-to-one'
        }
      ]
      break
      
    case 'product':
      selectedDomainEntities.value = [
        {
          id: 'product-info',
          name: '产品信息',
          type: 'core',
          x: 150,
          y: 120,
          fields: [
            { id: 1, name: 'product_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'product_name', type: 'varchar(200)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 3, name: 'category_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: true },
            { id: 4, name: 'price', type: 'decimal(10,2)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 5, name: 'description', type: 'text', isPrimaryKey: false, isForeignKey: false, isRequired: false }
          ]
        },
        {
          id: 'product-category',
          name: '产品分类',
          type: 'reference',
          x: 500,
          y: 120,
          fields: [
            { id: 1, name: 'category_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'category_name', type: 'varchar(100)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 3, name: 'parent_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: false }
          ]
        },
        {
          id: 'product-inventory',
          name: '产品库存',
          type: 'operational',
          x: 320,
          y: 350,
          fields: [
            { id: 1, name: 'inventory_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'product_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: true },
            { id: 3, name: 'quantity', type: 'int', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 4, name: 'warehouse_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: true }
          ]
        }
      ]
      entityRelationships.value = [
        {
          id: 'rel-1',
          x1: 350,
          y1: 150,
          x2: 500,
          y2: 150,
          fromEntity: 'product-info',
          toEntity: 'product-category',
          type: 'many-to-one'
        },
        {
          id: 'rel-2',
          x1: 250,
          y1: 240,
          x2: 320,
          y2: 350,
          fromEntity: 'product-info',
          toEntity: 'product-inventory',
          type: 'one-to-many'
        }
      ]
      break
      
    case 'order':
      selectedDomainEntities.value = [
        {
          id: 'order-header',
          name: '订单头',
          type: 'core',
          x: 150,
          y: 120,
          fields: [
            { id: 1, name: 'order_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'customer_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: true },
            { id: 3, name: 'order_date', type: 'timestamp', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 4, name: 'total_amount', type: 'decimal(12,2)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 5, name: 'status', type: 'varchar(20)', isPrimaryKey: false, isForeignKey: false, isRequired: true }
          ]
        },
        {
          id: 'order-item',
          name: '订单明细',
          type: 'core',
          x: 500,
          y: 120,
          fields: [
            { id: 1, name: 'item_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'order_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: true },
            { id: 3, name: 'product_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: true },
            { id: 4, name: 'quantity', type: 'int', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 5, name: 'unit_price', type: 'decimal(10,2)', isPrimaryKey: false, isForeignKey: false, isRequired: true }
          ]
        }
      ]
      entityRelationships.value = [
        {
          id: 'rel-1',
          x1: 350,
          y1: 180,
          x2: 500,
          y2: 180,
          fromEntity: 'order-header',
          toEntity: 'order-item',
          type: 'one-to-many'
        }
      ]
      break
      
    case 'payment':
      selectedDomainEntities.value = [
        {
          id: 'payment-record',
          name: '支付记录',
          type: 'core',
          x: 150,
          y: 120,
          fields: [
            { id: 1, name: 'payment_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'order_id', type: 'bigint', isPrimaryKey: false, isForeignKey: true, isRequired: true },
            { id: 3, name: 'payment_method', type: 'varchar(50)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 4, name: 'amount', type: 'decimal(12,2)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 5, name: 'payment_time', type: 'timestamp', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 6, name: 'status', type: 'varchar(20)', isPrimaryKey: false, isForeignKey: false, isRequired: true }
          ]
        },
        {
          id: 'payment-gateway',
          name: '支付网关',
          type: 'reference',
          x: 500,
          y: 120,
          fields: [
            { id: 1, name: 'gateway_id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
            { id: 2, name: 'gateway_name', type: 'varchar(100)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
            { id: 3, name: 'provider', type: 'varchar(50)', isPrimaryKey: false, isForeignKey: false, isRequired: true }
          ]
        }
      ]
      entityRelationships.value = [
        {
          id: 'rel-1',
          x1: 350,
          y1: 160,
          x2: 500,
          y2: 160,
          fromEntity: 'payment-record',
          toEntity: 'payment-gateway',
          type: 'many-to-one'
        }
      ]
      break
      
    default:
      selectedDomainEntities.value = []
      entityRelationships.value = []
  }
}

const handleEntityClick = (data: any) => {
  if (data.type === 'entity') {
    // 从当前选中域的实体中查找对应的实体
    const entity = selectedDomainEntities.value.find(e => e.id === data.id)
    if (entity) {
      selectedEntity.value = {
        id: entity.id,
        name: entity.name,
        description: `${entity.name}的详细描述`,
        domain: selectedDomain.value,
        domainName: dataDomains.value.find(d => d.id === selectedDomain.value)?.name,
        governanceLevel: 4,
        fields: entity.fields || []
      }
    } else {
      // 如果在当前域找不到，创建默认的实体信息
      selectedEntity.value = {
        id: data.id,
        name: data.label,
        description: `${data.label}的详细描述`,
        domain: selectedDomain.value,
        domainName: dataDomains.value.find(d => d.id === selectedDomain.value)?.name,
        governanceLevel: 3,
        fields: [
          { id: 1, name: 'id', type: 'bigint', isPrimaryKey: true, isForeignKey: false, isRequired: true },
          { id: 2, name: 'name', type: 'varchar(100)', isPrimaryKey: false, isForeignKey: false, isRequired: true },
          { id: 3, name: 'created_at', type: 'timestamp', isPrimaryKey: false, isForeignKey: false, isRequired: true }
        ]
      }
    }
  } else if (data.type === 'domain') {
    // 如果点击的是域，切换到对应的域
    const domainId = data.id.replace('-domain', '')
    if (dataDomains.value.find(d => d.id === domainId)) {
      selectDomain(domainId)
    }
  }
}

const switchView = (viewType: string) => {
  currentView.value = viewType
}

const addDataEntity = () => {
  console.log('添加数据实体')
}

const addRelationship = () => {
  console.log('添加实体关系')
}

const autoLayout = () => {
  console.log('自动布局')
}

const exportModel = () => {
  console.log('导出数据模型')
}

const handleEntitySelected = (entity: any) => {
  selectedEntity.value = entity
}

const handleRelationshipCreated = (relationship: any) => {
  entityRelationships.value.push(relationship)
}

// 辅助方法
const getEntityTypeColor = (type: string): string => {
  const colors: Record<string, string> = {
    domain: '#1890ff',
    entity: '#52c41a',
    attribute: '#faad14'
  }
  return colors[type] || '#666'
}

const getEntityIcon = (type: string): string => {
  const icons: Record<string, string> = {
    domain: 'Folder',
    entity: 'Files',
    attribute: 'Key'
  }
  return icons[type] || 'Files'
}

const getDomainTagType = (domain: string): string => {
  const types: Record<string, string> = {
    customer: 'primary',
    product: 'success',
    order: 'warning',
    payment: 'danger'
  }
  return types[domain] || 'info'
}

const getDimensionStatus = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 70) return 'warning'
  return 'exception'
}

const getIssueTypeColor = (severity: string): string => {
  const colors: Record<string, string> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return colors[severity] || 'info'
}

const getQualityTrendClass = (trend: string) => {
  return {
    'trend-up': trend === 'up',
    'trend-down': trend === 'down',
    'trend-stable': trend === 'stable'
  }
}

const getQualityTrendIcon = (trend: string): string => {
  const icons: Record<string, string> = {
    up: 'CaretTop',
    down: 'CaretBottom',
    stable: 'Minus'
  }
  return icons[trend] || 'Minus'
}

const getAccessTypeColor = (permission: string): string => {
  const colors: Record<string, string> = {
    '只读': 'info',
    '读写': 'success',
    '受限读': 'warning'
  }
  return colors[permission] || 'info'
}

onMounted(() => {
  loadDomainEntities(selectedDomain.value)
})
</script>

<style lang="scss" scoped>
.data-modeler-workspace {
  height: 100vh;
  background: linear-gradient(135deg, #fef7f0 0%, #fff 50%, #f8f1f4 100%);
  overflow: hidden;

  // 数据架构概览
  .data-architecture-overview {
    background: white;
    border-bottom: 2px solid #e5e7eb;
    padding: 16px 20px 0;

    .project-context {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .project-selector {
        width: 200px;
      }

      .data-title {
        text-align: center;
        flex: 1;

        h2 {
          margin: 0;
          color: #dc2626;
          font-size: 24px;
          font-weight: 700;
        }

        .subtitle {
          color: #6b7280;
          font-size: 13px;
          font-weight: 500;
          letter-spacing: 1px;
        }
      }
    }

    .data-domains {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      margin-bottom: 16px;

      .domain-card {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #dc2626;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
        }

        &.active {
          border-color: #dc2626;
          background: #fef2f2;
          box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
        }

        .domain-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .domain-icon {
            font-size: 20px;
            margin-right: 8px;
          }

          .domain-name {
            font-weight: 600;
            font-size: 15px;
          }
        }

        .domain-stats {
          display: flex;
          justify-content: space-between;

          .stat-item {
            text-align: center;

            .stat-value {
              display: block;
              font-size: 18px;
              font-weight: 700;
              color: #dc2626;
            }

            .stat-label {
              font-size: 12px;
              color: #6b7280;
            }
          }
        }
      }
    }
  }

  // 主数据工作区
  .data-main-workspace {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    height: calc(100vh - 140px);
    gap: 2px;

    // 左侧数据目录
    .data-catalog-panel {
      background: white;
      border-right: 2px solid #e5e7eb;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .catalog-card {
        height: 100%;
        border: none;
        box-shadow: none;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;

          span {
            flex: 1;
            font-weight: 600;
          }
        }

        .catalog-filters {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-bottom: 16px;
        }

        .data-catalog-tree {
          flex: 1;
          overflow-y: auto;

          .catalog-node {
            display: flex;
            align-items: center;
            width: 100%;

            .node-icon {
              margin-right: 8px;
              font-size: 16px;
            }

            .node-label {
              flex: 1;
              font-size: 14px;
            }

            .node-badges {
              display: flex;
              gap: 4px;

              .el-tag {
                font-size: 10px;
                height: 18px;
                line-height: 16px;
              }
            }
          }
        }
      }
    }

    // 中央建模画布
    .data-modeling-canvas {
      background: white;
      display: flex;
      flex-direction: column;
      position: relative;
      min-height: 600px;

      .canvas-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid #e5e7eb;
        background: #f9fafb;

        .canvas-actions {
          display: flex;
          gap: 8px;
        }
      }

      .modeling-canvas-content {
        flex: 1;
        position: relative;
        background: #fafafa;
        background-image: 
          radial-gradient(circle, #e5e7eb 1px, transparent 1px);
        background-size: 20px 20px;
        overflow: hidden;
        min-height: 500px;
      }

      .data-stats-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;
        background: white;
        border-top: 1px solid #e5e7eb;

        .stats-group {
          display: flex;
          gap: 24px;

          .stat-display {
            .stat-label {
              font-size: 12px;
              color: #6b7280;
              margin-right: 4px;
            }

            .stat-value {
              font-weight: 600;
              color: #dc2626;
            }
          }
        }

        .quality-indicator {
          width: 150px;
        }
      }
    }

    // 右侧治理面板
    .governance-control-panel {
      background: white;
      border-left: 2px solid #e5e7eb;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 2px;

      .el-card {
        border: none;
        box-shadow: none;
        border-bottom: 1px solid #e5e7eb;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }
      }

      // 实体详情卡片
      .entity-details-card {
        .entity-content {
          .entity-basic-info {
            margin-bottom: 16px;

            .info-row {
              display: flex;
              align-items: center;
              margin-bottom: 8px;

              .info-label {
                font-size: 12px;
                color: #6b7280;
                width: 80px;
                font-weight: 500;
              }

              .info-value {
                font-size: 14px;
                color: #111827;
                flex: 1;
              }
            }
          }

          .fields-section {
            h5 {
              margin: 0 0 12px 0;
              font-size: 14px;
              color: #374151;
              font-weight: 600;
            }

            .fields-list {
              max-height: 200px;
              overflow-y: auto;

              .field-row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                margin-bottom: 6px;
                background: #f9fafb;

                .field-info {
                  .field-name {
                    font-weight: 500;
                    color: #111827;
                    margin-right: 8px;
                  }

                  .field-type {
                    font-size: 12px;
                    color: #6b7280;
                  }
                }

                .field-badges {
                  display: flex;
                  gap: 4px;

                  .pk-icon { color: #dc2626; }
                  .fk-icon { color: #2563eb; }
                  .required-icon { color: #f59e0b; }
                }
              }
            }
          }
        }
      }

      // 质量监控卡片
      .quality-monitoring-card {
        .quality-content {
          .quality-overview {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 16px;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border-radius: 8px;

            .quality-score {
              text-align: center;

              .score-circle {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                background: #dc2626;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
                font-weight: 700;
                margin: 0 auto 4px;
              }

              .score-label {
                font-size: 12px;
                color: #6b7280;
              }
            }

            .quality-trend {
              display: flex;
              align-items: center;
              gap: 6px;

              .trend-up { color: #10b981; }
              .trend-down { color: #ef4444; }
              .trend-stable { color: #6b7280; }

              .trend-text {
                font-size: 14px;
                font-weight: 500;
              }
            }
          }

          .quality-dimensions {
            margin-bottom: 20px;

            .dimension-item {
              margin-bottom: 12px;

              .dimension-header {
                display: flex;
                justify-content: space-between;
                margin-bottom: 6px;

                .dimension-name {
                  font-size: 13px;
                  color: #374151;
                }

                .dimension-score {
                  font-size: 13px;
                  font-weight: 600;
                  color: #dc2626;
                }
              }
            }
          }

          .quality-issues {
            h5 {
              margin: 0 0 12px 0;
              font-size: 14px;
              color: #374151;
              font-weight: 600;
            }

            .issues-list {
              .issue-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                margin-bottom: 6px;
                background: #fefefe;

                .issue-description {
                  font-size: 13px;
                  color: #4b5563;
                }
              }
            }
          }
        }
      }

      // 治理策略卡片
      .governance-policies-card {
        flex: 1;

        .governance-content {
          .policy-categories {
            .standards-list {
              .standard-item {
                padding: 12px;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                margin-bottom: 8px;
                background: #fafafa;

                .standard-header {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 6px;

                  .standard-name {
                    font-weight: 500;
                    color: #111827;
                  }
                }

                .standard-description {
                  font-size: 12px;
                  color: #6b7280;
                }
              }
            }

            .access-rules {
              .access-rule {
                padding: 12px;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                margin-bottom: 8px;
                background: #fafafa;

                .rule-header {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 6px;

                  .rule-role {
                    font-weight: 500;
                    color: #111827;
                  }
                }

                .rule-scope {
                  font-size: 12px;
                  color: #6b7280;
                }
              }
            }

            .lifecycle-stages {
              .lifecycle-stage {
                padding: 12px;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                margin-bottom: 8px;
                background: #fafafa;

                .stage-name {
                  font-weight: 500;
                  color: #111827;
                  margin-bottom: 4px;
                }

                .stage-duration {
                  font-size: 12px;
                  color: #6b7280;
                  margin-bottom: 8px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// ========== 深色主题支持 ==========
:global(.theme-dark) .data-modeler-workspace {
  background: linear-gradient(135deg, #111827 0%, #0f172a 50%, #111827 100%) !important;
  color: #f9fafb !important;

  // 数据架构概览 - 深色主题覆盖
  .data-architecture-overview {
    background: #1f2937 !important;
    border-bottom: 2px solid #374151 !important;

    .project-context {
      .data-title {
        h2 {
          color: #ef4444 !important;
          text-shadow: 0 0 10px rgba(239, 68, 68, 0.3) !important;
          font-weight: 900 !important;
        }

        .subtitle {
          color: #e5e7eb !important;
          font-weight: 600 !important;
          text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
        }
      }
    }

    .data-domains {
      .domain-card {
        background: #374151 !important;
        border: 2px solid #4b5563 !important;
        color: #f9fafb !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;

        &:hover {
          border-color: #ef4444 !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 8px 25px rgba(239, 68, 68, 0.25) !important;
          background: #475569 !important;
        }

        &.active {
          border-color: #ef4444 !important;
          background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%) !important;
          box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4) !important;
          color: #ffffff !important;
        }

        .domain-header {
          .domain-name {
            color: #f9fafb !important;
            font-weight: 800 !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
          }
        }

        .domain-stats {
          .stat-value {
            color: #fca5a5 !important;
            font-weight: 900 !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
          }

          .stat-label {
            color: #e5e7eb !important;
            font-weight: 600 !important;
          }
        }
      }
    }
  }

  // 主数据工作区 - 深色主题覆盖
  .data-main-workspace {
    // 左侧数据目录
    .data-catalog-panel {
      background: #1f2937 !important;
      border-right: 2px solid #374151 !important;

      .catalog-card {
        background: #1f2937 !important;
        
        .card-header {
          background: #374151 !important;
          border-bottom: 1px solid #4b5563 !important;
          
          span {
            color: #f9fafb !important;
            font-weight: 700 !important;
          }
        }
      }
    }

    // 中央建模画布
    .data-modeling-canvas {
      background: #1f2937 !important;

      .canvas-toolbar {
        background: #374151 !important;
        border-bottom: 1px solid #4b5563 !important;
      }

      .modeling-canvas-content {
        background: #111827 !important;
        background-image: radial-gradient(circle, #374151 1px, transparent 1px) !important;
      }

      .data-stats-bar {
        background: #1f2937 !important;
        border-top: 1px solid #374151 !important;

        .stats-group {
          .stat-display {
            .stat-label {
              color: #e5e7eb !important;
              font-weight: 700 !important;
              text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
            }

            .stat-value {
              color: #fca5a5 !important;
              font-weight: 900 !important;
              text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
            }
          }
        }
      }
    }

    // 右侧治理面板
    .governance-control-panel {
      background: #1f2937 !important;
      border-left: 2px solid #374151 !important;

      .el-card {
        background: #1f2937 !important;
        border-bottom: 1px solid #374151 !important;

        .card-header {
          background: #374151 !important;
          border-bottom: 1px solid #4b5563 !important;
          color: #f9fafb !important;
          font-weight: 700 !important;
        }
      }

      // 实体详情卡片
      .entity-details-card .entity-content {
        .entity-basic-info .info-row {
          .info-label {
            color: #d1d5db !important;
            font-weight: 600 !important;
          }

          .info-value {
            color: #f9fafb !important;
            font-weight: 500 !important;
          }
        }

        .fields-section {
          h5 {
            color: #f9fafb !important;
            font-weight: 700 !important;
          }

          .fields-list .field-row {
            border: 1px solid #4b5563 !important;
            background: #374151 !important;

            .field-info {
              .field-name {
                color: #f9fafb !important;
                font-weight: 600 !important;
              }

              .field-type {
                color: #e5e7eb !important;
                font-weight: 400 !important;
              }
            }

            .field-badges {
              .pk-icon { color: #fca5a5 !important; }
              .fk-icon { color: #93c5fd !important; }
              .required-icon { color: #fbbf24 !important; }
            }
          }
        }
      }

      // 质量监控卡片
      .quality-monitoring-card .quality-content {
        .quality-overview {
          background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%) !important;
          border: 1px solid #b91c1c !important;

          .quality-score {
            .score-circle {
              background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
              box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4) !important;
            }

            .score-label {
              color: #f3f4f6 !important;
              font-weight: 600 !important;
            }
          }

          .quality-trend {
            .trend-text {
              color: #f3f4f6 !important;
              font-weight: 600 !important;
            }
          }
        }

        .quality-dimensions .dimension-item {
          .dimension-header {
            .dimension-name {
              color: #f9fafb !important;
              font-weight: 600 !important;
            }

            .dimension-score {
              color: #fca5a5 !important;
              font-weight: 700 !important;
            }
          }
        }

        .quality-issues {
          h5 {
            color: #f9fafb !important;
            font-weight: 700 !important;
          }

          .issues-list .issue-item {
            border: 1px solid #4b5563 !important;
            background: #374151 !important;

            .issue-description {
              color: #e5e7eb !important;
              font-weight: 500 !important;
            }
          }
        }
      }

      // 治理策略卡片
      .governance-policies-card .governance-content .policy-categories {
        .standards-list .standard-item,
        .access-rules .access-rule,
        .lifecycle-stages .lifecycle-stage {
          border: 1px solid #4b5563 !important;
          background: #374151 !important;

          .standard-header, .rule-header {
            .standard-name, .rule-role, .stage-name {
              color: #f9fafb !important;
              font-weight: 600 !important;
            }
          }

          .standard-description, .rule-scope, .stage-duration {
            color: #e5e7eb !important;
            font-weight: 400 !important;
          }
        }
      }
    }
  }
}

// 深度样式覆盖 - 深色主题
:global(.theme-dark) :deep(.el-card) {
  background: #1f2937 !important;
  border: 1px solid #374151 !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;

  .el-card__header {
    background: #374151 !important;
    border-bottom: 1px solid #4b5563 !important;
    color: #f9fafb !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
  }

  .el-card__body {
    background: #1f2937 !important;
    color: #f9fafb !important;
    
    // 强化所有文字的对比度
    * {
      color: #f9fafb !important;
    }
    
    h5, .info-label, .dimension-name, .standard-name, .rule-role, .stage-name {
      color: #ffffff !important;
      font-weight: 700 !important;
      text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
    }
    
    .info-value, .dimension-score, .issue-description, .standard-description, .rule-scope, .stage-duration {
      color: #f3f4f6 !important;
      font-weight: 600 !important;
    }
    
    // 字段列表样式增强
    .field-row {
      border: 1px solid #4b5563 !important;
      background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
      
      .field-name {
        color: #ffffff !important;
        font-weight: 700 !important;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
      }
      
      .field-type {
        color: #e5e7eb !important;
        font-weight: 600 !important;
      }
    }
    
    // 问题列表样式增强
    .issue-item {
      border: 1px solid #4b5563 !important;
      background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
      
      .issue-description {
        color: #f3f4f6 !important;
        font-weight: 600 !important;
      }
    }
    
    // 策略项目样式增强
    .standard-item, .access-rule, .lifecycle-stage {
      border: 1px solid #4b5563 !important;
      background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
      
      .standard-name, .rule-role, .stage-name {
        color: #ffffff !important;
        font-weight: 700 !important;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
      }
      
      .standard-description, .rule-scope, .stage-duration {
        color: #e5e7eb !important;
        font-weight: 600 !important;
      }
    }
  }
}

:global(.theme-dark) :deep(.el-tree) {
  background: #1f2937 !important;
  color: #f9fafb !important;

  .el-tree-node__content {
    color: #f3f4f6 !important;
    font-weight: 700 !important;
    border-radius: 6px !important;
    margin: 2px 0 !important;
    padding: 8px !important;
    transition: all 0.2s ease !important;

    &:hover {
      background: #374151 !important;
      color: #ffffff !important;
      font-weight: 800 !important;
      transform: translateX(4px) !important;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3) !important;
    }
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%) !important;
    color: #ffffff !important;
    font-weight: 900 !important;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4) !important;
    border: 1px solid #ef4444 !important;
  }

  .el-tree-node__label {
    color: #f3f4f6 !important;
    font-weight: 700 !important;
    font-size: 14px !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
  }

  .el-tree-node__expand-icon {
    color: #d1d5db !important;
    font-weight: 700 !important;
    
    &:hover {
      color: #ffffff !important;
    }
  }

  // 目录节点样式增强
  .catalog-node {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    
    .node-label {
      color: #f3f4f6 !important;
      font-weight: 700 !important;
      font-size: 14px !important;
      text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
      flex: 1 !important;
    }
    
    .node-icon {
      opacity: 0.9 !important;
      margin-right: 8px !important;
      font-size: 16px !important;
      font-weight: bold !important;
    }
    
    .node-badges {
      display: flex !important;
      gap: 4px !important;
      margin-left: auto !important;
      
      .el-tag {
        font-size: 10px !important;
        height: 18px !important;
        line-height: 16px !important;
        font-weight: 600 !important;
        
        &.el-tag--danger {
          background: #7f1d1d !important;
          border-color: #b91c1c !important;
          color: #fca5a5 !important;
          font-weight: 700 !important;
        }
        
        &.el-tag--warning {
          background: #78350f !important;
          border-color: #a16207 !important;
          color: #fbbf24 !important;
          font-weight: 700 !important;
        }
      }
    }
  }
  
  // 树节点分组样式
  .el-tree-node[data-level="1"] > .el-tree-node__content {
    background: #374151 !important;
    border-left: 4px solid #ef4444 !important;
    font-weight: 800 !important;
    margin-bottom: 4px !important;
    
    .catalog-node .node-label {
      color: #ffffff !important;
      font-weight: 800 !important;
      font-size: 15px !important;
    }
    
    &:hover {
      background: #475569 !important;
      border-left: 4px solid #dc2626 !important;
    }
  }
  
  .el-tree-node[data-level="2"] > .el-tree-node__content {
    margin-left: 16px !important;
    border-left: 2px solid #6b7280 !important;
    
    &:hover {
      border-left: 2px solid #ef4444 !important;
    }
  }
}

:global(.theme-dark) :deep(.el-tabs) {
  .el-tabs__header {
    background: #374151;
  }

  .el-tabs__nav-wrap {
    background: #374151;
  }

  .el-tabs__item {
    color: #d1d5db;
    font-weight: 500;

    &.is-active {
      color: #fca5a5;
      font-weight: 700;
    }

    &:hover {
      color: #f9fafb;
    }
  }

  .el-tabs__active-bar {
    background-color: #ef4444;
  }

  .el-tabs__content {
    background: #1f2937;
    color: #f9fafb;
  }
}

:global(.theme-dark) :deep(.el-input) {
  .el-input__wrapper {
    background: #374151 !important;
    border: 1px solid #4b5563 !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
    
    &:hover {
      border-color: #6b7280 !important;
      box-shadow: 0 2px 6px rgba(0,0,0,0.3) !important;
    }
    
    &.is-focus {
      border-color: #ef4444 !important;
      box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
    }
  }

  .el-input__inner {
    color: #f9fafb !important;
    background: transparent !important;
    font-weight: 600 !important;
    
    &::placeholder {
      color: #9ca3af !important;
      font-weight: 500 !important;
    }
  }

  .el-input__prefix-inner {
    color: #d1d5db !important;
  }
}

:global(.theme-dark) :deep(.el-select) {
  .el-select__wrapper {
    background: #374151 !important;
    border: 1px solid #4b5563 !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
    
    &:hover {
      border-color: #6b7280 !important;
      box-shadow: 0 2px 6px rgba(0,0,0,0.3) !important;
    }
    
    &.is-focused {
      border-color: #ef4444 !important;
      box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
    }
  }

  .el-select__selected-item {
    color: #f9fafb !important;
    font-weight: 700 !important;
  }

  .el-select__placeholder {
    color: #9ca3af !important;
    font-weight: 500 !important;
  }

  .el-select__suffix {
    color: #d1d5db !important;
  }
}

:global(.theme-dark) :deep(.el-tag) {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;

  &.el-tag--danger {
    background: #7f1d1d;
    border-color: #b91c1c;
    color: #fca5a5;
  }

  &.el-tag--warning {
    background: #78350f;
    border-color: #a16207;
    color: #fbbf24;
  }

  &.el-tag--success {
    background: #064e3b;
    border-color: #047857;
    color: #6ee7b7;
  }

  &.el-tag--info {
    background: #1e3a8a;
    border-color: #1d4ed8;
    color: #93c5fd;
  }
}

:global(.theme-dark) :deep(.el-progress) {
  .el-progress__text {
    color: #f9fafb;
    font-weight: 700;
  }

  .el-progress-bar__outer {
    background: #374151;
  }

  .el-progress-bar__inner {
    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
  }
}

:global(.theme-dark) :deep(.el-rate) {
  .el-rate__item {
    color: #4b5563;
    
    &.is-active {
      color: #fbbf24;
    }
  }
}

:global(.theme-dark) :deep(.el-button) {
  background: #4b5563 !important;
  border-color: #6b7280 !important;
  color: #f9fafb !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5) !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;

  &:hover {
    background: #6b7280 !important;
    border-color: #9ca3af !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
  }

  &.el-button--primary {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
    border-color: #ef4444 !important;
    color: #ffffff !important;
    font-weight: 800 !important;

    &:hover {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
      border-color: #dc2626 !important;
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4) !important;
    }
  }

  &.el-button--small {
    font-weight: 600 !important;
  }
}

// ========== 蓝色主题支持 ==========
:global(.theme-blue) .data-modeler-workspace {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 50%, #bfdbfe 100%);

  .data-architecture-overview {
    background: #f8fafc;
    border-bottom: 2px solid #cbd5e1;

    .data-title h2 {
      color: #1d4ed8;
      font-weight: 700;
    }

    .data-domains .domain-card {
      &:hover {
        border-color: #1d4ed8;
        box-shadow: 0 4px 12px rgba(29, 78, 216, 0.1);
      }

      &.active {
        border-color: #1d4ed8;
        background: #eff6ff;
        box-shadow: 0 4px 12px rgba(29, 78, 216, 0.15);
      }

      .stat-value {
        color: #1d4ed8;
      }
    }
  }

  .data-main-workspace {
    .data-modeling-canvas .data-stats-bar .stat-value {
      color: #1d4ed8;
    }

    .governance-control-panel {
      .quality-monitoring-card .quality-content {
        .quality-overview {
          background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);

          .score-circle {
            background: #1d4ed8;
          }
        }

        .dimension-score {
          color: #1d4ed8;
        }
      }
    }
  }
}

// ========== 简约主题支持 ==========
:global(.theme-minimal) .data-modeler-workspace {
  background: #ffffff;

  .data-architecture-overview {
    background: #ffffff;
    border-bottom: 1px solid #f1f5f9;

    .data-title h2 {
      color: #0f172a;
      font-weight: 500;
    }

    .data-domains .domain-card {
      border: 1px solid #e2e8f0;
      box-shadow: none;

      &:hover {
        border-color: #0f172a;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }

      &.active {
        border-color: #0f172a;
        background: #f8fafc;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }

      .stat-value {
        color: #0f172a;
      }
    }
  }

  .data-main-workspace {
    .data-catalog-panel {
      border-right: 1px solid #e2e8f0;
    }

    .data-modeling-canvas {
      .canvas-toolbar {
        background: #ffffff;
        border-bottom: 1px solid #e2e8f0;
      }

      .data-stats-bar {
        border-top: 1px solid #e2e8f0;

        .stat-value {
          color: #0f172a;
        }
      }
    }

    .governance-control-panel {
      border-left: 1px solid #e2e8f0;

      .quality-monitoring-card .quality-content {
        .quality-overview {
          background: #f8fafc;

          .score-circle {
            background: #0f172a;
          }
        }

        .dimension-score {
          color: #0f172a;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .data-modeler-workspace {
    .data-main-workspace {
      grid-template-columns: 250px 1fr 280px;
    }

    .data-architecture-overview .data-domains {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 1200px) {
  .data-modeler-workspace {
    .data-main-workspace {
      grid-template-columns: 200px 1fr 250px;
    }
  }
}

@media (max-width: 768px) {
  .data-modeler-workspace {
    .data-main-workspace {
      grid-template-columns: 1fr;
      grid-template-rows: 200px 1fr 200px;
    }

    .data-architecture-overview .data-domains {
      grid-template-columns: 1fr;
    }
  }
}

// 深度样式覆盖
:deep(.el-card) {
  border: none;
  box-shadow: none;

  .el-card__header {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 12px 16px;
  }

  .el-card__body {
    padding: 16px;
  }
}

:deep(.el-tree) {
  .el-tree-node__content {
    padding: 4px 8px;
    border-radius: 4px;

    &:hover {
      background: #f3f4f6;
    }
  }

  .el-tree-node.is-current > .el-tree-node__content {
    background: #fef2f2;
    color: #dc2626;
  }
}

:deep(.el-tabs) {
  .el-tabs__header {
    margin: 0;
  }

  .el-tabs__nav-wrap {
    background: #f9fafb;
  }

  .el-tabs__item {
    font-size: 13px;
    padding: 0 12px;
  }
}

:deep(.el-progress) {
  .el-progress__text {
    font-size: 12px;
    font-weight: 600;
  }
}
</style> 