<template>
  <div class="data-modeling-canvas" ref="canvasContainer">
    <!-- ERD视图 -->
    <div v-if="viewType === 'erd'" class="erd-view">
      <div class="canvas-content">
        <svg 
          ref="erdSvg" 
          class="erd-svg"
          viewBox="0 0 1200 800"
          preserveAspectRatio="xMidYMid meet"
          @mousedown="handleCanvasMouseDown"
          @mousemove="handleCanvasMouseMove" 
          @mouseup="handleCanvasMouseUp"
        >
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                    refX="10" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
            </marker>
          </defs>
          
          <!-- 实体关系线 -->
          <g class="relationships">
            <line 
              v-for="relationship in relationships" 
              :key="relationship.id"
              :x1="relationship.x1" 
              :y1="relationship.y1"
              :x2="relationship.x2" 
              :y2="relationship.y2"
              stroke="#666" 
              stroke-width="2"
              marker-end="url(#arrowhead)"
            />
          </g>
          
          <!-- 实体框 -->
          <g class="entities">
            <g 
              v-for="entity in entities" 
              :key="entity.id"
              :transform="`translate(${entity.x || 100}, ${entity.y || 100})`"
              class="entity-group"
              @mousedown="handleEntityMouseDown($event, entity)"
            >
              <rect 
                width="200" 
                height="120" 
                rx="8" 
                fill="white" 
                stroke="#dc2626" 
                stroke-width="2"
                class="entity-rect"
              />
              <text x="100" y="20" text-anchor="middle" class="entity-title">
                {{ entity.name }}
              </text>
              <line x1="10" y1="30" x2="190" y2="30" stroke="#dc2626" stroke-width="1"/>
              
              <!-- 实体字段 -->
              <g class="entity-fields">
                <text 
                  v-for="(field, index) in (entity.fields || []).slice(0, 4)" 
                  :key="field.id"
                  :x="15" 
                  :y="50 + index * 18"
                  class="field-text"
                  :class="{ 'primary-key': field.isPrimaryKey }"
                >
                  {{ field.name }}: {{ field.type }}
                </text>
                <text 
                  v-if="(entity.fields || []).length > 4"
                  x="15" 
                  y="110"
                  class="field-text more-fields"
                >
                  ... 更多字段 ({{ entity.fields.length - 4 }})
                </text>
              </g>
            </g>
          </g>
        </svg>
      </div>
    </div>

    <!-- 数据流图视图 -->
    <div v-else-if="viewType === 'dataflow'" class="dataflow-view">
      <div class="canvas-content">
        <svg ref="dataflowSvg" class="dataflow-svg" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid meet">
          <!-- 数据流连接线 -->
          <g class="data-flows">
            <path 
              v-for="flow in dataFlows" 
              :key="flow.id"
              :d="flow.path"
              stroke="#2563eb" 
              stroke-width="3"
              fill="none"
              marker-end="url(#arrowhead)"
              class="flow-path"
            />
          </g>
          
          <!-- 数据源节点 -->
          <g class="data-sources">
            <g 
              v-for="source in dataSources" 
              :key="source.id"
              :transform="`translate(${source.x}, ${source.y})`"
              class="source-node"
            >
              <circle r="30" fill="#10b981" class="source-circle"/>
              <text x="0" y="5" text-anchor="middle" class="source-text">
                {{ source.name }}
              </text>
            </g>
          </g>
          
          <!-- 数据处理节点 -->
          <g class="data-processors">
            <g 
              v-for="processor in dataProcessors" 
              :key="processor.id"
              :transform="`translate(${processor.x}, ${processor.y})`"
              class="processor-node"
            >
              <rect x="-60" y="-30" width="120" height="60" rx="10" fill="#f59e0b" class="processor-rect"/>
              <text x="0" y="5" text-anchor="middle" class="processor-text">
                {{ processor.name }}
              </text>
            </g>
          </g>
        </svg>
      </div>
    </div>

    <!-- 血缘图谱视图 -->
    <div v-else-if="viewType === 'lineage'" class="lineage-view">
      <div class="canvas-content">
        <svg ref="lineageSvg" class="lineage-svg" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid meet">
          <!-- 血缘关系线 -->
          <g class="lineage-connections">
            <path 
              v-for="connection in lineageConnections" 
              :key="connection.id"
              :d="connection.path"
              stroke="#7c3aed" 
              stroke-width="2"
              fill="none"
              stroke-dasharray="5,5"
              marker-end="url(#arrowhead)"
              class="lineage-path"
            />
          </g>
          
          <!-- 数据血缘节点 -->
          <g class="lineage-nodes">
            <g 
              v-for="node in lineageNodes" 
              :key="node.id"
              :transform="`translate(${node.x}, ${node.y})`"
              class="lineage-node"
            >
              <rect 
                x="-75"
                y="-40"
                width="150" 
                height="80" 
                rx="12" 
                :fill="getLineageNodeColor(node.type)"
                class="lineage-rect"
              />
              <text x="0" y="-5" text-anchor="middle" class="lineage-node-title">
                {{ node.name }}
              </text>
              <text x="0" y="15" text-anchor="middle" class="lineage-node-type">
                {{ node.type }}
              </text>
            </g>
          </g>
        </svg>
      </div>
    </div>

    <!-- 工具栏浮动面板 -->
    <div class="canvas-toolbar" v-if="showToolbar">
      <el-button-group size="small">
        <el-button icon="ZoomIn" @click="zoomIn">放大</el-button>
        <el-button icon="ZoomOut" @click="zoomOut">缩小</el-button>
        <el-button icon="Refresh" @click="resetView">重置</el-button>
      </el-button-group>
    </div>

    <!-- 右键菜单 -->
    <div 
      v-if="contextMenu.visible" 
      class="context-menu"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
    >
      <div class="menu-item" @click="editSelectedEntity">编辑实体</div>
      <div class="menu-item" @click="deleteSelectedEntity">删除实体</div>
      <div class="menu-separator"></div>
      <div class="menu-item" @click="addNewRelationship">添加关系</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  viewType: 'erd' | 'dataflow' | 'lineage'
  entities: any[]
  relationships: any[]
  dataFlows: any[]
}

const props = withDefaults(defineProps<Props>(), {
  viewType: 'erd',
  entities: () => [],
  relationships: () => [],
  dataFlows: () => []
})

// Emits
const emit = defineEmits(['entity-selected', 'relationship-created', 'entity-moved'])

// Refs
const canvasContainer = ref<HTMLDivElement>()
const erdSvg = ref<SVGElement>()
const dataflowSvg = ref<SVGElement>()
const lineageSvg = ref<SVGElement>()

// 状态管理
const isDragging = ref(false)
const dragTarget = ref<any>(null)
const dragOffset = reactive({ x: 0, y: 0 })
const showToolbar = ref(true)
const zoom = ref(1)
const pan = reactive({ x: 0, y: 0 })

// 右键菜单
const contextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  target: null
})

// 模拟数据
const dataSources = ref([
  { id: 1, name: '用户表', x: 100, y: 100 },
  { id: 2, name: '订单表', x: 300, y: 100 },
  { id: 3, name: '产品表', x: 500, y: 100 }
])

const dataProcessors = ref([
  { id: 1, name: '数据清洗', x: 200, y: 250 },
  { id: 2, name: '数据聚合', x: 400, y: 250 }
])

const lineageNodes = ref([
  { id: 1, name: '源系统A', type: 'source', x: 100, y: 100 },
  { id: 2, name: 'ETL处理', type: 'transform', x: 300, y: 100 },
  { id: 3, name: '数据仓库', type: 'target', x: 500, y: 100 }
])

const lineageConnections = ref([
  { id: 1, path: 'M 175 100 Q 225 50 275 100' },
  { id: 2, path: 'M 375 100 Q 425 50 475 100' }
])

// 方法
const handleCanvasMouseDown = (event: MouseEvent) => {
  if (event.button === 2) { // 右键
    showContextMenu(event)
    return
  }
  
  // 隐藏右键菜单
  contextMenu.visible = false
}

const handleCanvasMouseMove = (event: MouseEvent) => {
  if (isDragging.value && dragTarget.value) {
    const rect = (event.currentTarget as SVGElement).getBoundingClientRect()
    const x = event.clientX - rect.left - dragOffset.x
    const y = event.clientY - rect.top - dragOffset.y
    
    dragTarget.value.x = x
    dragTarget.value.y = y
    
    // 更新关系线
    updateRelationshipPositions()
  }
}

const handleCanvasMouseUp = () => {
  if (isDragging.value && dragTarget.value) {
    emit('entity-moved', dragTarget.value)
  }
  
  isDragging.value = false
  dragTarget.value = null
}

const handleEntityMouseDown = (event: MouseEvent, entity: any) => {
  event.stopPropagation()
  
  if (event.button === 0) { // 左键
    isDragging.value = true
    dragTarget.value = entity
    
    const rect = (event.currentTarget as Element).getBoundingClientRect()
    dragOffset.x = event.clientX - rect.left
    dragOffset.y = event.clientY - rect.top
    
    emit('entity-selected', entity)
  }
}

const showContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  contextMenu.visible = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
}

const updateRelationshipPositions = () => {
  // 根据实体位置更新关系线的坐标
  // 这里可以添加更复杂的连线计算逻辑
}

const zoomIn = () => {
  zoom.value = Math.min(zoom.value * 1.2, 3)
  applyTransform()
}

const zoomOut = () => {
  zoom.value = Math.max(zoom.value / 1.2, 0.3)
  applyTransform()
}

const resetView = () => {
  zoom.value = 1
  pan.x = 0
  pan.y = 0
  applyTransform()
}

const applyTransform = () => {
  const svg = getCurrentSvg()
  if (svg) {
    svg.style.transform = `scale(${zoom.value}) translate(${pan.x}px, ${pan.y}px)`
  }
}

const getCurrentSvg = () => {
  switch (props.viewType) {
    case 'erd': return erdSvg.value
    case 'dataflow': return dataflowSvg.value
    case 'lineage': return lineageSvg.value
    default: return null
  }
}

const getLineageNodeColor = (type: string) => {
  const colors: Record<string, string> = {
    source: '#10b981',
    transform: '#f59e0b', 
    target: '#3b82f6'
  }
  return colors[type] || '#6b7280'
}

// 右键菜单操作
const editSelectedEntity = () => {
  ElMessage.info('编辑实体功能')
  contextMenu.visible = false
}

const deleteSelectedEntity = () => {
  ElMessage.warning('删除实体功能')
  contextMenu.visible = false
}

const addNewRelationship = () => {
  ElMessage.info('添加关系功能')
  contextMenu.visible = false
}

// 监听视图类型变化
watch(() => props.viewType, () => {
  nextTick(() => {
    resetView()
  })
})

// 隐藏右键菜单（点击其他地方）
onMounted(() => {
  document.addEventListener('click', () => {
    contextMenu.visible = false
  })
  
  // 禁用右键菜单
  if (canvasContainer.value) {
    canvasContainer.value.addEventListener('contextmenu', (e) => {
      e.preventDefault()
    })
  }
})
</script>

<style lang="scss" scoped>
.data-modeling-canvas {
  width: 100%;
  height: 100%;
  min-height: 500px;
  position: relative;
  overflow: hidden;
  background: #fafafa;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }

  .canvas-content {
    width: 100%;
    height: 100%;
    min-height: 450px;
    position: relative;
  }

  // SVG样式
  .erd-svg, .dataflow-svg, .lineage-svg {
    width: 100%;
    height: 100%;
    display: block;
    min-height: 400px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
  }

  // ERD视图样式
  .erd-view {
    .entity-group {
      cursor: move;

      &:hover .entity-rect {
        stroke: #ef4444;
        stroke-width: 3;
      }

      .entity-title {
        font-size: 14px;
        font-weight: 600;
        fill: #111827;
      }

      .field-text {
        font-size: 12px;
        fill: #374151;

        &.primary-key {
          fill: #dc2626;
          font-weight: 600;
        }

        &.more-fields {
          fill: #6b7280;
          font-style: italic;
        }
      }
    }

    .relationships line {
      cursor: pointer;

      &:hover {
        stroke: #dc2626;
        stroke-width: 3;
      }
    }
  }

  // 数据流视图样式
  .dataflow-view {
    .source-node {
      cursor: move;

      .source-circle {
        filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
      }

      .source-text {
        font-size: 10px;
        fill: white;
        font-weight: 600;
      }

      &:hover .source-circle {
        fill: #059669;
      }
    }

    .processor-node {
      cursor: move;

      .processor-rect {
        filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
      }

      .processor-text {
        font-size: 11px;
        fill: white;
        font-weight: 600;
      }

      &:hover .processor-rect {
        fill: #d97706;
      }
    }

    .flow-path {
      cursor: pointer;
      animation: flowAnimation 2s infinite linear;

      &:hover {
        stroke: #1d4ed8;
        stroke-width: 4;
      }
    }
  }

  // 血缘图谱视图样式
  .lineage-view {
    .lineage-node {
      cursor: move;

      .lineage-rect {
        filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.1));
      }

      .lineage-node-title {
        font-size: 12px;
        fill: white;
        font-weight: 600;
      }

      .lineage-node-type {
        font-size: 10px;
        fill: rgba(255,255,255,0.8);
      }

      &:hover .lineage-rect {
        opacity: 0.8;
        transform: scale(1.05);
      }
    }

    .lineage-path {
      cursor: pointer;
      animation: lineageFlow 3s infinite linear;

      &:hover {
        stroke: #5b21b6;
        stroke-width: 3;
      }
    }
  }

  // 工具栏
  .canvas-toolbar {
    position: absolute;
    top: 10px;
    right: 10px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 8px;
    z-index: 10;
  }

  // 右键菜单
  .context-menu {
    position: fixed;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 4px 0;
    z-index: 1000;
    min-width: 120px;

    .menu-item {
      padding: 8px 16px;
      font-size: 13px;
      cursor: pointer;
      color: #374151;

      &:hover {
        background: #f3f4f6;
        color: #111827;
      }
    }

    .menu-separator {
      height: 1px;
      background: #e5e7eb;
      margin: 4px 0;
    }
  }
}

// 动画效果
@keyframes flowAnimation {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 20;
  }
}

@keyframes lineageFlow {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 10;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-modeling-canvas {
    .canvas-toolbar {
      top: 5px;
      right: 5px;
      padding: 4px;
    }

    .context-menu {
      min-width: 100px;

      .menu-item {
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }
}

// 深色主题支持
:global(.theme-dark) .data-modeling-canvas {
  background: #111827;
  color: #f9fafb;

  .erd-svg, .dataflow-svg, .lineage-svg {
    background: #1f2937;
    border: 1px solid #374151;
  }

  // ERD视图深色主题
  .erd-view {
    .entity-group {
      .entity-rect {
        fill: #1f2937;
        stroke: #ef4444;
        stroke-width: 2.5;
        filter: drop-shadow(0 4px 12px rgba(0,0,0,0.4));
      }

      .entity-title {
        fill: #ffffff;
        font-weight: 800;
        font-size: 16px;
        text-shadow: 0 1px 3px rgba(0,0,0,0.5);
      }

      .field-text {
        fill: #f3f4f6;
        font-weight: 600;
        font-size: 13px;

        &.primary-key {
          fill: #fbbf24;
          font-weight: 800;
        }

        &.more-fields {
          fill: #e5e7eb;
          font-style: italic;
          font-weight: 500;
        }
      }

      &:hover .entity-rect {
        stroke: #dc2626;
        stroke-width: 3;
        fill: #374151;
      }

      // 分隔线
      line {
        stroke: #ef4444;
        stroke-width: 2;
      }
    }

    .relationships line {
      stroke: #e5e7eb;
      stroke-width: 2.5;

      &:hover {
        stroke: #fbbf24;
        stroke-width: 3.5;
      }
    }
  }

  // 数据流视图深色主题
  .dataflow-view {
    .source-node {
      .source-circle {
        fill: #059669;
        filter: drop-shadow(0 4px 12px rgba(5, 150, 105, 0.4));
      }

      .source-text {
        fill: #ffffff;
        font-weight: 800;
        font-size: 12px;
        text-shadow: 0 1px 3px rgba(0,0,0,0.7);
      }

      &:hover .source-circle {
        fill: #10b981;
        transform: scale(1.1);
      }
    }

    .processor-node {
      .processor-rect {
        fill: #d97706;
        filter: drop-shadow(0 4px 12px rgba(217, 119, 6, 0.4));
      }

      .processor-text {
        fill: #ffffff;
        font-weight: 800;
        font-size: 13px;
        text-shadow: 0 1px 3px rgba(0,0,0,0.7);
      }

      &:hover .processor-rect {
        fill: #f59e0b;
        transform: scale(1.05);
      }
    }

    .flow-path {
      stroke: #60a5fa;
      stroke-width: 3.5;

      &:hover {
        stroke: #3b82f6;
        stroke-width: 4.5;
        filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));
      }
    }
  }

  // 血缘图谱深色主题
  .lineage-view {
    .lineage-node {
      .lineage-rect {
        filter: drop-shadow(0 4px 12px rgba(0,0,0,0.3));
      }

      .lineage-node-title {
        fill: #ffffff;
        font-weight: 800;
        font-size: 14px;
        text-shadow: 0 1px 3px rgba(0,0,0,0.7);
      }

      .lineage-node-type {
        fill: #f3f4f6;
        font-weight: 600;
        font-size: 12px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
      }

      &:hover .lineage-rect {
        opacity: 0.9;
        transform: scale(1.05);
        filter: drop-shadow(0 6px 16px rgba(0,0,0,0.4));
      }
    }

    .lineage-path {
      stroke: #c084fc;
      stroke-width: 3;

      &:hover {
        stroke: #a855f7;
        stroke-width: 4;
        filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.6));
      }
    }
  }

  // 工具栏深色主题
  .canvas-toolbar {
    background: #374151;
    border: 1px solid #4b5563;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);

    .el-button {
      background: #4b5563;
      border-color: #6b7280;
      color: #f9fafb;
      font-weight: 600;

      &:hover {
        background: #6b7280;
        border-color: #9ca3af;
        color: #ffffff;
      }
    }
  }

  // 右键菜单深色主题
  .context-menu {
    background: #1f2937;
    border: 1px solid #374151;
    box-shadow: 0 8px 24px rgba(0,0,0,0.4);

    .menu-item {
      color: #f9fafb;
      font-weight: 600;

      &:hover {
        background: #374151;
        color: #ffffff;
      }
    }

    .menu-separator {
      background: #4b5563;
    }
  }

  // SVG marker箭头深色主题
  defs marker polygon {
    fill: #e5e7eb;
  }
}

// SVG 深色主题内联样式重写 - 完全重构
:global(.theme-dark) .data-modeling-canvas svg {
  // 箭头标记
  defs marker#arrowhead polygon {
    fill: #e5e7eb !important;
  }
  
  // 强制重写所有SVG文字样式 - 确保高对比度
  text {
    fill: #ffffff !important;
    font-weight: 700 !important;
    font-size: 14px !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.8) !important;
    paint-order: stroke fill !important;
    stroke: rgba(0,0,0,0.8) !important;
    stroke-width: 0.5px !important;
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif !important;
  }
  
  // 实体标题 - 更高的对比度
  text.entity-title {
    fill: #ffffff !important;
    font-weight: 900 !important;
    font-size: 16px !important;
    text-shadow: 0 2px 6px rgba(0,0,0,0.9) !important;
    stroke: rgba(0,0,0,0.9) !important;
    stroke-width: 1px !important;
  }
  
  // 主键字段 - 金色高亮
  text.primary-key {
    fill: #fbbf24 !important;
    font-weight: 900 !important;
    font-size: 14px !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.8) !important;
    stroke: rgba(0,0,0,0.8) !important;
    stroke-width: 0.8px !important;
  }
  
  // 字段文字
  text.field-text {
    fill: #f3f4f6 !important;
    font-weight: 700 !important;
    font-size: 13px !important;
    text-shadow: 0 1px 3px rgba(0,0,0,0.8) !important;
    stroke: rgba(0,0,0,0.6) !important;
    stroke-width: 0.3px !important;
  }
  
  // 更多字段提示
  text.more-fields {
    fill: #e5e7eb !important;
    font-weight: 600 !important;
    font-size: 12px !important;
    font-style: italic !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.7) !important;
  }
  
  // 数据源节点文字
  text.source-text {
    fill: #ffffff !important;
    font-weight: 900 !important;
    font-size: 12px !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.9) !important;
    stroke: rgba(0,0,0,0.8) !important;
    stroke-width: 0.5px !important;
  }
  
  // 处理器节点文字
  text.processor-text {
    fill: #ffffff !important;
    font-weight: 900 !important;
    font-size: 13px !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.9) !important;
    stroke: rgba(0,0,0,0.8) !important;
    stroke-width: 0.5px !important;
  }
  
  // 血缘节点标题
  text.lineage-node-title {
    fill: #ffffff !important;
    font-weight: 900 !important;
    font-size: 14px !important;
    text-shadow: 0 2px 6px rgba(0,0,0,0.9) !important;
    stroke: rgba(0,0,0,0.8) !important;
    stroke-width: 0.8px !important;
  }
  
  // 血缘节点类型
  text.lineage-node-type {
    fill: #f3f4f6 !important;
    font-weight: 700 !important;
    font-size: 12px !important;
    text-shadow: 0 1px 3px rgba(0,0,0,0.8) !important;
    stroke: rgba(0,0,0,0.6) !important;
    stroke-width: 0.3px !important;
  }
}

// 强制应用深色主题下的文字样式到所有可能的文字元素
:global(.theme-dark) .data-modeling-canvas {
  // ERD视图深色主题增强
  .erd-view {
    .entity-group {
      .entity-rect {
        fill: #1f2937 !important;
        stroke: #ef4444 !important;
        stroke-width: 3 !important;
        filter: drop-shadow(0 4px 12px rgba(0,0,0,0.4)) !important;
      }

      .entity-title {
        fill: #ffffff !important;
        font-weight: 900 !important;
        font-size: 16px !important;
        text-shadow: 0 2px 6px rgba(0,0,0,0.9) !important;
      }

      .field-text {
        fill: #f3f4f6 !important;
        font-weight: 700 !important;
        font-size: 13px !important;
        text-shadow: 0 1px 3px rgba(0,0,0,0.8) !important;

        &.primary-key {
          fill: #fbbf24 !important;
          font-weight: 900 !important;
        }

        &.more-fields {
          fill: #e5e7eb !important;
          font-weight: 600 !important;
          font-style: italic !important;
        }
      }

      &:hover .entity-rect {
        stroke: #dc2626 !important;
        stroke-width: 4 !important;
        fill: #374151 !important;
      }

      // 分隔线
      line {
        stroke: #ef4444 !important;
        stroke-width: 2 !important;
      }
    }

    .relationships line {
      stroke: #e5e7eb !important;
      stroke-width: 2.5 !important;

      &:hover {
        stroke: #fbbf24 !important;
        stroke-width: 3.5 !important;
      }
    }
  }

  // 数据流视图深色主题增强
  .dataflow-view {
    .source-node {
      .source-circle {
        fill: #059669 !important;
        filter: drop-shadow(0 4px 12px rgba(5, 150, 105, 0.4)) !important;
      }

      .source-text {
        fill: #ffffff !important;
        font-weight: 900 !important;
        font-size: 12px !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.9) !important;
      }

      &:hover .source-circle {
        fill: #10b981 !important;
        transform: scale(1.1) !important;
      }
    }

    .processor-node {
      .processor-rect {
        fill: #d97706 !important;
        filter: drop-shadow(0 4px 12px rgba(217, 119, 6, 0.4)) !important;
      }

      .processor-text {
        fill: #ffffff !important;
        font-weight: 900 !important;
        font-size: 13px !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.9) !important;
      }

      &:hover .processor-rect {
        fill: #f59e0b !important;
        transform: scale(1.05) !important;
      }
    }

    .flow-path {
      stroke: #60a5fa !important;
      stroke-width: 3.5 !important;

      &:hover {
        stroke: #3b82f6 !important;
        stroke-width: 4.5 !important;
        filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6)) !important;
      }
    }
  }

  // 血缘图谱深色主题增强
  .lineage-view {
    .lineage-node {
      .lineage-rect {
        filter: drop-shadow(0 4px 12px rgba(0,0,0,0.3)) !important;
      }

      .lineage-node-title {
        fill: #ffffff !important;
        font-weight: 900 !important;
        font-size: 14px !important;
        text-shadow: 0 2px 6px rgba(0,0,0,0.9) !important;
      }

      .lineage-node-type {
        fill: #f3f4f6 !important;
        font-weight: 700 !important;
        font-size: 12px !important;
        text-shadow: 0 1px 3px rgba(0,0,0,0.8) !important;
      }

      &:hover .lineage-rect {
        opacity: 0.9 !important;
        transform: scale(1.05) !important;
        filter: drop-shadow(0 6px 16px rgba(0,0,0,0.4)) !important;
      }
    }

    .lineage-path {
      stroke: #c084fc !important;
      stroke-width: 3 !important;

      &:hover {
        stroke: #a855f7 !important;
        stroke-width: 4 !important;
        filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.6)) !important;
      }
    }
  }

  // 工具栏深色主题
  .canvas-toolbar {
    background: #374151 !important;
    border: 1px solid #4b5563 !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;

    .el-button {
      background: #4b5563 !important;
      border-color: #6b7280 !important;
      color: #f9fafb !important;
      font-weight: 600 !important;

      &:hover {
        background: #6b7280 !important;
        border-color: #9ca3af !important;
        color: #ffffff !important;
      }
    }
  }

  // 右键菜单深色主题
  .context-menu {
    background: #1f2937 !important;
    border: 1px solid #374151 !important;
    box-shadow: 0 8px 24px rgba(0,0,0,0.4) !important;

    .menu-item {
      color: #f9fafb !important;
      font-weight: 600 !important;

      &:hover {
        background: #374151 !important;
        color: #ffffff !important;
      }
    }

    .menu-separator {
      background: #4b5563 !important;
    }
  }

  // SVG marker箭头深色主题
  defs marker polygon {
    fill: #e5e7eb !important;
  }
}

// 蓝色主题支持
:global(.theme-blue) .data-modeling-canvas {
  .erd-view {
    .entity-group {
      .entity-rect {
        stroke: #1d4ed8;
      }

      .entity-title {
        fill: #1e40af;
      }

      .field-text.primary-key {
        fill: #1d4ed8;
      }

      &:hover .entity-rect {
        stroke: #2563eb;
      }
    }

    .relationships line {
      &:hover {
        stroke: #1d4ed8;
      }
    }
  }

  .data-stats-bar .stat-value {
    color: #1d4ed8;
  }
}

// 简约主题支持
:global(.theme-minimal) .data-modeling-canvas {
  background: #ffffff;

  .erd-svg, .dataflow-svg, .lineage-svg {
    background: #ffffff;
    border: 1px solid #e2e8f0;
  }

  .erd-view {
    .entity-group {
      .entity-rect {
        stroke: #0f172a;
      }

      .entity-title {
        fill: #0f172a;
      }

      .field-text.primary-key {
        fill: #0f172a;
      }

      &:hover .entity-rect {
        stroke: #334155;
      }
    }

    .relationships line {
      &:hover {
        stroke: #0f172a;
      }
    }
  }

  .canvas-toolbar {
    background: #ffffff;
    border: 1px solid #e2e8f0;
  }
}
</style> 