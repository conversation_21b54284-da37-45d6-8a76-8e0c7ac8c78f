// UML和SysML核心组件工厂

import type { ComponentFactory, ComponentInstance } from '@/core/ViewEngine'
import type { ComponentConfig } from '@/types/view-system'

// ====== UML组件工厂 ======

/**
 * UML类节点组件工厂
 */
export class UMLClassNodeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'UMLClassNode',
          props: {
            classData: config.dataBinding,
            showAttributes: config.customProps?.showAttributes ?? true,
            showOperations: config.customProps?.showOperations ?? true,
            showStereotypes: config.customProps?.showStereotypes ?? true,
            compartmentCollapsible: config.customProps?.compartmentCollapsible ?? true,
            styling: config.styling
          }
        }
      },
      update(data: any) {
        // 更新类节点数据
        console.log('Updating UML class node with data:', data)
      },
      destroy() {
        // 清理类节点资源
        console.log('Destroying UML class node:', config.id)
      }
    }
  }
}

/**
 * UML接口节点组件工厂
 */
export class UMLInterfaceNodeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'UMLInterfaceNode',
          props: {
            interfaceData: config.dataBinding,
            showOperations: config.customProps?.showOperations ?? true,
            showAsCircle: config.customProps?.showAsCircle ?? false,
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating UML interface node with data:', data)
      },
      destroy() {
        console.log('Destroying UML interface node:', config.id)
      }
    }
  }
}

/**
 * UML继承关系组件工厂
 */
export class UMLInheritanceEdgeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'UMLInheritanceEdge',
          props: {
            relationshipData: config.dataBinding,
            arrowType: config.customProps?.arrowType ?? 'hollow-triangle',
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating UML inheritance edge with data:', data)
      },
      destroy() {
        console.log('Destroying UML inheritance edge:', config.id)
      }
    }
  }
}

/**
 * UML关联关系组件工厂
 */
export class UMLAssociationEdgeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'UMLAssociationEdge',
          props: {
            relationshipData: config.dataBinding,
            showMultiplicity: config.customProps?.showMultiplicity ?? true,
            showRoleName: config.customProps?.showRoleName ?? true,
            showNavigability: config.customProps?.showNavigability ?? true,
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating UML association edge with data:', data)
      },
      destroy() {
        console.log('Destroying UML association edge:', config.id)
      }
    }
  }
}

/**
 * UML参与者节点组件工厂
 */
export class UMLActorNodeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'UMLActorNode',
          props: {
            actorData: config.dataBinding,
            showName: config.customProps?.showName ?? true,
            actorStyle: config.customProps?.actorStyle ?? 'stick-figure',
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating UML actor node with data:', data)
      },
      destroy() {
        console.log('Destroying UML actor node:', config.id)
      }
    }
  }
}

/**
 * UML用例节点组件工厂
 */
export class UMLUseCaseNodeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'UMLUseCaseNode',
          props: {
            useCaseData: config.dataBinding,
            showDescription: config.customProps?.showDescription ?? true,
            ellipseShape: config.customProps?.ellipseShape ?? true,
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating UML use case node with data:', data)
      },
      destroy() {
        console.log('Destroying UML use case node:', config.id)
      }
    }
  }
}

/**
 * UML系统边界组件工厂
 */
export class UMLSystemBoundaryFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'UMLSystemBoundary',
          props: {
            systemData: config.dataBinding,
            showSystemName: config.customProps?.showSystemName ?? true,
            boundaryStyle: config.customProps?.boundaryStyle ?? 'dashed-rectangle',
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating UML system boundary with data:', data)
      },
      destroy() {
        console.log('Destroying UML system boundary:', config.id)
      }
    }
  }
}

/**
 * UML图工具栏组件工厂
 */
export class UMLDiagramToolbarFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'UMLDiagramToolbar',
          props: {
            tools: config.customProps?.tools ?? ['zoom', 'pan', 'select', 'export', 'layout'],
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating UML diagram toolbar with data:', data)
      },
      destroy() {
        console.log('Destroying UML diagram toolbar:', config.id)
      }
    }
  }
}

// ====== SysML组件工厂 ======

/**
 * SysML块节点组件工厂
 */
export class SysMLBlockNodeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'SysMLBlockNode',
          props: {
            blockData: config.dataBinding,
            showValueProperties: config.customProps?.showValueProperties ?? true,
            showPartProperties: config.customProps?.showPartProperties ?? true,
            showReferenceProperties: config.customProps?.showReferenceProperties ?? true,
            showConstraintProperties: config.customProps?.showConstraintProperties ?? false,
            showOperations: config.customProps?.showOperations ?? true,
            showPorts: config.customProps?.showPorts ?? true,
            portTypes: config.customProps?.portTypes ?? ['flow_port', 'standard_port', 'proxy_port'],
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating SysML block node with data:', data)
      },
      destroy() {
        console.log('Destroying SysML block node:', config.id)
      }
    }
  }
}

/**
 * SysML需求树组件工厂
 */
export class SysMLRequirementTreeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'SysMLRequirementTree',
          props: {
            requirements: config.dataBinding,
            hierarchyLevels: config.customProps?.hierarchyLevels ?? ['functional', 'performance', 'interface', 'design', 'implementation'],
            requirementProperties: config.customProps?.requirementProperties ?? ['id', 'text', 'priority', 'status', 'rationale'],
            traceabilityMatrix: config.customProps?.traceabilityMatrix ?? true,
            verificationStatus: config.customProps?.verificationStatus ?? true,
            expandable: config.customProps?.expandable ?? true,
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating SysML requirement tree with data:', data)
      },
      destroy() {
        console.log('Destroying SysML requirement tree:', config.id)
      }
    }
  }
}

/**
 * SysML需求关系组件工厂
 */
export class SysMLRequirementRelationFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'SysMLRequirementRelation',
          props: {
            relationshipData: config.dataBinding,
            showRelationType: config.customProps?.showRelationType ?? true,
            relationshipColors: config.customProps?.relationshipColors ?? {
              derive: '#4CAF50',
              satisfy: '#2196F3',
              verify: '#FF9800',
              refine: '#9C27B0',
              trace: '#607D8B'
            },
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating SysML requirement relation with data:', data)
      },
      destroy() {
        console.log('Destroying SysML requirement relation:', config.id)
      }
    }
  }
}

/**
 * SysML需求属性面板组件工厂
 */
export class SysMLRequirementPropertiesPanelFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'SysMLRequirementPropertiesPanel',
          props: {
            editableProperties: config.customProps?.editableProperties ?? ['text', 'priority', 'status', 'rationale'],
            showVerificationMethods: config.customProps?.showVerificationMethods ?? true,
            showTraceability: config.customProps?.showTraceability ?? true,
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating SysML requirement properties panel with data:', data)
      },
      destroy() {
        console.log('Destroying SysML requirement properties panel:', config.id)
      }
    }
  }
}

/**
 * SysML组合关系组件工厂
 */
export class SysMLCompositionEdgeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'SysMLCompositionEdge',
          props: {
            relationshipData: config.dataBinding,
            arrowType: config.customProps?.arrowType ?? 'filled-diamond',
            showMultiplicity: config.customProps?.showMultiplicity ?? true,
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating SysML composition edge with data:', data)
      },
      destroy() {
        console.log('Destroying SysML composition edge:', config.id)
      }
    }
  }
}

/**
 * SysML泛化关系组件工厂
 */
export class SysMLGeneralizationEdgeFactory implements ComponentFactory {
  create(config: ComponentConfig, engine: any): ComponentInstance {
    return {
      id: config.id,
      type: config.type,
      render() {
        return {
          component: 'SysMLGeneralizationEdge',
          props: {
            relationshipData: config.dataBinding,
            arrowType: config.customProps?.arrowType ?? 'hollow-triangle',
            styling: config.styling
          }
        }
      },
      update(data: any) {
        console.log('Updating SysML generalization edge with data:', data)
      },
      destroy() {
        console.log('Destroying SysML generalization edge:', config.id)
      }
    }
  }
}

// ====== 组件工厂注册映射 ======

export const coreComponentFactories = {
  // UML组件工厂
  'uml-class-node': UMLClassNodeFactory,
  'uml-interface-node': UMLInterfaceNodeFactory,
  'uml-inheritance-edge': UMLInheritanceEdgeFactory,
  'uml-association-edge': UMLAssociationEdgeFactory,
  'uml-actor-node': UMLActorNodeFactory,
  'uml-usecase-node': UMLUseCaseNodeFactory,
  'uml-system-boundary': UMLSystemBoundaryFactory,
  'uml-diagram-toolbar': UMLDiagramToolbarFactory,
  
  // SysML组件工厂
  'sysml-block-node': SysMLBlockNodeFactory,
  'sysml-requirement-tree': SysMLRequirementTreeFactory,
  'sysml-requirement-relation': SysMLRequirementRelationFactory,
  'sysml-requirement-properties-panel': SysMLRequirementPropertiesPanelFactory,
  'sysml-composition-edge': SysMLCompositionEdgeFactory,
  'sysml-generalization-edge': SysMLGeneralizationEdgeFactory
}

/**
 * 注册所有核心组件工厂到视图引擎
 */
export function registerCoreComponentFactories(viewEngine: any) {
  Object.entries(coreComponentFactories).forEach(([type, Factory]) => {
    viewEngine.registerComponent(type, new Factory())
  })
  
  console.log('Registered core UML/SysML component factories:', Object.keys(coreComponentFactories))
}

/**
 * 获取组件工厂实例
 */
export function getCoreComponentFactory(type: string): ComponentFactory | null {
  const Factory = coreComponentFactories[type as keyof typeof coreComponentFactories]
  return Factory ? new Factory() : null
}

/**
 * 核心组件类型检查
 */
export function isCoreComponentType(type: string): boolean {
  return type in coreComponentFactories
}

/**
 * 获取UML组件类型列表
 */
export function getUMLComponentTypes(): string[] {
  return Object.keys(coreComponentFactories).filter(type => type.startsWith('uml-'))
}

/**
 * 获取SysML组件类型列表
 */
export function getSysMLComponentTypes(): string[] {
  return Object.keys(coreComponentFactories).filter(type => type.startsWith('sysml-'))
}

// 导出类型以供其他模块使用
export type { ComponentConfig, ComponentInstance, ComponentFactory } 