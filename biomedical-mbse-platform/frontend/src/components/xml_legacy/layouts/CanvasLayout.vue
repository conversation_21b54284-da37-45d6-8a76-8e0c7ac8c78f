<template>
  <div class="canvas-layout">
    <div class="canvas-container" ref="canvasContainer">
      <div 
        v-for="item in items"
        :key="item.id"
        class="canvas-item"
        :style="getItemStyle(item)"
      >
        <slot :item="item" :index="item.id">
          <div class="default-content">
            {{ item.content || '画布项目' }}
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface CanvasItem {
  id: string
  content?: string
  x?: number
  y?: number
  width?: number
  height?: number
  zIndex?: number
}

interface Props {
  width?: number | string
  height?: number | string
  backgroundColor?: string
  showGrid?: boolean
  gridSize?: number
  zoomable?: boolean
  pannable?: boolean
  items?: CanvasItem[]
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '500px',
  backgroundColor: '#fafafa',
  showGrid: true,
  gridSize: 20,
  zoomable: true,
  pannable: true,
  items: () => []
})

const emit = defineEmits<{
  'canvas-click': [event: MouseEvent]
  'canvas-drag': [event: MouseEvent]
  'zoom-change': [zoom: number]
}>()

const canvasContainer = ref<HTMLElement | undefined>(undefined)

const getItemStyle = (item: CanvasItem) => {
  return {
    position: 'absolute',
    left: `${item.x || 0}px`,
    top: `${item.y || 0}px`,
    width: `${item.width || 200}px`,
    height: `${item.height || 150}px`,
    zIndex: item.zIndex || 1
  }
}

// 响应式数据
const canvasRef = ref<HTMLElement>()
const zoom = ref(1)
const panX = ref(0)
const panY = ref(0)
const isDragging = ref(false)

// 计算属性
const canvasStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  backgroundColor: props.backgroundColor,
  transform: `translate(${panX.value}px, ${panY.value}px) scale(${zoom.value})`,
  cursor: isDragging.value ? 'grabbing' : (props.pannable ? 'grab' : 'default')
}))

const gridStyle = computed(() => ({
  backgroundImage: props.showGrid ? 
    `linear-gradient(to right, #e0e0e0 1px, transparent 1px),
     linear-gradient(to bottom, #e0e0e0 1px, transparent 1px)` : 'none',
  backgroundSize: props.showGrid ? `${props.gridSize}px ${props.gridSize}px` : 'auto'
}))

// 方法
const handleCanvasClick = (event: MouseEvent) => {
  emit('canvas-click', event)
}

const handleMouseDown = (event: MouseEvent) => {
  if (props.pannable) {
    isDragging.value = true
    event.preventDefault()
  }
}

const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value && props.pannable) {
    panX.value += event.movementX
    panY.value += event.movementY
    emit('canvas-drag', event)
  }
}

const handleMouseUp = () => {
  isDragging.value = false
}

const handleWheel = (event: WheelEvent) => {
  if (props.zoomable) {
    event.preventDefault()
    const delta = event.deltaY > 0 ? -0.1 : 0.1
    zoom.value = Math.max(0.1, Math.min(3, zoom.value + delta))
    emit('zoom-change', zoom.value)
  }
}

const resetView = () => {
  zoom.value = 1
  panX.value = 0
  panY.value = 0
  emit('zoom-change', zoom.value)
}

// 生命周期
onMounted(() => {
  if (props.pannable) {
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})

// 暴露方法给父组件
defineExpose({
  resetView,
  zoom: () => zoom.value,
  pan: () => ({ x: panX.value, y: panY.value })
})
</script>

<style scoped>
.canvas-layout {
  width: 100%;
  height: 100%;
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: auto;
}

.canvas-item {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
  background: var(--el-bg-color);
}

.default-content {
  padding: 16px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 