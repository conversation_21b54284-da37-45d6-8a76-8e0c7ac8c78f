<template>
  <div class="flex-layout" :style="containerStyle">
    <div 
      v-for="item in items"
      :key="item.id"
      class="flex-item"
      :style="getItemStyle(item)"
    >
      <slot :item="item" :index="item.id">
        <div class="default-content">
          {{ item.content || '弹性项目' }}
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface FlexItem {
  id: string
  content?: string
  flex?: string
  order?: number
}

interface Props {
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse'
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse'
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly'
  align?: 'stretch' | 'flex-start' | 'center' | 'flex-end' | 'baseline'
  gap?: string | number
  padding?: string | number
  width?: string | number
  height?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'row',
  wrap: 'nowrap',
  justify: 'flex-start',
  align: 'stretch',
  gap: '16px',
  padding: '0',
  width: '100%',
  height: 'auto'
})

// 计算属性
const containerStyle = computed(() => ({
  display: 'flex',
  flexDirection: props.direction,
  flexWrap: props.wrap,
  justifyContent: props.justify,
  alignItems: props.align,
  gap: typeof props.gap === 'number' ? `${props.gap}px` : props.gap,
  padding: typeof props.padding === 'number' ? `${props.padding}px` : props.padding,
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))

const isVertical = computed(() => 
  props.direction === 'column' || props.direction === 'column-reverse'
)

const isReverse = computed(() => 
  props.direction === 'row-reverse' || props.direction === 'column-reverse'
)

const items = ref<FlexItem[]>([])

const getItemStyle = (item: FlexItem) => {
  return {
    flex: item.flex || '1',
    order: item.order
  }
}
</script>

<style scoped>
.flex-layout {
  width: 100%;
  height: 100%;
}

.flex-item {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
  min-height: 100px;
}

.default-content {
  padding: 16px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page);
}
</style> 