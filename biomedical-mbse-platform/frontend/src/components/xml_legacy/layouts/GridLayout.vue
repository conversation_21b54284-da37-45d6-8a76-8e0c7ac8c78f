<template>
  <div class="grid-layout" :style="gridStyles">
    <div 
      v-for="item in items"
      :key="item.id"
      class="grid-item"
      :style="getItemStyle(item)"
    >
      <slot :item="item" :index="item.id">
        <div class="default-content">
          {{ item.content || '网格项目' }}
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface GridItem {
  id: string
  component: string
  props?: Record<string, any>
  gridArea?: string
  gridColumn?: string
  gridRow?: string
}

interface Props {
  items?: GridItem[]
  columns?: number | string
  rows?: number | string
  gap?: string | number
  padding?: string | number
  width?: string | number
  height?: string | number
  autoFlow?: 'row' | 'column' | 'row dense' | 'column dense'
  alignItems?: 'start' | 'end' | 'center' | 'stretch'
  justifyItems?: 'start' | 'end' | 'center' | 'stretch'
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  columns: 'repeat(auto-fit, minmax(250px, 1fr))',
  rows: 'auto',
  gap: '16px',
  padding: '0',
  width: '100%',
  height: 'auto',
  autoFlow: 'row',
  alignItems: 'stretch',
  justifyItems: 'stretch'
})

// 响应式数据
const gridItems = ref<GridItem[]>(props.items)

// 计算属性
const gridStyle = computed(() => ({
  display: 'grid',
  gridTemplateColumns: typeof props.columns === 'number' 
    ? `repeat(${props.columns}, 1fr)` 
    : props.columns,
  gridTemplateRows: typeof props.rows === 'number' 
    ? `repeat(${props.rows}, 1fr)` 
    : props.rows,
  gap: typeof props.gap === 'number' ? `${props.gap}px` : props.gap,
  padding: typeof props.padding === 'number' ? `${props.padding}px` : props.padding,
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  gridAutoFlow: props.autoFlow,
  alignItems: props.alignItems,
  justifyItems: props.justifyItems
}))

// 方法
const getItemStyle = (item: GridItem) => {
  const style: Record<string, string> = {}
  
  if (item.gridArea) {
    style.gridArea = item.gridArea
  } else {
    if (item.gridColumn) style.gridColumn = item.gridColumn
    if (item.gridRow) style.gridRow = item.gridRow
  }
  
  return style
}

const addItem = (item: GridItem) => {
  gridItems.value.push(item)
}

const removeItem = (id: string) => {
  const index = gridItems.value.findIndex(item => item.id === id)
  if (index > -1) {
    gridItems.value.splice(index, 1)
  }
}

const updateItem = (id: string, updates: Partial<GridItem>) => {
  const item = gridItems.value.find(item => item.id === id)
  if (item) {
    Object.assign(item, updates)
  }
}

// 暴露方法给父组件
defineExpose({
  addItem,
  removeItem,
  updateItem,
  items: () => gridItems.value
})
</script>

<style scoped>
.grid-layout {
  width: 100%;
  height: 100%;
}

.grid-item {
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.default-content {
  padding: 16px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color-page);
}
</style> 