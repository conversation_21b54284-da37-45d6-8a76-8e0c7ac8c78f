<template>
  <el-dialog v-model="dialogVisible" title="高级搜索" width="600px">
    <div class="advanced-search">
      <p>高级搜索功能正在开发中...</p>
      <el-form>
        <el-form-item label="搜索条件">
          <el-input placeholder="请输入搜索条件" />
        </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface SearchFilter {
  field: string
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than'
  value: string
  type: 'text' | 'number' | 'date' | 'boolean'
}

interface Props {
  visible: boolean
  title?: string
  fields?: Array<{ key: string; label: string; type: string }>
}

const props = withDefaults(defineProps<Props>(), {
  title: '高级搜索',
  fields: () => []
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  search: [filters: SearchFilter[]]
  reset: []
}>()

// 响应式数据
const filters = ref<SearchFilter[]>([
  { field: '', operator: 'contains', value: '', type: 'text' }
])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

const operatorOptions = computed(() => [
  { value: 'equals', label: '等于' },
  { value: 'contains', label: '包含' },
  { value: 'starts_with', label: '开头是' },
  { value: 'ends_with', label: '结尾是' },
  { value: 'greater_than', label: '大于' },
  { value: 'less_than', label: '小于' }
])

const canSearch = computed(() => 
  filters.value.some(filter => filter.field && filter.value)
)

// 方法
const addFilter = () => {
  filters.value.push({
    field: '',
    operator: 'contains',
    value: '',
    type: 'text'
  })
}

const removeFilter = (index: number) => {
  if (filters.value.length > 1) {
    filters.value.splice(index, 1)
  }
}

const getFieldConfig = (field: string) => {
  if (!props.fields) return null
  for (let i = 0; i < props.fields.length; i++) {
    if (props.fields[i].key === field) {
      return props.fields[i]
    }
  }
  return null
}

const onFieldChange = (filter: SearchFilter, field: string) => {
  const fieldConfig = getFieldConfig(field)
  if (fieldConfig) {
    filter.field = field
    filter.type = fieldConfig.type as any
    filter.value = ''
  }
}

const handleSearch = () => {
  const validFilters = filters.value.filter(f => f.field && f.value)
  emit('search', validFilters)
  dialogVisible.value = false
}

const handleReset = () => {
  filters.value = [{ field: '', operator: 'contains', value: '', type: 'text' }]
  emit('reset')
}

const handleCancel = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.advanced-search {
  padding: 20px;
}
</style> 