<template>
  <div class="metadata-importer">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>XML元数据导入</span>
          <el-button 
            class="button" 
            type="primary" 
            @click="resetImporter"
          >
            重置
          </el-button>
        </div>
      </template>

      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          v-model:file-list="fileList"
          class="upload-dragger"
          drag
          action=""
          :auto-upload="false"
          :on-change="handleFileChange"
          :before-remove="beforeRemove"
          multiple
          accept=".xml,.xmi,.bpmn"
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将XML/XMI/BPMN文件拖拽到此处，或<em>点击选择文件</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持格式：XML、XMI、BPMN、SysML（文件大小不超过100MB）
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 解析配置 -->
      <div v-if="fileList.length > 0" class="parse-config">
        <el-form :model="parseConfig" label-width="120px">
          <el-form-item label="解析选项">
            <el-checkbox-group v-model="parseConfig.options">
              <el-checkbox value="validateSchema">Schema验证</el-checkbox>
              <el-checkbox value="includeMetadata">包含元数据</el-checkbox>
              <el-checkbox value="smartRecognition">智能需求识别</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="置信度阈值">
            <el-slider 
              v-model="parseConfig.confidenceThreshold" 
              :min="0.5" 
              :max="1" 
              :step="0.05"
              show-tooltip
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 解析按钮 -->
      <div v-if="fileList.length > 0" class="action-buttons">
        <el-button 
          type="primary" 
          size="large"
          :loading="isProcessing"
          @click="startParsing"
        >
          <el-icon><DocumentAdd /></el-icon>
          开始解析
        </el-button>
      </div>

      <!-- 解析进度 -->
      <div v-if="isProcessing" class="progress-section">
        <el-progress 
          :percentage="parseProgress.percentage" 
          :status="parseProgress.status"
        />
        <div class="progress-info">
          <p>{{ parseProgress.message }}</p>
          <p v-if="parseProgress.speed">
            处理速度: {{ parseProgress.speed }} 元素/秒
          </p>
        </div>
      </div>

      <!-- 解析结果 -->
      <div v-if="parseResults.length > 0" class="results-section">
        <h3>解析结果</h3>
        <el-table :data="parseResults" style="width: 100%">
          <el-table-column prop="fileName" label="文件名" width="200" />
          <el-table-column prop="elementsCount" label="元素数量" width="120" />
          <el-table-column prop="requirementsCount" label="识别需求" width="120" />
          <el-table-column prop="confidence" label="置信度" width="100">
            <template #default="scope">
              <el-tag :type="getConfidenceColor(scope.row.confidence)">
                {{ (scope.row.confidence * 100).toFixed(1) }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button 
                size="small" 
                @click="previewRequirements(scope.row)"
              >
                预览
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="confirm-import">
          <el-button 
            type="success" 
            size="large"
            @click="confirmImport"
          >
            确认导入需求
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 需求预览对话框 -->
    <el-dialog 
      v-model="showPreviewDialog" 
      title="需求预览" 
      width="800px"
    >
      <div v-if="previewData">
        <p><strong>文件:</strong> {{ previewData.fileName }}</p>
        <p><strong>识别的需求:</strong></p>
        <el-table :data="previewData.requirements" style="width: 100%">
          <el-table-column prop="title" label="需求标题" />
          <el-table-column prop="type" label="类型" width="100" />
          <el-table-column prop="confidence" label="置信度" width="100">
            <template #default="scope">
              {{ (scope.row.confidence * 100).toFixed(1) }}%
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, DocumentAdd } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import type { Requirement } from '../../../api/modules/requirements'

// Emits
const emit = defineEmits<{
  'requirements-imported': [requirements: Requirement[]]
}>()

// 响应式数据
const fileList = ref<UploadFile[]>([])
const isProcessing = ref(false)
const showPreviewDialog = ref(false)
const previewData = ref<any>(null)

const parseConfig = reactive({
  options: ['validateSchema', 'includeMetadata', 'smartRecognition'],
  confidenceThreshold: 0.8
})

const parseProgress = reactive({
  percentage: 0,
  status: 'active' as 'active' | 'success' | 'exception',
  message: '',
  speed: ''
})

const parseResults = ref<any[]>([])

// 方法
const handleFileChange = (file: UploadFile) => {
  ElMessage.success(`已选择文件: ${file.name}`)
}

const beforeRemove = (file: UploadFile) => {
  return ElMessageBox.confirm(`确定移除文件 ${file.name}？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = fileList.value.indexOf(file)
    if (index > -1) {
      fileList.value.splice(index, 1)
    }
  }).catch(() => {
    // 用户取消删除
  })
}

const resetImporter = () => {
  fileList.value = []
  parseResults.value = []
  isProcessing.value = false
  parseProgress.percentage = 0
  parseProgress.message = ''
}

const startParsing = () => {
  isProcessing.value = true
  parseProgress.percentage = 0
  parseProgress.status = 'active'
  parseProgress.message = '开始解析XML文件...'

  // 模拟解析过程
  let currentProgress = 0
  const progressInterval = setInterval(() => {
    currentProgress += 10
    parseProgress.percentage = currentProgress
    
    if (currentProgress < 30) {
      parseProgress.message = '正在验证文件格式...'
    } else if (currentProgress < 60) {
      parseProgress.message = '正在解析XML结构...'
      parseProgress.speed = '7,699'
    } else if (currentProgress < 90) {
      parseProgress.message = '正在识别需求信息...'
    } else if (currentProgress >= 100) {
      parseProgress.message = '解析完成！'
      clearInterval(progressInterval)
      
      // 模拟解析结果
      parseResults.value = fileList.value.map((file, index) => ({
        fileName: file.name,
        elementsCount: Math.floor(Math.random() * 500) + 100,
        requirementsCount: Math.floor(Math.random() * 20) + 5,
        confidence: 0.85 + Math.random() * 0.1,
        requirements: generateMockRequirements()
      }))

      parseProgress.status = 'success'
      ElMessage.success('XML解析完成！')
      isProcessing.value = false
    }
  }, 200)
}

const generateMockRequirements = () => {
  const types = ['functional', 'performance', 'security', 'interface']
  const requirements = []
  
  for (let i = 0; i < 5; i++) {
    requirements.push({
      title: `需求-${i + 1}: 示例需求标题`,
      type: types[Math.floor(Math.random() * types.length)],
      confidence: 0.8 + Math.random() * 0.2
    })
  }
  
  return requirements
}

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.9) return 'success'
  if (confidence >= 0.7) return 'warning'
  return 'danger'
}

const previewRequirements = (result: any) => {
  previewData.value = result
  showPreviewDialog.value = true
}

const confirmImport = () => {
  // 生成模拟需求数据
  const requirements: Requirement[] = []
  
  parseResults.value.forEach((result: any) => {
    result.requirements.forEach((req: any, index: number) => {
      requirements.push({
        id: `REQ-${Date.now()}-${index}`,
        title: req.title,
        description: `从${result.fileName}中自动识别的需求`,
        type: req.type,
        priority: 'medium',
        status: 'draft',
        source: result.fileName,
        tags: ['从XML导入'],
        acceptanceCriteria: [],
        dependencies: [],
        stakeholders: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'xml_importer'
      })
    })
  })

  emit('requirements-imported', requirements)
  resetImporter()
}
</script>

<style scoped lang="scss">
.metadata-importer {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .upload-section {
    margin-bottom: 20px;
    
    :deep(.el-upload-dragger) {
      width: 100%;
      height: 180px;
    }
  }

  .parse-config {
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .action-buttons {
    text-align: center;
    margin: 20px 0;
  }

  .progress-section {
    margin: 20px 0;
    
    .progress-info {
      margin-top: 10px;
      text-align: center;
      color: #606266;
    }
  }

  .results-section {
    margin-top: 30px;
    
    h3 {
      margin-bottom: 15px;
      color: #303133;
    }
    
    .confirm-import {
      text-align: center;
      margin-top: 20px;
    }
  }
}
</style> 