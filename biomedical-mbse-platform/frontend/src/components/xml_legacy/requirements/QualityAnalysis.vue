<template>
  <div class="quality-analysis">
    <el-card>
      <template #header>
        <div class="header">
          <h3>质量分析</h3>
          <el-button type="primary" @click="runAnalysis" :loading="isAnalyzing">
            <el-icon><TrendCharts /></el-icon>
            运行分析
          </el-button>
        </div>
      </template>
      
      <div class="analysis-content">
        <div v-if="!analysisResults && !isAnalyzing" class="no-data">
          <el-empty description="暂无分析结果" />
          <p>点击"运行分析"开始质量评估</p>
        </div>
        
        <div v-if="isAnalyzing" class="analyzing">
          <el-progress :percentage="progress" />
          <p>正在分析中...</p>
        </div>
        
        <div v-if="analysisResults" class="results">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ analysisResults.overallScore }}</div>
                <div class="metric-label">总体评分</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ analysisResults.completeness }}%</div>
                <div class="metric-label">完整性</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ analysisResults.consistency }}%</div>
                <div class="metric-label">一致性</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-card">
                <div class="metric-value">{{ analysisResults.clarity }}%</div>
                <div class="metric-label">清晰度</div>
              </div>
            </el-col>
          </el-row>
          
          <div class="issues-section">
            <h4>发现的问题</h4>
            <el-table :data="analysisResults.issues" size="small">
              <el-table-column prop="type" label="类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getIssueType(row.severity)" size="small">
                    {{ row.type }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" />
              <el-table-column prop="severity" label="严重程度" width="100">
                <template #default="{ row }">
                  <el-tag :type="getIssueType(row.severity)" size="small">
                    {{ row.severity }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button size="small" @click="fixIssue(row)">修复</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

interface QualityIssue {
  id: string
  type: string
  description: string
  severity: 'high' | 'medium' | 'low'
  suggestion?: string
}

interface QualityResults {
  overallScore: number
  completeness: number
  consistency: number
  clarity: number
  issues: QualityIssue[]
}

interface Props {
  data?: any[]
  autoAnalyze?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  autoAnalyze: false
})

const emit = defineEmits<{
  'analysis-complete': [results: QualityResults]
  'issue-fixed': [issue: QualityIssue]
}>()

// 响应式数据
const isAnalyzing = ref(false)
const progress = ref(0)
const analysisResults = ref<QualityResults | null>(null)

// 计算属性
const hasData = computed(() => props.data && props.data.length > 0)

// 方法
const runAnalysis = async () => {
  if (!hasData.value) {
    ElMessage.warning('没有数据需要分析')
    return
  }
  
  isAnalyzing.value = true
  progress.value = 0
  
  try {
    // 模拟分析过程
    for (let i = 0; i <= 100; i += 10) {
      progress.value = i
      await new Promise(resolve => setTimeout(resolve, 200))
    }
    
    // 生成模拟结果
    analysisResults.value = {
      overallScore: 85,
      completeness: 92,
      consistency: 78,
      clarity: 85,
      issues: [
        {
          id: '1',
          type: '命名不一致',
          description: '发现5个命名不一致的元素',
          severity: 'medium'
        },
        {
          id: '2', 
          type: '缺失描述',
          description: '3个元素缺少描述信息',
          severity: 'low'
        },
        {
          id: '3',
          type: '循环依赖',
          description: '检测到潜在的循环依赖关系',
          severity: 'high'
        }
      ]
    }
    
    emit('analysis-complete', analysisResults.value)
    ElMessage.success('质量分析完成')
    
  } catch (error) {
    ElMessage.error('分析失败: ' + error)
  } finally {
    isAnalyzing.value = false
  }
}

const getIssueType = (severity: string) => {
  switch (severity) {
    case 'high': return 'danger'
    case 'medium': return 'warning'
    case 'low': return 'info'
    default: return 'default'
  }
}

const fixIssue = (issue: QualityIssue) => {
  ElMessage.info(`开始修复问题: ${issue.description}`)
  emit('issue-fixed', issue)
  
  // 从结果中移除已修复的问题
  if (analysisResults.value) {
    const index = analysisResults.value.issues.findIndex(i => i.id === issue.id)
    if (index > -1) {
      analysisResults.value.issues.splice(index, 1)
    }
  }
}

// 自动分析
if (props.autoAnalyze && hasData.value) {
  runAnalysis()
}
</script>

<style scoped>
.quality-analysis {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h3 {
  margin: 0;
}

.analysis-content {
  min-height: 300px;
}

.no-data,
.analyzing {
  text-align: center;
  padding: 40px 20px;
}

.metric-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.issues-section {
  margin-top: 24px;
}

.issues-section h4 {
  margin: 0 0 16px 0;
}
</style> 