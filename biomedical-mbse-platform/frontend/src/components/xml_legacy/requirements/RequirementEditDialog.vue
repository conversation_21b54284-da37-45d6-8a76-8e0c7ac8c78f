<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEditing ? '编辑需求' : '创建需求'"
    width="800px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      @submit.prevent
    >
      <!-- 基本信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="需求ID" prop="id">
            <el-input 
              v-model="formData.id"
              :disabled="isEditing"
              placeholder="自动生成或手动输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="需求类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择需求类型">
              <el-option 
                v-for="type in requirementTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="需求标题" prop="title">
        <el-input 
          v-model="formData.title"
          placeholder="请输入需求标题"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="需求描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="详细描述需求的功能、性能等特征"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>

      <!-- 优先级和状态 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-rate 
              v-model="formData.priority"
              :max="5"
              :texts="['很低', '低', '中', '高', '很高']"
              show-text
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态">
              <el-option 
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 关系设置 -->
      <el-form-item label="父需求">
        <RequirementSelector
          v-model="formData.parentId"
          :exclude="[formData.id]"
          placeholder="选择父需求（可选）"
        />
      </el-form-item>

      <!-- 验收标准 -->
      <el-form-item label="验收标准">
        <div class="acceptance-criteria">
          <div 
            v-for="(criterion, index) in formData.acceptanceCriteria"
            :key="index"
            class="criterion-item"
          >
            <el-row :gutter="10">
              <el-col :span="16">
                <el-input
                  v-model="criterion.description"
                  placeholder="验收标准描述"
                />
              </el-col>
              <el-col :span="6">
                <el-select 
                  v-model="criterion.verificationMethod"
                  placeholder="验证方法"
                >
                  <el-option label="分析" value="analysis" />
                  <el-option label="检查" value="inspection" />
                  <el-option label="演示" value="demonstration" />
                  <el-option label="测试" value="test" />
                </el-select>
              </el-col>
              <el-col :span="2">
                <el-button 
                  type="danger"
                  icon="Delete"
                  size="small"
                  @click="removeCriterion(index)"
                />
              </el-col>
            </el-row>
          </div>
          
          <el-button 
            type="primary"
            icon="Plus"
            size="small"
            @click="addCriterion"
          >
            添加验收标准
          </el-button>
        </div>
      </el-form-item>

      <!-- 元数据 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="元数据信息" name="metadata">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="风险等级">
                <el-select v-model="formData.metadata.riskLevel">
                  <el-option label="低" value="low" />
                  <el-option label="中" value="medium" />
                  <el-option label="高" value="high" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="复杂度">
                <el-input-number 
                  v-model="formData.metadata.complexity"
                  :min="1"
                  :max="10"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="预估工作量">
                <el-input-number 
                  v-model="formData.metadata.estimatedEffort"
                  :min="0"
                  :precision="1"
                />
                <span style="margin-left: 10px;">人天</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="XML元素ID">
                <el-input 
                  v-model="formData.metadata.xmlElementId"
                  placeholder="关联的XML元素ID"
                  readonly
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="干系人">
            <el-select 
              v-model="formData.metadata.stakeholders"
              multiple
              filterable
              allow-create
              placeholder="选择或输入干系人"
            >
              <el-option 
                v-for="stakeholder in stakeholderOptions"
                :key="stakeholder"
                :label="stakeholder"
                :value="stakeholder"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="标签">
            <el-select 
              v-model="formData.metadata.tags"
              multiple
              filterable
              allow-create
              placeholder="添加标签"
            >
              <el-option 
                v-for="tag in tagOptions"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="saving"
          @click="handleSave"
        >
          保存
        </el-button>
        <el-button 
          v-if="!isEditing"
          type="success"
          :loading="saving"
          @click="handleSaveAndCreate"
        >
          保存并继续创建
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Plus } from '@element-plus/icons-vue'
import type { Requirement, AcceptanceCriterion } from '../../../api/modules/requirements'
import RequirementSelector from './RequirementSelector.vue'

// Props
interface Props {
  modelValue: boolean
  requirement?: Partial<Requirement> | null
}

const props = withDefaults(defineProps<Props>(), {
  requirement: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  save: [requirement: Requirement]
}>()

// 响应式数据
const formRef = ref<any>()
const saving = ref(false)
const activeCollapse = ref(['metadata'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const isEditing = computed(() => !!(props.requirement?.id))

// 表单数据
const formData = reactive<Partial<Requirement>>({
  id: '',
  title: '',
  description: '',
  type: 'functional',
  priority: 'medium',
  status: 'draft',
  source: '',
  tags: [],
  acceptanceCriteria: [],
  dependencies: [],
  stakeholders: [],
  createdBy: ''
})

// 选项数据
const requirementTypes = [
  { label: '功能需求', value: 'functional' },
  { label: '性能需求', value: 'performance' },
  { label: '安全需求', value: 'security' },
  { label: '接口需求', value: 'interface' },
  { label: '约束需求', value: 'constraint' }
]

const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '审核中', value: 'review' },
  { label: '已批准', value: 'approved' },
  { label: '已实现', value: 'implemented' },
  { label: '已测试', value: 'tested' },
  { label: '已验证', value: 'verified' },
  { label: '已废弃', value: 'obsolete' }
]

const stakeholderOptions = [
  '产品经理', '系统架构师', '开发团队', '测试团队', 
  '业务分析师', '最终用户', '运维团队', '安全团队'
]

const tagOptions = [
  '核心功能', '用户界面', '性能优化', '安全加固',
  '易用性', '可维护性', '可扩展性', '兼容性'
]

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入需求标题', trigger: 'blur' },
    { min: 5, max: 200, message: '标题长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入需求描述', trigger: 'blur' },
    { min: 10, max: 2000, message: '描述长度在 10 到 2000 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择需求类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请设置优先级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 方法
const initFormData = () => {
  if (props.requirement) {
    // 手动复制属性而不使用Object.assign
    formData.id = props.requirement.id || ''
    formData.title = props.requirement.title || ''
    formData.description = props.requirement.description || ''
    formData.type = props.requirement.type || 'functional'
    formData.priority = props.requirement.priority || 'medium'
    formData.status = props.requirement.status || 'draft'
    formData.source = props.requirement.source || ''
    formData.tags = props.requirement.tags || []
    formData.acceptanceCriteria = props.requirement.acceptanceCriteria || []
    formData.dependencies = props.requirement.dependencies || []
    formData.stakeholders = props.requirement.stakeholders || []
    formData.createdBy = props.requirement.createdBy || ''
  } else {
    // 重置为默认值
    formData.id = ''
    formData.title = ''
    formData.description = ''
    formData.type = 'functional'
    formData.priority = 'medium'
    formData.status = 'draft'
    formData.source = ''
    formData.tags = []
    formData.acceptanceCriteria = []
    formData.dependencies = []
    formData.stakeholders = []
    formData.createdBy = ''
  }
}

const addCriterion = () => {
  formData.acceptanceCriteria?.push({
    id: Date.now().toString(),
    description: '',
    completed: false,
    testScenarios: []
  })
}

const removeCriterion = (index: number) => {
  formData.acceptanceCriteria?.splice(index, 1)
}

const validateForm = async () => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    ElMessage.error('请检查表单输入')
    return false
  }
}

const handleSave = async () => {
  if (!(await validateForm())) return

  saving.value = true
  
  try {
    // 验证验收标准
    const validCriteria = formData.acceptanceCriteria?.filter(c => c.description.trim()) || []
    if (validCriteria.length === 0) {
      ElMessage.warning('至少需要添加一个验收标准')
      return
    }

    const requirementData = {
      ...formData,
      acceptanceCriteria: validCriteria,
      createdBy: formData.createdBy || 'current_user', // 应该从用户状态获取
      createdAt: formData.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as Requirement

    emit('save', requirementData)
    ElMessage.success(isEditing.value ? '需求更新成功' : '需求创建成功')
    
  } catch (error: any) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const handleSaveAndCreate = async () => {
  await handleSave()
  // 保存后清空表单继续创建
  initFormData()
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleClose = async () => {
  // 检查是否有未保存的更改
  const hasChanges = checkFormChanges()
  
  if (hasChanges) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要关闭吗？',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消关闭
    }
  }
  
  emit('update:modelValue', false)
  // 清理表单
  nextTick(() => {
    formRef.value?.resetFields()
    initFormData()
  })
}

const checkFormChanges = () => {
  // 简单的变更检测
  if (!props.requirement) {
    return formData.title || formData.description
  }
  
  return (
    formData.title !== props.requirement.title ||
    formData.description !== props.requirement.description ||
    formData.type !== props.requirement.type ||
    formData.priority !== props.requirement.priority ||
    formData.status !== props.requirement.status
  )
}

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      initFormData()
      nextTick(() => {
        formRef.value?.clearValidate()
      })
    }
  }
)

watch(
  () => props.requirement,
  () => {
    if (props.modelValue) {
      initFormData()
    }
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.acceptance-criteria {
  .criterion-item {
    margin-bottom: 10px;
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-collapse-item__header) {
  font-weight: 500;
}
</style> 