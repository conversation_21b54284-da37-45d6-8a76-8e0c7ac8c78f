<template>
  <div class="requirement-management">
    <div class="management-header">
      <h2>需求管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建需求
        </el-button>
        <el-button @click="exportRequirements">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>
    
    <div class="management-content">
      <!-- 筛选和搜索 -->
      <el-card class="filter-card">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-input 
              v-model="searchText" 
              placeholder="搜索需求..." 
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filterStatus" placeholder="状态筛选" clearable>
              <el-option label="全部" value="" />
              <el-option label="草稿" value="draft" />
              <el-option label="待审核" value="pending" />
              <el-option label="已批准" value="approved" />
              <el-option label="已实现" value="implemented" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filterPriority" placeholder="优先级筛选" clearable>
              <el-option label="全部" value="" />
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <div class="view-controls">
              <el-radio-group v-model="viewMode" size="small">
                <el-radio-button label="list">列表</el-radio-button>
                <el-radio-button label="card">卡片</el-radio-button>
                <el-radio-button label="tree">树形</el-radio-button>
              </el-radio-group>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 需求列表/卡片/树形视图 -->
      <el-card class="requirements-display">
        <!-- 列表视图 -->
        <el-table v-if="viewMode === 'list'" :data="filteredRequirements" stripe>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="负责人" width="120" />
          <el-table-column prop="created_at" label="创建时间" width="180" />
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click="viewRequirement(row)">查看</el-button>
              <el-button size="small" @click="editRequirement(row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteRequirement(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 卡片视图 -->
        <div v-else-if="viewMode === 'card'" class="card-view">
          <div class="requirements-grid">
            <div 
              v-for="requirement in filteredRequirements" 
              :key="requirement.id"
              class="requirement-card"
              @click="viewRequirement(requirement)"
            >
              <div class="card-header">
                <h4>{{ requirement.title }}</h4>
                <el-tag :type="getPriorityType(requirement.priority)" size="small">
                  {{ getPriorityText(requirement.priority) }}
                </el-tag>
              </div>
              <p class="card-description">{{ requirement.description }}</p>
              <div class="card-footer">
                <el-tag :type="getStatusType(requirement.status)" size="small">
                  {{ getStatusText(requirement.status) }}
                </el-tag>
                <span class="assignee">{{ requirement.assignee || '未分配' }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 树形视图 -->
        <el-tree
          v-else-if="viewMode === 'tree'"
          :data="treeRequirements"
          :props="{ children: 'children', label: 'title' }"
          node-key="id"
          default-expand-all
          @node-click="viewRequirement"
        >
          <template #default="{ node, data }">
            <span class="tree-node">
              <span>{{ data.title }}</span>
              <el-tag :type="getStatusType(data.status)" size="small">
                {{ getStatusText(data.status) }}
              </el-tag>
            </span>
          </template>
        </el-tree>
      </el-card>
    </div>
    
    <!-- 创建需求对话框 -->
    <RequirementEditDialog
      v-model="showCreateDialog"
      :requirement="newRequirement"
      @save="handleCreateRequirement"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { Requirement } from '../../../api/modules/requirements'
import RequirementEditDialog from './RequirementEditDialog.vue'

interface Props {
  projectId?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'requirement-selected': [requirement: Requirement]
  'requirements-updated': [requirements: Requirement[]]
}>()

// 响应式数据
const requirements = ref<Requirement[]>([])
const searchText = ref('')
const filterStatus = ref('')
const filterPriority = ref('')
const viewMode = ref<'list' | 'card' | 'tree'>('list')
const showCreateDialog = ref(false)
const loading = ref(false)

// 新需求模板
const newRequirement = ref<Partial<Requirement>>({
  title: '',
  description: '',
  priority: 'medium',
  status: 'draft'
})

// 计算属性
const filteredRequirements = computed(() => {
  let filtered = requirements.value

  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(req => 
      req.title.toLowerCase().includes(search) ||
      req.description.toLowerCase().includes(search)
    )
  }

  if (filterStatus.value) {
    filtered = filtered.filter(req => req.status === filterStatus.value)
  }

  if (filterPriority.value) {
    filtered = filtered.filter(req => req.priority === filterPriority.value)
  }

  return filtered
})

const treeRequirements = computed(() => {
  // 暂时禁用树状结构，因为Requirement接口不支持parent_id
  return filteredRequirements.value
})

// 方法
const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'high': return 'danger'
    case 'medium': return 'warning'  
    case 'low': return 'info'
    default: return 'default'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return priority
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'approved': return 'success'
    case 'implemented': return 'success'
    case 'pending': return 'warning'
    case 'draft': return 'info'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'draft': return '草稿'
    case 'pending': return '待审核'
    case 'approved': return '已批准'
    case 'implemented': return '已实现'
    default: return status
  }
}

const loadRequirements = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    requirements.value = [
      {
        id: 'REQ-001',
        title: '用户登录功能',
        description: '系统应支持用户通过用户名和密码登录',
        type: 'functional',
        priority: 'high',
        status: 'approved',
        source: 'user_requirements.xml',
        tags: ['核心功能', '安全'],
        acceptanceCriteria: [],
        dependencies: [],
        stakeholders: ['张三'],
        createdAt: '2025-01-10T09:00:00Z',
        updatedAt: '2025-01-10T09:00:00Z',
        createdBy: 'analyst_01',
        assignedTo: 'dev_team_01'
      },
      {
        id: 'REQ-002', 
        title: '数据导出功能',
        description: '用户应能够导出数据为Excel格式',
        type: 'functional',
        priority: 'medium',
        status: 'reviewed',
        source: 'business_requirements.xml',
        tags: ['数据处理', '用户体验'],
        acceptanceCriteria: [],
        dependencies: [],
        stakeholders: ['李四'],
        createdAt: '2025-01-11T10:30:00Z',
        updatedAt: '2025-01-11T10:30:00Z',
        createdBy: 'analyst_02',
        assignedTo: 'dev_team_02'
      },
      {
        id: 'REQ-003',
        title: '系统性能优化',
        description: '系统响应时间应在2秒内',
        type: 'non-functional',
        priority: 'high',
        status: 'draft',
        source: 'performance_requirements.xml',
        tags: ['性能', '优化'],
        acceptanceCriteria: [],
        dependencies: [],
        stakeholders: ['运维团队'],
        createdAt: '2025-01-12T14:15:00Z',
        updatedAt: '2025-01-12T14:15:00Z',
        createdBy: 'analyst_03'
      }
    ]
    
    emit('requirements-updated', requirements.value)
  } catch (error) {
    ElMessage.error('加载需求失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const viewRequirement = (requirement: Requirement) => {
  emit('requirement-selected', requirement)
}

const editRequirement = (requirement: Requirement) => {
  // 编辑逻辑
  ElMessage.info(`编辑需求: ${requirement.title}`)
}

const deleteRequirement = async (requirement: Requirement) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除需求"${requirement.title}"吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    const index = requirements.value.findIndex(r => r.id === requirement.id)
    if (index > -1) {
      requirements.value.splice(index, 1)
      ElMessage.success('需求已删除')
      emit('requirements-updated', requirements.value)
    }
  } catch {
    // 用户取消删除
  }
}

const handleCreateRequirement = (requirement: Requirement) => {
  // 模拟添加到列表
  requirements.value.unshift({
    ...requirement,
    id: generateRequirementId(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  })
  
  showCreateDialog.value = false
  ElMessage.success('需求创建成功')
  emit('requirements-updated', requirements.value)
}

const exportRequirements = () => {
  ElMessage.success('需求导出功能开发中...')
}

const generateRequirementId = () => {
  const nextId = requirements.value.length + 1
  const idStr = String(nextId)
  const paddedId = idStr.length === 1 ? '00' + idStr : idStr.length === 2 ? '0' + idStr : idStr
  return `REQ-${paddedId}`
}

// 生命周期
onMounted(() => {
  loadRequirements()
})
</script>

<style scoped>
.requirement-management {
  padding: 20px;
}

.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.management-header h2 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.management-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-card,
.requirements-display {
  margin-bottom: 16px;
}

.view-controls {
  display: flex;
  justify-content: flex-end;
}

.card-view {
  min-height: 400px;
}

.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.requirement-card {
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.requirement-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-header h4 {
  margin: 0;
  font-size: 16px;
  flex: 1;
}

.card-description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.assignee {
  font-size: 12px;
  color: #999;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}
</style> 