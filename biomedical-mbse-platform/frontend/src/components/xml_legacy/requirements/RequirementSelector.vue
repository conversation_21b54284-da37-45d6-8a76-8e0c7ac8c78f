<template>
  <el-select
    v-model="selectedValue"
    placeholder="选择需求"
    filterable
    clearable
    :loading="loading"
    @change="handleChange"
  >
    <el-option
      v-for="req in filteredRequirements"
      :key="req.id"
      :label="`${req.id} - ${req.title}`"
      :value="req.id"
      :disabled="exclude.indexOf(req.id) !== -1"
    >
      <div class="requirement-option">
        <span class="req-title">{{ req.title }}</span>
        <el-tag :type="getTypeColor(req.type)" size="small">
          {{ req.type }}
        </el-tag>
      </div>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { Requirement } from '../../../api/modules/requirements'

interface Props {
  modelValue?: string
  exclude?: string[]
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  exclude: () => [],
  placeholder: '请选择需求'
})

const emit = defineEmits<{
  'update:modelValue': [value: string | undefined]
}>()

const loading = ref(false)
const requirements = ref<Requirement[]>([])

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value: string | undefined) => emit('update:modelValue', value)
})

const filteredRequirements = computed(() => {
  return requirements.value.filter(req => props.exclude.indexOf(req.id) === -1)
})

const handleChange = (value: string) => {
  emit('update:modelValue', value)
}

const getTypeColor = (type: string) => {
  switch (type) {
    case 'functional': return 'primary'
    case 'performance': return 'success'
    case 'security': return 'danger'
    case 'interface': return 'warning'
    default: return 'info'
  }
}

// 模拟数据加载
const loadRequirements = async () => {
  loading.value = true
  try {
    // 这里应该调用实际的API
    requirements.value = [
      {
        id: 'REQ-001',
        title: '用户登录功能',
        type: 'functional',
        description: '系统应支持用户通过用户名和密码登录',
        priority: 'high',
        status: 'approved',
        source: 'system_requirements.xml',
        tags: ['核心功能', '安全'],
        acceptanceCriteria: [],
        dependencies: [],
        stakeholders: ['产品经理', '开发团队'],
        estimatedEffort: 3,
        riskLevel: 'medium',
        createdAt: '2025-06-17T10:00:00Z',
        updatedAt: '2025-06-17T10:00:00Z',
        createdBy: 'analyst_01'
      },
      {
        id: 'REQ-002', 
        title: '系统响应时间',
        type: 'non-functional',
        description: '系统响应时间应小于2秒',
        priority: 'medium',
        status: 'draft',
        source: 'performance_requirements.xml',
        tags: ['性能', '用户体验'],
        acceptanceCriteria: [],
        dependencies: [],
        stakeholders: ['测试团队', '运维团队'],
        estimatedEffort: 2,
        riskLevel: 'low',
        createdAt: '2025-06-17T11:00:00Z',
        updatedAt: '2025-06-17T11:00:00Z',
        createdBy: 'analyst_02'
      }
    ] as Requirement[]
  } catch (error) {
    console.error('加载需求列表失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadRequirements()
})
</script>

<style scoped>
.requirement-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.req-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 