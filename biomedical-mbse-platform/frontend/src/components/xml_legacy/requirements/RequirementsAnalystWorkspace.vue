<template>
  <div class="requirements-analyst-workspace">
    <!-- 顶部项目条 -->
    <div class="project-context-bar">
      <div class="project-selector">
        <span class="context-label">当前项目：</span>
        <el-select v-model="currentProject" placeholder="选择项目" class="project-select" size="small">
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
      </div>
      <div class="project-info" v-if="currentProjectInfo">
        <el-tag type="info" size="small">{{ currentProjectInfo.domain }}</el-tag>
        <el-tag :type="getStatusType(currentProjectInfo.status)" size="small">
          {{ currentProjectInfo.status }}
        </el-tag>
      </div>
      <div class="workspace-title">
        <h3>需求分析师工作台</h3>
        <span class="subtitle">基于MBSE的需求建模与追溯性管理</span>
      </div>
    </div>

    <!-- 统计仪表板 -->
    <div class="stats-dashboard">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon requirements">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ workspaceStats.totalRequirements }}</div>
              <div class="stat-label">需求总数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ workspaceStats.completedRequirements }}</div>
              <div class="stat-label">已验证</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon traceability">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ workspaceStats.traceabilityRatio }}%</div>
              <div class="stat-label">追溯完整性</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon quality">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ workspaceStats.qualityScore }}%</div>
              <div class="stat-label">模型质量</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主工作区布局 -->
    <div class="main-workspace-layout">
      <!-- 左侧快速操作面板 -->
      <div class="left-sidebar">
        <el-card class="quick-actions-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Operation /></el-icon>
              <span>快速操作</span>
            </div>
          </template>
          <div class="action-grid">
            <el-button 
              type="primary" 
              icon="UploadFilled"
              @click="activeTab = 'import'"
              class="action-btn"
            >
              导入模型
            </el-button>
            <el-button 
              type="success" 
              icon="Plus"
              @click="createNewRequirement"
              class="action-btn"
            >
              新建需求
            </el-button>
            <el-button 
              type="info" 
              icon="Search"
              @click="openAdvancedSearch"
              class="action-btn"
            >
              高级搜索
            </el-button>
            <el-button 
              type="warning" 
              icon="TrendCharts"
              @click="activeTab = 'quality'"
              class="action-btn"
            >
              质量验证
            </el-button>
          </div>
        </el-card>

        <el-card class="recommendations-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Star /></el-icon>
              <span>智能推荐</span>
            </div>
          </template>
          <div class="recommendation-list">
            <div 
              v-for="rec in intelligentRecommendations"
              :key="rec.id"
              class="recommendation-item"
              @click="handleRecommendation(rec)"
            >
              <div class="rec-priority">
                <el-tag :type="getPriorityColor(rec.priority)" size="small">
                  {{ rec.priority }}
                </el-tag>
              </div>
              <div class="rec-content">
                <div class="rec-title">{{ rec.title }}</div>
                <div class="rec-description">{{ rec.description }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 中央主内容区 -->
      <div class="main-content-area">
        <el-tabs v-model="activeTab" type="card" class="workspace-tabs">
          <el-tab-pane label="模型导入" name="import">
            <div class="tab-content-wrapper">
              <MetadataImporter @requirements-imported="handleRequirementsImported" />
            </div>
          </el-tab-pane>

          <el-tab-pane label="需求管理" name="management">
            <div class="tab-content-wrapper">
              <RequirementManagement 
                :requirements="requirements"
                @requirement-created="handleRequirementCreated"
                @requirement-updated="handleRequirementUpdated"
                @requirement-deleted="handleRequirementDeleted"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="追溯分析" name="traceability">
            <div class="tab-content-wrapper">
              <TraceabilityAnalysis 
                :requirements="requirements"
                @trace-created="handleTraceCreated"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="验证场景" name="scenarios">
            <div class="tab-content-wrapper">
              <ScenarioManagement 
                :requirements="requirements"
                @scenario-created="handleScenarioCreated"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="质量分析" name="quality">
            <div class="tab-content-wrapper">
              <QualityAnalysis 
                :requirements="requirements"
                @analysis-updated="handleAnalysisUpdated"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 右侧活动面板 -->
      <div class="right-sidebar">
        <el-card class="activities-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Timer /></el-icon>
              <span>最近活动</span>
            </div>
          </template>
          <div class="activity-timeline">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="getActivityClass(activity.type)">
                <el-icon>
                  <component :is="getActivityIcon(activity.type)" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-action">{{ activity.action }}</div>
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-time">{{ activity.timestamp }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 对话框组件 -->
    <RequirementEditDialog
      v-model="showCreateDialog"
      @save="handleRequirementCreated"
    />

    <AdvancedSearchDialog
      v-model:visible="showSearchDialog"
      @search="handleAdvancedSearch"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  UploadFilled, Plus, Search, TrendCharts, Star, 
  Document, CircleCheckFilled, Collection, Trophy,
  Operation, Timer, Setting, 
  DocumentAdd, Check, TrendCharts as Charts
} from '@element-plus/icons-vue'
import type { Requirement } from '../../../api/modules/requirements'
import MetadataImporter from './MetadataImporter.vue'
import RequirementEditDialog from './RequirementEditDialog.vue'
import RequirementManagement from './RequirementManagement.vue'
import TraceabilityAnalysis from './TraceabilityAnalysis.vue'
import ScenarioManagement from './ScenarioManagement.vue'
import QualityAnalysis from './QualityAnalysis.vue'
import AdvancedSearchDialog from './AdvancedSearchDialog.vue'

// 响应式数据
const activeTab = ref('import')
const showCreateDialog = ref(false)
const showSearchDialog = ref(false)
const requirements = ref<Requirement[]>([])
const currentProject = ref<string>('')
const currentProjectInfo = ref<any>(null)

// 项目数据
const projects = ref([
  {
    id: 'proj-001',
    name: '智能交通系统',
    domain: 'SysML',
    status: '进行中',
    description: '基于SysML的智能交通管理系统建模项目'
  },
  {
    id: 'proj-002', 
    name: '企业数据平台',
    domain: 'UML',
    status: '规划中',
    description: '企业级数据处理平台架构设计'
  },
  {
    id: 'proj-003',
    name: '医疗设备控制',
    domain: 'SysML',
    status: '已完成',
    description: '医疗设备嵌入式控制系统'
  }
])

// 工作台统计数据
const workspaceStats = reactive({
  totalRequirements: 0,
  completedRequirements: 0,
  traceabilityRatio: 0,
  qualityScore: 0
})

// 智能推荐
const intelligentRecommendations = ref([
  {
    id: '1',
    title: '补充需求追溯',
    description: '检测到5个需求缺少上游追溯链接',
    priority: 'high',
    action: 'traceability'
  },
  {
    id: '2',
    title: '验收标准优化',
    description: '建议为3个功能需求添加更详细的验收标准',
    priority: 'medium',
    action: 'acceptance'
  },
  {
    id: '3',
    title: '重复需求检测',
    description: '发现2个可能重复的需求需要合并',
    priority: 'high',
    action: 'duplicate'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: '1',
    action: '导入需求模型',
    description: '成功导入包含156个元素的SysML模型',
    timestamp: '2025-06-17 14:30',
    type: 'import'
  },
  {
    id: '2',
    action: '创建需求',
    description: '新建了功能需求"用户身份验证"',
    timestamp: '2025-06-17 14:15',
    type: 'create'
  },
  {
    id: '3',
    action: '更新追溯关系',
    description: '为需求REQ-001建立了到设计元素的追溯',
    timestamp: '2025-06-17 14:00',
    type: 'trace'
  }
])

// 计算属性
const completionRate = computed(() => {
  if (workspaceStats.totalRequirements === 0) return 0
  return Math.round((workspaceStats.completedRequirements / workspaceStats.totalRequirements) * 100)
})

// 事件处理方法
const handleRequirementsImported = (importedRequirements: Requirement[]) => {
  requirements.value.push(...importedRequirements)
  updateWorkspaceStats()
  
  ElMessage.success(`成功导入 ${importedRequirements.length} 个需求`)
  
  // 添加到最近活动
  recentActivities.value.unshift({
    id: Date.now().toString(),
    action: '批量导入需求',
    description: `从需求模型中识别并导入了 ${importedRequirements.length} 个需求`,
    timestamp: new Date().toLocaleString(),
    type: 'import'
  })
}

const handleRequirementCreated = (requirement: Requirement) => {
  requirements.value.push(requirement)
  updateWorkspaceStats()
  showCreateDialog.value = false
  
  ElMessage.success('需求创建成功')
  
  // 添加到最近活动
  recentActivities.value.unshift({
    id: Date.now().toString(),
    action: '创建需求',
    description: `新建了${requirement.type}需求"${requirement.title}"`,
    timestamp: new Date().toLocaleString(),
    type: 'create'
  })
}

const handleRequirementUpdated = (requirement: Requirement) => {
  const index = requirements.value.findIndex(r => r.id === requirement.id)
  if (index !== -1) {
    requirements.value[index] = requirement
    updateWorkspaceStats()
    ElMessage.success('需求更新成功')
  }
}

const handleRequirementDeleted = (requirementId: string) => {
  const index = requirements.value.findIndex(r => r.id === requirementId)
  if (index !== -1) {
    requirements.value.splice(index, 1)
    updateWorkspaceStats()
    ElMessage.success('需求删除成功')
  }
}

const handleTraceCreated = (trace: any) => {
  ElMessage.success('追溯关系创建成功')
  updateWorkspaceStats()
}

const handleScenarioCreated = (scenario: any) => {
  ElMessage.success('验证场景创建成功')
}

const handleAnalysisUpdated = (analysis: any) => {
  workspaceStats.qualityScore = analysis.overallScore
  ElMessage.success('质量分析已更新')
}

const createNewRequirement = () => {
  showCreateDialog.value = true
}

const openAdvancedSearch = () => {
  showSearchDialog.value = true
}

const handleAdvancedSearch = (searchResults: Requirement[]) => {
  // 处理高级搜索结果
  ElMessage.info(`找到 ${searchResults.length} 个匹配的需求`)
  showSearchDialog.value = false
}

const handleRecommendation = (recommendation: any) => {
  switch (recommendation.action) {
    case 'traceability':
      activeTab.value = 'traceability'
      break
    case 'acceptance':
      activeTab.value = 'management'
      break
    case 'duplicate':
      activeTab.value = 'quality'
      break
  }
  
  ElMessage.info(`已切换到相关功能: ${recommendation.title}`)
}

const updateWorkspaceStats = () => {
  workspaceStats.totalRequirements = requirements.value.length
  workspaceStats.completedRequirements = requirements.value.filter(
    r => {
      const completedStatuses = ['implemented', 'tested', 'verified']
      return completedStatuses.indexOf(r.status) !== -1
    }
  ).length
  
  // 模拟追溯完整性计算
  workspaceStats.traceabilityRatio = Math.round(Math.random() * 30 + 70)
  
  // 模拟模型质量计算
  workspaceStats.qualityScore = Math.round(Math.random() * 20 + 75)
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high': return 'danger'
    case 'medium': return 'warning'
    case 'low': return 'info'
    default: return 'info'
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case '进行中': return 'success'
    case '规划中': return 'warning'
    case '已完成': return 'info'
    default: return 'info'
  }
}

const getActivityClass = (type: string) => {
  return `activity-${type}`
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'import': return 'UploadFilled'
    case 'create': return 'DocumentAdd'
    case 'update': return 'Check'
    case 'trace': return 'TrendCharts'
    default: return 'Setting'
  }
}

// 初始化
onMounted(() => {
  updateWorkspaceStats()
  
  // 默认选择第一个项目
  if (projects.value[0]) {
    currentProject.value = projects.value[0].id
    currentProjectInfo.value = projects.value[0]
  }
  
  // 模拟加载已有需求
  requirements.value = [
    {
      id: 'REQ-001',
      title: '用户身份验证',
      description: '系统应支持多种身份验证方式',
      type: 'functional',
      priority: 'high',
      status: 'approved',
      source: 'system_analysis.xml',
      tags: ['安全', '核心功能'],
      acceptanceCriteria: [],
      dependencies: [],
      stakeholders: ['产品经理', '开发团队'],
      estimatedEffort: 5,
      businessValue: 9,
      riskLevel: 'medium',
      createdAt: '2025-06-17T10:00:00Z',
      updatedAt: '2025-06-17T10:00:00Z',
      createdBy: 'analyst_01',
      assignedTo: 'dev_team_01'
    }
  ] as Requirement[]
  
  updateWorkspaceStats()
})

// 监听项目切换
watch(() => currentProject.value, (newProjectId: string) => {
  const project = projects.value.find(p => p.id === newProjectId)
  currentProjectInfo.value = project
  if (project) {
    ElMessage.success(`已切换到项目：${project.name}`)
    // 重新加载项目相关的需求数据
    loadProjectRequirements(newProjectId)
  }
})

// 加载项目需求数据
const loadProjectRequirements = (projectId: string) => {
  // 模拟根据项目加载不同的需求数据
  ElMessage.info(`正在加载项目 ${projectId} 的需求数据...`)
  updateWorkspaceStats()
}
</script>

<style scoped lang="scss">
.requirements-analyst-workspace {
  display: grid;
  grid-template-rows: auto auto 1fr;
  gap: 16px;
  height: 100vh;
  padding: 16px;
  background: #f8fafc;
  overflow: hidden;

  // 项目上下文条
  .project-context-bar {
    display: grid;
    grid-template-columns: auto auto 1fr;
    gap: 20px;
    align-items: center;
    background: white;
    padding: 16px 24px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #2563eb;

    .project-selector {
      display: flex;
      align-items: center;
      gap: 8px;

      .context-label {
        font-size: 14px;
        color: #64748b;
        font-weight: 500;
        white-space: nowrap;
      }

      .project-select {
        width: 200px;
      }
    }

    .project-info {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .workspace-title {
      justify-self: end;
      text-align: right;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #1e293b;
      }

      .subtitle {
        font-size: 13px;
        color: #64748b;
      }
    }
  }

  // 统计仪表板
  .stats-dashboard {
    .stat-card {
      display: flex;
      align-items: center;
      gap: 16px;
      background: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .stat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        border-radius: 12px;
        font-size: 24px;

        &.requirements {
          background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
          color: #2563eb;
        }

        &.completed {
          background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
          color: #059669;
        }

        &.traceability {
          background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
          color: #d97706;
        }

        &.quality {
          background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
          color: #7c3aed;
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
      }
    }
  }

  // 主工作区布局
  .main-workspace-layout {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    gap: 16px;
    min-height: 0;
    overflow: hidden;

    // 左侧边栏
    .left-sidebar {
      display: flex;
      flex-direction: column;
      gap: 16px;
      
      .quick-actions-card {
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .action-grid {
          display: grid;
          gap: 12px;

          .action-btn {
            width: 100%;
            justify-content: flex-start;
            height: 44px;
            font-weight: 500;
          }
        }
      }

      .recommendations-card {
        flex: 1;
        min-height: 0;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .recommendation-list {
          max-height: 400px;
          overflow-y: auto;

          .recommendation-item {
            display: flex;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 8px;
            border: 1px solid #e5e7eb;

            &:hover {
              background: #f8fafc;
              border-color: #2563eb;
              transform: translateX(4px);
            }

            .rec-content {
              flex: 1;

              .rec-title {
                font-weight: 600;
                color: #374151;
                margin-bottom: 4px;
                font-size: 14px;
              }

              .rec-description {
                font-size: 12px;
                color: #6b7280;
                line-height: 1.4;
              }
            }
          }
        }
      }
    }

    // 中央内容区
    .main-content-area {
      display: flex;
      flex-direction: column;
      min-height: 0;
      background: white;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .workspace-tabs {
        flex: 1;
        display: flex;
        flex-direction: column;

        .tab-content-wrapper {
          flex: 1;
          padding: 24px;
          overflow-y: auto;
          background: #ffffff;
        }
      }
    }

    // 右侧边栏
    .right-sidebar {
      .activities-card {
        height: 100%;
        display: flex;
        flex-direction: column;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #374151;
        }

        .activity-timeline {
          flex: 1;
          overflow-y: auto;
          
          .activity-item {
            display: flex;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;

            &:last-child {
              border-bottom: none;
            }

            .activity-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              border-radius: 8px;
              font-size: 16px;
              flex-shrink: 0;

              &.activity-import {
                background: #dbeafe;
                color: #2563eb;
              }

              &.activity-create {
                background: #dcfce7;
                color: #059669;
              }

              &.activity-update {
                background: #fef3c7;
                color: #d97706;
              }

              &.activity-trace {
                background: #f3e8ff;
                color: #7c3aed;
              }
            }

            .activity-content {
              flex: 1;
              min-width: 0;

              .activity-action {
                font-weight: 600;
                color: #374151;
                font-size: 14px;
                margin-bottom: 4px;
              }

              .activity-description {
                font-size: 12px;
                color: #6b7280;
                line-height: 1.4;
                margin-bottom: 4px;
              }

              .activity-time {
                font-size: 11px;
                color: #9ca3af;
              }
            }
          }
        }
      }
    }
  }

  // 响应式适配
  @media (max-width: 1400px) {
    .main-workspace-layout {
      grid-template-columns: 260px 1fr 280px;
    }
  }

  @media (max-width: 1200px) {
    .main-workspace-layout {
      grid-template-columns: 1fr 280px;
      
      .left-sidebar {
        display: none;
      }
    }
  }

  @media (max-width: 768px) {
    .project-context-bar {
      grid-template-columns: 1fr;
      gap: 12px;

      .workspace-title {
        justify-self: start;
        text-align: left;
      }
    }

    .stats-dashboard {
      .el-col {
        margin-bottom: 12px;
      }
    }

    .main-workspace-layout {
      grid-template-columns: 1fr;
      
      .right-sidebar {
        display: none;
      }
    }
  }
}

// 深度样式覆盖
:deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

:deep(.el-tab-pane) {
  height: 100%;
}

:deep(.el-tabs__header) {
  margin: 0;
  background: #f8fafc;
  padding: 0 24px;
}

:deep(.el-tabs__nav) {
  border: none;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
  
  &.is-active {
    color: #2563eb;
  }
}

:deep(.el-card) {
  border: 1px solid #e5e7eb;
  box-shadow: none;

  .el-card__header {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 16px 20px;
  }

  .el-card__body {
    padding: 20px;
  }
}

// ========== 深色主题支持 ==========
:global(.theme-dark) .requirements-analyst-workspace {
  background: linear-gradient(135deg, #111827 0%, #0f172a 50%, #111827 100%);
  color: #f9fafb;

  .project-context-bar {
    background: #1f2937;
    border-bottom: 2px solid #374151;

    .workspace-title {
      h2 {
        color: #3b82f6;
        text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
      }

      .subtitle {
        color: #d1d5db;
      }
    }

    .project-selector .el-select {
      .el-select__wrapper {
        background: #374151;
        border-color: #4b5563;
      }
    }
  }

  .stats-dashboard {
    .stat-card {
      background: #374151;
      border: 2px solid #4b5563;

      &:hover {
        border-color: #3b82f6;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
        background: #475569;
      }

      .stat-icon {
        background: #1e3a8a;
        color: #93c5fd;
      }

      .stat-content {
        .stat-number {
          color: #60a5fa;
          font-weight: 800;
        }

        .stat-label {
          color: #d1d5db;
          font-weight: 500;
        }
      }
    }
  }

  .main-workspace-layout {
    // 左侧边栏
    .left-sidebar {
      background: #1f2937;
      border-right: 2px solid #374151;

      .quick-actions-card {
        background: #1f2937;

        .card-header {
          background: #374151;
          border-bottom: 1px solid #4b5563;
          color: #f9fafb;
          font-weight: 700;
        }

        .actions-grid {
          .action-button {
            background: #374151;
            border: 1px solid #4b5563;
            color: #f9fafb;

            &:hover {
              background: #475569;
              border-color: #6b7280;
              transform: translateY(-2px);
            }

            .action-icon {
              color: #60a5fa;
            }

            .action-text {
              color: #f3f4f6;
              font-weight: 600;
            }
          }
        }
      }

      .intelligent-recommendations-card {
        background: #1f2937;

        .card-header {
          background: #374151;
          border-bottom: 1px solid #4b5563;
          color: #f9fafb;
          font-weight: 700;
        }

        .recommendation-item {
          background: #374151;
          border: 1px solid #4b5563;

          &:hover {
            background: #475569;
          }

          .recommendation-text {
            color: #f3f4f6;
            font-weight: 500;
          }

          .recommendation-type {
            background: #1e3a8a;
            color: #93c5fd;
          }
        }
      }
    }

    // 主内容区
    .main-content-area {
      background: #1f2937;

      .workspace-tabs {
        .tab-content-wrapper {
          background: #1f2937;
          color: #f9fafb;
        }
      }
    }

    // 右侧边栏
    .right-sidebar {
      background: #1f2937;

      .activities-card {
        background: #1f2937;

        .card-header {
          background: #374151;
          border-bottom: 1px solid #4b5563;
          color: #f9fafb;
          font-weight: 700;
        }

        .activity-timeline {
          .activity-item {
            border-bottom: 1px solid #374151;

            .activity-icon {
              &.activity-import {
                background: #1e3a8a;
                color: #93c5fd;
              }

              &.activity-create {
                background: #064e3b;
                color: #6ee7b7;
              }

              &.activity-update {
                background: #78350f;
                color: #fbbf24;
              }

              &.activity-trace {
                background: #581c87;
                color: #c084fc;
              }
            }

            .activity-content {
              .activity-action {
                color: #f3f4f6;
              }

              .activity-description {
                color: #d1d5db;
              }

              .activity-time {
                color: #9ca3af;
              }
            }
          }
        }
      }
    }
  }
}

// 深度样式覆盖 - 深色主题
:global(.theme-dark) :deep(.el-tabs__header) {
  background: #374151;
  border-bottom: 1px solid #4b5563;
}

:global(.theme-dark) :deep(.el-tabs__item) {
  color: #d1d5db;
  font-weight: 500;
  
  &.is-active {
    color: #60a5fa;
    font-weight: 700;
  }

  &:hover {
    color: #f9fafb;
  }
}

:global(.theme-dark) :deep(.el-tabs__active-bar) {
  background-color: #3b82f6;
}

:global(.theme-dark) :deep(.el-card) {
  background: #1f2937;
  border: 1px solid #374151;

  .el-card__header {
    background: #374151;
    border-bottom: 1px solid #4b5563;
    color: #f9fafb;
  }

  .el-card__body {
    background: #1f2937;
    color: #f9fafb;
  }
}

:global(.theme-dark) :deep(.el-select) {
  .el-select__wrapper {
    background: #374151;
    border: 1px solid #4b5563;
    
    &:hover {
      border-color: #6b7280;
    }
    
    &.is-focused {
      border-color: #3b82f6;
    }
  }

  .el-select__selected-item {
    color: #f9fafb;
  }

  .el-select__placeholder {
    color: #9ca3af;
  }
}

// 深度样式覆盖
:deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
}

:deep(.el-tab-pane) {
  height: 100%;
}

:deep(.el-tabs__header) {
  margin: 0;
  background: #f8fafc;
  padding: 0 24px;
}

:deep(.el-tabs__nav) {
  border: none;
}

:deep(.el-tabs__item) {
  color: #6b7280;
  font-weight: 500;
  
  &.is-active {
    color: #2563eb;
  }
}

:deep(.el-card) {
  border: 1px solid #e5e7eb;
  box-shadow: none;

  .el-card__header {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 16px 20px;
  }

  .el-card__body {
    padding: 20px;
  }
}
</style> 