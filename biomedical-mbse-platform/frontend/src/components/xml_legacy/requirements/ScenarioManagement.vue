<template>
  <div class="scenario-management">
    <el-card>
      <template #header>
        <div class="header">
          <h3>场景管理</h3>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新建场景
          </el-button>
        </div>
      </template>
      
      <div class="scenarios-content">
        <!-- 场景列表 -->
        <div class="scenarios-list">
          <div 
            v-for="scenario in scenarios" 
            :key="scenario.id"
            class="scenario-item"
            :class="{ active: selectedScenario?.id === scenario.id }"
            @click="selectScenario(scenario)"
          >
            <div class="scenario-header">
              <h4>{{ scenario.name }}</h4>
              <el-tag :type="getScenarioTypeTag(scenario.type)" size="small">
                {{ getScenarioTypeName(scenario.type) }}
              </el-tag>
            </div>
            <p class="scenario-description">{{ scenario.description }}</p>
            <div class="scenario-meta">
              <span class="scenario-steps">{{ scenario.steps.length }} 步骤</span>
              <span class="scenario-updated">{{ formatDate(scenario.updated_at) }}</span>
            </div>
            <div class="scenario-actions">
              <el-button size="small" @click.stop="editScenario(scenario)">编辑</el-button>
              <el-button size="small" @click.stop="duplicateScenario(scenario)">复制</el-button>
              <el-button size="small" type="danger" @click.stop="deleteScenario(scenario)">删除</el-button>
            </div>
          </div>
        </div>
        
        <!-- 场景详情 -->
        <div class="scenario-detail" v-if="selectedScenario">
          <div class="detail-header">
            <h3>{{ selectedScenario.name }}</h3>
            <div class="detail-actions">
              <el-button @click="runScenario(selectedScenario)">
                <el-icon><VideoPlay /></el-icon>
                运行场景
              </el-button>
            </div>
          </div>
          
          <div class="scenario-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="类型">
                {{ getScenarioTypeName(selectedScenario.type) }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(selectedScenario.status)">
                  {{ getStatusText(selectedScenario.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDate(selectedScenario.created_at) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatDate(selectedScenario.updated_at) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <!-- 场景步骤 -->
          <div class="scenario-steps">
            <h4>场景步骤</h4>
            <el-steps :active="currentStep" direction="vertical">
              <el-step 
                v-for="(step, index) in selectedScenario.steps"
                :key="index"
                :title="step.title"
                :description="step.description"
                :status="getStepStatus(step, index)"
              />
            </el-steps>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-else class="empty-state">
          <el-empty description="请选择一个场景查看详情" />
        </div>
      </div>
    </el-card>
    
    <!-- 创建场景对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建新场景" width="600px">
      <el-form :model="newScenario" label-width="100px">
        <el-form-item label="场景名称" required>
          <el-input v-model="newScenario.name" placeholder="输入场景名称" />
        </el-form-item>
        <el-form-item label="场景类型" required>
          <el-select v-model="newScenario.type" placeholder="选择场景类型">
            <el-option label="用户场景" value="user" />
            <el-option label="系统场景" value="system" />
            <el-option label="异常场景" value="exception" />
            <el-option label="测试场景" value="test" />
          </el-select>
        </el-form-item>
        <el-form-item label="场景描述">
          <el-input 
            v-model="newScenario.description" 
            type="textarea" 
            :rows="3"
            placeholder="描述场景的目的和内容"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createScenario">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

interface ScenarioStep {
  title: string
  description: string
  action: string
  expected?: string
  status?: 'pending' | 'running' | 'completed' | 'failed'
}

interface Scenario {
  id: string
  name: string
  description: string
  type: 'user' | 'system' | 'exception' | 'test'
  status: 'draft' | 'ready' | 'running' | 'completed' | 'failed'
  steps: ScenarioStep[]
  created_at: string
  updated_at: string
}

interface Props {
  projectId?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'scenario-selected': [scenario: Scenario]
  'scenario-created': [scenario: Scenario]
  'scenario-updated': [scenario: Scenario]
  'scenario-deleted': [id: string]
}>()

// 响应式数据
const scenarios = ref<Scenario[]>([
  {
    id: 'SCN-001',
    name: '用户登录场景',
    description: '测试用户通过正确的凭据成功登录系统',
    type: 'user',
    status: 'ready',
    steps: [
      { title: '打开登录页面', description: '用户访问系统登录页面', action: 'navigate' },
      { title: '输入用户名', description: '在用户名字段输入有效用户名', action: 'input' },
      { title: '输入密码', description: '在密码字段输入正确密码', action: 'input' },
      { title: '点击登录按钮', description: '提交登录表单', action: 'click' },
      { title: '验证登录成功', description: '确认用户成功进入系统主页', action: 'verify' }
    ],
    created_at: '2025-01-10 09:00:00',
    updated_at: '2025-01-15 14:30:00'
  },
  {
    id: 'SCN-002',
    name: '数据导出异常场景',
    description: '测试在网络异常情况下的数据导出处理',
    type: 'exception',
    status: 'draft',
    steps: [
      { title: '选择导出数据', description: '用户选择要导出的数据', action: 'select' },
      { title: '模拟网络异常', description: '模拟网络连接中断', action: 'simulate' },
      { title: '执行导出操作', description: '尝试执行数据导出', action: 'export' },
      { title: '验证错误处理', description: '确认系统正确处理网络异常', action: 'verify' }
    ],
    created_at: '2025-01-12 10:30:00',
    updated_at: '2025-01-12 10:30:00'
  }
])

const selectedScenario = ref<Scenario | null>(null)
const showCreateDialog = ref(false)
const currentStep = ref(0)

// 新场景表单
const newScenario = reactive({
  name: '',
  type: 'user' as const,
  description: ''
})

// 计算属性
const hasScenarios = computed(() => scenarios.value.length > 0)

// 方法
const selectScenario = (scenario: Scenario) => {
  selectedScenario.value = scenario
  currentStep.value = 0
  emit('scenario-selected', scenario)
}

const getScenarioTypeTag = (type: string) => {
  switch (type) {
    case 'user': return 'primary'
    case 'system': return 'success'
    case 'exception': return 'warning'
    case 'test': return 'info'
    default: return 'default'
  }
}

const getScenarioTypeName = (type: string) => {
  switch (type) {
    case 'user': return '用户场景'
    case 'system': return '系统场景'
    case 'exception': return '异常场景'
    case 'test': return '测试场景'
    default: return type
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'running': return 'warning'
    case 'ready': return 'primary'
    case 'failed': return 'danger'
    case 'draft': return 'info'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'draft': return '草稿'
    case 'ready': return '就绪'
    case 'running': return '运行中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    default: return status
  }
}

const getStepStatus = (step: ScenarioStep, index: number) => {
  if (step.status === 'completed') return 'finish'
  if (step.status === 'failed') return 'error'
  if (step.status === 'running') return 'process'
  if (index < currentStep.value) return 'finish'
  if (index === currentStep.value) return 'process'
  return 'wait'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const generateScenarioId = () => {
  const nextId = scenarios.value.length + 1
  const idStr = String(nextId)
  const paddedId = idStr.length === 1 ? '00' + idStr : idStr.length === 2 ? '0' + idStr : idStr
  return `SCN-${paddedId}`
}

const createScenario = () => {
  if (!newScenario.name || !newScenario.type) {
    ElMessage.warning('请填写必要信息')
    return
  }
  
  const scenario: Scenario = {
    id: generateScenarioId(),
    name: newScenario.name,
    description: newScenario.description,
    type: newScenario.type,
    status: 'draft',
    steps: [],
    created_at: new Date().toLocaleString('zh-CN'),
    updated_at: new Date().toLocaleString('zh-CN')
  }
  
  scenarios.value.push(scenario)
  emit('scenario-created', scenario)
  
  // 重置表单
  newScenario.name = ''
  newScenario.type = 'user'
  newScenario.description = ''
  showCreateDialog.value = false
  
  ElMessage.success('场景创建成功')
}

const editScenario = (scenario: Scenario) => {
  ElMessage.info(`编辑场景: ${scenario.name}`)
}

const duplicateScenario = (scenario: Scenario) => {
  const duplicated: Scenario = {
    ...scenario,
    id: generateScenarioId(),
    name: `${scenario.name} (副本)`,
    status: 'draft',
    created_at: new Date().toLocaleString('zh-CN'),
    updated_at: new Date().toLocaleString('zh-CN')
  }
  
  scenarios.value.push(duplicated)
  ElMessage.success('场景复制成功')
}

const deleteScenario = async (scenario: Scenario) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除场景"${scenario.name}"吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    const index = scenarios.value.findIndex(s => s.id === scenario.id)
    if (index > -1) {
      scenarios.value.splice(index, 1)
      
      if (selectedScenario.value?.id === scenario.id) {
        selectedScenario.value = null
      }
      
      emit('scenario-deleted', scenario.id)
      ElMessage.success('场景已删除')
    }
  } catch {
    // 用户取消删除
  }
}

const runScenario = async (scenario: Scenario) => {
  ElMessage.info(`开始运行场景: ${scenario.name}`)
  
  scenario.status = 'running'
  currentStep.value = 0
  
  // 模拟执行步骤
  for (let i = 0; i < scenario.steps.length; i++) {
    currentStep.value = i
    scenario.steps[i].status = 'running'
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    scenario.steps[i].status = 'completed'
  }
  
  currentStep.value = scenario.steps.length
  scenario.status = 'completed'
  scenario.updated_at = new Date().toLocaleString('zh-CN')
  
  ElMessage.success('场景执行完成')
}
</script>

<style scoped>
.scenario-management {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h3 {
  margin: 0;
}

.scenarios-content {
  display: flex;
  gap: 20px;
  min-height: 500px;
}

.scenarios-list {
  flex: 1;
  max-width: 400px;
  max-height: 600px;
  overflow-y: auto;
}

.scenario-item {
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.scenario-item:hover,
.scenario-item.active {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.scenario-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.scenario-header h4 {
  margin: 0;
  font-size: 14px;
  flex: 1;
}

.scenario-description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.scenario-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: #999;
}

.scenario-actions {
  display: flex;
  gap: 8px;
}

.scenario-detail {
  flex: 2;
  border-left: 1px solid #e8eaec;
  padding-left: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.detail-header h3 {
  margin: 0;
}

.scenario-info {
  margin-bottom: 24px;
}

.scenario-steps h4 {
  margin: 0 0 16px 0;
}

.empty-state {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #e8eaec;
  padding-left: 20px;
}
</style> 