<template>
  <div class="traceability-analysis">
    <el-card>
      <template #header>
        <div class="header">
          <h3>可追溯性分析</h3>
          <div class="header-actions">
            <el-button @click="generateMatrix">
              <el-icon><Grid /></el-icon>
              生成追溯矩阵
            </el-button>
            <el-button type="primary" @click="analyzeTraceability">
              <el-icon><TrendCharts /></el-icon>
              分析可追溯性
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="analysis-content">
        <!-- 分析配置 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="config-card">
              <template #header>
                <span>分析配置</span>
              </template>
              
              <el-form :model="analysisConfig" label-width="100px" size="small">
                <el-form-item label="分析深度">
                  <el-select v-model="analysisConfig.depth">
                    <el-option label="基础分析" :value="1" />
                    <el-option label="中级分析" :value="2" />
                    <el-option label="深度分析" :value="3" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="追溯类型">
                  <el-checkbox-group v-model="analysisConfig.types">
                    <el-checkbox label="forward">前向追溯</el-checkbox>
                    <el-checkbox label="backward">后向追溯</el-checkbox>
                    <el-checkbox label="bidirectional">双向追溯</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                
                <el-form-item label="包含关系">
                  <el-checkbox-group v-model="analysisConfig.relationships">
                    <el-checkbox label="implements">实现</el-checkbox>
                    <el-checkbox label="derives">派生</el-checkbox>
                    <el-checkbox label="validates">验证</el-checkbox>
                    <el-checkbox label="depends">依赖</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                
                <el-form-item label="输出格式">
                  <el-radio-group v-model="analysisConfig.outputFormat">
                    <el-radio label="matrix">矩阵</el-radio>
                    <el-radio label="graph">图形</el-radio>
                    <el-radio label="report">报告</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          
          <el-col :span="16">
            <!-- 分析结果 -->
            <el-card class="results-card">
              <template #header>
                <div class="results-header">
                  <span>分析结果</span>
                  <el-tag v-if="analysisResults" type="success">
                    {{ analysisResults.totalRelations }} 个关系
                  </el-tag>
                </div>
              </template>
              
              <div v-if="isAnalyzing" class="analyzing">
                <el-progress :percentage="analysisProgress" />
                <p>正在分析可追溯性关系...</p>
              </div>
              
              <div v-else-if="analysisResults" class="results">
                <!-- 统计信息 -->
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-value">{{ analysisResults.coverage }}%</div>
                    <div class="stat-label">覆盖率</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ analysisResults.orphanCount }}</div>
                    <div class="stat-label">孤立元素</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ analysisResults.duplicateCount }}</div>
                    <div class="stat-label">重复关系</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ analysisResults.missingCount }}</div>
                    <div class="stat-label">缺失关系</div>
                  </div>
                </div>
                
                <!-- 追溯矩阵 -->
                <div v-if="analysisConfig.outputFormat === 'matrix'" class="matrix-view">
                  <h4>追溯矩阵</h4>
                  <el-table 
                    :data="traceabilityMatrix" 
                    border 
                    size="small"
                    max-height="400"
                  >
                    <el-table-column prop="source" label="源元素" width="200" fixed />
                    <el-table-column 
                      v-for="target in matrixTargets" 
                      :key="target"
                      :prop="target"
                      :label="target"
                      width="120"
                      align="center"
                    >
                      <template #default="{ row }">
                        <el-icon v-if="row[target]" color="#67c23a">
                          <Check />
                        </el-icon>
                        <span v-else>-</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                
                <!-- 图形视图 -->
                <div v-else-if="analysisConfig.outputFormat === 'graph'" class="graph-view">
                  <h4>关系图</h4>
                  <div class="graph-container">
                    <div class="graph-placeholder">
                      <el-icon class="graph-icon"><Share /></el-icon>
                      <p>可追溯性关系图</p>
                      <p class="graph-desc">显示{{ analysisResults.totalRelations }}个追溯关系</p>
                    </div>
                  </div>
                </div>
                
                <!-- 报告视图 -->
                <div v-else-if="analysisConfig.outputFormat === 'report'" class="report-view">
                  <h4>分析报告</h4>
                  <div class="report-content">
                    <div class="report-section">
                      <h5>总体评估</h5>
                      <p>系统整体可追溯性覆盖率为 {{ analysisResults.coverage }}%，处于良好水平。</p>
                    </div>
                    
                    <div class="report-section">
                      <h5>发现的问题</h5>
                      <ul>
                        <li v-if="analysisResults.orphanCount > 0">
                          发现 {{ analysisResults.orphanCount }} 个孤立元素，建议建立追溯关系
                        </li>
                        <li v-if="analysisResults.duplicateCount > 0">
                          发现 {{ analysisResults.duplicateCount }} 个重复关系，建议清理
                        </li>
                        <li v-if="analysisResults.missingCount > 0">
                          发现 {{ analysisResults.missingCount }} 个缺失关系，建议补充
                        </li>
                      </ul>
                    </div>
                    
                    <div class="report-section">
                      <h5>改进建议</h5>
                      <ul>
                        <li>加强需求与设计之间的追溯关系</li>
                        <li>完善测试用例与需求的映射</li>
                        <li>建立自动化追溯关系检查机制</li>
                      </ul>
                    </div>
                  </div>
                </div>
                
                <!-- 问题列表 -->
                <div class="issues-section">
                  <h4>发现的问题</h4>
                  <el-table :data="analysisResults.issues" size="small">
                    <el-table-column prop="type" label="类型" width="100">
                      <template #default="{ row }">
                        <el-tag :type="getIssueType(row.type)" size="small">
                          {{ getIssueTypeName(row.type) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="element" label="元素" width="150" />
                    <el-table-column prop="description" label="描述" />
                    <el-table-column prop="severity" label="严重程度" width="100">
                      <template #default="{ row }">
                        <el-tag :type="getSeverityType(row.severity)" size="small">
                          {{ row.severity }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                      <template #default="{ row }">
                        <el-button size="small" @click="fixIssue(row)">修复</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              
              <div v-else class="no-results">
                <el-empty description="暂无分析结果" />
                <p>请点击"分析可追溯性"开始分析</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

interface TraceabilityIssue {
  id: string
  type: 'orphan' | 'duplicate' | 'missing' | 'weak'
  element: string
  description: string
  severity: 'high' | 'medium' | 'low'
}

interface AnalysisResults {
  totalRelations: number
  coverage: number
  orphanCount: number
  duplicateCount: number
  missingCount: number
  issues: TraceabilityIssue[]
}

interface MatrixRow {
  source: string
  [key: string]: boolean | string
}

interface Props {
  requirements?: any[]
  designElements?: any[]
  testCases?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  requirements: () => [],
  designElements: () => [],
  testCases: () => []
})

const emit = defineEmits<{
  'analysis-complete': [results: AnalysisResults]
  'matrix-generated': [matrix: MatrixRow[]]
}>()

// 响应式数据
const isAnalyzing = ref(false)
const analysisProgress = ref(0)
const analysisResults = ref<AnalysisResults | null>(null)

const analysisConfig = reactive({
  depth: 2,
  types: ['forward', 'backward'] as string[],
  relationships: ['implements', 'derives', 'validates'] as string[],
  outputFormat: 'matrix' as 'matrix' | 'graph' | 'report'
})

// 模拟追溯矩阵数据
const traceabilityMatrix = ref<MatrixRow[]>([
  { source: 'REQ-001', 'DESIGN-001': true, 'DESIGN-002': false, 'TEST-001': true, 'TEST-002': false },
  { source: 'REQ-002', 'DESIGN-001': false, 'DESIGN-002': true, 'TEST-001': false, 'TEST-002': true },
  { source: 'REQ-003', 'DESIGN-001': true, 'DESIGN-002': true, 'TEST-001': true, 'TEST-002': false }
])

// 计算属性
const matrixTargets = computed(() => ['DESIGN-001', 'DESIGN-002', 'TEST-001', 'TEST-002'])

const hasData = computed(() => 
  props.requirements.length > 0 || 
  props.designElements.length > 0 || 
  props.testCases.length > 0
)

// 方法
const analyzeTraceability = async () => {
  if (!hasData.value) {
    ElMessage.warning('没有数据进行可追溯性分析')
    return
  }
  
  isAnalyzing.value = true
  analysisProgress.value = 0
  
  try {
    // 模拟分析过程
    const steps = [
      '收集元素数据',
      '识别关系类型', 
      '构建追溯链',
      '计算覆盖率',
      '检测问题',
      '生成报告'
    ]
    
    for (let i = 0; i < steps.length; i++) {
      analysisProgress.value = Math.round((i + 1) / steps.length * 100)
      await new Promise(resolve => setTimeout(resolve, 800))
    }
    
    // 生成分析结果
    analysisResults.value = {
      totalRelations: 47,
      coverage: 78,
      orphanCount: 3,
      duplicateCount: 2,
      missingCount: 5,
      issues: [
        {
          id: 'ISSUE-001',
          type: 'orphan',
          element: 'REQ-015',
          description: '该需求没有对应的设计元素',
          severity: 'high'
        },
        {
          id: 'ISSUE-002',
          type: 'missing',
          element: 'DESIGN-005',
          description: '缺少对应的测试用例',
          severity: 'medium'
        },
        {
          id: 'ISSUE-003',
          type: 'duplicate',
          element: 'TEST-003',
          description: '与TEST-007存在重复关系',
          severity: 'low'
        }
      ]
    }
    
    emit('analysis-complete', analysisResults.value)
    ElMessage.success('可追溯性分析完成')
    
  } catch (error) {
    ElMessage.error('分析失败: ' + error)
  } finally {
    isAnalyzing.value = false
  }
}

const generateMatrix = () => {
  // 生成追溯矩阵逻辑
  emit('matrix-generated', traceabilityMatrix.value)
  analysisConfig.outputFormat = 'matrix'
  ElMessage.success('追溯矩阵已生成')
}

const getIssueType = (type: string) => {
  switch (type) {
    case 'orphan': return 'warning'
    case 'missing': return 'danger'
    case 'duplicate': return 'info'
    case 'weak': return 'warning'
    default: return 'default'
  }
}

const getIssueTypeName = (type: string) => {
  switch (type) {
    case 'orphan': return '孤立'
    case 'missing': return '缺失'
    case 'duplicate': return '重复'
    case 'weak': return '薄弱'
    default: return type
  }
}

const getSeverityType = (severity: string) => {
  switch (severity) {
    case 'high': return 'danger'
    case 'medium': return 'warning'
    case 'low': return 'info'
    default: return 'default'
  }
}

const fixIssue = (issue: TraceabilityIssue) => {
  ElMessage.info(`开始修复问题: ${issue.description}`)
  
  // 从问题列表中移除
  if (analysisResults.value) {
    const index = analysisResults.value.issues.findIndex(i => i.id === issue.id)
    if (index > -1) {
      analysisResults.value.issues.splice(index, 1)
      ElMessage.success('问题已修复')
    }
  }
}
</script>

<style scoped>
.traceability-analysis {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.analysis-content {
  margin-top: 16px;
}

.config-card,
.results-card {
  height: fit-content;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analyzing {
  text-align: center;
  padding: 40px 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.matrix-view,
.graph-view,
.report-view,
.issues-section {
  margin-bottom: 24px;
}

.matrix-view h4,
.graph-view h4,
.report-view h4,
.issues-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.graph-container {
  height: 300px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.graph-placeholder {
  text-align: center;
}

.graph-icon {
  font-size: 48px;
  color: #adb5bd;
  margin-bottom: 16px;
}

.graph-placeholder p {
  margin: 0;
  color: #666;
}

.graph-desc {
  font-size: 12px !important;
  color: #999 !important;
  margin-top: 8px !important;
}

.report-content {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.report-section {
  margin-bottom: 20px;
}

.report-section:last-child {
  margin-bottom: 0;
}

.report-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #2c3e50;
}

.report-section p,
.report-section ul {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.report-section ul {
  padding-left: 20px;
}

.report-section li {
  margin: 6px 0;
}

.no-results {
  text-align: center;
  padding: 40px 20px;
}

.no-results p {
  margin-top: 16px;
  color: #999;
}
</style> 