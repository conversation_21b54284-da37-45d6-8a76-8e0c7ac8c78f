<template>
  <div class="component-editor">
    <el-card>
      <template #header>
        <div class="editor-header">
          <span>组件编辑器</span>
          <div class="header-actions">
            <el-button size="small" @click="saveComponent">保存</el-button>
            <el-button size="small" @click="resetComponent">重置</el-button>
          </div>
        </div>
      </template>
      
      <div class="editor-content">
        <el-form :model="componentData" label-width="100px">
          <el-form-item label="组件名称">
            <el-input v-model="componentData.name" placeholder="输入组件名称" />
          </el-form-item>
          
          <el-form-item label="组件类型">
            <el-select v-model="componentData.type" placeholder="选择类型">
              <el-option label="UI组件" value="ui" />
              <el-option label="业务组件" value="business" />
              <el-option label="工具组件" value="utility" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="描述">
            <el-input 
              v-model="componentData.description" 
              type="textarea" 
              :rows="3"
              placeholder="描述组件功能"
            />
          </el-form-item>
        </el-form>
        
        <div class="preview-section">
          <h4>组件预览</h4>
          <div class="component-preview">
            <div class="preview-placeholder">
              <h3>{{ componentData.name || '未命名组件' }}</h3>
              <p>{{ componentData.description || '暂无描述' }}</p>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

interface ComponentData {
  name: string
  type: string
  description: string
}

interface Props {
  component?: ComponentData
}

const props = withDefaults(defineProps<Props>(), {
  component: () => ({ name: '', type: '', description: '' })
})

const emit = defineEmits<{
  'component-saved': [component: ComponentData]
  'component-changed': [component: ComponentData]
}>()

const componentData = reactive<ComponentData>({
  name: props.component.name,
  type: props.component.type,
  description: props.component.description
})

const saveComponent = () => {
  if (!componentData.name) {
    ElMessage.warning('请输入组件名称')
    return
  }
  
  emit('component-saved', { ...componentData })
  ElMessage.success('组件已保存')
}

const resetComponent = () => {
  Object.assign(componentData, props.component)
  ElMessage.info('组件已重置')
}
</script>

<style scoped>
.component-editor {
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.editor-content {
  padding: 20px 0;
}

.preview-section {
  margin-top: 24px;
}

.preview-section h4 {
  margin: 0 0 16px 0;
}

.component-preview {
  min-height: 200px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.preview-placeholder {
  text-align: center;
  padding: 20px;
}

.preview-placeholder h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.preview-placeholder p {
  margin: 0;
  color: #666;
  font-size: 14px;
}
</style> 