<template>
  <div class="customization-panel">
    <el-drawer v-model="visible" title="自定义面板" size="400px">
      <div class="panel-content">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="组件" name="components">
            <div class="components-list">
              <h4>可用组件</h4>
              <div class="component-grid">
                <div 
                  v-for="component in availableComponents"
                  :key="component.type"
                  class="component-item"
                  @click="addComponent(component)"
                >
                  <div class="component-icon">
                    <el-icon><Grid /></el-icon>
                  </div>
                  <div class="component-info">
                    <div class="component-name">{{ component.name }}</div>
                    <div class="component-desc">{{ component.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="布局" name="layout">
            <div class="layout-options">
              <h4>布局类型</h4>
              <el-radio-group v-model="layoutType">
                <el-radio label="grid">网格布局</el-radio>
                <el-radio label="flex">弹性布局</el-radio>
                <el-radio label="canvas">画布布局</el-radio>
              </el-radio-group>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="样式" name="styles">
            <div class="style-options">
              <h4>主题设置</h4>
              <el-form label-width="80px">
                <el-form-item label="主色调">
                  <el-color-picker v-model="primaryColor" />
                </el-form-item>
                <el-form-item label="背景色">
                  <el-color-picker v-model="backgroundColor" />
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Grid } from '@element-plus/icons-vue'

interface Props {
  visible?: boolean
  availableComponents?: Array<{
    type: string
    name: string
    description: string
    category: string
  }>
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  availableComponents: () => []
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'add-component': [component: any]
  'update-layout': [type: string]
  'update-styles': [styles: any]
}>()

const visible = ref(props.visible)
const activeTab = ref('components')
const layoutType = ref('grid')
const primaryColor = ref('#409EFF')
const backgroundColor = ref('#FFFFFF')

const addComponent = (component: any) => {
  emit('add-component', component)
}
</script>

<style scoped>
.customization-panel {
  width: 100%;
  height: 100%;
}

.components-list h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.component-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.component-item:hover {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.component-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-color-primary-light-8);
  border-radius: 4px;
  color: var(--el-color-primary);
}

.component-info {
  flex: 1;
}

.component-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.component-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

.layout-options h4,
.style-options h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}
</style> 