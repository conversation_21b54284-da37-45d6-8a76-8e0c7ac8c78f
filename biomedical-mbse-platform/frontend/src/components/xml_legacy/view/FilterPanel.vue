<template>
  <div class="filter-panel">
    <el-card>
      <template #header>
        <div class="panel-header">
          <span>筛选面板</span>
          <el-button size="small" @click="resetFilters">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </template>
      
      <el-form label-width="80px" size="small">
        <el-form-item label="搜索">
          <el-input 
            v-model="filters.searchText" 
            placeholder="输入搜索关键词"
            clearable
            @input="handleFilterChange"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select 
            v-model="filters.type" 
            placeholder="选择类型"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部" value="" />
            <el-option label="元素" value="element" />
            <el-option label="属性" value="attribute" />
            <el-option label="关系" value="relationship" />
            <el-option label="模型" value="model" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-checkbox-group v-model="filters.status" @change="handleFilterChange">
            <el-checkbox label="active">激活</el-checkbox>
            <el-checkbox label="inactive">非激活</el-checkbox>
            <el-checkbox label="draft">草稿</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select 
            v-model="filters.tags" 
            placeholder="选择标签"
            multiple
            collapse-tags
            @change="handleFilterChange"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag.value"
              :label="tag.label"
              :value="tag.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleFilterChange"
          />
        </el-form-item>
        
        <el-form-item label="属性">
          <el-row :gutter="8">
            <el-col :span="12">
              <el-input 
                v-model="customFilter.key" 
                placeholder="属性名"
                size="small"
              />
            </el-col>
            <el-col :span="12">
              <el-input 
                v-model="customFilter.value" 
                placeholder="属性值"
                size="small"
                @keyup.enter="addCustomFilter"
              />
            </el-col>
          </el-row>
          <el-button 
            size="small" 
            type="primary" 
            @click="addCustomFilter"
            style="margin-top: 8px;"
          >
            添加筛选条件
          </el-button>
        </el-form-item>
        
        <!-- 自定义筛选条件列表 -->
        <div v-if="filters.customFilters.length > 0" class="custom-filters">
          <h5>自定义筛选条件</h5>
          <el-tag
            v-for="(filter, index) in filters.customFilters"
            :key="index"
            closable
            @close="removeCustomFilter(index)"
            class="filter-tag"
          >
            {{ filter.key }}: {{ filter.value }}
          </el-tag>
        </div>
      </el-form>
      
      <!-- 筛选统计 -->
      <div class="filter-stats">
        <el-divider />
        <div class="stats-info">
          <el-icon><Filter /></el-icon>
          <span>已应用 {{ activeFiltersCount }} 个筛选条件</span>
        </div>
        <div class="results-count">
          <span>筛选结果: {{ filteredCount }} 项</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'

interface CustomFilter {
  key: string
  value: string
}

interface FilterData {
  searchText: string
  type: string
  status: string[]
  tags: string[]
  dateRange: [string, string] | null
  customFilters: CustomFilter[]
}

interface Tag {
  label: string
  value: string
}

interface Props {
  filteredCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  filteredCount: 0
})

const emit = defineEmits<{
  'filter-change': [filters: FilterData]
  'filter-reset': []
}>()

// 响应式数据
const filters = reactive<FilterData>({
  searchText: '',
  type: '',
  status: [],
  tags: [],
  dateRange: null,
  customFilters: []
})

const customFilter = reactive({
  key: '',
  value: ''
})

const availableTags = ref<Tag[]>([
  { label: '重要', value: 'important' },
  { label: '紧急', value: 'urgent' },
  { label: '完成', value: 'completed' },
  { label: '待办', value: 'pending' },
  { label: '核心', value: 'core' },
  { label: '扩展', value: 'extension' }
])

// 计算属性
const activeFiltersCount = computed(() => {
  let count = 0
  if (filters.searchText) count++
  if (filters.type) count++
  if (filters.status.length > 0) count++
  if (filters.tags.length > 0) count++
  if (filters.dateRange) count++
  count += filters.customFilters.length
  return count
})

// 方法
const handleFilterChange = () => {
  emit('filter-change', { ...filters })
}

const resetFilters = () => {
  filters.searchText = ''
  filters.type = ''
  filters.status = []
  filters.tags = []
  filters.dateRange = null
  filters.customFilters = []
  customFilter.key = ''
  customFilter.value = ''
  
  emit('filter-reset')
}

const addCustomFilter = () => {
  if (customFilter.key && customFilter.value) {
    filters.customFilters.push({
      key: customFilter.key,
      value: customFilter.value
    })
    
    customFilter.key = ''
    customFilter.value = ''
    handleFilterChange()
  }
}

const removeCustomFilter = (index: number) => {
  filters.customFilters.splice(index, 1)
  handleFilterChange()
}

// 监听筛选条件变化
watch(filters, handleFilterChange, { deep: true })
</script>

<style scoped>
.filter-panel {
  height: 100%;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.custom-filters {
  margin-top: 16px;
}

.custom-filters h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #666;
}

.filter-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.filter-stats {
  margin-top: 16px;
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.results-count {
  color: #409eff;
  font-size: 13px;
  font-weight: 500;
}
</style> 