<template>
  <div class="view-toolbar">
    <div class="toolbar-left">
      <el-button-group>
        <el-button :type="customizeMode ? 'primary' : ''" @click="toggleCustomize">
          <el-icon><Edit /></el-icon>
          {{ customizeMode ? '退出编辑' : '自定义' }}
        </el-button>
        <el-button @click="resetView">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button @click="saveView" :disabled="!hasChanges">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </el-button-group>
      
      <el-divider direction="vertical" />
      
      <el-button-group>
        <el-button @click="zoomIn" :disabled="zoomLevel >= 200">
          <el-icon><ZoomIn /></el-icon>
        </el-button>
        <el-button @click="zoomOut" :disabled="zoomLevel <= 50">
          <el-icon><ZoomOut /></el-icon>
        </el-button>
        <el-button @click="resetZoom">
          <span class="zoom-level">{{ zoomLevel }}%</span>
        </el-button>
      </el-button-group>
    </div>
    
    <div class="toolbar-center">
      <el-text class="view-title">{{ viewTitle }}</el-text>
      <el-tag v-if="hasChanges" type="warning" size="small" class="changes-indicator">
        未保存
      </el-tag>
    </div>
    
    <div class="toolbar-right">
      <el-tooltip content="视图模式">
        <el-select v-model="viewMode" size="small" style="width: 120px;" @change="handleViewModeChange">
          <el-option label="列表视图" value="list" />
          <el-option label="卡片视图" value="card" />
          <el-option label="树形视图" value="tree" />
          <el-option label="图表视图" value="chart" />
        </el-select>
      </el-tooltip>
      
      <el-divider direction="vertical" />
      
      <el-button-group>
        <el-tooltip content="筛选器">
          <el-button 
            :type="showFilters ? 'primary' : ''" 
            @click="toggleFilters"
          >
            <el-icon><Filter /></el-icon>
            <el-badge :value="filterCount" :hidden="filterCount === 0" class="filter-badge">
              筛选
            </el-badge>
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="搜索">
          <el-button @click="toggleSearch">
            <el-icon><Search /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="导出">
          <el-button @click="exportView">
            <el-icon><Download /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="全屏">
          <el-button @click="toggleFullscreen">
            <el-icon><FullScreen /></el-icon>
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="设置">
          <el-button @click="showSettings">
            <el-icon><Setting /></el-icon>
          </el-button>
        </el-tooltip>
      </el-button-group>
    </div>
  </div>
  
  <!-- 快速搜索栏 -->
  <transition name="slide-down">
    <div v-if="showSearchBar" class="search-bar">
      <el-input
        v-model="searchQuery"
        placeholder="快速搜索..."
        clearable
        @input="handleSearch"
        @keyup.enter="handleSearchEnter"
        style="max-width: 400px;"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #suffix>
          <el-button text @click="closeSearch">
            <el-icon><Close /></el-icon>
          </el-button>
        </template>
      </el-input>
      
      <div v-if="searchResults.length > 0" class="search-results">
        <span class="results-count">找到 {{ searchResults.length }} 个结果</span>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Refresh, Check, Filter, FullScreen, ZoomIn, ZoomOut, Search, Download, Setting, Close } from '@element-plus/icons-vue'

interface Props {
  viewTitle?: string
  customizeMode?: boolean
  showFilters?: boolean
  filterCount?: number
  hasChanges?: boolean
  zoomLevel?: number
  viewMode?: string
}

const props = withDefaults(defineProps<Props>(), {
  viewTitle: '自定义视图',
  customizeMode: false,
  showFilters: false,
  filterCount: 0,
  hasChanges: false,
  zoomLevel: 100,
  viewMode: 'list'
})

const emit = defineEmits<{
  'update:customizeMode': [value: boolean]
  'update:showFilters': [value: boolean]
  'update:zoomLevel': [value: number]
  'update:viewMode': [value: string]
  'reset-view': []
  'save-view': []
  'toggle-fullscreen': []
  'export-view': []
  'search': [query: string]
  'settings': []
}>()

// 响应式数据
const customizeMode = ref(props.customizeMode)
const showFilters = ref(props.showFilters)
const zoomLevel = ref(props.zoomLevel)
const viewMode = ref(props.viewMode)
const showSearchBar = ref(false)
const searchQuery = ref('')
const searchResults = ref([])

// 计算属性
const hasChanges = computed(() => props.hasChanges)
const filterCount = computed(() => props.filterCount)

// 方法
const toggleCustomize = () => {
  customizeMode.value = !customizeMode.value
  emit('update:customizeMode', customizeMode.value)
}

const resetView = () => {
  emit('reset-view')
  ElMessage.info('视图已重置')
}

const saveView = () => {
  if (hasChanges.value) {
    emit('save-view')
    ElMessage.success('视图已保存')
  }
}

const zoomIn = () => {
  if (zoomLevel.value < 200) {
    zoomLevel.value += 10
    emit('update:zoomLevel', zoomLevel.value)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 50) {
    zoomLevel.value -= 10
    emit('update:zoomLevel', zoomLevel.value)
  }
}

const resetZoom = () => {
  zoomLevel.value = 100
  emit('update:zoomLevel', zoomLevel.value)
}

const toggleFilters = () => {
  showFilters.value = !showFilters.value
  emit('update:showFilters', showFilters.value)
}

const toggleSearch = () => {
  showSearchBar.value = !showSearchBar.value
  if (showSearchBar.value) {
    // 聚焦搜索框
    setTimeout(() => {
      const input = document.querySelector('.search-bar input')
      if (input) input.focus()
    }, 100)
  }
}

const closeSearch = () => {
  showSearchBar.value = false
  searchQuery.value = ''
  searchResults.value = []
}

const handleSearch = (query: string) => {
  emit('search', query)
  // 模拟搜索结果
  if (query) {
    searchResults.value = Array.from({ length: Math.floor(Math.random() * 10) }, (_, i) => ({
      id: i,
      title: `搜索结果 ${i + 1}`
    }))
  } else {
    searchResults.value = []
  }
}

const handleSearchEnter = () => {
  if (searchQuery.value) {
    ElMessage.info(`搜索: ${searchQuery.value}`)
  }
}

const handleViewModeChange = (mode: string) => {
  emit('update:viewMode', mode)
}

const toggleFullscreen = () => {
  emit('toggle-fullscreen')
}

const exportView = () => {
  emit('export-view')
  ElMessage.info('正在导出视图...')
}

const showSettings = () => {
  emit('settings')
}
</script>

<style scoped>
.view-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  position: relative;
}

.toolbar-left,
.toolbar-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-center {
  flex: 1;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.view-title {
  font-weight: 500;
  font-size: 16px;
}

.changes-indicator {
  margin-left: 8px;
}

.zoom-level {
  font-size: 12px;
  min-width: 40px;
  display: inline-block;
}

.filter-badge {
  position: relative;
}

.search-bar {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 10;
}

.search-results {
  flex: 1;
  display: flex;
  align-items: center;
}

.results-count {
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

/* 动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}
</style> 