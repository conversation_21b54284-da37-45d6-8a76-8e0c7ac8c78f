/**
 * 建模引擎组合式API
 * ==================
 * 
 * 提供UML/SysML建模功能的Vue 3组合式API
 */

import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  UMLDiagram,
  UMLElement,
  SysMLBlock,
  BiomedicalProject,
  ProjectCreateRequest,
  DiagramCreateRequest,
  BlockCreateRequest,
  WorkflowExecuteRequest,
  KnowledgeIntegrationRequest,
  ProjectStatusResponse,
  DiagramCreateResponse,
  BlockCreateResponse,
  WorkflowExecuteResponse,
  ConstraintSolveResponse,
  ValidationResult,
  ToolRecommendation
} from '@/types/modeling'

// API基础URL
const API_BASE_URL = '/api/v1/modeling'

// 响应式状态
interface ModelingEngineState {
  isLoading: boolean
  error: string | null
  currentProject: BiomedicalProject | null
  activeDiagram: UMLDiagram | null
  selectedElements: string[]
  projects: BiomedicalProject[]
  diagrams: UMLDiagram[]
  recommendations: ToolRecommendation[]
}

const state = reactive<ModelingEngineState>({
  isLoading: false,
  error: null,
  currentProject: null,
  activeDiagram: null,
  selectedElements: [],
  projects: [],
  diagrams: [],
  recommendations: []
})

/**
 * 建模引擎组合式API
 */
export function useModelingEngine() {
  
  // API辅助函数
  const apiRequest = async <T>(
    url: string, 
    options: RequestInit = {}
  ): Promise<T> => {
    state.isLoading = true
    state.error = null
    
    try {
      const response = await fetch(`${API_BASE_URL}${url}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP ${response.status}`)
      }
      
      const data = await response.json()
      return data
    } catch (error) {
      state.error = error instanceof Error ? error.message : 'Unknown error'
      ElMessage.error(state.error)
      throw error
    } finally {
      state.isLoading = false
    }
  }

  // 项目管理
  const createProject = async (request: ProjectCreateRequest): Promise<string> => {
    const response = await apiRequest<{ project_id: string }>('/projects', {
      method: 'POST',
      body: JSON.stringify(request)
    })
    
    ElMessage.success(`项目 ${request.name} 创建成功`)
    return response.project_id
  }

  const getProjectStatus = async (projectId: string): Promise<ProjectStatusResponse> => {
    const status = await apiRequest<ProjectStatusResponse>(`/projects/${projectId}/status`)
    return status
  }

  const setCurrentProject = async (projectId: string) => {
    try {
      const projectStatus = await getProjectStatus(projectId)
      state.currentProject = {
        id: projectStatus.project_id,
        name: projectStatus.config.name,
        description: projectStatus.config.description || '',
        project_type: projectStatus.config.project_type,
        domain: projectStatus.config.domain,
        status: projectStatus.status,
        diagrams_count: projectStatus.diagrams_count,
        models_count: projectStatus.models_count,
        workflows_count: projectStatus.workflows_count,
        knowledge_links_count: projectStatus.knowledge_links_count,
        created_at: new Date().toISOString(),
        config: projectStatus.config
      }
      
      // 加载项目图形
      await loadProjectDiagrams(projectId)
    } catch (error) {
      console.error('设置当前项目失败:', error)
    }
  }

  const loadProjectDiagrams = async (projectId: string) => {
    try {
      const diagrams = await apiRequest<UMLDiagram[]>(`/projects/${projectId}/diagrams`)
      state.diagrams = diagrams
    } catch (error) {
      console.error('加载项目图形失败:', error)
    }
  }

  // UML图形管理
  const createUMLDiagram = async (
    projectId: string, 
    request: DiagramCreateRequest
  ): Promise<DiagramCreateResponse> => {
    const response = await apiRequest<DiagramCreateResponse>(
      `/projects/${projectId}/diagrams/uml`,
      {
        method: 'POST',
        body: JSON.stringify(request)
      }
    )
    
    // 更新本地状态
    state.diagrams.push(response.diagram)
    state.recommendations = response.recommendations
    
    ElMessage.success(`UML图形 ${request.name} 创建成功`)
    return response
  }

  const createSysMLDiagram = async (
    projectId: string, 
    request: DiagramCreateRequest
  ): Promise<DiagramCreateResponse> => {
    const response = await apiRequest<DiagramCreateResponse>(
      `/projects/${projectId}/diagrams/sysml`,
      {
        method: 'POST',
        body: JSON.stringify(request)
      }
    )
    
    // 更新本地状态
    state.diagrams.push(response.diagram)
    state.recommendations = response.recommendations
    
    ElMessage.success(`SysML图形 ${request.name} 创建成功`)
    return response
  }

  const getDiagram = async (projectId: string, diagramId: string): Promise<UMLDiagram> => {
    const response = await apiRequest<{ diagram: UMLDiagram }>(
      `/projects/${projectId}/diagrams/${diagramId}`
    )
    return response.diagram
  }

  const setActiveDiagram = async (projectId: string, diagramId: string) => {
    try {
      const diagram = await getDiagram(projectId, diagramId)
      state.activeDiagram = diagram
    } catch (error) {
      console.error('设置活动图形失败:', error)
    }
  }

  // SysML块管理
  const createBiomedicalBlock = async (
    projectId: string, 
    diagramId: string, 
    request: BlockCreateRequest
  ): Promise<BlockCreateResponse> => {
    const response = await apiRequest<BlockCreateResponse>(
      `/projects/${projectId}/diagrams/${diagramId}/blocks`,
      {
        method: 'POST',
        body: JSON.stringify(request)
      }
    )
    
    // 更新活动图形
    if (state.activeDiagram && state.activeDiagram.id === diagramId) {
      state.activeDiagram.elements.push(response.block)
    }
    
    ElMessage.success(`生物医学块 ${request.name} 创建成功`)
    return response
  }

  // 工作流管理
  const executeWorkflow = async (
    projectId: string, 
    request: WorkflowExecuteRequest
  ): Promise<WorkflowExecuteResponse> => {
    const response = await apiRequest<WorkflowExecuteResponse>(
      `/projects/${projectId}/workflows`,
      {
        method: 'POST',
        body: JSON.stringify(request)
      }
    )
    
    ElMessage.success('工作流已启动')
    return response
  }

  const getWorkflowStatus = async (workflowId: string) => {
    const status = await apiRequest(`/workflows/${workflowId}/status`)
    return status
  }

  // 知识集成
  const integrateKnowledge = async (
    projectId: string, 
    diagramId: string, 
    request: KnowledgeIntegrationRequest
  ) => {
    const response = await apiRequest<{ knowledge_links_count: number }>(
      `/projects/${projectId}/diagrams/${diagramId}/knowledge`,
      {
        method: 'POST',
        body: JSON.stringify(request)
      }
    )
    
    ElMessage.success(`已集成 ${response.knowledge_links_count} 个知识链接`)
    return response
  }

  // 图形验证
  const validateDiagram = async (diagramId: string, projectId: string): Promise<ValidationResult> => {
    const response = await apiRequest<{ validation_result: ValidationResult }>(
      `/diagrams/${diagramId}/validate`,
      {
        method: 'POST',
        body: JSON.stringify({ project_id: projectId })
      }
    )
    
    const result = response.validation_result
    if (result.is_valid) {
      ElMessage.success('图形验证通过')
    } else {
      ElMessage.warning(`图形验证发现 ${result.violations.length} 个问题`)
    }
    
    return result
  }

  // 约束求解
  const solveBlockConstraints = async (
    projectId: string,
    diagramId: string,
    blockId: string,
    constraintValues: Record<string, any>
  ): Promise<ConstraintSolveResponse> => {
    const response = await apiRequest<ConstraintSolveResponse>(
      `/projects/${projectId}/diagrams/${diagramId}/blocks/${blockId}/solve`,
      {
        method: 'POST',
        body: JSON.stringify(constraintValues)
      }
    )
    
    ElMessage.success('约束求解完成')
    return response
  }

  // 工具和推荐
  const getRecommendations = async (context: Record<string, any>): Promise<ToolRecommendation[]> => {
    const recommendations = await apiRequest<ToolRecommendation[]>('/recommendations', {
      method: 'POST',
      body: JSON.stringify(context)
    })
    
    state.recommendations = recommendations
    return recommendations
  }

  // 元素操作
  const selectElements = (elementIds: string[]) => {
    state.selectedElements = elementIds
  }

  const addElement = (element: UMLElement) => {
    if (state.activeDiagram) {
      state.activeDiagram.elements.push(element)
    }
  }

  const removeElement = (elementId: string) => {
    if (state.activeDiagram) {
      state.activeDiagram.elements = state.activeDiagram.elements.filter(
        e => e.id !== elementId
      )
    }
    // 从选中列表中移除
    state.selectedElements = state.selectedElements.filter(id => id !== elementId)
  }

  const updateElement = (elementId: string, updates: Partial<UMLElement>) => {
    if (state.activeDiagram) {
      const elementIndex = state.activeDiagram.elements.findIndex(e => e.id === elementId)
      if (elementIndex !== -1) {
        state.activeDiagram.elements[elementIndex] = {
          ...state.activeDiagram.elements[elementIndex],
          ...updates,
          updated_at: new Date().toISOString()
        }
      }
    }
  }

  // 清理状态
  const clearState = () => {
    state.currentProject = null
    state.activeDiagram = null
    state.selectedElements = []
    state.diagrams = []
    state.recommendations = []
    state.error = null
  }

  // 计算属性
  const isProjectActive = computed(() => !!state.currentProject)
  const isDiagramActive = computed(() => !!state.activeDiagram)
  const hasSelectedElements = computed(() => state.selectedElements.length > 0)
  const selectedElementsData = computed(() => {
    if (!state.activeDiagram) return []
    return state.activeDiagram.elements.filter(e => 
      state.selectedElements.includes(e.id)
    )
  })
  const diagramElementCount = computed(() => 
    state.activeDiagram?.elements.length || 0
  )

  return {
    // 状态 - 直接返回reactive对象的属性
    isLoading: computed(() => state.isLoading),
    error: computed(() => state.error),
    currentProject: computed(() => state.currentProject),
    activeDiagram: computed(() => state.activeDiagram),
    selectedElements: computed(() => state.selectedElements),
    projects: computed(() => state.projects),
    diagrams: computed(() => state.diagrams),
    recommendations: computed(() => state.recommendations),
    
    // 计算属性
    isProjectActive,
    isDiagramActive,
    hasSelectedElements,
    selectedElementsData,
    diagramElementCount,
    
    // 项目管理方法
    createProject,
    getProjectStatus,
    setCurrentProject,
    loadProjectDiagrams,
    
    // 图形管理方法
    createUMLDiagram,
    createSysMLDiagram,
    getDiagram,
    setActiveDiagram,
    
    // 块管理方法
    createBiomedicalBlock,
    
    // 工作流管理方法
    executeWorkflow,
    getWorkflowStatus,
    
    // 知识集成方法
    integrateKnowledge,
    
    // 验证和求解方法
    validateDiagram,
    solveBlockConstraints,
    
    // 推荐方法
    getRecommendations,
    
    // 元素操作方法
    selectElements,
    addElement,
    removeElement,
    updateElement,
    
    // 工具方法
    clearState
  }
} 