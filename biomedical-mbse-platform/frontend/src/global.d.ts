/// <reference types="node" />

// 全局类型声明
declare global {
  // Promise 全局构造函数
  interface Window {
    Promise: PromiseConstructor;
  }
  
  // Process 全局声明
  var process: NodeJS.Process;
  
  // Promise 构造函数声明
  var Promise: PromiseConstructor;
}

// Node.js 环境变量支持
declare namespace NodeJS {
  interface ProcessEnv {
    VITE_API_BASE_URL?: string;
    NODE_ENV: 'development' | 'production' | 'test';
  }
}

export {}; 