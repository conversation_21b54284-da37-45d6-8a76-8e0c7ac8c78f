import { createRouter, createWebHistory } from 'vue-router'

// 导入生物医学组件
import BiomedicalDashboard from '@/components/biomedical/BiomedicalDashboard.vue'
import ToolManager from '@/components/biomedical/ToolManager.vue'
import MolecularViewer from '@/components/biomedical/MolecularViewer.vue'
import DataStandardsViewer from '@/components/biomedical/DataStandardsViewer.vue'

const routes = [
  {
    path: '/',
    redirect: '/workspace'
  },
  {
    path: '/workspace',
    name: 'Workspace',
    component: () => import('@/views/workspace/Workspace.vue'),
    meta: {
      title: '工作区',
      icon: 'House'
    }
  },
  {
    path: '/visualization-demo',
    name: 'VisualizationDemo',
    component: () => import('@/views/examples/VisualizationDemo.vue'),
    meta: {
      title: '可视化演示',
      icon: 'View'
    }
  },
  {
    path: '/biomedical',
    name: 'BiomedicalDashboard',
    component: BiomedicalDashboard,
    meta: {
      title: '生物医学工作台',
      icon: 'Microscope'
    }
  },
  {
    path: '/tools',
    name: 'ToolManager',
    component: ToolManager,
    meta: {
      title: '工具管理',
      icon: 'Tools'
    }
  },
  {
    path: '/molecular',
    name: 'MolecularViewer',
    component: MolecularViewer,
    meta: {
      title: '分子查看器',
      icon: 'View'
    }
  },
  {
    path: '/standards',
    name: 'DataStandardsViewer',
    component: DataStandardsViewer,
    meta: {
      title: '数据标准',
      icon: 'Document'
    }
  },
  {
    path: '/projects',
    name: 'ProjectManagement',
    component: () => import('@/views/biomedical/ProjectManagement.vue'),
    meta: {
      title: '项目管理',
      icon: 'Folder'
    }
  },
  {
    path: '/modeling',
    name: 'ModelingWorkspace',
    component: () => import('@/views/biomedical/ModelingWorkspace.vue'),
    meta: {
      title: '建模工作区',
      icon: 'SetUp'
    }
  },
  {
    path: '/analysis',
    name: 'AnalysisCenter',
    component: () => import('@/views/biomedical/AnalysisCenter.vue'),
    meta: {
      title: '分析中心',
      icon: 'DataAnalysis'
    }
  },
  // XML遗留系统路由（可选访问）
  {
    path: '/legacy',
    name: 'LegacySystem',
    component: () => import('@/views/biomedical/LegacySystem.vue'),
    meta: {
      title: 'XML元数据系统',
      icon: 'Files'
    },
    children: [
      {
        path: 'dashboard',
        name: 'LegacyDashboard',
        component: () => import('@/components/xml_legacy/SmartDashboard.vue'),
        meta: { title: '智能仪表板' }
      },
      {
        path: 'requirements',
        name: 'RequirementsAnalyst',
        component: () => import('@/components/xml_legacy/requirements/RequirementsAnalystWorkspace.vue'),
        meta: { title: '需求分析' }
      },
      {
        path: 'architecture',
        name: 'SystemArchitect',
        component: () => import('@/components/xml_legacy/architect/SystemArchitectWorkspace.vue'),
        meta: { title: '系统架构' }
      },
      {
        path: 'behavior',
        name: 'BehaviorAnalyst',
        component: () => import('@/components/xml_legacy/behavior/BehaviorAnalystWorkspace.vue'),
        meta: { title: '行为分析' }
      },
      {
        path: 'data',
        name: 'DataModeler',
        component: () => import('@/components/xml_legacy/data/DataModelerWorkspace.vue'),
        meta: { title: '数据建模' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 导航守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 生物医学MBSE建模平台`
  }
  next()
})

export default router 