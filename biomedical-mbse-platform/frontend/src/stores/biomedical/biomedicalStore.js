import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { biomedicalApi } from '@/api/biomedical/biomedicalApi'

export const useBiomedicalStore = defineStore('biomedical', () => {
  // 状态
  const isLoading = ref(false)
  const currentProject = ref(null)
  const projects = ref([])
  const aiRecommendations = ref([])
  const systemHealth = reactive({
    status: 'healthy',
    aiEngineConnected: true,
    xmlSystemConnected: true,
    lastUpdate: new Date()
  })

  // 统计数据
  const statistics = reactive({
    totalProjects: 0,
    activeTools: 0,
    completedAnalyses: 0,
    modelAccuracy: 0.95
  })

  // 方法
  async function initializePlatform() {
    isLoading.value = true
    try {
      // 初始化平台数据
      await Promise.all([
        loadProjects(),
        loadAIRecommendations(),
        checkSystemHealth()
      ])
    } catch (error) {
      console.error('平台初始化失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  async function loadProjects() {
    try {
      const response = await biomedicalApi.getProjects()
      // 后端返回格式：{ data: [], meta: {} }
      const projectData = response.data?.data || response.data || []
      projects.value = Array.isArray(projectData) ? projectData : []
      statistics.totalProjects = projects.value.length
      console.log('加载项目数据:', projects.value) // 调试日志
    } catch (error) {
      console.error('加载项目失败:', error)
      projects.value = []
    }
  }

  async function loadAIRecommendations() {
    try {
      const response = await biomedicalApi.getAIRecommendations()
      aiRecommendations.value = response.data || []
    } catch (error) {
      console.error('加载AI推荐失败:', error)
      aiRecommendations.value = []
    }
  }

  async function checkSystemHealth() {
    try {
      const response = await biomedicalApi.getSystemHealth()
      Object.assign(systemHealth, response.data)
      systemHealth.lastUpdate = new Date()
    } catch (error) {
      console.error('系统健康检查失败:', error)
      systemHealth.status = 'error'
    }
  }

  async function createProject(projectData) {
    isLoading.value = true
    try {
      const response = await biomedicalApi.createProject(projectData)
      // 后端返回格式：{ data: {}, message: "", status: "" }
      const newProject = response.data?.data || response.data
      if (newProject && typeof newProject === 'object') {
        projects.value.unshift(newProject)
        statistics.totalProjects = projects.value.length
      }
      return newProject
    } catch (error) {
      console.error('创建项目失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  function setCurrentProject(project) {
    currentProject.value = project
  }

  function updateStatistics(newStats) {
    Object.assign(statistics, newStats)
  }

  return {
    // 状态
    isLoading,
    currentProject,
    projects,
    aiRecommendations,
    systemHealth,
    statistics,
    
    // 方法
    initializePlatform,
    loadProjects,
    loadAIRecommendations,
    checkSystemHealth,
    createProject,
    setCurrentProject,
    updateStatistics
  }
}) 