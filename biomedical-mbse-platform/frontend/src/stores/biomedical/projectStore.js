import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'
import { biomedicalApi } from '@/api/biomedical/biomedicalApi'

export const useProjectStore = defineStore('project', () => {
  // 状态
  const currentProject = ref(null)
  const projects = ref([])
  const projectTemplates = ref([])
  const isLoading = ref(false)
  const workflows = ref([])
  const projectStats = reactive({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    pendingProjects: 0
  })

  // 计算属性
  const activeProjects = computed(() => 
    projects.value.filter(p => p.status === 'active')
  )

  const completedProjects = computed(() => 
    projects.value.filter(p => p.status === 'completed')
  )

  const projectsByType = computed(() => {
    const grouped = {}
    projects.value.forEach(project => {
      if (!grouped[project.type]) {
        grouped[project.type] = []
      }
      grouped[project.type].push(project)
    })
    return grouped
  })

  // 方法
  async function loadProjects() {
    isLoading.value = true
    try {
      const response = await biomedicalApi.getProjects()
      projects.value = response.data || []
      updateProjectStats()
    } catch (error) {
      console.error('加载项目失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  async function createProject(projectData) {
    try {
      const response = await biomedicalApi.createProject(projectData)
      const newProject = response.data
      projects.value.unshift(newProject)
      updateProjectStats()
      return newProject
    } catch (error) {
      console.error('创建项目失败:', error)
      throw error
    }
  }

  async function updateProject(projectId, projectData) {
    try {
      const response = await biomedicalApi.updateProject(projectId, projectData)
      const updatedProject = response.data
      
      const index = projects.value.findIndex(p => p.id === projectId)
      if (index !== -1) {
        projects.value[index] = updatedProject
      }
      
      if (currentProject.value?.id === projectId) {
        currentProject.value = updatedProject
      }
      
      updateProjectStats()
      return updatedProject
    } catch (error) {
      console.error('更新项目失败:', error)
      throw error
    }
  }

  async function deleteProject(projectId) {
    try {
      await biomedicalApi.deleteProject(projectId)
      projects.value = projects.value.filter(p => p.id !== projectId)
      
      if (currentProject.value?.id === projectId) {
        currentProject.value = null
      }
      
      updateProjectStats()
    } catch (error) {
      console.error('删除项目失败:', error)
      throw error
    }
  }

  async function loadProject(projectId) {
    try {
      const response = await biomedicalApi.getProject(projectId)
      const project = response.data
      currentProject.value = project
      return project
    } catch (error) {
      console.error('加载项目详情失败:', error)
      throw error
    }
  }

  function setCurrentProject(project) {
    currentProject.value = project
  }

  function updateProjectStats() {
    projectStats.totalProjects = projects.value.length
    projectStats.activeProjects = projects.value.filter(p => p.status === 'active').length
    projectStats.completedProjects = projects.value.filter(p => p.status === 'completed').length
    projectStats.pendingProjects = projects.value.filter(p => p.status === 'pending').length
  }

  async function executeWorkflow(workflowData) {
    try {
      const response = await biomedicalApi.executeWorkflow(workflowData)
      return response.data
    } catch (error) {
      console.error('执行工作流失败:', error)
      throw error
    }
  }

  function getProjectsByStatus(status) {
    return projects.value.filter(p => p.status === status)
  }

  function getProjectProgress(projectId) {
    const project = projects.value.find(p => p.id === projectId)
    return project?.progress || 0
  }

  return {
    // 状态
    currentProject,
    projects,
    projectTemplates,
    isLoading,
    workflows,
    projectStats,
    
    // 计算属性
    activeProjects,
    completedProjects,
    projectsByType,
    
    // 方法
    loadProjects,
    createProject,
    updateProject,
    deleteProject,
    loadProject,
    setCurrentProject,
    updateProjectStats,
    executeWorkflow,
    getProjectsByStatus,
    getProjectProgress
  }
}) 