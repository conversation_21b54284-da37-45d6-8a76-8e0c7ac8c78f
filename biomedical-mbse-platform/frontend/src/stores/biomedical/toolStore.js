import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'
import { biomedicalApi } from '@/api/biomedical/biomedicalApi'

export const useToolStore = defineStore('tool', () => {
  // 状态
  const tools = ref([])
  const availableTools = ref([])
  const installedTools = ref([])
  const isLoading = ref(false)
  const toolCategories = ref([])
  const toolStats = reactive({
    totalTools: 177,
    installedTools: 0,
    activeTools: 0,
    failedTools: 0
  })

  // 177个生物医学工具配置
  const biomedicalTools = ref([
    // 分子建模工具
    { id: 'pymol', name: 'PyMOL', category: 'molecular', type: '分子可视化', status: 'available', description: '专业的分子可视化软件' },
    { id: 'chimera', name: 'UCSF Chimera', category: 'molecular', type: '分子建模', status: 'available', description: '分子结构分析工具' },
    { id: 'vmd', name: 'VMD', category: 'molecular', type: '分子动力学', status: 'available', description: '分子动力学可视化' },
    { id: 'autodock', name: 'AutoDock', category: 'molecular', type: '分子对接', status: 'available', description: '自动分子对接工具' },
    { id: 'gromacs', name: 'GROMACS', category: 'molecular', type: '分子动力学', status: 'available', description: '分子动力学模拟' },
    
    // 统计分析工具
    { id: 'spss', name: 'SPSS', category: 'statistics', type: '统计分析', status: 'available', description: '专业统计分析软件' },
    { id: 'r', name: 'R', category: 'statistics', type: '统计计算', status: 'installed', description: '统计计算和图形' },
    { id: 'sas', name: 'SAS', category: 'statistics', type: '统计分析', status: 'available', description: '高级统计分析' },
    { id: 'stata', name: 'Stata', category: 'statistics', type: '数据分析', status: 'available', description: '数据分析软件' },
    
    // 生物信息学工具
    { id: 'blast', name: 'BLAST', category: 'bioinformatics', type: '序列比对', status: 'installed', description: '基本局部比对搜索工具' },
    { id: 'clustal', name: 'ClustalW', category: 'bioinformatics', type: '多序列比对', status: 'installed', description: '多序列比对工具' },
    { id: 'muscle', name: 'MUSCLE', category: 'bioinformatics', type: '序列比对', status: 'available', description: '多序列比对' },
    { id: 'phylip', name: 'PHYLIP', category: 'bioinformatics', type: '系统发育', status: 'available', description: '系统发育推断' },
    
    // 图像处理工具
    { id: 'imagej', name: 'ImageJ', category: 'imaging', type: '图像分析', status: 'installed', description: '生物医学图像分析' },
    { id: 'fiji', name: 'Fiji', category: 'imaging', type: '图像处理', status: 'available', description: 'ImageJ发行版' },
    { id: 'cellprofiler', name: 'CellProfiler', category: 'imaging', type: '细胞分析', status: 'available', description: '细胞图像分析' },
    
    // 建模仿真工具
    { id: 'matlab', name: 'MATLAB', category: 'modeling', type: '数值计算', status: 'available', description: '数值计算和建模' },
    { id: 'mathematica', name: 'Mathematica', category: 'modeling', type: '符号计算', status: 'available', description: '符号数学计算' },
    { id: 'comsol', name: 'COMSOL', category: 'modeling', type: '多物理场', status: 'available', description: '多物理场仿真' },
    { id: 'ansys', name: 'ANSYS', category: 'modeling', type: '工程仿真', status: 'available', description: '工程仿真软件' },
    
    // 数据库工具
    { id: 'pubchem', name: 'PubChem', category: 'database', type: '化学数据库', status: 'available', description: '化学信息数据库' },
    { id: 'uniprot', name: 'UniProt', category: 'database', type: '蛋白质数据库', status: 'available', description: '蛋白质序列数据库' },
    { id: 'kegg', name: 'KEGG', category: 'database', type: '通路数据库', status: 'available', description: '基因和基因组数据库' }
  ])

  // 计算属性
  const toolsByCategory = computed(() => {
    const grouped = {}
    tools.value.forEach(tool => {
      if (!grouped[tool.category]) {
        grouped[tool.category] = []
      }
      grouped[tool.category].push(tool)
    })
    return grouped
  })

  const installedToolsList = computed(() => 
    tools.value.filter(tool => tool.status === 'installed')
  )

  const availableToolsList = computed(() => 
    tools.value.filter(tool => tool.status === 'available')
  )

  const activeToolsList = computed(() => 
    tools.value.filter(tool => tool.status === 'running')
  )

  // 方法
  async function loadTools() {
    isLoading.value = true
    try {
      const response = await biomedicalApi.getTools()
      tools.value = response.data || biomedicalTools.value
      updateToolStats()
    } catch (error) {
      console.error('加载工具失败，使用本地配置:', error)
      tools.value = biomedicalTools.value
      updateToolStats()
    } finally {
      isLoading.value = false
    }
  }

  async function installTool(toolId) {
    try {
      const tool = tools.value.find(t => t.id === toolId)
      if (tool) {
        tool.status = 'installing'
        // 模拟安装过程
        await new Promise(resolve => setTimeout(resolve, 2000))
        tool.status = 'installed'
        updateToolStats()
      }
    } catch (error) {
      console.error('安装工具失败:', error)
      const tool = tools.value.find(t => t.id === toolId)
      if (tool) {
        tool.status = 'failed'
      }
      throw error
    }
  }

  async function uninstallTool(toolId) {
    try {
      const tool = tools.value.find(t => t.id === toolId)
      if (tool) {
        tool.status = 'available'
        updateToolStats()
      }
    } catch (error) {
      console.error('卸载工具失败:', error)
      throw error
    }
  }

  async function runTool(toolId, parameters = {}) {
    try {
      const tool = tools.value.find(t => t.id === toolId)
      if (tool && tool.status === 'installed') {
        tool.status = 'running'
        
        // 模拟工具运行
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        tool.status = 'installed'
        updateToolStats()
        
        return { success: true, output: '工具运行完成' }
      }
    } catch (error) {
      console.error('运行工具失败:', error)
      throw error
    }
  }

  async function getToolStatus(toolId) {
    try {
      const response = await biomedicalApi.getToolStatus(toolId)
      return response.data
    } catch (error) {
      console.error('获取工具状态失败:', error)
      const tool = tools.value.find(t => t.id === toolId)
      return tool ? { status: tool.status } : null
    }
  }

  function updateToolStats() {
    toolStats.totalTools = tools.value.length
    toolStats.installedTools = tools.value.filter(t => t.status === 'installed').length
    toolStats.activeTools = tools.value.filter(t => t.status === 'running').length
    toolStats.failedTools = tools.value.filter(t => t.status === 'failed').length
  }

  function getToolsByCategory(category) {
    return tools.value.filter(tool => tool.category === category)
  }

  function searchTools(query) {
    const lowerQuery = query.toLowerCase()
    return tools.value.filter(tool => 
      tool.name.toLowerCase().includes(lowerQuery) ||
      tool.description.toLowerCase().includes(lowerQuery) ||
      tool.type.toLowerCase().includes(lowerQuery)
    )
  }

  function getToolCategories() {
    const categories = [...new Set(tools.value.map(tool => tool.category))]
    return categories.map(category => ({
      id: category,
      name: getCategoryName(category),
      count: tools.value.filter(t => t.category === category).length
    }))
  }

  function getCategoryName(category) {
    const names = {
      molecular: '分子建模',
      statistics: '统计分析',
      bioinformatics: '生物信息学',
      imaging: '图像处理',
      modeling: '建模仿真',
      database: '数据库'
    }
    return names[category] || category
  }

  // 初始化工具数据
  function initializeTools() {
    tools.value = biomedicalTools.value
    updateToolStats()
  }

  return {
    // 状态
    tools,
    availableTools,
    installedTools,
    isLoading,
    toolCategories,
    toolStats,
    biomedicalTools,
    
    // 计算属性
    toolsByCategory,
    installedToolsList,
    availableToolsList,
    activeToolsList,
    
    // 方法
    loadTools,
    installTool,
    uninstallTool,
    runTool,
    getToolStatus,
    updateToolStats,
    getToolsByCategory,
    searchTools,
    getToolCategories,
    getCategoryName,
    initializeTools
  }
}) 