import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref(null)
  const isAuthenticated = ref(false)
  const permissions = ref([])
  const preferences = reactive({
    theme: 'light',
    language: 'zh-CN',
    notifications: true,
    autoSave: true,
    defaultView: 'dashboard'
  })
  
  const userProfiles = ref([
    {
      id: 1,
      username: 'researcher',
      name: '生物医学研究员',
      email: '<EMAIL>',
      role: 'researcher',
      department: '分子生物学',
      avatar: '',
      lastLogin: new Date().toISOString(),
      projects: ['project-1', 'project-2'],
      preferences: {
        theme: 'light',
        language: 'zh-CN',
        notifications: true,
        autoSave: true,
        defaultView: 'dashboard'
      }
    },
    {
      id: 2,
      username: 'admin',
      name: '系统管理员',
      email: '<EMAIL>',
      role: 'admin',
      department: 'IT部门',
      avatar: '',
      lastLogin: new Date().toISOString(),
      projects: [],
      preferences: {
        theme: 'dark',
        language: 'zh-CN',
        notifications: true,
        autoSave: true,
        defaultView: 'system'
      }
    }
  ])

  // 计算属性
  const userRole = computed(() => currentUser.value?.role || 'guest')
  
  const canAccessAdmin = computed(() => 
    userRole.value === 'admin' || userRole.value === 'superuser'
  )
  
  const canCreateProjects = computed(() => 
    ['admin', 'manager', 'researcher'].includes(userRole.value)
  )
  
  const canManageTools = computed(() => 
    ['admin', 'manager'].includes(userRole.value)
  )

  const userStats = computed(() => {
    if (!currentUser.value) return null
    
    return {
      projectCount: currentUser.value.projects?.length || 0,
      lastLogin: currentUser.value.lastLogin,
      role: currentUser.value.role,
      department: currentUser.value.department
    }
  })

  // 方法
  function login(credentials) {
    return new Promise((resolve, reject) => {
      // 模拟登录验证
      setTimeout(() => {
        const user = userProfiles.value.find(u => 
          u.username === credentials.username
        )
        
        if (user && credentials.password === 'password123') {
          currentUser.value = { ...user }
          isAuthenticated.value = true
          Object.assign(preferences, user.preferences)
          
          // 更新最后登录时间
          user.lastLogin = new Date().toISOString()
          
          // 设置权限
          setUserPermissions(user.role)
          
          resolve(user)
        } else {
          reject(new Error('用户名或密码错误'))
        }
      }, 1000)
    })
  }

  function logout() {
    currentUser.value = null
    isAuthenticated.value = false
    permissions.value = []
    
    // 重置偏好设置
    Object.assign(preferences, {
      theme: 'light',
      language: 'zh-CN',
      notifications: true,
      autoSave: true,
      defaultView: 'dashboard'
    })
  }

  function updateProfile(profileData) {
    if (currentUser.value) {
      Object.assign(currentUser.value, profileData)
      
      // 更新用户档案
      const userIndex = userProfiles.value.findIndex(u => u.id === currentUser.value.id)
      if (userIndex !== -1) {
        Object.assign(userProfiles.value[userIndex], profileData)
      }
    }
  }

  function updatePreferences(newPreferences) {
    Object.assign(preferences, newPreferences)
    
    if (currentUser.value) {
      currentUser.value.preferences = { ...preferences }
      
      // 更新用户档案中的偏好设置
      const userIndex = userProfiles.value.findIndex(u => u.id === currentUser.value.id)
      if (userIndex !== -1) {
        userProfiles.value[userIndex].preferences = { ...preferences }
      }
    }
  }

  function setUserPermissions(role) {
    const rolePermissions = {
      admin: [
        'system.manage',
        'users.manage',
        'projects.create',
        'projects.edit',
        'projects.delete',
        'tools.manage',
        'data.export',
        'settings.modify'
      ],
      manager: [
        'projects.create',
        'projects.edit',
        'projects.delete',
        'tools.manage',
        'data.export',
        'team.manage'
      ],
      researcher: [
        'projects.create',
        'projects.edit',
        'projects.view',
        'tools.use',
        'data.analyze',
        'reports.generate'
      ],
      viewer: [
        'projects.view',
        'data.view',
        'reports.view'
      ],
      guest: [
        'projects.view'
      ]
    }
    
    permissions.value = rolePermissions[role] || rolePermissions.guest
  }

  function hasPermission(permission) {
    return permissions.value.includes(permission)
  }

  function canAccess(resource, action = 'view') {
    const permissionKey = `${resource}.${action}`
    return hasPermission(permissionKey)
  }

  // 获取用户的项目权限
  function getProjectPermissions(projectId) {
    if (!currentUser.value) return []
    
    const basePermissions = []
    
    // 基于用户角色的权限
    if (userRole.value === 'admin') {
      basePermissions.push('read', 'write', 'delete', 'share', 'manage')
    } else if (userRole.value === 'manager') {
      basePermissions.push('read', 'write', 'share', 'manage')
    } else if (userRole.value === 'researcher') {
      basePermissions.push('read', 'write', 'share')
    } else {
      basePermissions.push('read')
    }
    
    return basePermissions
  }

  // 切换主题
  function toggleTheme() {
    const newTheme = preferences.theme === 'light' ? 'dark' : 'light'
    updatePreferences({ ...preferences, theme: newTheme })
  }

  // 设置语言
  function setLanguage(language) {
    updatePreferences({ ...preferences, language })
  }

  // 初始化用户状态（例如从localStorage恢复）
  function initializeUser() {
    try {
      const savedUser = localStorage.getItem('biomedical_user')
      const savedPrefs = localStorage.getItem('biomedical_preferences')
      
      if (savedUser) {
        const userData = JSON.parse(savedUser)
        currentUser.value = userData
        isAuthenticated.value = true
        setUserPermissions(userData.role)
      }
      
      if (savedPrefs) {
        const prefsData = JSON.parse(savedPrefs)
        Object.assign(preferences, prefsData)
      }
    } catch (error) {
      console.error('初始化用户状态失败:', error)
    }
  }

  // 保存用户状态到localStorage
  function saveUserState() {
    try {
      if (currentUser.value) {
        localStorage.setItem('biomedical_user', JSON.stringify(currentUser.value))
      }
      localStorage.setItem('biomedical_preferences', JSON.stringify(preferences))
    } catch (error) {
      console.error('保存用户状态失败:', error)
    }
  }

  return {
    // 状态
    currentUser,
    isAuthenticated,
    permissions,
    preferences,
    userProfiles,
    
    // 计算属性
    userRole,
    canAccessAdmin,
    canCreateProjects,
    canManageTools,
    userStats,
    
    // 方法
    login,
    logout,
    updateProfile,
    updatePreferences,
    setUserPermissions,
    hasPermission,
    canAccess,
    getProjectPermissions,
    toggleTheme,
    setLanguage,
    initializeUser,
    saveUserState
  }
}) 