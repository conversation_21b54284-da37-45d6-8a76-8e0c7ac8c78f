import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

interface Connection {
  id: string
  name: string
  type: 'database' | 'api' | 'service' | 'file'
  status: 'connected' | 'disconnected' | 'error' | 'connecting'
  url: string
  lastConnected?: string
  responseTime?: number
  description?: string
}

interface ConnectionStats {
  total: number
  connected: number
  error: number
  avgResponseTime: number
}

export const useConnectionStore = defineStore('connections', () => {
  // 状态
  const connections = ref<Connection[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const connectionStats = computed((): ConnectionStats => {
    const total = connections.value.length
    const connected = connections.value.filter(c => c.status === 'connected').length
    const errorCount = connections.value.filter(c => c.status === 'error').length
    const responseTimes = connections.value
      .filter(c => c.responseTime)
      .map(c => c.responseTime!)
    const avgResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0

    return {
      total,
      connected,
      error: errorCount,
      avgResponseTime
    }
  })

  const activeConnections = computed(() => 
    connections.value.filter(c => c.status === 'connected')
  )

  const failedConnections = computed(() => 
    connections.value.filter(c => c.status === 'error')
  )

  // 操作方法
  const fetchConnections = async () => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // 模拟数据
      connections.value = [
        {
          id: '1',
          name: 'XML数据库',
          type: 'database',
          status: 'connected',
          url: 'mysql://localhost:3306/xml_metadata',
          lastConnected: new Date().toISOString(),
          responseTime: 45,
          description: '主要的XML元数据存储数据库'
        },
        {
          id: '2',
          name: 'API网关',
          type: 'api',
          status: 'connected',
          url: 'http://localhost:5001/api',
          lastConnected: new Date().toISOString(),
          responseTime: 120,
          description: '后端API服务接口'
        },
        {
          id: '3',
          name: 'Redis缓存',
          type: 'service',
          status: 'error',
          url: 'redis://localhost:6379',
          description: '缓存服务器连接失败'
        },
        {
          id: '4',
          name: 'XML文件系统',
          type: 'file',
          status: 'connected',
          url: '/data/xml_files',
          lastConnected: new Date().toISOString(),
          responseTime: 25,
          description: 'XML文件存储系统'
        }
      ]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取连接信息失败'
    } finally {
      loading.value = false
    }
  }

  const testConnection = async (id: string) => {
    const connection = connections.value.find(c => c.id === id)
    if (!connection) return

    connection.status = 'connecting'
    
    try {
      // 模拟连接测试
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 随机模拟成功或失败
      const success = Math.random() > 0.3
      
      if (success) {
        connection.status = 'connected'
        connection.lastConnected = new Date().toISOString()
        connection.responseTime = Math.floor(Math.random() * 200) + 20
      } else {
        connection.status = 'error'
      }
    } catch (err) {
      connection.status = 'error'
    }
  }

  const addConnection = (connection: Omit<Connection, 'id'>) => {
    const newConnection: Connection = {
      ...connection,
      id: Date.now().toString()
    }
    connections.value.push(newConnection)
  }

  const removeConnection = (id: string) => {
    const index = connections.value.findIndex(c => c.id === id)
    if (index > -1) {
      connections.value.splice(index, 1)
    }
  }

  const updateConnection = (id: string, updates: Partial<Connection>) => {
    const connection = connections.value.find(c => c.id === id)
    if (connection) {
      Object.assign(connection, updates)
    }
  }

  const analyzeConnections = async (role?: string) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟连接分析
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 基于角色调整连接分析策略
      const analysisDepth = role === 'system_architect' ? 'deep' : 'standard'
      
      // 更新连接状态和性能指标
      connections.value.forEach(connection => {
        // 模拟分析结果
        if (connection.status === 'connected') {
          // 随机更新响应时间
          connection.responseTime = Math.floor(Math.random() * 100) + 20
          connection.lastConnected = new Date().toISOString()
        }
        
        // 基于角色进行特定分析
        if (role === 'system_architect' && connection.type === 'database') {
          // 系统架构师关注数据库性能
          connection.responseTime = Math.floor(Math.random() * 50) + 10
        } else if (role === 'test_engineer' && connection.type === 'api') {
          // 测试工程师关注API可靠性
          connection.status = Math.random() > 0.8 ? 'error' : 'connected'
        }
      })
      
      // 可以在这里添加更多基于角色的分析逻辑
      console.log(`完成${role || 'default'}角色的连接分析，深度: ${analysisDepth}`)
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : '连接分析失败'
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    connections,
    loading,
    error,
    
    // 计算属性
    connectionStats,
    activeConnections,
    failedConnections,
    
    // 方法
    fetchConnections,
    analyzeConnections,
    testConnection,
    addConnection,
    removeConnection,
    updateConnection
  }
}) 