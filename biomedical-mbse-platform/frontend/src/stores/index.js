import { createPinia } from 'pinia'

// 导入各个store模块
import { useBiomedicalStore } from './biomedical/biomedicalStore'
import { useProjectStore } from './biomedical/projectStore'
import { useToolStore } from './biomedical/toolStore'
import { useUserStore } from './biomedical/userStore'

const pinia = createPinia()

export default pinia

// 导出所有store以便使用
export {
  useBiomedicalStore,
  useProjectStore,
  useToolStore,
  useUserStore
} 