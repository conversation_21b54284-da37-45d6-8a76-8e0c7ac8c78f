import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Recommendation } from '../types/parsing'

export const useRecommendationStore = defineStore('recommendations', () => {
  // 状态
  const recommendations = ref<Recommendation[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeRecommendations = computed(() => 
    recommendations.value
  )

  const recommendationsByPriority = computed(() => {
    const groups = {
      high: [] as Recommendation[],
      medium: [] as Recommendation[],
      low: [] as Recommendation[]
    }
    
    activeRecommendations.value.forEach(rec => {
      groups[rec.priority].push(rec)
    })
    
    return groups
  })

  const totalRecommendations = computed(() => recommendations.value.length)

  // 操作方法
  const fetchRecommendations = async () => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      recommendations.value = [
        {
          id: '1',
          title: '优化XML解析性能',
          description: '建议使用流式解析来处理大型XML文件，可以减少内存使用量50%以上',
          priority: 'medium',
          type: 'optimization',
          targetElements: ['root', 'data'],
          suggestedAction: '启用流式解析模式',
          confidence: 0.85
        },
        {
          id: '2',
          title: '增强数据验证',
          description: '添加XML Schema验证可以提高数据质量和系统安全性',
          priority: 'high',
          type: 'structure',
          targetElements: ['schema'],
          suggestedAction: '添加XSD验证',
          confidence: 0.92
        },
        {
          id: '3',
          title: '改进元素命名',
          description: '建议使用更具描述性的元素名称来提升可读性',
          priority: 'low',
          type: 'naming',
          targetElements: ['item1', 'item2'],
          suggestedAction: '重命名元素',
          confidence: 0.68
        }
      ]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取推荐失败'
    } finally {
      loading.value = false
    }
  }

  const applyRecommendation = async (id: string) => {
    const recommendation = recommendations.value.find(r => r.id === id)
    if (recommendation) {
      // 在真实应用中，这里会调用API来应用推荐
      console.log(`应用推荐: ${recommendation.title}`)
      // 从列表中移除已应用的推荐
      const index = recommendations.value.findIndex(r => r.id === id)
      if (index > -1) {
        recommendations.value.splice(index, 1)
      }
    }
  }

  const dismissRecommendation = async (id: string) => {
    const index = recommendations.value.findIndex(r => r.id === id)
    if (index > -1) {
      recommendations.value.splice(index, 1)
    }
  }

  const addRecommendation = (recommendation: Omit<Recommendation, 'id'>) => {
    const newRecommendation: Recommendation = {
      ...recommendation,
      id: Date.now().toString()
    }
    recommendations.value.unshift(newRecommendation)
  }

  const generateRecommendations = async (options?: {
    role?: string
    includePerformance?: boolean
    includeOptimization?: boolean
  }) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const { role = 'system_architect', includePerformance = true, includeOptimization = true } = options || {}
      
      // 基于角色和选项生成不同的推荐
      const newRecommendations: Recommendation[] = []
      
      if (includePerformance) {
        newRecommendations.push({
          id: `perf_${Date.now()}`,
          title: '优化缓存策略',
          description: `基于${role}角色分析，建议调整缓存策略以提升性能`,
          priority: 'medium',
          type: 'optimization',
          targetElements: ['cache', 'memory'],
          suggestedAction: '调整缓存配置',
          confidence: 0.78
        })
      }
      
      if (includeOptimization) {
        newRecommendations.push({
          id: `opt_${Date.now()}`,
          title: '内存使用优化',
          description: '检测到内存使用率偏高，建议启用增量解析模式',
          priority: 'high',
          type: 'optimization',
          targetElements: ['parser', 'memory'],
          suggestedAction: '启用增量解析',
          confidence: 0.89
        })
      }
      
      // 角色特定推荐
      if (role === 'system_architect') {
        newRecommendations.push({
          id: `arch_${Date.now()}`,
          title: '系统架构优化',
          description: '建议采用微服务架构来提升系统可扩展性',
          priority: 'low',
          type: 'structure',
          targetElements: ['architecture'],
          suggestedAction: '重构为微服务',
          confidence: 0.65
        })
      }
      
      // 替换现有推荐
      recommendations.value = newRecommendations
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : '生成推荐失败'
    } finally {
      loading.value = false
    }
  }

  return {
    // 状态
    recommendations,
    loading,
    error,
    
    // 计算属性
    activeRecommendations,
    recommendationsByPriority,
    totalRecommendations,
    
    // 方法
    fetchRecommendations,
    generateRecommendations,
    applyRecommendation,
    dismissRecommendation,
    addRecommendation
  }
}) 