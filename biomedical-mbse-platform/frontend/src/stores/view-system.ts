// 视图系统状态管理

import { defineStore } from 'pinia'
import { ref, computed, reactive } from 'vue'
import { viewEngine } from '@/core/ViewEngine'
import type { 
  ViewConfig, 
  ComponentConfig, 
  MetadataContext,
  FilterConfig,
  CustomizationConfig,
  ViewType,
  ComponentType,
  ComponentState
} from '@/types/view-system'
import type { UserRole } from '@/types/parsing'

export const useViewSystemStore = defineStore('view-system', () => {
  // ====== 核心状态 ======
  
  // 视图管理
  const availableViews = ref<ViewConfig[]>([])
  const activeViewId = ref<string | null>(null)
  const userRole = ref<UserRole>('system_architect')
  const metadataContext = ref<MetadataContext | null>(null)
  
  // 定制化状态
  const customizationMode = ref(false)
  const selectedComponentId = ref<string | null>(null)
  const viewHistory = ref<string[]>([])
  const userCustomizations = reactive(new Map<string, Partial<ViewConfig>>())
  
  // 过滤和搜索
  const activeFilters = ref<FilterConfig[]>([])
  const searchQuery = ref('')
  const filterPresets = ref<FilterConfig[]>([])
  
  // UI状态
  const sidebarOpen = ref(true)
  const toolbarVisible = ref(true)
  const customizationPanelOpen = ref(false)
  const componentEditorOpen = ref(false)
  
  // ====== 计算属性 ======
  
  // 当前激活的视图
  const activeView = computed(() => {
    if (!activeViewId.value) return null
    return availableViews.value.find(view => view.id === activeViewId.value) || null
  })
  
  // 基于角色过滤的可用视图
  const accessibleViews = computed(() => {
    return availableViews.value.filter(view => {
      const permissions = view.metadata?.permissions || []
      return permissions.some(permission => 
        permission.role === userRole.value || 
        permission.role === 'all'
      )
    })
  })
  
  // 按类型分组的视图
  const viewsByType = computed(() => {
    const grouped: Record<ViewType, ViewConfig[]> = {
      dashboard: [],
      hierarchy: [],
      network: [],
      timeline: [],
      spatial: [],
      analytical: [],
      comparative: [],
      custom: []
    }
    
    accessibleViews.value.forEach(view => {
      grouped[view.type].push(view)
    })
    
    return grouped
  })
  
  // 搜索过滤后的视图
  const filteredViews = computed(() => {
    if (!searchQuery.value) return accessibleViews.value
    
    const query = searchQuery.value.toLowerCase()
    return accessibleViews.value.filter(view => 
      view.name.toLowerCase().includes(query) ||
      (view.description && view.description.toLowerCase().includes(query)) ||
      (view.metadata?.tags && view.metadata.tags.some(tag => tag.toLowerCase().includes(query)))
    )
  })
  
  // 当前视图的组件列表
  const currentViewComponents = computed(() => {
    return activeView.value?.components || []
  })
  
  // 选中的组件配置
  const selectedComponent = computed(() => {
    if (!selectedComponentId.value || !activeView.value) return null
    return activeView.value.components.find(c => c.id === selectedComponentId.value) || null
  })
  
  // 可用的组件类型（基于角色和数据上下文）
  const availableComponentTypes = computed(() => {
    const components: Array<{
      type: ComponentType
      name: string
      description: string
      category: string
      icon: string
      supportedRoles: (UserRole | 'all')[]
      requiredData: string[]
      complexity: 'simple' | 'medium' | 'advanced'
    }> = [
      // 数据展示组件
      {
        type: 'element-tree',
        name: '元素树',
        description: '层次化显示XML元素结构',
        category: '数据展示',
        icon: 'tree',
        supportedRoles: ['system_architect', 'requirements_analyst'],
        requiredData: ['elements'],
        complexity: 'simple'
      },
      {
        type: 'element-list',
        name: '元素列表',
        description: '平铺显示XML元素',
        category: '数据展示',
        icon: 'list',
        supportedRoles: ['all'],
        requiredData: ['elements'],
        complexity: 'simple'
      },
      {
        type: 'element-card',
        name: '元素卡片',
        description: '卡片式展示元素信息',
        category: '数据展示',
        icon: 'card',
        supportedRoles: ['all'],
        requiredData: ['elements'],
        complexity: 'simple'
      },
      {
        type: 'element-table',
        name: '元素表格',
        description: '表格形式展示元素属性',
        category: '数据展示',
        icon: 'table',
        supportedRoles: ['all'],
        requiredData: ['elements'],
        complexity: 'medium'
      },
      
      // 关系可视化组件
      {
        type: 'relationship-graph',
        name: '关系图',
        description: '可视化元素间的关系',
        category: '关系可视化',
        icon: 'network',
        supportedRoles: ['system_architect', 'behavior_analyst'],
        requiredData: ['relationships'],
        complexity: 'medium'
      },
      {
        type: 'hierarchy-tree',
        name: '层次树',
        description: '树状结构展示层次关系',
        category: '关系可视化',
        icon: 'tree-alt',
        supportedRoles: ['system_architect', 'requirements_analyst'],
        requiredData: ['relationships'],
        complexity: 'medium'
      },
      {
        type: 'network-diagram',
        name: '网络图',
        description: '网络拓扑图展示复杂关系',
        category: '关系可视化',
        icon: 'project-diagram',
        supportedRoles: ['system_architect'],
        requiredData: ['relationships'],
        complexity: 'advanced'
      },
      {
        type: 'connection-matrix',
        name: '连接矩阵',
        description: '矩阵形式展示连接关系',
        category: '关系可视化',
        icon: 'border-all',
        supportedRoles: ['system_architect', 'behavior_analyst'],
        requiredData: ['relationships'],
        complexity: 'medium'
      },
      
      // 分析组件
      {
        type: 'semantic-cluster',
        name: '语义聚类',
        description: '基于语义相似性的元素聚类',
        category: '分析组件',
        icon: 'cluster',
        supportedRoles: ['requirements_analyst', 'behavior_analyst'],
        requiredData: ['clusters'],
        complexity: 'advanced'
      },
      {
        type: 'perspective-view',
        name: '视角视图',
        description: '基于用户角色的专门视角',
        category: '分析组件',
        icon: 'eye',
        supportedRoles: ['all'],
        requiredData: ['perspectives'],
        complexity: 'medium'
      },
      {
        type: 'recommendation-panel',
        name: '推荐面板',
        description: '智能推荐和洞察',
        category: '分析组件',
        icon: 'lightbulb',
        supportedRoles: ['all'],
        requiredData: ['recommendations'],
        complexity: 'medium'
      },
      {
        type: 'insight-summary',
        name: '洞察摘要',
        description: '业务洞察和模式总结',
        category: '分析组件',
        icon: 'brain',
        supportedRoles: ['all'],
        requiredData: ['recommendations'],
        complexity: 'advanced'
      },
      
      // 图表组件
      {
        type: 'bar-chart',
        name: '柱状图',
        description: '统计数据的柱状图展示',
        category: '图表组件',
        icon: 'chart-bar',
        supportedRoles: ['all'],
        requiredData: ['elements'],
        complexity: 'simple'
      },
      {
        type: 'line-chart',
        name: '折线图',
        description: '趋势数据的折线图展示',
        category: '图表组件',
        icon: 'chart-line',
        supportedRoles: ['all'],
        requiredData: ['elements'],
        complexity: 'simple'
      },
      {
        type: 'pie-chart',
        name: '饼图',
        description: '比例数据的饼图展示',
        category: '图表组件',
        icon: 'chart-pie',
        supportedRoles: ['all'],
        requiredData: ['elements'],
        complexity: 'simple'
      },
      {
        type: 'scatter-plot',
        name: '散点图',
        description: '相关性数据的散点图展示',
        category: '图表组件',
        icon: 'braille',
        supportedRoles: ['behavior_analyst', 'test_engineer'],
        requiredData: ['elements'],
        complexity: 'medium'
      },
      {
        type: 'heatmap',
        name: '热力图',
        description: '密度数据的热力图展示',
        category: '图表组件',
        icon: 'th',
        supportedRoles: ['system_architect', 'behavior_analyst'],
        requiredData: ['relationships'],
        complexity: 'medium'
      },
      
      // 3D组件
      {
        type: '3d-scene',
        name: '3D场景',
        description: '三维场景容器',
        category: '3D组件',
        icon: 'cube',
        supportedRoles: ['system_architect'],
        requiredData: ['elements', 'relationships'],
        complexity: 'advanced'
      },
      {
        type: '3d-model',
        name: '3D模型',
        description: '三维模型展示',
        category: '3D组件',
        icon: 'shapes',
        supportedRoles: ['system_architect'],
        requiredData: ['elements'],
        complexity: 'advanced'
      },
      {
        type: '3d-network',
        name: '3D网络图',
        description: '三维关系网络可视化',
        category: '3D组件',
        icon: 'project-diagram',
        supportedRoles: ['system_architect'],
        requiredData: ['relationships', 'clusters'],
        complexity: 'advanced'
      },
      
      // 交互组件
      {
        type: 'filter-panel',
        name: '过滤面板',
        description: '数据过滤控制面板',
        category: '交互组件',
        icon: 'filter',
        supportedRoles: ['all'],
        requiredData: [],
        complexity: 'simple'
      },
      {
        type: 'search-box',
        name: '搜索框',
        description: '数据搜索组件',
        category: '交互组件',
        icon: 'search',
        supportedRoles: ['all'],
        requiredData: [],
        complexity: 'simple'
      },
      {
        type: 'control-panel',
        name: '控制面板',
        description: '系统控制操作面板',
        category: '交互组件',
        icon: 'sliders-h',
        supportedRoles: ['system_architect', 'test_engineer'],
        requiredData: [],
        complexity: 'medium'
      },
      {
        type: 'toolbar',
        name: '工具栏',
        description: '功能操作工具栏',
        category: '交互组件',
        icon: 'tools',
        supportedRoles: ['all'],
        requiredData: [],
        complexity: 'simple'
      }
    ]
    
    // 基于角色过滤
    return components.filter(component => 
      component.supportedRoles.includes('all') || 
      component.supportedRoles.includes(userRole.value)
    ).filter(component => {
      // 基于可用数据过滤
      if (!metadataContext.value || component.requiredData.length === 0) {
        return true
      }
      
      return component.requiredData.every(dataType => {
        switch (dataType) {
          case 'elements':
            return metadataContext.value!.elements.length > 0
          case 'relationships':
            return metadataContext.value!.relationships.length > 0
          case 'clusters':
            return metadataContext.value!.semanticClusters.length > 0
          case 'perspectives':
            return metadataContext.value!.perspectives.length > 0
          case 'recommendations':
            return metadataContext.value!.recommendations.length > 0
          default:
            return true
        }
      })
    })
  })
  
  // 按类别分组的可用组件
  const componentsByCategory = computed(() => {
    const grouped: Record<string, typeof availableComponentTypes.value> = {}
    
    availableComponentTypes.value.forEach(component => {
      if (!grouped[component.category]) {
        grouped[component.category] = []
      }
      grouped[component.category].push(component)
    })
    
    return grouped
  })
  
  // ====== 辅助函数 ======

  // 创建默认的组件状态样式
  const createDefaultStateStyles = (): Record<ComponentState, Record<string, string>> => {
    return {
      default: {},
      hover: {},
      active: {},
      selected: {},
      disabled: {},
      loading: {},
      error: {},
      custom: {}
    }
  }
  
  // ====== 操作方法 ======
  
  // 视图管理
  const registerView = (config: ViewConfig) => {
    viewEngine.registerView(config)
    const index = availableViews.value.findIndex(v => v.id === config.id)
    if (index > -1) {
      availableViews.value[index] = config
    } else {
      availableViews.value.push(config)
    }
  }
  
  const activateView = async (viewId: string) => {
    try {
      await viewEngine.activateView(viewId)
      activeViewId.value = viewId
      
      // 更新历史记录
      const historyIndex = viewHistory.value.indexOf(viewId)
      if (historyIndex > -1) {
        viewHistory.value.splice(historyIndex, 1)
      }
      viewHistory.value.unshift(viewId)
      
      // 限制历史记录长度
      if (viewHistory.value.length > 10) {
        viewHistory.value = viewHistory.value.slice(0, 10)
      }
      
      return true
    } catch (error) {
      console.error('激活视图失败:', error)
      return false
    }
  }
  
  const createCustomView = (baseViewId: string, customizations: Partial<ViewConfig>): string => {
    const baseView = availableViews.value.find(v => v.id === baseViewId)
    if (!baseView) {
      throw new Error(`基础视图 ${baseViewId} 不存在`)
    }
    
    const customViewId = `${baseViewId}_custom_${Date.now()}`
    const customView: ViewConfig = {
      ...baseView,
      ...customizations,
      id: customViewId,
      metadata: {
        author: 'user_customization',
        version: '1.0.0',
        created: new Date().toISOString(),
        modified: new Date().toISOString(),
        tags: [...(baseView.metadata?.tags || []), 'custom'],
        category: baseView.metadata?.category || 'custom',
        permissions: baseView.metadata?.permissions || [],
        dependencies: baseView.metadata?.dependencies || [],
        compatibility: baseView.metadata?.compatibility || {
          minVersion: '1.0.0',
          requiredFeatures: [],
          optionalFeatures: []
        },
        ...customizations.metadata
      }
    }
    
    registerView(customView)
    return customViewId
  }
  
  const deleteView = (viewId: string) => {
    const index = availableViews.value.findIndex(v => v.id === viewId)
    if (index > -1) {
      availableViews.value.splice(index, 1)
      
      // 如果删除的是当前激活视图，切换到默认视图
      if (activeViewId.value === viewId) {
        activeViewId.value = null
      }
      
      // 从历史记录中移除
      const historyIndex = viewHistory.value.indexOf(viewId)
      if (historyIndex > -1) {
        viewHistory.value.splice(historyIndex, 1)
      }
      
      // 清理用户定制
      userCustomizations.delete(viewId)
    }
  }
  
  // 组件管理
  const addComponent = (componentType: ComponentType, position?: { x: number; y: number }) => {
    if (!activeView.value) return false
    
    const newComponent: ComponentConfig = {
      id: `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: componentType,
      name: `${componentType} 组件`,
      category: 'user-added',
      position: {
        x: position?.x || 0,
        y: position?.y || 0,
        width: 200,
        height: 150
      },
      dataBinding: {
        source: { type: 'elements', cache: true, realtime: false },
        filters: [],
        transformations: [],
        aggregations: [],
        refresh: { auto: false, triggers: [] }
      },
      styling: {
        baseStyles: {},
        stateStyles: createDefaultStateStyles(),
        customClasses: [],
        theme: 'default'
      },
      customProps: { userCustomizable: true }
    }
    
    activeView.value.components.push(newComponent)
    return newComponent.id
  }
  
  const removeComponent = (componentId: string) => {
    if (!activeView.value) return false
    
    const index = activeView.value.components.findIndex(c => c.id === componentId)
    if (index > -1) {
      activeView.value.components.splice(index, 1)
      
      // 如果删除的是选中组件，清除选择
      if (selectedComponentId.value === componentId) {
        selectedComponentId.value = null
      }
      
      return true
    }
    return false
  }
  
  const updateComponent = (componentId: string, updates: Partial<ComponentConfig>) => {
    if (!activeView.value) return false
    
    const component = activeView.value.components.find(c => c.id === componentId)
    if (component) {
      Object.assign(component, updates)
      return true
    }
    return false
  }
  
  const selectComponent = (componentId: string | null) => {
    selectedComponentId.value = componentId
  }
  
  // 定制化管理
  const enterCustomizationMode = () => {
    customizationMode.value = true
    customizationPanelOpen.value = true
  }
  
  const exitCustomizationMode = () => {
    customizationMode.value = false
    customizationPanelOpen.value = false
    selectedComponentId.value = null
  }
  
  const saveCustomization = () => {
    if (!activeView.value) return false
    
    // 保存用户定制
    userCustomizations.set(activeView.value.id, { ...activeView.value })
    
    // 保存到视图引擎
    viewEngine.saveViewCustomization(activeView.value.id, activeView.value)
    
    exitCustomizationMode()
    return true
  }
  
  const resetCustomization = () => {
    if (!activeViewId.value) return false
    
    // 清除用户定制
    userCustomizations.delete(activeViewId.value)
    
    // 重新加载原始视图
    const originalView = viewEngine.getView(activeViewId.value)
    if (originalView) {
      const index = availableViews.value.findIndex(v => v.id === activeViewId.value)
      if (index > -1) {
        availableViews.value[index] = { ...originalView }
      }
    }
    
    return true
  }
  
  // 过滤管理
  const addFilter = (filter: FilterConfig) => {
    activeFilters.value.push(filter)
  }
  
  const removeFilter = (filterId: string) => {
    const index = activeFilters.value.findIndex(f => f.id === filterId)
    if (index > -1) {
      activeFilters.value.splice(index, 1)
    }
  }
  
  const updateFilter = (filterId: string, updates: Partial<FilterConfig>) => {
    const filter = activeFilters.value.find(f => f.id === filterId)
    if (filter) {
      Object.assign(filter, updates)
    }
  }
  
  const clearFilters = () => {
    activeFilters.value = []
  }
  
  const saveFilterPreset = (name: string, filters: FilterConfig[]) => {
    const preset: FilterConfig = {
      id: `preset_${Date.now()}`,
      name,
      type: 'custom',
      rules: [],
      logic: 'AND',
      active: false,
      userConfigurable: true
    }
    
    filterPresets.value.push(preset)
  }
  
  // 系统状态管理
  const setUserRole = (role: UserRole) => {
    userRole.value = role
    viewEngine.setCurrentRole(role)
  }
  
  const setMetadataContext = (context: MetadataContext) => {
    metadataContext.value = context
    viewEngine.setMetadataContext(context)
  }
  
  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }
  
  const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value
  }
  
  const toggleToolbar = () => {
    toolbarVisible.value = !toolbarVisible.value
  }
  
  const openComponentEditor = () => {
    componentEditorOpen.value = true
  }
  
  const closeComponentEditor = () => {
    componentEditorOpen.value = false
    selectedComponentId.value = null
  }
  
  // 导入/导出
  const exportView = (viewId: string) => {
    const view = availableViews.value.find(v => v.id === viewId)
    if (!view) return null
    
    return JSON.stringify(view, null, 2)
  }
  
  const importView = (viewJson: string) => {
    try {
      const view: ViewConfig = JSON.parse(viewJson)
      
      // 验证视图配置
      if (!view.id || !view.name || !view.type) {
        throw new Error('无效的视图配置')
      }
      
      // 确保ID唯一
      if (availableViews.value.some(v => v.id === view.id)) {
        view.id = `${view.id}_imported_${Date.now()}`
      }
      
      registerView(view)
      return view.id
    } catch (error) {
      console.error('导入视图失败:', error)
      return null
    }
  }
  
  const exportAllViews = () => {
    return JSON.stringify(availableViews.value, null, 2)
  }
  
  const importAllViews = (viewsJson: string) => {
    try {
      const views: ViewConfig[] = JSON.parse(viewsJson)
      
      views.forEach(view => {
        // 确保ID唯一
        if (availableViews.value.some(v => v.id === view.id)) {
          view.id = `${view.id}_imported_${Date.now()}`
        }
        registerView(view)
      })
      
      return true
    } catch (error) {
      console.error('导入视图列表失败:', error)
      return false
    }
  }
  
  return {
    // 状态
    availableViews,
    activeViewId,
    userRole,
    metadataContext,
    customizationMode,
    selectedComponentId,
    viewHistory,
    userCustomizations,
    activeFilters,
    searchQuery,
    filterPresets,
    sidebarOpen,
    toolbarVisible,
    customizationPanelOpen,
    componentEditorOpen,
    
    // 计算属性
    activeView,
    accessibleViews,
    viewsByType,
    filteredViews,
    currentViewComponents,
    selectedComponent,
    availableComponentTypes,
    componentsByCategory,
    
    // 方法
    registerView,
    activateView,
    createCustomView,
    deleteView,
    addComponent,
    removeComponent,
    updateComponent,
    selectComponent,
    enterCustomizationMode,
    exitCustomizationMode,
    saveCustomization,
    resetCustomization,
    addFilter,
    removeFilter,
    updateFilter,
    clearFilters,
    saveFilterPreset,
    setUserRole,
    setMetadataContext,
    setSearchQuery,
    toggleSidebar,
    toggleToolbar,
    openComponentEditor,
    closeComponentEditor,
    exportView,
    importView,
    exportAllViews,
    importAllViews
  }
}) 