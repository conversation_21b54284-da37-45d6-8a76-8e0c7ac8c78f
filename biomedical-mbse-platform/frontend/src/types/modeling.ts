/**
 * 建模系统TypeScript类型定义
 * ==============================
 * 
 * 定义UML/SysML建模系统相关的TypeScript类型
 */

// 基础类型定义
export interface VisualProperties {
  x: number
  y: number
  width: number
  height: number
  fill_color: string
  stroke_color: string
  stroke_width: number
}

export interface BiologicalType {
  category: 'protein' | 'gene' | 'pathway' | 'cell' | 'tissue' | 'organism'
  subcategory?: string
  ontology_id?: string // GO, KEGG, UniProt等本体ID
  evidence_codes?: string[]
}

export interface SequenceData {
  sequence: string
  sequence_type: 'DNA' | 'RNA' | 'Protein'
  length: number
  gc_content?: number
  molecular_weight?: number
  modifications?: SequenceModification[]
}

export interface SequenceModification {
  position: number
  modification_type: string
  description: string
}

export interface MolecularProperties {
  formula?: string
  mass: number
  charge?: number
  stability_score?: number
  binding_affinity?: BindingAffinity[]
  structural_features?: StructuralFeature[]
}

export interface BindingAffinity {
  target: string
  affinity_value: number
  unit: string
  measurement_method: string
}

export interface StructuralFeature {
  feature_type: string
  start_position?: number
  end_position?: number
  description: string
}

export interface ToolAnalysisResult {
  tool_name: string
  tool_version: string
  analysis_type: string
  result_data: any
  confidence_score?: number
  execution_time: number
  parameters_used: Record<string, any>
}

export interface KnowledgeLink {
  document_id: string
  document_title: string
  section: string
  relevance_score: number
  relationship_type: 'describes' | 'references' | 'validates' | 'conflicts'
}

// UML核心类型
export interface UMLElement {
  id: string
  name: string
  element_type: string
  stereotype?: string
  tagged_values?: Record<string, any>
  documentation?: string
  
  // 基础属性
  visual_properties: VisualProperties
  
  // 生物医学扩展属性
  biological_type?: BiologicalType
  sequence_data?: SequenceData
  molecular_properties?: MolecularProperties
  pathway_annotations?: PathwayAnnotation[]
  
  // 工具集成结果
  analysis_results?: ToolAnalysisResult[]
  
  // 知识关联
  knowledge_links?: KnowledgeLink[]
  
  // 时间戳
  created_at: string
  updated_at: string
}

export interface UMLClass extends UMLElement {
  attributes: UMLAttribute[]
  operations: UMLOperation[]
  is_abstract: boolean
  visibility: string
}

export interface UMLAttribute {
  name: string
  type: string
  visibility: string
  multiplicity: string
  default_value?: any
}

export interface UMLOperation {
  name: string
  return_type: string
  parameters: UMLParameter[]
  visibility: string
  is_static: boolean
  is_abstract: boolean
}

export interface UMLParameter {
  name: string
  type: string
  direction: 'in' | 'out' | 'inout'
  default_value?: any
}

export interface UMLRelationship {
  id: string
  name?: string
  relationship_type: string
  source_id: string
  target_id: string
  multiplicity_source?: string
  multiplicity_target?: string
  properties?: Record<string, any>
}

export interface UMLDiagram {
  id: string
  name: string
  diagram_type: string
  elements: UMLElement[]
  relationships: UMLRelationship[]
  documentation: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

// SysML扩展类型
export interface ValueProperty {
  name: string
  type: string
  unit?: string
  default_value?: any
  constraints?: PropertyConstraint[]
}

export interface FlowProperty {
  name: string
  type: string
  direction: 'in' | 'out' | 'inout'
  multiplicity?: string
  protocol?: string
}

export interface ConstraintProperty {
  name: string
  expression: string
  parameters: string[]
  constraint_type: 'equality' | 'inequality' | 'optimization'
  biological_significance?: string
}

export interface PropertyConstraint {
  constraint_type: string
  constraint_value: any
  description?: string
}

export interface SysMLBlock extends UMLElement {
  // SysML特有属性
  value_properties: ValueProperty[]
  flow_properties: FlowProperty[]
  constraint_properties: ConstraintProperty[]
  part_properties?: PartProperty[]
  reference_properties?: ReferenceProperty[]
  
  // 生物医学SysML扩展
  biological_functions?: BiologicalFunction[]
  interaction_interfaces?: InteractionInterface[]
  regulatory_mechanisms?: RegulatoryMechanism[]
}

export interface PartProperty {
  name: string
  type: string
  multiplicity?: string
}

export interface ReferenceProperty {
  name: string
  type: string
  multiplicity?: string
}

export interface BiologicalFunction {
  function_id: string
  function_name: string
  go_term?: string
  mechanism: string
  regulation_level: 'transcriptional' | 'translational' | 'post_translational'
  context_dependencies?: string[]
}

export interface InteractionInterface {
  interface_name: string
  interaction_type: string
  binding_partners?: string[]
  regulatory_effects?: string[]
}

export interface RegulatoryMechanism {
  mechanism_type: string
  target_elements: string[]
  regulation_direction: 'positive' | 'negative' | 'neutral'
  conditions?: string[]
}

export interface SysMLRequirement extends UMLElement {
  text: string
  id_text: string
  requirement_type: string
  priority: string
  status: string
  verification_method: string
  
  // 生物医学需求特化
  biological_context?: Record<string, any>
  regulatory_compliance?: string[]
}

export interface PathwayAnnotation {
  pathway_id: string
  pathway_name: string
  database: string // KEGG, Reactome, etc.
  confidence_score?: number
}

// 项目和工作流类型
export interface BiomedicalProject {
  id: string
  name: string
  description: string
  project_type: string
  domain: string
  status: string
  diagrams_count: number
  models_count: number
  workflows_count: number
  knowledge_links_count: number
  created_at: string
  config: ProjectConfig
}

export interface ProjectConfig {
  name: string
  description?: string
  project_type: string
  domain: string
  data_type?: string
  size_hint?: string
}

export interface WorkflowConfig {
  analysis_type: string
  data_format: string
  input_data?: any
  target_diagram_id?: string
  update_models: boolean
  tools?: string[]
}

export interface WorkflowStatus {
  workflow_id: string
  status: 'running' | 'completed' | 'failed'
  project_id: string
  timestamp: string
  result?: any
  error?: string
  progress?: number
}

// 工具相关类型
export interface BiomedicalTool {
  name: string
  category: string
  status: string
  description: string
  performance_score?: number
  supported_formats?: string[]
  last_used?: string
  usage_count?: number
  average_execution_time?: number
}

export interface ToolRecommendation {
  type: 'element' | 'tool' | 'workflow'
  title: string
  description: string
  action: string
  params: Record<string, any>
  confidence_score?: number
}

// API请求/响应类型
export interface ProjectCreateRequest {
  name: string
  description?: string
  project_type?: string
  domain?: string
  data_type?: string
  size_hint?: string
}

export interface DiagramCreateRequest {
  name: string
  diagram_type: string
  domain?: string
}

export interface BlockCreateRequest {
  name: string
  biological_type?: string
  auto_configure?: boolean
  add_standard_properties?: boolean
  add_experimental_data?: boolean
  auto_annotate?: boolean
}

export interface WorkflowExecuteRequest {
  analysis_type: string
  data_format?: string
  input_data?: any
  target_diagram_id?: string
  update_models?: boolean
  tools?: string[]
}

export interface KnowledgeIntegrationRequest {
  document_ids: string[]
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface ProjectStatusResponse {
  project_id: string
  status: string
  config: ProjectConfig
  diagrams_count: number
  models_count: number
  workflows_count: number
  knowledge_links_count: number
}

export interface DiagramCreateResponse {
  diagram_id: string
  diagram: UMLDiagram
  recommendations: ToolRecommendation[]
}

export interface BlockCreateResponse {
  block_id: string
  block: SysMLBlock
  recommended_tools: BiomedicalTool[]
}

export interface WorkflowExecuteResponse {
  success: boolean
  workflow_id: string
  message: string
}

export interface ConstraintSolveResponse {
  block_id: string
  solution: {
    solved_parameters: Record<string, any>
    constraint_violations: any[]
    optimization_suggestions: any[]
  }
  timestamp: string
}

// 主题和样式类型
export interface ModelingTheme {
  name: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
  }
  element_styles: {
    [elementType: string]: ElementStyle
  }
}

export interface ElementStyle {
  fill_color: string
  stroke_color: string
  stroke_width: number
  font_family: string
  font_size: number
  font_color: string
}

// 事件类型
export interface ModelingEvent {
  type: string
  element_id?: string
  diagram_id?: string
  data?: any
  timestamp: string
}

// 验证类型
export interface ValidationResult {
  is_valid: boolean
  violations: ValidationViolation[]
}

export interface ValidationViolation {
  element_id?: string
  violation_type: string
  message: string
  severity: 'error' | 'warning' | 'info'
}

// 状态管理类型
export interface ModelingState {
  current_project?: BiomedicalProject
  active_diagram?: UMLDiagram
  selected_elements: string[]
  clipboard_data: any[]
  undo_stack: ModelingEvent[]
  redo_stack: ModelingEvent[]
}

export interface ToolState {
  available_tools: BiomedicalTool[]
  active_workflows: Map<string, WorkflowStatus>
  tool_categories: Record<string, BiomedicalTool[]>
}

export interface KnowledgeState {
  knowledge_links: KnowledgeLink[]
  document_cache: Map<string, any>
  search_results: any[]
} 