// 解析配置接口
export interface ParsingConfig {
  maxDepth: number
  elementLimits: {
    L1: number
    L2: number
    L3: number
  }
  enableSemanticAnalysis: boolean
  generateStableIds: boolean
  enablePerspectiveAnalysis?: boolean
  enableSemanticClustering?: boolean
  enableIntelligentRecommendations?: boolean
  enableSmartPreloading?: boolean
}

// 性能指标接口
export interface PerformanceMetrics {
  totalTime: number
  parseTime: number
  analysisTime: number
  cacheTime: number
  memoryUsage: number
  cpuUsage: number
}

// 元数据接口
export interface ParsingMetadata {
  elementCount: number
  depth: number
  fileSize: number
  processingDate: string
  version: string
}

// 解析结果接口
export interface ParsingResult {
  id: string
  filename: string
  performance: PerformanceMetrics
  metadata: ParsingMetadata
  elements: XMLElement[]
  perspectives?: PerspectiveAnalysis[]
  clusters?: SemanticCluster[]
  recommendations?: Recommendation[]
  fromCache?: boolean
  timestamp: string
}

// XML元素接口
export interface XMLElement {
  id: string
  tagName: string
  attributes: Record<string, string>
  textContent?: string
  children: XMLElement[]
  parent?: XMLElement
  namespaceURI?: string
  path: string
  lineNumber?: number
  columnNumber?: number
}

// 视角分析接口
export interface PerspectiveAnalysis {
  role: UserRole
  relevantElements: string[]
  insights: string[]
  recommendations: string[]
  confidence: number
}

// 语义聚类接口
export interface SemanticCluster {
  id: string
  name: string
  elements: string[]
  centroid?: any
  similarity?: number
  category?: string
}

// 推荐接口
export interface Recommendation {
  id: string
  type: 'optimization' | 'structure' | 'naming' | 'relationship'
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  targetElements: string[]
  suggestedAction: string
  confidence: number
}

// 用户角色类型
export type UserRole = 
  | 'system_architect'
  | 'requirements_analyst' 
  | 'behavior_analyst'
  | 'data_modeler'
  | 'test_engineer'
  | 'viewer'

// 缓存指标接口
export interface CacheMetrics {
  hitRate: number
  totalRequests: number
  hitCount: number
  missCount: number
  memoryUsage: number
  strategySwitchTime: number
}

// API回调接口
export interface ParseCallbacks {
  onProgress?: (progress: number) => void
  onStageChange?: (stage: string) => void
  onError?: (error: Error) => void
}

// API选项接口
export interface ParseOptions extends ParseCallbacks {
  enablePerspectiveAnalysis?: boolean
  enableSemanticClustering?: boolean
  enableIntelligentRecommendations?: boolean
  enableSmartPreloading?: boolean
}

// 统一API响应格式
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  message: string
  errors: string[]
  timestamp: string
  version: string
}

// XML解析和元数据相关的类型定义

export interface MetadataContext {
  sourceFile?: string
  parseTimestamp?: Date
  elementCount?: number
  attributeCount?: number
  namespaces?: string[]
  encoding?: string
  version?: string
  rootElement?: XMLElement
  elements?: XMLElement[]
  relationships?: ElementRelationship[]
  clusters?: SemanticCluster[]
  perspectives?: PerspectiveData[]
  recommendations?: RecommendationData[]
}

export interface ElementRelationship {
  id: string
  sourceId: string
  targetId: string
  type: 'parent-child' | 'reference' | 'dependency' | 'semantic'
  weight?: number
  properties?: Record<string, any>
}

export interface PerspectiveData {
  role: string
  relevantElements: string[]
  hiddenElements: string[]
  highlightedRelationships: string[]
  customViews: string[]
}

export interface RecommendationData {
  id: string
  type: 'optimization' | 'structure' | 'naming' | 'relationship'
  title: string
  description: string
  priority: 'low' | 'medium' | 'high'
  targetElements: string[]
  suggestedAction: string
  confidence: number
} 