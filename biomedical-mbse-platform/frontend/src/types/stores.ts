// Store类型定义，用于解决Pinia类型推断问题

import type { Recommendation } from './parsing'

export interface ParsingStore {
  currentFile: File | null
  parsingProgress: number
  results: any[]
  isProcessing: boolean
  cacheMetrics: any
  performanceStats: {
    preloadHitRate: number
    throughput: number
    cacheHitRate: number
    strategySwitchTime: number
    surfaceProcessingSpeed: number
    recommendationGenerationRate: number
  }
  latestResult: any
  processingTime: number
  elementCount: number
  cacheEfficiency: number
  parseFile: (file: File, config: any) => Promise<any>
  getParsingHistory: () => Promise<any>
  clearCache: () => void
}

export interface RecommendationStore {
  recommendations: Recommendation[]
  loading: boolean
  error: string | null
  activeRecommendations: Recommendation[]
  recommendationsByPriority: {
    high: Recommendation[]
    medium: Recommendation[]
    low: Recommendation[]
  }
  totalRecommendations: number
  fetchRecommendations: () => Promise<void>
  generateRecommendations: (options?: {
    role?: string
    includePerformance?: boolean
    includeOptimization?: boolean
  }) => Promise<void>
  applyRecommendation: (id: string) => Promise<void>
  dismissRecommendation: (id: string) => Promise<void>
  addRecommendation: (recommendation: Omit<Recommendation, 'id'>) => void
}

export interface ConnectionStore {
  connections: any[]
  loading: boolean
  error: string | null
  connectionStats: {
    total: number
    connected: number
    error: number
    avgResponseTime: number
  }
  activeConnections: any[]
  failedConnections: any[]
  fetchConnections: () => Promise<void>
  analyzeConnections: (role?: string) => Promise<void>
  testConnection: (id: string) => Promise<void>
  addConnection: (connection: any) => void
  removeConnection: (id: string) => void
  updateConnection: (id: string, updates: any) => void
} 