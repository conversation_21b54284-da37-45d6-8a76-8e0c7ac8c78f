// 视图系统核心类型定义

import type { UserRole, XMLElement, ParsingResult } from './parsing'

// ====== 核心元数据类型 ======

/**
 * 元数据上下文 - 所有视图的数据基础
 */
export interface MetadataContext {
  elements: XMLElement[]
  parsingResult: ParsingResult
  relationships: ElementRelationship[]
  semanticClusters: SemanticCluster[]
  perspectives: PerspectiveData[]
  recommendations: RecommendationData[]
}

/**
 * 元素关系映射
 */
export interface ElementRelationship {
  source: string
  target: string
  type: 'parent-child' | 'reference' | 'dependency' | 'semantic' | 'custom'
  weight: number
  metadata: Record<string, any>
}

/**
 * 语义聚类数据
 */
export interface SemanticCluster {
  id: string
  name: string
  elements: string[]
  centroid: number[]
  confidence: number
  semanticDomain: string
  subClusters?: SemanticCluster[]
}

/**
 * 视角数据
 */
export interface PerspectiveData {
  role: UserRole
  focusElements: string[]
  relevanceScores: Record<string, number>
  insights: string[]
  customFilters: FilterRule[]
}

/**
 * 推荐数据
 */
export interface RecommendationData {
  id: string
  type: 'optimization' | 'pattern' | 'anomaly' | 'insight'
  targetElements: string[]
  confidence: number
  actionable: boolean
  metadata: Record<string, any>
}

// ====== 视图配置系统 ======

/**
 * 视图配置 - 高度可定制的视图定义
 */
export interface ViewConfig {
  id: string
  name: string
  description?: string
  type: ViewType
  layout: LayoutConfig
  components: ComponentConfig[]
  filters: FilterConfig[]
  interactions?: InteractionConfig[]
  customizations?: CustomizationConfig
  metadata?: ViewMetadata
}

/**
 * 视图类型枚举
 */
export type ViewType = 
  | 'dashboard'      // 仪表板视图
  | 'hierarchy'      // 层次结构视图
  | 'network'        // 网络关系视图
  | 'timeline'       // 时间线视图
  | 'spatial'        // 空间/3D视图
  | 'analytical'     // 分析报告视图
  | 'comparative'    // 对比分析视图
  | 'custom'         // 自定义视图

/**
 * 布局配置
 */
export interface LayoutConfig {
  type: 'grid' | 'flex' | 'absolute' | 'canvas' | 'custom'
  responsive?: boolean
  breakpoints?: Record<string, LayoutBreakpoint>
  gridConfig?: GridLayoutConfig
  flexConfig?: FlexLayoutConfig
  canvasConfig?: CanvasLayoutConfig
}

export interface LayoutBreakpoint {
  columns: number
  rows?: number
  gap: number
  padding: number
}

export interface GridLayoutConfig {
  columns: number
  rows: number
  gap: number
  autoResize: boolean
}

export interface FlexLayoutConfig {
  direction: 'row' | 'column'
  wrap: boolean
  justify: 'start' | 'center' | 'end' | 'between' | 'around'
  align: 'start' | 'center' | 'end' | 'stretch'
}

export interface CanvasLayoutConfig {
  width: number
  height: number
  zoom: boolean
  pan: boolean
  minimap: boolean
}

// ====== 组件配置系统 ======

/**
 * 组件配置 - 基于元数据的可定制组件
 */
export interface ComponentConfig {
  id: string
  type: ComponentType
  name: string
  description?: string
  category: string
  props?: Record<string, any>
  position?: {
    x: number
    y: number
    width: number
    height: number
  }
  styling?: ComponentStyling
  dataBinding?: DataBinding
  customProps?: {
    userCustomizable?: boolean
    [key: string]: any
  }
}

/**
 * 组件类型
 */
export type ComponentType =
  // 数据展示组件
  | 'element-tree'       // 元素树
  | 'element-list'       // 元素列表
  | 'element-card'       // 元素卡片
  | 'element-table'      // 元素表格
  
  // 关系可视化组件
  | 'relationship-graph' // 关系图
  | 'hierarchy-tree'     // 层次树
  | 'network-diagram'    // 网络图
  | 'connection-matrix'  // 连接矩阵
  
  // 分析组件
  | 'semantic-cluster'   // 语义聚类
  | 'perspective-view'   // 视角视图
  | 'recommendation-panel' // 推荐面板
  | 'insight-summary'    // 洞察摘要
  
  // 图表组件
  | 'bar-chart'          // 柱状图
  | 'line-chart'         // 折线图
  | 'pie-chart'          // 饼图
  | 'scatter-plot'       // 散点图
  | 'heatmap'            // 热力图
  
  // 3D组件
  | '3d-scene'           // 3D场景
  | '3d-model'           // 3D模型
  | '3d-network'         // 3D网络
  
  // 交互组件
  | 'filter-panel'       // 过滤面板
  | 'search-box'         // 搜索框
  | 'control-panel'      // 控制面板
  | 'toolbar'            // 工具栏
  
  // UML核心组件
  | 'uml-class-node'     // UML类节点
  | 'uml-interface-node' // UML接口节点
  | 'uml-inheritance-edge' // UML继承关系
  | 'uml-association-edge' // UML关联关系
  | 'uml-actor-node'     // UML参与者节点
  | 'uml-usecase-node'   // UML用例节点
  | 'uml-system-boundary' // UML系统边界
  | 'uml-diagram-toolbar' // UML图工具栏
  | 'uml-sequence-diagram' // UML序列图
  | 'uml-activity-flow'  // UML活动流
  | 'uml-state-machine'  // UML状态机
  | 'uml-communication-diagram' // UML通信图
  | 'uml-component-diagram' // UML组件图
  | 'uml-deployment-diagram' // UML部署图
  | 'uml-package-diagram' // UML包图
  | 'uml-object-diagram' // UML对象图
  | 'uml-profile-diagram' // UML配置文件图
  
  // SysML核心组件
  | 'sysml-block-node'   // SysML块节点
  | 'sysml-requirement-tree' // SysML需求树
  | 'sysml-requirement-relation' // SysML需求关系
  | 'sysml-requirement-properties-panel' // SysML需求属性面板
  | 'sysml-composition-edge' // SysML组合关系
  | 'sysml-generalization-edge' // SysML泛化关系
  | 'sysml-internal-block-diagram' // SysML内部块图
  | 'sysml-parametric-diagram' // SysML参数图
  | 'sysml-activity-diagram' // SysML活动图
  | 'sysml-sequence-diagram' // SysML序列图
  | 'sysml-state-machine' // SysML状态机
  | 'sysml-constraint-block' // SysML约束块
  | 'sysml-port-node'    // SysML端口节点
  | 'sysml-connector-edge' // SysML连接器
  | 'sysml-allocation-diagram' // SysML分配图
  
  // 自定义组件
  | 'custom'             // 自定义组件

/**
 * 组件位置
 */
export interface ComponentPosition {
  x: number
  y: number
  z?: number
  anchor: 'top-left' | 'top-center' | 'top-right' | 'center' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  relative: boolean
}

/**
 * 组件尺寸
 */
export interface ComponentSize {
  width: number | 'auto' | string
  height: number | 'auto' | string
  minWidth?: number
  minHeight?: number
  maxWidth?: number
  maxHeight?: number
  aspectRatio?: number
  resizable: boolean
}

/**
 * 数据绑定配置
 */
export interface DataBinding {
  source: DataSource
  filters: FilterRule[]
  transformations: DataTransformation[]
  aggregations: DataAggregation[]
  refresh: RefreshConfig
}

/**
 * 数据源配置
 */
export interface DataSource {
  type: 'elements' | 'relationships' | 'clusters' | 'perspectives' | 'recommendations' | 'custom'
  query?: QueryConfig
  cache: boolean
  realtime: boolean
}

/**
 * 查询配置
 */
export interface QueryConfig {
  filter?: Record<string, any>
  sort?: SortConfig[]
  limit?: number
  offset?: number
  includes?: string[]
  excludes?: string[]
}

/**
 * 排序配置
 */
export interface SortConfig {
  field: string
  order: 'asc' | 'desc'
  type: 'string' | 'number' | 'date' | 'custom'
}

/**
 * 数据转换
 */
export interface DataTransformation {
  type: 'map' | 'filter' | 'reduce' | 'group' | 'flatten' | 'custom'
  config: Record<string, any>
  order: number
}

/**
 * 数据聚合
 */
export interface DataAggregation {
  field: string
  operation: 'count' | 'sum' | 'avg' | 'min' | 'max' | 'custom'
  groupBy?: string[]
}

/**
 * 刷新配置
 */
export interface RefreshConfig {
  auto: boolean
  interval?: number
  triggers: RefreshTrigger[]
}

/**
 * 刷新触发器
 */
export type RefreshTrigger = 
  | 'data-change'
  | 'perspective-change'
  | 'filter-change'
  | 'user-action'
  | 'time-interval'
  | 'selection-change'

// ====== 过滤系统 ======

/**
 * 过滤配置
 */
export interface FilterConfig {
  id: string
  name: string
  type: FilterType
  rules: FilterRule[]
  logic: 'AND' | 'OR' | 'CUSTOM'
  active: boolean
  userConfigurable: boolean
}

/**
 * 过滤类型
 */
export type FilterType = 
  | 'perspective'     // 基于视角的过滤
  | 'semantic'        // 基于语义的过滤
  | 'structural'      // 基于结构的过滤
  | 'attribute'       // 基于属性的过滤
  | 'relationship'    // 基于关系的过滤
  | 'recommendation'  // 基于推荐的过滤
  | 'custom'          // 自定义过滤

/**
 * 过滤规则
 */
export interface FilterRule {
  id?: string
  name?: string
  type?: FilterType
  field: string
  operator: FilterOperator
  value: any
  logic?: 'AND' | 'OR'
  active?: boolean
  userConfigurable?: boolean
  metadata?: Record<string, any>
}

/**
 * 过滤操作符
 */
export type FilterOperator = 
  | 'equals' | 'not-equals'
  | 'contains' | 'not-contains'
  | 'starts-with' | 'ends-with'
  | 'greater-than' | 'less-than'
  | 'greater-equal' | 'less-equal'
  | 'in' | 'not-in'
  | 'exists' | 'not-exists'
  | 'matches' | 'not-matches'
  | 'custom'

// ====== 交互系统 ======

/**
 * 交互配置
 */
export interface InteractionConfig {
  id: string
  type: InteractionType
  trigger: InteractionTrigger
  target: InteractionTarget
  action: InteractionAction
  conditions: InteractionCondition[]
  metadata: Record<string, any>
}

/**
 * 交互类型
 */
export type InteractionType = 
  | 'selection'       // 选择交互
  | 'hover'           // 悬停交互
  | 'click'           // 点击交互
  | 'drag'            // 拖拽交互
  | 'zoom'            // 缩放交互
  | 'pan'             // 平移交互
  | 'filter'          // 过滤交互
  | 'navigate'        // 导航交互
  | 'custom'          // 自定义交互

/**
 * 交互触发器
 */
export interface InteractionTrigger {
  event: string
  modifiers?: string[]
  debounce?: number
  throttle?: number
}

/**
 * 交互目标
 */
export interface InteractionTarget {
  type: 'element' | 'component' | 'view' | 'global'
  selector?: string
  multiple: boolean
}

/**
 * 交互动作
 */
export interface InteractionAction {
  type: 'highlight' | 'select' | 'navigate' | 'filter' | 'transform' | 'custom'
  config: Record<string, any>
  async: boolean
}

/**
 * 交互条件
 */
export interface InteractionCondition {
  type: 'permission' | 'state' | 'data' | 'custom'
  check: string | Function
  invert: boolean
}

// ====== 样式系统 ======

/**
 * 组件样式配置
 */
export interface ComponentStyling {
  baseStyles: Record<string, string>
  stateStyles: Record<ComponentState, Record<string, string>>
  customClasses: string[]
  theme?: string
}

/**
 * 组件状态
 */
export type ComponentState = 
  | 'default'
  | 'error'
  | 'custom'
  | 'hover'
  | 'active'
  | 'disabled'
  | 'loading'
  | 'selected'

// ====== 行为系统 ======

/**
 * 组件行为配置
 */
export interface ComponentBehavior {
  lifecycle: LifecycleHooks
  events: EventHandlers
  animations: AnimationConfig[]
  validation: ValidationConfig[]
  caching: CachingConfig
}

/**
 * 生命周期钩子
 */
export interface LifecycleHooks {
  onMount?: Function
  onUnmount?: Function
  onUpdate?: Function
  onDataChange?: Function
  onResize?: Function
  onError?: Function
}

/**
 * 事件处理器
 */
export interface EventHandlers {
  [eventName: string]: Function | Function[]
}

/**
 * 动画配置
 */
export interface AnimationConfig {
  name: string
  type: 'transition' | 'keyframe' | 'physics' | 'custom'
  duration: number
  delay?: number
  easing?: string
  trigger: AnimationTrigger
  properties: Record<string, any>
}

/**
 * 动画触发器
 */
export type AnimationTrigger = 
  | 'mount'
  | 'unmount'
  | 'data-change'
  | 'state-change'
  | 'user-interaction'
  | 'custom'

/**
 * 验证配置
 */
export interface ValidationConfig {
  field: string
  rules: ValidationRule[]
  message?: string
}

/**
 * 验证规则
 */
export interface ValidationRule {
  type: 'required' | 'pattern' | 'range' | 'custom'
  value?: any
  message?: string
}

/**
 * 缓存配置
 */
export interface CachingConfig {
  enabled: boolean
  ttl?: number
  strategy: 'memory' | 'session' | 'local' | 'custom'
  invalidation: CacheInvalidation[]
}

/**
 * 缓存失效配置
 */
export interface CacheInvalidation {
  trigger: 'data-change' | 'time' | 'manual' | 'custom'
  condition?: string | Function
}

// ====== 定制化系统 ======

/**
 * 定制化配置
 */
export interface CustomizationConfig {
  userCustomizable: boolean
  customizableProps: CustomizableProperty[]
  presets: CustomizationPreset[]
  restrictions: CustomizationRestriction[]
}

/**
 * 可定制属性
 */
export interface CustomizableProperty {
  path: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  default: any
  options?: any[]
  validation?: ValidationRule[]
  description?: string
}

/**
 * 定制预设
 */
export interface CustomizationPreset {
  id: string
  name: string
  description: string
  config: Record<string, any>
  tags: string[]
}

/**
 * 定制限制
 */
export interface CustomizationRestriction {
  property: string
  restriction: 'readonly' | 'hidden' | 'limited'
  condition?: string | Function
  reason?: string
}

// ====== 视图元数据 ======

/**
 * 视图元数据
 */
export interface ViewMetadata {
  author: string
  version: string
  created: string
  modified: string
  tags: string[]
  category: string
  permissions: Permission[]
  dependencies: string[]
  compatibility: CompatibilityInfo
}

/**
 * 权限配置
 */
export interface Permission {
  role: UserRole | 'all'
  actions: PermissionAction[]
}

/**
 * 权限动作
 */
export type PermissionAction = 
  | 'view'
  | 'edit'
  | 'customize'
  | 'share'
  | 'export'
  | 'delete'

/**
 * 兼容性信息
 */
export interface CompatibilityInfo {
  minVersion: string
  maxVersion?: string
  requiredFeatures: string[]
  optionalFeatures: string[]
}

// ViewEngine接口
export interface ViewEngine {
  setMetadataContext(context: any): void
  setCurrentRole(role: string): void
  activateView(viewId: string): Promise<void>
  getActiveView(): ViewConfig | null
  createComponent(config: ComponentConfig): any
  on(event: string, handler: Function): void
  off(event: string, handler?: Function): void
  emit(event: string, ...args: any[]): void
}

export interface SystemComponent {
  id: string
  name: string
  type: 'service' | 'data' | 'ui' | 'infrastructure'
  layer: 'presentation' | 'business' | 'data' | 'infrastructure'
  techSpecs: Array<{ name: string; value: string }>
  qualityAttrs: Array<{ name: string; rating: number }>
  x?: number
  y?: number
  width?: number
  height?: number
  status?: 'active' | 'inactive' | 'error'
}

export interface LayerConfig {
  id: string
  name: string
  icon: string
  components: number
  interfaces: number
  color?: string
  visible?: boolean
}

export interface ConnectionConfig {
  id: string
  source: string
  target: string
  type: string
  weight?: number
  status?: 'active' | 'inactive' | 'error'
  properties?: Record<string, any>
} 