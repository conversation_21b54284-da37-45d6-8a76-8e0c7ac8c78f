/// <reference lib="es2015" />
/// <reference lib="es2016" />
/// <reference lib="es2017" />
/// <reference lib="dom" />

// 首先声明Promise全局类型
declare global {
  // Promise类型声明
  interface PromiseConstructor {
    new <T>(executor: (resolve: (value: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void): Promise<T>
    resolve<T>(value: T | PromiseLike<T>): Promise<T>
    resolve(): Promise<void>
    reject<T = never>(reason?: any): Promise<T>
    all<T>(values: readonly (T | PromiseLike<T>)[]): Promise<T[]>
    race<T>(values: readonly (T | PromiseLike<T>)[]): Promise<T>
  }
  
  const Promise: PromiseConstructor
  
  interface Promise<T> {
    then<TResult1 = T, TResult2 = never>(
      onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null,
      onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null
    ): Promise<TResult1 | TResult2>
    catch<TResult = never>(
      onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null
    ): Promise<T | TResult>
    finally(onfinally?: (() => void) | undefined | null): Promise<T>
  }
}

// Vue类型声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// Pinia类型声明
declare module 'pinia' {
  export interface DefineStoreOptions<Id extends string, S, G, A> {
    id: Id
    state?: () => S
    getters?: G
    actions?: A
  }
  
  export interface Store<Id extends string = string, S = {}, G = {}, A = {}> {
    $id: Id
    $state: S
  }
  
  export function defineStore<Id extends string, S, G, A>(
    id: Id,
    setup: () => any,
    options?: DefineStoreOptions<Id, S, G, A>
  ): () => Store<Id, S, G, A>
  
  export function defineStore<Id extends string, S, G, A>(
    options: DefineStoreOptions<Id, S, G, A> & { id: Id }
  ): () => Store<Id, S, G, A>
}

// Vue核心类型声明
declare module 'vue' {
  export interface Ref<T = any> {
    value: T
  }
  
  export interface ComputedRef<T = any> extends Ref<T> {
    readonly value: T
  }
  
  export interface WritableComputedRef<T = any> extends Ref<T> {
    value: T
  }
  
  export function ref<T = any>(value?: T): Ref<T>
  export function reactive<T extends object>(target: T): T
  export function computed<T>(getter: () => T): ComputedRef<T>
  export function computed<T>(options: {
    get: () => T
    set: (value: T) => void
  }): WritableComputedRef<T>
  export function readonly<T>(target: T): T
  export function onMounted(hook: () => void): void
  export function onUnmounted(hook: () => void): void
  export function watch<T>(
    source: () => T,
    callback: (newValue: T, oldValue: T) => void,
    options?: { immediate?: boolean; deep?: boolean }
  ): void
  export function nextTick(callback?: () => void): Promise<void>
}

// ECharts类型声明
declare module 'echarts' {
  export interface EChartsOption {
    [key: string]: any
  }
  
  export interface ECharts {
    setOption(option: EChartsOption, notMerge?: boolean, silent?: boolean): void
    resize(): void
    dispose(): void
    on(eventName: string, handler: Function): void
    off(eventName: string, handler?: Function): void
  }
  
  export function init(dom: HTMLElement, theme?: string): ECharts
  export function dispose(target: HTMLElement | ECharts): void
  export function registerTheme(name: string, theme: any): void
  export const graphic: any
  export const color: any
}

// Element Plus类型声明
declare module 'element-plus' {
  export interface MessageOptions {
    message?: string
    type?: 'success' | 'warning' | 'info' | 'error'
    showClose?: boolean
    duration?: number
  }

  export interface MessageBoxOptions {
    title?: string
    message?: string
    type?: 'success' | 'warning' | 'info' | 'error'
    confirmButtonText?: string
    cancelButtonText?: string
    showCancelButton?: boolean
    beforeClose?: (action: string, instance: any, done: () => void) => void
  }

  export interface UploadFile {
    name: string
    size?: number
    type?: string
    raw?: File
    uid?: number
    url?: string
    status?: 'ready' | 'uploading' | 'success' | 'fail'
  }

  export const ElMessage: {
    (message: string): void
    (options: MessageOptions): void
    success: (message: string) => void
    error: (message: string) => void
    warning: (message: string) => void
    info: (message: string) => void
    confirm: (message: string) => Promise<any>
  }
  
  export const ElMessageBox: {
    alert: (message: string, title?: string, options?: MessageBoxOptions) => Promise<any>
    confirm: (message: string, title?: string, options?: MessageBoxOptions) => Promise<any>
    prompt: (message: string, title?: string, options?: MessageBoxOptions) => Promise<any>
  }
  
  export const ElButton: any
  export const ElProgress: any
  export const ElIcon: any
  export const ElInput: any
  export const ElCard: any
  export const ElDialog: any
  export const ElForm: any
  export const ElFormItem: any
  export const ElSelect: any
  export const ElOption: any
  export const ElInputNumber: any
  export const ElRate: any
  export const ElRow: any
  export const ElCol: any
  export const ElCollapse: any
  export const ElCollapseItem: any
  export const ElTabs: any
  export const ElTabPane: any
  export const ElTimeline: any
  export const ElTimelineItem: any
  export const ElUpload: any
  export const ElTable: any
  export const ElTableColumn: any
  export const ElTag: any
  export const ElCheckbox: any
  export const ElCheckboxGroup: any
  export const ElSlider: any
}

// Element Plus图标类型声明
declare module '@element-plus/icons-vue' {
  // 基础操作图标
  export const Edit: any
  export const Refresh: any
  export const Check: any
  export const Filter: any
  export const FullScreen: any
  export const ZoomIn: any
  export const ZoomOut: any
  export const Search: any
  export const Download: any
  export const Setting: any
  export const Close: any
  export const Grid: any
  export const Delete: any
  export const Plus: any
  export const UploadFilled: any
  export const DocumentAdd: any
  
  // 文件和文档图标
  export const Files: any
  export const FolderOpened: any
  export const Document: any
  export const Notebook: any
  export const Memo: any
  export const Collection: any
  export const Tickets: any
  
  // 导航和箭头图标
  export const ArrowLeft: any
  export const ArrowRight: any
  export const ArrowUp: any
  export const ArrowDown: any
  export const Top: any
  export const Bottom: any
  export const Back: any
  export const Right: any
  
  // 状态和指示器图标
  export const CircleCheckFilled: any
  export const SuccessFilled: any
  export const WarningFilled: any
  export const InfoFilled: any
  export const QuestionFilled: any
  export const CirclePlus: any
  export const CirclePlusFilled: any
  
  // 媒体和设备图标
  export const VideoCamera: any
  export const Camera: any
  export const Microphone: any
  export const Monitor: any
  export const Phone: any
  export const Cellphone: any
  
  // 工具和设置图标
  export const Tools: any
  export const Operation: any
  export const SetUp: any
  export const Management: any
  export const Service: any
  export const Switch: any
  
  // 数据和图表图标
  export const TrendCharts: any
  export const DataLine: any
  export const PieChart: any
  export const List: any
  export const Reading: any
  export const Checked: any
  
  // 位置和地图图标
  export const Location: any
  export const Position: any
  export const MapLocation: any
  export const Place: any
  export const Coordinate: any
  
  // 用户和身份图标
  export const User: any
  export const UserFilled: any
  export const Avatar: any
  export const House: any
  export const HomeFilled: any
  
  // 通信和消息图标
  export const ChatRound: any
  export const ChatSquare: any
  export const Message: any
  export const Comment: any
  export const Bell: any
  export const BellFilled: any
  
  // 日期和时间图标
  export const Calendar: any
  export const Timer: any
  export const AlarmClock: any
  export const Watch: any
  
  // 商业和金融图标
  export const Money: any
  export const CreditCard: any
  export const Wallet: any
  export const ShoppingCart: any
  export const Goods: any
  export const Trophy: any
  
  // 其他常用图标
  export const Key: any
  export const Lock: any
  export const Unlock: any
  export const Help: any
  export const HelpFilled: any
  export const Star: any
  export const StarFilled: any
  export const Flag: any
  export const Menu: any
  export const More: any
  export const MoreFilled: any
  export const Open: any
  export const View: any
}

// Global类型扩展
declare global {
  interface Window {
    __APP_VERSION__?: string
    __BUILD_TIME__?: string
  }
  
  // Set类型声明
  interface SetConstructor {
    new <T = any>(values?: readonly T[] | null): Set<T>
  }
  
  interface Set<T> {
    add(value: T): this
    clear(): void
    delete(value: T): boolean
    has(value: T): boolean
    readonly size: number
    values(): IterableIterator<T>
    keys(): IterableIterator<T>
    entries(): IterableIterator<[T, T]>
    forEach(callbackfn: (value: T, value2: T, set: Set<T>) => void, thisArg?: any): void
  }
  
  const Set: SetConstructor
  
  // Map类型声明
  interface MapConstructor {
    new <K = any, V = any>(entries?: readonly (readonly [K, V])[] | null): Map<K, V>
  }
  
  interface Map<K, V> {
    clear(): void
    delete(key: K): boolean
    get(key: K): V | undefined
    has(key: K): boolean
    set(key: K, value: V): this
    readonly size: number
    keys(): IterableIterator<K>
    values(): IterableIterator<V>
    entries(): IterableIterator<[K, V]>
    forEach(callbackfn: (value: V, key: K, map: Map<K, V>) => void, thisArg?: any): void
  }
  
  const Map: MapConstructor
  
  // Array扩展方法
  interface ArrayConstructor {
    from<T>(arrayLike: ArrayLike<T>): T[]
    from<T, U>(arrayLike: ArrayLike<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[]
    from<T>(iterable: Iterable<T>): T[]
    from<T, U>(iterable: Iterable<T>, mapfn: (v: T, k: number) => U, thisArg?: any): U[]
  }
  
  interface Array<T> {
    includes(searchElement: T, fromIndex?: number): boolean
    find<S extends T>(predicate: (this: void, value: T, index: number, obj: T[]) => value is S, thisArg?: any): S | undefined
    find(predicate: (value: T, index: number, obj: T[]) => unknown, thisArg?: any): T | undefined
    findIndex(predicate: (value: T, index: number, obj: T[]) => unknown, thisArg?: any): number
  }
  
  // Object扩展方法
  interface ObjectConstructor {
    assign<T, U>(target: T, source: U): T & U
    assign<T, U, V>(target: T, source1: U, source2: V): T & U & V
    assign<T, U, V, W>(target: T, source1: U, source2: V, source3: W): T & U & V & W
    assign(target: object, ...sources: any[]): any
  }
  
  // String扩展方法
  interface String {
    padStart(targetLength: number, padString?: string): string
    padEnd(targetLength: number, padString?: string): string
  }
  
  // Node.js全局类型
  const process: {
    cwd(): string
    env: Record<string, string>
  }
  
  // NodeJS命名空间
  namespace NodeJS {
    interface Timeout {
      ref(): this
      unref(): this
    }
    
    interface Timer {
      ref(): this
      unref(): this
    }
  }
} 