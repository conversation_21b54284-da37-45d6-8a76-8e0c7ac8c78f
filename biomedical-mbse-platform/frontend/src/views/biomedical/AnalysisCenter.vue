<template>
  <div class="analysis-center">
    <div class="page-header">
      <h1>分析中心</h1>
      <p>生物医学数据分析与可视化平台</p>
    </div>

    <div class="analysis-layout">
      <!-- 分析工具栏 -->
      <div class="toolbar">
        <el-card class="toolbar-card">
          <div class="toolbar-content">
            <div class="tool-group">
              <span class="group-label">数据源:</span>
              <el-select v-model="selectedDataSource" placeholder="选择数据源">
                <el-option label="项目数据" value="project" />
                <el-option label="公共数据库" value="database" />
                <el-option label="上传文件" value="upload" />
              </el-select>
            </div>
            
            <div class="tool-group">
              <span class="group-label">分析类型:</span>
              <el-select v-model="selectedAnalysisType" placeholder="选择分析类型">
                <el-option label="统计分析" value="statistics" />
                <el-option label="机器学习" value="ml" />
                <el-option label="生物信息学" value="bioinformatics" />
                <el-option label="图像分析" value="imaging" />
              </el-select>
            </div>
            
            <div class="tool-group">
              <el-button type="primary" @click="startAnalysis" :loading="isAnalyzing">
                <el-icon><DataAnalysis /></el-icon>
                开始分析
              </el-button>
              <el-button @click="exportResults" :disabled="!analysisResults">
                <el-icon><Download /></el-icon>
                导出结果
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <el-row :gutter="20">
          <!-- 左侧：数据预览和参数设置 -->
          <el-col :span="8">
            <div class="left-section">
              <!-- 数据预览 -->
              <el-card class="data-preview-card">
                <template #header>
                  <div class="card-header">
                    <span>数据预览</span>
                    <el-button size="small" @click="refreshData">
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </div>
                </template>
                
                <div class="data-preview">
                  <el-table :data="previewData" size="small" max-height="300">
                    <el-table-column v-for="column in dataColumns" 
                                     :key="column.prop"
                                     :prop="column.prop" 
                                     :label="column.label"
                                     show-overflow-tooltip />
                  </el-table>
                  
                  <div class="data-summary">
                    <p>总记录数: {{ totalRecords }}</p>
                    <p>数据类型: {{ dataType }}</p>
                  </div>
                </div>
              </el-card>

              <!-- 分析参数 -->
              <el-card class="parameters-card">
                <template #header>
                  <span>分析参数</span>
                </template>
                
                <div class="parameters-content">
                  <el-form :model="analysisParams" label-width="100px" size="small">
                    <el-form-item label="置信度">
                      <el-slider v-model="analysisParams.confidence" :min="0.8" :max="0.99" :step="0.01" />
                    </el-form-item>
                    
                    <el-form-item label="样本大小">
                      <el-input-number v-model="analysisParams.sampleSize" :min="10" :max="10000" />
                    </el-form-item>
                    
                    <el-form-item label="算法选择">
                      <el-select v-model="analysisParams.algorithm" style="width: 100%">
                        <el-option label="线性回归" value="linear" />
                        <el-option label="随机森林" value="rf" />
                        <el-option label="支持向量机" value="svm" />
                        <el-option label="神经网络" value="nn" />
                      </el-select>
                    </el-form-item>
                    
                    <el-form-item label="交叉验证">
                      <el-switch v-model="analysisParams.crossValidation" />
                    </el-form-item>
                  </el-form>
                </div>
              </el-card>
            </div>
          </el-col>

          <!-- 右侧：结果展示 -->
          <el-col :span="16">
            <div class="results-section">
              <!-- 分析结果 -->
              <el-card class="results-card">
                <template #header>
                  <div class="card-header">
                    <span>分析结果</span>
                    <div class="result-actions">
                      <el-tag v-if="analysisResults" type="success">分析完成</el-tag>
                      <el-button size="small" @click="showDetailedResults = !showDetailedResults">
                        详细结果
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div v-if="isAnalyzing" class="analyzing-status">
                  <el-progress :percentage="analysisProgress" />
                  <p>正在进行{{ currentAnalysisStep }}...</p>
                </div>
                
                <div v-else-if="analysisResults" class="results-content">
                  <!-- 结果概览 -->
                  <div class="results-overview">
                    <el-row :gutter="16">
                      <el-col :span="6">
                        <div class="metric-card">
                          <div class="metric-value">{{ analysisResults.accuracy }}%</div>
                          <div class="metric-label">准确率</div>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <div class="metric-card">
                          <div class="metric-value">{{ analysisResults.pValue }}</div>
                          <div class="metric-label">P值</div>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <div class="metric-card">
                          <div class="metric-value">{{ analysisResults.sampleSize }}</div>
                          <div class="metric-label">样本数</div>
                        </div>
                      </el-col>
                      <el-col :span="6">
                        <div class="metric-card">
                          <div class="metric-value">{{ analysisResults.runtime }}s</div>
                          <div class="metric-label">运行时间</div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 图表展示 -->
                  <div class="charts-container">
                    <el-tabs v-model="activeChart">
                      <el-tab-pane label="散点图" name="scatter">
                        <div class="chart-placeholder">
                          <el-icon class="chart-icon"><TrendCharts /></el-icon>
                          <p>散点图可视化</p>
                          <p class="chart-desc">显示数据点之间的相关性</p>
                        </div>
                      </el-tab-pane>
                      
                      <el-tab-pane label="热力图" name="heatmap">
                        <div class="chart-placeholder">
                          <el-icon class="chart-icon"><Grid /></el-icon>
                          <p>热力图可视化</p>
                          <p class="chart-desc">显示数据密度分布</p>
                        </div>
                      </el-tab-pane>
                      
                      <el-tab-pane label="网络图" name="network">
                        <div class="chart-placeholder">
                          <el-icon class="chart-icon"><Share /></el-icon>
                          <p>网络图可视化</p>
                          <p class="chart-desc">显示节点间的关系网络</p>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </div>

                  <!-- 详细结果 -->
                  <div v-if="showDetailedResults" class="detailed-results">
                    <h4>详细分析报告</h4>
                    <el-table :data="detailedResults" size="small">
                      <el-table-column prop="parameter" label="参数" />
                      <el-table-column prop="value" label="值" />
                      <el-table-column prop="confidence" label="置信区间" />
                      <el-table-column prop="significance" label="显著性" />
                    </el-table>
                  </div>
                </div>
                
                <div v-else class="no-results">
                  <el-icon class="no-results-icon"><DataBoard /></el-icon>
                  <p>暂无分析结果</p>
                  <p class="no-results-desc">请选择数据源和分析类型，然后点击"开始分析"</p>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 历史记录面板 -->
    <div class="history-section">
      <el-card class="history-card">
        <template #header>
          <span>分析历史</span>
        </template>
        
        <el-table :data="analysisHistory" size="small">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="分析名称" />
          <el-table-column prop="type" label="类型" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ getAnalysisTypeName(row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button size="small" @click="viewAnalysis(row)">查看</el-button>
              <el-button size="small" @click="rerunAnalysis(row)">重新运行</el-button>
              <el-button size="small" type="danger" @click="deleteAnalysis(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const selectedDataSource = ref('')
const selectedAnalysisType = ref('')
const isAnalyzing = ref(false)
const analysisProgress = ref(0)
const currentAnalysisStep = ref('数据预处理')
const analysisResults = ref(null)
const showDetailedResults = ref(false)
const activeChart = ref('scatter')

// 数据预览
const previewData = ref([
  { gene_id: 'GENE001', expression: 2.45, condition: 'Control', tissue: 'Brain' },
  { gene_id: 'GENE002', expression: 1.89, condition: 'Treatment', tissue: 'Brain' },
  { gene_id: 'GENE003', expression: 3.12, condition: 'Control', tissue: 'Liver' },
  { gene_id: 'GENE004', expression: 0.78, condition: 'Treatment', tissue: 'Liver' },
  { gene_id: 'GENE005', expression: 4.56, condition: 'Control', tissue: 'Heart' }
])

const dataColumns = ref([
  { prop: 'gene_id', label: '基因ID' },
  { prop: 'expression', label: '表达量' },
  { prop: 'condition', label: '条件' },
  { prop: 'tissue', label: '组织' }
])

const totalRecords = ref(15432)
const dataType = ref('基因表达数据')

// 分析参数
const analysisParams = reactive({
  confidence: 0.95,
  sampleSize: 1000,
  algorithm: 'rf',
  crossValidation: true
})

// 详细结果
const detailedResults = ref([
  { parameter: '回归系数', value: '0.756', confidence: '[0.612, 0.901]', significance: '***' },
  { parameter: 'R²', value: '0.834', confidence: '[0.789, 0.878]', significance: '***' },
  { parameter: 'F统计量', value: '247.56', confidence: '-', significance: '***' },
  { parameter: 'AIC', value: '1245.67', confidence: '-', significance: '-' }
])

// 分析历史
const analysisHistory = ref([
  {
    id: 'A001',
    name: '基因表达差异分析',
    type: 'statistics',
    status: 'completed',
    created_at: '2025-01-15 14:30:25'
  },
  {
    id: 'A002',
    name: '蛋白质结构预测',
    type: 'ml',
    status: 'running',
    created_at: '2025-01-15 15:45:12'
  },
  {
    id: 'A003',
    name: '序列比对分析',
    type: 'bioinformatics',
    status: 'failed',
    created_at: '2025-01-15 16:20:08'
  }
])

// 方法
const startAnalysis = async () => {
  if (!selectedDataSource.value || !selectedAnalysisType.value) {
    ElMessage.warning('请选择数据源和分析类型')
    return
  }

  isAnalyzing.value = true
  analysisProgress.value = 0
  analysisResults.value = null

  try {
    // 模拟分析过程
    const steps = [
      { name: '数据预处理', duration: 1000 },
      { name: '特征提取', duration: 1500 },
      { name: '模型训练', duration: 2000 },
      { name: '结果验证', duration: 800 },
      { name: '生成报告', duration: 500 }
    ]

    for (let i = 0; i < steps.length; i++) {
      currentAnalysisStep.value = steps[i].name
      await new Promise(resolve => setTimeout(resolve, steps[i].duration))
      analysisProgress.value = ((i + 1) / steps.length) * 100
    }

    // 生成模拟结果
    analysisResults.value = {
      accuracy: 94.7,
      pValue: 0.001,
      sampleSize: analysisParams.sampleSize,
      runtime: 5.8,
      algorithm: analysisParams.algorithm
    }

    ElMessage.success('分析完成')

  } catch (error) {
    ElMessage.error('分析失败: ' + error.message)
  } finally {
    isAnalyzing.value = false
  }
}

const exportResults = () => {
  if (!analysisResults.value) return
  
  ElMessage.success('结果导出完成')
}

const refreshData = () => {
  ElMessage.info('数据已刷新')
}

const getAnalysisTypeName = (type) => {
  const types = {
    statistics: '统计分析',
    ml: '机器学习',
    bioinformatics: '生物信息学',
    imaging: '图像分析'
  }
  return types[type] || type
}

const getStatusType = (status) => {
  const types = {
    completed: 'success',
    running: 'warning',
    failed: 'danger',
    pending: 'info'
  }
  return types[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    completed: '已完成',
    running: '运行中',
    failed: '失败',
    pending: '等待中'
  }
  return texts[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const viewAnalysis = (analysis) => {
  console.log('查看分析:', analysis)
  ElMessage.info(`查看分析: ${analysis.name}`)
}

const rerunAnalysis = (analysis) => {
  console.log('重新运行分析:', analysis)
  ElMessage.info(`重新运行分析: ${analysis.name}`)
}

const deleteAnalysis = (analysis) => {
  console.log('删除分析:', analysis)
  ElMessage.success(`已删除分析: ${analysis.name}`)
}

// 生命周期
onMounted(() => {
  console.log('分析中心已加载')
})
</script>

<style scoped>
.analysis-center {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #2c3e50;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
}

.analysis-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.toolbar {
  width: 100%;
}

.toolbar-card {
  border-radius: 8px;
}

.toolbar-content {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.tool-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.main-content {
  flex: 1;
}

.left-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.data-preview-card,
.parameters-card,
.results-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.data-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.data-summary {
  font-size: 12px;
  color: #666;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
}

.data-summary p {
  margin: 0;
  line-height: 1.5;
}

.parameters-content {
  padding: 8px 0;
}

.results-section {
  height: 100%;
}

.analyzing-status {
  text-align: center;
  padding: 40px 20px;
}

.analyzing-status p {
  margin: 16px 0 0 0;
  color: #666;
}

.results-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.results-overview {
  margin-bottom: 20px;
}

.metric-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #7f8c8d;
}

.charts-container {
  min-height: 300px;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  text-align: center;
}

.chart-icon {
  font-size: 48px;
  color: #adb5bd;
  margin-bottom: 16px;
}

.chart-placeholder p {
  margin: 0;
  color: #666;
}

.chart-desc {
  font-size: 12px !important;
  color: #999 !important;
  margin-top: 8px !important;
}

.detailed-results {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
}

.detailed-results h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #2c3e50;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
}

.no-results-icon {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 16px;
}

.no-results p {
  margin: 0;
  color: #666;
}

.no-results-desc {
  font-size: 12px !important;
  color: #999 !important;
  margin-top: 8px !important;
}

.history-section {
  margin-top: 32px;
}

.history-card {
  margin-bottom: 20px;
}

.result-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style> 