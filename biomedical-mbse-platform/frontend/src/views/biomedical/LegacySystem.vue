<template>
  <div class="legacy-system">
    <div class="page-header">
      <h1>XML元数据系统</h1>
      <p>访问原XML元数据系统v3.0的功能模块</p>
    </div>

    <div class="legacy-content">
      <!-- 系统状态 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <span>系统状态</span>
            <el-tag :type="systemStatus.connected ? 'success' : 'danger'">
              {{ systemStatus.connected ? '已连接' : '未连接' }}
            </el-tag>
          </div>
        </template>

        <div class="status-content">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="status-item">
                <div class="status-value">{{ systemStatus.version }}</div>
                <div class="status-label">系统版本</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="status-item">
                <div class="status-value">{{ systemStatus.uptime }}</div>
                <div class="status-label">运行时间</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="status-item">
                <div class="status-value">{{ systemStatus.aiEngines }}/4</div>
                <div class="status-label">AI引擎</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="status-item">
                <div class="status-value">{{ systemStatus.cacheHitRate }}%</div>
                <div class="status-label">缓存命中率</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 功能模块导航 -->
      <el-card class="modules-card">
        <template #header>
          <span>功能模块</span>
        </template>

        <div class="modules-grid">
          <div 
            v-for="module in legacyModules" 
            :key="module.id"
            class="module-card"
            @click="accessModule(module)"
          >
            <div class="module-icon">
              <el-icon :class="module.icon" />
            </div>
            <div class="module-info">
              <h3>{{ module.name }}</h3>
              <p>{{ module.description }}</p>
              <div class="module-meta">
                <el-tag size="small" :type="getStatusType(module.status)">
                  {{ getStatusText(module.status) }}
                </el-tag>
                <span class="module-users">{{ module.activeUsers }} 活跃用户</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 数据迁移状态 -->
      <el-card class="migration-card">
        <template #header>
          <div class="card-header">
            <span>数据迁移状态</span>
            <el-button size="small" @click="refreshMigrationStatus">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>

        <div class="migration-content">
          <el-progress 
            :percentage="migrationProgress.overall" 
            :status="migrationProgress.status"
            stroke-width="8"
          />
          
          <div class="migration-details">
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="migration-item">
                  <div class="migration-label">AI引擎迁移</div>
                  <el-progress :percentage="migrationProgress.aiEngines" size="small" />
                </div>
              </el-col>
              <el-col :span="8">
                <div class="migration-item">
                  <div class="migration-label">前端组件迁移</div>
                  <el-progress :percentage="migrationProgress.frontend" size="small" />
                </div>
              </el-col>
              <el-col :span="8">
                <div class="migration-item">
                  <div class="migration-label">数据库迁移</div>
                  <el-progress :percentage="migrationProgress.database" size="small" />
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="migration-summary">
            <p><strong>迁移摘要:</strong></p>
            <ul>
              <li>✅ AI引擎集成: 100% 完成，所有4个引擎已成功迁移</li>
              <li>✅ 前端组件: 90% 完成，26个Vue组件需要完善脚本</li>
              <li>⚠️ 数据桥接: 75% 完成，正在优化性能</li>
              <li>🔄 系统测试: 进行中，预计今日完成</li>
            </ul>
          </div>
        </div>
      </el-card>

      <!-- 性能指标 -->
      <el-card class="performance-card">
        <template #header>
          <span>性能指标</span>
        </template>

        <div class="performance-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="performance-section">
                <h4>AI引擎性能</h4>
                <div class="metrics-list">
                  <div class="metric-row">
                    <span class="metric-name">推荐引擎</span>
                    <span class="metric-value">6,852/秒</span>
                    <el-tag size="small" type="success">优秀</el-tag>
                  </div>
                  <div class="metric-row">
                    <span class="metric-name">智能预加载</span>
                    <span class="metric-value">66.7% 命中率</span>
                    <el-tag size="small" type="warning">良好</el-tag>
                  </div>
                  <div class="metric-row">
                    <span class="metric-name">自适应缓存</span>
                    <span class="metric-value">94.9% 命中率</span>
                    <el-tag size="small" type="success">优秀</el-tag>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="performance-section">
                <h4>系统吞吐量</h4>
                <div class="metrics-list">
                  <div class="metric-row">
                    <span class="metric-name">并发处理</span>
                    <span class="metric-value">18,000/秒</span>
                    <el-tag size="small" type="success">优秀</el-tag>
                  </div>
                  <div class="metric-row">
                    <span class="metric-name">响应时间</span>
                    <span class="metric-value">0.78ms</span>
                    <el-tag size="small" type="success">优秀</el-tag>
                  </div>
                  <div class="metric-row">
                    <span class="metric-name">错误率</span>
                    <span class="metric-value">0.01%</span>
                    <el-tag size="small" type="success">优秀</el-tag>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 模块访问对话框 -->
    <el-dialog 
      v-model="showModuleDialog" 
      :title="selectedModule?.name"
      width="800px"
    >
      <div v-if="selectedModule" class="module-dialog-content">
        <div class="module-description">
          <p>{{ selectedModule.fullDescription }}</p>
        </div>
        
        <div class="module-features">
          <h4>主要功能:</h4>
          <ul>
            <li v-for="feature in selectedModule.features" :key="feature">
              {{ feature }}
            </li>
          </ul>
        </div>

        <div class="access-options">
          <el-alert
            title="访问说明"
            type="info"
            :closable="false"
            show-icon
          >
            <p>此模块来自原XML元数据系统v3.0，已通过桥接器完整集成到新平台中。</p>
            <p>您可以直接在新界面中使用，也可以访问原始界面进行高级配置。</p>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <el-button @click="showModuleDialog = false">取消</el-button>
        <el-button type="primary" @click="openModuleInNewWindow">
          在新窗口打开
        </el-button>
        <el-button type="success" @click="navigateToModule">
          在当前页面访问
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const showModuleDialog = ref(false)
const selectedModule = ref(null)

// 系统状态
const systemStatus = reactive({
  connected: true,
  version: 'v3.0',
  uptime: '127天',
  aiEngines: 4,
  cacheHitRate: 94.9
})

// 迁移进度
const migrationProgress = reactive({
  overall: 87.5,
  status: 'success',
  aiEngines: 100,
  frontend: 90,
  database: 75
})

// 遗留模块
const legacyModules = ref([
  {
    id: 'smart-dashboard',
    name: '智能仪表板',
    description: 'XML元数据的智能分析与可视化仪表板',
    fullDescription: '提供XML元数据的全方位智能分析，包括实时监控、趋势分析、异常检测等功能。',
    icon: 'DataBoard',
    status: 'active',
    activeUsers: 23,
    route: '/legacy/dashboard',
    features: [
      '实时数据监控和分析',
      '智能趋势预测',
      '异常检测和告警',
      '可定制化仪表板',
      '数据可视化图表'
    ]
  },
  {
    id: 'requirements-analyst',
    name: '需求分析工作区',
    description: '专业的需求分析和管理工具',
    fullDescription: '基于AI的需求分析工具，支持需求收集、分析、跟踪和验证的全生命周期管理。',
    icon: 'Document',
    status: 'active',
    activeUsers: 15,
    route: '/legacy/requirements',
    features: [
      'AI辅助需求挖掘',
      '需求关系图谱',
      '需求变更追踪',
      '冲突检测和解决',
      '需求验证和测试'
    ]
  },
  {
    id: 'system-architect',
    name: '系统架构工作区',
    description: '系统架构设计和建模平台',
    fullDescription: '提供完整的系统架构设计工具，支持多层架构建模和设计模式应用。',
    icon: 'SetUp',
    status: 'active',
    activeUsers: 8,
    route: '/legacy/architecture',
    features: [
      '多层架构建模',
      '组件关系设计',
      '架构模式库',
      '性能分析评估',
      '架构文档生成'
    ]
  },
  {
    id: 'behavior-analyst',
    name: '行为分析工作区',
    description: '系统行为建模和分析工具',
    fullDescription: '专注于系统动态行为的建模、仿真和分析，支持复杂业务流程的优化。',
    icon: 'TrendCharts',
    status: 'active',
    activeUsers: 12,
    route: '/legacy/behavior',
    features: [
      '行为流程建模',
      '状态机设计',
      '时序分析',
      '性能仿真',
      '优化建议'
    ]
  },
  {
    id: 'data-modeler',
    name: '数据建模工作区',
    description: '数据模型设计和管理平台',
    fullDescription: '提供专业的数据建模工具，支持概念模型、逻辑模型和物理模型的设计。',
    icon: 'Files',
    status: 'maintenance',
    activeUsers: 5,
    route: '/legacy/data',
    features: [
      'ER图设计',
      '数据字典管理',
      '数据血缘分析',
      '质量评估',
      'DDL脚本生成'
    ]
  }
])

// 方法
const getStatusType = (status) => {
  const types = {
    active: 'success',
    maintenance: 'warning',
    deprecated: 'info',
    offline: 'danger'
  }
  return types[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '运行中',
    maintenance: '维护中',
    deprecated: '已弃用',
    offline: '离线'
  }
  return texts[status] || status
}

const accessModule = (module) => {
  selectedModule.value = module
  showModuleDialog.value = true
}

const navigateToModule = () => {
  if (selectedModule.value?.route) {
    router.push(selectedModule.value.route)
    showModuleDialog.value = false
  }
}

const openModuleInNewWindow = () => {
  if (selectedModule.value?.route) {
    const url = `${window.location.origin}${selectedModule.value.route}`
    window.open(url, '_blank')
    showModuleDialog.value = false
  }
}

const refreshMigrationStatus = () => {
  ElMessage.info('迁移状态已刷新')
  // 模拟刷新数据
  migrationProgress.overall = Math.min(migrationProgress.overall + 2, 100)
  migrationProgress.frontend = Math.min(migrationProgress.frontend + 5, 100)
  migrationProgress.database = Math.min(migrationProgress.database + 10, 100)
}

// 生命周期
onMounted(() => {
  console.log('XML遗留系统页面已加载')
})
</script>

<style scoped>
.legacy-system {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #2c3e50;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
}

.legacy-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-content {
  padding: 16px 0;
}

.status-item {
  text-align: center;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
}

.status-label {
  font-size: 12px;
  color: #7f8c8d;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.module-card {
  display: flex;
  padding: 20px;
  border: 1px solid #e8eaec;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.module-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.module-icon {
  font-size: 40px;
  color: #409eff;
  margin-right: 16px;
  display: flex;
  align-items: center;
}

.module-info {
  flex: 1;
}

.module-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #2c3e50;
}

.module-info p {
  margin: 0 0 12px 0;
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
}

.module-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.module-users {
  font-size: 12px;
  color: #999;
}

.migration-content {
  padding: 16px 0;
}

.migration-details {
  margin: 24px 0;
}

.migration-item {
  text-align: center;
}

.migration-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.migration-summary {
  margin-top: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.migration-summary p {
  margin: 0 0 8px 0;
  font-weight: 500;
}

.migration-summary ul {
  margin: 0;
  padding-left: 20px;
}

.migration-summary li {
  margin: 4px 0;
  font-size: 14px;
}

.performance-content {
  padding: 16px 0;
}

.performance-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #2c3e50;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.metric-name {
  font-size: 13px;
  color: #666;
}

.metric-value {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

.module-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.module-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.module-features h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #2c3e50;
}

.module-features ul {
  margin: 0;
  padding-left: 20px;
}

.module-features li {
  margin: 6px 0;
  color: #666;
  font-size: 14px;
}

.access-options {
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}
</style> 