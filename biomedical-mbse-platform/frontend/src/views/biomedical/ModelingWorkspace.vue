<template>
  <div class="modeling-workspace">
    <div class="page-header">
      <h1>建模工作区</h1>
      <p>生物医学系统建模与仿真环境</p>
    </div>

    <div class="workspace-layout">
      <!-- 左侧工具面板 -->
      <div class="left-panel">
        <el-card class="tools-panel">
          <template #header>
            <span>建模工具</span>
          </template>
          
          <el-collapse v-model="activeTools" accordion>
            <el-collapse-item title="分子建模" name="molecular">
              <div class="tool-category">
                <div v-for="tool in molecularTools" :key="tool.id" class="tool-item" @click="selectTool(tool)">
                  <el-icon><Connection /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="系统建模" name="systems">
              <div class="tool-category">
                <div v-for="tool in systemTools" :key="tool.id" class="tool-item" @click="selectTool(tool)">
                  <el-icon><SetUp /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="数据分析" name="analysis">
              <div class="tool-category">
                <div v-for="tool in analysisTools" :key="tool.id" class="tool-item" @click="selectTool(tool)">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>{{ tool.name }}</span>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>

        <!-- 属性面板 -->
        <el-card class="properties-panel">
          <template #header>
            <span>属性设置</span>
          </template>
          
          <div v-if="selectedElement" class="properties-content">
            <el-form :model="selectedElement" label-width="80px" size="small">
              <el-form-item label="名称">
                <el-input v-model="selectedElement.name" />
              </el-form-item>
              <el-form-item label="类型">
                <el-select v-model="selectedElement.type">
                  <el-option label="蛋白质" value="protein" />
                  <el-option label="基因" value="gene" />
                  <el-option label="代谢物" value="metabolite" />
                  <el-option label="反应" value="reaction" />
                </el-select>
              </el-form-item>
              <el-form-item label="描述">
                <el-input v-model="selectedElement.description" type="textarea" :rows="3" />
              </el-form-item>
            </el-form>
          </div>
          <div v-else class="no-selection">
            <p>请选择一个元素来编辑属性</p>
          </div>
        </el-card>
      </div>

      <!-- 中央画布区域 -->
      <div class="canvas-area">
        <el-card class="canvas-container">
          <template #header>
            <div class="canvas-header">
              <span>{{ currentModel.name || '新建模型' }}</span>
              <div class="canvas-actions">
                <el-button size="small" @click="zoomIn">
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
                <el-button size="small" @click="zoomOut">
                  <el-icon><ZoomOut /></el-icon>
                </el-button>
                <el-button size="small" @click="resetZoom">
                  <el-icon><Refresh /></el-icon>
                </el-button>
                <el-button size="small" type="primary" @click="saveModel">
                  <el-icon><DocumentAdd /></el-icon>
                  保存
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="modeling-canvas" ref="canvasRef" @click="onCanvasClick">
            <!-- 网格背景 -->
            <div class="grid-background"></div>
            
            <!-- 模型元素 -->
            <div v-for="element in modelElements" :key="element.id" 
                 :class="['model-element', element.type, { selected: selectedElement?.id === element.id }]"
                 :style="{
                   left: element.x + 'px',
                   top: element.y + 'px',
                   transform: `scale(${zoomLevel})`
                 }"
                 @click.stop="selectElement(element)"
                 @mousedown="startDrag(element, $event)">
              <div class="element-icon">
                <el-icon v-if="element.type === 'protein'"><User /></el-icon>
                <el-icon v-else-if="element.type === 'gene'"><Document /></el-icon>
                <el-icon v-else-if="element.type === 'metabolite'"><Star /></el-icon>
                <el-icon v-else><Connection /></el-icon>
              </div>
              <div class="element-label">{{ element.name }}</div>
            </div>
            
            <!-- 连接线 -->
            <svg class="connections-layer" :style="{ transform: `scale(${zoomLevel})` }">
              <line v-for="connection in connections" :key="connection.id"
                    :x1="connection.x1" :y1="connection.y1"
                    :x2="connection.x2" :y2="connection.y2"
                    stroke="#409eff" stroke-width="2" />
            </svg>
          </div>
        </el-card>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 模型结构 -->
        <el-card class="structure-panel">
          <template #header>
            <span>模型结构</span>
          </template>
          
          <el-tree
            :data="modelStructure"
            :props="{ children: 'children', label: 'name' }"
            node-key="id"
            :expand-on-click-node="false"
            @node-click="onTreeNodeClick">
            <template #default="{ node, data }">
              <span class="tree-node">
                <el-icon v-if="data.type === 'folder'"><Folder /></el-icon>
                <el-icon v-else><Document /></el-icon>
                {{ data.name }}
              </span>
            </template>
          </el-tree>
        </el-card>

        <!-- 仿真控制 -->
        <el-card class="simulation-panel">
          <template #header>
            <span>仿真控制</span>
          </template>
          
          <div class="simulation-content">
            <el-form size="small" label-width="80px">
              <el-form-item label="时间范围">
                <el-input-number v-model="simulationConfig.timeRange" :min="1" :max="1000" />
              </el-form-item>
              <el-form-item label="步长">
                <el-input-number v-model="simulationConfig.stepSize" :min="0.01" :max="1" :step="0.01" />
              </el-form-item>
              <el-form-item label="算法">
                <el-select v-model="simulationConfig.algorithm">
                  <el-option label="欧拉法" value="euler" />
                  <el-option label="龙格-库塔" value="rk4" />
                  <el-option label="LSODA" value="lsoda" />
                </el-select>
              </el-form-item>
            </el-form>
            
            <div class="simulation-actions">
              <el-button type="success" @click="runSimulation" :loading="isSimulating">
                <el-icon><VideoPlay /></el-icon>
                运行仿真
              </el-button>
              <el-button @click="stopSimulation" :disabled="!isSimulating">
                <el-icon><VideoPause /></el-icon>
                停止
              </el-button>
            </div>
            
            <div v-if="simulationResults" class="simulation-results">
              <h4>仿真结果</h4>
              <div class="result-chart">
                <!-- 这里可以集成图表库显示结果 -->
                <p>仿真完成，共计算 {{ simulationResults.steps }} 步</p>
                <p>计算时间: {{ simulationResults.duration }}ms</p>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const activeTools = ref(['molecular'])
const selectedElement = ref(null)
const selectedTool = ref(null)
const zoomLevel = ref(1)
const isSimulating = ref(false)
const simulationResults = ref(null)
const canvasRef = ref()

// 当前模型
const currentModel = reactive({
  name: '生物医学模型',
  type: 'systems',
  elements: [],
  connections: []
})

// 模型元素
const modelElements = ref([
  {
    id: 'element-1',
    name: 'Protein A',
    type: 'protein',
    x: 100,
    y: 100,
    description: '关键调节蛋白'
  },
  {
    id: 'element-2',
    name: 'Gene B',
    type: 'gene',
    x: 300,
    y: 150,
    description: '编码基因'
  },
  {
    id: 'element-3',
    name: 'Metabolite C',
    type: 'metabolite',
    x: 200,
    y: 250,
    description: '代谢产物'
  }
])

// 连接关系
const connections = ref([
  {
    id: 'conn-1',
    x1: 150, y1: 120,
    x2: 300, y2: 170
  },
  {
    id: 'conn-2',
    x1: 320, y1: 180,
    x2: 220, y2: 250
  }
])

// 模型结构树
const modelStructure = ref([
  {
    id: 'systems',
    name: '系统模块',
    type: 'folder',
    children: [
      { id: 'sys-1', name: '信号传导', type: 'module' },
      { id: 'sys-2', name: '代谢网络', type: 'module' }
    ]
  },
  {
    id: 'components',
    name: '组件库',
    type: 'folder',
    children: [
      { id: 'comp-1', name: '蛋白质组件', type: 'component' },
      { id: 'comp-2', name: '基因组件', type: 'component' }
    ]
  }
])

// 工具配置
const molecularTools = ref([
  { id: 'protein-tool', name: '蛋白质工具', type: 'protein' },
  { id: 'gene-tool', name: '基因工具', type: 'gene' },
  { id: 'reaction-tool', name: '反应工具', type: 'reaction' }
])

const systemTools = ref([
  { id: 'pathway-tool', name: '通路建模', type: 'pathway' },
  { id: 'network-tool', name: '网络分析', type: 'network' },
  { id: 'regulation-tool', name: '调控关系', type: 'regulation' }
])

const analysisTools = ref([
  { id: 'stats-tool', name: '统计分析', type: 'statistics' },
  { id: 'viz-tool', name: '可视化', type: 'visualization' },
  { id: 'export-tool', name: '数据导出', type: 'export' }
])

// 仿真配置
const simulationConfig = reactive({
  timeRange: 100,
  stepSize: 0.1,
  algorithm: 'rk4'
})

// 拖拽相关
const dragState = reactive({
  isDragging: false,
  element: null,
  startX: 0,
  startY: 0,
  elementX: 0,
  elementY: 0
})

// 方法
const selectTool = (tool) => {
  selectedTool.value = tool
  ElMessage.info(`选择了工具: ${tool.name}`)
}

const selectElement = (element) => {
  selectedElement.value = element
}

const onCanvasClick = (event) => {
  if (selectedTool.value && !dragState.isDragging) {
    // 在画布上添加新元素
    const rect = canvasRef.value.getBoundingClientRect()
    const x = (event.clientX - rect.left) / zoomLevel.value
    const y = (event.clientY - rect.top) / zoomLevel.value
    
    const newElement = {
      id: `element-${Date.now()}`,
      name: `新${selectedTool.value.name}`,
      type: selectedTool.value.type,
      x: x - 25, // 居中偏移
      y: y - 25,
      description: '新建元素'
    }
    
    modelElements.value.push(newElement)
    selectedElement.value = newElement
  } else {
    selectedElement.value = null
  }
}

const startDrag = (element, event) => {
  dragState.isDragging = false
  dragState.element = element
  dragState.startX = event.clientX
  dragState.startY = event.clientY
  dragState.elementX = element.x
  dragState.elementY = element.y
  
  const onMouseMove = (e) => {
    dragState.isDragging = true
    const deltaX = (e.clientX - dragState.startX) / zoomLevel.value
    const deltaY = (e.clientY - dragState.startY) / zoomLevel.value
    
    element.x = dragState.elementX + deltaX
    element.y = dragState.elementY + deltaY
  }
  
  const onMouseUp = () => {
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
    setTimeout(() => { dragState.isDragging = false }, 10)
  }
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.2)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const saveModel = () => {
  currentModel.elements = [...modelElements.value]
  currentModel.connections = [...connections.value]
  ElMessage.success('模型已保存')
}

const runSimulation = async () => {
  isSimulating.value = true
  simulationResults.value = null
  
  try {
    // 模拟仿真过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    simulationResults.value = {
      steps: Math.floor(simulationConfig.timeRange / simulationConfig.stepSize),
      duration: 2847,
      success: true
    }
    
    ElMessage.success('仿真完成')
  } catch (error) {
    ElMessage.error('仿真失败: ' + error.message)
  } finally {
    isSimulating.value = false
  }
}

const stopSimulation = () => {
  isSimulating.value = false
  ElMessage.info('仿真已停止')
}

const onTreeNodeClick = (data) => {
  console.log('树节点点击:', data)
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    console.log('建模工作区已加载')
  })
})
</script>

<style scoped>
.modeling-workspace {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #2c3e50;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
}

.workspace-layout {
  display: flex;
  flex: 1;
  gap: 20px;
  min-height: 0;
}

.left-panel {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tools-panel,
.properties-panel {
  flex: 1;
}

.tool-category {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tool-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #e8eaec;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.tool-item:hover {
  border-color: #409eff;
  background-color: #f0f7ff;
}

.properties-content {
  max-height: 400px;
  overflow-y: auto;
}

.no-selection {
  text-align: center;
  color: #999;
  padding: 40px 20px;
}

.canvas-area {
  flex: 1;
  min-width: 0;
}

.canvas-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.canvas-actions {
  display: flex;
  gap: 8px;
}

.modeling-canvas {
  flex: 1;
  position: relative;
  background: #fafafa;
  border: 1px solid #e8eaec;
  border-radius: 6px;
  overflow: hidden;
  cursor: crosshair;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(to right, #e8eaec 1px, transparent 1px),
    linear-gradient(to bottom, #e8eaec 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
}

.model-element {
  position: absolute;
  width: 80px;
  height: 60px;
  border: 2px solid #409eff;
  border-radius: 8px;
  background: white;
  cursor: move;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  transform-origin: center;
}

.model-element:hover {
  border-color: #67c23a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.model-element.selected {
  border-color: #e6a23c;
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);
}

.model-element.protein {
  border-color: #409eff;
}

.model-element.gene {
  border-color: #67c23a;
}

.model-element.metabolite {
  border-color: #e6a23c;
}

.model-element.reaction {
  border-color: #f56c6c;
}

.element-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.element-label {
  font-size: 11px;
  text-align: center;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  padding: 0 4px;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  transform-origin: top left;
}

.right-panel {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.structure-panel,
.simulation-panel {
  flex: 1;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
}

.simulation-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.simulation-actions {
  display: flex;
  gap: 8px;
}

.simulation-results {
  border-top: 1px solid #e8eaec;
  padding-top: 16px;
}

.simulation-results h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
}

.result-chart {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  color: #666;
}
</style> 