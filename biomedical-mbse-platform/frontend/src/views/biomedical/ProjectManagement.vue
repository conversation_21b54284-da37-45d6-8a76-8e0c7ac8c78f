<template>
  <div class="project-management">
    <div class="page-header">
      <h1>项目管理</h1>
      <p>管理您的生物医学MBSE建模项目</p>
    </div>

    <!-- 项目统计卡片 -->
    <el-row :gutter="24" class="stats-cards">
      <el-col :span="6" :xs="12" :sm="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.totalProjects }}</div>
              <div class="stat-label">总项目数</div>
            </div>
            <el-icon class="stat-icon"><Folder /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6" :xs="12" :sm="6">
        <el-card class="stat-card active-card">
          <div class="stat-content">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.activeProjects }}</div>
              <div class="stat-label">活跃项目</div>
            </div>
            <el-icon class="stat-icon"><Edit /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6" :xs="12" :sm="6">
        <el-card class="stat-card completed-card">
          <div class="stat-content">
            <div class="stat-info">
              <div class="stat-number">{{ statistics.completedProjects }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <el-icon class="stat-icon"><Select /></el-icon>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6" :xs="12" :sm="6">
        <el-card class="stat-card success-card">
          <div class="stat-content">
            <div class="stat-info">
              <div class="stat-number">{{ (statistics.successRate * 100).toFixed(1) }}%</div>
              <div class="stat-label">成功率</div>
            </div>
            <el-icon class="stat-icon"><TrendCharts /></el-icon>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 项目列表 -->
    <el-card class="projects-table-card">
      <template #header>
        <div class="card-header">
          <span>项目列表</span>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新建项目
          </el-button>
        </div>
      </template>

      <div class="table-container">
        <el-table 
          :data="filteredProjects" 
          v-loading="isLoading"
          empty-text="暂无项目数据"
          stripe
          :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: '600' }"
          :row-style="{ height: '60px' }"
          style="width: 100%"
        >
          <el-table-column prop="name" label="项目名称" min-width="180" width="220">
            <template #default="{ row }">
              <div class="project-name">
                <el-tag :type="getProjectTypeColor(row.type)" size="small" class="project-type-tag">
                  {{ getProjectTypeLabel(row.type) }}
                </el-tag>
                <span class="project-title">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="进度" width="120">
            <template #default="{ row }">
              <el-progress 
                :percentage="row.progress || 0" 
                :stroke-width="6"
                :show-text="true"
              />
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" @click="viewProject(row)">查看</el-button>
                <el-button size="small" type="primary" @click="editProject(row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteProject(row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 创建项目对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      title="新建项目" 
      width="600px"
      @close="resetCreateForm"
    >
      <el-form :model="createForm" :rules="createRules" ref="createFormRef" label-width="100px">
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目类型" prop="type">
          <el-select v-model="createForm.type" placeholder="请选择项目类型" style="width: 100%">
            <el-option label="分子建模" value="molecular" />
            <el-option label="细胞建模" value="cellular" />
            <el-option label="组织建模" value="tissue" />
            <el-option label="器官建模" value="organ" />
            <el-option label="系统建模" value="system" />
          </el-select>
        </el-form-item>
        <el-form-item label="项目描述" prop="description">
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            :rows="4"
            placeholder="请输入项目描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreateProject" :loading="isCreating">
            创建项目
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useBiomedicalStore } from '@/stores/biomedical/biomedicalStore'

// Store
const biomedicalStore = useBiomedicalStore()

// 响应式数据
const isLoading = ref(false)
const isCreating = ref(false)
const showCreateDialog = ref(false)

// 表单数据
const createForm = reactive({
  name: '',
  type: '',
  description: ''
})

const createRules = {
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入项目描述', trigger: 'blur' }]
}

// 引用
const createFormRef = ref()

// 计算属性
const filteredProjects = computed(() => biomedicalStore.projects)

const statistics = computed(() => {
  const projects = biomedicalStore.projects
  const total = projects.length
  const active = projects.filter(p => p.status === 'active').length
  const completed = projects.filter(p => p.status === 'completed').length
  const successRate = total > 0 ? completed / total : 0

  return {
    totalProjects: total,
    activeProjects: active,
    completedProjects: completed,
    successRate
  }
})

// 方法
const getProjectTypeColor = (type) => {
  const colors = {
    molecular: 'primary',
    cellular: 'success',
    tissue: 'warning',
    organ: 'danger',
    system: 'info'
  }
  return colors[type] || 'info'
}

const getProjectTypeLabel = (type) => {
  const labels = {
    molecular: '分子建模',
    cellular: '细胞建模',
    tissue: '组织建模',
    organ: '器官建模',
    system: '系统建模'
  }
  return labels[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    active: 'success',
    paused: 'warning',
    completed: 'info',
    failed: 'danger'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    type: '',
    description: ''
  })
  createFormRef.value?.resetFields()
}

const handleCreateProject = async () => {
  try {
    await createFormRef.value.validate()
    
    isCreating.value = true
    const projectData = {
      ...createForm,
      status: 'active',
      progress: 0,
      created_at: new Date().toISOString()
    }
    
    await biomedicalStore.createProject(projectData)
    
    ElMessage.success('项目创建成功')
    showCreateDialog.value = false
    resetCreateForm()
  } catch (error) {
    ElMessage.error('项目创建失败: ' + (error.message || '未知错误'))
  } finally {
    isCreating.value = false
  }
}

const viewProject = (project) => {
  // 跳转到项目详情页
  console.log('查看项目:', project)
}

const editProject = (project) => {
  // 编辑项目逻辑
  console.log('编辑项目:', project)
}

const deleteProject = async (project) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目 "${project.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 删除项目逻辑
    console.log('删除项目:', project)
    ElMessage.success('项目删除成功')
  } catch {
    // 用户取消删除
  }
}

// 生命周期
onMounted(async () => {
  isLoading.value = true
  try {
    await biomedicalStore.loadProjects()
  } catch (error) {
    ElMessage.error('加载项目数据失败')
  } finally {
    isLoading.value = false
  }
})
</script>

<style scoped>
.project-management {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
}

.page-header {
  margin-bottom: 24px;
  padding: 0 4px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  color: #2c3e50;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 24px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-info {
  text-align: left;
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

.stat-icon {
  font-size: 40px;
  color: rgba(52, 152, 219, 0.15);
  transition: all 0.3s ease;
}

/* 不同类型卡片的主题色 */
.active-card .stat-number {
  color: #67c23a;
}

.active-card .stat-icon {
  color: rgba(103, 194, 58, 0.15);
}

.active-card:hover .stat-icon {
  color: rgba(103, 194, 58, 0.25);
}

.completed-card .stat-number {
  color: #409eff;
}

.completed-card .stat-icon {
  color: rgba(64, 158, 255, 0.15);
}

.completed-card:hover .stat-icon {
  color: rgba(64, 158, 255, 0.25);
}

.success-card .stat-number {
  color: #f56c6c;
}

.success-card .stat-icon {
  color: rgba(245, 108, 108, 0.15);
}

.success-card:hover .stat-icon {
  color: rgba(245, 108, 108, 0.25);
}

.projects-table-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-container {
  overflow-x: auto;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.project-name {
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-type-tag {
  flex-shrink: 0;
}

.project-title {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  min-width: 1000px;
}

:deep(.el-table th) {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  white-space: nowrap;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f1f3f4;
  padding: 12px 8px;
}

:deep(.el-table .el-table__row:hover > td) {
  background-color: #f8f9fa;
}

:deep(.el-table .el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.action-buttons .el-button {
  flex-shrink: 0;
}

/* 按钮样式优化 */
:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 4px;
  min-width: auto;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog) {
  border-radius: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 4px;
}

:deep(.el-textarea__inner) {
  border-radius: 4px;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 进度条样式 */
:deep(.el-progress-bar__outer) {
  background-color: #f5f7fa;
  border-radius: 10px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 10px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .project-management {
    max-width: 100%;
    padding: 16px;
  }
  
  :deep(.el-table) {
    min-width: 900px;
  }
}

@media (max-width: 768px) {
  .project-management {
    padding: 12px;
  }
  
  .stats-cards .el-col {
    margin-bottom: 16px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .stat-number {
    font-size: 28px;
  }
  
  .stat-icon {
    font-size: 36px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .el-button {
    width: 100%;
    margin: 0;
  }
}
</style> 