<template>
  <div class="visualization-demo">
    <div class="container">
      <div class="page-header">
        <h1>可视化组件使用演示</h1>
        <p>展示如何在您的项目中使用生物医学可视化组件库</p>
      </div>

      <!-- 使用说明 -->
      <div class="usage-section">
        <el-card>
          <template #header>
            <span>📖 使用说明</span>
          </template>
          <div class="usage-content">
            <h3>1. 安装依赖包</h3>
            <el-code class="code-block">
npm install @antv/g6 @antv/g2plot echarts vue-echarts @antv/x6 d3 three @types/three
            </el-code>

            <h3>2. 在组件中导入</h3>
            <el-code class="code-block">
import { 
  MolecularViewer, 
  NetworkGraph, 
  FlowEditor 
} from '@/components/visualization'
            </el-code>

            <h3>3. 在模板中使用</h3>
            <el-code class="code-block">
&lt;MolecularViewer :molecule-data="moleculeData" /&gt;
&lt;NetworkGraph :data="networkData" /&gt;
&lt;FlowEditor @save="handleSave" /&gt;
            </el-code>
          </div>
        </el-card>
      </div>

      <!-- 分子查看器演示 -->
      <div class="demo-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>🧬 3D分子结构查看器</span>
              <div class="header-controls">
                <el-button size="small" @click="loadWaterMolecule">水分子</el-button>
                <el-button size="small" @click="loadMethaneMolecule">甲烷</el-button>
                <el-button size="small" @click="loadCaffeineMolecule">咖啡因</el-button>
              </div>
            </div>
          </template>
          
          <div class="demo-content">
            <MolecularViewer 
              :molecule-data="currentMolecule"
              :width="800"
              :height="500"
              @atom-click="onAtomClick"
              @bond-click="onBondClick"
              @selection-change="onMoleculeSelectionChange"
            />
            
            <div class="demo-info">
              <h4>功能特性：</h4>
              <ul>
                <li>✅ 3D分子结构可视化</li>
                <li>✅ 多种渲染模式（球棍、空间填充、线框、表面）</li>
                <li>✅ 多种配色方案（元素色、残基色、链色）</li>
                <li>✅ 交互控制（旋转、缩放、全屏）</li>
                <li>✅ PDB格式数据支持</li>
              </ul>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 网络图演示 -->
      <div class="demo-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>🔗 生物网络关系图</span>
              <div class="header-controls">
                <el-button size="small" @click="loadProteinNetwork">蛋白质网络</el-button>
                <el-button size="small" @click="loadGeneNetwork">基因网络</el-button>
                <el-button size="small" @click="loadPathwayNetwork">通路网络</el-button>
              </div>
            </div>
          </template>
          
          <div class="demo-content">
            <NetworkGraph
              :data="currentNetwork"
              :width="800"
              :height="500"
              :node-types="nodeTypes"
              @node-click="onNodeClick"
              @edge-click="onEdgeClick"
              @selection-change="onNetworkSelectionChange"
            />
            
            <div class="demo-info">
              <h4>功能特性：</h4>
              <ul>
                <li>✅ 多种布局算法（力导向、层次、径向、网格、圆形）</li>
                <li>✅ 自定义生物医学节点类型</li>
                <li>✅ 智能交互（悬停高亮、搜索过滤）</li>
                <li>✅ 数据导出功能</li>
                <li>✅ 响应式设计</li>
              </ul>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 流程编辑器演示 -->
      <div class="demo-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>⚙️ 生物医学流程编辑器</span>
              <div class="header-controls">
                <el-button size="small" @click="loadSampleFlow">加载示例</el-button>
                <el-button size="small" @click="clearFlow">清空画布</el-button>
              </div>
            </div>
          </template>
          
          <div class="demo-content">
            <FlowEditor
              ref="flowEditorRef"
              @save="onFlowSave"
              @export="onFlowExport"
              @change="onFlowChange"
            />
            
            <div class="demo-info">
              <h4>功能特性：</h4>
              <ul>
                <li>✅ 拖拽式流程设计</li>
                <li>✅ 丰富的组件库（基础、生物医学、建模）</li>
                <li>✅ 撤销/重做操作</li>
                <li>✅ 属性面板编辑</li>
                <li>✅ 网格对齐和吸附</li>
                <li>✅ 多种导出格式</li>
              </ul>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 主题切换演示 -->
      <div class="demo-section">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>🎨 主题系统演示</span>
              <div class="header-controls">
                <el-select v-model="currentTheme" size="small" @change="onThemeChange">
                  <el-option label="生物医学主题" value="biomedical" />
                  <el-option label="暗色主题" value="dark" />
                  <el-option label="简洁主题" value="minimal" />
                  <el-option label="科学期刊主题" value="scientific" />
                  <el-option label="高对比度主题" value="high-contrast" />
                </el-select>
              </div>
            </div>
          </template>
          
          <div class="theme-demo">
            <div class="theme-preview">
              <h4>当前主题：{{ getThemeName(currentTheme) }}</h4>
              <div class="color-palette">
                <div 
                  v-for="(color, name) in getThemeColors()" 
                  :key="name"
                  class="color-item"
                >
                  <div class="color-swatch" :style="{ backgroundColor: color }"></div>
                  <span class="color-name">{{ name }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- API参考 -->
      <div class="demo-section">
        <el-card>
          <template #header>
            <span>📚 API参考</span>
          </template>
          
          <el-tabs>
            <el-tab-pane label="MolecularViewer" name="molecular">
              <div class="api-content">
                <h4>Props:</h4>
                <ul>
                  <li><code>molecule-data</code> - 分子数据对象</li>
                  <li><code>width</code> - 查看器宽度（默认800）</li>
                  <li><code>height</code> - 查看器高度（默认600）</li>
                </ul>
                
                <h4>Events:</h4>
                <ul>
                  <li><code>@atom-click</code> - 原子点击事件</li>
                  <li><code>@bond-click</code> - 化学键点击事件</li>
                  <li><code>@selection-change</code> - 选择变化事件</li>
                </ul>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="NetworkGraph" name="network">
              <div class="api-content">
                <h4>Props:</h4>
                <ul>
                  <li><code>data</code> - 网络数据对象（nodes, edges）</li>
                  <li><code>width</code> - 图宽度（默认800）</li>
                  <li><code>height</code> - 图高度（默认600）</li>
                  <li><code>node-types</code> - 节点类型配置</li>
                </ul>
                
                <h4>Events:</h4>
                <ul>
                  <li><code>@node-click</code> - 节点点击事件</li>
                  <li><code>@edge-click</code> - 边点击事件</li>
                  <li><code>@selection-change</code> - 选择变化事件</li>
                </ul>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="FlowEditor" name="flow">
              <div class="api-content">
                <h4>Events:</h4>
                <ul>
                  <li><code>@save</code> - 保存流程事件</li>
                  <li><code>@export</code> - 导出流程事件</li>
                  <li><code>@change</code> - 流程变化事件</li>
                </ul>
                
                <h4>Methods:</h4>
                <ul>
                  <li><code>clearCanvas()</code> - 清空画布</li>
                  <li><code>loadFlow(data)</code> - 加载流程数据</li>
                </ul>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  MolecularViewer, 
  NetworkGraph, 
  FlowEditor 
} from '@/components/visualization'
import { ThemeUtils } from '@/components/visualization/themes'

// 当前数据
const currentMolecule = ref({})
const currentNetwork = ref({ nodes: [], edges: [] })
const currentTheme = ref('biomedical')
const flowEditorRef = ref(null)

// 节点类型配置
const nodeTypes = ref([
  { name: '蛋白质', color: '#4F46E5', shape: 'circle' },
  { name: '基因', color: '#059669', shape: 'rect' },
  { name: '化合物', color: '#DC2626', shape: 'diamond' },
  { name: '通路', color: '#7C3AED', shape: 'ellipse' }
])

// 分子数据
const loadWaterMolecule = () => {
  currentMolecule.value = {
    name: '水分子 (H₂O)',
    atomCount: 3,
    molecularWeight: '18.015',
    atoms: [
      { element: 'O', x: 0, y: 0, z: 0, radius: 1.5 },
      { element: 'H', x: 1.5, y: 1, z: 0, radius: 0.8 },
      { element: 'H', x: -1.5, y: 1, z: 0, radius: 0.8 }
    ],
    bonds: [
      { start: 0, end: 1, type: 'single' },
      { start: 0, end: 2, type: 'single' }
    ]
  }
  ElMessage.success('已加载水分子')
}

const loadMethaneMolecule = () => {
  currentMolecule.value = {
    name: '甲烷 (CH₄)',
    atomCount: 5,
    molecularWeight: '16.043',
    atoms: [
      { element: 'C', x: 0, y: 0, z: 0, radius: 1.2 },
      { element: 'H', x: 1.5, y: 1, z: 0.5, radius: 0.8 },
      { element: 'H', x: -1.5, y: 1, z: 0.5, radius: 0.8 },
      { element: 'H', x: 0.5, y: -1.5, z: 1, radius: 0.8 },
      { element: 'H', x: -0.5, y: -1.5, z: -1, radius: 0.8 }
    ],
    bonds: [
      { start: 0, end: 1, type: 'single' },
      { start: 0, end: 2, type: 'single' },
      { start: 0, end: 3, type: 'single' },
      { start: 0, end: 4, type: 'single' }
    ]
  }
  ElMessage.success('已加载甲烷分子')
}

const loadCaffeineMolecule = () => {
  currentMolecule.value = {
    name: '咖啡因 (C₈H₁₀N₄O₂)',
    atomCount: 24,
    molecularWeight: '194.194',
    atoms: [
      // 简化的咖啡因结构
      { element: 'N', x: 0, y: 0, z: 0, radius: 1.0 },
      { element: 'C', x: 2, y: 0, z: 0, radius: 1.0 },
      { element: 'C', x: 4, y: 2, z: 0, radius: 1.0 },
      { element: 'N', x: 2, y: 4, z: 0, radius: 1.0 },
      { element: 'C', x: 0, y: 2, z: 0, radius: 1.0 },
      { element: 'O', x: 6, y: 2, z: 0, radius: 1.2 },
      { element: 'H', x: -1, y: -1, z: 1, radius: 0.8 },
      { element: 'H', x: 1, y: -1, z: 1, radius: 0.8 }
    ],
    bonds: [
      { start: 0, end: 1, type: 'single' },
      { start: 1, end: 2, type: 'double' },
      { start: 2, end: 3, type: 'single' },
      { start: 3, end: 4, type: 'double' },
      { start: 4, end: 0, type: 'single' },
      { start: 2, end: 5, type: 'double' }
    ]
  }
  ElMessage.success('已加载咖啡因分子')
}

// 网络数据
const loadProteinNetwork = () => {
  currentNetwork.value = {
    nodes: [
      { id: 'p1', label: 'EGFR', nodeType: 'protein' },
      { id: 'p2', label: 'MAPK', nodeType: 'protein' },
      { id: 'p3', label: 'AKT', nodeType: 'protein' },
      { id: 'p4', label: 'mTOR', nodeType: 'protein' },
      { id: 'p5', label: 'p53', nodeType: 'protein' }
    ],
    edges: [
      { source: 'p1', target: 'p2', edgeType: 'interaction' },
      { source: 'p1', target: 'p3', edgeType: 'activation' },
      { source: 'p3', target: 'p4', edgeType: 'regulation' },
      { source: 'p2', target: 'p5', edgeType: 'inhibition' }
    ]
  }
  ElMessage.success('已加载蛋白质相互作用网络')
}

const loadGeneNetwork = () => {
  currentNetwork.value = {
    nodes: [
      { id: 'g1', label: 'KRAS', nodeType: 'gene' },
      { id: 'g2', label: 'TP53', nodeType: 'gene' },
      { id: 'g3', label: 'BRCA1', nodeType: 'gene' },
      { id: 'g4', label: 'MYC', nodeType: 'gene' },
      { id: 'p1', label: 'RAS蛋白', nodeType: 'protein' },
      { id: 'p2', label: 'p53蛋白', nodeType: 'protein' }
    ],
    edges: [
      { source: 'g1', target: 'p1', edgeType: 'regulation' },
      { source: 'g2', target: 'p2', edgeType: 'regulation' },
      { source: 'g3', target: 'g2', edgeType: 'interaction' },
      { source: 'g4', target: 'g1', edgeType: 'regulation' }
    ]
  }
  ElMessage.success('已加载基因调控网络')
}

const loadPathwayNetwork = () => {
  currentNetwork.value = {
    nodes: [
      { id: 'pw1', label: 'EGFR信号通路', nodeType: 'pathway' },
      { id: 'pw2', label: 'mTOR信号通路', nodeType: 'pathway' },
      { id: 'c1', label: 'ATP', nodeType: 'compound' },
      { id: 'c2', label: 'GTP', nodeType: 'compound' },
      { id: 'p1', label: 'EGFR', nodeType: 'protein' },
      { id: 'p2', label: 'mTOR', nodeType: 'protein' }
    ],
    edges: [
      { source: 'p1', target: 'pw1', edgeType: 'pathway' },
      { source: 'p2', target: 'pw2', edgeType: 'pathway' },
      { source: 'c1', target: 'p1', edgeType: 'binding' },
      { source: 'c2', target: 'p2', edgeType: 'binding' },
      { source: 'pw1', target: 'pw2', edgeType: 'interaction' }
    ]
  }
  ElMessage.success('已加载信号通路网络')
}

// 流程相关
const loadSampleFlow = () => {
  ElMessage.success('示例流程已加载到编辑器')
}

const clearFlow = () => {
  if (flowEditorRef.value) {
    flowEditorRef.value.clearCanvas()
  }
  ElMessage.info('画布已清空')
}

// 主题相关
const getThemeName = (theme) => {
  const names = {
    biomedical: '生物医学主题',
    dark: '暗色主题',
    minimal: '简洁主题',
    scientific: '科学期刊主题',
    'high-contrast': '高对比度主题'
  }
  return names[theme] || theme
}

const getThemeColors = () => {
  const theme = ThemeUtils.getTheme(currentTheme.value)
  return theme.colors?.biomedical || theme.colors || {}
}

const onThemeChange = (theme) => {
  ElMessage.info(`已切换到${getThemeName(theme)}`)
}

// 事件处理
const onAtomClick = (atom) => {
  ElMessage.info(`点击了原子: ${atom.element}`)
}

const onBondClick = (bond) => {
  ElMessage.info(`点击了化学键: ${bond.bondType}`)
}

const onMoleculeSelectionChange = (selection) => {
  console.log('分子选择变化:', selection)
}

const onNodeClick = (node) => {
  ElMessage.info(`点击了节点: ${node.label}`)
}

const onEdgeClick = (edge) => {
  ElMessage.info(`点击了连线: ${edge.edgeType}`)
}

const onNetworkSelectionChange = (nodes) => {
  ElMessage.info(`选中了 ${nodes.length} 个节点`)
}

const onFlowSave = (data) => {
  console.log('保存流程:', data)
  ElMessage.success('流程已保存')
}

const onFlowExport = (data) => {
  console.log('导出流程:', data)
  ElMessage.success('流程已导出')
}

const onFlowChange = (data) => {
  console.log('流程变化:', data)
}
</script>

<style scoped>
.visualization-demo {
  min-height: calc(100vh - 64px);
  background: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 36px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 18px;
  color: #64748b;
  margin: 0;
}

.demo-section {
  margin-bottom: 32px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  gap: 8px;
}

.demo-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
  align-items: start;
}

.demo-info {
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
}

.demo-info h4 {
  margin: 0 0 12px 0;
  color: #1e293b;
}

.demo-info ul {
  margin: 0;
  padding-left: 20px;
}

.demo-info li {
  margin-bottom: 8px;
  color: #475569;
}

.usage-content h3 {
  margin: 24px 0 12px 0;
  color: #1e293b;
}

.code-block {
  display: block;
  background: #1e293b;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Monaco', 'Consolas', monospace;
  white-space: pre;
  margin-bottom: 16px;
}

.theme-demo {
  padding: 20px;
}

.theme-preview h4 {
  margin: 0 0 16px 0;
  color: #1e293b;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.color-name {
  font-size: 12px;
  color: #64748b;
  text-transform: capitalize;
}

.api-content h4 {
  margin: 0 0 12px 0;
  color: #1e293b;
}

.api-content ul {
  margin: 0 0 24px 0;
  padding-left: 20px;
}

.api-content li {
  margin-bottom: 8px;
  color: #475569;
}

.api-content code {
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
}

@media (max-width: 768px) {
  .demo-content {
    grid-template-columns: 1fr;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: center;
  }
}
</style> 