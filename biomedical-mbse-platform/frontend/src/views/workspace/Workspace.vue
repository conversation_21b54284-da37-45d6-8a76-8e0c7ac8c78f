<template>
  <div class="workspace">
    <div class="container">
      <!-- 头部信息 -->
      <div class="workspace-header">
        <div class="header-content">
          <div class="title-section">
            <h1>生物医学MBSE建模工作区</h1>
            <p>系统工程建模与可视化平台</p>
          </div>
          <div class="stats-section">
            <div class="stat-card">
              <div class="stat-value">{{ projectCount }}</div>
              <div class="stat-label">活跃项目</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ modelCount }}</div>
              <div class="stat-label">模型数量</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ teamSize }}</div>
              <div class="stat-label">团队成员</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能模块 -->
      <div class="section">
        <div class="section-header">
          <div class="section-title">
            <div class="section-icon">🛠️</div>
            <div>
              <h3>建模工具</h3>
              <p>专业的生物医学系统建模工具集</p>
            </div>
          </div>
        </div>
        
        <div class="tools-grid">
          <div class="tool-card" @click="navigateTo('/projects')">
            <div class="tool-icon">📊</div>
            <div class="tool-content">
              <h4>项目管理</h4>
              <p>创建和管理生物医学建模项目</p>
            </div>
            <div class="tool-arrow">→</div>
          </div>
          
          <div class="tool-card" @click="navigateTo('/models')">
            <div class="tool-icon">🧬</div>
            <div class="tool-content">
              <h4>模型构建</h4>
              <p>系统架构与行为模型设计</p>
            </div>
            <div class="tool-arrow">→</div>
          </div>
          
          <div class="tool-card" @click="navigateTo('/analysis')">
            <div class="tool-icon">🔬</div>
            <div class="tool-content">
              <h4>分析验证</h4>
              <p>模型验证与性能分析</p>
            </div>
            <div class="tool-arrow">→</div>
          </div>
          
          <div class="tool-card" @click="navigateTo('/collaboration')">
            <div class="tool-icon">👥</div>
            <div class="tool-content">
              <h4>团队协作</h4>
              <p>多人协同建模与版本控制</p>
            </div>
            <div class="tool-arrow">→</div>
          </div>
        </div>
      </div>

      <!-- 可视化演示区域 -->
      <div class="section">
        <div class="section-header">
          <div class="section-title">
            <div class="section-icon">📊</div>
            <div>
              <h3>可视化演示</h3>
              <p>生物医学建模可视化组件展示</p>
            </div>
          </div>
        </div>
        
        <div class="viz-demo-grid">
          <!-- 3D分子查看器演示 -->
          <div class="viz-card">
            <div class="viz-card-header">
              <h4>3D分子结构查看器</h4>
              <el-button size="small" @click="loadSampleMolecule">加载示例分子</el-button>
            </div>
            <MolecularViewer 
              :molecule-data="sampleMolecule"
              :width="600"
              :height="400"
              @atom-click="handleAtomClick"
            />
          </div>
          
          <!-- 生物网络图演示 -->
          <div class="viz-card">
            <div class="viz-card-header">
              <h4>生物网络关系图</h4>
              <el-button size="small" @click="loadSampleNetwork">加载示例网络</el-button>
            </div>
            <NetworkGraph
              :data="sampleNetwork"
              :width="600"
              :height="400"
              @node-click="handleNodeClick"
              @selection-change="handleSelectionChange"
            />
          </div>
          
          <!-- 流程编辑器演示 -->
          <div class="viz-card full-width">
            <div class="viz-card-header">
              <h4>生物医学流程编辑器</h4>
              <el-button size="small" @click="createSampleFlow">创建示例流程</el-button>
            </div>
            <FlowEditor
              @save="handleFlowSave"
              @export="handleFlowExport"
              @change="handleFlowChange"
            />
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="section">
        <div class="section-header">
          <div class="section-title">
            <div class="section-icon">⏰</div>
            <div>
              <h3>最近活动</h3>
              <p>项目进展与团队动态</p>
            </div>
          </div>
          <el-button size="small">查看全部</el-button>
        </div>
        
        <div class="activity-list">
          <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
            <div class="activity-avatar">{{ activity.user.charAt(0) }}</div>
            <div class="activity-content">
              <div class="activity-text">
                <strong>{{ activity.user }}</strong> {{ activity.action }}
                <span class="activity-target">{{ activity.target }}</span>
              </div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
            <div class="activity-status" :class="activity.status"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  MolecularViewer, 
  NetworkGraph, 
  FlowEditor 
} from '@/components/visualization'

const router = useRouter()

// 统计数据
const projectCount = ref(12)
const modelCount = ref(45)
const teamSize = ref(8)

// 可视化演示数据
const sampleMolecule = ref({})
const sampleNetwork = ref({ 
  nodes: [
    { id: 'protein1', label: 'Protein A', nodeType: 'protein', x: 100, y: 100 },
    { id: 'protein2', label: 'Protein B', nodeType: 'protein', x: 200, y: 100 },
    { id: 'gene1', label: 'Gene X', nodeType: 'gene', x: 150, y: 200 },
    { id: 'compound1', label: 'Drug Y', nodeType: 'compound', x: 100, y: 300 }
  ], 
  edges: [
    { source: 'protein1', target: 'protein2', label: '相互作用', edgeType: 'interaction' },
    { source: 'gene1', target: 'protein1', label: '表达', edgeType: 'regulation' },
    { source: 'compound1', target: 'protein2', label: '结合', edgeType: 'binding' }
  ]
})

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    user: '张博士',
    action: '更新了模型',
    target: '蛋白质相互作用网络',
    time: '2分钟前',
    status: 'success'
  },
  {
    id: 2,
    user: '李研究员',
    action: '创建了新项目',
    target: '药物分子设计',
    time: '1小时前',
    status: 'info'
  },
  {
    id: 3,
    user: '王教授',
    action: '完成了验证',
    target: '代谢通路模型',
    time: '3小时前',
    status: 'success'
  },
  {
    id: 4,
    user: '陈工程师',
    action: '修复了问题',
    target: '数据导入模块',
    time: '1天前',
    status: 'warning'
  }
])

// 导航方法
const navigateTo = (path) => {
  router.push(path)
}

// 可视化组件事件处理
const loadSampleMolecule = () => {
  // 加载示例分子数据
  sampleMolecule.value = {
    name: '水分子 (H2O)',
    atomCount: 3,
    molecularWeight: '18.015',
    atoms: [
      { element: 'O', x: 0, y: 0, z: 0, radius: 1.5 },
      { element: 'H', x: 1.5, y: 1, z: 0, radius: 0.8 },
      { element: 'H', x: -1.5, y: 1, z: 0, radius: 0.8 }
    ],
    bonds: [
      { start: 0, end: 1, type: 'single' },
      { start: 0, end: 2, type: 'single' }
    ]
  }
  ElMessage.success('已加载水分子示例')
}

const loadSampleNetwork = () => {
  console.log('加载示例网络数据...')
  // 加载示例网络数据
  const networkData = {
    nodes: [
      { id: 'protein1', label: 'EGFR', nodeType: 'protein' },
      { id: 'protein2', label: 'MAPK', nodeType: 'protein' },
      { id: 'gene1', label: 'KRAS', nodeType: 'gene' },
      { id: 'compound1', label: 'ATP', nodeType: 'compound' },
      { id: 'pathway1', label: 'EGFR信号通路', nodeType: 'pathway' }
    ],
    edges: [
      { source: 'protein1', target: 'protein2', label: '激活', edgeType: 'interaction' },
      { source: 'gene1', target: 'protein1', label: '编码', edgeType: 'regulation' },
      { source: 'compound1', target: 'protein2', label: '磷酸化', edgeType: 'binding' },
      { source: 'protein2', target: 'pathway1', label: '参与', edgeType: 'pathway' }
    ]
  }
  
  console.log('网络数据:', networkData)
  sampleNetwork.value = networkData
  ElMessage.success(`已加载蛋白质相互作用网络示例 (${networkData.nodes.length}个节点, ${networkData.edges.length}条边)`)
}

const createSampleFlow = () => {
  ElMessage.success('流程编辑器已准备就绪，可以开始创建流程')
}

const handleAtomClick = (atom) => {
  ElMessage.info(`点击了原子: ${atom.element}`)
}

const handleNodeClick = (node) => {
  console.log('NetworkGraph节点点击事件:', node)
  ElMessage.info(`点击了节点: ${node.label || node.id} (类型: ${node.nodeType || '未知'})`)
}

const handleSelectionChange = (nodes) => {
  ElMessage.info(`选中了 ${nodes.length} 个节点`)
}

const handleFlowSave = (flowData) => {
  console.log('保存流程:', flowData)
  ElMessage.success('流程已保存')
}

const handleFlowExport = (flowData) => {
  console.log('导出流程:', flowData)
  ElMessage.success('流程已导出')
}

const handleFlowChange = (flowData) => {
  console.log('流程变化:', flowData)
}

onMounted(() => {
  // 页面加载完成后的初始化
  console.log('工作区页面加载完成')
})
</script>

<style scoped>
.workspace {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

.workspace-header {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
}

.title-section p {
  margin: 0;
  font-size: 16px;
  color: #64748b;
}

.stats-section {
  display: flex;
  gap: 24px;
}

.stat-card {
  text-align: center;
  min-width: 80px;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #3b82f6;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  margin-top: 4px;
}

.section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-icon {
  font-size: 24px;
}

.section-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.section-title p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.tool-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.tool-icon {
  font-size: 32px;
  margin-right: 16px;
}

.tool-content {
  flex: 1;
}

.tool-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.tool-content p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.tool-arrow {
  font-size: 18px;
  color: #3b82f6;
  font-weight: bold;
}

.viz-demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
  gap: 24px;
}

.viz-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.viz-card.full-width {
  grid-column: 1 / -1;
}

.viz-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.viz-card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.activity-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 14px;
  color: #1e293b;
  margin-bottom: 4px;
}

.activity-target {
  color: #3b82f6;
  font-weight: 500;
}

.activity-time {
  font-size: 12px;
  color: #64748b;
}

.activity-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.activity-status.success {
  background: #10b981;
}

.activity-status.info {
  background: #3b82f6;
}

.activity-status.warning {
  background: #f59e0b;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .tools-grid {
    grid-template-columns: 1fr;
  }
  
  .viz-demo-grid {
    grid-template-columns: 1fr;
  }
  
  .viz-card {
    min-width: 0;
  }
}
</style> 