#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端组件迁移脚本
===============

功能：
1. 迁移XML元数据系统的Vue.js前端组件
2. 创建生物医学专用前端组件
3. 整合现有UI设计
4. 设置前端开发环境

使用方法：
python migration_scripts/migrate_frontend_components.py
"""

import shutil
import os
import json
from pathlib import Path
import logging
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FrontendMigrator:
    """前端组件迁移器"""
    
    def __init__(self):
        self.source_base = Path("d:/xmltest/xml_metadata_system/frontend")
        self.target_base = Path("biomedical-mbse-platform/frontend")
        
        logger.info("初始化前端组件迁移器")
    
    def validate_source_frontend(self) -> bool:
        """验证源前端系统"""
        logger.info("🔍 验证XML元数据系统前端...")
        
        required_paths = [
            self.source_base / "src",
            self.source_base / "src/components",
            self.source_base / "src/views",
            self.source_base / "package.json"
        ]
        
        for path in required_paths:
            if not path.exists():
                logger.error(f"❌ 缺少必要路径: {path}")
                return False
        
        logger.info("✅ 前端系统验证通过")
        return True
    
    def create_frontend_structure(self):
        """创建前端目录结构"""
        logger.info("📁 创建前端目录结构...")
        
        frontend_dirs = [
            "src/components/biomedical",
            "src/components/xml_legacy", 
            "src/views/biomedical",
            "src/stores/biomedical",
            "src/api/biomedical",
            "src/assets/biomedical",
            "public"
        ]
        
        for dir_path in frontend_dirs:
            full_path = self.target_base / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"📁 创建前端目录: {full_path}")
    
    def migrate_vue_components(self):
        """迁移Vue组件"""
        logger.info("🔧 迁移Vue组件...")
        
        source_components = self.source_base / "src/components"
        target_legacy = self.target_base / "src/components/xml_legacy"
        
        if source_components.exists():
            if target_legacy.exists():
                shutil.rmtree(target_legacy)
            
            shutil.copytree(source_components, target_legacy)
            logger.info(f"✅ Vue组件迁移完成: {source_components} → {target_legacy}")
            
            # 分析组件并创建生物医学适配版本
            self.create_biomedical_components()
    
    def create_biomedical_components(self):
        """创建生物医学专用组件"""
        logger.info("🧬 创建生物医学专用组件...")
        
        # 创建生物医学仪表板
        self.create_biomedical_dashboard()
        
        # 创建分子可视化组件
        self.create_molecular_viewer()
        
        # 创建工具管理组件
        self.create_tool_manager()
        
        # 创建数据标准组件
        self.create_data_standards_viewer()
        
        logger.info("✅ 生物医学组件创建完成")
    
    def create_biomedical_dashboard(self):
        """创建生物医学仪表板"""
        dashboard_file = self.target_base / "src/components/biomedical/BiomedicalDashboard.vue"
        
        dashboard_code = '''<template>
  <div class="biomedical-dashboard">
    <el-container class="dashboard-container">
      <!-- 顶部工具栏 -->
      <el-header class="dashboard-header">
        <div class="header-title">
          <h2>🧬 生物医学MBSE建模平台</h2>
        </div>
        <div class="header-tools">
          <el-button-group>
            <el-button type="primary" @click="showMolecularViewer">
              分子可视化
            </el-button>
            <el-button type="success" @click="showToolChain">
              工具链管理
            </el-button>
            <el-button type="info" @click="showDataStandards">
              数据标准
            </el-button>
          </el-button-group>
        </div>
      </el-header>
      
      <!-- 主内容区域 -->
      <el-container>
        <!-- 左侧导航 -->
        <el-aside width="250px" class="sidebar">
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item index="molecular">
              <el-icon><Molecule /></el-icon>
              <span>分子建模</span>
            </el-menu-item>
            <el-menu-item index="pathway">
              <el-icon><Share /></el-icon>
              <span>通路分析</span>
            </el-menu-item>
            <el-menu-item index="statistics">
              <el-icon><DataAnalysis /></el-icon>
              <span>统计分析</span>
            </el-menu-item>
            <el-menu-item index="tools">
              <el-icon><Tools /></el-icon>
              <span>工具管理</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <!-- 主工作区 -->
        <el-main class="main-workspace">
          <component 
            :is="currentComponent" 
            :data="workspaceData"
            @update="handleDataUpdate"
          />
        </el-main>
        
        <!-- 右侧面板 -->
        <el-aside width="300px" class="right-panel">
          <!-- AI推荐 -->
          <RecommendationPanel 
            :recommendations="aiRecommendations"
            @apply="applyRecommendation"
          />
          
          <!-- 性能监控 -->
          <PerformancePanel 
            :metrics="performanceMetrics"
          />
        </el-aside>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import RecommendationPanel from './RecommendationPanel.vue'
import PerformancePanel from './PerformancePanel.vue'

// 响应式数据
const activeMenu = ref('molecular')
const currentComponent = ref('MolecularWorkspace')
const workspaceData = reactive({})
const aiRecommendations = ref([])
const performanceMetrics = ref({})

// 计算属性
const dashboardTitle = computed(() => {
  const titles = {
    molecular: '分子建模工作区',
    pathway: '通路分析工作区', 
    statistics: '统计分析工作区',
    tools: '工具管理工作区'
  }
  return titles[activeMenu.value] || '生物医学MBSE平台'
})

// 方法
const handleMenuSelect = (index) => {
  activeMenu.value = index
  currentComponent.value = getComponentForMenu(index)
  loadWorkspaceData(index)
}

const getComponentForMenu = (menuIndex) => {
  const components = {
    molecular: 'MolecularWorkspace',
    pathway: 'PathwayWorkspace',
    statistics: 'StatisticsWorkspace', 
    tools: 'ToolManagementWorkspace'
  }
  return components[menuIndex]
}

const loadWorkspaceData = async (workspace) => {
  try {
    // 调用后端API加载工作区数据
    const response = await fetch(`/api/v1/biomedical/workspace/${workspace}`)
    const data = await response.json()
    Object.assign(workspaceData, data)
    
    // 获取AI推荐
    loadAIRecommendations(workspace)
  } catch (error) {
    ElMessage.error('加载工作区数据失败')
  }
}

const loadAIRecommendations = async (workspace) => {
  try {
    const response = await fetch('/api/v1/xml-system/test-recommendation', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        workspace_type: workspace,
        analysis_type: 'biomedical',
        context: workspaceData
      })
    })
    const data = await response.json()
    aiRecommendations.value = data.recommended_tools || []
  } catch (error) {
    console.error('获取AI推荐失败:', error)
  }
}

const applyRecommendation = (recommendation) => {
  ElMessage.success(`应用推荐: ${recommendation.name}`)
  // 实现推荐应用逻辑
}

const handleDataUpdate = (newData) => {
  Object.assign(workspaceData, newData)
  // 触发AI重新分析
  loadAIRecommendations(activeMenu.value)
}

// 生命周期
onMounted(() => {
  loadWorkspaceData('molecular')
  startPerformanceMonitoring()
})

const startPerformanceMonitoring = () => {
  setInterval(async () => {
    try {
      const response = await fetch('/api/v1/biomedical/performance')
      const metrics = await response.json()
      performanceMetrics.value = metrics
    } catch (error) {
      console.error('性能监控失败:', error)
    }
  }, 5000)
}
</script>

<style scoped>
.biomedical-dashboard {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-container {
  height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dashboard-header {
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
}

.sidebar {
  background: #2c3e50;
  color: white;
}

.sidebar-menu {
  background: transparent;
  border: none;
}

.main-workspace {
  background: #f8f9fa;
  padding: 20px;
}

.right-panel {
  background: white;
  border-left: 1px solid #e0e0e0;
  padding: 15px;
}
</style>'''
        
        with open(dashboard_file, 'w', encoding='utf-8') as f:
            f.write(dashboard_code)
        
        logger.info("🎨 创建生物医学仪表板组件")
    
    def create_molecular_viewer(self):
        """创建分子可视化组件"""
        viewer_file = self.target_base / "src/components/biomedical/MolecularViewer.vue"
        
        viewer_code = '''<template>
  <div class="molecular-viewer">
    <div class="viewer-controls">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="selectedProtein" placeholder="选择蛋白质">
            <el-option
              v-for="protein in availableProteins"
              :key="protein.id"
              :label="protein.name"
              :value="protein.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="viewMode" placeholder="显示模式">
            <el-option label="线框模式" value="wireframe" />
            <el-option label="球棍模式" value="ball_stick" />
            <el-option label="表面模式" value="surface" />
            <el-option label="卡通模式" value="cartoon" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="loadStructure">
            <el-icon><View /></el-icon>
            加载结构
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="analyzeStructure">
            <el-icon><DataAnalysis /></el-icon>
            结构分析
          </el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 3D可视化区域 -->
    <div class="viewer-3d" ref="viewer3d">
      <!-- PyMOL或其他分子可视化工具的嵌入区域 -->
      <div v-if="!structureLoaded" class="loading-placeholder">
        <el-icon class="loading-icon" size="48"><Loading /></el-icon>
        <p>准备分子可视化环境...</p>
      </div>
      <div v-else class="structure-display">
        <!-- 实际的3D结构显示 -->
        <canvas ref="molecularCanvas" />
      </div>
    </div>
    
    <!-- 分析结果面板 -->
    <div class="analysis-panel" v-if="analysisResults">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="PDB ID">
              {{ analysisResults.pdb_id }}
            </el-descriptions-item>
            <el-descriptions-item label="分子量">
              {{ analysisResults.molecular_weight }}
            </el-descriptions-item>
            <el-descriptions-item label="氨基酸数">
              {{ analysisResults.amino_acid_count }}
            </el-descriptions-item>
            <el-descriptions-item label="二级结构">
              {{ analysisResults.secondary_structure }}
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="结构特征" name="features">
          <StructureFeatures :features="analysisResults.features" />
        </el-tab-pane>
        <el-tab-pane label="相互作用" name="interactions">
          <InteractionAnalysis :interactions="analysisResults.interactions" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const selectedProtein = ref('')
const viewMode = ref('cartoon')
const structureLoaded = ref(false)
const analysisResults = ref(null)
const activeTab = ref('basic')

const availableProteins = ref([
  { id: '1ABC', name: '血红蛋白 (1ABC)' },
  { id: '2XYZ', name: '胰岛素 (2XYZ)' },
  { id: '3DEF', name: '溶菌酶 (3DEF)' }
])

// 模板引用
const viewer3d = ref(null)
const molecularCanvas = ref(null)

// 方法
const loadStructure = async () => {
  if (!selectedProtein.value) {
    ElMessage.warning('请先选择要加载的蛋白质')
    return
  }
  
  try {
    structureLoaded.value = false
    
    // 调用后端API加载蛋白质结构
    const response = await fetch('/api/v1/biomedical/molecular/load', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        protein_id: selectedProtein.value,
        view_mode: viewMode.value
      })
    })
    
    const data = await response.json()
    
    if (data.status === 'success') {
      await initializeMolecularViewer(data.structure_data)
      structureLoaded.value = true
      ElMessage.success('蛋白质结构加载成功')
    }
  } catch (error) {
    ElMessage.error('加载蛋白质结构失败')
    console.error(error)
  }
}

const initializeMolecularViewer = async (structureData) => {
  // 初始化分子可视化（这里需要集成PyMOL或其他可视化库）
  await nextTick()
  
  if (molecularCanvas.value) {
    // 设置canvas
    const canvas = molecularCanvas.value
    canvas.width = 800
    canvas.height = 600
    
    // 这里应该集成实际的分子可视化库
    // 例如：PyMOL、ChimeraX、或基于WebGL的库
    renderMolecularStructure(canvas, structureData)
  }
}

const renderMolecularStructure = (canvas, data) => {
  // 渲染分子结构的实际实现
  const ctx = canvas.getContext('2d')
  ctx.fillStyle = '#f0f8ff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  // 绘制示例蛋白质结构
  ctx.fillStyle = '#ff6b6b'
  ctx.beginPath()
  ctx.arc(400, 300, 50, 0, Math.PI * 2)
  ctx.fill()
  
  ctx.fillStyle = '#4ecdc4'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('蛋白质结构可视化', 400, 350)
  ctx.fillText(`ID: ${selectedProtein.value}`, 400, 370)
}

const analyzeStructure = async () => {
  if (!structureLoaded.value) {
    ElMessage.warning('请先加载蛋白质结构')
    return
  }
  
  try {
    const response = await fetch('/api/v1/biomedical/molecular/analyze', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        protein_id: selectedProtein.value,
        analysis_type: 'comprehensive'
      })
    })
    
    const data = await response.json()
    analysisResults.value = data.results
    
    ElMessage.success('结构分析完成')
  } catch (error) {
    ElMessage.error('结构分析失败')
    console.error(error)
  }
}

onMounted(() => {
  // 初始化分子可视化环境
  initializeViewer()
})

const initializeViewer = () => {
  // 检查可用的分子可视化工具
  checkAvailableTools()
}

const checkAvailableTools = async () => {
  try {
    const response = await fetch('/api/v1/biomedical/tools')
    const data = await response.json()
    
    const molecularTools = data.tools.filter(
      tool => tool.category === 'molecular_visualization'
    )
    
    if (molecularTools.length === 0) {
      ElMessage.warning('未检测到分子可视化工具，请安装PyMOL或ChimeraX')
    }
  } catch (error) {
    console.error('检查工具失败:', error)
  }
}
</script>

<style scoped>
.molecular-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 20px;
}

.viewer-controls {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.viewer-3d {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-placeholder {
  text-align: center;
  color: #666;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.structure-display {
  width: 100%;
  height: 100%;
  position: relative;
}

.analysis-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
}
</style>'''
        
        with open(viewer_file, 'w', encoding='utf-8') as f:
            f.write(viewer_code)
        
        logger.info("🔬 创建分子可视化组件")
    
    def create_tool_manager(self):
        """创建工具管理组件"""
        tool_file = self.target_base / "src/components/biomedical/ToolManager.vue"
        
        tool_code = '''<template>
  <div class="tool-manager">
    <!-- 工具概览 -->
    <div class="tools-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="tool-stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ availableTools.length }}</div>
              <div class="stat-label">可用工具</div>
            </div>
            <el-icon class="stat-icon"><Tools /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="tool-stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ activeTools.length }}</div>
              <div class="stat-label">运行中</div>
            </div>
            <el-icon class="stat-icon"><Loading /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="tool-stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ toolChains.length }}</div>
              <div class="stat-label">工具链</div>
            </div>
            <el-icon class="stat-icon"><Share /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="tool-stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ completedJobs }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <el-icon class="stat-icon"><SuccessFilled /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 工具列表 -->
    <div class="tools-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>177个生物医学工具</span>
            <el-button type="primary" @click="refreshTools">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>
        
        <!-- 工具过滤 -->
        <div class="tools-filter">
          <el-row :gutter="15">
            <el-col :span="8">
              <el-select v-model="filterCategory" placeholder="按类别筛选">
                <el-option label="全部" value="all" />
                <el-option label="分子可视化" value="molecular_visualization" />
                <el-option label="统计分析" value="statistical_analysis" />
                <el-option label="序列分析" value="sequence_analysis" />
                <el-option label="药物发现" value="drug_discovery" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select v-model="filterStatus" placeholder="按状态筛选">
                <el-option label="全部" value="all" />
                <el-option label="可用" value="available" />
                <el-option label="运行中" value="running" />
                <el-option label="离线" value="offline" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索工具名称"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
          </el-row>
        </div>
        
        <!-- 工具表格 -->
        <el-table
          :data="filteredTools"
          style="width: 100%"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="工具名称" width="150">
            <template #default="scope">
              <div class="tool-name">
                <el-icon class="tool-icon">
                  <component :is="getToolIcon(scope.row.category)" />
                </el-icon>
                {{ scope.row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="类别" width="150">
            <template #default="scope">
              <el-tag :type="getCategoryTagType(scope.row.category)">
                {{ getCategoryLabel(scope.row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 'available' ? 'success' : 
                       scope.row.status === 'running' ? 'warning' : 'danger'"
              >
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button-group>
                <el-button
                  size="small"
                  type="primary"
                  @click="launchTool(scope.row)"
                  :disabled="scope.row.status !== 'available'"
                >
                  启动
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  @click="configTool(scope.row)"
                >
                  配置
                </el-button>
                <el-button
                  size="small"
                  type="success"
                  @click="addToChain(scope.row)"
                >
                  加入链
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 工具链管理 -->
    <div class="toolchain-management">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>工具链管理</span>
            <el-button type="success" @click="createNewChain">
              <el-icon><Plus /></el-icon>
              新建工具链
            </el-button>
          </div>
        </template>
        
        <div class="toolchain-list">
          <el-row :gutter="20">
            <el-col
              :span="8"
              v-for="chain in toolChains"
              :key="chain.id"
            >
              <el-card class="toolchain-card">
                <div class="chain-header">
                  <h4>{{ chain.name }}</h4>
                  <el-tag type="info">{{ chain.tools.length }}个工具</el-tag>
                </div>
                <div class="chain-tools">
                  <div
                    v-for="(tool, index) in chain.tools"
                    :key="tool.id"
                    class="chain-tool"
                  >
                    <span>{{ index + 1 }}. {{ tool.name }}</span>
                    <el-icon v-if="index < chain.tools.length - 1">
                      <ArrowRight />
                    </el-icon>
                  </div>
                </div>
                <div class="chain-actions">
                  <el-button size="small" type="primary" @click="executeChain(chain)">
                    执行
                  </el-button>
                  <el-button size="small" @click="editChain(chain)">
                    编辑
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const availableTools = ref([])
const activeTools = ref([])
const toolChains = ref([])
const completedJobs = ref(0)
const filterCategory = ref('all')
const filterStatus = ref('all')
const searchKeyword = ref('')
const selectedTools = ref([])

// 计算属性
const filteredTools = computed(() => {
  let tools = availableTools.value
  
  // 按类别过滤
  if (filterCategory.value !== 'all') {
    tools = tools.filter(tool => tool.category === filterCategory.value)
  }
  
  // 按状态过滤
  if (filterStatus.value !== 'all') {
    tools = tools.filter(tool => tool.status === filterStatus.value)
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    tools = tools.filter(tool =>
      tool.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  return tools
})

// 方法
const refreshTools = async () => {
  try {
    const response = await fetch('/api/v1/biomedical/tools')
    const data = await response.json()
    availableTools.value = data.tools
    
    // 获取活跃工具
    const activeResponse = await fetch('/api/v1/biomedical/tools/active')
    const activeData = await activeResponse.json()
    activeTools.value = activeData.tools
    
    ElMessage.success('工具列表已刷新')
  } catch (error) {
    ElMessage.error('刷新工具列表失败')
  }
}

const launchTool = async (tool) => {
  try {
    const response = await fetch(`/api/v1/biomedical/tools/${tool.id}/launch`, {
      method: 'POST'
    })
    
    if (response.ok) {
      ElMessage.success(`${tool.name} 启动成功`)
      tool.status = 'running'
      activeTools.value.push(tool)
    }
  } catch (error) {
    ElMessage.error(`启动 ${tool.name} 失败`)
  }
}

const configTool = (tool) => {
  ElMessageBox.prompt(`配置 ${tool.name}`, '工具配置', {
    confirmButtonText: '保存',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    ElMessage.success('配置已保存')
  })
}

const addToChain = (tool) => {
  // 添加工具到工具链的逻辑
  ElMessage.success(`${tool.name} 已添加到工具链`)
}

const createNewChain = () => {
  ElMessageBox.prompt('请输入工具链名称', '新建工具链', {
    confirmButtonText: '创建',
    cancelButtonText: '取消'
  }).then(({ value }) => {
    const newChain = {
      id: Date.now(),
      name: value,
      tools: [],
      status: 'draft'
    }
    toolChains.value.push(newChain)
    ElMessage.success('工具链创建成功')
  })
}

const executeChain = async (chain) => {
  try {
    const response = await fetch(`/api/v1/biomedical/toolchains/${chain.id}/execute`, {
      method: 'POST'
    })
    
    if (response.ok) {
      ElMessage.success(`工具链 ${chain.name} 开始执行`)
    }
  } catch (error) {
    ElMessage.error('执行工具链失败')
  }
}

const editChain = (chain) => {
  // 打开工具链编辑器
  ElMessage.info('打开工具链编辑器')
}

const handleSelectionChange = (selection) => {
  selectedTools.value = selection
}

// 工具函数
const getToolIcon = (category) => {
  const icons = {
    molecular_visualization: 'View',
    statistical_analysis: 'DataAnalysis',
    sequence_analysis: 'List',
    drug_discovery: 'Medicine'
  }
  return icons[category] || 'Tools'
}

const getCategoryTagType = (category) => {
  const types = {
    molecular_visualization: 'primary',
    statistical_analysis: 'success',
    sequence_analysis: 'warning',
    drug_discovery: 'danger'
  }
  return types[category] || 'info'
}

const getCategoryLabel = (category) => {
  const labels = {
    molecular_visualization: '分子可视化',
    statistical_analysis: '统计分析',
    sequence_analysis: '序列分析',
    drug_discovery: '药物发现'
  }
  return labels[category] || category
}

const getStatusLabel = (status) => {
  const labels = {
    available: '可用',
    running: '运行中',
    offline: '离线'
  }
  return labels[status] || status
}

// 生命周期
onMounted(() => {
  refreshTools()
  loadToolChains()
})

const loadToolChains = async () => {
  try {
    const response = await fetch('/api/v1/biomedical/toolchains')
    const data = await response.json()
    toolChains.value = data.toolchains || []
  } catch (error) {
    console.error('加载工具链失败:', error)
  }
}
</script>

<style scoped>
.tool-manager {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tools-overview {
  margin-bottom: 20px;
}

.tool-stat-card {
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  color: #7f8c8d;
  margin-top: 5px;
}

.stat-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 2em;
  color: #ecf0f1;
  z-index: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tools-filter {
  margin-bottom: 20px;
}

.tool-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-icon {
  color: #3498db;
}

.toolchain-card {
  margin-bottom: 15px;
}

.chain-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chain-header h4 {
  margin: 0;
}

.chain-tools {
  margin-bottom: 15px;
}

.chain-tool {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0;
  font-size: 14px;
}

.chain-actions {
  display: flex;
  gap: 10px;
}
</style>'''
        
        with open(tool_file, 'w', encoding='utf-8') as f:
            f.write(tool_code)
        
        logger.info("🔧 创建工具管理组件")
    
    def create_data_standards_viewer(self):
        """创建数据标准查看器"""
        standards_file = self.target_base / "src/components/biomedical/DataStandardsViewer.vue"
        
        # 这里省略具体实现，类似上面的模式
        with open(standards_file, 'w', encoding='utf-8') as f:
            f.write('<!-- 数据标准查看器组件 -->')
        
        logger.info("📊 创建数据标准查看器组件")
    
    def create_package_json(self):
        """创建前端package.json"""
        logger.info("📦 创建前端package.json...")
        
        package_json = {
            "name": "biomedical-mbse-frontend",
            "version": "1.0.0",
            "description": "生物医学MBSE建模平台前端",
            "type": "module",
            "scripts": {
                "dev": "vite",
                "build": "vite build",
                "preview": "vite preview"
            },
            "dependencies": {
                "vue": "^3.3.4",
                "vue-router": "^4.2.4",
                "pinia": "^2.1.6",
                "element-plus": "^2.3.8",
                "@element-plus/icons-vue": "^2.1.0",
                "axios": "^1.4.0",
                "echarts": "^5.4.3",
                "three": "^0.154.0"
            },
            "devDependencies": {
                "@vitejs/plugin-vue": "^4.2.3",
                "vite": "^4.4.5",
                "typescript": "^5.0.2",
                "vue-tsc": "^1.8.5"
            }
        }
        
        package_file = self.target_base / "package.json"
        with open(package_file, 'w', encoding='utf-8') as f:
            json.dump(package_json, f, indent=2, ensure_ascii=False)
        
        logger.info("✅ package.json创建完成")
    
    def create_vite_config(self):
        """创建Vite配置文件"""
        vite_config = '''import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
})'''
        
        vite_file = self.target_base / "vite.config.js"
        with open(vite_file, 'w', encoding='utf-8') as f:
            f.write(vite_config)
        
        logger.info("⚡ Vite配置文件创建完成")
    
    def run_frontend_migration(self):
        """执行前端迁移"""
        logger.info("🎨 开始前端组件迁移...")
        
        try:
            # 1. 验证源系统
            if not self.validate_source_frontend():
                logger.error("❌ 前端系统验证失败")
                return False
            
            # 2. 创建前端结构
            self.create_frontend_structure()
            
            # 3. 迁移Vue组件
            self.migrate_vue_components()
            
            # 4. 创建配置文件
            self.create_package_json()
            self.create_vite_config()
            
            logger.info("🎉 前端迁移完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 前端迁移失败: {e}")
            return False

def main():
    """主执行函数"""
    print("🎨 开始前端组件迁移...")
    
    migrator = FrontendMigrator()
    success = migrator.run_frontend_migration()
    
    if success:
        print("\n🎉 前端迁移成功完成！")
        print("📋 下一步操作：")
        print("1. cd biomedical-mbse-platform/frontend")
        print("2. npm install")
        print("3. npm run dev")
    else:
        print("\n❌ 前端迁移失败，请查看日志")
    
    return success

if __name__ == "__main__":
    main() 