# 动态领域示例 - 最小依赖包配置

# 核心依赖包 - 必需
asyncpg==0.30.0          # PostgreSQL异步驱动
bcrypt==4.3.0             # 密码加密
pydantic==2.11.7          # 数据验证和序列化

# Pydantic依赖包 - 自动安装
annotated-types==0.7.0
pydantic-core==2.33.2
typing-extensions==4.14.0
typing-inspection==0.4.1

# 内置包（无需安装）
# asyncio - 异步编程支持
# json - JSON数据处理  
# logging - 日志记录
# datetime - 时间处理
# typing - 类型注解
# dataclasses - 数据类支持
# enum - 枚举类型
# uuid - UUID生成 