# 生物医学MBSE建模平台 - Python依赖包

# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.11.7

# 数据库和缓存
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
asyncpg==0.30.0
redis==5.0.1
neo4j==5.14.0
influxdb-client==1.39.0

# 异步支持
asyncio==3.4.3
aiofiles==23.2.1
httpx==0.25.2

# 数据处理
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4

# 生物医学数据处理
biopython==1.81
# libsbml==5.20.2  # 需要单独安装
# cellml==0.1.0    # 需要单独安装

# 机器学习
scikit-learn==1.3.2
torch==2.1.1
transformers==4.36.0

# 可视化和图形
matplotlib==3.8.2
plotly==5.17.0
networkx==3.2.1

# 工具集成（可选）
# pymol==2.5.0    # 需要单独安装
# rdkit==2023.9.1 # 需要单独安装

# 开发和测试
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0

# XML和JSON处理
lxml==4.9.3
xmltodict==0.13.0
pyyaml==6.0.1

# 文件处理
openpyxl==3.1.2
xlsxwriter==3.1.9

# Web相关
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.3.0

# 动态领域架构支持 (新增)
# asyncpg - PostgreSQL异步驱动，支持动态Schema操作
# bcrypt - 密码加密，用于安全领域管理
# pydantic - 数据验证和序列化，用于Element模型定义 