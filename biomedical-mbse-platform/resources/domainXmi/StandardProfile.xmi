<?xml version="1.0" encoding="UTF-8"?>
<xmi:XMI xmlns:xmi="http://www.omg.org/spec/XMI/20131001" xmlns:uml="http://www.omg.org/spec/UML/20161101" xmlns:mofext="http://www.omg.org/spec/MOF/20131001">
	<uml:Profile xmi:type="uml:Profile" xmi:id="_0" name="StandardProfile" metamodelReference="_packageImport.0" URI="http://www.omg.org/spec/UML/20161101/StandardProfile">
		<packageImport xmi:type="uml:PackageImport" xmi:id="_packageImport.0">
			<importedPackage href="http://www.omg.org/spec/UML/20161101/UML.xmi#_0"/>
		</packageImport>
		<packagedElement xmi:type="uml:Extension" xmi:id="Abstraction_Derive" name="Abstraction_Derive" memberEnd="Abstraction_Derive-extension_Derive Derive-base_Abstraction">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Abstraction_Derive-extension_Derive" name="extension_Derive" type="Derive" aggregation="composite" association="Abstraction_Derive">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Abstraction_Derive-extension_Derive-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Abstraction_Refine" name="Abstraction_Refine" memberEnd="Abstraction_Refine-extension_Refine Refine-base_Abstraction">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Abstraction_Refine-extension_Refine" name="extension_Refine" type="Refine" aggregation="composite" association="Abstraction_Refine">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Abstraction_Refine-extension_Refine-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Abstraction_Trace" name="Abstraction_Trace" memberEnd="Abstraction_Trace-extension_Trace Trace-base_Abstraction">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Abstraction_Trace-extension_Trace" name="extension_Trace" type="Trace" aggregation="composite" association="Abstraction_Trace">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Abstraction_Trace-extension_Trace-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Artifact_Document" name="Artifact_Document" memberEnd="Artifact_Document-extension_Document Document-base_Artifact">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Artifact_Document-extension_Document" name="extension_Document" type="Document" aggregation="composite" association="Artifact_Document">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Artifact_Document-extension_Document-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Artifact_Executable" name="Artifact_Executable" memberEnd="Artifact_Executable-extension_Executable Executable-base_Artifact">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Artifact_Executable-extension_Executable" name="extension_Executable" type="Executable" aggregation="composite" association="Artifact_Executable">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Artifact_Executable-extension_Executable-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Artifact_File" name="Artifact_File" memberEnd="Artifact_File-extension_File File-base_Artifact">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Artifact_File-extension_File" name="extension_File" type="File" aggregation="composite" association="Artifact_File">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Artifact_File-extension_File-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Artifact_Library" name="Artifact_Library" memberEnd="Artifact_Library-extension_Library Library-base_Artifact">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Artifact_Library-extension_Library" name="extension_Library" type="Library" aggregation="composite" association="Artifact_Library">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Artifact_Library-extension_Library-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Artifact_Script" name="Artifact_Script" memberEnd="Artifact_Script-extension_Script Script-base_Artifact">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Artifact_Script-extension_Script" name="extension_Script" type="Script" aggregation="composite" association="Artifact_Script">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Artifact_Script-extension_Script-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Artifact_Source" name="Artifact_Source" memberEnd="Artifact_Source-extension_Source Source-base_Artifact">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Artifact_Source-extension_Source" name="extension_Source" type="Source" aggregation="composite" association="Artifact_Source">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Artifact_Source-extension_Source-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="BehavioralFeature_Create" name="BehavioralFeature_Create" memberEnd="BehavioralFeature_Create-extension_Create Create-base_BehavioralFeature">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="BehavioralFeature_Create-extension_Create" name="extension_Create" type="Create" aggregation="composite" association="BehavioralFeature_Create">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="BehavioralFeature_Create-extension_Create-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="BehavioralFeature_Destroy" name="BehavioralFeature_Destroy" memberEnd="BehavioralFeature_Destroy-extension_Destroy Destroy-base_BehavioralFeature">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="BehavioralFeature_Destroy-extension_Destroy" name="extension_Destroy" type="Destroy" aggregation="composite" association="BehavioralFeature_Destroy">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="BehavioralFeature_Destroy-extension_Destroy-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Class_Auxiliary" name="Class_Auxiliary" memberEnd="Class_Auxiliary-extension_Auxiliary Auxiliary-base_Class">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Class_Auxiliary-extension_Auxiliary" name="extension_Auxiliary" type="Auxiliary" aggregation="composite" association="Class_Auxiliary">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Class_Auxiliary-extension_Auxiliary-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Class_Focus" name="Class_Focus" memberEnd="Class_Focus-extension_Focus Focus-base_Class">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Class_Focus-extension_Focus" name="extension_Focus" type="Focus" aggregation="composite" association="Class_Focus">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Class_Focus-extension_Focus-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Class_ImplementationClass" name="Class_ImplementationClass" memberEnd="Class_ImplementationClass-extension_ImplementationClass ImplementationClass-base_Class">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Class_ImplementationClass-extension_ImplementationClass" name="extension_ImplementationClass" type="ImplementationClass" aggregation="composite" association="Class_ImplementationClass">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Class_ImplementationClass-extension_ImplementationClass-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Class_Metaclass" name="Class_Metaclass" memberEnd="Class_Metaclass-extension_Metaclass Metaclass-base_Class">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Class_Metaclass-extension_Metaclass" name="extension_Metaclass" type="Metaclass" aggregation="composite" association="Class_Metaclass">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Class_Metaclass-extension_Metaclass-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Class_Type" name="Class_Type" memberEnd="Class_Type-extension_Type Type-base_Class">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Class_Type-extension_Type" name="extension_Type" type="Type" aggregation="composite" association="Class_Type">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Class_Type-extension_Type-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Class_Utility" name="Class_Utility" memberEnd="Class_Utility-extension_Utility Utility-base_Class">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Class_Utility-extension_Utility" name="extension_Utility" type="Utility" aggregation="composite" association="Class_Utility">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Class_Utility-extension_Utility-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Classifier_Realization" name="Classifier_Realization" memberEnd="Classifier_Realization-extension_Realization Realization-base_Classifier">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Classifier_Realization-extension_Realization" name="extension_Realization" type="Realization" aggregation="composite" association="Classifier_Realization">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Classifier_Realization-extension_Realization-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Classifier_Specification" name="Classifier_Specification" memberEnd="Classifier_Specification-extension_Specification Specification-base_Classifier">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Classifier_Specification-extension_Specification" name="extension_Specification" type="Specification" aggregation="composite" association="Classifier_Specification">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Classifier_Specification-extension_Specification-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Component_Entity" name="Component_Entity" memberEnd="Component_Entity-extension_Entity Entity-base_Component">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Component_Entity-extension_Entity" name="extension_Entity" type="Entity" aggregation="composite" association="Component_Entity">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Component_Entity-extension_Entity-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Component_Implement" name="Component_Implement" memberEnd="Component_Implement-extension_Implement Implement-base_Component">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Component_Implement-extension_Implement" name="extension_Implement" type="Implement" aggregation="composite" association="Component_Implement">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Component_Implement-extension_Implement-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Component_Process" name="Component_Process" memberEnd="Component_Process-extension_Process Process-base_Component">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Component_Process-extension_Process" name="extension_Process" type="Process" aggregation="composite" association="Component_Process">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Component_Process-extension_Process-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Component_Service" name="Component_Service" memberEnd="Component_Service-extension_Service Service-base_Component">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Component_Service-extension_Service" name="extension_Service" type="Service" aggregation="composite" association="Component_Service">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Component_Service-extension_Service-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Component_Subsystem" name="Component_Subsystem" memberEnd="Component_Subsystem-extension_Subsystem Subsystem-base_Component">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Component_Subsystem-extension_Subsystem" name="extension_Subsystem" type="Subsystem" aggregation="composite" association="Component_Subsystem">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Component_Subsystem-extension_Subsystem-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Package_Framework" name="Package_Framework" memberEnd="Package_Framework-extension_Framework Framework-base_Package">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Package_Framework-extension_Framework" name="extension_Framework" type="Framework" aggregation="composite" association="Package_Framework">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Package_Framework-extension_Framework-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Package_ModelLibrary" name="Package_ModelLibrary" memberEnd="Package_ModelLibrary-extension_ModelLibrary ModelLibrary-base_Package">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Package_ModelLibrary-extension_ModelLibrary" name="extension_ModelLibrary" type="ModelLibrary" aggregation="composite" association="Package_ModelLibrary">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Package_ModelLibrary-extension_ModelLibrary-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Usage_Call" name="Usage_Call" memberEnd="Usage_Call-extension_Call Call-base_Usage">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Usage_Call-extension_Call" name="extension_Call" type="Call" aggregation="composite" association="Usage_Call">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Usage_Call-extension_Call-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Usage_Create" name="Usage_Create" memberEnd="Usage_Create-extension_Create Create-base_Usage">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Usage_Create-extension_Create" name="extension_Create" type="Create" aggregation="composite" association="Usage_Create">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Usage_Create-extension_Create-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Usage_Instantiate" name="Usage_Instantiate" memberEnd="Usage_Instantiate-extension_Instantiate Instantiate-base_Usage">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Usage_Instantiate-extension_Instantiate" name="extension_Instantiate" type="Instantiate" aggregation="composite" association="Usage_Instantiate">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Usage_Instantiate-extension_Instantiate-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Usage_Responsibility" name="Usage_Responsibility" memberEnd="Usage_Responsibility-extension_Responsibility Responsibility-base_Usage">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Usage_Responsibility-extension_Responsibility" name="extension_Responsibility" type="Responsibility" aggregation="composite" association="Usage_Responsibility">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Usage_Responsibility-extension_Responsibility-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Usage_Send" name="Usage_Send" memberEnd="Usage_Send-extension_Send Send-base_Usage">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Usage_Send-extension_Send" name="extension_Send" type="Send" aggregation="composite" association="Usage_Send">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Usage_Send-extension_Send-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Auxiliary" name="Auxiliary">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Auxiliary-base_Class" name="base_Class" association="Class_Auxiliary">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Call" name="Call">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Call-base_Usage" name="base_Usage" association="Usage_Call">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Usage"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Create" name="Create">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Create-base_BehavioralFeature" name="base_BehavioralFeature" association="BehavioralFeature_Create">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#BehavioralFeature"/>
			</ownedAttribute>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Create-base_Usage" name="base_Usage" association="Usage_Create">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Usage"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Derive" name="Derive">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Derive-base_Abstraction" name="base_Abstraction" association="Abstraction_Derive">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Abstraction"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Destroy" name="Destroy">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Destroy-base_BehavioralFeature" name="base_BehavioralFeature" association="BehavioralFeature_Destroy">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#BehavioralFeature"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Document" name="Document">
			<generalization xmi:type="uml:Generalization" xmi:id="Document-_generalization.0" general="File"/>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Document-base_Artifact" name="base_Artifact" association="Artifact_Document">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Artifact"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Entity" name="Entity">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Entity-base_Component" name="base_Component" association="Component_Entity">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Component"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Executable" name="Executable">
			<generalization xmi:type="uml:Generalization" xmi:id="Executable-_generalization.0" general="File"/>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Executable-base_Artifact" name="base_Artifact" association="Artifact_Executable">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Artifact"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="File" name="File">
			<ownedAttribute xmi:type="uml:Property" xmi:id="File-base_Artifact" name="base_Artifact" association="Artifact_File">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Artifact"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Focus" name="Focus">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Focus-base_Class" name="base_Class" association="Class_Focus">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Framework" name="Framework">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Framework-base_Package" name="base_Package" association="Package_Framework">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Package"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Implement" name="Implement">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Implement-base_Component" name="base_Component" association="Component_Implement">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Component"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="ImplementationClass" name="ImplementationClass">
			<ownedAttribute xmi:type="uml:Property" xmi:id="ImplementationClass-base_Class" name="base_Class" association="Class_ImplementationClass">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Instantiate" name="Instantiate">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Instantiate-base_Usage" name="base_Usage" association="Usage_Instantiate">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Usage"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Library" name="Library">
			<generalization xmi:type="uml:Generalization" xmi:id="Library-_generalization.0" general="File"/>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Library-base_Artifact" name="base_Artifact" association="Artifact_Library">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Artifact"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Metaclass" name="Metaclass">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Metaclass-base_Class" name="base_Class" association="Class_Metaclass">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="ModelLibrary" name="ModelLibrary">
			<ownedAttribute xmi:type="uml:Property" xmi:id="ModelLibrary-base_Package" name="base_Package" association="Package_ModelLibrary">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Package"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Process" name="Process">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Process-base_Component" name="base_Component" association="Component_Process">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Component"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Realization" name="Realization">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Realization-base_Classifier" name="base_Classifier" association="Classifier_Realization">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Classifier"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Refine" name="Refine">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Refine-base_Abstraction" name="base_Abstraction" association="Abstraction_Refine">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Abstraction"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Responsibility" name="Responsibility">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Responsibility-base_Usage" name="base_Usage" association="Usage_Responsibility">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Usage"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Script" name="Script">
			<generalization xmi:type="uml:Generalization" xmi:id="Script-_generalization.0" general="File"/>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Script-base_Artifact" name="base_Artifact" association="Artifact_Script">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Artifact"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Send" name="Send">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Send-base_Usage" name="base_Usage" association="Usage_Send">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Usage"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Service" name="Service">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Service-base_Component" name="base_Component" association="Component_Service">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Component"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Source" name="Source">
			<generalization xmi:type="uml:Generalization" xmi:id="Source-_generalization.0" general="File"/>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Source-base_Artifact" name="base_Artifact" association="Artifact_Source">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Artifact"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Specification" name="Specification">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Specification-base_Classifier" name="base_Classifier" association="Classifier_Specification">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Classifier"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Subsystem" name="Subsystem">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Subsystem-base_Component" name="base_Component" association="Component_Subsystem">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Component"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Trace" name="Trace">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Trace-base_Abstraction" name="base_Abstraction" association="Abstraction_Trace">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Abstraction"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Type" name="Type">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Type-base_Class" name="base_Class" association="Class_Type">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Utility" name="Utility">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Utility-base_Class" name="base_Class" association="Class_Utility">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Component_BuildComponent" name="Component_BuildComponent" memberEnd="Component_BuildComponent-extension_BuildComponent BuildComponent-base_Component">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Component_BuildComponent-extension_BuildComponent" name="extension_BuildComponent" type="BuildComponent" aggregation="composite" association="Component_BuildComponent">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Component_BuildComponent-extension_BuildComponent-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Model_Metamodel" name="Model_Metamodel" memberEnd="Model_Metamodel-extension_Metamodel Metamodel-base_Model">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Model_Metamodel-extension_Metamodel" name="extension_Metamodel" type="Metamodel" aggregation="composite" association="Model_Metamodel">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Model_Metamodel-extension_Metamodel-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Extension" xmi:id="Model_SystemModel" name="Model_SystemModel" memberEnd="Model_SystemModel-extension_SystemModel SystemModel-base_Model">
			<ownedEnd xmi:type="uml:ExtensionEnd" xmi:id="Model_SystemModel-extension_SystemModel" name="extension_SystemModel" type="SystemModel" aggregation="composite" association="Model_SystemModel">
				<lowerValue xmi:type="uml:LiteralInteger" xmi:id="Model_SystemModel-extension_SystemModel-_lowerValue"/>
			</ownedEnd>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="BuildComponent" name="BuildComponent">
			<ownedAttribute xmi:type="uml:Property" xmi:id="BuildComponent-base_Component" name="base_Component" association="Component_BuildComponent">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Component"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="Metamodel" name="Metamodel">
			<ownedAttribute xmi:type="uml:Property" xmi:id="Metamodel-base_Model" name="base_Model" association="Model_Metamodel">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Model"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Stereotype" xmi:id="SystemModel" name="SystemModel">
			<ownedAttribute xmi:type="uml:Property" xmi:id="SystemModel-base_Model" name="base_Model" association="Model_SystemModel">
				<type xmi:type="uml:Class" href="http://www.omg.org/spec/UML/20161101/UML.xmi#Model"/>
			</ownedAttribute>
		</packagedElement>
	</uml:Profile>
	<mofext:Tag xmi:type="mofext:Tag" xmi:id="_1" name="org.omg.xmi.nsPrefix" value="StandardProfile" element="_0"/>
</xmi:XMI>
