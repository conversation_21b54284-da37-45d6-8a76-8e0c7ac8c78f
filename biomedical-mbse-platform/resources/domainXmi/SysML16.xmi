<?xml version='1.0' encoding='UTF-8'?>
<xmi:XMI 
xmlns:xmi="http://www.omg.org/spec/XMI/20131001" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:uml="http://www.omg.org/spec/UML/20161101" xmlns:mofext="http://www.omg.org/spec/MOF/20131001" xmlns:SysML="http://www.omg.org/spec/SysML/20161101/SysML" xmlns:StandardProfile="http://www.omg.org/spec/UML/20161101/StandardProfile">
  <uml:Profile xmi:id="SysML" URI="http://www.omg.org/spec/SysML/20181001/SysML" name="SysML">
    <metamodelReference xmi:id="SysML._packageImport.UML" xmi:type="uml:PackageImport">
      <importedPackage href="http://www.omg.org/spec/UML/20161101/UML.xmi#_0"/>
    </metamodelReference>
    <packageImport xmi:id="SysML._packageImport.PrimitiveTypes" xmi:type="uml:PackageImport">
      <importedPackage href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#_0"/>
    </packageImport>
    <packageImport xmi:id="SysML._packageImport.StandardProfile" xmi:type="uml:PackageImport">
      <importedPackage href="http://www.omg.org/spec/UML/20161101/StandardProfile.xmi#_0"/>
    </packageImport>
    <packageImport xmi:idref="SysML._packageImport.UML"/>
    <packagedElement xmi:id="SysML.Activities" xmi:type="uml:Package" URI="" name="Activities">
      <packagedElement xmi:id="SysML.Optional" xmi:type="uml:Stereotype" name="Optional">
        <ownedAttribute xmi:id="SysML.Optional.base_Parameter" xmi:type="uml:Property" name="base_Parameter">
          <association xmi:idref="SysML.E_extension_Optional_base_Parameter"/>
          <lowerValue xmi:id="SysML.Optional.base_Parameter.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Parameter"/>
          <upperValue xmi:id="SysML.Optional.base_Parameter.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Optional._comment0" xmi:type="uml:Comment" body="When the «optional» stereotype is applied to parameters, the lower multiplicity shall be equal to zero. This means the parameter is not required to have a value for the activity or any behavior to begin or end execution. Otherwise, the lower multiplicity shall be greater than zero, which is called “required.” The absence of this stereotype indicates a constraint, see below.">
</ownedComment>
        <ownedRule xmi:id="SysML.Optional._rule.1_lower_is_0" xmi:type="uml:Constraint" name="1_lower_is_0">
          <ownedComment xmi:id="SysML.Optional._rule.1_lower_is_0._comment0" xmi:type="uml:Comment" body="A parameter with the «optional» stereotypes applied shall have multiplicity.lower equal to zero, otherwise multiplicity.lower shall be greater than zero"/>
          <specification xmi:id="SysML.Optional._rule.1_lower_is_0.specification" xmi:type="uml:OpaqueExpression" body="UML::Parameter.allInstances()-&gt;forAll(p | Optional.allInstances().base_Parameter-&gt;includes(p) xor p.lower &gt; 0)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Continuous" xmi:type="uml:Stereotype" name="Continuous">
        <generalization xmi:id="SysML.Continuous._generalization.SysML.Rate" xmi:type="uml:Generalization" isSubstitutable="false">
          <general xmi:idref="SysML.Rate"/>
        </generalization>
        <ownedComment 
        xmi:id="SysML.Continuous._comment0" xmi:type="uml:Comment" body="Continuous rate is a special case of rate of flow (see Rate) where the increment of time between items approaches zero. It is intended to represent continuous flows that may correspond to water flowing through a pipe, a time continuous signal, or continuous energy flow. It is independent from UML streaming, see clause ********. A streaming parameter may or may not apply to continuous flow, and a continuous flow may or may not apply to streaming parameters.

UML places no restriction on the rate at which tokens flow. In particular, the time between tokens can approach as close to zero as needed, for example to simulate continuous flow. There is also no restriction in UML on the kind of values that flow through an activity. In particular, the value may represent as small a number as needed, for example to simulate continuous material or energy flow. Finally, the exact timing of token flow is not completely prescribed in UML. In particular, token flow on different edges may be coordinated to occur in a clocked fashion, as in time march algorithms for numerical solvers of ordinary differential equations, such as Runge-Kutta.">
</ownedComment>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Rate_base_Parameter" xmi:type="uml:Extension" name="E_extension_Rate_base_Parameter">
        <memberEnd xmi:idref="SysML.E_extension_Rate_base_Parameter.extension_Rate"/>
        <memberEnd xmi:idref="SysML.Rate.base_Parameter"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Rate_base_Parameter.extension_Rate"/>
        <ownedEnd xmi:id="SysML.E_extension_Rate_base_Parameter.extension_Rate" xmi:type="uml:ExtensionEnd" name="extension_Rate">
          <association xmi:idref="SysML.E_extension_Rate_base_Parameter"/>
          <lowerValue xmi:id="SysML.E_extension_Rate_base_Parameter.extension_Rate.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Rate"/>
          <upperValue xmi:id="SysML.E_extension_Rate_base_Parameter.extension_Rate.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Rate_base_ObjectNode" xmi:type="uml:Extension" name="E_extension_Rate_base_ObjectNode">
        <memberEnd xmi:idref="SysML.E_extension_Rate_base_ObjectNode.extension_Rate"/>
        <memberEnd xmi:idref="SysML.Rate.base_ObjectNode"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Rate_base_ObjectNode.extension_Rate"/>
        <ownedEnd xmi:id="SysML.E_extension_Rate_base_ObjectNode.extension_Rate" xmi:type="uml:ExtensionEnd" name="extension_Rate">
          <association xmi:idref="SysML.E_extension_Rate_base_ObjectNode"/>
          <lowerValue xmi:id="SysML.E_extension_Rate_base_ObjectNode.extension_Rate.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Rate"/>
          <upperValue xmi:id="SysML.E_extension_Rate_base_ObjectNode.extension_Rate.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.NoBuffer" xmi:type="uml:Stereotype" name="NoBuffer">
        <ownedAttribute xmi:id="SysML.NoBuffer.base_ObjectNode" xmi:type="uml:Property" name="base_ObjectNode">
          <association xmi:idref="SysML.E_extension_NoBuffer_base_ObjectNode"/>
          <lowerValue xmi:id="SysML.NoBuffer.base_ObjectNode.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ObjectNode"/>
          <upperValue xmi:id="SysML.NoBuffer.base_ObjectNode.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.NoBuffer._comment0" xmi:type="uml:Comment" body="When the «nobuffer» stereotype is applied to object nodes, tokens arriving at the node are discarded if they are refused by outgoing edges, or refused by actions for object nodes that are input pins. This is typically used with fast or continuously flowing data values, to prevent buffer overrun, or to model transient values, such as electrical signals. For object nodes that are the target of continuous flows, «nobuffer» and «overwrite» have the same effect. The stereotype does not override UML token offering semantics; it just indicates what happens to the token when it is accepted. When the stereotype is not applied, the semantics are as in UML, specifically, tokens arriving at an object node that are refused by outgoing edges, or action for input pins, are held until they can leave the object node.">
</ownedComment>
        <ownedRule xmi:id="SysML.NoBuffer._rule.1_not_overwrite" xmi:type="uml:Constraint" name="1_not_overwrite">
          <ownedComment xmi:id="SysML.NoBuffer._rule.1_not_overwrite._comment0" xmi:type="uml:Comment" body="The «nobuffer» and «overwrite» stereotypes cannot be applied to the same element at the same time."/>
          <specification xmi:id="SysML.NoBuffer._rule.1_not_overwrite.specification" xmi:type="uml:OpaqueExpression" body="Overwrite.allInstances().base_ObjectNode-&gt;excludes(self.base_ObjectNode)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ControlOperator_base_Behavior" xmi:type="uml:Extension" name="E_extension_ControlOperator_base_Behavior">
        <memberEnd xmi:idref="SysML.E_extension_ControlOperator_base_Behavior.extension_ControlOperator"/>
        <memberEnd xmi:idref="SysML.ControlOperator.base_Behavior"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ControlOperator_base_Behavior.extension_ControlOperator"/>
        <ownedEnd xmi:id="SysML.E_extension_ControlOperator_base_Behavior.extension_ControlOperator" xmi:type="uml:ExtensionEnd" name="extension_ControlOperator">
          <association xmi:idref="SysML.E_extension_ControlOperator_base_Behavior"/>
          <lowerValue xmi:id="SysML.E_extension_ControlOperator_base_Behavior.extension_ControlOperator.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ControlOperator"/>
          <upperValue xmi:id="SysML.E_extension_ControlOperator_base_Behavior.extension_ControlOperator.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.Probability" xmi:type="uml:Stereotype" name="Probability">
        <ownedAttribute xmi:id="SysML.Probability.base_ActivityEdge" xmi:type="uml:Property" name="base_ActivityEdge">
          <association xmi:idref="SysML.E_extension_Probability_base_ActivityEdge"/>
          <lowerValue xmi:id="SysML.Probability.base_ActivityEdge.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ActivityEdge"/>
          <upperValue xmi:id="SysML.Probability.base_ActivityEdge.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Probability.base_ParameterSet" xmi:type="uml:Property" name="base_ParameterSet">
          <association xmi:idref="SysML.E_extension_Probability_base_ParameterSet"/>
          <lowerValue xmi:id="SysML.Probability.base_ParameterSet.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ParameterSet"/>
          <upperValue xmi:id="SysML.Probability.base_ParameterSet.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Probability.probability" xmi:type="uml:Property" name="probability">
          <lowerValue xmi:id="SysML.Probability.probability.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.Probability.probability._comment0" xmi:type="uml:Comment" body="Value of the probability"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ValueSpecification"/>
          <upperValue xmi:id="SysML.Probability.probability.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Probability._comment0" xmi:type="uml:Comment" body="When the «probability» stereotype is applied to edges coming out of decision nodes and object nodes, it provides an expression for the probability that the edge will be traversed. These shall be between zero and one inclusive, and add up to one for edges with same source at the time the probabilities are used.

When the «probability» stereotype is applied to output parameter sets, it gives the probability the parameter set will be given values at runtime. These shall be between zero and one inclusive, and add up to one for output parameter sets of the same behavior at the time the probabilities are used.">
</ownedComment>
        <ownedRule xmi:id="SysML.Probability._rule.2_all_outgoing_edges" xmi:type="uml:Constraint" name="2_all_outgoing_edges">
          <ownedComment xmi:id="SysML.Probability._rule.2_all_outgoing_edges._comment0" xmi:type="uml:Comment" body="When the «probability» stereotype is applied to an activity edge, then it shall be applied to all edges coming out of the same source."/>
          <specification 
          xmi:id="SysML.Probability._rule.2_all_outgoing_edges.specification" xmi:type="uml:OpaqueExpression" body="self.base_ActivityEdge-&gt;notEmpty() implies Probability.allInstances().base_ActivityEdge-&gt;includesAll(self.base_ActivityEdge.target.incoming) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Probability._rule.3_all_parametersets" xmi:type="uml:Constraint" name="3_all_parametersets">
          <ownedComment xmi:id="SysML.Probability._rule.3_all_parametersets._comment0" xmi:type="uml:Comment" body="When the «probability» stereotype is applied to an output parameter set, it shall be applied to all the parameter sets of the behavior or operation owning the original parameter set."/>
          <specification 
          xmi:id="SysML.Probability._rule.3_all_parametersets.specification" xmi:type="uml:OpaqueExpression" body="self.base_ParameterSet-&gt;notEmpty() implies Probability.allInstances().base_ParameterSet-&gt;includesAll(self.base_ParameterSet.namespace.ownedMember-&gt;select(m | m.oclIsKindOf(UML::ParameterSet))) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Probability._rule.1_source_decisionnode_or_objectnode" xmi:type="uml:Constraint" name="1_source_decisionnode_or_objectnode">
          <ownedComment xmi:id="SysML.Probability._rule.1_source_decisionnode_or_objectnode._comment0" xmi:type="uml:Comment" body="The «probability» stereotype shall only be applied to activity edges that have decision nodes or object nodes as sources, or to output parameter sets."/>
          <specification 
          xmi:id="SysML.Probability._rule.1_source_decisionnode_or_objectnode.specification" xmi:type="uml:OpaqueExpression" body="(self.base_ActivityEdge-&gt;notEmpty() implies self.base_ActivityEdge.source.oclIsKindOf(UML::DecisionNode)) and (self.base_ParameterSet-&gt;notEmpty() implies self.base_ParameterSet.parameter-&gt;forAll(p | p.direction=UML::ParameterDirectionKind::out)) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Probability._rule.4_all_outputparameter_in_parametersets" xmi:type="uml:Constraint" name="4_all_outputparameter_in_parametersets">
          <ownedComment xmi:id="SysML.Probability._rule.4_all_outputparameter_in_parametersets._comment0" xmi:type="uml:Comment" body="When the «probability» stereotype is applied to an output parameter set, all the output parameters shall be in some parameter set."/>
          <specification 
          xmi:id="SysML.Probability._rule.4_all_outputparameter_in_parametersets.specification" xmi:type="uml:OpaqueExpression" body="(self.base_ActivityEdge-&gt;notEmpty() implies Continuous.allInstances().base_ActivityEdge-&gt;excludes(self.base_ActivityEdge)) and (self.base_Parameter-&gt;notEmpty() implies Continuous.allInstances().base_Parameter-&gt;excludes(self.base_Parameter)) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Rate" xmi:type="uml:Stereotype" name="Rate">
        <ownedAttribute xmi:id="SysML.Rate.base_ActivityEdge" xmi:type="uml:Property" name="base_ActivityEdge">
          <association xmi:idref="SysML.E_extension_Rate_base_ActivityEdge"/>
          <lowerValue xmi:id="SysML.Rate.base_ActivityEdge.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ActivityEdge"/>
          <upperValue xmi:id="SysML.Rate.base_ActivityEdge.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Rate.base_ObjectNode" xmi:type="uml:Property" name="base_ObjectNode">
          <association xmi:idref="SysML.E_extension_Rate_base_ObjectNode"/>
          <lowerValue xmi:id="SysML.Rate.base_ObjectNode.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ObjectNode"/>
          <upperValue xmi:id="SysML.Rate.base_ObjectNode.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Rate.base_Parameter" xmi:type="uml:Property" name="base_Parameter">
          <association xmi:idref="SysML.E_extension_Rate_base_Parameter"/>
          <lowerValue xmi:id="SysML.Rate.base_Parameter.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Parameter"/>
          <upperValue xmi:id="SysML.Rate.base_Parameter.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Rate.rate" xmi:type="uml:Property" name="rate">
          <lowerValue xmi:id="SysML.Rate.rate.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.Rate.rate._comment0" xmi:type="uml:Comment" body="Value of the rate"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#InstanceSpecification"/>
          <upperValue xmi:id="SysML.Rate.rate.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Rate._comment0" xmi:type="uml:Comment" body="When the «rate» stereotype is applied to an activity edge, it specifies the expected value of the number of objects and values that traverse the edge per time interval, that is, the expected value rate at which they leave the source node and arrive at the target node. It does not refer to the rate at which a value changes over time. When the stereotype is applied to a parameter, the parameter shall be streaming, and the stereotype gives the number of objects or values that flow in or out of the parameter per time interval while the behavior or operation is executing. Streaming is a characteristic of UML behavior parameters that supports the input and output of items while a behavior is executing, rather than only when the behavior starts and stops. The flow may be continuous or discrete, see the specialized rates in clause 11.3.2.1 and clause 11.3.2.3. The «rate» stereotype has a rate property of type InstanceSpecification. The values of this property shall be instances of classifiers stereotyped by «valueType» or «distributionDefinition», see  clause 8. In particular, the denominator for units used in the rate property shall be time units.">
</ownedComment>
        <ownedRule xmi:id="SysML.Rate._rule.2_edges_rates" xmi:type="uml:Constraint" name="2_edges_rates">
          <ownedComment xmi:id="SysML.Rate._rule.2_edges_rates._comment0" xmi:type="uml:Comment" body="The rate of a parameter shall be less than or equal to rates on edges that come into or go out from pins and parameters nodes corresponding to the parameter."/>
          <specification 
          xmi:id="SysML.Rate._rule.2_edges_rates.specification" xmi:type="uml:OpaqueExpression" body="self.base_Parameter-&gt;notEmpty() implies (
  let nodes: Set(UML::ObjectNode) =  
  if self.base_Parameter.owner.oclIsKindOf(UML::Behavior) then
    let pOwner: UML::Behavior = self.base_Parameter.owner.oclAsType(UML::Behavior) in
    UML::CallBehaviorAction.allInstances()-&gt;select(a | a.behavior = pOwner)
    -&gt;collect(a | a.argument-&gt;at(pOwner.ownedParameter-&gt;indexOf(self.base_Parameter)))
    -&gt;union(UML::StartObjectBehaviorAction.allInstances()-&gt;select(a | a.behavior() = pOwner)
    -&gt;collect(a | a.argument-&gt;at(pOwner.ownedParameter-&gt;indexOf(self.base_Parameter))))
    -&gt;union(UML::ActivityParameterNode.allInstances()-&gt;select(n | n.parameter = self.base_Parameter))-&gt;asSet()
  else if self.base_Parameter.owner.oclIsKindOf(UML::Operation) then
    let pOwner: UML::Operation = self.base_Parameter.owner.oclAsType(UML::Operation) in
    UML::CallOperationAction.allInstances()-&gt;select(a | a.operation = pOwner)
   -&gt;collect(a | a.argument-&gt;at(pOwner.ownedParameter-&gt;indexOf(self.base_Parameter)))-&gt;asSet()
  else
    Set(UML::ObjectNode){}
  endif endif in
  nodes.incoming-&gt;flatten()-&gt;union(nodes.outgoing-&gt;flatten())
  -&gt;forAll(e | let eRate: Rate = Rate.allInstances()-&gt;any(r |  r.base_ActivityEdge=e) in
  (not eRate.oclIsUndefined() and self.rate.specification.realValue() &lt;= eRate.rate.specification.realValue())) )" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Rate._rule.1_streaming" xmi:type="uml:Constraint" name="1_streaming">
          <ownedComment xmi:id="SysML.Rate._rule.1_streaming._comment0" xmi:type="uml:Comment" body="When the «rate» stereotype is applied to a parameter, the parameter shall be streaming."/>
          <specification xmi:id="SysML.Rate._rule.1_streaming.specification" xmi:type="uml:OpaqueExpression" body="self.base_Parameter-&gt;notEmpty() implies self.base_Parameter.isStream" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Discrete" xmi:type="uml:Stereotype" name="Discrete">
        <generalization xmi:id="SysML.Discrete._generalization.SysML.Rate" xmi:type="uml:Generalization" isSubstitutable="false">
          <general xmi:idref="SysML.Rate"/>
        </generalization>
        <ownedComment 
        xmi:id="SysML.Discrete._comment0" xmi:type="uml:Comment" body="Discrete rate is a special case of rate of flow (see clause********) where the increment of time between items is a non-zero. Examples include the production of assemblies in a factory and signals set at periodic time intervals.">
</ownedComment>
        <ownedRule xmi:id="SysML.Discrete._rule.1_not_continuous" xmi:type="uml:Constraint" name="1_not_continuous">
          <ownedComment xmi:id="SysML.Discrete._rule.1_not_continuous._comment0" xmi:type="uml:Comment" body="The «discrete» and «continuous» stereotypes shall not be applied to the same element at the same time."/>
          <specification 
          xmi:id="SysML.Discrete._rule.1_not_continuous.specification" xmi:type="uml:OpaqueExpression" body="(self.base_ActivityEdge-&gt;notEmpty() implies Continuous.allInstances().base_ActivityEdge-&gt;excludes(self.base_ActivityEdge)) and (self.base_Parameter-&gt;notEmpty() implies Continuous.allInstances().base_Parameter-&gt;excludes(self.base_Parameter)) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.ControlOperator" xmi:type="uml:Stereotype" name="ControlOperator">
        <ownedAttribute xmi:id="SysML.ControlOperator.base_Behavior" xmi:type="uml:Property" name="base_Behavior">
          <association xmi:idref="SysML.E_extension_ControlOperator_base_Behavior"/>
          <lowerValue xmi:id="SysML.ControlOperator.base_Behavior.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Behavior"/>
          <upperValue xmi:id="SysML.ControlOperator.base_Behavior.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ControlOperator.base_Operation" xmi:type="uml:Property" name="base_Operation">
          <association xmi:idref="SysML.E_extension_ControlOperator_base_Operation"/>
          <lowerValue xmi:id="SysML.ControlOperator.base_Operation.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Operation"/>
          <upperValue xmi:id="SysML.ControlOperator.base_Operation.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ControlOperator._comment0" xmi:type="uml:Comment" body="A control operator is a behavior that is intended to represent an arbitrarily complex logical operator that can be used to enable and disable other actions. When the «controlOperator» stereotype is applied to behaviors, the behavior takes control values as inputs or provides them as outputs, that is, it treats control as data (see clause 11.3.3.1.1). When the «controlOperator» stereotype is not applied, the behavior may not have a parameter typed by ControlValue. The «controlOperator» stereotype also applies to operations with the same semantics.

The control value inputs do not enable or disable the control operator execution based on their value, they only enable based on their presence as data. Pins for control parameters are regular pins, not UML control pins. This is so the control value can be passed into or out of the action and the invoked behavior, rather than control the starting of the action, or indicating the ending of it.">
</ownedComment>
        <ownedRule xmi:id="SysML.ControlOperator._rule.1_one_parameter_controlvalue" xmi:type="uml:Constraint" name="1_one_parameter_controlvalue">
          <ownedComment 
          xmi:id="SysML.ControlOperator._rule.1_one_parameter_controlvalue._comment0" xmi:type="uml:Comment" body="When the «controlOperator» stereotype is applied, the behavior or operation shall have at least one parameter typed by ControlValue. If the stereotype is not applied, the behavior or operation may not have any parameter typed by ControlValue.">
</ownedComment>
          <specification 
          xmi:id="SysML.ControlOperator._rule.1_one_parameter_controlvalue.specification" xmi:type="uml:OpaqueExpression" body="UML::Behavior.allInstances()-&gt;forAll(b | not (ControlOperator.allInstances().base_Behavior-&gt;includes(b) xor b.ownedParameter-&gt;exists(p | p.type=SysML::Libraries::ControlValues::ControlValue))) and  UML::Operation.allInstances()-&gt;forAll(o | not (ControlOperator.allInstances().base_Operation-&gt;includes(o) xor o.ownedParameter-&gt;exists(p | p.type=SysML::Libraries::ControlValues::ControlValue))) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ControlOperator._rule.2_controloperator_operation_method" xmi:type="uml:Constraint" name="2_controloperator_operation_method">
          <ownedComment xmi:id="SysML.ControlOperator._rule.2_controloperator_operation_method._comment0" xmi:type="uml:Comment" body="A behavior shall have the «controlOperator» stereotype applied if it is a method of an operation that has the «controlOperator» stereotype applied."/>
          <specification 
          xmi:id="SysML.ControlOperator._rule.2_controloperator_operation_method.specification" xmi:type="uml:OpaqueExpression" body="(self.base_Operation-&gt;notEmpty() and self.base_Operation.method-&gt;notEmpty()) implies  self.base_Operation.method-&gt;forAll(b | ControlOperator.allInstances().base_Behavior-&gt;includes(b))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Overwrite" xmi:type="uml:Stereotype" name="Overwrite">
        <ownedAttribute xmi:id="SysML.Overwrite.base_ObjectNode" xmi:type="uml:Property" name="base_ObjectNode">
          <association xmi:idref="SysML.E_extension_Overwrite_base_ObjectNode"/>
          <lowerValue xmi:id="SysML.Overwrite.base_ObjectNode.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ObjectNode"/>
          <upperValue xmi:id="SysML.Overwrite.base_ObjectNode.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Overwrite._comment0" xmi:type="uml:Comment" body="When the «overwrite» stereotype is applied to object nodes, a token arriving at a full object node removes one that is already there before being added (a full object node has as many tokens as allowed by its upper bound). This is typically used on an input pin with an upper bound of 1 to ensure that stale data is overridden at an input pin. For upper bounds greater than one, the token removed is the one that has been in the object node the longest. For FIFO ordering, this is the token that is next to be selected, for LIFO it is the token that would be last to be selected. Tokens arriving at a full object node with the Overwrite stereotype applied take up their positions in the ordering as normal, if any. The arriving tokens do not take the positions of the removed tokens. A null token removes all the tokens already there. The number of tokens replaced is equal to the weight of the incoming edge, which defaults to 1. For object nodes that are the target of continuous flows, «overwrite» and «nobuffer» have the same effect. The stereotype does not override UML token offering semantics, just indicates what happens to the token when it is accepted. When the stereotype is not applied, the semantics is as in UML, specifically, tokens arriving at object nodes do not replace ones that are already there.">
</ownedComment>
        <ownedRule xmi:id="SysML.Overwrite._rule.1_not_nobuffer" xmi:type="uml:Constraint" name="1_not_nobuffer">
          <ownedComment xmi:id="SysML.Overwrite._rule.1_not_nobuffer._comment0" xmi:type="uml:Comment" body="The «overwrite» and «nobuffer» stereotypes cannot be applied to the same element at the same time."/>
          <specification xmi:id="SysML.Overwrite._rule.1_not_nobuffer.specification" xmi:type="uml:OpaqueExpression" body="NoBuffer.allInstances().base_ObjectNode-&gt;excludes(self.base_ObjectNode)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Rate_base_ActivityEdge" xmi:type="uml:Extension" name="E_extension_Rate_base_ActivityEdge">
        <memberEnd xmi:idref="SysML.E_extension_Rate_base_ActivityEdge.extension_Rate"/>
        <memberEnd xmi:idref="SysML.Rate.base_ActivityEdge"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Rate_base_ActivityEdge.extension_Rate"/>
        <ownedEnd xmi:id="SysML.E_extension_Rate_base_ActivityEdge.extension_Rate" xmi:type="uml:ExtensionEnd" name="extension_Rate">
          <association xmi:idref="SysML.E_extension_Rate_base_ActivityEdge"/>
          <lowerValue xmi:id="SysML.E_extension_Rate_base_ActivityEdge.extension_Rate.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Rate"/>
          <upperValue xmi:id="SysML.E_extension_Rate_base_ActivityEdge.extension_Rate.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_NoBuffer_base_ObjectNode" xmi:type="uml:Extension" name="E_extension_NoBuffer_base_ObjectNode">
        <memberEnd xmi:idref="SysML.E_extension_NoBuffer_base_ObjectNode.extension_NoBuffer"/>
        <memberEnd xmi:idref="SysML.NoBuffer.base_ObjectNode"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_NoBuffer_base_ObjectNode.extension_NoBuffer"/>
        <ownedEnd xmi:id="SysML.E_extension_NoBuffer_base_ObjectNode.extension_NoBuffer" xmi:type="uml:ExtensionEnd" name="extension_NoBuffer">
          <association xmi:idref="SysML.E_extension_NoBuffer_base_ObjectNode"/>
          <lowerValue xmi:id="SysML.E_extension_NoBuffer_base_ObjectNode.extension_NoBuffer.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.NoBuffer"/>
          <upperValue xmi:id="SysML.E_extension_NoBuffer_base_ObjectNode.extension_NoBuffer.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Probability_base_ActivityEdge" xmi:type="uml:Extension" name="E_extension_Probability_base_ActivityEdge">
        <memberEnd xmi:idref="SysML.E_extension_Probability_base_ActivityEdge.extension_Probability"/>
        <memberEnd xmi:idref="SysML.Probability.base_ActivityEdge"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Probability_base_ActivityEdge.extension_Probability"/>
        <ownedEnd xmi:id="SysML.E_extension_Probability_base_ActivityEdge.extension_Probability" xmi:type="uml:ExtensionEnd" name="extension_Probability">
          <association xmi:idref="SysML.E_extension_Probability_base_ActivityEdge"/>
          <lowerValue xmi:id="SysML.E_extension_Probability_base_ActivityEdge.extension_Probability.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Probability"/>
          <upperValue xmi:id="SysML.E_extension_Probability_base_ActivityEdge.extension_Probability.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ControlOperator_base_Operation" xmi:type="uml:Extension" name="E_extension_ControlOperator_base_Operation">
        <memberEnd xmi:idref="SysML.E_extension_ControlOperator_base_Operation.extension_ControlOperator"/>
        <memberEnd xmi:idref="SysML.ControlOperator.base_Operation"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ControlOperator_base_Operation.extension_ControlOperator"/>
        <ownedEnd xmi:id="SysML.E_extension_ControlOperator_base_Operation.extension_ControlOperator" xmi:type="uml:ExtensionEnd" name="extension_ControlOperator">
          <association xmi:idref="SysML.E_extension_ControlOperator_base_Operation"/>
          <lowerValue xmi:id="SysML.E_extension_ControlOperator_base_Operation.extension_ControlOperator.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ControlOperator"/>
          <upperValue xmi:id="SysML.E_extension_ControlOperator_base_Operation.extension_ControlOperator.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Optional_base_Parameter" xmi:type="uml:Extension" name="E_extension_Optional_base_Parameter">
        <memberEnd xmi:idref="SysML.E_extension_Optional_base_Parameter.extension_Optional"/>
        <memberEnd xmi:idref="SysML.Optional.base_Parameter"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Optional_base_Parameter.extension_Optional"/>
        <ownedEnd xmi:id="SysML.E_extension_Optional_base_Parameter.extension_Optional" xmi:type="uml:ExtensionEnd" name="extension_Optional">
          <association xmi:idref="SysML.E_extension_Optional_base_Parameter"/>
          <lowerValue xmi:id="SysML.E_extension_Optional_base_Parameter.extension_Optional.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Optional"/>
          <upperValue xmi:id="SysML.E_extension_Optional_base_Parameter.extension_Optional.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Probability_base_ParameterSet" xmi:type="uml:Extension" name="E_extension_Probability_base_ParameterSet">
        <memberEnd xmi:idref="SysML.E_extension_Probability_base_ParameterSet.extension_Probability"/>
        <memberEnd xmi:idref="SysML.Probability.base_ParameterSet"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Probability_base_ParameterSet.extension_Probability"/>
        <ownedEnd xmi:id="SysML.E_extension_Probability_base_ParameterSet.extension_Probability" xmi:type="uml:ExtensionEnd" name="extension_Probability">
          <association xmi:idref="SysML.E_extension_Probability_base_ParameterSet"/>
          <lowerValue xmi:id="SysML.E_extension_Probability_base_ParameterSet.extension_Probability.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Probability"/>
          <upperValue xmi:id="SysML.E_extension_Probability_base_ParameterSet.extension_Probability.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Overwrite_base_ObjectNode" xmi:type="uml:Extension" name="E_extension_Overwrite_base_ObjectNode">
        <memberEnd xmi:idref="SysML.E_extension_Overwrite_base_ObjectNode.extension_Overwrite"/>
        <memberEnd xmi:idref="SysML.Overwrite.base_ObjectNode"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Overwrite_base_ObjectNode.extension_Overwrite"/>
        <ownedEnd xmi:id="SysML.E_extension_Overwrite_base_ObjectNode.extension_Overwrite" xmi:type="uml:ExtensionEnd" name="extension_Overwrite">
          <association xmi:idref="SysML.E_extension_Overwrite_base_ObjectNode"/>
          <lowerValue xmi:id="SysML.E_extension_Overwrite_base_ObjectNode.extension_Overwrite.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Overwrite"/>
          <upperValue xmi:id="SysML.E_extension_Overwrite_base_ObjectNode.extension_Overwrite.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
    </packagedElement>
    <packagedElement xmi:id="SysML.Requirements" xmi:type="uml:Package" URI="" name="Requirements">
      <packagedElement xmi:id="SysML.Trace" xmi:type="uml:Stereotype" name="Trace">
        <generalization xmi:id="SysML.Trace._generalization.SysML.DirectedRelationshipPropertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.DirectedRelationshipPropertyPath"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.Trace.base_Abstraction" xmi:type="uml:Property" name="base_Abstraction">
          <association xmi:idref="SysML.E_extension_Trace_base_Abstraction"/>
          <lowerValue xmi:id="SysML.Trace.base_Abstraction.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <redefinedProperty xmi:idref="SysML.DirectedRelationshipPropertyPath.base_DirectedRelationship"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Abstraction"/>
          <upperValue xmi:id="SysML.Trace.base_Abstraction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Trace._comment0" xmi:type="uml:Comment" body="The Trace stereotype specializes UML4SysML Trace and DirectedRelationshipPropertyPath to enable traces to identify their sources and targets by a multi-level path of accessible properties from context blocks for the sources and targets.">
</ownedComment>
        <ownedOperation xmi:id="SysML.Trace.getTracedFrom_NamedElement" xmi:type="uml:Operation" isQuery="true" isStatic="true" name="getTracedFrom">
          <bodyCondition xmi:id="SysML.Trace.getTracedFrom_NamedElement._rule.getTracedFrom_body" xmi:type="uml:Constraint" name="getTracedFrom_body">
            <specification xmi:id="SysML.Trace.getTracedFrom_NamedElement._rule.getTracedFrom_body.getTracedFrom_body_specification" xmi:type="uml:OpaqueExpression" body="AbstractRequirement.allInstances()-&gt;select(tracedTo-&gt;includes(ref)) " language="OCL" name="getTracedFrom_body_specification">
              <type xmi:idref="SysML.AbstractRequirement"/>
            </specification>
          </bodyCondition>
          <ownedComment 
          xmi:id="SysML.Trace.getTracedFrom_NamedElement._comment0" xmi:type="uml:Comment" body="The query getTracedFrom() gives all the requirements that are clients (“from” end of the concrete syntax) of a «Trace» relationship whose supplier is the element in parameter. This is a static query.">
</ownedComment>
          <ownedParameter xmi:id="SysML.Trace.getTracedFrom_NamedElement.ref" xmi:type="uml:Parameter" effect="create" name="ref">
            <lowerValue xmi:id="SysML.Trace.getTracedFrom_NamedElement.ref.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.Trace.getTracedFrom_NamedElement.ref.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.Trace.getTracedFrom_NamedElement.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.Trace.getTracedFrom_NamedElement.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.Trace.getTracedFrom_NamedElement.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.Trace.getTracedFrom_NamedElement._rule.getTracedFrom_body"/>
        </ownedOperation>
        <ownedRule xmi:id="SysML.Trace._rule.2_binary" xmi:type="uml:Constraint" name="2_binary">
          <ownedComment xmi:id="SysML.Trace._rule.2_binary._comment0" xmi:type="uml:Comment" body="Abstractions with a Trace stereotype or one of its specializations applied shall have exactly one client and one supplier."/>
          <specification xmi:id="SysML.Trace._rule.2_binary.specification" xmi:type="uml:OpaqueExpression" body="self.base_Abstraction.client-&gt;size()=1 and self.base_Abstraction.supplier-&gt;size()=1" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.TestCase" xmi:type="uml:Stereotype" name="TestCase">
        <ownedAttribute xmi:id="SysML.TestCase.base_Behavior" xmi:type="uml:Property" name="base_Behavior">
          <association xmi:idref="SysML.E_extension_TestCase_base_Behavior"/>
          <lowerValue xmi:id="SysML.TestCase.base_Behavior.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Behavior"/>
          <upperValue xmi:id="SysML.TestCase.base_Behavior.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.TestCase.base_Operation" xmi:type="uml:Property" name="base_Operation">
          <association xmi:idref="SysML.E_extension_TestCase_base_Operation"/>
          <lowerValue xmi:id="SysML.TestCase.base_Operation.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Operation"/>
          <upperValue xmi:id="SysML.TestCase.base_Operation.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment xmi:id="SysML.TestCase._comment0" xmi:type="uml:Comment" body="A test case is a method for verifying a requirement is satisfied."/>
        <ownedRule xmi:id="SysML.TestCase._rule.1_return_verdictkind" xmi:type="uml:Constraint" name="1_return_verdictkind">
          <ownedComment xmi:id="SysML.TestCase._rule.1_return_verdictkind._comment0" xmi:type="uml:Comment" body="The type of return parameter of the stereotyped model element shall be VerdictKind. (note this is consistent with the UML Testing Profile)."/>
          <specification 
          xmi:id="SysML.TestCase._rule.1_return_verdictkind.specification" xmi:type="uml:OpaqueExpression" body="(self.base_Behavior-&gt;notEmpty() implies self.base_Behavior.ownedParameter-&gt;exists(p | p.direction=UML::ParameterDirectionKind::return and p.type = VerdictKind )) and (self.base_Operation-&gt;notEmpty() implies self.base_Operation.ownedParameter-&gt;exists(p | p.direction=UML::ParameterDirectionKind::return and p.type = VerdictKind )) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_TestCase_base_Behavior" xmi:type="uml:Extension" name="E_extension_TestCase_base_Behavior">
        <memberEnd xmi:idref="SysML.E_extension_TestCase_base_Behavior.extension_TestCase"/>
        <memberEnd xmi:idref="SysML.TestCase.base_Behavior"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_TestCase_base_Behavior.extension_TestCase"/>
        <ownedEnd xmi:id="SysML.E_extension_TestCase_base_Behavior.extension_TestCase" xmi:type="uml:ExtensionEnd" name="extension_TestCase">
          <association xmi:idref="SysML.E_extension_TestCase_base_Behavior"/>
          <lowerValue xmi:id="SysML.E_extension_TestCase_base_Behavior.extension_TestCase.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.TestCase"/>
          <upperValue xmi:id="SysML.E_extension_TestCase_base_Behavior.extension_TestCase.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.Requirement" xmi:type="uml:Stereotype" name="Requirement">
        <generalization xmi:id="SysML.Requirement._generalization.SysML.AbstractRequirement" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.AbstractRequirement"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.Requirement.base_Class" xmi:type="uml:Property" name="base_Class">
          <association xmi:idref="SysML.E_extension_Requirement_base_Class"/>
          <lowerValue xmi:id="SysML.Requirement.base_Class.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <redefinedProperty xmi:idref="SysML.AbstractRequirement.base_NamedElement"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
          <upperValue xmi:id="SysML.Requirement.base_Class.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Requirement._comment0" xmi:type="uml:Comment" body="A requirement specifies a capability or condition that must (or should) be satisfied. A requirement may specify a function that a system must perform or a performance condition that a system must satisfy. Requirements are used to establish a contract between the customer (or other stakeholder) and those responsible for designing and implementing the system.

A requirement is a stereotype of both Class and Abstract Requirement. Compound requirements can be created by using the nesting capability of the class definition mechanism. The default interpretation of a compound requirement, unless stated differently by the compound requirement itself, is that all its subrequirements shall be satisfied for the compound requirement to be satisfied. Subrequirements shall be accessed through the “nestedClassifier” property of a class. When a requirement has nested requirements, all the nested requirements apply as part of the container requirement. Deleting the container requirement deleted the nested requirements, a functionality inherited from UML.">
</ownedComment>
        <ownedRule xmi:id="SysML.Requirement._rule.4_no_generalization" xmi:type="uml:Constraint" name="4_no_generalization">
          <ownedComment xmi:id="SysML.Requirement._rule.4_no_generalization._comment0" xmi:type="uml:Comment" body="Classes stereotyped by «requirement» shall not participate in generalizations."/>
          <specification xmi:id="SysML.Requirement._rule.4_no_generalization.specification" xmi:type="uml:OpaqueExpression" body="UML::Classifier.allInstances().general-&gt;flatten()-&gt;excludes(self.base_Class)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Requirement._rule.5_nestedclassifiers_are_requirements" xmi:type="uml:Constraint" name="5_nestedclassifiers_are_requirements">
          <ownedComment xmi:id="SysML.Requirement._rule.5_nestedclassifiers_are_requirements._comment0" xmi:type="uml:Comment" body="A nested classifier of a class stereotyped by Requirement or one of its specializations shall also be stereotyped by Requirement or one of its specializations"/>
          <specification xmi:id="SysML.Requirement._rule.5_nestedclassifiers_are_requirements.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.nestedClassifier-&gt;forAll(c | Requirement.allInstances().base_Class-&gt;includes(c))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Requirement._rule.1_no_operation" xmi:type="uml:Constraint" name="1_no_operation">
          <ownedComment xmi:id="SysML.Requirement._rule.1_no_operation._comment0" xmi:type="uml:Comment" body="The property “ownedOperation” shall be empty."/>
          <specification xmi:id="SysML.Requirement._rule.1_no_operation.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.ownedOperation-&gt;isEmpty()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Requirement._rule.2_no_attribute" xmi:type="uml:Constraint" name="2_no_attribute">
          <ownedComment xmi:id="SysML.Requirement._rule.2_no_attribute._comment0" xmi:type="uml:Comment" body="The property “ownedAttribute” shall be empty."/>
          <specification xmi:id="SysML.Requirement._rule.2_no_attribute.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.ownedAttribute-&gt;isEmpty()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Requirement._rule.3_no_association" xmi:type="uml:Constraint" name="3_no_association">
          <ownedComment xmi:id="SysML.Requirement._rule.3_no_association._comment0" xmi:type="uml:Comment" body="Classes stereotyped by «requirement» shall not participate in associations."/>
          <specification xmi:id="SysML.Requirement._rule.3_no_association.specification" xmi:type="uml:OpaqueExpression" body="UML::Association.allInstances().memberEnd-&gt;flatten().type-&gt;excludes(self.base_Class)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Requirement._rule.6_not_a_type" xmi:type="uml:Constraint" name="6_not_a_type">
          <ownedComment xmi:id="SysML.Requirement._rule.6_not_a_type._comment0" xmi:type="uml:Comment" body="Classes stereotyped by «requirement» shall not be used to type any other model element."/>
          <specification xmi:id="SysML.Requirement._rule.6_not_a_type.specification" xmi:type="uml:OpaqueExpression" body="UML::TypedElement.allInstances().type-&gt;excludes(self.base_Class)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Verify" xmi:type="uml:Stereotype" name="Verify">
        <generalization xmi:id="SysML.Verify._generalization.SysML.Trace" xmi:type="uml:Generalization" isSubstitutable="false">
          <general xmi:idref="SysML.Trace"/>
        </generalization>
        <ownedComment 
        xmi:id="SysML.Verify._comment0" xmi:type="uml:Comment" body="A Verify relationship is a dependency between a requirement and a test case or other model element that can determine whether a system fulfills the requirement. As with other dependencies, the arrow direction points from the (client) element to the (supplier) requirement.">
</ownedComment>
        <ownedOperation xmi:id="SysML.Verify.getVerifies_NamedElement" xmi:type="uml:Operation" isQuery="true" isStatic="true" name="getVerifies">
          <bodyCondition xmi:id="SysML.Verify.getVerifies_NamedElement._rule.getVerifies_body" xmi:type="uml:Constraint" name="getVerifies_body">
            <specification xmi:id="SysML.Verify.getVerifies_NamedElement._rule.getVerifies_body.getVerifies_body_specification" xmi:type="uml:OpaqueExpression" body="Verify.allInstances()-&gt;select(base_Abstraction.client=ref).base_Abstraction.supplier" language="OCL" name="getVerifies_body_specification">
              <type xmi:idref="SysML.AbstractRequirement"/>
            </specification>
          </bodyCondition>
          <ownedComment 
          xmi:id="SysML.Verify.getVerifies_NamedElement._comment0" xmi:type="uml:Comment" body="The query getVerifies() gives all the requirements that are suppliers ( “to” end of the concrete syntax ) of a «Verify» relationships whose client is the element in parameter. This is a static query.">
</ownedComment>
          <ownedParameter xmi:id="SysML.Verify.getVerifies_NamedElement.ref" xmi:type="uml:Parameter" effect="create" name="ref">
            <lowerValue xmi:id="SysML.Verify.getVerifies_NamedElement.ref.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.Verify.getVerifies_NamedElement.ref.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.Verify.getVerifies_NamedElement.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.Verify.getVerifies_NamedElement.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.Verify.getVerifies_NamedElement.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.Verify.getVerifies_NamedElement._rule.getVerifies_body"/>
        </ownedOperation>
        <ownedRule xmi:id="SysML.Verify._rule.1_supplier_is_requirement" xmi:type="uml:Constraint" name="1_supplier_is_requirement">
          <ownedComment xmi:id="SysML.Verify._rule.1_supplier_is_requirement._comment0" xmi:type="uml:Comment" body="The supplier shall be an element stereotyped by any subtype of «AbstractRequirement»."/>
          <specification xmi:id="SysML.Verify._rule.1_supplier_is_requirement.specification" xmi:type="uml:OpaqueExpression" body="AbstractRequirement.allInstances().base_NamedElement-&gt;includes(self.base_Abstraction.supplier)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Refine_base_Abstraction" xmi:type="uml:Extension" name="E_extension_Refine_base_Abstraction">
        <generalization xmi:id="SysML.E_extension_Refine_base_Abstraction._generalization.SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship"/>
        </generalization>
        <memberEnd xmi:idref="SysML.Refine.base_Abstraction"/>
        <memberEnd xmi:idref="SysML.E_extension_Refine_base_Abstraction.extension_Refine"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Refine_base_Abstraction.extension_Refine"/>
        <ownedEnd xmi:id="SysML.E_extension_Refine_base_Abstraction.extension_Refine" xmi:type="uml:ExtensionEnd" name="extension_Refine">
          <association xmi:idref="SysML.E_extension_Refine_base_Abstraction"/>
          <lowerValue xmi:id="SysML.E_extension_Refine_base_Abstraction.extension_Refine.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <redefinedProperty xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship.extension_DirectedRelationshipPropertyPath"/>
          <type xmi:idref="SysML.Refine"/>
          <upperValue xmi:id="SysML.E_extension_Refine_base_Abstraction.extension_Refine.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.Refine" xmi:type="uml:Stereotype" name="Refine">
        <generalization xmi:id="SysML.Refine._generalization.SysML.DirectedRelationshipPropertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.DirectedRelationshipPropertyPath"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.Refine.base_Abstraction" xmi:type="uml:Property" name="base_Abstraction">
          <association xmi:idref="SysML.E_extension_Refine_base_Abstraction"/>
          <lowerValue xmi:id="SysML.Refine.base_Abstraction.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <redefinedProperty xmi:idref="SysML.DirectedRelationshipPropertyPath.base_DirectedRelationship"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Abstraction"/>
          <upperValue xmi:id="SysML.Refine.base_Abstraction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Refine._comment0" xmi:type="uml:Comment" body="The Refine stereotype specializes UML4SysML Refine and DirectedRelationshipPropertyPath to enable refinements to identify their sources and targets by a multi-level path of accessible properties from context blocks for the sources and targets.">
</ownedComment>
        <ownedOperation xmi:id="SysML.Refine.getRefines_NamedElement" xmi:type="uml:Operation" isQuery="true" isStatic="true" name="getRefines">
          <bodyCondition xmi:id="SysML.Refine.getRefines_NamedElement._rule.getRefines_body" xmi:type="uml:Constraint" name="getRefines_body">
            <specification xmi:id="SysML.Refine.getRefines_NamedElement._rule.getRefines_body.getRefines_body_specification" xmi:type="uml:OpaqueExpression" body="Refine.allInstances()-&gt;select(base_Abstraction.client=ref).base_Abstraction.supplier" language="OCL" name="getRefines_body_specification">
              <type xmi:idref="SysML.AbstractRequirement"/>
            </specification>
          </bodyCondition>
          <ownedComment xmi:id="SysML.Refine.getRefines_NamedElement._comment0" xmi:type="uml:Comment" body="The query getRefines() gives all the requirements that are suppliers (“to”end of the concrete syntax) of a «Refine» relationships whose client is the element in parameter. This is a static query."/>
          <ownedParameter xmi:id="SysML.Refine.getRefines_NamedElement.ref" xmi:type="uml:Parameter" effect="create" name="ref">
            <lowerValue xmi:id="SysML.Refine.getRefines_NamedElement.ref.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.Refine.getRefines_NamedElement.ref.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.Refine.getRefines_NamedElement.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.Refine.getRefines_NamedElement.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.Refine.getRefines_NamedElement.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.Refine.getRefines_NamedElement._rule.getRefines_body"/>
        </ownedOperation>
        <ownedRule xmi:id="SysML.Refine._rule.2_binary" xmi:type="uml:Constraint" name="2_binary">
          <ownedComment xmi:id="SysML.Refine._rule.2_binary._comment0" xmi:type="uml:Comment" body="Abstractions with a Refine stereotype or one of its specializations applied shall have exactly one client and one supplier."/>
          <specification xmi:id="SysML.Refine._rule.2_binary.specification" xmi:type="uml:OpaqueExpression" body="self.base_Abstraction.client-&gt;size()=1 and self.base_Abstraction.supplier-&gt;size()=1" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Trace_base_Abstraction" xmi:type="uml:Extension" name="E_extension_Trace_base_Abstraction">
        <generalization xmi:id="SysML.E_extension_Trace_base_Abstraction._generalization.SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship"/>
        </generalization>
        <memberEnd xmi:idref="SysML.Trace.base_Abstraction"/>
        <memberEnd xmi:idref="SysML.E_extension_Trace_base_Abstraction.extension_Trace"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Trace_base_Abstraction.extension_Trace"/>
        <ownedEnd xmi:id="SysML.E_extension_Trace_base_Abstraction.extension_Trace" xmi:type="uml:ExtensionEnd" name="extension_Trace">
          <association xmi:idref="SysML.E_extension_Trace_base_Abstraction"/>
          <lowerValue xmi:id="SysML.E_extension_Trace_base_Abstraction.extension_Trace.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <redefinedProperty xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship.extension_DirectedRelationshipPropertyPath"/>
          <type xmi:idref="SysML.Trace"/>
          <upperValue xmi:id="SysML.E_extension_Trace_base_Abstraction.extension_Trace.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Requirement_base_Class" xmi:type="uml:Extension" name="E_extension_Requirement_base_Class">
        <memberEnd xmi:idref="SysML.E_extension_Requirement_base_Class.extension_Requirement"/>
        <memberEnd xmi:idref="SysML.Requirement.base_Class"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Requirement_base_Class.extension_Requirement"/>
        <ownedEnd xmi:id="SysML.E_extension_Requirement_base_Class.extension_Requirement" xmi:type="uml:ExtensionEnd" name="extension_Requirement">
          <association xmi:idref="SysML.E_extension_Requirement_base_Class"/>
          <lowerValue xmi:id="SysML.E_extension_Requirement_base_Class.extension_Requirement.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Requirement"/>
          <upperValue xmi:id="SysML.E_extension_Requirement_base_Class.extension_Requirement.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.Satisfy" xmi:type="uml:Stereotype" name="Satisfy">
        <generalization xmi:id="SysML.Satisfy._generalization.SysML.Trace" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.Trace"/>
        </generalization>
        <ownedComment 
        xmi:id="SysML.Satisfy._comment0" xmi:type="uml:Comment" body="A Satisfy relationship is a dependency between a requirement and a model element that fulfills the requirement. As with other dependencies, the arrow direction points from the satisfying (client) model element to the (supplier) requirement that is satisfied.">
</ownedComment>
        <ownedOperation xmi:id="SysML.Satisfy.getSatisfies_NamedElement" xmi:type="uml:Operation" isQuery="true" isStatic="true" name="getSatisfies">
          <bodyCondition xmi:id="SysML.Satisfy.getSatisfies_NamedElement._rule.getSatisfies_body" xmi:type="uml:Constraint" name="getSatisfies_body">
            <specification 
            xmi:id="SysML.Satisfy.getSatisfies_NamedElement._rule.getSatisfies_body.getSatisfies_body_specification" xmi:type="uml:OpaqueExpression" body="Satisfy.allInstances()-&gt;select(base_Abstraction.client=ref).base_Abstraction.supplier" language="OCL" name="getSatisfies_body_specification">
              <type xmi:idref="SysML.AbstractRequirement"/>
</specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.Satisfy.getSatisfies_NamedElement.ref" xmi:type="uml:Parameter" effect="create" name="ref">
            <lowerValue xmi:id="SysML.Satisfy.getSatisfies_NamedElement.ref.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.Satisfy.getSatisfies_NamedElement.ref.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.Satisfy.getSatisfies_NamedElement.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.Satisfy.getSatisfies_NamedElement.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.Satisfy.getSatisfies_NamedElement.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.Satisfy.getSatisfies_NamedElement._rule.getSatisfies_body"/>
        </ownedOperation>
        <ownedRule xmi:id="SysML.Satisfy._rule.1_supplier_is_requirement" xmi:type="uml:Constraint" name="1_supplier_is_requirement">
          <ownedComment xmi:id="SysML.Satisfy._rule.1_supplier_is_requirement._comment0" xmi:type="uml:Comment" body="The supplier shall be an element stereotyped by any subtype of «AbstractRequirement»."/>
          <specification xmi:id="SysML.Satisfy._rule.1_supplier_is_requirement.specification" xmi:type="uml:OpaqueExpression" body="AbstractRequirement.allInstances().base_NamedElement-&gt;includes(self.base_Abstraction.supplier)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Copy" xmi:type="uml:Stereotype" name="Copy">
        <generalization xmi:id="SysML.Copy._generalization.SysML.Trace" xmi:type="uml:Generalization" isSubstitutable="false">
          <general xmi:idref="SysML.Trace"/>
        </generalization>
        <ownedComment 
        xmi:id="SysML.Copy._comment0" xmi:type="uml:Comment" body="A Copy relationship is a dependency between a supplier requirement and a client requirement that specifies that the text of the client requirement is a read-only copy of the text of the supplier requirement.

A Copy dependency created between two requirements maintains a master/slave relationship between the two elements for the purpose of requirements re-use in different contexts. When a Copy dependency exists between two requirements, the requirement text of the client requirement is a read-only copy of the requirement text ofthe requirement at the supplier end of the dependency.">
</ownedComment>
        <ownedOperation xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement" xmi:type="uml:Operation" concurrency="concurrent" isQuery="true" name="isCopy">
          <bodyCondition xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement._rule.body_condition" xmi:type="uml:Constraint" name="body_condition">
            <specification 
            xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement._rule.body_condition.specification" xmi:type="uml:OpaqueExpression" body="let subReq1: Set(AbstractRequirement) = AbstractRequirement.allInstances()
-&gt;select(r | req1.base_NamedElement.ownedElement-&gt;includes(r.base_NamedElement)) in
let subReq2: Set(AbstractRequirement) = AbstractRequirement.allInstances()
-&gt;select(r | req2.base_NamedElement.ownedElement-&gt;includes(r.base_NamedElement)) in
req1.text = req2.text and subReq1-&gt;size() = subReq2-&gt;size() and
subReq1-&gt;forAll(r1 | subReq2-&gt;exists(r2 | self.isCopy(r1, r2) ))" language="OCL" name="specification">
              <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.req1" xmi:type="uml:Parameter" effect="read" name="req1">
            <lowerValue xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.req1.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.req1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.req2" xmi:type="uml:Parameter" effect="read" name="req2">
            <lowerValue xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.req2.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.req2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.result.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
            <upperValue xmi:id="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.Copy.isCopy_AbstractRequirement_AbstractRequirement._rule.body_condition"/>
        </ownedOperation>
        <ownedRule xmi:id="SysML.Copy._rule.1_source_and_taget_are_requirements" xmi:type="uml:Constraint" name="1_source_and_taget_are_requirements">
          <ownedComment xmi:id="SysML.Copy._rule.1_source_and_taget_are_requirements._comment0" xmi:type="uml:Comment" body="A Copy dependency may only be created between two NamedElements that have a subtype of the abstractRequirement stereotype applied"/>
          <specification 
          xmi:id="SysML.Copy._rule.1_source_and_taget_are_requirements.specification" xmi:type="uml:OpaqueExpression" body="AbstractRequirement.allInstances().base_NamedElement-&gt;includesAll(self.base_Abstraction.client) and AbstractRequirement.allInstances().base_NamedElement-&gt;includesAll(self.base_Abstraction.supplier)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Copy._rule.2_same_text" xmi:type="uml:Constraint" name="2_same_text">
          <ownedComment xmi:id="SysML.Copy._rule.2_same_text._comment0" xmi:type="uml:Comment" body="The text property of the client requirement is constrained to be a read-only copy of the text property of the supplier requirement and this applies recursively to all subrequirements"/>
          <specification 
          xmi:id="SysML.Copy._rule.2_same_text.specification" xmi:type="uml:OpaqueExpression" body="let cltReq: AbstractRequirement = AbstractRequirement.allInstances()-&gt;any(r | self.base_Abstraction.client-&gt;includes(r.base_NamedElement)) in let supReq: AbstractRequirement = AbstractRequirement.allInstances()-&gt;any(r | self.base_Abstraction.supplier-&gt;includes(r.base_NamedElement)) in self.isCopy(cltReq, supReq)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_TestCase_base_Operation" xmi:type="uml:Extension" name="E_extension_TestCase_base_Operation">
        <memberEnd xmi:idref="SysML.E_extension_TestCase_base_Operation.extension_TestCase"/>
        <memberEnd xmi:idref="SysML.TestCase.base_Operation"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_TestCase_base_Operation.extension_TestCase"/>
        <ownedEnd xmi:id="SysML.E_extension_TestCase_base_Operation.extension_TestCase" xmi:type="uml:ExtensionEnd" name="extension_TestCase">
          <association xmi:idref="SysML.E_extension_TestCase_base_Operation"/>
          <lowerValue xmi:id="SysML.E_extension_TestCase_base_Operation.extension_TestCase.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.TestCase"/>
          <upperValue xmi:id="SysML.E_extension_TestCase_base_Operation.extension_TestCase.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML_dataType.VerdictKind" xmi:type="uml:Enumeration" name="VerdictKind">
        <ownedComment xmi:id="SysML_dataType.VerdictKind._comment0" xmi:type="uml:Comment" body="Type of a return parameter of a TestCase must be VerdictKind, consistent with the UML Testing Profile."/>
        <ownedLiteral xmi:id="SysML_dataType.VerdictKind.error" xmi:type="uml:EnumerationLiteral" name="error"/>
        <ownedLiteral xmi:id="SysML_dataType.VerdictKind.fail" xmi:type="uml:EnumerationLiteral" name="fail"/>
        <ownedLiteral xmi:id="SysML_dataType.VerdictKind.inconclusive" xmi:type="uml:EnumerationLiteral" name="inconclusive"/>
        <ownedLiteral xmi:id="SysML_dataType.VerdictKind.pass" xmi:type="uml:EnumerationLiteral" name="pass"/>
      </packagedElement>
      <packagedElement xmi:id="SysML.AbstractRequirement" xmi:type="uml:Stereotype" isAbstract="true" name="AbstractRequirement">
        <ownedAttribute xmi:id="SysML.AbstractRequirement.base_NamedElement" xmi:type="uml:Property" name="base_NamedElement">
          <lowerValue xmi:id="SysML.AbstractRequirement.base_NamedElement.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
          <upperValue xmi:id="SysML.AbstractRequirement.base_NamedElement.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.derived" xmi:type="uml:Property" isDerived="true" name="derived">
          <lowerValue xmi:id="SysML.AbstractRequirement.derived.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.AbstractRequirement.derived._comment0" xmi:type="uml:Comment" body="Derived from all requirements that are the client of a «deriveReqt» relationship for which this requirement is a supplier."/>
          <type xmi:idref="SysML.AbstractRequirement"/>
          <upperValue xmi:id="SysML.AbstractRequirement.derived.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.derivedFrom" xmi:type="uml:Property" isDerived="true" name="derivedFrom">
          <lowerValue xmi:id="SysML.AbstractRequirement.derivedFrom.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.AbstractRequirement.derivedFrom._comment0" xmi:type="uml:Comment" body="Derived from all requirements that are the supplier of a «deriveReqt» relationship for which this requirement is a client."/>
          <type xmi:idref="SysML.AbstractRequirement"/>
          <upperValue xmi:id="SysML.AbstractRequirement.derivedFrom.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.id" xmi:type="uml:Property" name="id">
          <defaultValue xmi:id="SysML.AbstractRequirement.id.defaultValue0" xmi:type="uml:LiteralString" name="" value=""/>
          <lowerValue xmi:id="SysML.AbstractRequirement.id.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.AbstractRequirement.id._comment0" xmi:type="uml:Comment" body="The unique id of the requirement."/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.AbstractRequirement.id.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.master" xmi:type="uml:Property" isDerived="true" name="master">
          <lowerValue xmi:id="SysML.AbstractRequirement.master.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.AbstractRequirement.master._comment0" xmi:type="uml:Comment" body="This is a derived property that lists the master requirement for this slave requirement. The master attribute is derived from the supplier of the Copy dependency that has this requirement as the slave."/>
          <type xmi:idref="SysML.AbstractRequirement"/>
          <upperValue xmi:id="SysML.AbstractRequirement.master.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.refinedBy" xmi:type="uml:Property" isDerived="true" name="refinedBy">
          <lowerValue xmi:id="SysML.AbstractRequirement.refinedBy.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.AbstractRequirement.refinedBy._comment0" xmi:type="uml:Comment" body="Derived from all elements that are the client of a «refine» relationship for which this requirement is a supplier."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
          <upperValue xmi:id="SysML.AbstractRequirement.refinedBy.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.satisfiedBy" xmi:type="uml:Property" isDerived="true" name="satisfiedBy">
          <lowerValue xmi:id="SysML.AbstractRequirement.satisfiedBy.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.AbstractRequirement.satisfiedBy._comment0" xmi:type="uml:Comment" body="Derived from all elements that are the client of a «satisfy» relationship for which this requirement is a supplier."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
          <upperValue xmi:id="SysML.AbstractRequirement.satisfiedBy.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.text" xmi:type="uml:Property" name="text">
          <defaultValue xmi:id="SysML.AbstractRequirement.text.defaultValue0" xmi:type="uml:LiteralString" name="" value=""/>
          <lowerValue xmi:id="SysML.AbstractRequirement.text.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.AbstractRequirement.text._comment0" xmi:type="uml:Comment" body="The textual representation or a reference to the textual representation of the requirement."/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.AbstractRequirement.text.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.tracedTo" xmi:type="uml:Property" isDerived="true" name="tracedTo">
          <lowerValue xmi:id="SysML.AbstractRequirement.tracedTo.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.AbstractRequirement.tracedTo._comment0" xmi:type="uml:Comment" body="Derived from all elements that are the client of a «trace» relationship for which this requirement is a supplier."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
          <upperValue xmi:id="SysML.AbstractRequirement.tracedTo.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AbstractRequirement.verifiedBy" xmi:type="uml:Property" isDerived="true" name="verifiedBy">
          <lowerValue xmi:id="SysML.AbstractRequirement.verifiedBy.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.AbstractRequirement.verifiedBy._comment0" xmi:type="uml:Comment" body="Derived from all elements that are the client of a «verify» relationship for which this requirement is a supplier."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
          <upperValue xmi:id="SysML.AbstractRequirement.verifiedBy.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.AbstractRequirement._comment0" xmi:type="uml:Comment" body="An AbstractRequirement establishes the attributes and relationships essential to any potential kind of requirement. Any intended requirement kind should subclass AbstractRequirement. The only normative stereotype based on AbstractRequirement is the Requirement stereotype, described in clause ******** . Examples of additional non-normative stereotypes based on AbstractRequirement are included in clause 22.8 .">
</ownedComment>
        <ownedOperation xmi:id="SysML.AbstractRequirement.getSatisfiedBy_" xmi:type="uml:Operation" name="getSatisfiedBy">
          <bodyCondition xmi:id="SysML.AbstractRequirement.getSatisfiedBy_._rule.getSatisfiedBy_body" xmi:type="uml:Constraint" name="getSatisfiedBy_body">
            <specification 
            xmi:id="SysML.AbstractRequirement.getSatisfiedBy_._rule.getSatisfiedBy_body.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="Satisfy.allInstances()-&gt;select(base_Abstraction.supplier=self).base_Abstraction.client " language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.AbstractRequirement.getSatisfiedBy_.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.AbstractRequirement.getSatisfiedBy_.return.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.AbstractRequirement.getSatisfiedBy_.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.AbstractRequirement.getSatisfiedBy_._rule.getSatisfiedBy_body"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.AbstractRequirement.getVerifiedBy_" xmi:type="uml:Operation" isQuery="true" name="getVerifiedBy">
          <bodyCondition xmi:id="SysML.AbstractRequirement.getVerifiedBy_._rule.getVerifiedBy_body" xmi:type="uml:Constraint" name="getVerifiedBy_body">
            <specification 
            xmi:id="SysML.AbstractRequirement.getVerifiedBy_._rule.getVerifiedBy_body.getVerifiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="Verify.allInstances()-&gt;select(base_Abstraction.supplier=self).base_Abstraction.client" language="OCL" name="getVerifiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.AbstractRequirement.getVerifiedBy_.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.AbstractRequirement.getVerifiedBy_.return.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.AbstractRequirement.getVerifiedBy_.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.AbstractRequirement.getVerifiedBy_._rule.getVerifiedBy_body"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.AbstractRequirement.getTracedTo_" xmi:type="uml:Operation" isQuery="true" name="getTracedTo">
          <bodyCondition xmi:id="SysML.AbstractRequirement.getTracedTo_._rule.getTracedTo_body" xmi:type="uml:Constraint" name="getTracedTo_body">
            <specification xmi:id="SysML.AbstractRequirement.getTracedTo_._rule.getTracedTo_body.getTracedTo_body_specification" xmi:type="uml:OpaqueExpression" body="Trace.allInstances()-&gt;select(base_Abstraction.client=self).base_Abstraction.supplier" language="OCL" name="getTracedTo_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            </specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.AbstractRequirement.getTracedTo_.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.AbstractRequirement.getTracedTo_.return.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.AbstractRequirement.getTracedTo_.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.AbstractRequirement.getTracedTo_._rule.getTracedTo_body"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.AbstractRequirement.getDerived_" xmi:type="uml:Operation" isQuery="true" name="getDerived">
          <bodyCondition xmi:id="SysML.AbstractRequirement.getDerived_._rule.getDerived_body" xmi:type="uml:Constraint" name="getDerived_body">
            <specification xmi:id="SysML.AbstractRequirement.getDerived_._rule.getDerived_body.getDerived_body_specification" xmi:type="uml:OpaqueExpression" body="DeriveReqt.allInstances()-&gt;select(base_Abstraction.supplier=self).base_Abstraction.client" language="OCL" name="getDerived_body_specification">
              <type xmi:idref="SysML.AbstractRequirement"/>
            </specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.AbstractRequirement.getDerived_.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.AbstractRequirement.getDerived_.return.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.AbstractRequirement.getDerived_.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.AbstractRequirement.getDerived_._rule.getDerived_body"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.AbstractRequirement.getDerivedFrom_" xmi:type="uml:Operation" isQuery="true" name="getDerivedFrom">
          <bodyCondition xmi:id="SysML.AbstractRequirement.getDerivedFrom_._rule.getDerivedFrom_body" xmi:type="uml:Constraint" name="getDerivedFrom_body">
            <specification 
            xmi:id="SysML.AbstractRequirement.getDerivedFrom_._rule.getDerivedFrom_body.getDerivedFrom_body_specification" xmi:type="uml:OpaqueExpression" body="DeriveReqt.allInstances()-&gt;select(base_Abstraction.client=self).base_Abstraction.supplier" language="OCL" name="getDerivedFrom_body_specification">
              <type xmi:idref="SysML.AbstractRequirement"/>
</specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.AbstractRequirement.getDerivedFrom_.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.AbstractRequirement.getDerivedFrom_.return.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.AbstractRequirement.getDerivedFrom_.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.AbstractRequirement.getDerivedFrom_._rule.getDerivedFrom_body"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.AbstractRequirement.getRefinedBy_" xmi:type="uml:Operation" isQuery="true" name="getRefinedBy">
          <bodyCondition xmi:id="SysML.AbstractRequirement.getRefinedBy_._rule.getRefinedBy_body" xmi:type="uml:Constraint" name="getRefinedBy_body">
            <specification 
            xmi:id="SysML.AbstractRequirement.getRefinedBy_._rule.getRefinedBy_body.getRefinedBy_body_specification" xmi:type="uml:OpaqueExpression" body="Refine.allInstances()-&gt;select(base_Abstraction.supplier=self).base_Abstraction.client" language="OCL" name="getRefinedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.AbstractRequirement.getRefinedBy_.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.AbstractRequirement.getRefinedBy_.return.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.AbstractRequirement.getRefinedBy_.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.AbstractRequirement.getRefinedBy_._rule.getRefinedBy_body"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.AbstractRequirement.getMaster_" xmi:type="uml:Operation" isQuery="true" name="getMaster">
          <bodyCondition xmi:id="SysML.AbstractRequirement.getMaster_._rule.getMaster_body" xmi:type="uml:Constraint" name="getMaster_body">
            <specification xmi:id="SysML.AbstractRequirement.getMaster_._rule.getMaster_body.getMaster_body_specification" xmi:type="uml:OpaqueExpression" body="Copy.allInstances()-&gt;select(base_Abstraction.client=self).base_Abstraction.supplier" language="OCL" name="getMaster_body_specification">
              <type xmi:idref="SysML.AbstractRequirement"/>
            </specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.AbstractRequirement.getMaster_.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.AbstractRequirement.getMaster_.return.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.AbstractRequirement"/>
            <upperValue xmi:id="SysML.AbstractRequirement.getMaster_.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.AbstractRequirement.getMaster_._rule.getMaster_body"/>
        </ownedOperation>
      </packagedElement>
      <packagedElement xmi:id="SysML.DeriveReqt" xmi:type="uml:Stereotype" name="DeriveReqt">
        <generalization xmi:id="SysML.DeriveReqt._generalization.SysML.Trace" xmi:type="uml:Generalization" isSubstitutable="false">
          <general xmi:idref="SysML.Trace"/>
        </generalization>
        <ownedComment 
        xmi:id="SysML.DeriveReqt._comment0" xmi:type="uml:Comment" body="A DeriveReqt relationship is a dependency between two requirements in which a client requirement can be derived from the supplier requirement. For example, a system requirement may be derived from a business need, or lower-level requirements may be derived from a system requirement. As with other dependencies, the arrow direction points from the derived (client) requirement to the (supplier) requirement from which it is derived.">
</ownedComment>
        <ownedRule xmi:id="SysML.DeriveReqt._rule.1_supplier_is_requirement" xmi:type="uml:Constraint" name="1_supplier_is_requirement">
          <ownedComment xmi:id="SysML.DeriveReqt._rule.1_supplier_is_requirement._comment0" xmi:type="uml:Comment" body="The supplier shall be an element stereotyped by a subtype of AbstractRequirement."/>
          <specification xmi:id="SysML.DeriveReqt._rule.1_supplier_is_requirement.specification" xmi:type="uml:OpaqueExpression" body="AbstractRequirement.allInstances().base_NamedElement-&gt;includesAll(self.base_Abstraction.client)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DeriveReqt._rule.2_client_is_requirement" xmi:type="uml:Constraint" name="2_client_is_requirement">
          <ownedComment xmi:id="SysML.DeriveReqt._rule.2_client_is_requirement._comment0" xmi:type="uml:Comment" body="The client shall be an element stereotyped by a subtype of AbstractRequirement."/>
          <specification xmi:id="SysML.DeriveReqt._rule.2_client_is_requirement.specification" xmi:type="uml:OpaqueExpression" body="AbstractRequirement.allInstances().base_NamedElement-&gt;includesAll(self.base_Abstraction.supplier)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
    </packagedElement>
    <packagedElement xmi:id="SysML.Libraries" xmi:type="uml:Package" URI="" name="Libraries">
      <packagedElement xmi:id="SysML.Libraries.ControlValues" xmi:type="uml:Package" URI="" name="ControlValues">
        <packagedElement xmi:id="SysML_dataType.ControlValueKind" xmi:type="uml:Enumeration" name="ControlValueKind">
          <ownedComment 
          xmi:id="SysML_dataType.ControlValueKind._comment0" xmi:type="uml:Comment" body="The ControlValueKind enumeration is a type for treating control values as data (see clause11.3.2.2) and for UML control pins. It can be used as the type of behavior and operation parameters, object nodes, and attributes, and so on. The possible runtime values are given as enumeration literals. Modelers can extend the enumeration with additional literals, such as suspend, resume, with their own semantics.

The disable literal means a termination of an executing behavior that can only be started again from the beginning (compare to suspend). The enable literal means to start a new execution of a behavior (compare to resume).">
</ownedComment>
          <ownedLiteral xmi:id="SysML_dataType.ControlValueKind.disable" xmi:type="uml:EnumerationLiteral" name="disable">
            <ownedComment xmi:id="SysML_dataType.ControlValueKind.disable._comment0" xmi:type="uml:Comment" body="The disable literal means a termination of an executing behavior that can only be started again from the beginning (compare to suspend)."/>
          </ownedLiteral>
          <ownedLiteral xmi:id="SysML_dataType.ControlValueKind.enable" xmi:type="uml:EnumerationLiteral" name="enable">
            <ownedComment xmi:id="SysML_dataType.ControlValueKind.enable._comment0" xmi:type="uml:Comment" body="The enable literal means to start a new execution of a behavior (compare to resume)."/>
          </ownedLiteral>
          <ownedRule xmi:id="SysML_dataType.ControlValueKind._rule.1_node_is_controltype" xmi:type="uml:Constraint" name="1_node_is_controltype">
            <ownedComment xmi:id="SysML_dataType.ControlValueKind._rule.1_node_is_controltype._comment0" xmi:type="uml:Comment" body="UML::ObjectNode::isControlType is true for object nodes with type ControlValue"/>
            <specification xmi:id="SysML_dataType.ControlValueKind._rule.1_node_is_controltype.specification" xmi:type="uml:OpaqueExpression" body="" language="OCL" name="specification">
              <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
            </specification>
          </ownedRule>
        </packagedElement>
      </packagedElement>
      <packagedElement xmi:id="SysML.Libraries.PrimitiveValueTypes" xmi:type="uml:Package" URI="" name="PrimitiveValueTypes">
        <packagedElement xmi:id="SysML_dataType.Number" xmi:type="uml:PrimitiveType" isAbstract="true" name="Number">
          <ownedComment xmi:id="SysML_dataType.Number._comment0" xmi:type="uml:Comment" body="Number is an abstract value type from which other value types that express concepts of mathematical numbers are specialized."/>
        </packagedElement>
        <packagedElement xmi:id="SysML_dataType.Complex" xmi:type="uml:PrimitiveType" name="Complex">
          <generalization xmi:id="SysML_dataType.Complex._generalization.SysML_dataType.Number" xmi:type="uml:Generalization">
            <general xmi:idref="SysML_dataType.Number"/>
          </generalization>
          <ownedAttribute xmi:id="SysML_dataType.Complex.imaginaryPart" xmi:type="uml:Property" name="imaginaryPart">
            <lowerValue xmi:id="SysML_dataType.Complex.imaginaryPart.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <ownedComment xmi:id="SysML_dataType.Complex.imaginaryPart._comment0" xmi:type="uml:Comment" body="A real number used to express the imaginary part of a complex number."/>
            <type xmi:idref="SysML_dataType.Real"/>
            <upperValue xmi:id="SysML_dataType.Complex.imaginaryPart.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedAttribute>
          <ownedAttribute xmi:id="SysML_dataType.Complex.realPart" xmi:type="uml:Property" name="realPart">
            <lowerValue xmi:id="SysML_dataType.Complex.realPart.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <ownedComment xmi:id="SysML_dataType.Complex.realPart._comment0" xmi:type="uml:Comment" body="A real number used to express the real part of a complex number."/>
            <type xmi:idref="SysML_dataType.Real"/>
            <upperValue xmi:id="SysML_dataType.Complex.realPart.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedAttribute>
          <ownedComment 
          xmi:id="SysML_dataType.Complex._comment0" xmi:type="uml:Comment" body="               A Complex value type represents the mathematical concept of a complex number. A complex number consists of a real part defined by a real number, and an imaginary part defined by a real number multiplied by the square root of -1. Complex numbers are used to express solutions to various forms of mathematical equations.             ">
</ownedComment>
        </packagedElement>
        <packagedElement xmi:id="SysML_dataType.String" xmi:type="uml:PrimitiveType" name="String">
          <ownedComment xmi:id="SysML_dataType.String._comment0" xmi:type="uml:Comment" body="A String value type consists of a sequence of characters in some suitable character set. Character sets may include non-Roman alphabets and characters."/>
        </packagedElement>
        <packagedElement xmi:id="SysML_dataType.Boolean" xmi:type="uml:PrimitiveType" name="Boolean">
          <ownedComment xmi:id="SysML_dataType.Boolean._comment0" xmi:type="uml:Comment" body="A Boolean value type consists of the predefined values true and false."/>
        </packagedElement>
        <packagedElement xmi:id="SysML_dataType.Real" xmi:type="uml:PrimitiveType" name="Real">
          <generalization xmi:id="SysML_dataType.Real._generalization.SysML_dataType.Number" xmi:type="uml:Generalization">
            <general xmi:idref="SysML_dataType.Number"/>
          </generalization>
          <ownedComment 
          xmi:id="SysML_dataType.Real._comment0" xmi:type="uml:Comment" body="A Real value type represents the mathematical concept of a real number. A Real value type may be used to type values that hold continuous quantities, without committing a specific representation such as a floating point data type with restrictions on precision and scale.">
</ownedComment>
        </packagedElement>
        <packagedElement xmi:id="SysML_dataType.Integer" xmi:type="uml:PrimitiveType" name="Integer">
          <generalization xmi:id="SysML_dataType.Integer._generalization.SysML_dataType.Number" xmi:type="uml:Generalization">
            <general xmi:idref="SysML_dataType.Number"/>
          </generalization>
          <ownedComment 
          xmi:id="SysML_dataType.Integer._comment0" xmi:type="uml:Comment" body="An Integer value type represents the mathematical concept of an integer number. An Integer value type may be used to type values that hold negative or positive integer quantities, without committing to a specific representation such as a binary or decimal digits with fixed precision or scale.">
</ownedComment>
        </packagedElement>
      </packagedElement>
      <packagedElement xmi:id="SysML.Libraries.UnitAndQuantityKind" xmi:type="uml:Package" URI="" name="UnitAndQuantityKind">
        <packagedElement xmi:id="SysML.A_quantityKind_unit" xmi:type="uml:Association" name="A_quantityKind_unit">
          <memberEnd xmi:idref="SysML.Unit.quantityKind"/>
          <memberEnd xmi:idref="SysML.A_quantityKind_unit.unit"/>
          <ownedEnd xmi:id="SysML.A_quantityKind_unit.unit" xmi:type="uml:Property" name="unit">
            <association xmi:idref="SysML.A_quantityKind_unit"/>
            <lowerValue xmi:id="SysML.A_quantityKind_unit.unit.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.Unit"/>
            <upperValue xmi:id="SysML.A_quantityKind_unit.unit.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedEnd>
        </packagedElement>
        <packagedElement xmi:id="SysML.QuantityKind" xmi:type="uml:Class" name="QuantityKind">
          <ownedAttribute xmi:id="SysML.QuantityKind.definitionURI" xmi:type="uml:Property" name="definitionURI">
            <lowerValue xmi:id="SysML.QuantityKind.definitionURI.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML_dataType.String"/>
            <upperValue xmi:id="SysML.QuantityKind.definitionURI.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedAttribute>
          <ownedAttribute xmi:id="SysML.QuantityKind.description" xmi:type="uml:Property" name="description">
            <lowerValue xmi:id="SysML.QuantityKind.description.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML_dataType.String"/>
            <upperValue xmi:id="SysML.QuantityKind.description.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedAttribute>
          <ownedAttribute xmi:id="SysML.QuantityKind.symbol" xmi:type="uml:Property" name="symbol">
            <lowerValue xmi:id="SysML.QuantityKind.symbol.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML_dataType.String"/>
            <upperValue xmi:id="SysML.QuantityKind.symbol.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedAttribute>
          <ownedComment 
          xmi:id="SysML.QuantityKind._comment0" xmi:type="uml:Comment" body="A QuantityKind is a kind of quantity that may be stated by means of defined units. For example, the quantity kind of length may be measured by units of meters, kilometers, or feet. QuantityKind is defined as a non-abstract SysML Block defined in the SysML UnitAndQuantityKind model library. QuantityKind, or a specialization of it, classifies an InstanceSpecification to define a particular “kind-of-quantity” in the sense of an “aspect common to mutually comparable quantities” [VIM3-1.2], where a SysML value property is understood to correspond to the VIM concept of “quantity” defined as a “property of a phenomenon, body or substance, where the property has a magnitude that can be expressed as a number and a reference” [VIM3-1.1]. Modelers specialize QuantityKind as done in SysML’s QUDV model library or in a similar manner in other model libraries.
The definitionURI of an InstanceSpecification classified by a kind of QuantityKind identifies the particular “kind-of-quantity” [VIM3-1.2] that the InstanceSpecification represents. Two such InstanceSpecifications represent the same “kind-of-quantity” if and only if their definitionURIs have values and their values are equal. The only valid use of a QuantityKind instance is to be referenced by the quantityKind property of a ValueType or Unit.
See the non-normative model library in E.5 for an optional way to specify more comprehensive definitions of units and quantity kinds as part of systems of units and systems of quantities. The name of a QuantityKind, its definitionURI, or other means may be used to link individual quantity kinds to additional sources of documentation such as this optional model library.">
</ownedComment>
        </packagedElement>
        <packagedElement xmi:id="SysML.Unit" xmi:type="uml:Class" name="Unit">
          <ownedAttribute xmi:id="SysML.Unit.definitionURI" xmi:type="uml:Property" name="definitionURI">
            <lowerValue xmi:id="SysML.Unit.definitionURI.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML_dataType.String"/>
            <upperValue xmi:id="SysML.Unit.definitionURI.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedAttribute>
          <ownedAttribute xmi:id="SysML.Unit.description" xmi:type="uml:Property" name="description">
            <lowerValue xmi:id="SysML.Unit.description.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML_dataType.String"/>
            <upperValue xmi:id="SysML.Unit.description.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedAttribute>
          <ownedAttribute xmi:id="SysML.Unit.quantityKind" xmi:type="uml:Property" name="quantityKind">
            <association xmi:idref="SysML.A_quantityKind_unit"/>
            <lowerValue xmi:id="SysML.Unit.quantityKind.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.QuantityKind"/>
            <upperValue xmi:id="SysML.Unit.quantityKind.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedAttribute>
          <ownedAttribute xmi:id="SysML.Unit.symbol" xmi:type="uml:Property" name="symbol">
            <lowerValue xmi:id="SysML.Unit.symbol.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML_dataType.String"/>
            <upperValue xmi:id="SysML.Unit.symbol.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedAttribute>
          <ownedComment 
          xmi:id="SysML.Unit._comment0" xmi:type="uml:Comment" body=" QuantityKind is a kind of quantity that may be stated by means of defined units. For example, the quantity kind of length may be measured by units of meters, kilometers, or feet. QuantityKind is defined as a non-abstract SysML Block defined in the SysML UnitAndQuantityKind model library. QuantityKind, or a specialization of it, classifies an InstanceSpecification to define a particular “kind-of-quantity” in the sense of an “aspect common to mutually comparable quantities” [VIM3-1.2], where a SysML value property is understood to correspond to the VIM concept of “quantity” defined as a “property of a phenomenon, body or substance, where the property has a magnitude that can be expressed as a number and a reference” [VIM3-1.1]. Modelers specialize QuantityKind as done in SysML’s QUDV model library or in a similar manner in other model libraries.
The definitionURI of an InstanceSpecification classified by a kind of QuantityKind identifies the particular “kind-of-quantity” [VIM3-1.2] that the InstanceSpecification represents. Two such InstanceSpecifications represent the same “kind-of-quantity” if and only if their definitionURIs have values and their values are equal. The only valid use of a QuantityKind instance is to be referenced by the quantityKind property of a ValueType or Unit.
See the non-normative model library in E.5 for an optional way to specify more comprehensive definitions of units and quantity kinds as part of systems of units and systems of quantities. The name of a QuantityKind, its definitionURI, or other means may be used to link individual quantity kinds to additional sources of documentation such as this optional model library.
A Unit is a quantity in terms of which the magnitudes of other quantities that have the same quantity kind can be stated. A unit often relies on precise and reproducible ways to measure the unit. For example, a unit of length such as meter may be specified as a multiple of a particular wavelength of light. A unit may also specify less stable or precise ways to express some value, such as a cost expressed in some currency, or a severity rating measured by a numerical scale.
Unit is defined as a non-abstract SysML Block defined in the SysML UnitAndQuantityKind model library. Unit, or a specialization of it, classifies an InstanceSpecification to define a particular “measurement unit” in the sense of a “real scalar quantity, defined and adopted by convention, with which any other quantity of the same kind can be compared to express the ratio of the two quantities as a number” [VIM3-1.9], where a SysML value property is understood to correspond to the VIM concept of “quantity” defined as a “property of a phenomenon, body or substance, where the property has a magnitude that can be expressed as a number and a reference” [VIM3-1.1]. Modelers specialize Unit as done in SysML’s QUDV model library or in a similar manner in other model libraries.
The definitionURI of an InstanceSpecification classified by a kind of Unit identifies the particular “measurement unit” [VIM3-1.9] that the InstanceSpecification represents. Two such InstanceSpecifications represent the same “measurement unit” if and only if their definitionURIs have values and their values are equal.
The only valid use of a Unit instance is to be referenced by the unit property of a ValueType stereotype.
See the non-normative model library in E.5 for an optional way to specify more comprehensive definitions of units and quantity kinds as part of systems of units and systems of quantities. The name of a Unit, its definitionURI, or other means may be used to link individual units to additional sources of documentation such as this optional model library.">
</ownedComment>
        </packagedElement>
      </packagedElement>
      <profileApplication xmi:id="SysML.Libraries._profileApplication.SysML" xmi:type="uml:ProfileApplication">
        <appliedProfile xmi:idref="SysML"/>
      </profileApplication>
    </packagedElement>
    <packagedElement xmi:id="SysML.Blocks" xmi:type="uml:Package" URI="" name="Blocks">
      <packagedElement xmi:id="SysML.BoundReference" xmi:type="uml:Stereotype" name="BoundReference">
        <generalization xmi:id="SysML.BoundReference._generalization.SysML.EndPathMultiplicity" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.EndPathMultiplicity"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.BoundReference.bindingPath" xmi:type="uml:Property" isDerived="true" isOrdered="true" isUnique="false" name="bindingPath">
          <lowerValue xmi:id="SysML.BoundReference.bindingPath.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.BoundReference.bindingPath._comment0" xmi:type="uml:Comment" body="Gives the propertyPath of the NestedConnectorEnd applied, if any, to the boundEnd, appended to the role of the boundEnd. "/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.BoundReference.bindingPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.BoundReference.boundEnd" xmi:type="uml:Property" name="boundEnd">
          <lowerValue xmi:id="SysML.BoundReference.boundEnd.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.BoundReference.boundEnd._comment0" xmi:type="uml:Comment" body=" Gives a connector end of a binding connector opposite to the end linked to the stereotyped property, or linked to a property that generalizes the stereotyped one through redefinition."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ConnectorEnd"/>
          <upperValue xmi:id="SysML.BoundReference.boundEnd.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedRule xmi:id="SysML.BoundReference._rule.7_cannot_redefine_boundreference" xmi:type="uml:Constraint" name="7_cannot_redefine_boundreference">
          <ownedComment xmi:id="SysML.BoundReference._rule.7_cannot_redefine_boundreference._comment0" xmi:type="uml:Comment" body="BoundReferences shall not be applied to properties that are related by redefinition to other properties with BoundReference applied."/>
          <specification 
          xmi:id="SysML.BoundReference._rule.7_cannot_redefine_boundreference.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.redefinedElement-&gt;notEmpty() implies BoundReference.allInstances().base_Property-&gt;excludesAll(self.base_Property.redefinedElement)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.BoundReference._rule.8_notbounded_to_itslef" xmi:type="uml:Constraint" name="8_notbounded_to_itslef">
          <ownedComment xmi:id="SysML.BoundReference._rule.8_notbounded_to_itslef._comment0" xmi:type="uml:Comment" body="The binding connector identified in constraint 1 shall not have the same property on both ends, or properties related by redefinition."/>
          <specification 
          xmi:id="SysML.BoundReference._rule.8_notbounded_to_itslef.specification" xmi:type="uml:OpaqueExpression" body="let e1: UML::ConnectorEnd = self.boundEnd.owner.oclAsType(UML::Connector).end-&gt;at(1) in let e2: UML::ConnectorEnd = self.boundEnd.owner.oclAsType(UML::Connector).end-&gt;at(2) in e1.role &lt;&gt; e2.role and  (e1.role.oclIsKindOf(UML::Property) and e2.role.oclIsKindOf(UML::Property) implies e1.role.oclAsType(UML::Property).redefinedElement-&gt;excludes(e2.role) and e2.role.oclAsType(UML::Property).redefinedElement-&gt;excludes(e1.role))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.BoundReference._rule.2_opposite_bindingconnector_end" xmi:type="uml:Constraint" name="2_opposite_bindingconnector_end">
          <ownedComment xmi:id="SysML.BoundReference._rule.2_opposite_bindingconnector_end._comment0" xmi:type="uml:Comment" body="The value of boundEnd shall be a connector end of a binding connector, as identified in constraint 1, opposite the property, as identified in constraint 1."/>
          <specification 
          xmi:id="SysML.BoundReference._rule.2_opposite_bindingconnector_end.specification" xmi:type="uml:OpaqueExpression" body="let opposite: UML::ConnectorEnd = BindingConnector.allInstances().base_Connector.end-&gt;any(e | e.role=self.base_Property or self.base_Property-&gt;closure(redefinedElement)-&gt;includes(e.role)) in self.boundEnd = opposite.owner.oclAsType(UML::Connector).end-&gt;any(e | e&lt;&gt;opposite)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.BoundReference._rule.6_ordered_nonunique" xmi:type="uml:Constraint" name="6_ordered_nonunique">
          <ownedComment xmi:id="SysML.BoundReference._rule.6_ordered_nonunique._comment0" xmi:type="uml:Comment" body="Properties with BoundReference applied that have an upper multiplicity greater than one shall be ordered and non-unique."/>
          <specification xmi:id="SysML.BoundReference._rule.6_ordered_nonunique.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.upper &gt; 1 implies self.base_Property.isOrdered and not self.base_Property.isUnique" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.BoundReference._rule.5_reference_or_valueproperty" xmi:type="uml:Constraint" name="5_reference_or_valueproperty">
          <ownedComment xmi:id="SysML.BoundReference._rule.5_reference_or_valueproperty._comment0" xmi:type="uml:Comment" body="Properties to which BoundReference is applied shall either be reference properties or value properties."/>
          <specification xmi:id="SysML.BoundReference._rule.5_reference_or_valueproperty.specification" xmi:type="uml:OpaqueExpression" body="ValueType.allInstances().base_DataType-&gt;includes(self.base_Property.type) or not self.base_Property.isComposite()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.BoundReference._rule.1_bindingconnector_end" xmi:type="uml:Constraint" name="1_bindingconnector_end">
          <ownedComment xmi:id="SysML.BoundReference._rule.1_bindingconnector_end._comment0" xmi:type="uml:Comment" body="Properties to which BoundReference is applied shall be the role of a connector end of at least one binding connector, or generalized by such a property through redefinition"/>
          <specification 
          xmi:id="SysML.BoundReference._rule.1_bindingconnector_end.specification" xmi:type="uml:OpaqueExpression" body="BindingConnector.allInstances().base_Connector.end.role-&gt;exists(r | r=self.base_Property or self.base_Property-&gt;closure(redefinedElement)-&gt;includes(r))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.BoundReference._rule.4_propertypath_consistency" xmi:type="uml:Constraint" name="4_propertypath_consistency">
          <ownedComment xmi:id="SysML.BoundReference._rule.4_propertypath_consistency._comment0" xmi:type="uml:Comment" body="The last value of bindingPath shall be the role of boundEnd, and the other values shall be the propertyPath of the NestedConnectorEnd applied to boundEnd, if any."/>
          <specification 
          xmi:id="SysML.BoundReference._rule.4_propertypath_consistency.specification" xmi:type="uml:OpaqueExpression" body="self.boundEnd = self.bindingPath-&gt;last() and  (let nce: NestedConnectorEnd = NestedConnectorEnd.allInstances()-&gt;any(n| n.base_ConnectorEnd=self.boundEnd) in nce-&gt;oclIsUndefined() or self.bindingPath-&gt;subSequence(1, self.bindingPath-&gt;size()-1) = nce.propertyPath)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.BoundReference._rule.3_navigable" xmi:type="uml:Constraint" name="3_navigable">
          <ownedComment 
          xmi:id="SysML.BoundReference._rule.3_navigable._comment0" xmi:type="uml:Comment" body="The role of boundEnd shall be a property accessible by navigation from instances of the block owning the property to which BoundReference is applied, but shall not be the property to which BoundReference is applied, or one that it is related to by redefinition.">
</ownedComment>
          <specification 
          xmi:id="SysML.BoundReference._rule.3_navigable.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.association-&gt;notEmpty() and  self.boundEnd.definingEnd-&gt;notEmpty() and self.base_Property.association.navigableOwnedEnd-&gt;includes(self.boundEnd.definingEnd)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ClassifierBehaviorProperty_base_Property" xmi:type="uml:Extension" name="E_extension_ClassifierBehaviorProperty_base_Property">
        <memberEnd xmi:idref="SysML.ClassifierBehaviorProperty.base_Property"/>
        <memberEnd xmi:idref="SysML.E_extension_ClassifierBehaviorProperty_base_Property.extension_ClassifierBehaviorProperty"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ClassifierBehaviorProperty_base_Property.extension_ClassifierBehaviorProperty"/>
        <ownedEnd xmi:id="SysML.E_extension_ClassifierBehaviorProperty_base_Property.extension_ClassifierBehaviorProperty" xmi:type="uml:ExtensionEnd" name="extension_ClassifierBehaviorProperty">
          <association xmi:idref="SysML.E_extension_ClassifierBehaviorProperty_base_Property"/>
          <lowerValue xmi:id="SysML.E_extension_ClassifierBehaviorProperty_base_Property.extension_ClassifierBehaviorProperty.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ClassifierBehaviorProperty"/>
          <upperValue xmi:id="SysML.E_extension_ClassifierBehaviorProperty_base_Property.extension_ClassifierBehaviorProperty.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.AdjunctProperty" xmi:type="uml:Stereotype" name="AdjunctProperty">
        <ownedAttribute xmi:id="SysML.AdjunctProperty.base_Property" xmi:type="uml:Property" name="base_Property">
          <association xmi:idref="SysML.E_extension_AdjunctProperty_base_Property"/>
          <lowerValue xmi:id="SysML.AdjunctProperty.base_Property.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.AdjunctProperty.base_Property.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AdjunctProperty.principal" xmi:type="uml:Property" name="principal">
          <association xmi:idref="SysML.A_adjunctProperty_principal"/>
          <lowerValue xmi:id="SysML.AdjunctProperty.principal.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.AdjunctProperty.principal._comment0" xmi:type="uml:Comment" body="Gives the element that determines the values of the property."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Element"/>
          <upperValue xmi:id="SysML.AdjunctProperty.principal.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.AdjunctProperty._comment0" xmi:type="uml:Comment" body="The AdjunctProperty stereotype can be applied to properties to constrain their values to the values of connectors typed by association blocks, call actions, object nodes, variables, parameters, interaction uses, and submachine states. The values of connectors typed by association blocks are the instances of the association block typing a connector in the block having the stereotyped property. The values of call actions are the executions of behaviors invoked by the behavior having the call action and the stereotyped property (see ******** , Notation for more about this use of the stereotype). The values of object nodes are the values of tokens in the object nodes of the behavior having the stereotyped property (see ********.1 , Notation for more about this use of the stereotype). The values of variables are those assigned by executions of activities that have the stereotyped property. The values of parameters are those assigned by executions of behaviors that have the stereotyped property. The keyword «adjunct» before a property name indicates the property is stereotyped by AdjunctProperty.">
</ownedComment>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.2_same_name" xmi:type="uml:Constraint" name="2_same_name">
          <ownedComment xmi:id="SysML.AdjunctProperty._rule.2_same_name._comment0" xmi:type="uml:Comment" body="Properties to which AdjunctProperty applied shall have the same name as the principal, if the principal is a NamedElement."/>
          <specification xmi:id="SysML.AdjunctProperty._rule.2_same_name.specification" xmi:type="uml:OpaqueExpression" body=" self.principal.oclIsKindOf(UML::NamedElement) implies self.base_Property.name = self.principal.oclAsType(UML::NamedElement).name" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.3_connector_and_callaction_composite" xmi:type="uml:Constraint" name="3_connector_and_callaction_composite">
          <ownedComment xmi:id="SysML.AdjunctProperty._rule.3_connector_and_callaction_composite._comment0" xmi:type="uml:Comment" body="Properties with AdjunctProperty applied that have a Connector or CallAction as principal shall be composite."/>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.3_connector_and_callaction_composite.specification" xmi:type="uml:OpaqueExpression" body=" self.principal.oclIsKindOf(UML::Connector) or self.principal.oclIsKindOf(UML::CallAction) implies self.base_Property.isComposite()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.10_multiplicity_same_or_less_restrictive" xmi:type="uml:Constraint" name="10_multiplicity_same_or_less_restrictive">
          <ownedComment 
          xmi:id="SysML.AdjunctProperty._rule.10_multiplicity_same_or_less_restrictive._comment0" xmi:type="uml:Comment" body="Properties with AdjunctProperty applied that have a Variable or Parameter as principal shall have a lower multiplicity the same as or lower than the lower multiplicity of their principal, and an upper multiplicity the same as or higher than the upper multiplicity of their principal">
</ownedComment>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.10_multiplicity_same_or_less_restrictive.specification" xmi:type="uml:OpaqueExpression" body=" self.principal.oclIsKindOf(UML::MultiplicityElement) implies self.base_Property.lower &lt;=  self.principal.oclAsType(UML::MultiplicityElement).lower and self.base_Property.upper &gt;= self.principal.oclAsType(UML::MultiplicityElement).upper" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.1_principal_kind" xmi:type="uml:Constraint" name="1_principal_kind">
          <ownedComment xmi:id="SysML.AdjunctProperty._rule.1_principal_kind._comment0" xmi:type="uml:Comment" body="The principal of an applied AdjunctProperty shall be a Connector, CallAction, ObjectNode, Variable, Parameter, submachine State, or InteractionUse."/>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.1_principal_kind.specification" xmi:type="uml:OpaqueExpression" body="self.principal.oclIsKindOf(UML::Connector) or self.principal.oclIsKindOf(UML::CallAction) or self.principal.oclIsKindOf(UML::ObjectNode) or self.principal.oclIsKindOf(UML::Variable) or self.principal.oclIsKindOf(UML::Parameter) or self.principal.oclIsKindOf(UML::InteractionUse) or (self.principal.oclIsKindOf(UML::State) and self.principal.oclAsType(UML::State).isSubmachineState)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.9_objectnode_multiplicity" xmi:type="uml:Constraint" name="9_objectnode_multiplicity">
          <ownedComment 
          xmi:id="SysML.AdjunctProperty._rule.9_objectnode_multiplicity._comment0" xmi:type="uml:Comment" body="Properties with AdjunctProperty applied that have an ObjectNode as principal shall have a lower multiplicity of zero and an upper multiplicity the same as or higher than the upperBound of the ObjectNode.">
</ownedComment>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.9_objectnode_multiplicity.specification" xmi:type="uml:OpaqueExpression" body="self.principal.oclIsKindOf(UML::ObjectNode) implies self.base_Property.lower = 0 and self.base_Property.upper &gt;= self.principal.oclAsType(UML::ObjectNode).upperBound.unlimitedValue()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.8_callAction_composite_and_consitent_type" xmi:type="uml:Constraint" name="8_callAction_composite_and_consitent_type">
          <ownedComment 
          xmi:id="SysML.AdjunctProperty._rule.8_callAction_composite_and_consitent_type._comment0" xmi:type="uml:Comment" body="Properties with AdjunctProperty applied that have a CallAction as principal shall be composite and be typed by the behavior invoked by the call action or one of that behavior’s generalizations (for CallOperationActions, this shall generalize all behaviors that might be dispatched), and an upper multiplicity of one if the CallAction invokes a nonreentrant behavior.">
</ownedComment>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.8_callAction_composite_and_consitent_type.specification" xmi:type="uml:OpaqueExpression" body="self.principal.oclIsKindOf(UML::CallAction) implies if self.principal.oclIsKindOf(UML::CallOperationAction) then  let called: Set(UML::Behavior) = self.principal.oclAsType(UML::CallOperationAction).operation.method in  if called-&gt;isEmpty() then   self.base_Property.type-&gt;isEmpty()  else    self.base_Property.type-&gt;notEmpty() and called-&gt;forAll(b | b.general-&gt;including(b)-&gt;includes(self.base_Property.type))  endif else  let called: UML::Behavior = if self.principal.oclIsKindOf(UML::CallBehaviorAction) then   self.principal.oclAsType(UML::CallBehaviorAction).behavior  else   self.principal.oclAsType(UML::StartObjectBehaviorAction).behavior() endif in  if called.oclIsUndefined() then   self.base_Property.type.oclIsUndefined()  else    let behaviors: Set(UML::Behavior) = called-&gt;closure(generalization).oclAsType(UML::Behavior)-&gt;including(called)-&gt;asSet() in    self.base_Property.type-&gt;notEmpty() and behaviors-&gt;includes(self.base_Property.type)  endif endif" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.6_connector_principal_associationblock" xmi:type="uml:Constraint" name="6_connector_principal_associationblock">
          <ownedComment xmi:id="SysML.AdjunctProperty._rule.6_connector_principal_associationblock._comment0" xmi:type="uml:Comment" body="Connectors that are principals of an applied AdjunctProperty shall have association blocks as types"/>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.6_connector_principal_associationblock.specification" xmi:type="uml:OpaqueExpression" body=" self.principal.oclIsKindOf(UML::Connector) implies let type: UML::Association = self.principal.oclAsType(UML::Connector).type in Block.allInstances().base_Class-&gt;includes(type)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.11_submachine_and_interactionuse_composite_and_compatible_type" xmi:type="uml:Constraint" name="11_submachine_and_interactionuse_composite_and _compatible_type">
          <ownedComment 
          xmi:id="SysML.AdjunctProperty._rule.11_submachine_and_interactionuse_composite_and_compatible_type._comment0" xmi:type="uml:Comment" body="Properties with AdjunctProperty applied that have an InteractionUse or submachine State as principal shall be composite and be typed by the interaction or state machine invoked by the interaction use or submachine State or one of their generalizations.">
</ownedComment>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.11_submachine_and_interactionuse_composite_and_compatible_type.specification" xmi:type="uml:OpaqueExpression" body=" self.principal.oclIsKindOf(UML::InteractionUse) or self.principal.oclIsKindOf(UML::State) implies let behavior: UML::Behavior =  if self.principal.oclIsKindOf(UML::InteractionUse) then   self.principal.oclAsType(UML::InteractionUse).refersTo  else  self.principal.oclAsType(UML::State).submachine  endif in if behavior.oclIsUndefined() then  self.base_Property.type-&gt;isEmpty() else  self.base_Property.type-&gt;notEmpty() and behavior-&gt;closure(generalization)-&gt;including(behavior)-&gt;includes(self.base_Property.type) endif" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.7_adjunctproperty_connectorproperty_consistent" xmi:type="uml:Constraint" name="7_adjunctproperty_connectorproperty_consistent">
          <ownedComment xmi:id="SysML.AdjunctProperty._rule.7_adjunctproperty_connectorproperty_consistent._comment0" xmi:type="uml:Comment" body="AdjunctProperty and ConnectorProperty applied to the same property shall have the same values for principal and connector, respectively."/>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.7_adjunctproperty_connectorproperty_consistent.specification" xmi:type="uml:OpaqueExpression" body=" AdjunctProperty.allInstances()-&gt;forAll(ap | let cp: ConnectorProperty = ConnectorProperty.allInstances()-&gt;any(base_Property=ap.base_Property) in (not cp.oclIsUndefined()) implies cp.connector = ap.principal) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.4_same_owner" xmi:type="uml:Constraint" name="4_same_owner">
          <ownedComment xmi:id="SysML.AdjunctProperty._rule.4_same_owner._comment0" xmi:type="uml:Comment" body="Properties with AdjunctProperty applied shall be owned by an element that owns the principal, at least indirectly, or one of that element's specializations."/>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.4_same_owner.specification" xmi:type="uml:OpaqueExpression" body="let owners: Set(UML::Element) = self.principal-&gt;closure(owner) in let specializations: Set(UML::Element) = UML::Classifier.allInstances()-&gt;select(c | c-&gt;closure(general)-&gt;intersection(owners)-&gt;notEmpty()) in owners-&gt;union(specializations)-&gt;includes(self.base_Property.owner)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AdjunctProperty._rule.5_compatible_type" xmi:type="uml:Constraint" name="5_compatible_type">
          <ownedComment 
          xmi:id="SysML.AdjunctProperty._rule.5_compatible_type._comment0" xmi:type="uml:Comment" body="Properties with AdjunctProperty applied that have as principal a Connector, ObjectNode, Variable, or Parameter shall have the same type as the principal or one of that type’s generalizations.">
</ownedComment>
          <specification 
          xmi:id="SysML.AdjunctProperty._rule.5_compatible_type.specification" xmi:type="uml:OpaqueExpression" body="  self.principal.oclIsKindOf(UML::Connector) or self.principal.oclIsKindOf(UML::Variable) or self.principal.oclIsKindOf(UML::Parameter) implies  let principal_type: UML::Classifier = if self.principal.oclIsKindOf(UML::Connector) then self.principal.oclAsType(UML::Connector).type else self.principal.oclAsType(UML::TypedElement).type.oclAsType(UML::Classifier) endif in principal_type-&gt;closure(general)-&gt;including(principal_type)-&gt;includes(self.base_Property.type)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_valueType_quantityKind" xmi:type="uml:Association" name="A_valueType_quantityKind">
        <memberEnd xmi:idref="SysML.A_valueType_quantityKind.valueType"/>
        <memberEnd xmi:idref="SysML.ValueType.quantityKind"/>
        <ownedEnd xmi:id="SysML.A_valueType_quantityKind.valueType" xmi:type="uml:Property" name="valueType">
          <association xmi:idref="SysML.A_valueType_quantityKind"/>
          <lowerValue xmi:id="SysML.A_valueType_quantityKind.valueType.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ValueType"/>
          <upperValue xmi:id="SysML.A_valueType_quantityKind.valueType.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_DistributedProperty_base_Property" xmi:type="uml:Extension" name="E_extension_DistributedProperty_base_Property">
        <memberEnd xmi:idref="SysML.E_extension_DistributedProperty_base_Property.extension_DistributedProperty"/>
        <memberEnd xmi:idref="SysML.DistributedProperty.base_Property"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_DistributedProperty_base_Property.extension_DistributedProperty"/>
        <ownedEnd xmi:id="SysML.E_extension_DistributedProperty_base_Property.extension_DistributedProperty" xmi:type="uml:ExtensionEnd" name="extension_DistributedProperty">
          <association xmi:idref="SysML.E_extension_DistributedProperty_base_Property"/>
          <lowerValue xmi:id="SysML.E_extension_DistributedProperty_base_Property.extension_DistributedProperty.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.DistributedProperty"/>
          <upperValue xmi:id="SysML.E_extension_DistributedProperty_base_Property.extension_DistributedProperty.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ElementPropertyPath_base_Element" xmi:type="uml:Extension" name="E_extension_ElementPropertyPath_base_Element">
        <memberEnd xmi:idref="SysML.ElementPropertyPath.base_Element"/>
        <memberEnd xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element.extension_ElementPropertyPath"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element.extension_ElementPropertyPath"/>
        <ownedEnd xmi:id="SysML.E_extension_ElementPropertyPath_base_Element.extension_ElementPropertyPath" xmi:type="uml:ExtensionEnd" name="extension_ElementPropertyPath">
          <association xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element"/>
          <lowerValue xmi:id="SysML.E_extension_ElementPropertyPath_base_Element.extension_ElementPropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ElementPropertyPath"/>
          <upperValue xmi:id="SysML.E_extension_ElementPropertyPath_base_Element.extension_ElementPropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_directedRelationshipPropertyPath_sourcePropertyPath" xmi:type="uml:Association" name="A_directedRelationshipPropertyPath_sourcePropertyPath">
        <memberEnd xmi:idref="SysML.DirectedRelationshipPropertyPath.sourcePropertyPath"/>
        <memberEnd xmi:idref="SysML.A_directedRelationshipPropertyPath_sourcePropertyPath.directedRelationshipPropertyPath"/>
        <ownedEnd xmi:id="SysML.A_directedRelationshipPropertyPath_sourcePropertyPath.directedRelationshipPropertyPath" xmi:type="uml:Property" name="directedRelationshipPropertyPath">
          <association xmi:idref="SysML.A_directedRelationshipPropertyPath_sourcePropertyPath"/>
          <lowerValue xmi:id="SysML.A_directedRelationshipPropertyPath_sourcePropertyPath.directedRelationshipPropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.DirectedRelationshipPropertyPath"/>
          <upperValue xmi:id="SysML.A_directedRelationshipPropertyPath_sourcePropertyPath.directedRelationshipPropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.BindingConnector" xmi:type="uml:Stereotype" name="BindingConnector">
        <ownedAttribute xmi:id="SysML.BindingConnector.base_Connector" xmi:type="uml:Property" name="base_Connector">
          <association xmi:idref="SysML.E_extension_BindingConnector_base_Connector"/>
          <lowerValue xmi:id="SysML.BindingConnector.base_Connector.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Connector"/>
          <upperValue xmi:id="SysML.BindingConnector.base_Connector.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.BindingConnector._comment0" xmi:type="uml:Comment" body="A Binding Connector is a connector which specifies that the properties at both ends of the connector have equal values. If the properties at the ends of a binding connector are typed by a ValueType, the connector specifies that the instances of the properties shall hold equal values, recursively through any nested properties within the connected properties. If the properties at the ends of a binding connector are typed by a Block, the connector specifies that the instances of the properties shall refer to the same block instance. As with any connector owned by a SysML Block, the ends of a binding connector may be nested within a multi-level path of properties accessible from the owning block. The NestedConnectorEnd stereotype is used to represent such nested ends just as for nested ends of other SysML connectors.">
</ownedComment>
        <ownedRule xmi:id="SysML.BindingConnector._rule.1_compatible_types" xmi:type="uml:Constraint" name="1_compatible_types">
          <ownedComment xmi:id="SysML.BindingConnector._rule.1_compatible_types._comment0" xmi:type="uml:Comment" body="The two ends of a binding connector shall have either the same type or types that are compatible so that equality of their values can be defined."/>
          <specification 
          xmi:id="SysML.BindingConnector._rule.1_compatible_types.specification" xmi:type="uml:OpaqueExpression" body="self.base_Connector.end-&gt;at(1).role.type.conformsTo(self.base_Connector.end-&gt;at(2).role.type) or self.base_Connector.end-&gt;at(2).role.type.conformsTo(self.base_Connector.end-&gt;at(1).role.type)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.DistributedProperty" xmi:type="uml:Stereotype" name="DistributedProperty">
        <ownedAttribute xmi:id="SysML.DistributedProperty.base_Property" xmi:type="uml:Property" name="base_Property">
          <association xmi:idref="SysML.E_extension_DistributedProperty_base_Property"/>
          <lowerValue xmi:id="SysML.DistributedProperty.base_Property.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.DistributedProperty.base_Property.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.DistributedProperty._comment0" xmi:type="uml:Comment" body="DistributedProperty is a stereotype of Property used to apply a probability distribution to the values of the property. Specific distributions should be defined as subclasses of the DistributedProperty stereotype with the operands of the distributions represented by properties of those stereotype subclasses. A sample set of probability distributions that could be applied to value properties is given in clause 22.7 .">
</ownedComment>
        <ownedRule xmi:id="SysML.DistributedProperty._rule.1_block_or_valuetype" xmi:type="uml:Constraint" name="1_block_or_valuetype">
          <ownedComment xmi:id="SysML.DistributedProperty._rule.1_block_or_valuetype._comment0" xmi:type="uml:Comment" body="The DistributedProperty stereotype shall only be applied to properties of classifiers stereotyped by Block or ValueType."/>
          <specification 
          xmi:id="SysML.DistributedProperty._rule.1_block_or_valuetype.specification" xmi:type="uml:OpaqueExpression" body="Block.allInstances().base_Class.oclAsType(UML::Classifier)-&gt;union(ValueType.allInstances().base_DataType)-&gt;includes(self.base_Property.owner) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ParticipantProperty_base_Property" xmi:type="uml:Extension" name="E_extension_ParticipantProperty_base_Property">
        <memberEnd xmi:idref="SysML.E_extension_ParticipantProperty_base_Property.extension_ParticipantProperty"/>
        <memberEnd xmi:idref="SysML.ParticipantProperty.base_Property"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ParticipantProperty_base_Property.extension_ParticipantProperty"/>
        <ownedEnd xmi:id="SysML.E_extension_ParticipantProperty_base_Property.extension_ParticipantProperty" xmi:type="uml:ExtensionEnd" name="extension_ParticipantProperty">
          <association xmi:idref="SysML.E_extension_ParticipantProperty_base_Property"/>
          <lowerValue xmi:id="SysML.E_extension_ParticipantProperty_base_Property.extension_ParticipantProperty.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ParticipantProperty"/>
          <upperValue xmi:id="SysML.E_extension_ParticipantProperty_base_Property.extension_ParticipantProperty.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_BindingConnector_base_Connector" xmi:type="uml:Extension" name="E_extension_BindingConnector_base_Connector">
        <memberEnd xmi:idref="SysML.E_extension_BindingConnector_base_Connector.extension_BindingConnector"/>
        <memberEnd xmi:idref="SysML.BindingConnector.base_Connector"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_BindingConnector_base_Connector.extension_BindingConnector"/>
        <ownedEnd xmi:id="SysML.E_extension_BindingConnector_base_Connector.extension_BindingConnector" xmi:type="uml:ExtensionEnd" name="extension_BindingConnector">
          <association xmi:idref="SysML.E_extension_BindingConnector_base_Connector"/>
          <lowerValue xmi:id="SysML.E_extension_BindingConnector_base_Connector.extension_BindingConnector.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.BindingConnector"/>
          <upperValue xmi:id="SysML.E_extension_BindingConnector_base_Connector.extension_BindingConnector.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ValueType_base_DataType" xmi:type="uml:Extension" name="E_extension_ValueType_base_DataType">
        <memberEnd xmi:idref="SysML.E_extension_ValueType_base_DataType.extension_ValueType"/>
        <memberEnd xmi:idref="SysML.ValueType.base_DataType"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ValueType_base_DataType.extension_ValueType"/>
        <ownedEnd xmi:id="SysML.E_extension_ValueType_base_DataType.extension_ValueType" xmi:type="uml:ExtensionEnd" name="extension_ValueType">
          <association xmi:idref="SysML.E_extension_ValueType_base_DataType"/>
          <lowerValue xmi:id="SysML.E_extension_ValueType_base_DataType.extension_ValueType.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ValueType"/>
          <upperValue xmi:id="SysML.E_extension_ValueType_base_DataType.extension_ValueType.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_valueType_unit" xmi:type="uml:Association" name="A_valueType_unit">
        <memberEnd xmi:idref="SysML.A_valueType_unit.valueType"/>
        <memberEnd xmi:idref="SysML.ValueType.unit"/>
        <ownedEnd xmi:id="SysML.A_valueType_unit.valueType" xmi:type="uml:Property" name="valueType">
          <association xmi:idref="SysML.A_valueType_unit"/>
          <lowerValue xmi:id="SysML.A_valueType_unit.valueType.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ValueType"/>
          <upperValue xmi:id="SysML.A_valueType_unit.valueType.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ConnectorProperty_base_Property" xmi:type="uml:Extension" name="E_extension_ConnectorProperty_base_Property">
        <memberEnd xmi:idref="SysML.E_extension_ConnectorProperty_base_Property.extension_ConnectorProperty"/>
        <memberEnd xmi:idref="SysML.ConnectorProperty.base_Property"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ConnectorProperty_base_Property.extension_ConnectorProperty"/>
        <ownedEnd xmi:id="SysML.E_extension_ConnectorProperty_base_Property.extension_ConnectorProperty" xmi:type="uml:ExtensionEnd" name="extension_ConnectorProperty">
          <association xmi:idref="SysML.E_extension_ConnectorProperty_base_Property"/>
          <lowerValue xmi:id="SysML.E_extension_ConnectorProperty_base_Property.extension_ConnectorProperty.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ConnectorProperty"/>
          <upperValue xmi:id="SysML.E_extension_ConnectorProperty_base_Property.extension_ConnectorProperty.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_elementPropertyPath_propertyPath" xmi:type="uml:Association" name="A_elementPropertyPath_propertyPath">
        <memberEnd xmi:idref="SysML.A_elementPropertyPath_propertyPath.elementPropertyPath"/>
        <memberEnd xmi:idref="SysML.ElementPropertyPath.propertyPath"/>
        <ownedEnd xmi:id="SysML.A_elementPropertyPath_propertyPath.elementPropertyPath" xmi:type="uml:Property" name="elementPropertyPath">
          <association xmi:idref="SysML.A_elementPropertyPath_propertyPath"/>
          <lowerValue xmi:id="SysML.A_elementPropertyPath_propertyPath.elementPropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ElementPropertyPath"/>
          <upperValue xmi:id="SysML.A_elementPropertyPath_propertyPath.elementPropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.ParticipantProperty" xmi:type="uml:Stereotype" name="ParticipantProperty">
        <ownedAttribute xmi:id="SysML.ParticipantProperty.base_Property" xmi:type="uml:Property" name="base_Property">
          <association xmi:idref="SysML.E_extension_ParticipantProperty_base_Property"/>
          <lowerValue xmi:id="SysML.ParticipantProperty.base_Property.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.ParticipantProperty.base_Property.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ParticipantProperty.end" xmi:type="uml:Property" name="end">
          <lowerValue xmi:id="SysML.ParticipantProperty.end.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.ParticipantProperty.end._comment0" xmi:type="uml:Comment" body="A member end of the association block owning the property on which the stereotype is applied."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.ParticipantProperty.end.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ParticipantProperty._comment0" xmi:type="uml:Comment" body="The Block stereotype extends Class, so it can be applied to any specialization of Class, including Association Classes. These are informally called “association blocks.” An association block can own properties and connectors, like any other block. Each instance of an association block can link together instances of the end classifiers of the association.

To refer to linked objects and values of an instance of an association block, it is necessary for the modeler to specify which (participant) properties of the association block identify the instances being linked at which end of the association. The value of a participant property on an instance (link) of the association block is the value or object at the end of the link corresponding to this end of the association.

Participant properties can be the ends of connectors owned by an association block. The association block can be the type of multiple other connectors to reuse the same internal structure for all the connectors. The keyword «participant» before a property name indicates the property is stereotyped by ParticipantProperty. They are always the same as the corresponding association end type.

Participant properties can be the ends of connectors owned by an association block. The association block can be the type of multiple other connectors to reuse the same internal structure for all the connectors. The keyword «participant» before a property name indicates the property is stereotyped by ParticipantProperty. They are always the same as the corresponding association end type">
</ownedComment>
        <ownedRule xmi:id="SysML.ParticipantProperty._rule.6_multiplicity_1" xmi:type="uml:Constraint" name="6_multiplicity_1">
          <ownedComment xmi:id="SysML.ParticipantProperty._rule.6_multiplicity_1._comment0" xmi:type="uml:Comment" body="A property to which the ParticipantProperty is applied shall have a multiplicity of 1."/>
          <specification xmi:id="SysML.ParticipantProperty._rule.6_multiplicity_1.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.lower = 1 and self.base_Property.upper = 1" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ParticipantProperty._rule.3_aggregationkind_none" xmi:type="uml:Constraint" name="3_aggregationkind_none">
          <ownedComment xmi:id="SysML.ParticipantProperty._rule.3_aggregationkind_none._comment0" xmi:type="uml:Comment" body="The aggregation of a property stereotyped by ParticipantProperty shall be none."/>
          <specification xmi:id="SysML.ParticipantProperty._rule.3_aggregationkind_none.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.aggregation = UML::AggregationKind::none" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ParticipantProperty._rule.5_same_type" xmi:type="uml:Constraint" name="5_same_type">
          <ownedComment xmi:id="SysML.ParticipantProperty._rule.5_same_type._comment0" xmi:type="uml:Comment" body="A property stereotyped by ParticipantProperty shall have the same type as the property referred to by the end attribute."/>
          <specification xmi:id="SysML.ParticipantProperty._rule.5_same_type.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.type = self.end.type" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ParticipantProperty._rule.2_memberend" xmi:type="uml:Constraint" name="2_memberend">
          <ownedComment xmi:id="SysML.ParticipantProperty._rule.2_memberend._comment0" xmi:type="uml:Comment" body="ParticipantProperty shall not be applied to properties that are member ends of an association."/>
          <specification xmi:id="SysML.ParticipantProperty._rule.2_memberend.specification" xmi:type="uml:OpaqueExpression" body="UML::Association.allInstances().memberEnd-&gt;flatten()-&gt;excludes(self.base_Property)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ParticipantProperty._rule.4_end_owner" xmi:type="uml:Constraint" name="4_end_owner">
          <ownedComment xmi:id="SysML.ParticipantProperty._rule.4_end_owner._comment0" xmi:type="uml:Comment" body="The end attribute of the applied stereotype shall refer to a member end of the association block owning the property on which the stereotype is applied."/>
          <specification xmi:id="SysML.ParticipantProperty._rule.4_end_owner.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.association.memberEnd-&gt;includes(self.end)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ParticipantProperty._rule.1_associationblock" xmi:type="uml:Constraint" name="1_associationblock">
          <ownedComment xmi:id="SysML.ParticipantProperty._rule.1_associationblock._comment0" xmi:type="uml:Comment" body="ParticipantProperty shall only be applied to properties of association classes stereotyped by Block."/>
          <specification 
          xmi:id="SysML.ParticipantProperty._rule.1_associationblock.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.class.oclIsKindOf(UML::AssociationClass) and  Block.allInstances().base_Class-&gt;includes(self.base_Property.class)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_adjunctProperty_principal" xmi:type="uml:Association" name="A_adjunctProperty_principal">
        <memberEnd xmi:idref="SysML.AdjunctProperty.principal"/>
        <memberEnd xmi:idref="SysML.A_adjunctProperty_principal.adjunctProperty"/>
        <ownedEnd xmi:id="SysML.A_adjunctProperty_principal.adjunctProperty" xmi:type="uml:Property" name="adjunctProperty">
          <association xmi:idref="SysML.A_adjunctProperty_principal"/>
          <lowerValue xmi:id="SysML.A_adjunctProperty_principal.adjunctProperty.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.AdjunctProperty"/>
          <upperValue xmi:id="SysML.A_adjunctProperty_principal.adjunctProperty.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_PropertySpecificType_base_Classifier" xmi:type="uml:Extension" name="E_extension_PropertySpecificType_base_Classifier">
        <memberEnd xmi:idref="SysML.E_extension_PropertySpecificType_base_Classifier.extension_PropertySpecificType"/>
        <memberEnd xmi:idref="SysML.PropertySpecificType.base_Classifier"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_PropertySpecificType_base_Classifier.extension_PropertySpecificType"/>
        <ownedEnd xmi:id="SysML.E_extension_PropertySpecificType_base_Classifier.extension_PropertySpecificType" xmi:type="uml:ExtensionEnd" name="extension_PropertySpecificType">
          <association xmi:idref="SysML.E_extension_PropertySpecificType_base_Classifier"/>
          <lowerValue xmi:id="SysML.E_extension_PropertySpecificType_base_Classifier.extension_PropertySpecificType.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.PropertySpecificType"/>
          <upperValue xmi:id="SysML.E_extension_PropertySpecificType_base_Classifier.extension_PropertySpecificType.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.ConnectorProperty" xmi:type="uml:Stereotype" name="ConnectorProperty">
        <ownedAttribute xmi:id="SysML.ConnectorProperty.base_Property" xmi:type="uml:Property" name="base_Property">
          <association xmi:idref="SysML.E_extension_ConnectorProperty_base_Property"/>
          <lowerValue xmi:id="SysML.ConnectorProperty.base_Property.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.ConnectorProperty.base_Property.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ConnectorProperty.connector" xmi:type="uml:Property" name="connector">
          <lowerValue xmi:id="SysML.ConnectorProperty.connector.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.ConnectorProperty.connector._comment0" xmi:type="uml:Comment" body="A connector of the block owning the property on which the stereotype is applied."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Connector"/>
          <upperValue xmi:id="SysML.ConnectorProperty.connector.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ConnectorProperty._comment0" xmi:type="uml:Comment" body="Connectors can be typed by association classes that are stereotyped by Block (association blocks, see ParticipantProperty on page 60). These connectors specify instances of the association block created within the instances of the block that owns the connector. The values of a connector property are instances of the association block created due to the connector referred to by the connector property.

A connector property can optionally be shown in an internal block diagram with a dotted line from the connector line to a rectangle notating the connector property. The keyword «connector» before a property name indicates the property is stereotyped by ConnectorProperty.">
</ownedComment>
        <ownedRule xmi:id="SysML.ConnectorProperty._rule.1_block_property" xmi:type="uml:Constraint" name="1_block_property">
          <ownedComment xmi:id="SysML.ConnectorProperty._rule.1_block_property._comment0" xmi:type="uml:Comment" body="ConnectorProperty shall only be applied to properties of classes stereotyped by Block."/>
          <specification xmi:id="SysML.ConnectorProperty._rule.1_block_property.specification" xmi:type="uml:OpaqueExpression" body="Block.allInstances().base_Class-&gt;exists(c | c.ownedAttribute-&gt;includes(self.base_Property))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ConnectorProperty._rule.4_typed_by_associationblock" xmi:type="uml:Constraint" name="4_typed_by_associationblock">
          <ownedComment xmi:id="SysML.ConnectorProperty._rule.4_typed_by_associationblock._comment0" xmi:type="uml:Comment" body="The type of the connector referred to by a connector attribute shall be an association class stereotyped by Block."/>
          <specification xmi:id="SysML.ConnectorProperty._rule.4_typed_by_associationblock.specification" xmi:type="uml:OpaqueExpression" body="Block.allInstances().base_Class-&gt;exists(c | c.oclIsKindOf(UML::AssociationClass) and self.connector.type = c)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ConnectorProperty._rule.5_same_name" xmi:type="uml:Constraint" name="5_same_name">
          <ownedComment xmi:id="SysML.ConnectorProperty._rule.5_same_name._comment0" xmi:type="uml:Comment" body="A property stereotyped by ConnectorProperty shall have the same name and type as the connector referred to by the connector attribute."/>
          <specification xmi:id="SysML.ConnectorProperty._rule.5_same_name.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.name = self.connector.name" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ConnectorProperty._rule.3_composite" xmi:type="uml:Constraint" name="3_composite">
          <ownedComment xmi:id="SysML.ConnectorProperty._rule.3_composite._comment0" xmi:type="uml:Comment" body="The aggregation of a property stereotyped by ConnectorProperty shall be composite."/>
          <specification xmi:id="SysML.ConnectorProperty._rule.3_composite.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.isComposite" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ConnectorProperty._rule.2_owned_or_inherited" xmi:type="uml:Constraint" name="2_owned_or_inherited">
          <ownedComment xmi:id="SysML.ConnectorProperty._rule.2_owned_or_inherited._comment0" xmi:type="uml:Comment" body="The connector attribute of the applied stereotype shall refer to a connector owned or inherited by a block owning the property on which the stereotype is applied."/>
          <specification 
          xmi:id="SysML.ConnectorProperty._rule.2_owned_or_inherited.specification" xmi:type="uml:OpaqueExpression" body="let owner: UML::Class = Block.allInstances().base_Class-&gt;any(c | c.ownedAttribute-&gt;includes(self.base_Property)) in owner-&gt;closure(general)-&gt;select(oclIsKindOf(UML::Class)).oclAsType(UML::Class).ownedConnector-&gt;flatten()-&gt;includes(self.connector)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.EndPathMultiplicity" xmi:type="uml:Stereotype" name="EndPathMultiplicity">
        <ownedAttribute xmi:id="SysML.EndPathMultiplicity.base_Property" xmi:type="uml:Property" name="base_Property">
          <association xmi:idref="SysML.E_extension_EndPathMultiplicity_base_Property"/>
          <lowerValue xmi:id="SysML.EndPathMultiplicity.base_Property.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.EndPathMultiplicity.base_Property.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.EndPathMultiplicity.lower" xmi:type="uml:Property" name="lower">
          <defaultValue xmi:id="SysML.EndPathMultiplicity.lower.defaultValue0" xmi:type="uml:LiteralInteger" name=""/>
          <lowerValue xmi:id="SysML.EndPathMultiplicity.lower.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment 
          xmi:id="SysML.EndPathMultiplicity.lower._comment0" xmi:type="uml:Comment" body="Gives the minimum number of values of the property at the end of the related bindingPath, for each object reached by navigation along the bindingPath from an instance of the block owning the property to which EndPathMultiplicity is applied">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Integer"/>
          <upperValue xmi:id="SysML.EndPathMultiplicity.lower.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.EndPathMultiplicity.upper" xmi:type="uml:Property" name="upper">
          <defaultValue xmi:id="SysML.EndPathMultiplicity.upper.defaultValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          <lowerValue xmi:id="SysML.EndPathMultiplicity.upper.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment 
          xmi:id="SysML.EndPathMultiplicity.upper._comment0" xmi:type="uml:Comment" body="Gives the maximum number of values of the property at the end of the related bindingPath, for each object reached by navigation along the bindingPath from an instance of the block owning the property to which EndPathMultiplicity is applied.">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#UnlimitedNatural"/>
          <upperValue xmi:id="SysML.EndPathMultiplicity.upper.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.EndPathMultiplicity._comment0" xmi:type="uml:Comment" body="The EndPathMultiplicity stereotype can be applied to properties that are related by redefinition to properties that have BoundReference applied. The lower and upper properties of the stereotype give the minimum and maximum number of values, respectively, of the property at the bound end of the related bound reference, for each object reached by navigation along its binding path.">
</ownedComment>
        <ownedRule xmi:id="SysML.EndPathMultiplicity._rule.2_non_negative" xmi:type="uml:Constraint" name="2_non_negative">
          <ownedComment xmi:id="SysML.EndPathMultiplicity._rule.2_non_negative._comment0" xmi:type="uml:Comment" body="endPathLower shall be non-negative."/>
          <specification xmi:id="SysML.EndPathMultiplicity._rule.2_non_negative.specification" xmi:type="uml:OpaqueExpression" body="self.lower &gt;= 0" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.EndPathMultiplicity._rule.1_redefinition" xmi:type="uml:Constraint" name="1_redefinition">
          <ownedComment xmi:id="SysML.EndPathMultiplicity._rule.1_redefinition._comment0" xmi:type="uml:Comment" body="Properties to which EndPathMultiplicity is applied shall be related by redefinition to a property to which BoundReference is applied."/>
          <specification 
          xmi:id="SysML.EndPathMultiplicity._rule.1_redefinition.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.redefinedProperty-&gt;notEmpty() and BoundReference.allInstances().base_Property-&gt;exists(p | self.base_Property.redefinedProperty-&gt;includes(p))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_directedRelationshipPropertyPath_targetContext" xmi:type="uml:Association" name="A_directedRelationshipPropertyPath_targetContext">
        <memberEnd xmi:idref="SysML.DirectedRelationshipPropertyPath.targetContext"/>
        <memberEnd xmi:idref="SysML.A_directedRelationshipPropertyPath_targetContext.directedRelationshipPropertyPath"/>
        <ownedEnd xmi:id="SysML.A_directedRelationshipPropertyPath_targetContext.directedRelationshipPropertyPath" xmi:type="uml:Property" name="directedRelationshipPropertyPath">
          <association xmi:idref="SysML.A_directedRelationshipPropertyPath_targetContext"/>
          <lowerValue xmi:id="SysML.A_directedRelationshipPropertyPath_targetContext.directedRelationshipPropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.DirectedRelationshipPropertyPath"/>
          <upperValue xmi:id="SysML.A_directedRelationshipPropertyPath_targetContext.directedRelationshipPropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_directedRelationshipPropertyPath_sourceContext" xmi:type="uml:Association" name="A_directedRelationshipPropertyPath_sourceContext">
        <memberEnd xmi:idref="SysML.DirectedRelationshipPropertyPath.sourceContext"/>
        <memberEnd xmi:idref="SysML.A_directedRelationshipPropertyPath_sourceContext.directedRelationshipPropertyPath"/>
        <ownedEnd xmi:id="SysML.A_directedRelationshipPropertyPath_sourceContext.directedRelationshipPropertyPath" xmi:type="uml:Property" name="directedRelationshipPropertyPath">
          <association xmi:idref="SysML.A_directedRelationshipPropertyPath_sourceContext"/>
          <lowerValue xmi:id="SysML.A_directedRelationshipPropertyPath_sourceContext.directedRelationshipPropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.DirectedRelationshipPropertyPath"/>
          <upperValue xmi:id="SysML.A_directedRelationshipPropertyPath_sourceContext.directedRelationshipPropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.ClassifierBehaviorProperty" xmi:type="uml:Stereotype" name="ClassifierBehaviorProperty">
        <ownedAttribute xmi:id="SysML.ClassifierBehaviorProperty.base_Property" xmi:type="uml:Property" name="base_Property">
          <association xmi:idref="SysML.E_extension_ClassifierBehaviorProperty_base_Property"/>
          <lowerValue xmi:id="SysML.ClassifierBehaviorProperty.base_Property.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.ClassifierBehaviorProperty.base_Property.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ClassifierBehaviorProperty._comment0" xmi:type="uml:Comment" body="The ClassifierBehaviorProperty stereotype can be applied to properties to constrain their values to be the executions of classifier behaviors. The value of properties with ClassifierBehaviorProperty applied are the executions of classifier behaviors invoked by instantiation of the block that owns the stereotyped property or one of its specializations.">
</ownedComment>
        <ownedRule xmi:id="SysML.ClassifierBehaviorProperty._rule.1_owner_classifierbehavior" xmi:type="uml:Constraint" name="1_owner_classifierbehavior">
          <ownedComment xmi:id="SysML.ClassifierBehaviorProperty._rule.1_owner_classifierbehavior._comment0" xmi:type="uml:Comment" body="ClassifierBehaviorProperty shall only be applied to properties owned (not inherited) by blocks that have classifier behaviors."/>
          <specification 
          xmi:id="SysML.ClassifierBehaviorProperty._rule.1_owner_classifierbehavior.specification" xmi:type="uml:OpaqueExpression" body="Block.allInstances().base_Class-&gt;exists(c | c.ownedAttribute-&gt;includes(self.base_Property) and c.classifierBehavior-&gt;notEmpty())" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ClassifierBehaviorProperty._rule.2_composite" xmi:type="uml:Constraint" name="2_composite">
          <ownedComment xmi:id="SysML.ClassifierBehaviorProperty._rule.2_composite._comment0" xmi:type="uml:Comment" body="Properties to which ClassifierBehaviorProperty is applied shall be composite"/>
          <specification xmi:id="SysML.ClassifierBehaviorProperty._rule.2_composite.specification" xmi:type="uml:OpaqueExpression" body="self.base_Property.isComposite" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ClassifierBehaviorProperty._rule.3_typed_by_classifierbehavior" xmi:type="uml:Constraint" name="3_typed_by_classifierbehavior">
          <ownedComment xmi:id="SysML.ClassifierBehaviorProperty._rule.3_typed_by_classifierbehavior._comment0" xmi:type="uml:Comment" body="Properties to which ClassifierBehaviorProperty applied shall be typed by the classifier behavior of their owning block or a generalization of the classifier behavior.">
          </ownedComment>
          <specification 
          xmi:id="SysML.ClassifierBehaviorProperty._rule.3_typed_by_classifierbehavior.specification" xmi:type="uml:OpaqueExpression" body="let clBehavior: UML::Behavior = self.base_Property.owner.oclAsType(UML::Class).classifierBehavior in self.base_Property.type-&gt;notEmpty() and clBehavior-&gt;closure(general)-&gt;including(clBehavior)-&gt;includes(self.base_Property.type)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.PropertySpecificType" xmi:type="uml:Stereotype" name="PropertySpecificType">
        <ownedAttribute xmi:id="SysML.PropertySpecificType.base_Classifier" xmi:type="uml:Property" name="base_Classifier">
          <association xmi:idref="SysML.E_extension_PropertySpecificType_base_Classifier"/>
          <lowerValue xmi:id="SysML.PropertySpecificType.base_Classifier.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Classifier"/>
          <upperValue xmi:id="SysML.PropertySpecificType.base_Classifier.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.PropertySpecificType._comment0" xmi:type="uml:Comment" body="The PropertySpecificType stereotype can be applied to classifiers that type exactly one property and that are owned by the owner of that property. Classifiers with this stereotype applied shall be generalized by at most one other classifier.

Instances of a property-specific type are exactly those that are values of the property it types, in all instances of the property owner. Values are (de)classified under property-specific types as they are (removed from) added to the property they type:

• Added values are classified as instances of the property-specific type.
• Removed values are
	• Declassified as instances of the property-specific type.
	• Classified as instances of the most specific generalization of the property-specific type that is not a property-specific type, unless the instances are indirectly classified by that generalization already. If there is no such property-specific type, removed values are not additionally classified.
This enables values of the property to

• Support more features than they would when they are not values of the property.
• Have redefined or constrained features only while they are values of the property.">
</ownedComment>
        <ownedRule xmi:id="SysML.PropertySpecificType._rule.1_only_one_property" xmi:type="uml:Constraint" name="1_only_one_property">
          <ownedComment xmi:id="SysML.PropertySpecificType._rule.1_only_one_property._comment0" xmi:type="uml:Comment" body="A classifier to which the PropertySpecificType stereotype is applied shall be referenced as the type of one and only one property."/>
          <specification xmi:id="SysML.PropertySpecificType._rule.1_only_one_property.specification" xmi:type="uml:OpaqueExpression" body="UML::Property.allInstances()-&gt;select(p | p.type = self.base_Classifier)-&gt;size() = 1" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Block" xmi:type="uml:Stereotype" name="Block">
        <ownedAttribute xmi:id="SysML.Block.base_Class" xmi:type="uml:Property" name="base_Class">
          <association xmi:idref="SysML.E_extension_Block_base_Class"/>
          <lowerValue xmi:id="SysML.Block.base_Class.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
          <upperValue xmi:id="SysML.Block.base_Class.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Block.isEncapsulated" xmi:type="uml:Property" name="isEncapsulated">
          <lowerValue xmi:id="SysML.Block.isEncapsulated.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment 
          xmi:id="SysML.Block.isEncapsulated._comment0" xmi:type="uml:Comment" body="
If true, then the block is treated as a black box; a part typed by this black box can only be connected via its ports or directly to its outer boundary. If false, or if a value is not present, then connections can be established to elements of its internal structure via deep-nested connector ends.">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          <upperValue xmi:id="SysML.Block.isEncapsulated.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Block._comment0" xmi:type="uml:Comment" body="A Block is a modular unit that describes the structure of a system or element. It may include both structural and behavioral features, such as properties and operations, that represent the state of the system and behavior that the system may exhibit. Some of these properties may hold parts of a system, which can also be described by blocks that type the properties. Properties without types do not restrict the instances that can be values of the properties, as if they had the most general type possible. A block may include a structure of connectors between its properties to indicate how its parts or other properties relate to one another.

SysML blocks provide a general-purpose capability to describe the architecture of a system. They provide the ability to represent a system hierarchy, in which a system at one level is composed of systems at a more basic level. They can describe not only the connectivity relationships between the systems at any level, but also quantitative values or other information about a system.

SysML does not restrict the kind of system or system element that may be described by a block. Any reusable form of description that may be applied to a system or a set of system characteristics may be described by a block. Such reusable descriptions, for example, may be applied to purely conceptual aspects of a system design, such as relationships that hold between parts or properties of a system.

Connectors owned by SysML blocks may be used to define relationships between parts or other properties of the same containing block. Connectors can be typed by associations, which can specify more detail about the links between parts or other properties of a system, along with the types of the connected properties. Associations can also be blocks, and when used to type connectors give relationships their own interconnected parts and other properties. Connectors without types do not restrict the way the connected properties are linked together, as if they had the most general type possible. Connectors have both structural and behavioral functions, which can be used together or separately. Connectors as structure specify links between parts or other properties of a system. Connectors as behavior specify communication and item flow between parts or other properties. Connected properties can be linked without specifying communication and item flow, or can specify communication and item flow without specifying a particular kind of link, or both.

SysML excludes variations of associations in UML in which navigable ends can be owned directly by the association. In SysML, navigation is equivalent to a named property owned directly by a block. The only form of an association end that SysML allows an association to own directly is an unnamed end used to carry an inverse multiplicity of a reference property. This unnamed end provides a metamodel element to record an inverse multiplicity, to cover the specific case of a unidirectional reference that defines no named property for navigation in the inverse direction. SysML enforces its equivalence of navigation and ownership by means of constraints that the block stereotype enforces on the existing UML metamodel.

SysML establishes four basic classifications of properties belonging to a SysML Block or ValueType. A property typed by a SysML Block that has composite aggregation is classified as a part property, except for the special case of a constraint property. Constraint properties are further defined in clause 10 . A port is another category of property, as further defined in  Section 9  . A property typed by a Block that does not have composite aggregation is classified as a reference property. A property typed by a SysML ValueType is classified as a value property, and always has composite aggregation. Part, reference, value, and constraint properties may be shown in block definition compartments with the labels “parts,” “references,” “values,” and “constraints” respectively. Properties of any type may be shown in a “properties” compartment or in additional compartments with user-defined labels.

On a block definition diagram, a part property is shown by a black diamond symbol on an association. As in UML, an instance of a block may be included in at most one instance of a block at a time, though possibly as a value of more than one part property of the containing block. A part property holds instances that belong to a larger whole. Typically, a part- whole relationship means that certain operations that apply to the whole also apply to each of the parts. For example, if a whole represents a physical object, a change in position of the whole could also change the position of each of the parts. A property of the whole such as its mass could also be implied by its parts. Operations and relationships that apply to parts typically apply transitively across all parts of these parts, through any number of levels. A particular application domain may establish its own interpretation of part-whole relationships across the blocks defined in a particular model, including the definition of operations that apply to the parts along with the whole. For software objects, a typical interpretation is that delete, copy, and move operations apply across all parts of a composite object.

SysML also supports properties with shared aggregation, as shown by a white diamond symbol on an association. Like UML, SysML defines no specific semantics or constraints for properties with shared aggregation, but particular models or tools may interpret them in specific ways.

In addition to the form of default value specifications that SysML supports on properties of a block (with an optional “=” &lt;value-specification&gt; string following the rest of a property definition), SysML supports an additional form of value specification for properties using initialValue compartments on an internal block diagram (see Internal Block Diagram on page 46). An entire tree of context-specific values can be specified on a containing block to carry values of nested properties as shown on an internal block diagram.

Context-specific values are represented in the SysML metamodel by means of the InstanceValue subtype of UML ValueSpecification. Selected slots of UML instance specifications referenced by these instance values carry the individual values shown in initialValue compartments.

If a property belonging to a block has a specification of initial values for any of the properties belonging to its type, then the default value of that property shall be a UML InstanceValue element. This element shall reference a UML InstanceSpecification element created to hold the initial values of the individual properties within its usage context.

Selected slots of the referenced instance specification shall contain value specifications for the individual property values specified in a corresponding initialValues compartment. If a value of a property is shown by a nested property box with its own initialValues compartment, then the slot of the instance specification for the containing property shall hold a new InstanceValue element. Selected slots of the instance specification referenced by this value shall contain value specifications for any nested initial values, recursively through any number of levels of nesting. A tree of instance values referencing instance specifications, each of which may in turn hold slots carrying instance values, shall exist until self- contained value specifications are reached at the leaf level.">
</ownedComment>
        <ownedRule xmi:id="SysML.Block._rule.7_composition_acyclic" xmi:type="uml:Constraint" name="7_composition_acyclic">
          <ownedComment 
          xmi:id="SysML.Block._rule.7_composition_acyclic._comment0" xmi:type="uml:Comment" body="Within an instance of a SysML Block, the values of any property with composite aggregation (aggregation = composite) shall not contain the block in any of its own properties that also have composite aggregation, or within any unbroken chain of properties that all have composite aggregation. (Within an instance of a SysML Block, the instances of properties with composite aggregation shall form an acyclic graph.)">
</ownedComment>
          <specification xmi:id="SysML.Block._rule.7_composition_acyclic.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class-&gt;closure(part-&gt;select(p|p.type.oclIsKindOf(UML::Class)).type.oclAsType(UML::Class))-&gt;excludes(self.base_Class)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Block._rule.6_valueproperties_composite" xmi:type="uml:Constraint" name="6_valueproperties_composite">
          <ownedComment xmi:id="SysML.Block._rule.6_valueproperties_composite._comment0" xmi:type="uml:Comment" body="If a property owned by a SysML Block or SysML ValueType is typed by a SysML ValueType, then the aggregation attribute of the property shall be “composite.”"/>
          <specification xmi:id="SysML.Block._rule.6_valueproperties_composite.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.ownedAttribute-&gt;select(a| ValueType.allInstances().base_DataType-&gt;includes(a.type))-&gt;forAll(a|a.isComposite())" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Block._rule.1_associations_binary" xmi:type="uml:Constraint" name="1_associations_binary">
          <ownedComment xmi:id="SysML.Block._rule.1_associations_binary._comment0" xmi:type="uml:Comment" body="For an association in which both ends are typed by blocks, the number of ends shall be exactly two"/>
          <specification 
          xmi:id="SysML.Block._rule.1_associations_binary.specification" xmi:type="uml:OpaqueExpression" body="UML::Association.allInstances()-&gt;select(a| a.memberEnd-&gt;forAll(e| e.type-&gt;notEmpty() and Block.allInstances().base_Class-&gt;includes(e.type)))-&gt;forAll(a | a.memberEnd-&gt;size()=2)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Block._rule.8_specializations_are_blocks" xmi:type="uml:Constraint" name="8_specializations_are_blocks">
          <ownedComment xmi:id="SysML.Block._rule.8_specializations_are_blocks._comment0" xmi:type="uml:Comment" body="Any classifier that specializes a Block shall also have the Block stereotype or one of its specializations applied."/>
          <specification 
          xmi:id="SysML.Block._rule.8_specializations_are_blocks.specification" xmi:type="uml:OpaqueExpression" body="UML::Classifier.allInstances()-&gt;select(c | c.general-&gt;includes(self.base_Class))-&gt;forAll(c | Block.allInstances()-&gt;includes(c))" language="OCL" name="specification">
            <ownedComment xmi:id="SysML.Block._rule.8_specializations_are_blocks.specification._comment0" xmi:type="uml:Comment" body="Any classifier that specializes a Block shall also have the Block stereotype or one of its specializations applied."/>
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Block._rule.9_umlconstraint_removed" xmi:type="uml:Constraint" name="9_uml constraint_removed">
          <ownedComment xmi:id="SysML.Block._rule.9_umlconstraint_removed._comment0" xmi:type="uml:Comment" body="The following constraint under 11.8,“ConnectorEnd” in the UML 2 standard is removed by SysML: “[3] The property held in self.partWithPort must not be a Port.”"/>
          <specification xmi:id="SysML.Block._rule.9_umlconstraint_removed.specification" xmi:type="uml:OpaqueExpression" body="-- cannot be expressed in OCL" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Block._rule.2_connectors_binary" xmi:type="uml:Constraint" name="2_connectors_binary">
          <ownedComment 
          xmi:id="SysML.Block._rule.2_connectors_binary._comment0" xmi:type="uml:Comment" body="The number of ends of a connector owned by a block shall be exactly two. (In SysML, a binding connector is not typed by an association, so this constraint is not implied entirely by the preceding constraint.)">
</ownedComment>
          <specification xmi:id="SysML.Block._rule.2_connectors_binary.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.ownedConnector-&gt;forAll(c | c.end-&gt;size()=2 )" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Block._rule.5_uml_connector_constraint_removed" xmi:type="uml:Constraint" name="5_uml_connector_constraint_removed">
          <ownedComment 
          xmi:id="SysML.Block._rule.5_uml_connector_constraint_removed._comment0" xmi:type="uml:Comment" body="The following constraint under 11.8, “Connector” in the UML 2 standard is removed by SysML: “The ConnectableElements attached as roles to each ConnectorEnd owned by a Connector must be roles of the Classifier that owned the Connector, or they must be ports of such roles">
</ownedComment>
          <specification xmi:id="SysML.Block._rule.5_uml_connector_constraint_removed.specification" xmi:type="uml:OpaqueExpression" body="-- Cannot be expressed in OCL" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.ValueType" xmi:type="uml:Stereotype" name="ValueType">
        <ownedAttribute xmi:id="SysML.ValueType.base_DataType" xmi:type="uml:Property" name="base_DataType">
          <association xmi:idref="SysML.E_extension_ValueType_base_DataType"/>
          <lowerValue xmi:id="SysML.ValueType.base_DataType.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#DataType"/>
          <upperValue xmi:id="SysML.ValueType.base_DataType.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ValueType.quantityKind" xmi:type="uml:Property" name="quantityKind">
          <association xmi:idref="SysML.A_valueType_quantityKind"/>
          <lowerValue xmi:id="SysML.ValueType.quantityKind.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment 
          xmi:id="SysML.ValueType.quantityKind._comment0" xmi:type="uml:Comment" body="A ValueType defines types of values that may be used to express information about a system, but cannot be identified as the target of any reference. Since a value cannot be identified except by means of the value itself, each such value within a model is independent of any other, unless other forms of constraints are imposed.

Value types may be used to type properties, operation parameters, or potentially other elements within SysML. SysML defines ValueType as a stereotype of UML DataType to establish a more neutral term for system values that may never be given a concrete data representation. For example, the SysML “Real” ValueType expresses the mathematical concept of a real number, but does not impose any restrictions on the precision or scale of a fixed or floating-point representation that expresses this concept. More specific value types can define the concrete data representations that a digital computer can process, such as conventional Float, Integer, or String types.

SysML ValueType adds an ability to carry a unit of measure and quantity kind associated with the value. A quantity kind is a kind of quantity that may be stated in terms of defined units, but does not restrict the selection of a unit to state the value. A unit is a particular value in terms of which a quantity of the same quantity kind may be expressed. A SysML ValueType and its quantityKind establishes, via UML typing, the associative relationship between a particular “quantity” [VIM3-1.1] (modeled as a SysML value property typed by a ValueType) and a “kind of quantity” [VIM3-1.2] (the ValueType::quantityKind of the SysML value property’s type). This UML/SysML associative relationship reflects the terminological distinction made in VIM3 between the concepts of “quantity” [VIM3-1.1] and “kind-of-quantity” [VIM3- 1.2] that “cannot be in a generic or partitive hierarchical relation to each other” [Dybkaer-2010].

A SysML ValueType may define its own properties and/or operations, just as for a UML DataType. See *******, Block for property classifications that SysML defines for either a Block or ValueType.">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#InstanceSpecification"/>
          <upperValue xmi:id="SysML.ValueType.quantityKind.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ValueType.unit" xmi:type="uml:Property" name="unit">
          <association xmi:idref="SysML.A_valueType_unit"/>
          <lowerValue xmi:id="SysML.ValueType.unit.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.ValueType.unit._comment0" xmi:type="uml:Comment" body="A quantity, represented by an InstanceSpecification classified by a kind of SysML Unit, in terms of which the magnitudes of other quantities that have the same quantity kind can be stated."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#InstanceSpecification"/>
          <upperValue xmi:id="SysML.ValueType.unit.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ValueType._comment0" xmi:type="uml:Comment" body="A ValueType defines types of values that may be used to express information about a system, but cannot be identified as the target of any reference. Since a value cannot be identified except by means of the value itself, each such value within a model is independent of any other, unless other forms of constraints are imposed.

Value types may be used to type properties, operation parameters, or potentially other elements within SysML. SysML defines ValueType as a stereotype of UML DataType to establish a more neutral term for system values that may never be given a concrete data representation. For example, the SysML “Real” ValueType expresses the mathematical concept of a real number, but does not impose any restrictions on the precision or scale of a fixed or floating-point representation that expresses this concept. More specific value types can define the concrete data representations that a digital computer can process, such as conventional Float, Integer, or String types.

SysML ValueType adds an ability to carry a unit of measure and quantity kind associated with the value. A quantity kind is a kind of quantity that may be stated in terms of defined units, but does not restrict the selection of a unit to state the value. A unit is a particular value in terms of which a quantity of the same quantity kind may be expressed. A SysML ValueType and its quantityKind establishes, via UML typing, the associative relationship between a particular “quantity” [VIM3-1.1] (modeled as a SysML value property typed by a ValueType) and a “kind of quantity” [VIM3-1.2] (the ValueType::quantityKind of the SysML value property’s type). This UML/SysML associative relationship reflects the terminological distinction made in VIM3 between the concepts of “quantity” [VIM3-1.1] and “kind-of-quantity” [VIM3- 1.2] that “cannot be in a generic or partitive hierarchical relation to each other” [Dybkaer-2010].

A SysML ValueType may define its own properties and/or operations, just as for a UML DataType. See *******, Block for property classifications that SysML defines for either a Block or ValueType.">
</ownedComment>
        <ownedRule xmi:id="SysML.ValueType._rule.3_quantitykind" xmi:type="uml:Constraint" name="3_quantitykind">
          <ownedComment xmi:id="SysML.ValueType._rule.3_quantitykind._comment0" xmi:type="uml:Comment" body="The quantityKind of a ValueType, if any, shall be an InstanceSpecification classified by SysML’s QuantityKind block in the UnitAndQuantityKind model library or a specialization of it."/>
          <specification 
          xmi:id="SysML.ValueType._rule.3_quantitykind.specification" xmi:type="uml:OpaqueExpression" body="self.quantityKind-&gt;notEmpty() and self.quantityKind.classifier-&gt;notEmpty() implies self.quantityKind.classifier-&gt;forAll(c | c.oclIsKindOf(Libraries::UnitAndQuantityKind::QuantityKind))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ValueType._rule.1_specializations_are_valuetypes" xmi:type="uml:Constraint" name="1_specializations_are_valuetypes">
          <ownedComment xmi:id="SysML.ValueType._rule.1_specializations_are_valuetypes._comment0" xmi:type="uml:Comment" body="Any classifier that specializes a ValueType shall also have the ValueType stereotype applied."/>
          <specification 
          xmi:id="SysML.ValueType._rule.1_specializations_are_valuetypes.specification" xmi:type="uml:OpaqueExpression" body="UML::Classifier.allInstances()-&gt;forAll(c | c.general-&gt;includes(self.base_DataType) implies ValueType.allInstances().base_DataType-&gt;includes(c))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ValueType._rule.2_unit" xmi:type="uml:Constraint" name="2_unit">
          <ownedComment xmi:id="SysML.ValueType._rule.2_unit._comment0" xmi:type="uml:Comment" body="The unit of a ValueType, if any, shall be an InstanceSpecification classified by SysML’s Unit block in the UnitAndQuantityKind model library or a specialization of it."/>
          <specification 
          xmi:id="SysML.ValueType._rule.2_unit.specification" xmi:type="uml:OpaqueExpression" body="self.unit-&gt;notEmpty() and self.unit.classifier-&gt;notEmpty() implies self.unit.classifier-&gt;forAll(c | c.oclIsKindOf(Libraries::UnitAndQuantityKind::Unit))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd" xmi:type="uml:Extension" name="E_extension_NestedConnectorEnd_base_ConnectorEnd">
        <generalization xmi:id="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd._generalization.SysML.E_extension_ElementPropertyPath_base_Element" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element"/>
        </generalization>
        <memberEnd xmi:idref="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd.extension_NestedConnectorEnd"/>
        <memberEnd xmi:idref="SysML.NestedConnectorEnd.base_ConnectorEnd"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd.extension_NestedConnectorEnd"/>
        <ownedEnd xmi:id="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd.extension_NestedConnectorEnd" xmi:type="uml:ExtensionEnd" name="extension_NestedConnectorEnd">
          <association xmi:idref="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd"/>
          <lowerValue xmi:id="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd.extension_NestedConnectorEnd.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <redefinedProperty xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element.extension_ElementPropertyPath"/>
          <type xmi:idref="SysML.NestedConnectorEnd"/>
          <upperValue xmi:id="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd.extension_NestedConnectorEnd.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.ElementPropertyPath" xmi:type="uml:Stereotype" isAbstract="true" name="ElementPropertyPath">
        <ownedAttribute xmi:id="SysML.ElementPropertyPath.base_Element" xmi:type="uml:Property" name="base_Element">
          <association xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element"/>
          <lowerValue xmi:id="SysML.ElementPropertyPath.base_Element.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Element"/>
          <upperValue xmi:id="SysML.ElementPropertyPath.base_Element.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ElementPropertyPath.propertyPath" xmi:type="uml:Property" isOrdered="true" isUnique="false" name="propertyPath">
          <association xmi:idref="SysML.A_elementPropertyPath_propertyPath"/>
          <lowerValue xmi:id="SysML.ElementPropertyPath.propertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment 
          xmi:id="SysML.ElementPropertyPath.propertyPath._comment0" xmi:type="uml:Comment" body="A series of properties that identifies elements in the context of a block described in specializations of ElementPropertyPath. The ordering of properties is from a property of the context block, through a property of each intermediate block that types the preceding property, ending in a property with a type that owns or inherits the fully nested property. The fully nested property is not included in the propertyPath list, but is given by the element to which the ElementPropertyPath is applied in a way described in specializations of ElementPropertyPath. The same property might appear more than once because a block can own a property with the same or specialized block as a type.">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.ElementPropertyPath.propertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ElementPropertyPath._comment0" xmi:type="uml:Comment" body="The ElementPropertyPath stereotype based on UML Element enables elements to identify other elements by a multi-level path of properties accessible from a context block. The context block is described in specializations of ElementPropertyPath.">
</ownedComment>
        <ownedRule xmi:id="SysML.ElementPropertyPath._rule.1_path_consistency" xmi:type="uml:Constraint" name="1_path_consistency">
          <ownedComment 
          xmi:id="SysML.ElementPropertyPath._rule.1_path_consistency._comment0" xmi:type="uml:Comment" body="The property at each successive position of the propertyPath attribute, following the first position, shall be owned by the Block or ValueType that types the property at the immediately preceding position, or a generalization of the Block or ValueType.">
</ownedComment>
          <specification 
          xmi:id="SysML.ElementPropertyPath._rule.1_path_consistency.specification" xmi:type="uml:OpaqueExpression" body="self.propertyPath-&gt;size() &gt;1 implies self.propertyPath-&gt;subSequence(2, self.propertyPath-&gt;size())-&gt;forAll(p | let pp: UML::Property = self.propertyPath-&gt;at(self.propertyPath-&gt;indexOf(p)-1) in let owners: Set(UML::Classifier) = pp.type.oclAsType(UML::Classifier)-&gt;including(pp.type.oclAsType(UML::Classifier)) in owners-&gt;includes(p.owner))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Block_base_Class" xmi:type="uml:Extension" name="E_extension_Block_base_Class">
        <memberEnd xmi:idref="SysML.E_extension_Block_base_Class.extension_Block"/>
        <memberEnd xmi:idref="SysML.Block.base_Class"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Block_base_Class.extension_Block"/>
        <ownedEnd xmi:id="SysML.E_extension_Block_base_Class.extension_Block" xmi:type="uml:ExtensionEnd" name="extension_Block">
          <association xmi:idref="SysML.E_extension_Block_base_Class"/>
          <lowerValue xmi:id="SysML.E_extension_Block_base_Class.extension_Block.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Block"/>
          <upperValue xmi:id="SysML.E_extension_Block_base_Class.extension_Block.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.DirectedRelationshipPropertyPath" xmi:type="uml:Stereotype" isAbstract="true" name="DirectedRelationshipPropertyPath">
        <ownedAttribute xmi:id="SysML.DirectedRelationshipPropertyPath.base_DirectedRelationship" xmi:type="uml:Property" name="base_DirectedRelationship">
          <association xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship"/>
          <lowerValue xmi:id="SysML.DirectedRelationshipPropertyPath.base_DirectedRelationship.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#DirectedRelationship"/>
          <upperValue xmi:id="SysML.DirectedRelationshipPropertyPath.base_DirectedRelationship.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.DirectedRelationshipPropertyPath.sourceContext" xmi:type="uml:Property" name="sourceContext">
          <association xmi:idref="SysML.A_directedRelationshipPropertyPath_sourceContext"/>
          <lowerValue xmi:id="SysML.DirectedRelationshipPropertyPath.sourceContext.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath.sourceContext._comment0" xmi:type="uml:Comment" body="Gives the context for sourcePropertyPath to begin from."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Classifier"/>
          <upperValue xmi:id="SysML.DirectedRelationshipPropertyPath.sourceContext.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.DirectedRelationshipPropertyPath.sourcePropertyPath" xmi:type="uml:Property" isOrdered="true" isUnique="false" name="sourcePropertyPath">
          <association xmi:idref="SysML.A_directedRelationshipPropertyPath_sourcePropertyPath"/>
          <lowerValue xmi:id="SysML.DirectedRelationshipPropertyPath.sourcePropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment 
          xmi:id="SysML.DirectedRelationshipPropertyPath.sourcePropertyPath._comment0" xmi:type="uml:Comment" body="A series of properties that identifies the source of the directed relationship in the context of the block specified by the sourceContext property. The ordering of properties is from a property of the sourceContext block, through a property of each intermediate block that types the preceding property, ending in a property with a type that owns or inherits the source of the directed relationship. The source is not included in the propertyPath list. The same property might appear more than once because a block can own a property with the same or specialized block as a type.">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.DirectedRelationshipPropertyPath.sourcePropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.DirectedRelationshipPropertyPath.targetContext" xmi:type="uml:Property" name="targetContext">
          <association xmi:idref="SysML.A_directedRelationshipPropertyPath_targetContext"/>
          <lowerValue xmi:id="SysML.DirectedRelationshipPropertyPath.targetContext.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath.targetContext._comment0" xmi:type="uml:Comment" body="Gives the context for targetPropertyPath to begin from."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Classifier"/>
          <upperValue xmi:id="SysML.DirectedRelationshipPropertyPath.targetContext.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.DirectedRelationshipPropertyPath.targetPropertyPath" xmi:type="uml:Property" isOrdered="true" isUnique="false" name="targetPropertyPath">
          <association xmi:idref="SysML.A_directedRelationshipPropertyPath_targetPropertyPath"/>
          <lowerValue xmi:id="SysML.DirectedRelationshipPropertyPath.targetPropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment 
          xmi:id="SysML.DirectedRelationshipPropertyPath.targetPropertyPath._comment0" xmi:type="uml:Comment" body="A series of properties that identifies the target of the directed relationship in the context of the block specified by the targetContext property. The ordering of properties is from a property of the targetContext block, through a property of each intermediate block that types the preceding property, ending in a property with a type that owns or inherits the target of the directed relationship. The target is not included in the propertyPath list. The same property might appear more than once because a block can own a property with the same or specialized block as a type.">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.DirectedRelationshipPropertyPath.targetPropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.DirectedRelationshipPropertyPath._comment0" xmi:type="uml:Comment" body="The DirectedRelationshipPropertyPath stereotype based on UML DirectedRelationship enables directed relationships to identify their sources and targets by a multi-level path of properties accessible from context blocks for the sources and targets. Context blocks are typically the owner of the first property in the path of properties, but can be specializations of the owner to limit the scope of the relationship.">
</ownedComment>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.7_path_and_owners_consistency" xmi:type="uml:Constraint" name="7_path_and_owners_consistency">
          <ownedComment 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.7_path_and_owners_consistency._comment0" xmi:type="uml:Comment" body="The property at each successive position of the sourcePropertyPath and targetPropertyPath, following the first position, shall be owned by the Block or ValueType that types the property at the immediately preceding position, or a generalization of the Block or ValueType.">
</ownedComment>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.7_path_and_owners_consistency.specification" xmi:type="uml:OpaqueExpression" body="(self.sourcePropertyPath-&gt;size() &gt;1 implies self.sourcePropertyPath-&gt;subSequence(2, self.sourcePropertyPath-&gt;size())-&gt;forAll(p |  let pp: UML::Property = self.sourcePropertyPath-&gt;at(self.sourcePropertyPath-&gt;indexOf(p)-1) in  let owners: Set(UML::Classifier) = pp.type.oclAsType(UML::Classifier)-&gt;including(pp.type.oclAsType(UML::Classifier)) in  owners-&gt;includes(p.owner))) and (self.targetPropertyPath-&gt;size() &gt;1 implies self.targetPropertyPath-&gt;subSequence(2, self.targetPropertyPath-&gt;size())-&gt;forAll(p |  let pp: UML::Property = self.targetPropertyPath-&gt;at(self.targetPropertyPath-&gt;indexOf(p)-1) in  let owners: Set(UML::Classifier) = pp.type.oclAsType(UML::Classifier)-&gt;including(pp.type.oclAsType(UML::Classifier)) in  owners-&gt;includes(p.owner)))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.5_sourcecontext_owns_sourcepath_first" xmi:type="uml:Constraint" name="5_sourcecontext_owns_sourcepath_first">
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath._rule.5_sourcecontext_owns_sourcepath_first._comment0" xmi:type="uml:Comment" body="The property in the first position of the sourcePropertyPath list, if any, shall be owned by the sourceContext or one of its generalizations"/>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.5_sourcecontext_owns_sourcepath_first.specification" xmi:type="uml:OpaqueExpression" body="self.sourcePropertyPath-&gt;notEmpty() implies self.sourceContext.allAttributes()-&gt;includes(self.sourcePropertyPath-&gt;first())" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.1_sourcecontext_iif_property" xmi:type="uml:Constraint" name="1_sourcecontext_iif_property">
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath._rule.1_sourcecontext_iif_property._comment0" xmi:type="uml:Comment" body="sourceContext shall have a value when source is a property, otherwise it shall not have a value"/>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.1_sourcecontext_iif_property.specification" xmi:type="uml:OpaqueExpression" body="self.base_DirectedRelationship.source-&gt;exists(s | s.oclIsKindOf(UML::Property)) xor self.sourceContext-&gt;isEmpty() " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.2_targetcontext_iif_property" xmi:type="uml:Constraint" name="2_targetcontext_iif_property">
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath._rule.2_targetcontext_iif_property._comment0" xmi:type="uml:Comment" body="targetContext shall have a value when target is a property, otherwise it shall not have a value."/>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.2_targetcontext_iif_property.specification" xmi:type="uml:OpaqueExpression" body="self.base_DirectedRelationship.source-&gt;exists(s | s.oclIsKindOf(UML::Property)) xor self.sourceContext-&gt;isEmpty() " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.8_sourcepath_last_type_owns_source" xmi:type="uml:Constraint" name="8_sourcepath_last_type_owns_source">
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath._rule.8_sourcepath_last_type_owns_source._comment0" xmi:type="uml:Comment" body="The type of the property at the last position of the sourcePropertyPath list shall own or inherit the source of the stereotyped directed relationship."/>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.8_sourcepath_last_type_owns_source.specification" xmi:type="uml:OpaqueExpression" body="self.sourcePropertyPath-&gt;notEmpty() implies self.sourcePropertyPath-&gt;last().type.oclAsType(UML::Classifier).allAttributes()-&gt;includesAll(self.base_DirectedRelationship.source)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.6_targetcontext_owns_targetpath_first" xmi:type="uml:Constraint" name="6_targetcontext_owns_targetpath_first">
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath._rule.6_targetcontext_owns_targetpath_first._comment0" xmi:type="uml:Comment" body="The property in the first position of the targetPropertyPath list, if any, shall be owned by the targetContext or one of its generalizations."/>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.6_targetcontext_owns_targetpath_first.specification" xmi:type="uml:OpaqueExpression" body="self.targetPropertyPath-&gt;notEmpty() implies self.targetContext.allAttributes()-&gt;includes(self.targetPropertyPath-&gt;first())" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.9_targetpath_last_type_owns_target" xmi:type="uml:Constraint" name="9_targetpath_last_type_owns_target">
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath._rule.9_targetpath_last_type_owns_target._comment0" xmi:type="uml:Comment" body="The type of the property at the last position of the targetPropertyPath list shall own or inherit the target of the stereotyped directed relationship."/>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.9_targetpath_last_type_owns_target.specification" xmi:type="uml:OpaqueExpression" body="self.targetPropertyPath-&gt;notEmpty() implies self.targetPropertyPath-&gt;last().type.oclAsType(UML::Classifier).allAttributes()-&gt;includesAll(self.base_DirectedRelationship.target)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.4_targetpropertypath_implies_property" xmi:type="uml:Constraint" name="4_targetpropertypath_implies_property">
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath._rule.4_targetpropertypath_implies_property._comment0" xmi:type="uml:Comment" body="target shall be a property when targetPropertyPath has a value."/>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.4_targetpropertypath_implies_property.specification" xmi:type="uml:OpaqueExpression" body="self.targetPropertyPath-&gt;notEmpty() implies self.base_DirectedRelationship.target-&gt;forAll(s | s.oclIsKindOf(UML::Property)) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedRelationshipPropertyPath._rule.3_sourcepropertypath_implies_property" xmi:type="uml:Constraint" name="3_sourcepropertypath_implies_property">
          <ownedComment xmi:id="SysML.DirectedRelationshipPropertyPath._rule.3_sourcepropertypath_implies_property._comment0" xmi:type="uml:Comment" body="source shall be a property when sourcePropertyPath has a value."/>
          <specification 
          xmi:id="SysML.DirectedRelationshipPropertyPath._rule.3_sourcepropertypath_implies_property.specification" xmi:type="uml:OpaqueExpression" body="self.sourcePropertyPath-&gt;notEmpty() implies self.base_DirectedRelationship.source-&gt;forAll(s | s.oclIsKindOf(UML::Property)) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_AdjunctProperty_base_Property" xmi:type="uml:Extension" name="E_extension_AdjunctProperty_base_Property">
        <memberEnd xmi:idref="SysML.AdjunctProperty.base_Property"/>
        <memberEnd xmi:idref="SysML.E_extension_AdjunctProperty_base_Property.extension_AdjunctProperty"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_AdjunctProperty_base_Property.extension_AdjunctProperty"/>
        <ownedEnd xmi:id="SysML.E_extension_AdjunctProperty_base_Property.extension_AdjunctProperty" xmi:type="uml:ExtensionEnd" name="extension_AdjunctProperty">
          <association xmi:idref="SysML.E_extension_AdjunctProperty_base_Property"/>
          <lowerValue xmi:id="SysML.E_extension_AdjunctProperty_base_Property.extension_AdjunctProperty.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.AdjunctProperty"/>
          <upperValue xmi:id="SysML.E_extension_AdjunctProperty_base_Property.extension_AdjunctProperty.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_directedRelationshipPropertyPath_targetPropertyPath" xmi:type="uml:Association" name="A_directedRelationshipPropertyPath_targetPropertyPath">
        <memberEnd xmi:idref="SysML.DirectedRelationshipPropertyPath.targetPropertyPath"/>
        <memberEnd xmi:idref="SysML.A_directedRelationshipPropertyPath_targetPropertyPath.directedRelationshipPropertyPath"/>
        <ownedEnd xmi:id="SysML.A_directedRelationshipPropertyPath_targetPropertyPath.directedRelationshipPropertyPath" xmi:type="uml:Property" name="directedRelationshipPropertyPath">
          <association xmi:idref="SysML.A_directedRelationshipPropertyPath_targetPropertyPath"/>
          <lowerValue xmi:id="SysML.A_directedRelationshipPropertyPath_targetPropertyPath.directedRelationshipPropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.DirectedRelationshipPropertyPath"/>
          <upperValue xmi:id="SysML.A_directedRelationshipPropertyPath_targetPropertyPath.directedRelationshipPropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.NestedConnectorEnd" xmi:type="uml:Stereotype" name="NestedConnectorEnd">
        <generalization xmi:id="SysML.NestedConnectorEnd._generalization.SysML.ElementPropertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.ElementPropertyPath"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.NestedConnectorEnd.base_ConnectorEnd" xmi:type="uml:Property" name="base_ConnectorEnd">
          <association xmi:idref="SysML.E_extension_NestedConnectorEnd_base_ConnectorEnd"/>
          <lowerValue xmi:id="SysML.NestedConnectorEnd.base_ConnectorEnd.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <redefinedProperty xmi:idref="SysML.ElementPropertyPath.base_Element"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ConnectorEnd"/>
          <upperValue xmi:id="SysML.NestedConnectorEnd.base_ConnectorEnd.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.NestedConnectorEnd._comment0" xmi:type="uml:Comment" body="The NestedConnectorEnd stereotype of UML ConnectorEnd extends a UML ConnectorEnd so that the connected property may be identified by a multi-level path of accessible properties from the block that owns the connector. The propertyPath inherited from ElementPropertyPath gives a series of properties that identifies the connected property in the context of the block that owns the connector. The ordering of properties is from a property of the block that owns the connector, through a property of each intermediate block that types the preceding property, ending in a property with a type that owns or inherits the property that is the role of the connector end (the property that the connector graphically attaches to at that end). The property that is the role of the connector end is not included in the propertyPath list.">
</ownedComment>
        <ownedRule xmi:id="SysML.NestedConnectorEnd._rule.1_propertypath_first_owned_by_connector_owner" xmi:type="uml:Constraint" name="1_propertypath_first_owned_by_connector_owner">
          <ownedComment xmi:id="SysML.NestedConnectorEnd._rule.1_propertypath_first_owned_by_connector_owner._comment0" xmi:type="uml:Comment" body="The first property in propertyPath shall be owned by the block that owns the connector, or one of the block’s generalizations."/>
          <specification 
          xmi:id="SysML.NestedConnectorEnd._rule.1_propertypath_first_owned_by_connector_owner.specification" xmi:type="uml:OpaqueExpression" body="let owningBlock: UML::Class = self.base_ConnectorEnd.owner.oclAsType(UML::Connector).owner.oclAsType(UML::Class) in (not owningBlock.oclIsUndefined()) and owningBlock-&gt;closure(general)-&gt;including(owningBlock)-&gt;includes(self.propertyPath-&gt;first().owner) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.NestedConnectorEnd._rule.2_propertypath_last_type_owns_role" xmi:type="uml:Constraint" name="2_propertypath_last_type_owns_role">
          <ownedComment xmi:id="SysML.NestedConnectorEnd._rule.2_propertypath_last_type_owns_role._comment0" xmi:type="uml:Comment" body="The type of the property at the last position of the propertyPath list shall own or inherit the role property of the stereotyped connector end"/>
          <specification 
          xmi:id="SysML.NestedConnectorEnd._rule.2_propertypath_last_type_owns_role.specification" xmi:type="uml:OpaqueExpression" body="let type: UML::Classifier = self.propertyPath-&gt;last().type.oclAsType(UML::Classifier) in (not type.oclIsUndefined()) and type.allFeatures()-&gt;includes(self.base_ConnectorEnd.role) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship" xmi:type="uml:Extension" name="E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship">
        <memberEnd xmi:idref="SysML.DirectedRelationshipPropertyPath.base_DirectedRelationship"/>
        <memberEnd xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship.extension_DirectedRelationshipPropertyPath"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship.extension_DirectedRelationshipPropertyPath"/>
        <ownedEnd xmi:id="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship.extension_DirectedRelationshipPropertyPath" xmi:type="uml:ExtensionEnd" name="extension_DirectedRelationshipPropertyPath">
          <association xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship"/>
          <lowerValue xmi:id="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship.extension_DirectedRelationshipPropertyPath.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.DirectedRelationshipPropertyPath"/>
          <upperValue xmi:id="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship.extension_DirectedRelationshipPropertyPath.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_EndPathMultiplicity_base_Property" xmi:type="uml:Extension" name="E_extension_EndPathMultiplicity_base_Property">
        <memberEnd xmi:idref="SysML.EndPathMultiplicity.base_Property"/>
        <memberEnd xmi:idref="SysML.E_extension_EndPathMultiplicity_base_Property.extension_EndPathMultiplicity"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_EndPathMultiplicity_base_Property.extension_EndPathMultiplicity"/>
        <ownedEnd xmi:id="SysML.E_extension_EndPathMultiplicity_base_Property.extension_EndPathMultiplicity" xmi:type="uml:ExtensionEnd" name="extension_EndPathMultiplicity">
          <association xmi:idref="SysML.E_extension_EndPathMultiplicity_base_Property"/>
          <lowerValue xmi:id="SysML.E_extension_EndPathMultiplicity_base_Property.extension_EndPathMultiplicity.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.EndPathMultiplicity"/>
          <upperValue xmi:id="SysML.E_extension_EndPathMultiplicity_base_Property.extension_EndPathMultiplicity.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
    </packagedElement>
    <packagedElement xmi:id="SysML.ModelElements" xmi:type="uml:Package" URI="" name="ModelElements">
      <packagedElement xmi:id="SysML.E_extension_Stakeholder_base_Classifier" xmi:type="uml:Extension" name="E_extension_Stakeholder_base_Classifier">
        <memberEnd xmi:idref="SysML.Stakeholder.base_Classifier"/>
        <memberEnd xmi:idref="SysML.E_extension_Stakeholder_base_Classifier.extension_Stakeholder"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Stakeholder_base_Classifier.extension_Stakeholder"/>
        <ownedEnd xmi:id="SysML.E_extension_Stakeholder_base_Classifier.extension_Stakeholder" xmi:type="uml:ExtensionEnd" name="extension_Stakeholder">
          <association xmi:idref="SysML.E_extension_Stakeholder_base_Classifier"/>
          <lowerValue xmi:id="SysML.E_extension_Stakeholder_base_Classifier.extension_Stakeholder.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Stakeholder"/>
          <upperValue xmi:id="SysML.E_extension_Stakeholder_base_Classifier.extension_Stakeholder.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Expose_base_Dependency" xmi:type="uml:Extension" name="E_extension_Expose_base_Dependency">
        <memberEnd xmi:idref="SysML.Expose.base_Dependency"/>
        <memberEnd xmi:idref="SysML.E_extension_Expose_base_Dependency.extension_Expose"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Expose_base_Dependency.extension_Expose"/>
        <ownedEnd xmi:id="SysML.E_extension_Expose_base_Dependency.extension_Expose" xmi:type="uml:ExtensionEnd" name="extension_Expose">
          <association xmi:idref="SysML.E_extension_Expose_base_Dependency"/>
          <lowerValue xmi:id="SysML.E_extension_Expose_base_Dependency.extension_Expose.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Expose"/>
          <upperValue xmi:id="SysML.E_extension_Expose_base_Dependency.extension_Expose.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Problem_base_Comment" xmi:type="uml:Extension" name="E_extension_Problem_base_Comment">
        <memberEnd xmi:idref="SysML.E_extension_Problem_base_Comment.extension_Problem"/>
        <memberEnd xmi:idref="SysML.Problem.base_Comment"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Problem_base_Comment.extension_Problem"/>
        <ownedEnd xmi:id="SysML.E_extension_Problem_base_Comment.extension_Problem" xmi:type="uml:ExtensionEnd" name="extension_Problem">
          <association xmi:idref="SysML.E_extension_Problem_base_Comment"/>
          <lowerValue xmi:id="SysML.E_extension_Problem_base_Comment.extension_Problem.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Problem"/>
          <upperValue xmi:id="SysML.E_extension_Problem_base_Comment.extension_Problem.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.Conform" xmi:type="uml:Stereotype" name="Conform">
        <ownedAttribute xmi:id="SysML.Conform.base_Generalization" xmi:type="uml:Property" name="base_Generalization">
          <association xmi:idref="SysML.E_extension_Conform_base_Generalization"/>
          <lowerValue xmi:id="SysML.Conform.base_Generalization.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Generalization"/>
          <upperValue xmi:id="SysML.Conform.base_Generalization.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Conform._comment0" xmi:type="uml:Comment" body="A Conform relationship is a dependency between a view and a viewpoint. The view conforms to the specified rules and conventions detailed in the viewpoint. Conform is a specialization of the UML dependency, and as with other dependencies the arrow direction points from the (client/source) to the (supplier/target).           ">
</ownedComment>
        <ownedRule xmi:id="SysML.Conform._rule.1_general_is_viewpoint" xmi:type="uml:Constraint" name="1_general_is_viewpoint">
          <ownedComment xmi:id="SysML.Conform._rule.1_general_is_viewpoint._comment0" xmi:type="uml:Comment" body="The general classifier shall be an element stereotyped by Viewpoint"/>
          <specification xmi:id="SysML.Conform._rule.1_general_is_viewpoint.specification" xmi:type="uml:OpaqueExpression" body="Viewpoint.allInstances()-&gt;exists(v | v.base_Class = self.base_Generalization.general)" language="OCL" name="specification"/>
        </ownedRule>
        <ownedRule xmi:id="SysML.Conform._rule.2_specific_is_view" xmi:type="uml:Constraint" name="2_specific_is_view">
          <ownedComment xmi:id="SysML.Conform._rule.2_specific_is_view._comment0" xmi:type="uml:Comment" body="The specific classifier shall be an element that is stereotyped by View"/>
          <specification xmi:id="SysML.Conform._rule.2_specific_is_view.specification" xmi:type="uml:OpaqueExpression" body="View.allInstances()-&gt;exists(v | v.base_Class = self.base_Generalization.specific)" language="OCL" name="specification"/>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Stakeholder" xmi:type="uml:Stereotype" name="Stakeholder">
        <ownedAttribute xmi:id="SysML.Stakeholder.base_Classifier" xmi:type="uml:Property" name="base_Classifier">
          <association xmi:idref="SysML.E_extension_Stakeholder_base_Classifier"/>
          <lowerValue xmi:id="SysML.Stakeholder.base_Classifier.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Classifier"/>
          <upperValue xmi:id="SysML.Stakeholder.base_Classifier.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Stakeholder.concern" xmi:type="uml:Property" isDerived="true" name="concern">
          <lowerValue xmi:id="SysML.Stakeholder.concern.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.Stakeholder.concern.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Stakeholder.concernList" xmi:type="uml:Property" name="concernList">
          <lowerValue xmi:id="SysML.Stakeholder.concernList.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Comment"/>
          <upperValue xmi:id="SysML.Stakeholder.concernList.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedComment xmi:id="SysML.Stakeholder._comment0" xmi:type="uml:Comment" body="A stakeholder represents a role, group, or individual who has concerns that will be addressed by the View of the model."/>
        <ownedRule xmi:id="SysML.Stakeholder._rule.1_not_association" xmi:type="uml:Constraint" name="1_not_association">
          <ownedComment xmi:id="SysML.Stakeholder._rule.1_not_association._comment0" xmi:type="uml:Comment" body="A Stakeholder stereotype can only be applied to UML::Actor or UML::Class which are not a UML::Association."/>
          <specification 
          xmi:id="SysML.Stakeholder._rule.1_not_association.specification" xmi:type="uml:OpaqueExpression" body="self.base_Classifier.oclIsKindOf(UML::Actor)
or 
(self.base_Classifier.oclIsKindOf(UML::Class)
and
not self.base_Classifier.oclIsKindOf(UML::Association))" language="OCL" name="specification">
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Stakeholder._rule.not_association" xmi:type="uml:Constraint" name="not_association">
          <ownedComment xmi:id="SysML.Stakeholder._rule.not_association._comment0" xmi:type="uml:Comment" body="The stakeholder stereotype can only be applied to UML::Actor or UML::Class which are not a UML::Association"/>
          <specification 
          xmi:id="SysML.Stakeholder._rule.not_association.specification" xmi:type="uml:OpaqueExpression" body="(self.base_Classifier.oclIsKindOf(UML::Actor) or self.base_Classifier.oclIsKindOf(UML::Class))
and not self.base_Classifier.oclIsKindOf(UML::Association)" language="OCL2.0" name="specification">
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ElementGroup_base_Comment" xmi:type="uml:Extension" name="E_extension_ElementGroup_base_Comment">
        <memberEnd xmi:idref="SysML.ElementGroup.base_Comment"/>
        <memberEnd xmi:idref="SysML.E_extension_ElementGroup_base_Comment.extension_ElementGroup"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ElementGroup_base_Comment.extension_ElementGroup"/>
        <ownedEnd xmi:id="SysML.E_extension_ElementGroup_base_Comment.extension_ElementGroup" xmi:type="uml:ExtensionEnd" name="extension_ElementGroup">
          <association xmi:idref="SysML.E_extension_ElementGroup_base_Comment"/>
          <lowerValue xmi:id="SysML.E_extension_ElementGroup_base_Comment.extension_ElementGroup.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ElementGroup"/>
          <upperValue xmi:id="SysML.E_extension_ElementGroup_base_Comment.extension_ElementGroup.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_View_base_Class" xmi:type="uml:Extension" name="E_extension_View_base_Class">
        <memberEnd xmi:idref="SysML.View.base_Class"/>
        <memberEnd xmi:idref="SysML.E_extension_View_base_Class.extension_View"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_View_base_Class.extension_View"/>
        <ownedEnd xmi:id="SysML.E_extension_View_base_Class.extension_View" xmi:type="uml:ExtensionEnd" name="extension_View">
          <association xmi:idref="SysML.E_extension_View_base_Class"/>
          <lowerValue xmi:id="SysML.E_extension_View_base_Class.extension_View.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.View"/>
          <upperValue xmi:id="SysML.E_extension_View_base_Class.extension_View.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.View" xmi:type="uml:Stereotype" name="View">
        <ownedAttribute xmi:id="SysML.View.base_Class" xmi:type="uml:Property" name="base_Class">
          <association xmi:idref="SysML.E_extension_View_base_Class"/>
          <lowerValue xmi:id="SysML.View.base_Class.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
          <upperValue xmi:id="SysML.View.base_Class.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.View.stakeholder" xmi:type="uml:Property" isDerived="true" name="stakeholder">
          <lowerValue xmi:id="SysML.View.stakeholder.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.View.stakeholder._comment0" xmi:type="uml:Comment" body="The list of stakeholders is derived from the viewpoint the view conforms to."/>
          <type xmi:idref="SysML.Stakeholder"/>
          <upperValue xmi:id="SysML.View.stakeholder.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.View.viewpoint" xmi:type="uml:Property" isDerived="true" name="viewpoint">
          <lowerValue xmi:id="SysML.View.viewpoint.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.View.viewpoint._comment0" xmi:type="uml:Comment" body="The viewpoint for this View is derived from the conform relationship."/>
          <type xmi:idref="SysML.Viewpoint"/>
          <upperValue xmi:id="SysML.View.viewpoint.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.View._comment0" xmi:type="uml:Comment" body="A View is a model element that represents a real world artifact that can be presented to stakeholders. The view is the result of querying one or more models that are defined by a viewpoint method. The view shall conform to the viewpoint in terms of the viewpoint stakeholders, concerns, method, language, and presentation requirements.

It is sometimes desirable to construct views from other views, and to establish an order for presenting the views. Views may include one or more views as properties, each of which conforms to their viewpoint. The order of the referenced views is reflected in the property order.

The information may be presented to the stakeholder in any format specified by the viewpoint, which may include figures, tables, plots, entire documents, presentation slides, or video.">
</ownedComment>
        <ownedRule xmi:id="SysML.View._rule.3_stakeholder_derived_from_conform" xmi:type="uml:Constraint" name="3_stakeholder_derived_from_conform">
          <ownedComment 
          xmi:id="SysML.View._rule.3_stakeholder_derived_from_conform._comment0" xmi:type="uml:Comment" body="The derived values of the stakeholder attribute shall be the classifiers stereotyped by Stakeholder that are the values of the stakeholder attribute of the general classifier of the generalization relationship stereotyped by Conform for which the View is the specific classifier.">
</ownedComment>
          <specification 
          xmi:id="SysML.View._rule.3_stakeholder_derived_from_conform.specification" xmi:type="uml:OpaqueExpression" body="self.stakeholder = Viewpoint.allInstances()-&gt;any(base_Class = Conform.allInstances()-&gt;any(base_Generalization.specific = self.base_Class).base_Generalization.general).stakeholder" language="OCL" name="specification">
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.View._rule.2_viewpoint_derived_from_conform" xmi:type="uml:Constraint" name="2_viewpoint_derived_from_conform">
          <ownedComment 
          xmi:id="SysML.View._rule.2_viewpoint_derived_from_conform._comment0" xmi:type="uml:Comment" body="The derived value of the viewpoint shall be the classifier stereotyped by Viewpoint that is the general classifier of the generalization relationship stereotyped by Conform for which the View is the specific classifier">
</ownedComment>
          <specification 
          xmi:id="SysML.View._rule.2_viewpoint_derived_from_conform.specification" xmi:type="uml:OpaqueExpression" body="self.viewpoint = Viewpoint.allInstances()-&gt;any(base_Class = Conform.allInstances()-&gt;any(base_Generalization.specific = self.base_Class).base_Generalization.general)" language="OCL" name="specification">
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.View._rule.1_single_viewpoint" xmi:type="uml:Constraint" name="1_single_viewpoint">
          <ownedComment xmi:id="SysML.View._rule.1_single_viewpoint._comment0" xmi:type="uml:Comment" body="A view shall only conform to a single viewpoint"/>
          <specification xmi:id="SysML.View._rule.1_single_viewpoint.specification" xmi:type="uml:OpaqueExpression" body="Conform.allInstances()-&gt;select(base_Generalization.specific = self.base_Class)-&gt;size() = 1" language="OCL" name="specification"/>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Expose" xmi:type="uml:Stereotype" name="Expose">
        <ownedAttribute xmi:id="SysML.Expose.base_Dependency" xmi:type="uml:Property" name="base_Dependency">
          <association xmi:idref="SysML.E_extension_Expose_base_Dependency"/>
          <lowerValue xmi:id="SysML.Expose.base_Dependency.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Dependency"/>
          <upperValue xmi:id="SysML.Expose.base_Dependency.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Expose._comment0" xmi:type="uml:Comment" body="The expose relationship relates a view to one or more model elements. Each model element is an access point to initiate the query. The view and the model elements related to the view are passed to the constructor when it is invoked. The method describes how the exposed elements are navigated to extract the desired information.">
</ownedComment>
        <ownedRule xmi:id="SysML.Expose._rule.1_client_is_view" xmi:type="uml:Constraint" name="1_client_is_view">
          <ownedComment xmi:id="SysML.Expose._rule.1_client_is_view._comment0" xmi:type="uml:Comment" body="The client shall be an element stereotyped by View."/>
          <specification xmi:id="SysML.Expose._rule.1_client_is_view.specification" xmi:type="uml:OpaqueExpression" body="View.allInstances()-&gt;exists(v | v.base_Class = self.base_Dependency.client)" language="OCL" name="specification"/>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.Viewpoint" xmi:type="uml:Stereotype" name="Viewpoint">
        <ownedAttribute xmi:id="SysML.Viewpoint.base_Class" xmi:type="uml:Property" name="base_Class">
          <association xmi:idref="SysML.E_extension_Viewpoint_base_Class"/>
          <lowerValue xmi:id="SysML.Viewpoint.base_Class.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Class"/>
          <upperValue xmi:id="SysML.Viewpoint.base_Class.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Viewpoint.concern" xmi:type="uml:Property" isDerived="true" name="concern">
          <lowerValue xmi:id="SysML.Viewpoint.concern.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.Viewpoint.concern._comment0" xmi:type="uml:Comment" body="The interest of the stakeholders displayed as the body of the comments from concernList."/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.Viewpoint.concern.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Viewpoint.concernList" xmi:type="uml:Property" name="concernList">
          <lowerValue xmi:id="SysML.Viewpoint.concernList.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.Viewpoint.concernList._comment0" xmi:type="uml:Comment" body="The interests of the stakeholders addressed by this viewpoint."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Comment"/>
          <upperValue xmi:id="SysML.Viewpoint.concernList.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Viewpoint.language" xmi:type="uml:Property" name="language">
          <lowerValue xmi:id="SysML.Viewpoint.language.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment 
          xmi:id="SysML.Viewpoint.language._comment0" xmi:type="uml:Comment" body="The languages used to express the models that represent content which is represented by the view. The language specification such as its metamodel, profile, or other language specification is referred to by its URI.">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.Viewpoint.language.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Viewpoint.method" xmi:type="uml:Property" isDerived="true" name="method">
          <lowerValue xmi:id="SysML.Viewpoint.method.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.Viewpoint.method._comment0" xmi:type="uml:Comment" body="The behavior is derived from the method of the operation with the Create stereotype."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Behavior"/>
          <upperValue xmi:id="SysML.Viewpoint.method.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Viewpoint.presentation" xmi:type="uml:Property" name="presentation">
          <lowerValue xmi:id="SysML.Viewpoint.presentation.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.Viewpoint.presentation._comment0" xmi:type="uml:Comment" body="The specifications prescribed for formatting and styling the view."/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.Viewpoint.presentation.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Viewpoint.purpose" xmi:type="uml:Property" name="purpose">
          <lowerValue xmi:id="SysML.Viewpoint.purpose.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.Viewpoint.purpose._comment0" xmi:type="uml:Comment" body="The purpose addresses the stakeholder concerns."/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.Viewpoint.purpose.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.Viewpoint.stakeholder" xmi:type="uml:Property" name="stakeholder">
          <lowerValue xmi:id="SysML.Viewpoint.stakeholder.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.Viewpoint.stakeholder._comment0" xmi:type="uml:Comment" body="Set of stakeholders whose concerns are to be addressed by the viewpoint."/>
          <type xmi:idref="SysML.Stakeholder"/>
          <upperValue xmi:id="SysML.Viewpoint.stakeholder.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Viewpoint._comment0" xmi:type="uml:Comment" body="A Viewpoint is a specification of the conventions and rules for constructing and using a view for the purpose of addressing a set of stakeholder concerns. The languages and methods for specifying a view may reference languages and methods in another viewpoint. They specify the elements expected to be represented in the view, and may be formally or informally defined. For example, the security viewpoint may require the security requirements, security functional and physical architecture, and security test cases.           ">
</ownedComment>
        <ownedRule xmi:id="SysML.Viewpoint._rule.1_method_derived_from_create_operations" xmi:type="uml:Constraint" name="1_method_derived_from_create_operations">
          <ownedComment 
          xmi:id="SysML.Viewpoint._rule.1_method_derived_from_create_operations._comment0" xmi:type="uml:Comment" body="The derived values of the method attribute shall be the names of the methods of the operations stereotyped by the UML Create stereotype on the classifier stereotyped by Viewpoint.">
</ownedComment>
          <specification 
          xmi:id="SysML.Viewpoint._rule.1_method_derived_from_create_operations.specification" xmi:type="uml:OpaqueExpression" body="self.method = self.base_Class.allFeatures()-&gt;select(f | f.oclIsKindOf(UML::Operation))-&gt;select(o | Standard::Create.allInstances().base_BehavioralFeature-&gt;includes(o)).oclAsType(UML::Operation).method" language="OCL" name="specification">
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.Viewpoint._rule.2_create_view_operation" xmi:type="uml:Constraint" name="2_create_view_operation">
          <ownedComment xmi:id="SysML.Viewpoint._rule.2_create_view_operation._comment0" xmi:type="uml:Comment" body="The property ownedOperation shall include at least one operation named “View” with the UML Create stereotype applied."/>
          <specification xmi:id="SysML.Viewpoint._rule.2_create_view_operation.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.ownedOperation-&gt;exists(o | o.name='View' and Standard::Create.allInstances().base_BehavioralFeature-&gt;includes(o))" language="OCL" name="specification"/>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Viewpoint_base_Class" xmi:type="uml:Extension" name="E_extension_Viewpoint_base_Class">
        <memberEnd xmi:idref="SysML.E_extension_Viewpoint_base_Class.extension_Viewpoint"/>
        <memberEnd xmi:idref="SysML.Viewpoint.base_Class"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Viewpoint_base_Class.extension_Viewpoint"/>
        <ownedEnd xmi:id="SysML.E_extension_Viewpoint_base_Class.extension_Viewpoint" xmi:type="uml:ExtensionEnd" name="extension_Viewpoint">
          <association xmi:idref="SysML.E_extension_Viewpoint_base_Class"/>
          <lowerValue xmi:id="SysML.E_extension_Viewpoint_base_Class.extension_Viewpoint.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Viewpoint"/>
          <upperValue xmi:id="SysML.E_extension_Viewpoint_base_Class.extension_Viewpoint.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.Rationale" xmi:type="uml:Stereotype" name="Rationale">
        <ownedAttribute xmi:id="SysML.Rationale.base_Comment" xmi:type="uml:Property" name="base_Comment">
          <association xmi:idref="SysML.E_extension_Rationale_base_Comment"/>
          <lowerValue xmi:id="SysML.Rationale.base_Comment.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Comment"/>
          <upperValue xmi:id="SysML.Rationale.base_Comment.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Rationale._comment0" xmi:type="uml:Comment" body="             A Rationale documents the justification for decisions and the requirements, design, and other decisions. A Rationale can be attached to any model element including relationships. It allows the user, for example, to specify a rationale that may reference more detailed documentation such as a trade study or analysis report. Rationale is a stereotype of comment and may be attached to any other model element in the same manner as a comment.           ">
</ownedComment>
      </packagedElement>
      <packagedElement xmi:id="SysML.Problem" xmi:type="uml:Stereotype" name="Problem">
        <ownedAttribute xmi:id="SysML.Problem.base_Comment" xmi:type="uml:Property" name="base_Comment">
          <association xmi:idref="SysML.E_extension_Problem_base_Comment"/>
          <lowerValue xmi:id="SysML.Problem.base_Comment.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Comment"/>
          <upperValue xmi:id="SysML.Problem.base_Comment.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Problem._comment0" xmi:type="uml:Comment" body="             A Problem documents a deficiency, limitation, or failure of one or more model elements to satisfy a requirement or need, or other undesired outcome. It may be used to capture problems identified during analysis, design, verification, or manufacture and associate the problem with the relevant model elements. Problem is a stereotype of comment and may be attached to any other model element in the same manner as a comment.           ">
</ownedComment>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Rationale_base_Comment" xmi:type="uml:Extension" name="E_extension_Rationale_base_Comment">
        <memberEnd xmi:idref="SysML.E_extension_Rationale_base_Comment.extension_Rationale"/>
        <memberEnd xmi:idref="SysML.Rationale.base_Comment"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Rationale_base_Comment.extension_Rationale"/>
        <ownedEnd xmi:id="SysML.E_extension_Rationale_base_Comment.extension_Rationale" xmi:type="uml:ExtensionEnd" name="extension_Rationale">
          <association xmi:idref="SysML.E_extension_Rationale_base_Comment"/>
          <lowerValue xmi:id="SysML.E_extension_Rationale_base_Comment.extension_Rationale.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Rationale"/>
          <upperValue xmi:id="SysML.E_extension_Rationale_base_Comment.extension_Rationale.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.ElementGroup" xmi:type="uml:Stereotype" name="ElementGroup">
        <ownedAttribute xmi:id="SysML.ElementGroup.base_Comment" xmi:type="uml:Property" name="base_Comment">
          <association xmi:idref="SysML.E_extension_ElementGroup_base_Comment"/>
          <lowerValue xmi:id="SysML.ElementGroup.base_Comment.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Comment"/>
          <upperValue xmi:id="SysML.ElementGroup.base_Comment.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ElementGroup.criterion" xmi:type="uml:Property" isDerived="true" name="criterion">
          <lowerValue xmi:id="SysML.ElementGroup.criterion.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.ElementGroup.criterion._comment0" xmi:type="uml:Comment" body="Specifies the rationale for being member of the group. Adding an element to the group asserts that the criterion applies to this element. Derived from Comment::body."/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.ElementGroup.criterion.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ElementGroup.member" xmi:type="uml:Property" isDerived="true" name="member">
          <lowerValue xmi:id="SysML.ElementGroup.member.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.ElementGroup.member._comment0" xmi:type="uml:Comment" body="Set specifying the members of the group. Derived from Comment::annotatedElement."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Element"/>
          <upperValue xmi:id="SysML.ElementGroup.member.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ElementGroup.name" xmi:type="uml:Property" name="name">
          <lowerValue xmi:id="SysML.ElementGroup.name.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.ElementGroup.name._comment0" xmi:type="uml:Comment" body="Name of the element group"/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
          <upperValue xmi:id="SysML.ElementGroup.name.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ElementGroup.orderedMember" xmi:type="uml:Property" isOrdered="true" name="orderedMember">
          <lowerValue xmi:id="SysML.ElementGroup.orderedMember.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment xmi:id="SysML.ElementGroup.orderedMember._comment0" xmi:type="uml:Comment" body="Organize member according to an arbitrary order. Optional."/>
          <subsettedProperty xmi:idref="SysML.ElementGroup.member"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Element"/>
          <upperValue xmi:id="SysML.ElementGroup.orderedMember.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ElementGroup.size" xmi:type="uml:Property" isDerived="true" name="size">
          <lowerValue xmi:id="SysML.ElementGroup.size.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.ElementGroup.size._comment0" xmi:type="uml:Comment" body="Number of members in the group. Derived."/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Integer"/>
          <upperValue xmi:id="SysML.ElementGroup.size.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ElementGroup._comment0" xmi:type="uml:Comment" body="The ElementGroup stereotype provides a lightweight mechanism for grouping various and possibly heterogeneous model elements by extending the capability of comments to refer to multiple annotated elements. For example, it can group elements that are associated with a particular release of the model, have a certain risk level, or are associated with a legacy design. The semantics of ElementGroup is modeler-defined. In particular, the body text is not restricted. It can describe the grouped elements as well as elements or values related to the grouped elements.

Element groups are named using the name property. The criterion for membership in an element group is specified by the body of the comment the stereotype is applied to. By grouping elements, the modeler asserts that the criterion of the group applies to the member. Optionally, members of an element group can be ordered using its orderedMember property.

ElementGroups appear in diagrams as comments, and properties of the stereotype appear in the notation for stereotype properties. Grouped elements are the annotated elements of the comment to which the stereotype is applied. This has several implications:

    • Element groups do not own their elements and thus an element can participate in an unlimited number of groups.
    • The elements in a group are identified by the modeler, as opposed to being the result of a query, as in views.
    • Element groups can be members of other element groups, but this does not  imply that members of the first are members of the second.

Elements related to the grouped elements are not included in the group, even though the body text can address them. In particular, element groups annotating deeply nested properties or properties with bindings are grouping only the properties, rather than their nesting or their bound properties.

Grouped elements are also limited to elements of models, rather than instances of values of those model elements. In particular, element groups annotating blocks or properties are not grouping the instances of the blocks or the values of the properties. However, since the semantics of ElementGroup is left to the modeler, the body text can refer to related elements outside the group, such as instances and values of the grouped elements, or to bound properties. The modeler is then responsible for writing body text that explains the implications for the related elements. For instance:

    • A group with the criterion: “Authored by John” could annotate any model element added in the model by John. This body text does not address any related elements. For example, if the annotated element is a property bound to another property, the group would not imply authorship of the second property.
    • A group with the criterion: “Instances are manufactured in a foreign country” could annotate Blocks to indicate that any instances of those Blocks are produced in a foreign country. This body text does not address the Block itself, which is not necessarily “manufactured” in a foreign country.
    • A group with criterion: “Values are manufactured in a foreign country” could annotate properties, including part properties, to indicate the values of the property are produced in a foreign country. This body text does not address the property itself, which is not necessarily “manufactured” in a foreign country.  Since the text is about values of the property, it is also about values of other properties that might be bound to the annotated property, because the values of bound properties are the same.
">
</ownedComment>
        <ownedOperation xmi:id="SysML.ElementGroup.allGroups_Element" xmi:type="uml:Operation" isQuery="true" isStatic="true" name="allGroups">
          <ownedComment xmi:id="SysML.ElementGroup.allGroups_Element._comment0" xmi:type="uml:Comment" body="The query allGroups() returns the set of all the groups an element is member of."/>
          <ownedParameter xmi:id="SysML.ElementGroup.allGroups_Element.e" xmi:type="uml:Parameter" effect="create" name="e">
            <lowerValue xmi:id="SysML.ElementGroup.allGroups_Element.e.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Element"/>
            <upperValue xmi:id="SysML.ElementGroup.allGroups_Element.e.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.ElementGroup.allGroups_Element.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.ElementGroup.allGroups_Element.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.ElementGroup"/>
            <upperValue xmi:id="SysML.ElementGroup.allGroups_Element.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.ElementGroup.criterion_" xmi:type="uml:Operation" isQuery="true" name="criterion">
          <ownedComment xmi:id="SysML.ElementGroup.criterion_._comment0" xmi:type="uml:Comment" body="The query criterion() returns the text describing the criterion defining the group."/>
          <ownedParameter xmi:id="SysML.ElementGroup.criterion_.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.ElementGroup.criterion_.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#String"/>
            <upperValue xmi:id="SysML.ElementGroup.criterion_.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.ElementGroup.member_" xmi:type="uml:Operation" isQuery="true" name="member">
          <ownedComment xmi:id="SysML.ElementGroup.member_._comment0" xmi:type="uml:Comment" body="The query member() returns the set of all the members of the group."/>
          <ownedParameter xmi:id="SysML.ElementGroup.member_.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.ElementGroup.member_.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Element"/>
            <upperValue xmi:id="SysML.ElementGroup.member_.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.ElementGroup.size_" xmi:type="uml:Operation" isQuery="true" name="size">
          <ownedComment xmi:id="SysML.ElementGroup.size_._comment0" xmi:type="uml:Comment" body="The query size() returns the number of elements which are members of the group."/>
          <ownedParameter xmi:id="SysML.ElementGroup.size_.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.ElementGroup.size_.result.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Integer"/>
            <upperValue xmi:id="SysML.ElementGroup.size_.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
        </ownedOperation>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Conform_base_Generalization" xmi:type="uml:Extension" name="E_extension_Conform_base_Generalization">
        <memberEnd xmi:idref="SysML.Conform.base_Generalization"/>
        <memberEnd xmi:idref="SysML.E_extension_Conform_base_Generalization.extension_Conform"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Conform_base_Generalization.extension_Conform"/>
        <ownedEnd xmi:id="SysML.E_extension_Conform_base_Generalization.extension_Conform" xmi:type="uml:ExtensionEnd" name="extension_Conform">
          <association xmi:idref="SysML.E_extension_Conform_base_Generalization"/>
          <lowerValue xmi:id="SysML.E_extension_Conform_base_Generalization.extension_Conform.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.Conform"/>
          <upperValue xmi:id="SysML.E_extension_Conform_base_Generalization.extension_Conform.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
    </packagedElement>
    <packagedElement xmi:id="SysML.ConstraintBlocks" xmi:type="uml:Package" URI="" name="ConstraintBlocks">
      <packageImport xmi:id="SysML.ConstraintBlocks._packageImport.SysML.Blocks" xmi:type="uml:PackageImport">
        <importedPackage xmi:idref="SysML.Blocks"/>
      </packageImport>
      <packagedElement xmi:id="SysML.ConstraintBlock" xmi:type="uml:Stereotype" name="ConstraintBlock">
        <generalization xmi:id="SysML.ConstraintBlock._generalization.SysML.Block" xmi:type="uml:Generalization" isSubstitutable="false">
          <general xmi:idref="SysML.Block"/>
        </generalization>
        <ownedComment 
        xmi:id="SysML.ConstraintBlock._comment0" xmi:type="uml:Comment" body="A constraint block is a block that packages the statement of a constraint so it may be applied in a reusable way to constrain properties of other blocks. A constraint block typically defines one or more constraint parameters, which are bound to properties of other blocks in a surrounding context where the constraint is used. Binding connectors, as defined in  clause 8 are used to bind each parameter of the constraint block to a property in the surrounding context. All properties of a constraint block are constraint parameters, with the exception of constraint properties that hold internally nested usages of constraint blocks.

A constraint property is a property of any block that is typed by a constraint block. It holds a localized usage of the constraint block. Binding connectors may be used to bind the parameters of this constraint block to other properties of the block that contains the usage.">
</ownedComment>
        <ownedRule xmi:id="SysML.ConstraintBlock._rule.1_constraintparameters_only" xmi:type="uml:Constraint" name="1_constraintparameters_only">
          <ownedComment 
          xmi:id="SysML.ConstraintBlock._rule.1_constraintparameters_only._comment0" xmi:type="uml:Comment" body="A constraint block shall not own any structural or behavioral elements beyond the properties that define its constraint parameters, constraint properties that hold internal usages of constraint blocks, binding connectors between its internally nested constraint parameters, constraint expressions that define an interpretation for the constraint block, and general-purpose model management and crosscutting elements.">
</ownedComment>
          <specification xmi:id="SysML.ConstraintBlock._rule.1_constraintparameters_only.specification" xmi:type="uml:OpaqueExpression" body="-- Cannot be expressed in OCL" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ConstraintBlock._rule.3_composite" xmi:type="uml:Constraint" name="3_composite">
          <ownedComment xmi:id="SysML.ConstraintBlock._rule.3_composite._comment0" xmi:type="uml:Comment" body="Any property of a block that is typed by a ConstraintBlock shall have composite aggregation."/>
          <specification xmi:id="SysML.ConstraintBlock._rule.3_composite.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.ownedAttribute-&gt;forAll(p| p.isComposite) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
    </packagedElement>
    <packagedElement xmi:id="SysML.PortsAndFlows" xmi:type="uml:Package" URI="" name="PortsAndFlows">
      <packageImport xmi:id="SysML.PortsAndFlows._packageImport.SysML.Blocks" xmi:type="uml:PackageImport">
        <importedPackage xmi:idref="SysML.Blocks"/>
      </packageImport>
      <packagedElement xmi:id="SysML.E_extension_ProxyPort_base_Port" xmi:type="uml:Extension" name="E_extension_ProxyPort_base_Port">
        <memberEnd xmi:idref="SysML.ProxyPort.base_Port"/>
        <memberEnd xmi:idref="SysML.E_extension_ProxyPort_base_Port.extension_ProxPort"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ProxyPort_base_Port.extension_ProxPort"/>
        <ownedEnd xmi:id="SysML.E_extension_ProxyPort_base_Port.extension_ProxPort" xmi:type="uml:ExtensionEnd" name="extension_ProxPort">
          <association xmi:idref="SysML.E_extension_ProxyPort_base_Port"/>
          <lowerValue xmi:id="SysML.E_extension_ProxyPort_base_Port.extension_ProxPort.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ProxyPort"/>
          <upperValue xmi:id="SysML.E_extension_ProxyPort_base_Port.extension_ProxPort.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.FullPort" xmi:type="uml:Stereotype" name="FullPort">
        <ownedAttribute xmi:id="SysML.FullPort.base_Port" xmi:type="uml:Property" name="base_Port">
          <association xmi:idref="SysML.E_extension_FullPort_base_Port"/>
          <lowerValue xmi:id="SysML.FullPort.base_Port.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Port"/>
          <upperValue xmi:id="SysML.FullPort.base_Port.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.FullPort._comment0" xmi:type="uml:Comment" body="Full ports specify a separate element of the system from the owning block or its internal parts. They might have their own internal parts, and behaviors to support interaction with the owning block, its internal parts, or external blocks. They cannot be behavioral ports, or linked to internal parts by binding connectors, because these constructs imply identity with the owning block or internal parts. However, full ports can be linked to non-full ports by binding connectors, because this does not necessarily imply identity with other parts of the system.">
</ownedComment>
        <ownedRule xmi:id="SysML.FullPort._rule.1_not_proxy" xmi:type="uml:Constraint" name="1_not_proxy">
          <ownedComment xmi:id="SysML.FullPort._rule.1_not_proxy._comment0" xmi:type="uml:Comment" body="Full ports shall not also be proxy ports. This applies even if some of the stereotypes are on subsetted or redefined ports."/>
          <specification xmi:id="SysML.FullPort._rule.1_not_proxy.specification" xmi:type="uml:OpaqueExpression" body="ProxyPort.allInstances()-&gt;excludes(self.base_Port)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.FullPort._rule.2_not_bound_to_fullport" xmi:type="uml:Constraint" name="2_not_bound_to_fullport">
          <ownedComment 
          xmi:id="SysML.FullPort._rule.2_not_bound_to_fullport._comment0" xmi:type="uml:Comment" body="Binding connectors shall not link full ports (either directly or indirectly through other binding connectors) to other composite properties of the block owning the full port (or that block’s generalizations or specializations), unless the composite properties are non-full ports.">
</ownedComment>
          <specification 
          xmi:id="SysML.FullPort._rule.2_not_bound_to_fullport.specification" xmi:type="uml:OpaqueExpression" body="let fullPorts: Set(UML::Port) = FullPort.allInstances().base_Port-&gt;asSet() in BindingConnector.allInstances().base_Connector-&gt;select(c | c.end.role-&gt;includes(self.base_Port))-&gt;forAll(c | fullPorts-&gt;excludesAll(c.end.role-&gt;reject(r | r=self.base_Port)))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.FullPort._rule.3_not_behavioral" xmi:type="uml:Constraint" name="3_not_behavioral">
          <ownedComment xmi:id="SysML.FullPort._rule.3_not_behavioral._comment0" xmi:type="uml:Comment" body="Full ports shall not be behavioral (isBehavior=false)."/>
          <specification xmi:id="SysML.FullPort._rule.3_not_behavioral.specification" xmi:type="uml:OpaqueExpression" body="not self.base_Port.isBehavior" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML." xmi:type="uml:Extension" name="">
        <memberEnd xmi:idref="SysML.AddFlowPropertyValueOnNestedPortAction.base_AddStructuralFeatureValueAction"/>
        <memberEnd xmi:idref="SysML..extension_AddFlowPropertyValueOnNestedPortAction"/>
        <navigableOwnedEnd xmi:idref="SysML..extension_AddFlowPropertyValueOnNestedPortAction"/>
        <ownedEnd xmi:id="SysML..extension_AddFlowPropertyValueOnNestedPortAction" xmi:type="uml:ExtensionEnd" aggregation="composite" name="extension_AddFlowPropertyValueOnNestedPortAction">
          <association xmi:idref="SysML."/>
          <lowerValue xmi:id="SysML..extension_AddFlowPropertyValueOnNestedPortAction.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.AddFlowPropertyValueOnNestedPortAction"/>
          <upperValue xmi:id="SysML..extension_AddFlowPropertyValueOnNestedPortAction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.DirectedFeature" xmi:type="uml:Stereotype" name="DirectedFeature">
        <ownedAttribute xmi:id="SysML.DirectedFeature.base_Feature" xmi:type="uml:Property" name="base_Feature">
          <association xmi:idref="SysML.E_extension_DirectedFeature_base_Feature"/>
          <lowerValue xmi:id="SysML.DirectedFeature.base_Feature.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Feature"/>
          <upperValue xmi:id="SysML.DirectedFeature.base_Feature.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.DirectedFeature.featureDirection" xmi:type="uml:Property" name="featureDirection">
          <lowerValue xmi:id="SysML.DirectedFeature.featureDirection.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment 
          xmi:id="SysML.DirectedFeature.featureDirection._comment0" xmi:type="uml:Comment" body="Specifies whether the feature is supported by the owning block (featureDirection=“provided”), or is to be supported by other blocks for the owning block to use (featureDirection=“required”), or both (featureDirection=“providedrequired”).">
</ownedComment>
          <type xmi:idref="SysML_dataType.FeatureDirectionKind"/>
          <upperValue xmi:id="SysML.DirectedFeature.featureDirection.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.DirectedFeature._comment0" xmi:type="uml:Comment" body="A DirectedFeature indicates whether the feature is supported by the owning block (provided), or is to be supported by other blocks for the owning block to use (required), or both (the owning block for features on types of proxy ports is the type of the block usage the proxy port is standing in for, which might be an internal part). Using non-flow properties means to read or write them, and using behavioral features means to invoke them. Provided non-flow properties are read and written on the owning block, while required non-flow properties are read or written on an external block. Provided behavioral features are invoked with the owning block as target, while required behavioral features are invoked with an external block as target (required).

Blocks owning or inheriting required behavioral features can have behaviors invoking the behavioral features on instances of the block. This sends invocations out along connectors from usages of the block in internal structures of other blocks, provided the behavioral features match on the other end of the connectors.

Invocations of provided behavioral features due to required behavioral features can only occur when the features match. A single provided behavioral feature shall match each required one according to the following conditions:

• The kind of behavioral feature is the same (operation or reception).
• Names are the same, including parameter names, in the same order.
• Parameter directions are the same, in the same order.
• Provided parameter types for parameters with:
	• in direction are the same or more general than the required ones, in order.
	• out or return direction are the same or more specialized than the required ones, in order.
	• inout direction are the same as the required ones, in order.

Parameters without types are treated as if their type is more general than all other types.

• Provided parameter multiplicity has the same condition as type, where wider multiplicities are “more general” than narrower ones.

• Provided parameter order (of each parameter separately) has the same condition as type, where unordered parameters are “more general” than ordered ones.

• Provided parameter uniqueness (of each parameter separately) has the same condition as type, where non-unique parameters are “more general” than unique ones.

• Provided operation preconditions are the same as or more general than required ones.

• Provided operation body conditions and postconditions are the same or more specialized than required ones.

If corresponding parameters in provided and required behavioral features both have defaults, the default value specification of the required feature is used for in parameters, and the default value specification of the provided feature is used for out and return parameters.

Reading or writing provided non-flow properties due to required non-flow properties can only occur when the features match. Matching non-flow properties shall have the same name. For reading non-flow properties, the types, multiplicities, uniqueness, and ordering shall match in the same way as out parameters for behavioral features above. For writing non- flow properties, the types, multiplicities, uniqueness, and ordering shall match in the same way as in parameters for behavioral features above. For both reading and writing non-flow properties, the types, multiplicities, uniqueness, and ordering shall be the same. If provided and required non-flow properties both have defaults, the default value specification of the required feature is used for writing and the default specification of the provided feature is used for reading.">
</ownedComment>
        <ownedRule xmi:id="SysML.DirectedFeature._rule.2_method_if_provided" xmi:type="uml:Constraint" name="2_method_if_provided">
          <ownedComment xmi:id="SysML.DirectedFeature._rule.2_method_if_provided._comment0" xmi:type="uml:Comment" body="A non-provided operation shall not be associated with a behavior as its method."/>
          <specification 
          xmi:id="SysML.DirectedFeature._rule.2_method_if_provided.specification" xmi:type="uml:OpaqueExpression" body="self.base_Feature.oclIsKindOf(UML::Operation) and self.featureDirection=FeatureDirectionKind::required implies self.base_Feature.oclAsType(UML::Operation).method-&gt;isEmpty()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.DirectedFeature._rule.1_behavioralfeature_or_not_flowproperty" xmi:type="uml:Constraint" name="1_behavioralfeature_or_not_flowproperty">
          <ownedComment xmi:id="SysML.DirectedFeature._rule.1_behavioralfeature_or_not_flowproperty._comment0" xmi:type="uml:Comment" body="DirectedFeature shall only be applied to behavioral features, or to properties that do not have FlowProperty applied, including on subsetted or redefined features."/>
          <specification 
          xmi:id="SysML.DirectedFeature._rule.1_behavioralfeature_or_not_flowproperty.specification" xmi:type="uml:OpaqueExpression" body="self.base_Feature.oclIsKindOf(UML::BehavioralFeature) or  (self.base_Feature.oclIsKindOf(UML::Property) and   let property: UML::Property = self.base_Feature.oclAsType(UML::Property) in  FlowProperty.allInstances().base_Property-&gt;excludesAll(property.redefinedProperty-&gt;union(property.subsettedProperty)-&gt;including(property)))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_TriggerOnNestedPort_base_Trigger" xmi:type="uml:Extension" name="E_extension_TriggerOnNestedPort_base_Trigger">
        <generalization xmi:id="SysML.E_extension_TriggerOnNestedPort_base_Trigger._generalization.SysML.E_extension_ElementPropertyPath_base_Element" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element"/>
        </generalization>
        <memberEnd xmi:idref="SysML.TriggerOnNestedPort.base_Trigger"/>
        <memberEnd xmi:idref="SysML.E_extension_TriggerOnNestedPort_base_Trigger.extension_TriggerOnNestedPort"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_TriggerOnNestedPort_base_Trigger.extension_TriggerOnNestedPort"/>
        <ownedEnd xmi:id="SysML.E_extension_TriggerOnNestedPort_base_Trigger.extension_TriggerOnNestedPort" xmi:type="uml:ExtensionEnd" name="extension_TriggerOnNestedPort">
          <association xmi:idref="SysML.E_extension_TriggerOnNestedPort_base_Trigger"/>
          <lowerValue xmi:id="SysML.E_extension_TriggerOnNestedPort_base_Trigger.extension_TriggerOnNestedPort.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <redefinedProperty xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element.extension_ElementPropertyPath"/>
          <type xmi:idref="SysML.TriggerOnNestedPort"/>
          <upperValue xmi:id="SysML.E_extension_TriggerOnNestedPort_base_Trigger.extension_TriggerOnNestedPort.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.InvocationOnNestedPortAction" xmi:type="uml:Stereotype" name="InvocationOnNestedPortAction">
        <generalization xmi:id="SysML.InvocationOnNestedPortAction._generalization.SysML.ElementPropertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.ElementPropertyPath"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.InvocationOnNestedPortAction.base_InvocationAction" xmi:type="uml:Property" name="base_InvocationAction">
          <association xmi:idref="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction"/>
          <lowerValue xmi:id="SysML.InvocationOnNestedPortAction.base_InvocationAction.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <redefinedProperty xmi:idref="SysML.ElementPropertyPath.base_Element"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#InvocationAction"/>
          <upperValue xmi:id="SysML.InvocationOnNestedPortAction.base_InvocationAction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.InvocationOnNestedPortAction.onNestedPort" xmi:type="uml:Property" isOrdered="true" isUnique="false" name="onNestedPort">
          <association xmi:idref="SysML.A_invocationOnNestedPortAction_onNestedPort"/>
          <lowerValue xmi:id="SysML.InvocationOnNestedPortAction.onNestedPort.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment 
          xmi:id="SysML.InvocationOnNestedPortAction.onNestedPort._comment0" xmi:type="uml:Comment" body="Gives a series of ports that identifies the port receiving the invocation in the context of the target object of the invocation. The ordering of ports is from a port of the target object, through a port of each intermediate block that types the preceding port, ending in a port with a type that owns or inherits the port given by the onPort property of the invocation action. The onPort port is not included in the onNestedPort list. The same port might appear more than once because a block can own a port with the same block as a type, or another block that has the same property.">
</ownedComment>
          <redefinedProperty xmi:idref="SysML.ElementPropertyPath.propertyPath"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Port"/>
          <upperValue xmi:id="SysML.InvocationOnNestedPortAction.onNestedPort.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.InvocationOnNestedPortAction._comment0" xmi:type="uml:Comment" body="This extends the capabilities of UML’s onPort property of InvocationAction to support nested ports. It identifies a nested port by a multi-level path of ports from the block that executes the action. Like UML’s onPort property, this extends invocation actions to send invocations out of ports of objects executing the actions, or to ports of those objects or other objects. Invocations intended to go out of the object executing the action shall be sent to the executing object on a proxy port. Invocations intended to go directly to a target object are sent to that object on a port of that object.">
</ownedComment>
        <ownedRule xmi:id="SysML.InvocationOnNestedPortAction._rule.2_onnestedport_first_owned_by_target_type" xmi:type="uml:Constraint" name="2_onnestedport_first_owned_by_target_type">
          <ownedComment 
          xmi:id="SysML.InvocationOnNestedPortAction._rule.2_onnestedport_first_owned_by_target_type._comment0" xmi:type="uml:Comment" body="The port at the first position in the onNestedPort list shall be owned (directly or via inheritance) by a block that types the target pin of the invocation action, or one of the block’s generalizations.">
</ownedComment>
          <specification 
          xmi:id="SysML.InvocationOnNestedPortAction._rule.2_onnestedport_first_owned_by_target_type.specification" xmi:type="uml:OpaqueExpression" body="let target: UML::InputPin = if self.base_InvocationAction.oclIsKindOf(UML::CallOperationAction) then
   self.base_InvocationAction.oclAsType(UML::CallOperationAction).target
else if self.base_InvocationAction.oclIsKindOf(UML::SendSignalAction) then
   self.base_InvocationAction.oclAsType(UML::SendSignalAction).target
else if self.base_InvocationAction.oclIsKindOf(UML::SendObjectAction) then
   self.base_InvocationAction.oclAsType(UML::SendObjectAction).target
else
   invalid
endif endif endif in
not target.oclIsUndefined() and (
  let target_type: UML::Class = Block.allInstances()-&gt;any(b | b.base_Class = target.type).base_Class in
  not target_type.oclIsUndefined() and target_type.allFeatures()-&gt;includes(self.onNestedPort-&gt;first()))  " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.InvocationOnNestedPortAction._rule.1_onPort_defined" xmi:type="uml:Constraint" name="1_onPort_defined">
          <ownedComment xmi:id="SysML.InvocationOnNestedPortAction._rule.1_onPort_defined._comment0" xmi:type="uml:Comment" body="The onPort property of an invocation action shall have a value when this stereotype is applied."/>
          <specification xmi:id="SysML.InvocationOnNestedPortAction._rule.1_onPort_defined.specification" xmi:type="uml:OpaqueExpression" body="self.base_InvocationAction.onPort-&gt;notEmpty()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.InvocationOnNestedPortAction._rule.4_onnestedport_last_type_owns_invocation_onPort" xmi:type="uml:Constraint" name="4_onnestedport_last_type_owns_invocation_onPort">
          <ownedComment xmi:id="SysML.InvocationOnNestedPortAction._rule.4_onnestedport_last_type_owns_invocation_onPort._comment0" xmi:type="uml:Comment" body="The type of the port at the last position of the onNestedPort list shall own or inherit the onPort port of the stereotyped invocation action."/>
          <specification 
          xmi:id="SysML.InvocationOnNestedPortAction._rule.4_onnestedport_last_type_owns_invocation_onPort.specification" xmi:type="uml:OpaqueExpression" body="self.onNestedPort-&gt;last().type.oclAsType(UML::Classifier).allFeatures()-&gt;includes(self.base_InvocationAction.onPort)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.InvocationOnNestedPortAction._rule.3_path_consistency" xmi:type="uml:Constraint" name="3_path_consistency">
          <ownedComment 
          xmi:id="SysML.InvocationOnNestedPortAction._rule.3_path_consistency._comment0" xmi:type="uml:Comment" body="The port at each successive position of the onNestedPort attribute, following the first position, shall be owned by the Block that types the port at the immediately preceding position, or a generalization of that Block.">
</ownedComment>
          <specification 
          xmi:id="SysML.InvocationOnNestedPortAction._rule.3_path_consistency.specification" xmi:type="uml:OpaqueExpression" body="self.onNestedPort-&gt;size() &gt;1 implies self.propertyPath-&gt;subSequence(2, self.onNestedPort-&gt;size())-&gt;forAll(p |
  let pp: UML::Property = self.onNestedPort-&gt;at(self.onNestedPort-&gt;indexOf(p)-1) in
  let owners: Set(UML::Classifier) = pp.type.oclAsType(UML::Classifier)-&gt;including(pp.type.oclAsType(UML::Classifier)) in
  owners-&gt;includes(p.owner))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction" xmi:type="uml:Extension" name="E_extension_InvocationOnNestedPortAction_base_InvocationAction">
        <generalization xmi:id="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction._generalization.SysML.E_extension_ElementPropertyPath_base_Element" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element"/>
        </generalization>
        <memberEnd xmi:idref="SysML.InvocationOnNestedPortAction.base_InvocationAction"/>
        <memberEnd xmi:idref="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction.extension_InvocationOnNestedPortAction"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction.extension_InvocationOnNestedPortAction"/>
        <ownedEnd xmi:id="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction.extension_InvocationOnNestedPortAction" xmi:type="uml:ExtensionEnd" name="extension_InvocationOnNestedPortAction">
          <association xmi:idref="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction"/>
          <lowerValue xmi:id="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction.extension_InvocationOnNestedPortAction.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <redefinedProperty xmi:idref="SysML.E_extension_ElementPropertyPath_base_Element.extension_ElementPropertyPath"/>
          <type xmi:idref="SysML.InvocationOnNestedPortAction"/>
          <upperValue xmi:id="SysML.E_extension_InvocationOnNestedPortAction_base_InvocationAction.extension_InvocationOnNestedPortAction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_DirectedFeature_base_Feature" xmi:type="uml:Extension" name="E_extension_DirectedFeature_base_Feature">
        <memberEnd xmi:idref="SysML.DirectedFeature.base_Feature"/>
        <memberEnd xmi:idref="SysML.E_extension_DirectedFeature_base_Feature.extension_DirectedFeature"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_DirectedFeature_base_Feature.extension_DirectedFeature"/>
        <ownedEnd xmi:id="SysML.E_extension_DirectedFeature_base_Feature.extension_DirectedFeature" xmi:type="uml:ExtensionEnd" name="extension_DirectedFeature">
          <association xmi:idref="SysML.E_extension_DirectedFeature_base_Feature"/>
          <lowerValue xmi:id="SysML.E_extension_DirectedFeature_base_Feature.extension_DirectedFeature.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.DirectedFeature"/>
          <upperValue xmi:id="SysML.E_extension_DirectedFeature_base_Feature.extension_DirectedFeature.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML_dataType.FlowDirectionKind" xmi:type="uml:Enumeration" name="FlowDirectionKind">
        <ownedComment 
        xmi:id="SysML_dataType.FlowDirectionKind._comment0" xmi:type="uml:Comment" body="FlowDirectionKind is an enumeration type that defines literals used for specifying the direction that items can flow to or from a block. FlowDirectionKind is used by flow properties to indicate the direction that its items can flow to or from its owner. (See clause 9.3.2.13  for definition of owning block of proxy ports in this case.)">
</ownedComment>
        <ownedLiteral xmi:id="SysML_dataType.FlowDirectionKind.in" xmi:type="uml:EnumerationLiteral" name="in">
          <ownedComment xmi:id="SysML_dataType.FlowDirectionKind.in._comment0" xmi:type="uml:Comment" body="Indicates that items of the flow property can flow into the owning block."/>
        </ownedLiteral>
        <ownedLiteral xmi:id="SysML_dataType.FlowDirectionKind.inout" xmi:type="uml:EnumerationLiteral" name="inout">
          <ownedComment xmi:id="SysML_dataType.FlowDirectionKind.inout._comment0" xmi:type="uml:Comment" body="Indicates that items of the flow property can flow into or out of the owning block."/>
        </ownedLiteral>
        <ownedLiteral xmi:id="SysML_dataType.FlowDirectionKind.out" xmi:type="uml:EnumerationLiteral" name="out">
          <ownedComment xmi:id="SysML_dataType.FlowDirectionKind.out._comment0" xmi:type="uml:Comment" body="Indicates that items of the flow property can flow out of the owning block."/>
        </ownedLiteral>
      </packagedElement>
      <packagedElement xmi:id="SysML.ChangeStructuralFeatureEvent" xmi:type="uml:Stereotype" name="ChangeStructuralFeatureEvent">
        <ownedAttribute xmi:id="SysML.ChangeStructuralFeatureEvent.base_ChangeEvent" xmi:type="uml:Property" name="base_ChangeEvent">
          <association xmi:idref="SysML.E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent"/>
          <lowerValue xmi:id="SysML.ChangeStructuralFeatureEvent.base_ChangeEvent.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ChangeEvent"/>
          <upperValue xmi:id="SysML.ChangeStructuralFeatureEvent.base_ChangeEvent.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ChangeStructuralFeatureEvent.structuralFeature" xmi:type="uml:Property" name="structuralFeature">
          <association xmi:idref="SysML.A_changeStructuralFeatureEvent_structuralFeature"/>
          <lowerValue xmi:id="SysML.ChangeStructuralFeatureEvent.structuralFeature.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.ChangeStructuralFeatureEvent.structuralFeature._comment0" xmi:type="uml:Comment" body="he event models occurrences of changes to values of this structural feature."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#StructuralFeature"/>
          <upperValue xmi:id="SysML.ChangeStructuralFeatureEvent.structuralFeature.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment xmi:id="SysML.ChangeStructuralFeatureEvent._comment0" xmi:type="uml:Comment" body="A ChangeStructuralFeatureEvent models changes in values of structural features."/>
        <ownedRule xmi:id="SysML.ChangeStructuralFeatureEvent._rule.2_one_featuringclassifier" xmi:type="uml:Constraint" name="2_one_featuringclassifier">
          <ownedComment xmi:id="SysML.ChangeStructuralFeatureEvent._rule.2_one_featuringclassifier._comment0" xmi:type="uml:Comment" body="The structural feature shall have exactly one featuringClassifier"/>
          <specification xmi:id="SysML.ChangeStructuralFeatureEvent._rule.2_one_featuringclassifier.specification" xmi:type="uml:OpaqueExpression" body="self.structuralFeature.featuringClassifier-&gt;size()=1" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ChangeStructuralFeatureEvent._rule.1_not_static" xmi:type="uml:Constraint" name="1_not_static">
          <ownedComment xmi:id="SysML.ChangeStructuralFeatureEvent._rule.1_not_static._comment0" xmi:type="uml:Comment" body="The structural feature shall not be static"/>
          <specification xmi:id="SysML.ChangeStructuralFeatureEvent._rule.1_not_static.specification" xmi:type="uml:OpaqueExpression" body="not self.structuralFeature.isStatic" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.FlowProperty" xmi:type="uml:Stereotype" name="FlowProperty">
        <ownedAttribute xmi:id="SysML.FlowProperty.base_Property" xmi:type="uml:Property" name="base_Property">
          <association xmi:idref="SysML.E_extension_FlowProperty_base_Property"/>
          <lowerValue xmi:id="SysML.FlowProperty.base_Property.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.FlowProperty.base_Property.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.FlowProperty.direction" xmi:type="uml:Property" name="direction">
          <defaultValue xmi:id="SysML.FlowProperty.direction.defaultValue0" xmi:type="uml:InstanceValue" name="">
            <instance xmi:idref="SysML_dataType.FlowDirectionKind.inout"/>
          </defaultValue>
          <lowerValue xmi:id="SysML.FlowProperty.direction.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.FlowProperty.direction._comment0" xmi:type="uml:Comment" body="Specifies if the property value is received from an external block (direction=“in”), transmitted to an external Block (direction=“out”) or both (direction=“inout”)."/>
          <type xmi:idref="SysML_dataType.FlowDirectionKind"/>
          <upperValue xmi:id="SysML.FlowProperty.direction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.FlowProperty._comment0" xmi:type="uml:Comment" body="A FlowProperty signifies a single kind of flow element that can flow to/from its owning instance that is specified by the block defining that flow property. A flow property’s values are either received from or transmitted to another instance. An &quot;in&quot; flow property value cannot be modified by the owning instance of that flow property, or by parts of that instance. An &quot;out&quot; flow property can only be modified by the owning instance of that flow property, or by parts of that instance. An &quot;inout&quot; flow property can be used as an &quot;in&quot; flow property or an &quot;out&quot; flow property, and there is no restriction regarding the way it can be modified. (The owning block of a proxy port in this case depends on how the port is nested in the internal structures of blocks, because the block directly owning the port might be used to type ports or parts at different levels of nesting in multiple blocks, or the same block. The owning block of a proxy port in the internal structure of a block is the block typing the innermost full port or part under which the port is nested.)

Flow due to flow properties can only occur when flow properties match. Matching flow properties shall have matching direction and types. Matching direction is defined below. Flow property types match when the target flow property type has the same, or a generalization of, the source flow property type. (See ********, ItemFlow for looser constraints on flow property types across connectors with item flows.) If multiple flow properties on either end of a connector match by direction and type, then the names of the flow properties shall also be the same for flow to occur. If multiple flow properties on either end match by direction, type, and name, which can happen for unnamed flow properties, then no flow will occur.

Flow properties enable item flows across connectors between usages typed by blocks having the properties. For Block and ValueType flow properties, setting an “out” or “inout” FlowProperty value of a block usage on one end of a connector will result in assigning the same value of an “in” or “inout” FlowProperty of a block usage at the other end of the connector, provided the flow properties are matched. It is not specified whether send/receive signal events are generated when values are written to out/in flow properties typed by Signal (implementations might choose to do this, but it is not required). This paragraph does not apply to internal connectors of proxy ports, see next paragraph.

Items going to or from behavioral ports (UML isBehavior = true) are actually going to or from the owning block. (See clause9.3.2.8 for definition of owning block of proxy ports in this case.) Items going to or from non-behavioral ports (UML isBehavior = false) are actually going to the port itself (for full ports) or to internal parts connected to the port (for proxy ports). Because of this, flow properties of a proxy port are the same as flow properties on the owning block or internal parts, so the flow property directions shall be the same on the proxy port and owning block or internal parts for items to flow. See Section ******** for the definition of internal connectors and the semantics of proxy ports.

The flow property semantics above applies to each connector of a block usage, including when the block usage has multiple connectors.

The binding of flow properties on ports to behavior parameters can be achieved in ways not dictated by SysML. One approach is to perform name and type matching. Another approach is to explicitly use binding relationships between the ports properties and behavior parameters or block properties.">
</ownedComment>
        <ownedRule xmi:id="SysML.FlowProperty._rule.1_restricted_types" xmi:type="uml:Constraint" name="1_restricted_types">
          <ownedComment xmi:id="SysML.FlowProperty._rule.1_restricted_types._comment0" xmi:type="uml:Comment" body="A FlowProperty shall be typed by a ValueType, Block, or Signal."/>
          <specification 
          xmi:id="SysML.FlowProperty._rule.1_restricted_types.specification" xmi:type="uml:OpaqueExpression" body="Block.allInstances().base_Class-&gt;includes(self.base_Property.type) or ValueType.allInstances().base_DataType-&gt;includes(self.base_Property.type) or self.base_Property.oclIsKindOf(UML::Signal)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML_dataType.FeatureDirectionKind" xmi:type="uml:Enumeration" name="FeatureDirectionKind">
        <ownedComment 
        xmi:id="SysML_dataType.FeatureDirectionKind._comment0" xmi:type="uml:Comment" body="FeatureDirectionKind is an enumeration type that defines literals used by directed features for specifying whether they are supported by the owning block, or is to be supported by other blocks for the owning block to use.">
</ownedComment>
        <ownedLiteral xmi:id="SysML_dataType.FeatureDirectionKind.provided" xmi:type="uml:EnumerationLiteral" name="provided">
          <ownedComment xmi:id="SysML_dataType.FeatureDirectionKind.provided._comment0" xmi:type="uml:Comment" body="Indicates that the feature shall be supported by the owning block."/>
        </ownedLiteral>
        <ownedLiteral xmi:id="SysML_dataType.FeatureDirectionKind.providedRequired" xmi:type="uml:EnumerationLiteral" name="providedRequired">
          <ownedComment xmi:id="SysML_dataType.FeatureDirectionKind.providedRequired._comment0" xmi:type="uml:Comment" body="Indicates that the feature shall be both provided and required."/>
        </ownedLiteral>
        <ownedLiteral xmi:id="SysML_dataType.FeatureDirectionKind.required" xmi:type="uml:EnumerationLiteral" name="required">
          <ownedComment xmi:id="SysML_dataType.FeatureDirectionKind.required._comment0" xmi:type="uml:Comment" body="Indicates that the feature shall be supported by other blocks."/>
        </ownedLiteral>
        <ownedRule xmi:id="SysML_dataType.FeatureDirectionKind._rule.2_specializations_are_constraintblocks" xmi:type="uml:Constraint" name="2_specializations_are_constraintblocks">
          <ownedComment xmi:id="SysML_dataType.FeatureDirectionKind._rule.2_specializations_are_constraintblocks._comment0" xmi:type="uml:Comment" body="Any classifier that specializes a ConstraintBlock shall also have the ConstraintBlock stereotype applied."/>
          <specification 
          xmi:id="SysML_dataType.FeatureDirectionKind._rule.2_specializations_are_constraintblocks.specification" xmi:type="uml:OpaqueExpression" body="UML::Classifier.allInstances()-&gt;forAll(c | c.general-&gt;includes(self.base_Class) implies ConstraintBlock.allInstances().base_Class-&gt;includes(c))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_FlowProperty_base_Property" xmi:type="uml:Extension" name="E_extension_FlowProperty_base_Property">
        <memberEnd xmi:idref="SysML.E_extension_FlowProperty_base_Property.extension_FlowProperty"/>
        <memberEnd xmi:idref="SysML.FlowProperty.base_Property"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_FlowProperty_base_Property.extension_FlowProperty"/>
        <ownedEnd xmi:id="SysML.E_extension_FlowProperty_base_Property.extension_FlowProperty" xmi:type="uml:ExtensionEnd" name="extension_FlowProperty">
          <association xmi:idref="SysML.E_extension_FlowProperty_base_Property"/>
          <lowerValue xmi:id="SysML.E_extension_FlowProperty_base_Property.extension_FlowProperty.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.FlowProperty"/>
          <upperValue xmi:id="SysML.E_extension_FlowProperty_base_Property.extension_FlowProperty.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.ItemFlow" xmi:type="uml:Stereotype" name="ItemFlow">
        <ownedAttribute xmi:id="SysML.ItemFlow.base_InformationFlow" xmi:type="uml:Property" name="base_InformationFlow">
          <association xmi:idref="SysML.E_extension_ItemFlow_base_InformationFlow"/>
          <lowerValue xmi:id="SysML.ItemFlow.base_InformationFlow.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#InformationFlow"/>
          <upperValue xmi:id="SysML.ItemFlow.base_InformationFlow.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.ItemFlow.itemProperty" xmi:type="uml:Property" name="itemProperty">
          <lowerValue xmi:id="SysML.ItemFlow.itemProperty.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <ownedComment 
          xmi:id="SysML.ItemFlow.itemProperty._comment0" xmi:type="uml:Comment" body="An optional property that relates the flowing item to the instances of the connector’s enclosing block. This property is applicable only for item flows realized by connectors. The itemProperty attribute has no values if the item flow is realized by an Association.">
</ownedComment>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
          <upperValue xmi:id="SysML.ItemFlow.itemProperty.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ItemFlow._comment0" xmi:type="uml:Comment" body="An ItemFlow describes the flow of items across a connector or an association. It may constrain the item exchange between blocks, block usages, or ports as specified by their flow properties. For example, a pump connected to a tank: the pump has an “out” flow property of type Liquid and the tank has an “in” FlowProperty of type Liquid. To signify that only water flows between the pump and the tank, we can specify an ItemFlow of type Water on the connector.

One can label an ItemFlow with the classifiers of the items that may be conveyed. For example: a label Water would imply that instances of Water might be transmitted over this ItemFlow. In addition, if the item flow identifies an item property, then one can label the item flow with the item property. For example, a label of “liquid: Water” means Water items might flow and these items are the values of the property “liquid,” i.e., the values of the “liquid” item property are the instances of Water flowing at any given time. Item properties are owned by the common (possibly indirect) owner of the source and target of the item flow, rather than by the source and target types, as flow properties are.

Item flows on connectors shall be compatible with flow properties of the blocks usages at each end of the connector, if any. The direction of the item flow shall be compatible wit the direction of flow specified by the flow properties. (See clause 9.3.2.12 and clause 9.3.2.13 about flow property direction.) Each classifier of conveyed items on an item flow shall be the same as, a specialization of, or a generalization of at least one flow property type on each end of the connected block usages (or their accessible nested block usages recursively, see clause 9.3.2.8 about encapsulated blocks). The target flow property type shall be the same as, or a generalization of, a classifier of the item flow or the source flow property type, whichever is more specialized. (See clause 9.3.2.13, for tighter constraints on flow property types across connectors without item flows.)">
</ownedComment>
        <ownedRule xmi:id="SysML.ItemFlow._rule.5_same_type" xmi:type="uml:Constraint" name="5_same_type">
          <ownedComment xmi:id="SysML.ItemFlow._rule.5_same_type._comment0" xmi:type="uml:Comment" body="If an ItemFlow has an itemProperty, one of the classifiers of conveyed items shall be the same as the type of the item property."/>
          <specification xmi:id="SysML.ItemFlow._rule.5_same_type.specification" xmi:type="uml:OpaqueExpression" body="self.itemProperty-&gt;notEmpty() implies self.base_InformationFlow.conveyed-&gt;includes(self.itemProperty.type)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ItemFlow._rule.3_itemproperty_common_owner" xmi:type="uml:Constraint" name="3_itemproperty_common_owner">
          <ownedComment xmi:id="SysML.ItemFlow._rule.3_itemproperty_common_owner._comment0" xmi:type="uml:Comment" body="If itemProperty has a value it shall be a property of the common (possibly indirect) owner of the source and the target."/>
          <specification 
          xmi:id="SysML.ItemFlow._rule.3_itemproperty_common_owner.specification" xmi:type="uml:OpaqueExpression" body="self.itemProperty-&gt;notEmpty() implies  (let target: UML::Element = self.base_InformationFlow.informationTarget-&gt;any(true) in let source: UML::Element = self.base_InformationFlow.informationSource-&gt;any(true) in target.oclIsKindOf(UML::Property) and  source.oclIsKindOf(UML::Property) and let owners: Set(UML::Classifier) = target-&gt;closure(owner)-&gt;select(o1 | o1.oclIsKindOf(UML::Classifier))-&gt;asSet() -&gt;intersection(source-&gt;closure(owner)-&gt;select(o2 | o2.oclIsKindOf(UML::Classifier))).oclAsType(UML::Classifier)-&gt;asSet() in owners.attribute-&gt;flatten()-&gt;includes(self.itemProperty))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ItemFlow._rule.4_association_xor_itemproperty" xmi:type="uml:Constraint" name="4_association_xor_itemproperty">
          <ownedComment xmi:id="SysML.ItemFlow._rule.4_association_xor_itemproperty._comment0" xmi:type="uml:Comment" body="itemProperty shall not have a value if the item flow is realized by an Association."/>
          <specification xmi:id="SysML.ItemFlow._rule.4_association_xor_itemproperty.specification" xmi:type="uml:OpaqueExpression" body="self.base_InformationFlow.realization-&gt;exists(r | r.oclIsKindOf(UML::Association)) implies self.itemProperty-&gt;isEmpty()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ItemFlow._rule.1_source_and_target_linked" xmi:type="uml:Constraint" name="1_source_and_target_linked">
          <ownedComment xmi:id="SysML.ItemFlow._rule.1_source_and_target_linked._comment0" xmi:type="uml:Comment" body="A Connector or an Association, or an inherited Association shall exist between the source and the target of the InformationFlow."/>
          <specification 
          xmi:id="SysML.ItemFlow._rule.1_source_and_target_linked.specification" xmi:type="uml:OpaqueExpression" body="let target: UML::NamedElement = self.base_InformationFlow.informationTarget-&gt;any(true) in let targets: Set(UML::NamedElement) = if target.oclIsKindOf(UML::Classifier) then  target.oclAsType(UML::Classifier)-&gt;closure(general)-&gt;including(target) else  target-&gt;asSet() endif in let source: UML::NamedElement = self.base_InformationFlow.informationSource-&gt;any(true) in let sources: Set(UML::NamedElement) = if source.oclIsKindOf(UML::Classifier) then  source.oclAsType(UML::Classifier)-&gt;closure(general)-&gt;including(source) else  source-&gt;asSet() endif in UML::Association.allInstances()-&gt;exists(a | a.memberEnd-&gt;intersection(targets)-&gt;notEmpty() and  a.memberEnd-&gt;intersection(sources)-&gt;notEmpty()) or UML::Connector.allInstances()-&gt;exists(c | c.end-&gt;intersection(targets)-&gt;notEmpty() and  c.end-&gt;intersection(sources)-&gt;notEmpty())  " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ItemFlow._rule.6_same_name" xmi:type="uml:Constraint" name="6_same_name">
          <ownedComment xmi:id="SysML.ItemFlow._rule.6_same_name._comment0" xmi:type="uml:Comment" body="If an ItemFlow has an itemProperty, its name shall be the same as the name of the item flow."/>
          <specification xmi:id="SysML.ItemFlow._rule.6_same_name.specification" xmi:type="uml:OpaqueExpression" body="self.itemProperty-&gt;notEmpty() implies self.itemProperty.name = self.base_InformationFlow.name" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ItemFlow._rule.2_type_restricted" xmi:type="uml:Constraint" name="2_type_restricted">
          <ownedComment xmi:id="SysML.ItemFlow._rule.2_type_restricted._comment0" xmi:type="uml:Comment" body="An ItemFlow itemProperty shall be typed by a ValueType, Block, or Signal."/>
          <specification 
          xmi:id="SysML.ItemFlow._rule.2_type_restricted.specification" xmi:type="uml:OpaqueExpression" body="ValueType.allInstances().base_DataType-&gt;includes(self.itemProperty.type) or Block.allInstances().base_Class-&gt;includes(self.itemProperty.type) or UML::Signal.allInstances()-&gt;includes(self.itemProperty.type)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent" xmi:type="uml:Extension" name="E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent">
        <memberEnd xmi:idref="SysML.ChangeStructuralFeatureEvent.base_ChangeEvent"/>
        <memberEnd xmi:idref="SysML.E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent.extension_ChangeStructuralFeatureEvent"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent.extension_ChangeStructuralFeatureEvent"/>
        <ownedEnd xmi:id="SysML.E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent.extension_ChangeStructuralFeatureEvent" xmi:type="uml:ExtensionEnd" name="extension_ChangeStructuralFeatureEvent">
          <association xmi:idref="SysML.E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent"/>
          <lowerValue xmi:id="SysML.E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent.extension_ChangeStructuralFeatureEvent.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ChangeStructuralFeatureEvent"/>
          <upperValue xmi:id="SysML.E_extension_ChangeStructuralFeatureEvent_base_ChangeEvent.extension_ChangeStructuralFeatureEvent.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.InterfaceBlock" xmi:type="uml:Stereotype" name="InterfaceBlock">
        <generalization xmi:id="SysML.InterfaceBlock._generalization.SysML.Block" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.Block"/>
        </generalization>
        <ownedComment xmi:id="SysML.InterfaceBlock._comment0" xmi:type="uml:Comment" body="Interface blocks cannot have behaviors, including classifier behaviors or methods, or internal parts."/>
        <ownedOperation xmi:id="SysML.InterfaceBlock.getConjugated_" xmi:type="uml:Operation" isQuery="true" name="getConjugated">
          <bodyCondition xmi:id="SysML.InterfaceBlock.getConjugated_._rule.getConjugated_body" xmi:type="uml:Constraint" name="getConjugated_body">
            <specification xmi:id="SysML.InterfaceBlock.getConjugated_._rule.getConjugated_body.bodySpecification" xmi:type="uml:OpaqueExpression" body="~InterfaceBlock.allInstances()-&gt;any(ib | ib.original = self)" language="OCL" name="bodySpecification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            </specification>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.InterfaceBlock.getConjugated_.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.InterfaceBlock.getConjugated_.return.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type xmi:idref="SysML.InterfaceBlock"/>
            <upperValue xmi:id="SysML.InterfaceBlock.getConjugated_.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.InterfaceBlock.getConjugated_._rule.getConjugated_body"/>
        </ownedOperation>
        <ownedRule xmi:id="SysML.InterfaceBlock._rule.3_interfaceblock_typed_ports" xmi:type="uml:Constraint" name="3_interfaceblock_typed_ports">
          <ownedComment xmi:id="SysML.InterfaceBlock._rule.3_interfaceblock_typed_ports._comment0" xmi:type="uml:Comment" body="Ports owned by interface blocks shall only be typed by interface blocks."/>
          <specification xmi:id="SysML.InterfaceBlock._rule.3_interfaceblock_typed_ports.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.ownedPort-&gt;forAll(p|InterfaceBlock.allInstances().base_Class-&gt;includes(p.type))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.InterfaceBlock._rule.2_no_part" xmi:type="uml:Constraint" name="2_no_part">
          <ownedComment xmi:id="SysML.InterfaceBlock._rule.2_no_part._comment0" xmi:type="uml:Comment" body="Interface blocks' composite properties are either ports, value properties or flow properties"/>
          <specification xmi:id="SysML.InterfaceBlock._rule.2_no_part.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.ownedAttribute-&gt;select(a|a.isComposite)-&gt;forAll(a | a.oclIsKindOf(UML::Port) or a.oclIsKindOf(ValueType))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.InterfaceBlock._rule.isconjugated_not_used" xmi:type="uml:Constraint" name="isconjugated_not_used">
          <ownedComment xmi:id="SysML.InterfaceBlock._rule.isconjugated_not_used._comment0" xmi:type="uml:Comment" body="Any port typed by an InterfaceBlock shall have its isConjugated property set to false."/>
          <specification xmi:id="SysML.InterfaceBlock._rule.isconjugated_not_used.specification" xmi:type="uml:OpaqueExpression" body="Port.allInstances()-&gt;forAll(p | p.type = self.base_Class implies p.isConjugated=false)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.InterfaceBlock._rule.1_no_behavior" xmi:type="uml:Constraint" name="1_no_behavior">
          <ownedComment xmi:id="SysML.InterfaceBlock._rule.1_no_behavior._comment0" xmi:type="uml:Comment" body="Interface blocks shall not own or inherit behaviors, have classifier behaviors, or methods for their behavioral features."/>
          <specification 
          xmi:id="SysML.InterfaceBlock._rule.1_no_behavior.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.inheritedMember-&gt;select(m | m.oclIsKindOf(UML::Behavior))-&gt;isEmpty() and self.base_Class.operation.method-&gt;flatten()-&gt;isEmpty()" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_FullPort_base_Port" xmi:type="uml:Extension" name="E_extension_FullPort_base_Port">
        <memberEnd xmi:idref="SysML.FullPort.base_Port"/>
        <memberEnd xmi:idref="SysML.E_extension_FullPort_base_Port.extension_FullPort"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_FullPort_base_Port.extension_FullPort"/>
        <ownedEnd xmi:id="SysML.E_extension_FullPort_base_Port.extension_FullPort" xmi:type="uml:ExtensionEnd" name="extension_FullPort">
          <association xmi:idref="SysML.E_extension_FullPort_base_Port"/>
          <lowerValue xmi:id="SysML.E_extension_FullPort_base_Port.extension_FullPort.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.FullPort"/>
          <upperValue xmi:id="SysML.E_extension_FullPort_base_Port.extension_FullPort.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.tildeInterfaceBlock" xmi:type="uml:Stereotype" name="~InterfaceBlock">
        <generalization xmi:id="SysML.tildeInterfaceBlock._generalization.SysML.InterfaceBlock" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.InterfaceBlock"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.tildeInterfaceBlock.original" xmi:type="uml:Property" name="original">
          <lowerValue xmi:id="SysML.tildeInterfaceBlock.original.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.tildeInterfaceBlock.original._comment0" xmi:type="uml:Comment" body="The InterfaceBlock that this is a conjugation of."/>
          <type xmi:idref="SysML.InterfaceBlock"/>
          <upperValue xmi:id="SysML.tildeInterfaceBlock.original.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.tildeInterfaceBlock._comment0" xmi:type="uml:Comment" body="The ~InterfaceBlock stereotype  (shall be pronounced: &quot;conjugated interface block&quot;) is a specialization of InterfaceBlock that has the same features as its original InterfaceBlock except that its DirectedFeatures  and FlowProperties are reversed (conjugated), for example, in flow properties are conjugated as out flow properties and provided features are conjugated as required features. Conjugation is specified by a constraint giving the features of  ~InterfaceBlocks according to those of their original InterfaceBlocks  (see the  Constraints subsection below). It is expected that tools conforming to this specification automatically create features of ~InterfaceBlocks.">
</ownedComment>
        <ownedOperation xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint" xmi:type="uml:Operation" isQuery="true" name="areSameConstraintSets ">
          <bodyCondition xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint._rule.bodyCondition" xmi:type="uml:Constraint" name="bodyCondition">
            <specification 
            xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint._rule.bodyCondition.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="(cs1-&gt;isEmpty() and cs2-&gt;isEmpty())
or (cs1-&gt;size() = cs2-&gt;size()
  and cs1-&gt;forAll(c1 | cs1-&gt;exists(c2 | c2.name = c1.name
    and c2.specification.booleanValue()=true implies c1.specification.booleanValue()=true
    and c2.specification.booleanValue()=false implies c1.specification.booleanValue()=false)))
" language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedComment xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint._comment0" xmi:type="uml:Comment" body="The areSameConstraintSets query is used for specifying the inverted_feature invariant. It checks whether two sets of constraints are equivalent."/>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.cs1" xmi:type="uml:Parameter" effect="create" name="cs1">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.cs1.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Constraint"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.cs1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.cs2" xmi:type="uml:Parameter" effect="create" name="cs2">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.cs2.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Constraint"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.cs2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.return.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML_dataType.Boolean"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.tildeInterfaceBlock.areSameConstraintSets_Constraint_Constraint._rule.bodyCondition"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet" xmi:type="uml:Operation" isQuery="true" name="areSameParameterSets ">
          <bodyCondition xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet._rule.bodyCondition" xmi:type="uml:Constraint" name="bodyCondition">
            <specification 
            xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet._rule.bodyCondition.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="(ps1-&gt;isEmpty() and ps2-&gt;isEmpty())
or (ps1-&gt;size() = ps2-&gt;size()
  and areSameConstraintSets(ps1.condition, ps2.condition
  and ps1.parameter-&gt;forAll(p1 | ps2.parameter-&gt;exists(p2 |
     bf1.ownedParameter-&gt;indexOf(p1) = bf2.ownedParameter-&gt;indexOf(p2)))))
" language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedComment xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet._comment0" xmi:type="uml:Comment" body="The areSameParameterSets query is used for specifying the inverted_feature invariant. It checks whether two sets of parameters are identical."/>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.ps1" xmi:type="uml:Parameter" effect="create" name="ps1">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.ps1.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ParameterSet"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.ps1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.ps2" xmi:type="uml:Parameter" effect="create" name="ps2">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.ps2.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ParameterSet"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.ps2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.return.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML_dataType.Boolean"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.tildeInterfaceBlock.areSameParameterSets_ParameterSet_ParameterSet._rule.bodyCondition"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature" xmi:type="uml:Operation" isQuery="true" name="haveSameSignatures ">
          <bodyCondition xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature._rule.bodyCondition" xmi:type="uml:Constraint" name="bodyCondition">
            <specification 
            xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature._rule.bodyCondition.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="bf1.name = bf2.name
and bf1.ownedParameter-&gt;size() = bf2.ownedParameter-&gt;size()
and bf1.ownedParameter-&gt;forAll(p1 | let p2: UML::Parameter = bf2.ownedParameter-&gt;at(bf1.ownedParameter-&gt;indexOf(p1)) in
  p1.name = p2.name
  and p1.type = p2.type
  and p1.direction = p2.direction
  and p1.isOrdered = p2.isOrdered
  and p1.isUnique = p2.isUnique
  and p1.lower = p2.lower
  and p1.upper = p2.upper
  and p1.effect = p2.effect
  and p1.isException = p2.isException
  and p1.isStream = p2.isStream)
" language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedComment 
          xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature._comment0" xmi:type="uml:Comment" body="The areSameConstraintSignatures query is used for specifying the inverted_feature invariant. It checks whether two behavioral features have the same signature.">
</ownedComment>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.bf1" xmi:type="uml:Parameter" effect="create" name="bf1">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.bf1.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#BehavioralFeature"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.bf1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.bf2" xmi:type="uml:Parameter" effect="create" name="bf2">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.bf2.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#BehavioralFeature"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.bf2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.return.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML_dataType.Boolean"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.tildeInterfaceBlock.haveSameSignatures_BehavioralFeature_BehavioralFeature._rule.bodyCondition"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature" xmi:type="uml:Operation" isQuery="true" name="areConjugated">
          <bodyCondition xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature._rule.bodyCondition" xmi:type="uml:Constraint" name="bodyCondition">
            <specification 
            xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature._rule.bodyCondition.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="if (df1.oclIsUndefined()) then
  (not df2.oclIsUndefined() and df2.featureDirection = FeatureDirectionKind::required)
else if (df2.oclIsUndefined()) then
  (not df1.oclIsUndefined() and df1.featureDirection = FeatureDirectionKind::required)
else
  (df1.featureDirection = FeatureDirectionKind::provided and df2.featureDirection = FeatureDirectionKind::required)
  or (df1.featureDirection = FeatureDirectionKind::required and df2.featureDirection = FeatureDirectionKind::provided)
  or (df1.featureDirection = FeatureDirectionKind::providedRequired and df2.featureDirection = FeatureDirectionKind::providedRequired)
endif endif
" language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedComment 
          xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature._comment0" xmi:type="uml:Comment" body="DirectedFeature overloaded version of the areConjugated query used for specifying the inverted_feature invariant that checks whether one feature definition is the conjugated definition of the other.">
</ownedComment>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.df1" xmi:type="uml:Parameter" effect="create" name="df1">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.df1.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML.DirectedFeature"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.df1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.df2" xmi:type="uml:Parameter" effect="create" name="df2">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.df2.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML.DirectedFeature"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.df2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.return.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML_dataType.Boolean"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.tildeInterfaceBlock.areConjugated_DirectedFeature_DirectedFeature._rule.bodyCondition"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty" xmi:type="uml:Operation" isQuery="true" name="areConjugated">
          <bodyCondition xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty._rule.bodyCondition" xmi:type="uml:Constraint" name="bodyCondition">
            <specification 
            xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty._rule.bodyCondition.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="(fp1.direction = FlowDirectionKind::_in and fp2.direction = FlowDirectionKind::out)
or (fp1.direction = FlowDirectionKind::out and fp2.direction = FlowDirectionKind::_in)
or (fp1.direction = FlowDirectionKind::inout and fp2.direction = FlowDirectionKind::inout)
" language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedComment 
          xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty._comment0" xmi:type="uml:Comment" body="FlowProperty overloaded version of the areConjugated query used for specifying the inverted_feature invariant that check whether one feature definition is the conjugated definition of the other">
</ownedComment>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.fp1" xmi:type="uml:Parameter" effect="create" name="fp1">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.fp1.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML.FlowProperty"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.fp1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.fp2" xmi:type="uml:Parameter" effect="create" name="fp2">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.fp2.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML.FlowProperty"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.fp2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.return.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML_dataType.Boolean"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.tildeInterfaceBlock.areConjugated_FlowProperty_FlowProperty._rule.bodyCondition"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception" xmi:type="uml:Operation" isQuery="true" name="areConjugated">
          <bodyCondition xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception._rule.bodyCondition" xmi:type="uml:Constraint" name="bodyCondition">
            <specification 
            xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception._rule.bodyCondition.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="let df1: DirectedFeature = DirectedFeature.allInstances()-&gt;any(base_Feature = r1) in
let df2: DirectedFeature = DirectedFeature.allInstances()-&gt;any(base_Feature = r2) in
r1.concurrency = r2.concurrency
and r1.isAbstract = r2.isAbstract
and r1.ownedParameterSet-&gt;forAll(ps1 | r2.ownedParameterSet-&gt;exists(ps2 | areSameParameterSets(r1, ps1, r2, ps2)))
and haveSameSignatures(r1, r2)
and r1.signal = r2.signal
and areConjugated(df1, df2)
" language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedComment 
          xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception._comment0" xmi:type="uml:Comment" body="Reception overloaded version of the areConjugated query used for specifying the inverted_feature invariant that check whether one feature definition is the conjugated definition of the other.">
</ownedComment>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.r1" xmi:type="uml:Parameter" effect="create" name="r1">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.r1.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Reception"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.r1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.r2" xmi:type="uml:Parameter" effect="create" name="r2">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.r2.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Reception"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.r2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.return.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML_dataType.Boolean"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.tildeInterfaceBlock.areConjugated_Reception_Reception._rule.bodyCondition"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation" xmi:type="uml:Operation" isQuery="true" name="areConjugated">
          <bodyCondition xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation._rule.bodyCondition" xmi:type="uml:Constraint" name="bodyCondition">
            <specification 
            xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation._rule.bodyCondition.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="let df1: DirectedFeature = DirectedFeature .allInstances()-&gt;any(base_Feature = o1) in
let df2: DirectedFeature = DirectedFeature .allInstances()-&gt;any(base_Feature = o2) in
o1.concurrency = o2.concurrency
and o1.isAbstract = o2.isAbstract
and o1.ownedParameterSet-&gt;forAll(ps1 | o2.ownedParameterSet-&gt;exists(ps2 | areSameParameterSets(o1, ps1, o2, ps2)))
and areSameConstraintSets(o1.bodyCondition-&gt;asSet(), o2.bodyCondition-&gt;asSet())
and areSameConstraintSets(o1.precondition, o2.precondition)
and areSameConstraintSets(o1.postcondition, o2.postcondition)
and haveSameSignatures(o1, o2)
and o1.raisedException-&gt;forAll(e1 | o2.raisedException-&gt;exists(e2 | e2 = e1))
and o1.isQuery = o2.isQuery
and areConjugated(df1, df2)
" language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedComment 
          xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation._comment0" xmi:type="uml:Comment" body="Operation overloaded version of the areConjugated query used for specifying the inverted_feature invariant that check whether one feature definition is the conjugated definition of the other.">
</ownedComment>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.o1" xmi:type="uml:Parameter" effect="create" name="o1">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.o1.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Operation"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.o1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.o2" xmi:type="uml:Parameter" effect="create" name="o2">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.o2.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Operation"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.o2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.return.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML_dataType.Boolean"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.tildeInterfaceBlock.areConjugated_Operation_Operation._rule.bodyCondition"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property" xmi:type="uml:Operation" isQuery="true" name="areConjugated">
          <bodyCondition xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property._rule.bodyCondition" xmi:type="uml:Constraint" name="bodyCondition">
            <specification 
            xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property._rule.bodyCondition.getSatisfiedBy_body_specification" xmi:type="uml:OpaqueExpression" body="let fp1: FlowProperty = FlowProperty.allInstances()-&gt;any(base_Property = a1) in
let fp2: FlowProperty = FlowProperty.allInstances()-&gt;any(base_Property = a2) in
let df1: DirectedFeature = DirectedFeature .allInstances()-&gt;any(base_Feature = a1) in
let df2: DirectedFeature = DirectedFeature .allInstances()-&gt;any(base_Feature = a2) in
a1.name = a2.name
and a1.type = a2.type
and a1.isStatic = a2.isStatic
and a1.isOrdered = a2.isOrdered
and a1.isUnique = a2.isUnique
and a1.lower = a2.lower
and a1.upper = a2.upper
and a1.isReadOnly = a2.isReadOnly
and a1.aggregation = a2.aggregation
and a1.isDerived = a2.isDerived
and a1.isDerivedUnion = a2.isDerivedUnion
and a1.isID = a2.isID
and ((not fp1.oclIsUndefined() and not fp2.oclIsUndefined() and areConjugated(fp1, fp2))
  or
  (fp1.oclIsUndefined() and fp2.oclIsUndefined()))
and ((not df1.oclIsUndefined() and not df2.oclIsUndefined() and areConjugated(df1, df2))
   or (df1.oclIsUndefined() and df2.oclIsUndefined()))
" language="OCL" name="getSatisfiedBy_body_specification">
              <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
</specification>
          </bodyCondition>
          <ownedComment 
          xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property._comment0" xmi:type="uml:Comment" body="Property overloaded version of the areConjugated query used for specifying the inverted_feature invariant that checks whether one feature definition is the conjugated definition of the other.">
</ownedComment>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.p1" xmi:type="uml:Parameter" effect="create" name="p1">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.p1.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.p1.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.p2" xmi:type="uml:Parameter" effect="create" name="p2">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.p2.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Property"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.p2.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.return" xmi:type="uml:Parameter" direction="return" effect="read" name="return">
            <lowerValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.return.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type xmi:idref="SysML_dataType.Boolean"/>
            <upperValue xmi:id="SysML.tildeInterfaceBlock.areConjugated_Property_Property.return.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.tildeInterfaceBlock.areConjugated_Property_Property._rule.bodyCondition"/>
        </ownedOperation>
        <ownedRule xmi:id="SysML.tildeInterfaceBlock._rule.inverted_features" xmi:type="uml:Constraint" name="inverted_features">
          <ownedComment 
          xmi:id="SysML.tildeInterfaceBlock._rule.inverted_features._comment0" xmi:type="uml:Comment" body="An ~InterfaceBlock has same features and owned rules than its original InterfaceBlock except that – where applicable – both its DirectedFeatures  and FlowProperties have inverted directions (i.e. are &quot;conjugated&quot;).">
</ownedComment>
          <specification 
          xmi:id="SysML.tildeInterfaceBlock._rule.inverted_features.specification" xmi:type="uml:OpaqueExpression" body="let allAttributes: Set(UML::Property) = self.base_Class.allFeatures()-&gt;select(oclIsKindOf(UML::Property)).oclAsType(UML::Property)-&gt;asSet() in
let allOperations: Set(UML::Operation) = self.base_Class.allFeatures()-&gt;select(oclIsKindOf(UML::Operation)).oclAsType(UML::Operation)-&gt;asSet() in
let allReceptions: Set(UML::Reception) = self.base_Class.allFeatures()-&gt;select(oclIsKindOf(UML::Reception)).oclAsType(UML::Reception)-&gt;asSet() in
let inheritedRules: Set(UML::Constraint) = self.base_Class.inherit(self.base_Class.inheritedMember-&gt;select(oclIsKindOf(UML::Constraint))).oclAsType(UML::Constraint)-&gt;asSet() in
let allRules: Set(UML::Constraint) = self.base_Class.ownedRule-&gt;union(inheritedRules) in
let allOriginalAttributes: Set(UML::Property) = self.original.base_Class.allFeatures()-&gt;select(oclIsKindOf(UML::Property)).oclAsType(UML::Property)-&gt;asSet() in
let allOriginalOperations: Set(UML::Operation) = self.original.base_Class.allFeatures()-&gt;select(oclIsKindOf(UML::Operation)).oclAsType(UML::Operation)-&gt;asSet() in
let allOriginalReceptions: Set(UML::Reception) = self.original.base_Class.allFeatures()-&gt;select(oclIsKindOf(UML::Reception)).oclAsType(UML::Reception)-&gt;asSet() in
let originalInheritedRules: Set(UML::Constraint) = self.original.base_Class.inherit(self.original.base_Class.inheritedMember-&gt;select(oclIsKindOf(UML::Constraint))).oclAsType(UML::Constraint)-&gt;asSet() in
let allOrignalRules: Set(UML::Constraint) = self.original.base_Class.ownedRule-&gt;union(originalInheritedRules) in

allAttributes-&gt;size() = allOriginalAttributes-&gt;size()
and allOperations-&gt;size() = allOriginalOperations-&gt;size()
and allReceptions-&gt;size() = allOriginalReceptions-&gt;size()

and (allAttributes-&gt;isEmpty() or allAttributes-&gt;forAll(a | allOriginalAttributes-&gt;exists(oa | areConjugated(a, oa))))
and (allOperations-&gt;isEmpty() or allOperations-&gt;forAll(o | allOriginalOperations-&gt;exists(oo | areConjugated(o, oo))))
and (allReceptions-&gt;isEmpty() or allReceptions-&gt;forAll(r | allOriginalReceptions-&gt;exists(ro | areConjugated(r, ro))))
and areSameConstraintSets(allRules, allOrignalRules)
" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.tildeInterfaceBlock._rule.enforced_name" xmi:type="uml:Constraint" name="enforced_name">
          <ownedComment xmi:id="SysML.tildeInterfaceBlock._rule.enforced_name._comment0" xmi:type="uml:Comment" body="The name of an ~InterfaceBlock shall be the name of its original InterfaceBlock with a tilde (&quot;~&quot;) character prepended"/>
          <specification xmi:id="SysML.tildeInterfaceBlock._rule.enforced_name.specification" xmi:type="uml:OpaqueExpression" body="self.base_Class.name = '~'+self.original.base_Class.name" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction" xmi:type="uml:Stereotype" name="AddFlowPropertyValueOnNestedPortAction">
        <generalization xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._generalization.SysML.ElementPropertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.ElementPropertyPath"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction.onNestedPort" xmi:type="uml:Property" isOrdered="true" isUnique="false" name="onNestedPort">
          <lowerValue xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction.onNestedPort.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment 
          xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction.onNestedPort._comment0" xmi:type="uml:Comment" body="Gives a series of ports that end in one supporting the flow property to which a value is being added. The ordering of ports is from a port of the object of the stereotyped action, through a port of each intermediate block that types the preceding port, ending in a port with a type that owns or inherits the flow property. The same port might appear more than once because a block can own a port with the same block as a type, or another block that has the same property.">
</ownedComment>
          <redefinedProperty xmi:idref="SysML.ElementPropertyPath.propertyPath"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Port"/>
          <upperValue xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction.onNestedPort.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction.base_AddStructuralFeatureValueAction" xmi:type="uml:Property" name="base_AddStructuralFeatureValueAction">
          <association xmi:idref="SysML."/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#AddStructuralFeatureValueAction"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._comment0" xmi:type="uml:Comment" body="This enables values added to a flow property to propagate out through a specified behavioral port of an object executing the action, rather than all behavior ports exposing the flow property. It also enables values added to a flow property to propagate into objects. Values flowing out of an object are added to an out or inout flow property of the executing object. In this case, the applied stereotype specifies a (possibly nested) behavioral port at the end of a (possibly multi-level) path of behavioral ports from a block that supports the flow property. Values flowing into an object are added to an in or inout flow property of that object, specifying a (possibly nested) port of that object.">
</ownedComment>
        <ownedRule xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.1_feature_flowproperty" xmi:type="uml:Constraint" name="1_feature_flowproperty">
          <ownedComment xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.1_feature_flowproperty._comment0" xmi:type="uml:Comment" body="The structural feature referred by actions with this stereotype applied must have FlowProperty applied."/>
          <specification 
          xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.1_feature_flowproperty.specification" xmi:type="uml:OpaqueExpression" body="FlowProperty.allInstances().base_Property-&gt;includes(self.base_AddStructuralFeatureValueAction.structuralFeature)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.4_onnestedport_last_type_owns_invocation_onPort" xmi:type="uml:Constraint" name="4_onnestedport_last_type_owns_invocation_onPort">
          <ownedComment 
          xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.4_onnestedport_last_type_owns_invocation_onPort._comment0" xmi:type="uml:Comment" body="The type of the port at the last position of the onNestedPort list shall own or inherit the flow property that is the structural feature of the stereotyped action">
</ownedComment>
          <specification 
          xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.4_onnestedport_last_type_owns_invocation_onPort.specification" xmi:type="uml:OpaqueExpression" body="self.onNestedPort-&gt;last().type.oclAsType(UML::Classifier).allFeatures()-&gt;includes(self.base_AddStructuralFeatureValueAction.structuralFeature)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.3_path_consistency" xmi:type="uml:Constraint" name="3_path_consistency">
          <ownedComment 
          xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.3_path_consistency._comment0" xmi:type="uml:Comment" body="The port at each successive position of the onNestedPort attribute, following the first position, shall be owned by the Block that types the port at the immediately preceding position, or a generalization of that Block">
</ownedComment>
          <specification 
          xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.3_path_consistency.specification" xmi:type="uml:OpaqueExpression" body="self.onNestedPort-&gt;size() &gt;1 implies self.propertyPath-&gt;subSequence(2, self.onNestedPort-&gt;size())-&gt;forAll(p |
let pp: UML::Property = self.onNestedPort-&gt;at(self.onNestedPort-&gt;indexOf(p)-1) in
let owners: Set(UML::Classifier) = pp.type.oclAsType(UML::Classifier)-&gt;including(pp.type.oclAsType(UML::Classifier)) in
owners-&gt;includes(p.owner))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.2_onnestedport_first_owned_by_target_type" xmi:type="uml:Constraint" name="2_onnestedport_first_owned_by_target_type">
          <ownedComment 
          xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.2_onnestedport_first_owned_by_target_type._comment0" xmi:type="uml:Comment" body="The port at the first position in the onNestedPort list shall be owned by the block that types the object pin of the stereotyped action, or one of that block’s generalizations.">
</ownedComment>
          <specification 
          xmi:id="SysML.AddFlowPropertyValueOnNestedPortAction._rule.2_onnestedport_first_owned_by_target_type.specification" xmi:type="uml:OpaqueExpression" body="self.base_AddStructuralFeatureValueAction.object.type.oclAsType(UML::Classifier)-&gt;allFeatures()-&gt;includes(self.onNestedPort-&gt;first()))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_invocationOnNestedPortAction_onNestedPort" xmi:type="uml:Association" name="A_invocationOnNestedPortAction_onNestedPort">
        <generalization xmi:id="SysML.A_invocationOnNestedPortAction_onNestedPort._generalization.SysML.A_elementPropertyPath_propertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.A_elementPropertyPath_propertyPath"/>
        </generalization>
        <memberEnd xmi:idref="SysML.A_invocationOnNestedPortAction_onNestedPort.invocationOnNestedPortAction"/>
        <memberEnd xmi:idref="SysML.InvocationOnNestedPortAction.onNestedPort"/>
        <ownedEnd xmi:id="SysML.A_invocationOnNestedPortAction_onNestedPort.invocationOnNestedPortAction" xmi:type="uml:Property" name="invocationOnNestedPortAction">
          <association xmi:idref="SysML.A_invocationOnNestedPortAction_onNestedPort"/>
          <lowerValue xmi:id="SysML.A_invocationOnNestedPortAction_onNestedPort.invocationOnNestedPortAction.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <redefinedProperty xmi:idref="SysML.A_elementPropertyPath_propertyPath.elementPropertyPath"/>
          <type xmi:idref="SysML.InvocationOnNestedPortAction"/>
          <upperValue xmi:id="SysML.A_invocationOnNestedPortAction_onNestedPort.invocationOnNestedPortAction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.AcceptChangeStructuralFeatureEventAction" xmi:type="uml:Stereotype" name="AcceptChangeStructuralFeatureEventAction">
        <ownedAttribute xmi:id="SysML.AcceptChangeStructuralFeatureEventAction.base_AcceptEventAction" xmi:type="uml:Property" name="base_AcceptEventAction">
          <association xmi:idref="SysML.E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction"/>
          <lowerValue xmi:id="SysML.AcceptChangeStructuralFeatureEventAction.base_AcceptEventAction.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#AcceptEventAction"/>
          <upperValue xmi:id="SysML.AcceptChangeStructuralFeatureEventAction.base_AcceptEventAction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._comment0" xmi:type="uml:Comment" body="Accept change structural feature event actions handle change structural feature events (see clause 9.3.2.10). The actions have exactly two output pins. The first output pin holds the values of the structural feature just after the values changed, while the second pin holds the values just before the values changed. The action only accepts events for structural features on the blocks owning the behavior containing the action, or on the behavior itself, if the behavior is not owned by a block.">
</ownedComment>
        <ownedRule xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.2_two_resultpins" xmi:type="uml:Constraint" name="2_two_resultpins">
          <ownedComment 
          xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.2_two_resultpins._comment0" xmi:type="uml:Comment" body="The action has two result pins with type and ordering the same as the type and ordering of the structural feature of the trigger event, and multiplicity compatible with the multiplicity of the structural feature.">
</ownedComment>
          <specification 
          xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.2_two_resultpins.specification" xmi:type="uml:OpaqueExpression" body="let event: ChangeStructuralFeatureEvent = ChangeStructuralFeatureEvent.allInstances()-&gt;any(e | e.base_ChangeEvent = self.base_AcceptEventAction.trigger-&gt;any(true).event) in self.base_AcceptEventAction.result-&gt;size() = 2 and  self.base_AcceptEventAction.result-&gt;forAll(r | r.type = event.structuralFeature.type   and r.isOrdered = event.structuralFeature.isOrdered  and r.lower &lt;= event.structuralFeature.lower  and r.upper &gt;= event.structuralFeature.upper) " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.5_uml_constraint_removed" xmi:type="uml:Constraint" name="5_uml_constraint_removed">
          <ownedComment 
          xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.5_uml_constraint_removed._comment0" xmi:type="uml:Comment" body="The constraint under 11.3.2, “AcceptEventAction” in the UML 2 standard, “[2] There are no output pins if the trigger events are only ChangeEvents,” shall be removed for accept event actions that have AcceptChangeStructuralFeatureEventAction applied.">
</ownedComment>
          <specification xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.5_uml_constraint_removed.specification" xmi:type="uml:OpaqueExpression" body="-- cannot be expressed in OCL " language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.4_can_access_structuralfeature" xmi:type="uml:Constraint" name="4_can_access_structuralfeature">
          <ownedComment xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.4_can_access_structuralfeature._comment0" xmi:type="uml:Comment" body="Visibility of the structural feature of the trigger event shall allow access to the object performing the action."/>
          <specification 
          xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.4_can_access_structuralfeature.specification" xmi:type="uml:OpaqueExpression" body="let event: ChangeStructuralFeatureEvent = ChangeStructuralFeatureEvent.allInstances()-&gt;any(e | e.base_ChangeEvent = self.base_AcceptEventAction.trigger-&gt;any(true).event) in  if event.structuralFeature.visibility = UML::VisibilityKind::private then  self.base_AcceptEventAction._'context'.feature-&gt;includes(event.structuralFeature)   else if event.structuralFeature.visibility = UML::VisibilityKind::protected then  self.base_AcceptEventAction._'context'.allFeatures()-&gt;includes(event.structuralFeature)   else if event.structuralFeature.visibility = UML::VisibilityKind::_'package' then  let thePackage: UML::Package = event.structuralFeature.allNamespaces()-&gt;select(n | n.oclIsKindOf(UML::Package))-&gt;first().oclAsType(UML::Package) in   (not thePackage.oclIsUndefined()) and (   let index: Integer = event.structuralFeature.allNamespaces()-&gt;indexOf(thePackage) in   event.structuralFeature.allNamespaces()-&gt;subOrderedSet(1, index)   -&gt;iterate(n; acc: Boolean=true |    acc and not (n.visibility=UML::VisibilityKind::private or n.visibility=UML::VisibilityKind::protected)) ) else  true endif endif endif" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.1_one_trigger" xmi:type="uml:Constraint" name="1_one_trigger">
          <ownedComment xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.1_one_trigger._comment0" xmi:type="uml:Comment" body="The action has exactly one trigger, the event of which shall be a change structural feature event."/>
          <specification 
          xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.1_one_trigger.specification" xmi:type="uml:OpaqueExpression" body="self.base_AcceptEventAction.trigger-&gt;size()=1 and let trigger: UML::Trigger = self.base_AcceptEventAction.trigger-&gt;any(true) in ChangeStructuralFeatureEvent.allInstances().base_ChangeEvent-&gt;includes(trigger.event)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.3_context_owns_structuralfeature" xmi:type="uml:Constraint" name="3_context_owns_structuralfeature">
          <ownedComment 
          xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.3_context_owns_structuralfeature._comment0" xmi:type="uml:Comment" body="The structural feature of the trigger event shall be owned by or inherited by the context of the behavior containing the action. (The context of a behavior is either its owning block or itself if it is not owned by a block. See definition in the UML 2 standard.)">
</ownedComment>
          <specification 
          xmi:id="SysML.AcceptChangeStructuralFeatureEventAction._rule.3_context_owns_structuralfeature.specification" xmi:type="uml:OpaqueExpression" body="let event: ChangeStructuralFeatureEvent = ChangeStructuralFeatureEvent.allInstances()-&gt;any(e | e.base_ChangeEvent = self.base_AcceptEventAction.trigger-&gt;any(true).event) in self.base_AcceptEventAction._'context'-&gt;notEmpty() and self.base_AcceptEventAction._'context'.allFeatures()-&gt;includes(event.structuralFeature)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.ProxyPort" xmi:type="uml:Stereotype" name="ProxyPort">
        <ownedAttribute xmi:id="SysML.ProxyPort.base_Port" xmi:type="uml:Property" name="base_Port">
          <association xmi:idref="SysML.E_extension_ProxyPort_base_Port"/>
          <lowerValue xmi:id="SysML.ProxyPort.base_Port.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Port"/>
          <upperValue xmi:id="SysML.ProxyPort.base_Port.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.ProxyPort._comment0" xmi:type="uml:Comment" body="Proxy ports identify features of the owning block or its internal parts that are available to external blocks through external connectors to the ports. They do not specify a separate element of the system from the owning block or internal parts. Actions on features of a proxy port have the same effect as if they were acting on features of the owning block or internal parts the port stands in for, and changes to features of the owning block or internal parts that the proxy port makes available to external blocks are visible to those blocks via connectors to the port. (This applies to provided features; for required features, see Section 9.3.2.10.) Proxy ports do not specify their own behaviors or internal parts, and shall be typed by interface blocks. Their nested ports shall also be proxy ports.
 A completely specified proxy port shall describe how any interaction through the port is handled or initiated. This can be achieved in several ways. For instance by making it behavioral, by binding it to a fully specified internal part or by having all its properties individually bound to internal parts. However, blocks can be defined with non-behavioral proxy ports that do not have internal connectors, with the expectation that these will be added in specialized blocks. Internal connectors to ports are the ones inside the port’s owner (specifically, they are the ones that do not have a UML partwithPort on the connector end linked to the port, assuming NestedConnectorEnd is not applied to that end, or if NestedConnectorEnd is applied to that end, they are the connectors that have only ports in the property path of that end). The rest of the connectors linked to a port are external.

Proxy ports can be connected to internal parts or ports on internal parts, identifying features on those parts or ports that are available to external blocks. When a proxy port is connected to a single internal part, the connector shall be a binding connector, or have the same semantics as a binding connector (the value of the proxy port and the connected internal part are the same; links of associations typing the connector are between all objects and themselves, and no others). When a proxy port is connected to multiple internal parts, the connectors have the same semantics as a single binding connector to an aggregate of those parts, supporting all their features, and treating flows and invocations from outside the aggregate as if they were to those parts, and flows and invocations it receives from those parts as if they were to the outside. This aggregate is not a separate element of the system, and only groups the internal parts for purposes of binding to the proxy port. Internal connectors to proxy ports can be typed by association blocks, including when the connector is binding.">
</ownedComment>
        <ownedRule xmi:id="SysML.ProxyPort._rule.1_not_fullport" xmi:type="uml:Constraint" name="1_not_fullport">
          <ownedComment xmi:id="SysML.ProxyPort._rule.1_not_fullport._comment0" xmi:type="uml:Comment" body="Proxy ports shall not also be full ports. This applies even if some of the stereotypes are on subsetted or redefined ports."/>
          <specification xmi:id="SysML.ProxyPort._rule.1_not_fullport.specification" xmi:type="uml:OpaqueExpression" body="FullPort.allInstances()-&gt;excludes(self.base_Port)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ProxyPort._rule.2_interfaceblock" xmi:type="uml:Constraint" name="2_interfaceblock">
          <ownedComment xmi:id="SysML.ProxyPort._rule.2_interfaceblock._comment0" xmi:type="uml:Comment" body="Proxy ports shall only be typed by interface blocks."/>
          <specification xmi:id="SysML.ProxyPort._rule.2_interfaceblock.specification" xmi:type="uml:OpaqueExpression" body="InterfaceBlock.allInstances().base_Class-&gt;includes(self.base_Port.type)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.ProxyPort._rule.3_subports_are_proxyports" xmi:type="uml:Constraint" name="3_subports_are_proxyports">
          <ownedComment xmi:id="SysML.ProxyPort._rule.3_subports_are_proxyports._comment0" xmi:type="uml:Comment" body="Ports owned by the type of a proxy port shall be proxy ports."/>
          <specification xmi:id="SysML.ProxyPort._rule.3_subports_are_proxyports.specification" xmi:type="uml:OpaqueExpression" body="ProxyPort.allInstances().base_Port-&gt;includesAll(self.base_Port.class.ownedPort)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.TriggerOnNestedPort" xmi:type="uml:Stereotype" name="TriggerOnNestedPort">
        <generalization xmi:id="SysML.TriggerOnNestedPort._generalization.SysML.ElementPropertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.ElementPropertyPath"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.TriggerOnNestedPort.base_Trigger" xmi:type="uml:Property" name="base_Trigger">
          <association xmi:idref="SysML.E_extension_TriggerOnNestedPort_base_Trigger"/>
          <lowerValue xmi:id="SysML.TriggerOnNestedPort.base_Trigger.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <redefinedProperty xmi:idref="SysML.ElementPropertyPath.base_Element"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Trigger"/>
          <upperValue xmi:id="SysML.TriggerOnNestedPort.base_Trigger.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.TriggerOnNestedPort.onNestedPort" xmi:type="uml:Property" isOrdered="true" isUnique="false" name="onNestedPort">
          <association xmi:idref="SysML.A_triggerOnNestedPort_onNestedPort"/>
          <lowerValue xmi:id="SysML.TriggerOnNestedPort.onNestedPort.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment 
          xmi:id="SysML.TriggerOnNestedPort.onNestedPort._comment0" xmi:type="uml:Comment" body="Gives a series of ports that identifies a port on which the event is occurring, in the context of a block in which the trigger is used. The ordering of ports is from a port of the receiving object, through a port of each intermediate block that types the preceding port, ending in a property with a type that owns or inherits the port given by the port property of the trigger. The port property is not included in the onNestedPort list. The same port might appear more than once because a block can own a port with the same block as a type, or another block that has the same property.">
</ownedComment>
          <redefinedProperty xmi:idref="SysML.ElementPropertyPath.propertyPath"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Port"/>
          <upperValue xmi:id="SysML.TriggerOnNestedPort.onNestedPort.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedAttribute>
        <ownedComment xmi:id="SysML.TriggerOnNestedPort._comment0" xmi:type="uml:Comment" body="This extends trigger to support nested ports. It identifies a nested port by a multi-level path of ports from the object receiving the triggering events. It is not applicable to full ports."/>
        <ownedRule xmi:id="SysML.TriggerOnNestedPort._rule.2_no_fullport" xmi:type="uml:Constraint" name="2_no_fullport">
          <ownedComment xmi:id="SysML.TriggerOnNestedPort._rule.2_no_fullport._comment0" xmi:type="uml:Comment" body="The values of the onNestedPort property shall not be full ports."/>
          <specification xmi:id="SysML.TriggerOnNestedPort._rule.2_no_fullport.specification" xmi:type="uml:OpaqueExpression" body="FullPort.allInstances().base_Port-&gt;excludesAll(self.onNestedPort)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.TriggerOnNestedPort._rule.5_onnestedport_last_type_owns_trigger_port" xmi:type="uml:Constraint" name="5_onnestedport_last_type_owns_trigger_port">
          <ownedComment xmi:id="SysML.TriggerOnNestedPort._rule.5_onnestedport_last_type_owns_trigger_port._comment0" xmi:type="uml:Comment" body="The type of the port at the last position of the onNestedPort list must own or inherit the port of the stereotyped trigger."/>
          <specification 
          xmi:id="SysML.TriggerOnNestedPort._rule.5_onnestedport_last_type_owns_trigger_port.specification" xmi:type="uml:OpaqueExpression" body="self.onNestedPort-&gt;last().type.oclAsType(UML::Classifier).allFeatures()-&gt;includes(self.base_Trigger.port)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.TriggerOnNestedPort._rule.4_path_consistency" xmi:type="uml:Constraint" name="4_path_consistency">
          <ownedComment 
          xmi:id="SysML.TriggerOnNestedPort._rule.4_path_consistency._comment0" xmi:type="uml:Comment" body="The port at each successive position of the onNestedPort attribute, following the first position, shall be owned by the Block that types the port at the immediately preceding position, or a generalization of the Block.">
</ownedComment>
          <specification 
          xmi:id="SysML.TriggerOnNestedPort._rule.4_path_consistency.specification" xmi:type="uml:OpaqueExpression" body="self.onNestedPort-&gt;size() &gt;1 implies self.onNestedPort-&gt;subSequence(2, self.onNestedPort-&gt;size())-&gt;forAll(p |
  let np: UML::Port = self.onNestedPort-&gt;at(self.onNestedPort-&gt;indexOf(p)-1) in
  let owners: Set(UML::Classifier) = np.type.oclAsType(UML::Classifier)-&gt;including(np.type.oclAsType(UML::Classifier)) in
  owners-&gt;includes(p.owner))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.TriggerOnNestedPort._rule.1_single_proxyport" xmi:type="uml:Constraint" name="1_single_proxyport">
          <ownedComment xmi:id="SysML.TriggerOnNestedPort._rule.1_single_proxyport._comment0" xmi:type="uml:Comment" body="The port property of the stereotyped trigger shall have exactly one value, and the value cannot be a full port."/>
          <specification xmi:id="SysML.TriggerOnNestedPort._rule.1_single_proxyport.specification" xmi:type="uml:OpaqueExpression" body="self.base_Trigger.port-&gt;size()=1 and FullPort.allInstances().base_Port-&gt;excludes(self.base_Trigger.port)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.TriggerOnNestedPort._rule.3_onnestedport_first_owned_by_context" xmi:type="uml:Constraint" name="3_onnestedport_first_owned_by_context">
          <ownedComment xmi:id="SysML.TriggerOnNestedPort._rule.3_onnestedport_first_owned_by_context._comment0" xmi:type="uml:Comment" body="The port at the first position in the onNestedPort list shall be owned by a block in which the trigger is used, or one of the block’s generalizations."/>
          <specification 
          xmi:id="SysML.TriggerOnNestedPort._rule.3_onnestedport_first_owned_by_context.specification" xmi:type="uml:OpaqueExpression" body="let theContext: UML::Classifier = if self.base_Trigger.owner.oclIsKindOf(UML::Action) then  self.base_Trigger.owner.oclAsType(UML::Action)._'context'.oclAsType(UML::Class) else  self.base_Trigger.owner.oclAsType(UML::Transition).containingStateMachine()._'context'.oclAsType(UML::Class) endif in let owners: Set(UML::Classifier) = theContext-&gt;closure(general)-&gt;including(theContext) in owners-&gt;includes(self.onNestedPort-&gt;first().owner)" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction" xmi:type="uml:Extension" name="E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction">
        <memberEnd xmi:idref="SysML.AcceptChangeStructuralFeatureEventAction.base_AcceptEventAction"/>
        <memberEnd xmi:idref="SysML.E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction.extension_AcceptChangeStructuralFeatureEventAction"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction.extension_AcceptChangeStructuralFeatureEventAction"/>
        <ownedEnd xmi:id="SysML.E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction.extension_AcceptChangeStructuralFeatureEventAction" xmi:type="uml:ExtensionEnd" name="extension_AcceptChangeStructuralFeatureEventAction">
          <association xmi:idref="SysML.E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction"/>
          <lowerValue xmi:id="SysML.E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction.extension_AcceptChangeStructuralFeatureEventAction.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.AcceptChangeStructuralFeatureEventAction"/>
          <upperValue xmi:id="SysML.E_extension_AcceptChangeStructuralFeatureEventAction_base_AcceptEventAction.extension_AcceptChangeStructuralFeatureEventAction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_changeStructuralFeatureEvent_structuralFeature" xmi:type="uml:Association" name="A_changeStructuralFeatureEvent_structuralFeature">
        <memberEnd xmi:idref="SysML.A_changeStructuralFeatureEvent_structuralFeature.changeStructuralFeatureEvent"/>
        <memberEnd xmi:idref="SysML.ChangeStructuralFeatureEvent.structuralFeature"/>
        <ownedEnd xmi:id="SysML.A_changeStructuralFeatureEvent_structuralFeature.changeStructuralFeatureEvent" xmi:type="uml:Property" name="changeStructuralFeatureEvent">
          <association xmi:idref="SysML.A_changeStructuralFeatureEvent_structuralFeature"/>
          <lowerValue xmi:id="SysML.A_changeStructuralFeatureEvent_structuralFeature.changeStructuralFeatureEvent.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ChangeStructuralFeatureEvent"/>
          <upperValue xmi:id="SysML.A_changeStructuralFeatureEvent_structuralFeature.changeStructuralFeatureEvent.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_ItemFlow_base_InformationFlow" xmi:type="uml:Extension" name="E_extension_ItemFlow_base_InformationFlow">
        <memberEnd xmi:idref="SysML.E_extension_ItemFlow_base_InformationFlow.extension_ItemFlow"/>
        <memberEnd xmi:idref="SysML.ItemFlow.base_InformationFlow"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_ItemFlow_base_InformationFlow.extension_ItemFlow"/>
        <ownedEnd xmi:id="SysML.E_extension_ItemFlow_base_InformationFlow.extension_ItemFlow" xmi:type="uml:ExtensionEnd" name="extension_ItemFlow">
          <association xmi:idref="SysML.E_extension_ItemFlow_base_InformationFlow"/>
          <lowerValue xmi:id="SysML.E_extension_ItemFlow_base_InformationFlow.extension_ItemFlow.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.ItemFlow"/>
          <upperValue xmi:id="SysML.E_extension_ItemFlow_base_InformationFlow.extension_ItemFlow.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.A_triggerOnNestedPort_onNestedPort" xmi:type="uml:Association" name="A_triggerOnNestedPort_onNestedPort">
        <generalization xmi:id="SysML.A_triggerOnNestedPort_onNestedPort._generalization.SysML.A_elementPropertyPath_propertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.A_elementPropertyPath_propertyPath"/>
        </generalization>
        <memberEnd xmi:idref="SysML.A_triggerOnNestedPort_onNestedPort.triggerOnNestedPort"/>
        <memberEnd xmi:idref="SysML.TriggerOnNestedPort.onNestedPort"/>
        <ownedEnd xmi:id="SysML.A_triggerOnNestedPort_onNestedPort.triggerOnNestedPort" xmi:type="uml:Property" name="triggerOnNestedPort">
          <association xmi:idref="SysML.A_triggerOnNestedPort_onNestedPort"/>
          <lowerValue xmi:id="SysML.A_triggerOnNestedPort_onNestedPort.triggerOnNestedPort.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <redefinedProperty xmi:idref="SysML.A_elementPropertyPath_propertyPath.elementPropertyPath"/>
          <type xmi:idref="SysML.TriggerOnNestedPort"/>
          <upperValue xmi:id="SysML.A_triggerOnNestedPort_onNestedPort.triggerOnNestedPort.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
        </ownedEnd>
      </packagedElement>
    </packagedElement>
    <packagedElement xmi:id="SysML.DeprecatedElements" xmi:type="uml:Package" URI="" name="DeprecatedElements">
      <packagedElement xmi:id="SysML.E_extension_FlowPort_base_Port" xmi:type="uml:Extension" name="E_extension_FlowPort_base_Port">
        <memberEnd xmi:idref="SysML.E_extension_FlowPort_base_Port.extension_FlowPort"/>
        <memberEnd xmi:idref="SysML.FlowPort.base_Port"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_FlowPort_base_Port.extension_FlowPort"/>
        <ownedEnd xmi:id="SysML.E_extension_FlowPort_base_Port.extension_FlowPort" xmi:type="uml:ExtensionEnd" name="extension_FlowPort">
          <association xmi:idref="SysML.E_extension_FlowPort_base_Port"/>
          <lowerValue xmi:id="SysML.E_extension_FlowPort_base_Port.extension_FlowPort.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.FlowPort"/>
          <upperValue xmi:id="SysML.E_extension_FlowPort_base_Port.extension_FlowPort.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_FlowSpecification_base_Interface" xmi:type="uml:Extension" name="E_extension_FlowSpecification_base_Interface">
        <memberEnd xmi:idref="SysML.E_extension_FlowSpecification_base_Interface.extension_FlowSpecification"/>
        <memberEnd xmi:idref="SysML.FlowSpecification.base_Interface"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_FlowSpecification_base_Interface.extension_FlowSpecification"/>
        <ownedEnd xmi:id="SysML.E_extension_FlowSpecification_base_Interface.extension_FlowSpecification" xmi:type="uml:ExtensionEnd" name="extension_FlowSpecification">
          <association xmi:idref="SysML.E_extension_FlowSpecification_base_Interface"/>
          <lowerValue xmi:id="SysML.E_extension_FlowSpecification_base_Interface.extension_FlowSpecification.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.FlowSpecification"/>
          <upperValue xmi:id="SysML.E_extension_FlowSpecification_base_Interface.extension_FlowSpecification.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.FlowPort" xmi:type="uml:Stereotype" name="FlowPort">
        <ownedAttribute xmi:id="SysML.FlowPort.base_Port" xmi:type="uml:Property" name="base_Port">
          <association xmi:idref="SysML.E_extension_FlowPort_base_Port"/>
          <lowerValue xmi:id="SysML.FlowPort.base_Port.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Port"/>
          <upperValue xmi:id="SysML.FlowPort.base_Port.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.FlowPort.direction" xmi:type="uml:Property" name="direction">
          <defaultValue xmi:id="SysML.FlowPort.direction.defaultValue0" xmi:type="uml:InstanceValue" name="">
            <instance xmi:idref="SysML_dataType.FlowDirectionKind.inout"/>
          </defaultValue>
          <lowerValue xmi:id="SysML.FlowPort.direction.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment 
          xmi:id="SysML.FlowPort.direction._comment0" xmi:type="uml:Comment" body="               Indicates the direction in which an atomic flow port relays its items. If the direction is set to “in,” then the items are relayed from an external connector via the flow port into the flow port’s owner (or one of its parts). If the direction is set to “out,” then the items are relayed from the flow port’s owner, via the flow port, through an external connector attached to the flow port. If the direction is set to “inout,” then items can flow both ways. By default, the value is inout.             ">
</ownedComment>
          <type xmi:idref="SysML_dataType.FlowDirectionKind"/>
          <upperValue xmi:id="SysML.FlowPort.direction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedAttribute xmi:id="SysML.FlowPort.isAtomic" xmi:type="uml:Property" isDerived="true" name="isAtomic">
          <lowerValue xmi:id="SysML.FlowPort.isAtomic.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <ownedComment xmi:id="SysML.FlowPort.isAtomic._comment0" xmi:type="uml:Comment" body="This is a derived attribute (derived from the flow port’s type). For a flow port typed by a flow specification the value of this attribute is False, otherwise the value is True."/>
          <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          <upperValue xmi:id="SysML.FlowPort.isAtomic.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.FlowPort._comment0" xmi:type="uml:Comment" body="             A FlowPort is an interaction point through which input and/or output of items such as data, material, or energy may flow. This enables the owning block to declare which items it may exchange with its environment and the interaction points through which the exchange is made. We distinguish between atomic flow port and a nonatomic flow port. Atomic flow ports relay items that are classified by a single Block, ValueType, DataType, or Signal classifier. A nonatomic flow port relays items of several types as specified by a FlowSpecification. Flow ports and associated flow specifications define “what can flow” between the block and its environment, whereas item flows specify “what does flow” in a specific usage context. Flow ports relay items to their owning block or to a connector that connects them with their owner’s internal parts (internal connector).           ">
</ownedComment>
      </packagedElement>
      <packagedElement xmi:id="SysML.FlowSpecification" xmi:type="uml:Stereotype" name="FlowSpecification">
        <ownedAttribute xmi:id="SysML.FlowSpecification.base_Interface" xmi:type="uml:Property" name="base_Interface">
          <association xmi:idref="SysML.E_extension_FlowSpecification_base_Interface"/>
          <lowerValue xmi:id="SysML.FlowSpecification.base_Interface.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Interface"/>
          <upperValue xmi:id="SysML.FlowSpecification.base_Interface.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment xmi:id="SysML.FlowSpecification._comment0" xmi:type="uml:Comment" body="A FlowSpecification specifies inputs and outputs as a set of flow properties. A flow specification is used by flow ports to specify what items can flow via the port."/>
      </packagedElement>
    </packagedElement>
    <packagedElement xmi:id="SysML.Allocations" xmi:type="uml:Package" URI="" name="Allocations">
      <packagedElement xmi:id="SysML.E_extension_AllocateActivityPartition_base_ActivityPartition" xmi:type="uml:Extension" name="E_extension_AllocateActivityPartition_base_ActivityPartition">
        <memberEnd xmi:idref="SysML.E_extension_AllocateActivityPartition_base_ActivityPartition.extension_AllocateActivityPartition"/>
        <memberEnd xmi:idref="SysML.AllocateActivityPartition.base_ActivityPartition"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_AllocateActivityPartition_base_ActivityPartition.extension_AllocateActivityPartition"/>
        <ownedEnd xmi:id="SysML.E_extension_AllocateActivityPartition_base_ActivityPartition.extension_AllocateActivityPartition" xmi:type="uml:ExtensionEnd" name="extension_AllocateActivityPartition">
          <association xmi:idref="SysML.E_extension_AllocateActivityPartition_base_ActivityPartition"/>
          <lowerValue xmi:id="SysML.E_extension_AllocateActivityPartition_base_ActivityPartition.extension_AllocateActivityPartition.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <type xmi:idref="SysML.AllocateActivityPartition"/>
          <upperValue xmi:id="SysML.E_extension_AllocateActivityPartition_base_ActivityPartition.extension_AllocateActivityPartition.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.E_extension_Allocate_base_Abstraction" xmi:type="uml:Extension" name="E_extension_Allocate_base_Abstraction">
        <generalization xmi:id="SysML.E_extension_Allocate_base_Abstraction._generalization.SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship"/>
        </generalization>
        <memberEnd xmi:idref="SysML.E_extension_Allocate_base_Abstraction.extension_Allocate"/>
        <memberEnd xmi:idref="SysML.Allocate.base_Abstraction"/>
        <navigableOwnedEnd xmi:idref="SysML.E_extension_Allocate_base_Abstraction.extension_Allocate"/>
        <ownedEnd xmi:id="SysML.E_extension_Allocate_base_Abstraction.extension_Allocate" xmi:type="uml:ExtensionEnd" name="extension_Allocate">
          <association xmi:idref="SysML.E_extension_Allocate_base_Abstraction"/>
          <lowerValue xmi:id="SysML.E_extension_Allocate_base_Abstraction.extension_Allocate.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
          <redefinedProperty xmi:idref="SysML.E_extension_DirectedRelationshipPropertyPath_base_DirectedRelationship.extension_DirectedRelationshipPropertyPath"/>
          <type xmi:idref="SysML.Allocate"/>
          <upperValue xmi:id="SysML.E_extension_Allocate_base_Abstraction.extension_Allocate.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedEnd>
      </packagedElement>
      <packagedElement xmi:id="SysML.Allocate" xmi:type="uml:Stereotype" name="Allocate">
        <generalization xmi:id="SysML.Allocate._generalization.SysML.DirectedRelationshipPropertyPath" xmi:type="uml:Generalization">
          <general xmi:idref="SysML.DirectedRelationshipPropertyPath"/>
        </generalization>
        <ownedAttribute xmi:id="SysML.Allocate.base_Abstraction" xmi:type="uml:Property" name="base_Abstraction">
          <association xmi:idref="SysML.E_extension_Allocate_base_Abstraction"/>
          <lowerValue xmi:id="SysML.Allocate.base_Abstraction.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <redefinedProperty xmi:idref="SysML.DirectedRelationshipPropertyPath.base_DirectedRelationship"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#Abstraction"/>
          <upperValue xmi:id="SysML.Allocate.base_Abstraction.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.Allocate._comment0" xmi:type="uml:Comment" body="Allocate is a dependency based on UML::Abstraction. It is a mechanism for associating elements of different types, or in different hierarchies, at an abstract level. Allocate is used for assessing user model consistency and directing future design activity. It is expected that an «allocate» relationship between model elements is a precursor to a more concrete relationship between the elements, their properties, operations, attributes, or sub-classes.

Allocate is a stereotype of a UML4SysML::Abstraction that is permissible between any two NamedElements. It is depicted as a dependency with the “allocate” keyword attached to it. Allocate is directional in that one NamedElement is the “from” end (no arrow), and one NamedElement is the “to” end (the end with the arrow). The Allocate stereotype specializes DirectedRelationshipPropertyPath to enable allocations to identify their sources and targets by a multi-level path of accessible properties from context blocks for the sources and targets.

The following paragraphs describe types of allocation that are typical in systems engineering.

Behavior allocation relates to the systems engineering concept segregating form from function. This concept requires independent models of “function” (behavior) and “form” (structure), and a separate, deliberate mapping between elements in each of these models. It is acknowledged that this concept does not support a standard object-oriented paradigm, not is this always even desirable. Experience on large scale, complex systems engineering problems have proven, however, that segregation of form and function is a valuable approach. In addition, behavior allocation may also include the allocation of Behaviors to BehavioralFeatures of Blocks (e.g., Operations).

Flow allocation specifically maps flows in functional system representations to flows in structural system representations.

Flow between activities can either be control or object flow. The figures in the Usage Examples show concrete syntax for how object flow is mapped to connectors on Activity Diagrams. Allocation of control flow is not specifically addressed in SysML, but may be represented by relating an ItemFlow to the Control Flow using the UML relationship InformationalFlow.realizingActivityEdge.

Note that allocation of ObjectFlow to Connector is an Allocation of Usage, and does NOT imply any relation between any defining Blocks of ObjectFlows and any defining associations of connectors.

The figures in the Usage Examples illustrate an available mechanism for relating the objectNode from an activity diagram to the ItemFlow on an internal block diagram. ItemFlow is discussed in  clause 9 , “Ports and Flows.”

Pin to Port allocation is not addressed in this release of SysML.

Structure allocation is associated with the concept of separate “logical” and “physical” representations of a system. It is often necessary to construct separate depictions of a system and define mappings between them. For example, a complete system hierarchy may be built and maintained at an abstract level. In turn, it shall then be mapped to another complete assembly hierarchy at a more concrete level. The set of models supporting complex systems development may include many of these levels of abstraction. This International Standard will not define “logical” or “physical” in this context, except to acknowledge the stated need to capture allocation relationships between separate system representations.">
</ownedComment>
        <ownedOperation xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement" xmi:type="uml:Operation" isQuery="true" isStatic="true" name="getAllocatedFrom">
          <bodyCondition xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement._rule.body_condition" xmi:type="uml:Constraint" name="body_condition">
            <specification xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement._rule.body_condition.specification" xmi:type="uml:OpaqueExpression" body="getAllocatedFrom = Allocate.allInstances()-&gt;select(to = ref).from" language="OCL" name="specification"/>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement.ref" xmi:type="uml:Parameter" effect="create" name="ref">
            <lowerValue xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement.ref.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement.ref.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.Allocate.getAllocatedFrom_NamedElement.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.Allocate.getAllocatedFrom_NamedElement._rule.body_condition"/>
        </ownedOperation>
        <ownedOperation xmi:id="SysML.Allocate.getAllocatedTo_NamedElement" xmi:type="uml:Operation" isQuery="true" isStatic="true" name="getAllocatedTo">
          <bodyCondition xmi:id="SysML.Allocate.getAllocatedTo_NamedElement._rule.body_condition" xmi:type="uml:Constraint" name="body_condition">
            <specification xmi:id="SysML.Allocate.getAllocatedTo_NamedElement._rule.body_condition.specification" xmi:type="uml:OpaqueExpression" body="getAllocatedFrom = Allocate.allInstances()-&gt;select(from = ref).to" language="OCL" name="specification"/>
          </bodyCondition>
          <ownedParameter xmi:id="SysML.Allocate.getAllocatedTo_NamedElement.ref" xmi:type="uml:Parameter" effect="create" name="ref">
            <lowerValue xmi:id="SysML.Allocate.getAllocatedTo_NamedElement.ref.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.Allocate.getAllocatedTo_NamedElement.ref.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
          </ownedParameter>
          <ownedParameter xmi:id="SysML.Allocate.getAllocatedTo_NamedElement.result" xmi:type="uml:Parameter" direction="return" effect="create" name="result">
            <lowerValue xmi:id="SysML.Allocate.getAllocatedTo_NamedElement.result.lowerValue0" xmi:type="uml:LiteralInteger" name=""/>
            <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#NamedElement"/>
            <upperValue xmi:id="SysML.Allocate.getAllocatedTo_NamedElement.result.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="*"/>
          </ownedParameter>
          <ownedRule xmi:idref="SysML.Allocate.getAllocatedTo_NamedElement._rule.body_condition"/>
        </ownedOperation>
        <ownedRule xmi:id="SysML.Allocate._rule.2_binary" xmi:type="uml:Constraint" name="2_binary">
          <ownedComment xmi:id="SysML.Allocate._rule.2_binary._comment0" xmi:type="uml:Comment" body="A single «allocate» dependency shall have only one client (from) and one supplier (to)."/>
          <specification xmi:id="SysML.Allocate._rule.2_binary.specification" xmi:type="uml:OpaqueExpression" body="self.base_Abstraction.source-&gt;size() = 1 and self.base_Abstraction.target-&gt;size() = 1" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
      <packagedElement xmi:id="SysML.AllocateActivityPartition" xmi:type="uml:Stereotype" name="AllocateActivityPartition">
        <ownedAttribute xmi:id="SysML.AllocateActivityPartition.base_ActivityPartition" xmi:type="uml:Property" name="base_ActivityPartition">
          <association xmi:idref="SysML.E_extension_AllocateActivityPartition_base_ActivityPartition"/>
          <lowerValue xmi:id="SysML.AllocateActivityPartition.base_ActivityPartition.lowerValue0" xmi:type="uml:LiteralInteger" name="" value="1"/>
          <type href="http://www.omg.org/spec/UML/20161101/UML.xmi#ActivityPartition"/>
          <upperValue xmi:id="SysML.AllocateActivityPartition.base_ActivityPartition.upperValue0" xmi:type="uml:LiteralUnlimitedNatural" name="" value="1"/>
        </ownedAttribute>
        <ownedComment 
        xmi:id="SysML.AllocateActivityPartition._comment0" xmi:type="uml:Comment" body="AllocateActivityPartition is used to depict an «allocate» relationship on an Activity diagram. The AllocateActivityPartition is a standard UML::ActivityPartition, with modified constraints as stated below.">
</ownedComment>
        <ownedRule xmi:id="SysML.AllocateActivityPartition._rule.1_actions_on_client_ends" xmi:type="uml:Constraint" name="1_actions_on_client_ends">
          <ownedComment 
          xmi:id="SysML.AllocateActivityPartition._rule.1_actions_on_client_ends._comment0" xmi:type="uml:Comment" body="An Action appearing in an “AllocateActivityPartition” shall be the /client (from) end of an “allocate” dependency. The element that represents the “AllocateActivityPartition” shall be the /supplier (to) end of the same “allocate” dependency. In the «AllocateActivityPartition» name field, Properties are designated by the use of a fully qualified name (including colon, e.g., “part_name:Block_Name”), and Classifiers are designated by a simple name (no colons, e.g., “Block_Name”).">
</ownedComment>
          <specification 
          xmi:id="SysML.AllocateActivityPartition._rule.1_actions_on_client_ends.specification" xmi:type="uml:OpaqueExpression" body="self.base_ActivityPartition.node-&gt;select(n|n.oclIsKindOf(UML::Action)) -&gt;forAll(a | let allocs: Set(UML::Abstraction) = Allocate.allInstances().base_Abstraction-&gt;select(x |x.client-&gt;includes(a))-&gt;asSet() in  allocs-&gt;exists(x | x.supplier-&gt;includes(self.base_ActivityPartition.represents)))" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
</specification>
        </ownedRule>
        <ownedRule xmi:id="SysML.AllocateActivityPartition._rule.2_not_uml_semantics" xmi:type="uml:Constraint" name="2_not_uml_semantics">
          <ownedComment 
          xmi:id="SysML.AllocateActivityPartition._rule.2_not_uml_semantics._comment0" xmi:type="uml:Comment" body="The «AllocateActivityPartition» shall maintain the constraints, but not the semantics, of the UML::ActivityPartition. Classifiers or Properties represented by an «AllocateActivityPartition» do not have any direct responsibility for invoking behavior depicted within the partition boundaries. To depict this kind of direct responsibility, the modeler is directed to the UML 2 standard, sub clause 12.3.10, “ActivityPartition,” Semantics topic.">
</ownedComment>
          <specification xmi:id="SysML.AllocateActivityPartition._rule.2_not_uml_semantics.specification" xmi:type="uml:OpaqueExpression" body="-- Cannot be expressed in OCL" language="OCL" name="specification">
            <type href="http://www.omg.org/spec/UML/20161101/PrimitiveTypes.xmi#Boolean"/>
          </specification>
        </ownedRule>
      </packagedElement>
    </packagedElement>
    <profileApplication xmi:id="SysML._profileApplication.StandardProfile" xmi:type="uml:ProfileApplication">
      <appliedProfile href="http://www.omg.org/spec/UML/20161101/StandardProfile.xmi#_0"/>
    </profileApplication>
  </uml:Profile>
  <StandardProfile:ModelLibrary xmi:id="StandardProfile.ModelLibrary_appliedOn_SysML.Libraries.UnitAndQuantityKind">
    <base_Package xmi:idref="SysML.Libraries.UnitAndQuantityKind"/>
  </StandardProfile:ModelLibrary>
  <StandardProfile:ModelLibrary xmi:id="StandardProfile.ModelLibrary_appliedOn_SysML.Libraries.PrimitiveValueTypes">
    <base_Package xmi:idref="SysML.Libraries.PrimitiveValueTypes"/>
  </StandardProfile:ModelLibrary>
  <StandardProfile:ModelLibrary xmi:id="StandardProfile.ModelLibrary_appliedOn_SysML.Libraries.ControlValues">
    <base_Package xmi:idref="SysML.Libraries.ControlValues"/>
  </StandardProfile:ModelLibrary>
  <SysML:Block xmi:id="SysML.Block_appliedOn_SysML.QuantityKind" isEncapsulated="false">
    <base_Class xmi:idref="SysML.QuantityKind"/>
  </SysML:Block>
  <SysML:ValueType xmi:id="SysML.ValueType_appliedOn_SysML_dataType.Number">
    <base_DataType xmi:idref="SysML_dataType.Number"/>
  </SysML:ValueType>
  <SysML:ValueType xmi:id="SysML.ValueType_appliedOn_SysML_dataType.Real">
    <base_DataType xmi:idref="SysML_dataType.Real"/>
  </SysML:ValueType>
  <SysML:Block xmi:id="SysML.Block_appliedOn_SysML.Unit" isEncapsulated="false">
    <base_Class xmi:idref="SysML.Unit"/>
  </SysML:Block>
  <StandardProfile:ModelLibrary xmi:id="StandardProfile.ModelLibrary_appliedOn_SysML.Libraries">
    <base_Package xmi:idref="SysML.Libraries"/>
  </StandardProfile:ModelLibrary>
  <SysML:ValueType xmi:id="SysML.ValueType_appliedOn_SysML_dataType.Boolean">
    <base_DataType xmi:idref="SysML_dataType.Boolean"/>
  </SysML:ValueType>
  <SysML:ValueType xmi:id="SysML.ValueType_appliedOn_SysML_dataType.Integer">
    <base_DataType xmi:idref="SysML_dataType.Integer"/>
  </SysML:ValueType>
  <SysML:ValueType xmi:id="SysML.ValueType_appliedOn_SysML_dataType.Complex">
    <base_DataType xmi:idref="SysML_dataType.Complex"/>
  </SysML:ValueType>
  <SysML:ValueType xmi:id="SysML.ValueType_appliedOn_SysML_dataType.String">
    <base_DataType xmi:idref="SysML_dataType.String"/>
  </SysML:ValueType>
  <SysML:ValueType xmi:id="SysML.ValueType_appliedOn_SysML_dataType.ControlValueKind">
    <base_DataType xmi:idref="SysML_dataType.ControlValueKind"/>
  </SysML:ValueType>
  <mofext:Tag xmi:id="SysML_mofTag0" xmi:type="mofext:Tag" name="org.omg.xmi.nsPrefix" element="SysML"/>
</xmi:XMI>

