<?xml version="1.0" encoding="UTF-8"?>
<xmi:XMI xmlns:xmi="http://www.omg.org/spec/XMI/20131001" xmlns:uml="http://www.omg.org/spec/UML/20131001" xmlns:mofext="http://www.omg.org/spec/MOF/20131001">
	<uml:Package xmi:type="uml:Package" xmi:id="_0" name="DC" URI="http://www.omg.org/spec/DD/20131001/DC">
		<ownedComment xmi:type="uml:Comment" xmi:id="_ownedComment.0" annotatedElement="_0">
			<body>The Diagram Common (DC) package contains abstractions shared by the Diagram Interchange and the Diagram Graphics packages.</body>
		</ownedComment>
		<packageImport xmi:type="uml:PackageImport" xmi:id="_packageImport.0">
			<importedPackage href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#_0"/>
		</packageImport>
		<packagedElement xmi:type="uml:DataType" xmi:id="Color" name="Color">
			<ownedComment xmi:type="uml:Comment" xmi:id="Color-_ownedComment.0" annotatedElement="Color">
				<body>Color is a data type that represents a color value in the RGB format.</body>
			</ownedComment>
			<ownedRule xmi:type="uml:Constraint" xmi:id="Color-valid_rgb" name="valid_rgb" constrainedElement="Color">
				<ownedComment xmi:type="uml:Comment" xmi:id="Color-valid_rgb-_ownedComment.0" annotatedElement="Color-valid_rgb">
					<body>the red, green and blue components of the color must be in the range (0...255).</body>
				</ownedComment>
				<specification xmi:type="uml:OpaqueExpression" xmi:id="Color-valid_rgb-_specification">
					<language>OCL</language>
					<body>red &gt;= 0 and red &lt;=255 and green &gt;= 0 and green &lt;=255 and blue &gt;= 0 and blue &lt;=255</body>
				</specification>
			</ownedRule>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Color-red" name="red">
				<ownedComment xmi:type="uml:Comment" xmi:id="Color-red-_ownedComment.0" annotatedElement="Color-red">
					<body>the red component of the color in the range (0..255).</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Integer"/>
			</ownedAttribute>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Color-green" name="green">
				<ownedComment xmi:type="uml:Comment" xmi:id="Color-green-_ownedComment.0" annotatedElement="Color-green">
					<body>the red component of the color in the range (0..255).</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Integer"/>
			</ownedAttribute>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Color-blue" name="blue">
				<ownedComment xmi:type="uml:Comment" xmi:id="Color-blue-_ownedComment.0" annotatedElement="Color-blue">
					<body>the red component of the color in the range (0..255).</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Integer"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:DataType" xmi:id="Point" name="Point">
			<ownedComment xmi:type="uml:Comment" xmi:id="Point-_ownedComment.0" annotatedElement="Point">
				<body>A Point specifies an location in some x-y coordinate system.</body>
			</ownedComment>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Point-x" name="x">
				<ownedComment xmi:type="uml:Comment" xmi:id="Point-x-_ownedComment.0" annotatedElement="Point-x">
					<body>a real number (&lt;= 0 or &gt;= 0) that represents the x-coordinate of the point.</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Real"/>
				<defaultValue xmi:type="uml:LiteralReal" xmi:id="Point-x-_defaultValue" value="0"/>
			</ownedAttribute>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Point-y" name="y">
				<ownedComment xmi:type="uml:Comment" xmi:id="Point-y-_ownedComment.0" annotatedElement="Point-y">
					<body>a real number (&lt;= 0 or &gt;= 0)  that represents the y-coordinate of the point.</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Real"/>
				<defaultValue xmi:type="uml:LiteralReal" xmi:id="Point-y-_defaultValue" value="0"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:DataType" xmi:id="Dimension" name="Dimension">
			<ownedComment xmi:type="uml:Comment" xmi:id="Dimension-_ownedComment.0" annotatedElement="Dimension">
				<body>Dimension specifies two lengths (width and height) along the x and y axes in some x-y coordinate system.</body>
			</ownedComment>
			<ownedRule xmi:type="uml:Constraint" xmi:id="Dimension-non_negative_dimension" name="non_negative_dimension" constrainedElement="Bounds">
				<ownedComment xmi:type="uml:Comment" xmi:id="Dimension-non_negative_dimension-_ownedComment.0" annotatedElement="Dimension-non_negative_dimension">
					<body>the width and height of a dimension cannot be negative</body>
				</ownedComment>
				<specification xmi:type="uml:OpaqueExpression" xmi:id="Dimension-non_negative_dimension-_specification">
					<language>OCL</language>
					<body>width &gt;= 0 and height &gt;=0</body>
				</specification>
			</ownedRule>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Dimension-width" name="width">
				<ownedComment xmi:type="uml:Comment" xmi:id="Dimension-width-_ownedComment.0" annotatedElement="Dimension-width">
					<body>a real number (&gt;=0) that represents a length along the x-axis.</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Real"/>
			</ownedAttribute>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Dimension-height" name="height">
				<ownedComment xmi:type="uml:Comment" xmi:id="Dimension-height-_ownedComment.0" annotatedElement="Dimension-height">
					<body>a real number (&gt;=0) that represents a length along the y-axis.</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Real"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:DataType" xmi:id="Bounds" name="Bounds">
			<ownedComment xmi:type="uml:Comment" xmi:id="Bounds-_ownedComment.0" annotatedElement="Bounds">
				<body>Bounds specifies a rectangular area in some x-y coordinate system that is defined by a location (x and y) and a size (width and height).</body>
			</ownedComment>
			<ownedRule xmi:type="uml:Constraint" xmi:id="Bounds-non_negative_size" name="non_negative_size" constrainedElement="Bounds">
				<ownedComment xmi:type="uml:Comment" xmi:id="Bounds-non_negative_size-_ownedComment.0" annotatedElement="Bounds-non_negative_size">
					<body>the width and height of bounds cannot be negative</body>
				</ownedComment>
				<specification xmi:type="uml:OpaqueExpression" xmi:id="Bounds-non_negative_size-_specification">
					<language>OCL</language>
					<body>width &gt;= 0 and height &gt;=0</body>
				</specification>
			</ownedRule>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Bounds-x" name="x">
				<ownedComment xmi:type="uml:Comment" xmi:id="Bounds-x-_ownedComment.0" annotatedElement="Bounds-x">
					<body>a real number (&gt;=0 or &lt;=0) that represents the x-coordinate of the bounds</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Real"/>
				<defaultValue xmi:type="uml:LiteralReal" xmi:id="Bounds-x-_defaultValue" value="0"/>
			</ownedAttribute>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Bounds-y" name="y">
				<ownedComment xmi:type="uml:Comment" xmi:id="Bounds-y-_ownedComment.0" annotatedElement="Bounds-y">
					<body>a real number (&gt;=0 or &lt;=0) that represents the y-coordinate of the bounds</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Real"/>
				<defaultValue xmi:type="uml:LiteralReal" xmi:id="Bounds-y-_defaultValue" value="0"/>
			</ownedAttribute>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Bounds-width" name="width">
				<ownedComment xmi:type="uml:Comment" xmi:id="Bounds-width-_ownedComment.0" annotatedElement="Bounds-width">
					<body>a real number (&gt;=0) that represents the width of the bounds</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Real"/>
			</ownedAttribute>
			<ownedAttribute xmi:type="uml:Property" xmi:id="Bounds-height" name="height">
				<ownedComment xmi:type="uml:Comment" xmi:id="Bounds-height-_ownedComment.0" annotatedElement="Bounds-height">
					<body>a real number (&gt;=0) that represents the height of the bounds</body>
				</ownedComment>
				<type xmi:type="uml:PrimitiveType" href="http://www.omg.org/spec/UML/20131001/PrimitiveTypes.xmi#Real"/>
			</ownedAttribute>
		</packagedElement>
		<packagedElement xmi:type="uml:Enumeration" xmi:id="AlignmentKind" name="AlignmentKind">
			<ownedComment xmi:type="uml:Comment" xmi:id="AlignmentKind-_ownedComment.0" annotatedElement="AlignmentKind">
				<body>AlignmentKind enumerates the possible options for alignment for layout purposes.</body>
			</ownedComment>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="AlignmentKind-start" name="start">
				<ownedComment xmi:type="uml:Comment" xmi:id="AlignmentKind-start-_ownedComment.0" annotatedElement="AlignmentKind-start">
					<body>an alignment to the start of a given length.</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="AlignmentKind-end" name="end">
				<ownedComment xmi:type="uml:Comment" xmi:id="AlignmentKind-end-_ownedComment.0" annotatedElement="AlignmentKind-end">
					<body>an alignment to the end of a given length</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="AlignmentKind-center" name="center">
				<ownedComment xmi:type="uml:Comment" xmi:id="AlignmentKind-center-_ownedComment.0" annotatedElement="AlignmentKind-center">
					<body>an alignment to the center of a given length</body>
				</ownedComment>
			</ownedLiteral>
		</packagedElement>
		<packagedElement xmi:type="uml:Enumeration" xmi:id="KnownColor" name="KnownColor">
			<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-_ownedComment.0" annotatedElement="KnownColor">
				<body>KnownColor is an enumeration of 17 known colors.</body>
			</ownedComment>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-maroon" name="maroon">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-maroon-_ownedComment.0" annotatedElement="KnownColor-maroon">
					<body>a color with a value of #800000</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-red" name="red">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-red-_ownedComment.0" annotatedElement="KnownColor-red">
					<body>a color with a value of #FF0000</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-orange" name="orange">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-orange-_ownedComment.0" annotatedElement="KnownColor-orange">
					<body>a color with a value of #FFA500</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-yellow" name="yellow">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-yellow-_ownedComment.0" annotatedElement="KnownColor-yellow">
					<body>a color with a value of #FFFF00</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-olive" name="olive">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-olive-_ownedComment.0" annotatedElement="KnownColor-olive">
					<body>a color with a value of #808000</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-purple" name="purple">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-purple-_ownedComment.0" annotatedElement="KnownColor-purple">
					<body>a color with a value of #800080</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-fuchsia" name="fuchsia">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-fuchsia-_ownedComment.0" annotatedElement="KnownColor-fuchsia">
					<body>a color with a value of #FF00FF</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-white" name="white">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-white-_ownedComment.0" annotatedElement="KnownColor-white">
					<body>a color with a value of #FFFFFF</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-lime" name="lime">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-lime-_ownedComment.0" annotatedElement="KnownColor-lime">
					<body>a color with a value of #00FF00</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-green" name="green">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-green-_ownedComment.0" annotatedElement="KnownColor-green">
					<body>a color with a value of #008000</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-navy" name="navy">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-navy-_ownedComment.0" annotatedElement="KnownColor-navy">
					<body>a color with a value of #000080</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-blue" name="blue">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-blue-_ownedComment.0" annotatedElement="KnownColor-blue">
					<body>a color with a value of #0000FF</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-aqua" name="aqua">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-aqua-_ownedComment.0" annotatedElement="KnownColor-aqua">
					<body>a color with a value of #00FFFF</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-teal" name="teal">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-teal-_ownedComment.0" annotatedElement="KnownColor-teal">
					<body>a color with a value of #008080</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-black" name="black">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-black-_ownedComment.0" annotatedElement="KnownColor-black">
					<body>a color with a value of #000000</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-silver" name="silver">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-silver-_ownedComment.0" annotatedElement="KnownColor-silver">
					<body>a color with a value of #C0C0C0</body>
				</ownedComment>
			</ownedLiteral>
			<ownedLiteral xmi:type="uml:EnumerationLiteral" xmi:id="KnownColor-gray" name="gray">
				<ownedComment xmi:type="uml:Comment" xmi:id="KnownColor-gray-_ownedComment.0" annotatedElement="KnownColor-gray">
					<body>a color with a value of #808080</body>
				</ownedComment>
			</ownedLiteral>
		</packagedElement>
	</uml:Package>
	<mofext:Tag xmi:type="mofext:Tag" xmi:id="_13" name="org.omg.xmi.nsPrefix" value="dc" element="_0"/>
</xmi:XMI>
