{"validation_date": "2025-06-27", "overall_status": true, "total_tests": 6, "passed_tests": 5, "pass_rate": "83.3%", "detailed_results": {"前端配置": {"status": true, "details": {"package.json": {"存在": true, "可解析": true, "包名": "biomedical-mbse-frontend", "版本": "1.0.0", "依赖数": 8, "开发依赖数": 5}, "vite.config.js": {"存在": true, "文件大小": 461}}}, "依赖包": {"status": true, "details": {"关键依赖": {"vue": {"存在": true, "版本": "^3.3.4", "描述": "Vue.js框架"}, "vue-router": {"存在": true, "版本": "^4.2.4", "描述": "路由管理"}, "pinia": {"存在": true, "版本": "^2.1.6", "描述": "状态管理"}, "element-plus": {"存在": true, "版本": "^2.3.8", "描述": "UI组件库"}, "axios": {"存在": true, "版本": "^1.4.0", "描述": "HTTP客户端"}}, "总依赖数": 13, "生产依赖": 8, "开发依赖": 5, "关键依赖完整度": "5/5"}}, "目录结构": {"status": true, "details": {"src": {"存在": true, "总文件数": 86, "Vue文件数": 37, "JS/TS文件数": 25}, "src/components": {"存在": true, "总文件数": 43, "Vue文件数": 32, "JS/TS文件数": 1}, "src/views": {"存在": true, "总文件数": 5, "Vue文件数": 4, "JS/TS文件数": 0}, "src/stores": {"存在": true, "总文件数": 10, "Vue文件数": 0, "JS/TS文件数": 9}, "src/api": {"存在": true, "总文件数": 7, "Vue文件数": 0, "JS/TS文件数": 5}, "src/assets": {"存在": true, "总文件数": 1, "Vue文件数": 0, "JS/TS文件数": 0}, "public": {"存在": true, "总文件数": 1, "Vue文件数": 0, "JS/TS文件数": 0}}, "存在目录数": 7, "总目录数": 7}, "Vue组件": {"status": false, "details": {"组件统计": {"biomedical\\BiomedicalDashboard.vue": {"文件大小": 5501, "行数": 197, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "biomedical\\DataStandardsViewer.vue": {"文件大小": 12985, "行数": 475, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "biomedical\\MolecularViewer.vue": {"文件大小": 7847, "行数": 276, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "biomedical\\PerformancePanel.vue": {"文件大小": 1827, "行数": 81, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "biomedical\\RecommendationPanel.vue": {"文件大小": 1529, "行数": 76, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "biomedical\\ToolManager.vue": {"文件大小": 13749, "行数": 490, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\PerformanceMetric.vue": {"文件大小": 1598, "行数": 77, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\RecommendationCard.vue": {"文件大小": 3126, "行数": 123, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\RoleSelector.vue": {"文件大小": 1938, "行数": 78, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\SmartDashboard.vue": {"文件大小": 12343, "行数": 423, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\architect\\SystemArchitectWorkspace.vue": {"文件大小": 29370, "行数": 1120, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\behavior\\BehaviorAnalystWorkspace.vue": {"文件大小": 43623, "行数": 1615, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\charts\\ConnectionChart.vue": {"文件大小": 3886, "行数": 185, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\charts\\PerformanceChart.vue": {"文件大小": 4912, "行数": 209, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\data\\DataModelerWorkspace.vue": {"文件大小": 66724, "行数": 2301, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\data\\DataModelingCanvas.vue": {"文件大小": 30050, "行数": 1238, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\layouts\\CanvasLayout.vue": {"文件大小": 4452, "行数": 188, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\layouts\\FlexLayout.vue": {"文件大小": 2484, "行数": 102, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\layouts\\GridLayout.vue": {"文件大小": 3327, "行数": 138, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\AdvancedSearchDialog.vue": {"文件大小": 3077, "行数": 125, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\MetadataImporter.vue": {"文件大小": 10488, "行数": 360, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\QualityAnalysis.vue": {"文件大小": 6806, "行数": 262, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\RequirementEditDialog.vue": {"文件大小": 15311, "行数": 536, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\RequirementManagement.vue": {"文件大小": 13885, "行数": 481, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\RequirementsAnalystWorkspace.vue": {"文件大小": 32359, "行数": 1288, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\RequirementSelector.vue": {"文件大小": 3626, "行数": 140, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\ScenarioManagement.vue": {"文件大小": 13998, "行数": 487, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\requirements\\TraceabilityAnalysis.vue": {"文件大小": 17112, "行数": 566, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\view\\ComponentEditor.vue": {"文件大小": 3592, "行数": 150, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\view\\CustomizationPanel.vue": {"文件大小": 4162, "行数": 159, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\view\\FilterPanel.vue": {"文件大小": 7294, "行数": 293, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}, "xml_legacy\\view\\ViewToolbar.vue": {"文件大小": 8726, "行数": 351, "有模板": true, "有脚本": true, "有样式": false, "完整性": true}}, "总组件数": 32, "关键组件数": "0/4", "完整组件数": 32}}, "组件质量": {"status": true, "details": {"空组件": 0, "小组件": 4, "中组件": 21, "大组件": 7, "问题组件": []}}, "缺失文件": {"status": true, "details": {"缺失关键文件": [], "空目录": [], "关键文件完整度": "5/5"}}}}