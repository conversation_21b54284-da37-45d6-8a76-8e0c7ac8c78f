{"validation_date": "2025-06-27", "overall_status": true, "total_tests": 6, "passed_tests": 6, "pass_rate": "100.0%", "detailed_results": {"XML桥接器": {"status": true, "details": {"桥接器初始化": true, "连接状态检查": true, "推荐引擎获取": true, "预加载器获取": true, "工具链管理器获取": true}, "connection_info": {"xml_system_connected": true, "ai_engines_loaded": 4, "biomedical_extensions_loaded": 4, "total_capabilities": {"recommendation_engine": true, "preloader": true, "tool_chain_manager": true, "ai_engines": ["connection", "intelligence", "caching", "loading"], "biomedical_extensions": ["recommendation_engine", "preloader", "cache_manager", "tool_chain_manager"]}}}, "AI引擎": {"status": true, "details": {"ai_engines.connection": {"导入成功": true, "模块内容": true, "模块属性数": 24}, "ai_engines.intelligence": {"导入成功": true, "模块内容": true, "模块属性数": 19}, "ai_engines.caching": {"导入成功": true, "模块内容": true, "模块属性数": 22}, "ai_engines.loading": {"导入成功": true, "模块内容": true, "模块属性数": 31}, "ai_engines.parsing": {"导入成功": true, "模块内容": true, "模块属性数": 20}, "ai_engines.perspectives": {"导入成功": true, "模块内容": true, "模块属性数": 20}}, "engines_count": 6}, "生物医学扩展": {"status": true, "details": {"biomedical_recommendation_engine": {"模块导入": true, "类获取": true, "实例创建": true, "方法数量": 4}, "biomedical_preloader": {"模块导入": true, "类获取": true, "实例创建": true, "方法数量": 2}, "biomedical_cache_manager": {"模块导入": true, "类获取": true, "实例创建": true, "方法数量": 3}, "tool_chain_manager": {"模块导入": true, "类获取": true, "实例创建": true, "方法数量": 9}}, "extensions_count": 4}, "核心模块": {"status": true, "details": {"core.parsing": {"目录存在": true, "文件数量": 5, "路径": "E:\\工作-华望\\工作区\\生物医药（晨）\\biomedical-mbse-platform\\backend\\core\\parsing"}, "core.services": {"目录存在": true, "文件数量": 4, "路径": "E:\\工作-华望\\工作区\\生物医药（晨）\\biomedical-mbse-platform\\backend\\core\\services"}, "core.adapters": {"目录存在": true, "文件数量": 6, "路径": "E:\\工作-华望\\工作区\\生物医药（晨）\\biomedical-mbse-platform\\backend\\core\\adapters"}, "core.models": {"目录存在": true, "文件数量": 5, "路径": "E:\\工作-华望\\工作区\\生物医药（晨）\\biomedical-mbse-platform\\backend\\core\\models"}, "core.utils": {"目录存在": true, "文件数量": 6, "路径": "E:\\工作-华望\\工作区\\生物医药（晨）\\biomedical-mbse-platform\\backend\\core\\utils"}}, "存在模块数": 5, "总模块数": 5}, "API服务": {"status": true, "details": {"FastAPI应用创建": true, "应用类型": true, "路由数量": 12}}, "前端组件": {"status": true, "details": {"Vue组件(生物医学)": {"目录存在": true, "Vue文件数": 4, "JS/TS文件数": 0, "总文件数": 4}, "Vue组件(迁移)": {"目录存在": true, "Vue文件数": 26, "JS/TS文件数": 1, "总文件数": 35}, "页面视图": {"目录存在": true, "Vue文件数": 0, "JS/TS文件数": 0, "总文件数": 1}, "状态管理": {"目录存在": true, "Vue文件数": 0, "JS/TS文件数": 0, "总文件数": 1}, "API服务": {"目录存在": true, "Vue文件数": 0, "JS/TS文件数": 0, "总文件数": 1}}, "存在组件数": 5, "总组件数": 5}}}