#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端组件详细验证测试
==================

专门验证Vue.js前端的完整性和可用性。

检查项目：
1. 前端配置文件
2. Vue组件完整性
3. 依赖包状态
4. 目录结构完整性
5. 组件代码质量
"""

import sys
import os
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Any

# 添加项目路径
project_root = Path(__file__).parent.parent
frontend_root = project_root / "frontend"

class FrontendValidator:
    """前端验证器"""
    
    def __init__(self):
        self.test_results = {}
        self.overall_status = True
        self.frontend_path = frontend_root
        
    def run_validation(self):
        """运行前端验证"""
        print("🎨 开始前端组件详细验证...")
        print("="*60)
        
        # 1. 检查前端配置
        self.test_frontend_config()
        
        # 2. 检查依赖包配置
        self.test_dependencies()
        
        # 3. 检查目录结构
        self.test_directory_structure()
        
        # 4. 检查Vue组件
        self.test_vue_components()
        
        # 5. 检查组件代码质量
        self.test_component_quality()
        
        # 6. 检查缺失的核心文件
        self.test_missing_files()
        
        # 生成验证报告
        self.generate_frontend_report()
        
        return self.overall_status
    
    def test_frontend_config(self):
        """测试前端配置文件"""
        print("\n📦 测试 前端配置文件...")
        
        config_files = {
            "package.json": "依赖配置",
            "vite.config.js": "构建配置"
        }
        
        config_results = {}
        
        for file_name, description in config_files.items():
            file_path = self.frontend_path / file_name
            
            if file_path.exists():
                try:
                    if file_name == "package.json":
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            config_results[file_name] = {
                                "存在": True,
                                "可解析": True,
                                "包名": data.get("name", ""),
                                "版本": data.get("version", ""),
                                "依赖数": len(data.get("dependencies", {})),
                                "开发依赖数": len(data.get("devDependencies", {}))
                            }
                    else:
                        config_results[file_name] = {
                            "存在": True,
                            "文件大小": file_path.stat().st_size
                        }
                    
                    print(f"  - {description}({file_name}): ✅ 通过")
                    
                except Exception as e:
                    config_results[file_name] = {
                        "存在": True,
                        "可解析": False,
                        "错误": str(e)
                    }
                    print(f"  - {description}({file_name}): ❌ 解析失败 - {e}")
            else:
                config_results[file_name] = {"存在": False}
                print(f"  - {description}({file_name}): ❌ 文件不存在")
                self.overall_status = False
        
        self.test_results["前端配置"] = {
            "status": all(result.get("存在", False) for result in config_results.values()),
            "details": config_results
        }
    
    def test_dependencies(self):
        """测试依赖包配置"""
        print("\n📚 测试 依赖包配置...")
        
        package_json = self.frontend_path / "package.json"
        
        if not package_json.exists():
            print("  ❌ package.json不存在")
            self.test_results["依赖包"] = {"status": False, "error": "package.json不存在"}
            self.overall_status = False
            return
        
        try:
            with open(package_json, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            dependencies = data.get("dependencies", {})
            dev_dependencies = data.get("devDependencies", {})
            
            # 检查关键依赖
            required_deps = {
                "vue": "Vue.js框架",
                "vue-router": "路由管理",
                "pinia": "状态管理",
                "element-plus": "UI组件库",
                "axios": "HTTP客户端"
            }
            
            dep_results = {}
            
            for dep, description in required_deps.items():
                if dep in dependencies:
                    dep_results[dep] = {
                        "存在": True,
                        "版本": dependencies[dep],
                        "描述": description
                    }
                    print(f"  - {description}({dep}): ✅ {dependencies[dep]}")
                else:
                    dep_results[dep] = {
                        "存在": False,
                        "描述": description
                    }
                    print(f"  - {description}({dep}): ❌ 缺失")
            
            # 统计信息
            total_deps = len(dependencies) + len(dev_dependencies)
            required_found = sum(1 for result in dep_results.values() if result["存在"])
            
            self.test_results["依赖包"] = {
                "status": required_found >= len(required_deps) * 0.8,  # 80%关键依赖存在
                "details": {
                    "关键依赖": dep_results,
                    "总依赖数": total_deps,
                    "生产依赖": len(dependencies),
                    "开发依赖": len(dev_dependencies),
                    "关键依赖完整度": f"{required_found}/{len(required_deps)}"
                }
            }
            
            print(f"  📊 依赖统计: 总计{total_deps}个包，关键依赖{required_found}/{len(required_deps)}")
            
        except Exception as e:
            print(f"  ❌ 依赖包检查失败: {e}")
            self.test_results["依赖包"] = {"status": False, "error": str(e)}
            self.overall_status = False
    
    def test_directory_structure(self):
        """测试目录结构"""
        print("\n📁 测试 前端目录结构...")
        
        required_dirs = {
            "src": "源代码目录",
            "src/components": "组件目录",
            "src/views": "页面目录", 
            "src/stores": "状态管理目录",
            "src/api": "API服务目录",
            "src/assets": "资源文件目录",
            "public": "静态资源目录"
        }
        
        dir_results = {}
        
        for dir_path, description in required_dirs.items():
            full_path = self.frontend_path / dir_path
            
            if full_path.exists() and full_path.is_dir():
                # 统计目录内容
                files = list(full_path.rglob("*"))
                vue_files = [f for f in files if f.suffix == '.vue']
                js_files = [f for f in files if f.suffix in ['.js', '.ts']]
                
                dir_results[dir_path] = {
                    "存在": True,
                    "总文件数": len(files),
                    "Vue文件数": len(vue_files),
                    "JS/TS文件数": len(js_files)
                }
                
                print(f"  - {description}: ✅ 存在 ({len(files)}个文件)")
                
            else:
                dir_results[dir_path] = {"存在": False}
                print(f"  - {description}: ❌ 缺失")
        
        dirs_exist = sum(1 for result in dir_results.values() if result.get("存在", False))
        
        self.test_results["目录结构"] = {
            "status": dirs_exist >= len(required_dirs) * 0.8,  # 80%目录存在
            "details": dir_results,
            "存在目录数": dirs_exist,
            "总目录数": len(required_dirs)
        }
    
    def test_vue_components(self):
        """测试Vue组件"""
        print("\n🧩 测试 Vue组件...")
        
        components_dir = self.frontend_path / "src/components"
        
        if not components_dir.exists():
            print("  ❌ 组件目录不存在")
            self.test_results["Vue组件"] = {"status": False, "error": "组件目录不存在"}
            self.overall_status = False
            return
        
        # 查找所有Vue组件
        vue_files = list(components_dir.rglob("*.vue"))
        
        component_results = {}
        
        for vue_file in vue_files:
            relative_path = vue_file.relative_to(components_dir)
            
            try:
                with open(vue_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 简单的内容检查
                has_template = '<template>' in content
                has_script = '<script>' in content or '<script setup' in content
                has_style = '<style>' in content
                
                size = vue_file.stat().st_size
                lines = content.count('\n') + 1
                
                component_results[str(relative_path)] = {
                    "文件大小": size,
                    "行数": lines,
                    "有模板": has_template,
                    "有脚本": has_script,
                    "有样式": has_style,
                    "完整性": has_template and has_script
                }
                
                status = "✅ 完整" if (has_template and has_script) else "⚠️ 不完整"
                print(f"  - {relative_path}: {status} ({lines}行)")
                
            except Exception as e:
                component_results[str(relative_path)] = {
                    "错误": str(e)
                }
                print(f"  - {relative_path}: ❌ 读取失败 - {e}")
        
        # 检查关键组件
        key_components = [
            "biomedical/BiomedicalDashboard.vue",
            "biomedical/ToolManager.vue",
            "biomedical/MolecularViewer.vue",
            "biomedical/DataStandardsViewer.vue"
        ]
        
        key_components_exist = 0
        for key_comp in key_components:
            if key_comp in component_results:
                key_components_exist += 1
        
        self.test_results["Vue组件"] = {
            "status": len(vue_files) > 0 and key_components_exist >= 3,
            "details": {
                "组件统计": component_results,
                "总组件数": len(vue_files),
                "关键组件数": f"{key_components_exist}/{len(key_components)}",
                "完整组件数": sum(1 for comp in component_results.values() 
                              if comp.get("完整性", False))
            }
        }
        
        print(f"  📊 组件统计: {len(vue_files)}个Vue组件，{key_components_exist}个关键组件")
    
    def test_component_quality(self):
        """测试组件代码质量"""
        print("\n⚡ 测试 组件代码质量...")
        
        vue_files = list((self.frontend_path / "src/components").rglob("*.vue"))
        
        quality_results = {
            "空组件": 0,
            "小组件": 0,  # <100行
            "中组件": 0,  # 100-500行
            "大组件": 0,  # >500行
            "问题组件": []
        }
        
        for vue_file in vue_files:
            try:
                with open(vue_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.count('\n') + 1
                size = len(content)
                
                # 分类组件大小
                if size < 50:  # 基本为空
                    quality_results["空组件"] += 1
                    quality_results["问题组件"].append({
                        "文件": str(vue_file.relative_to(self.frontend_path)),
                        "问题": "组件内容过少",
                        "大小": f"{size}字节"
                    })
                elif lines < 100:
                    quality_results["小组件"] += 1
                elif lines < 500:
                    quality_results["中组件"] += 1
                else:
                    quality_results["大组件"] += 1
                
            except Exception as e:
                quality_results["问题组件"].append({
                    "文件": str(vue_file.relative_to(self.frontend_path)),
                    "问题": f"读取失败: {e}"
                })
        
        total_components = len(vue_files)
        valid_components = total_components - quality_results["空组件"]
        
        print(f"  📊 质量分析:")
        print(f"    - 总组件: {total_components}")
        print(f"    - 有效组件: {valid_components}")
        print(f"    - 空组件: {quality_results['空组件']}")
        print(f"    - 问题组件: {len(quality_results['问题组件'])}")
        
        self.test_results["组件质量"] = {
            "status": quality_results["空组件"] < total_components * 0.5,  # 空组件少于50%
            "details": quality_results
        }
    
    def test_missing_files(self):
        """检查缺失的核心文件"""
        print("\n🔍 检查 缺失的核心文件...")
        
        missing_files = []
        
        # 检查关键文件
        critical_files = [
            "src/main.js",
            "src/App.vue", 
            "src/router/index.js",
            "src/stores/index.js",
            "public/index.html"
        ]
        
        for file_path in critical_files:
            full_path = self.frontend_path / file_path
            if not full_path.exists():
                missing_files.append(file_path)
                print(f"  ❌ 缺失关键文件: {file_path}")
        
        # 检查空目录
        empty_dirs = []
        important_dirs = [
            "src/views/biomedical",
            "src/stores/biomedical", 
            "src/api/biomedical"
        ]
        
        for dir_path in important_dirs:
            full_path = self.frontend_path / dir_path
            if full_path.exists() and full_path.is_dir():
                if not any(full_path.iterdir()):
                    empty_dirs.append(dir_path)
                    print(f"  ⚠️ 空目录: {dir_path}")
        
        if not missing_files and not empty_dirs:
            print("  ✅ 所有关键文件都存在")
        
        self.test_results["缺失文件"] = {
            "status": len(missing_files) == 0,
            "details": {
                "缺失关键文件": missing_files,
                "空目录": empty_dirs,
                "关键文件完整度": f"{len(critical_files)-len(missing_files)}/{len(critical_files)}"
            }
        }
    
    def generate_frontend_report(self):
        """生成前端验证报告"""
        print("\n" + "="*60)
        print("📊 前端组件验证报告")
        print("="*60)
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("status", False))
        
        print(f"总测试项目: {total_tests}")
        print(f"通过项目: {passed_tests}")
        print(f"失败项目: {total_tests - passed_tests}")
        print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result.get("status", False) else "❌"
            print(f"  {status_icon} {test_name}")
            
            if not result.get("status", False) and "error" in result:
                print(f"    错误: {result['error']}")
        
        # 前端特有的分析
        print("\n🎨 前端状态分析:")
        
        # Vue组件分析
        if "Vue组件" in self.test_results:
            vue_details = self.test_results["Vue组件"].get("details", {})
            total_components = vue_details.get("总组件数", 0)
            print(f"  - Vue组件总数: {total_components}")
            
        # 依赖包分析
        if "依赖包" in self.test_results:
            dep_details = self.test_results["依赖包"].get("details", {})
            if isinstance(dep_details, dict):
                total_deps = dep_details.get("总依赖数", 0)
                print(f"  - 依赖包总数: {total_deps}")
        
        # 建议
        print("\n💡 改进建议:")
        
        missing_details = self.test_results.get("缺失文件", {}).get("details", {})
        if missing_details.get("缺失关键文件"):
            print("  - 需要创建缺失的关键文件（如App.vue, main.js等）")
        
        if missing_details.get("空目录"):
            print("  - 需要完善空目录的内容")
        
        quality_details = self.test_results.get("组件质量", {}).get("details", {})
        if quality_details.get("空组件", 0) > 0:
            print(f"  - 需要完善{quality_details['空组件']}个空组件")
        
        # 总体状态
        if self.overall_status:
            print("\n🎉 前端验证结果: 基本可用!")
        else:
            print("\n⚠️ 前端验证结果: 需要完善!")
        
        # 保存报告
        report_file = self.frontend_path.parent / "frontend_validation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "validation_date": "2025-06-27",
                "overall_status": self.overall_status,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "pass_rate": f"{passed_tests/total_tests*100:.1f}%",
                "detailed_results": self.test_results
            }, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    print("🎨 Vue.js前端组件详细验证")
    print("目标: 检查前端的完整性和可用性")
    
    validator = FrontendValidator()
    success = validator.run_validation()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 