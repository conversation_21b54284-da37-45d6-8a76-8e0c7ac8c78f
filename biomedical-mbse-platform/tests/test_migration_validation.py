#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迁移功能100%验证测试
===================

验证从XML元数据系统迁移过来的所有核心功能是否正常工作。

测试覆盖：
1. XML元数据系统桥接器连接
2. 迁移的AI引擎功能
3. 生物医学专用扩展
4. 前端组件加载
5. API服务状态
"""

import sys
import os
import asyncio
import importlib
import traceback
from pathlib import Path
from typing import Dict, List, Any

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
sys.path.append(str(project_root / "backend"))

class MigrationValidator:
    """迁移功能验证器"""
    
    def __init__(self):
        self.test_results = {}
        self.overall_status = True
        
    def run_validation(self):
        """运行完整验证"""
        print("🔍 开始迁移功能100%验证...")
        print("="*60)
        
        # 1. 测试XML元数据系统桥接
        self.test_xml_bridge_connection()
        
        # 2. 测试迁移的AI引擎
        self.test_ai_engines()
        
        # 3. 测试生物医学扩展
        self.test_biomedical_extensions()
        
        # 4. 测试核心模块
        self.test_core_modules()
        
        # 5. 测试API服务
        self.test_api_service()
        
        # 6. 测试前端组件
        self.test_frontend_components()
        
        # 生成验证报告
        self.generate_validation_report()
        
        return self.overall_status
    
    def test_xml_bridge_connection(self):
        """测试XML元数据系统桥接器"""
        print("\n🌉 测试 XML元数据系统桥接...")
        
        try:
            from xml_metadata_integration.metadata_bridge import XMLMetadataBridge
            
            # 创建桥接器实例
            bridge = XMLMetadataBridge()
            
            # 测试连接状态
            connection_status = bridge.test_complete_integration()
            
            # 测试各个组件
            tests = {
                "桥接器初始化": bridge is not None,
                "连接状态检查": isinstance(connection_status, dict),
                "推荐引擎获取": bridge.get_biomedical_recommendation_engine() is not None,
                "预加载器获取": bridge.get_biomedical_preloader() is not None,
                "工具链管理器获取": bridge.get_tool_chain_manager() is not None
            }
            
            for test_name, result in tests.items():
                status = "✅ 通过" if result else "❌ 失败"
                print(f"  - {test_name}: {status}")
                
            self.test_results["XML桥接器"] = {
                "status": all(tests.values()),
                "details": tests,
                "connection_info": connection_status
            }
            
        except Exception as e:
            print(f"  ❌ XML桥接器测试失败: {e}")
            self.test_results["XML桥接器"] = {
                "status": False,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
            self.overall_status = False
    
    def test_ai_engines(self):
        """测试迁移的AI引擎"""
        print("\n🧠 测试 迁移的AI引擎...")
        
        ai_engines = [
            "ai_engines.connection",
            "ai_engines.intelligence", 
            "ai_engines.caching",
            "ai_engines.loading",
            "ai_engines.parsing",
            "ai_engines.perspectives"
        ]
        
        engine_results = {}
        
        for engine_name in ai_engines:
            try:
                # 尝试导入引擎模块
                module = importlib.import_module(engine_name)
                
                # 检查模块内容
                has_content = len(dir(module)) > 3  # 不只是__name__, __file__, __path__
                
                engine_results[engine_name] = {
                    "导入成功": True,
                    "模块内容": has_content,
                    "模块属性数": len(dir(module))
                }
                
                status = "✅ 通过" if has_content else "⚠️ 空模块"
                print(f"  - {engine_name}: {status}")
                
            except Exception as e:
                engine_results[engine_name] = {
                    "导入成功": False,
                    "错误": str(e)
                }
                print(f"  - {engine_name}: ❌ 失败 - {e}")
        
        all_engines_ok = all(
            result.get("导入成功", False) and result.get("模块内容", False)
            for result in engine_results.values()
        )
        
        self.test_results["AI引擎"] = {
            "status": all_engines_ok,
            "details": engine_results,
            "engines_count": len(ai_engines)
        }
        
        if not all_engines_ok:
            self.overall_status = False
    
    def test_biomedical_extensions(self):
        """测试生物医学专用扩展"""
        print("\n🧬 测试 生物医学专用扩展...")
        
        extensions = {
            "biomedical_recommendation_engine": "BiomedicalRecommendationEngine",
            "biomedical_preloader": "BiomedicalPreloader", 
            "biomedical_cache_manager": "BiomedicalCacheManager",
            "tool_chain_manager": "ToolChainManager"
        }
        
        extension_results = {}
        
        for module_name, class_name in extensions.items():
            try:
                # 导入模块
                module = importlib.import_module(f"biomedical_extensions.{module_name}")
                
                # 获取类
                extension_class = getattr(module, class_name)
                
                # 创建实例
                instance = extension_class()
                
                # 测试基本功能
                basic_tests = {
                    "模块导入": True,
                    "类获取": extension_class is not None,
                    "实例创建": instance is not None,
                    "方法数量": len([m for m in dir(instance) if not m.startswith('_')])
                }
                
                extension_results[module_name] = basic_tests
                
                method_count = basic_tests["方法数量"]
                status = "✅ 通过" if method_count > 0 else "⚠️ 无方法"
                print(f"  - {class_name}: {status} ({method_count}个方法)")
                
            except Exception as e:
                extension_results[module_name] = {
                    "模块导入": False,
                    "错误": str(e)
                }
                print(f"  - {class_name}: ❌ 失败 - {e}")
        
        all_extensions_ok = all(
            result.get("模块导入", False) and result.get("实例创建", False)
            for result in extension_results.values()
        )
        
        self.test_results["生物医学扩展"] = {
            "status": all_extensions_ok,
            "details": extension_results,
            "extensions_count": len(extensions)
        }
        
        if not all_extensions_ok:
            self.overall_status = False
    
    def test_core_modules(self):
        """测试迁移的核心模块"""
        print("\n⚙️ 测试 迁移的核心模块...")
        
        core_modules = [
            "core.parsing",
            "core.services",
            "core.adapters", 
            "core.models",
            "core.utils"
        ]
        
        core_results = {}
        
        for module_name in core_modules:
            try:
                # 检查模块目录是否存在
                module_path = project_root / "backend" / module_name.replace(".", "/")
                
                if module_path.exists():
                    # 计算模块文件数
                    py_files = list(module_path.glob("*.py"))
                    file_count = len([f for f in py_files if f.name != "__init__.py"])
                    
                    core_results[module_name] = {
                        "目录存在": True,
                        "文件数量": file_count,
                        "路径": str(module_path)
                    }
                    
                    status = "✅ 通过" if file_count > 0 else "⚠️ 空目录"
                    print(f"  - {module_name}: {status} ({file_count}个文件)")
                else:
                    core_results[module_name] = {
                        "目录存在": False,
                        "路径": str(module_path)
                    }
                    print(f"  - {module_name}: ❌ 目录不存在")
                    
            except Exception as e:
                core_results[module_name] = {
                    "目录存在": False,
                    "错误": str(e)
                }
                print(f"  - {module_name}: ❌ 错误 - {e}")
        
        modules_exist = sum(1 for result in core_results.values() if result.get("目录存在", False))
        
        self.test_results["核心模块"] = {
            "status": modules_exist >= len(core_modules) * 0.8,  # 80%存在就算通过
            "details": core_results,
            "存在模块数": modules_exist,
            "总模块数": len(core_modules)
        }
    
    def test_api_service(self):
        """测试API服务"""
        print("\n🌐 测试 API服务...")
        
        try:
            from api.main import app
            
            # 检查FastAPI应用
            api_tests = {
                "FastAPI应用创建": app is not None,
                "应用类型": str(type(app)).find("FastAPI") >= 0,
                "路由数量": len(app.routes) if hasattr(app, 'routes') else 0
            }
            
            for test_name, result in api_tests.items():
                if test_name == "路由数量":
                    status = f"✅ {result}个路由" if result > 0 else "⚠️ 无路由"
                else:
                    status = "✅ 通过" if result else "❌ 失败"
                print(f"  - {test_name}: {status}")
            
            self.test_results["API服务"] = {
                "status": api_tests["FastAPI应用创建"] and api_tests["应用类型"],
                "details": api_tests
            }
            
        except Exception as e:
            print(f"  ❌ API服务测试失败: {e}")
            self.test_results["API服务"] = {
                "status": False,
                "error": str(e)
            }
            self.overall_status = False
    
    def test_frontend_components(self):
        """测试前端组件"""
        print("\n🎨 测试 前端组件...")
        
        frontend_paths = {
            "Vue组件(生物医学)": project_root / "frontend/src/components/biomedical",
            "Vue组件(迁移)": project_root / "frontend/src/components/xml_legacy",
            "页面视图": project_root / "frontend/src/views",
            "状态管理": project_root / "frontend/src/stores",
            "API服务": project_root / "frontend/src/api"
        }
        
        frontend_results = {}
        
        for component_name, path in frontend_paths.items():
            try:
                if path.exists():
                    # 统计文件数
                    files = list(path.glob("**/*"))
                    vue_files = [f for f in files if f.suffix == '.vue']
                    js_files = [f for f in files if f.suffix in ['.js', '.ts']]
                    
                    frontend_results[component_name] = {
                        "目录存在": True,
                        "Vue文件数": len(vue_files),
                        "JS/TS文件数": len(js_files),
                        "总文件数": len(files)
                    }
                    
                    total_files = len(vue_files) + len(js_files)
                    status = "✅ 通过" if total_files > 0 else "⚠️ 空目录"
                    print(f"  - {component_name}: {status} ({total_files}个文件)")
                else:
                    frontend_results[component_name] = {
                        "目录存在": False,
                        "路径": str(path)
                    }
                    print(f"  - {component_name}: ❌ 目录不存在")
                    
            except Exception as e:
                frontend_results[component_name] = {
                    "目录存在": False,
                    "错误": str(e)
                }
                print(f"  - {component_name}: ❌ 错误 - {e}")
        
        components_exist = sum(1 for result in frontend_results.values() if result.get("目录存在", False))
        
        self.test_results["前端组件"] = {
            "status": components_exist >= len(frontend_paths) * 0.8,
            "details": frontend_results,
            "存在组件数": components_exist,
            "总组件数": len(frontend_paths)
        }
    
    def generate_validation_report(self):
        """生成验证报告"""
        print("\n" + "="*60)
        print("📊 迁移功能验证报告")
        print("="*60)
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("status", False))
        
        print(f"总测试项目: {total_tests}")
        print(f"通过项目: {passed_tests}")
        print(f"失败项目: {total_tests - passed_tests}")
        print(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result.get("status", False) else "❌"
            print(f"  {status_icon} {test_name}")
            
            if not result.get("status", False) and "error" in result:
                print(f"    错误: {result['error']}")
        
        # 总体状态
        if self.overall_status:
            print("\n🎉 验证结果: 迁移功能基本可用!")
        else:
            print("\n⚠️ 验证结果: 发现问题，需要修复!")
        
        # 保存报告到文件
        report_file = project_root / "migration_validation_report.json"
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "validation_date": "2025-06-27",
                "overall_status": self.overall_status,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "pass_rate": f"{passed_tests/total_tests*100:.1f}%",
                "detailed_results": self.test_results
            }, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    print("🔍 XML元数据系统迁移功能验证")
    print("目标: 确保迁移功能100%可用")
    
    validator = MigrationValidator()
    success = validator.run_validation()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 