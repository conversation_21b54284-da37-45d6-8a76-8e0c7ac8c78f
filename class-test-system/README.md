# MI2 Medical Immunology 2 考试系统

## 🧬 系统介绍

MI2考试系统是一个基于**MI2 Medical Immunology 2**课程内容的智能考试平台。系统根据课程大纲和教学内容，构建了全面的题库，能够自动生成不同类型和难度的考试，并提供详细的成绩分析和学习建议。

### 🎯 主要功能

- **智能出题**：基于MI2课程5大模块，智能生成涵盖全部知识点的考试
- **多样题型**：支持单选题、多选题、简答题、论述题、病例分析等5种题型
- **难度分级**：题目按基础、中等、高级三个难度级别分布
- **自动评分**：客观题自动评分，主观题提供参考答案和评分建议
- **详细分析**：提供成绩报告、知识点掌握情况和学习建议
- **Web界面**：直观友好的网页界面，支持在线考试和成绩查看

### 📚 课程模块覆盖

| 模块 | 内容 | 权重 |
|------|------|------|
| 🦠 微生物学 | 细菌学、病毒学、真菌学、寄生虫学 | 25% |
| 🛡️ 先天免疫 | 炎症反应、补体系统、细胞介导免疫 | 15% |
| 🎯 适应性免疫 | T细胞免疫、B细胞免疫、MHC系统、细胞因子 | 25% |
| 🔬 免疫病理学 | 超敏反应、自身免疫、免疫缺陷、肿瘤免疫 | 20% |
| 💊 临床免疫学 | 疫苗学、免疫治疗、免疫技术、免疫耐受 | 15% |

---

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Flask 2.0+
- 现代Web浏览器（Chrome, Firefox, Safari等）

### 安装步骤

1. **克隆或下载项目**
   ```bash
   # 如果是从git仓库
   git clone <repository-url>
   cd class-test-system
   
   # 或者直接进入项目目录
   cd class-test-system
   ```

2. **安装依赖**
   ```bash
   pip install flask
   ```

3. **启动系统**
   ```bash
   python app.py
   ```

4. **访问系统**
   - 打开浏览器访问：`http://localhost:5000`
   - 系统首页将显示功能介绍和导航

### 目录结构

```
class-test-system/
├── README.md                 # 系统说明文档
├── config.json              # 系统配置文件
├── app.py                   # Web应用主程序
├── exam_generator.py        # 考试生成器核心代码
├── question_banks/          # 题库目录
│   ├── microbiology.json   # 微生物学题库
│   ├── adaptive_immunity.json  # 适应性免疫题库
│   └── immune_pathology.json   # 免疫病理学题库
├── templates/              # HTML模板文件
│   ├── index.html         # 首页模板
│   └── start_exam.html    # 开始考试模板
├── exams/                 # 生成的考试文件（自动创建）
└── results/               # 考试结果文件（自动创建）
```

---

## 📖 使用指南

### 1. 开始考试

1. **访问首页**
   - 浏览器打开 `http://localhost:5000`
   - 查看系统功能介绍

2. **选择考试类型**
   - 点击"开始考试"按钮
   - 输入学生ID（如：student_001）
   - 选择考试类型：
     - 📝 **常规考试**：50道题，120分钟，涵盖所有模块
     - 📚 **期中考试**：30道题，90分钟，前半学期内容
     - 🎓 **期末考试**：80道题，180分钟，全学期综合考试

3. **开始答题**
   - 系统自动生成个性化考试
   - 按题目类型依次答题
   - 支持保存草稿和跳题

### 2. 题目类型说明

#### 📝 单选题 (Multiple Choice)
- **分值**：2分/题
- **示例**：细菌细胞壁的主要成分是什么？
- **答题**：从4个选项中选择1个正确答案

#### ☑️ 多选题 (Multiple Select)
- **分值**：3分/题
- **示例**：细菌的致病因子包括哪些？
- **答题**：从5个选项中选择2-4个正确答案

#### ✍️ 简答题 (Short Answer)
- **分值**：5分/题
- **示例**：简述病毒的复制周期主要步骤
- **答题**：文字回答，建议150字以内

#### 📋 论述题 (Essay)
- **分值**：15分/题
- **示例**：论述MHC系统在器官移植中的作用及其临床意义
- **答题**：详细论述，建议500字以内

#### 🏥 病例分析 (Case Analysis)
- **分值**：20分/题
- **示例**：基于给定临床病例，分析诊断和治疗方案
- **答题**：综合分析，建议800字以内

### 3. 成绩查看

1. **自动评分**
   - 客观题（单选、多选）：系统自动评分
   - 主观题（简答、论述、病例）：提供参考答案和评分建议

2. **成绩报告**
   - 总分和各题型得分
   - 正确率和答题时间
   - 知识点掌握情况分析
   - 个性化学习建议

3. **结果保存**
   - 所有考试结果自动保存
   - 支持历史成绩查询
   - 可导出详细报告

---

## ⚙️ 系统配置

### 配置文件 (config.json)

```json
{
  "course_info": {
    "course_code": "MI2",
    "course_name": "Medical Immunology 2",
    "semester": "Semester 2, 2024-2025"
  },
  "exam_settings": {
    "questions_per_exam": 50,
    "default_duration": 120,
    "passing_score": 60
  },
  "question_distribution": {
    "multiple_choice": 0.4,
    "multiple_select": 0.2,
    "short_answer": 0.25,
    "essay": 0.1,
    "case_analysis": 0.05
  }
}
```

### 题库扩展

1. **添加新题目**
   - 编辑对应模块的JSON文件
   - 按照现有格式添加题目
   - 包含中英文题目和答案

2. **新增模块**
   - 创建新的JSON题库文件
   - 在config.json中添加模块配置
   - 更新权重分配

---

## 🔧 技术架构

### 后端架构
- **Flask Web框架**：提供HTTP服务和路由
- **Python数据处理**：JSON格式题库和配置
- **智能算法**：基于权重的题目选择算法

### 前端界面
- **Bootstrap 5**：响应式UI框架
- **原生JavaScript**：交互逻辑和表单处理
- **CSS3动画**：提升用户体验

### 数据存储
- **JSON文件**：题库、配置、考试数据
- **文件系统**：考试结果和报告存储
- **Session管理**：考试状态和用户信息

---

## 📊 题库统计

### 当前题库规模
- **微生物学**：8道题（细菌学、病毒学、真菌学、寄生虫学）
- **适应性免疫**：10道题（MHC系统、T细胞、B细胞、细胞因子）
- **免疫病理学**：10道题（超敏反应、自身免疫、免疫缺陷、肿瘤免疫）

### 题型分布
- 单选题：60%
- 多选题：20%
- 简答题：15%
- 论述题：3%
- 病例分析：2%

### 难度分布
- 基础级：30%
- 中等级：50%
- 高级级：20%

---

## 🎯 学习建议

### 考试准备
1. **系统复习**：按照课程模块顺序复习
2. **重点掌握**：MHC系统、T/B细胞免疫、超敏反应
3. **实践应用**：结合临床案例理解免疫机制
4. **术语记忆**：掌握中英文专业术语对照

### 应试技巧
1. **时间管理**：合理分配各题型答题时间
2. **答题策略**：先完成客观题，再答主观题
3. **仔细审题**：注意题目中的关键信息
4. **完整作答**：主观题要点明确，逻辑清晰

---

## 🚨 注意事项

### 考试过程中
- ⚠️ 请勿刷新页面或关闭浏览器
- ⚠️ 确保网络连接稳定
- ⚠️ 建议在安静的环境中考试
- ⚠️ 合理安排时间，避免超时

### 系统维护
- 定期备份题库和配置文件
- 监控系统运行状态
- 及时更新题库内容
- 收集用户反馈优化系统

---

## 📞 技术支持

### 常见问题
1. **无法启动系统**：检查Python环境和Flask安装
2. **题库加载失败**：检查JSON文件格式和路径
3. **考试无法提交**：检查网络连接和浏览器设置
4. **成绩计算异常**：检查答题格式和评分逻辑

### 开发维护
- 系统基于开源技术构建
- 支持二次开发和功能扩展
- 可根据需求定制化修改
- 提供完整的代码文档

---

## 📄 版权信息

**MI2 Medical Immunology 2 考试系统**  
© 2024 基于MI2课程内容构建  
仅供教学和学习使用

---

## 🎉 开始使用

准备好开始您的MI2学习之旅了吗？

1. 确保系统已正确安装
2. 启动Web服务：`python app.py`
3. 访问：`http://localhost:5000`
4. 开始您的第一次考试！

祝您学习愉快，考试顺利！🌟 