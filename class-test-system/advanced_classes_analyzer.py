#!/usr/bin/env python3
"""
高级课件内容分析器
提供更深入的分析和可视化功能
"""

import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, Counter
import json

class AdvancedClassContentAnalyzer:
    def __init__(self, classes_dir: str):
        self.classes_dir = Path(classes_dir)
        self.analysis_results = {}
        
    def comprehensive_analysis(self) -> Dict:
        """进行全面的课件内容分析"""
        print("🔍 开始分析课件内容...")
        
        analysis = {
            'overview': self._generate_overview(),
            'course_taxonomy': self._analyze_course_taxonomy(),
            'learning_progression': self._analyze_learning_progression(),
            'content_themes': self._analyze_content_themes(),
            'temporal_analysis': self._analyze_temporal_patterns(),
            'file_metrics': self._analyze_file_metrics(),
            'curriculum_structure': self._analyze_curriculum_structure(),
            'recommendations': self._generate_recommendations()
        }
        
        return analysis
    
    def _generate_overview(self) -> Dict:
        """生成课程概览"""
        files = list(self.classes_dir.glob("*"))
        
        overview = {
            'total_files': len(files),
            'course_name': 'MI2 - 医学微生物学与免疫学',
            'analysis_date': datetime.now().isoformat(),
            'file_types': self._count_file_types(files),
            'total_size_mb': sum(f.stat().st_size for f in files if f.is_file()) / (1024 * 1024)
        }
        
        return overview
    
    def _count_file_types(self, files: List[Path]) -> Dict:
        """统计文件类型分布"""
        type_counts = defaultdict(int)
        for file in files:
            if file.is_file():
                type_counts[file.suffix.lower()] += 1
        return dict(type_counts)
    
    def _analyze_course_taxonomy(self) -> Dict:
        """分析课程分类学"""
        taxonomy = {
            'medical_microbiology': {
                'bacteriology': {
                    'keywords': ['bacterial', 'bacteria', 'pathogenesis', 'antimicrobial'],
                    'topics': [],
                    'description': '细菌学 - 细菌的结构、功能和致病机制'
                },
                'virology': {
                    'keywords': ['virus', 'viral', 'entry', 'replication', 'evolution'],
                    'topics': [],
                    'description': '病毒学 - 病毒的生物学特性和致病机制'
                },
                'mycology': {
                    'keywords': ['fungi', 'fungal'],
                    'topics': [],
                    'description': '真菌学 - 真菌的生物学和临床意义'
                }
            },
            'immunology': {
                'innate_immunity': {
                    'keywords': ['innate', 'dendritic', 'inflammation', 'complement'],
                    'topics': [],
                    'description': '先天性免疫 - 非特异性免疫反应'
                },
                'adaptive_immunity': {
                    'keywords': ['lymphocyte', 'antibody', 'antigen', 't cell', 'b cell'],
                    'topics': [],
                    'description': '适应性免疫 - 特异性免疫反应'
                },
                'immunopathology': {
                    'keywords': ['autoimmune', 'hypersensitivity', 'immunodeficiency', 'tumor'],
                    'topics': [],
                    'description': '免疫病理学 - 免疫系统相关疾病'
                },
                'clinical_immunology': {
                    'keywords': ['vaccination', 'immunotherapy', 'tolerance'],
                    'topics': [],
                    'description': '临床免疫学 - 免疫学的临床应用'
                }
            }
        }
        
        # 分析文件并分类
        files = list(self.classes_dir.glob("*"))
        for file in files:
            if file.is_file():
                filename_lower = file.name.lower()
                for major_category, subcategories in taxonomy.items():
                    for subcategory, data in subcategories.items():
                        for keyword in data['keywords']:
                            if keyword in filename_lower:
                                data['topics'].append({
                                    'filename': file.name,
                                    'extracted_topic': self._extract_topic_advanced(file.name),
                                    'lecture_info': self._extract_lecture_info(file.name)
                                })
                                break
        
        return taxonomy
    
    def _extract_topic_advanced(self, filename: str) -> str:
        """高级主题提取"""
        # 移除文件扩展名
        topic = re.sub(r'\.(pdf|pptx?|ppsx)$', '', filename, flags=re.IGNORECASE)
        
        # 移除各种前缀模式
        patterns_to_remove = [
            r'^MI2\s*[-\s]*',
            r'^L\d+\.?\d*\s*[-\s]*',
            r'^T\d+\.?\d*\s*[-\s]*',
            r'^\d{8}\s*',  # YYYYMMDD 格式日期
            r'2025\s*',    # 年份
            r'\(\d+\)',    # 括号中的数字
        ]
        
        for pattern in patterns_to_remove:
            topic = re.sub(pattern, '', topic, flags=re.IGNORECASE)
        
        return topic.strip()
    
    def _extract_lecture_info(self, filename: str) -> Dict:
        """提取讲座信息"""
        info = {
            'lecture_number': 'Unknown',
            'week': None,
            'session': None,
            'date': None
        }
        
        # 提取讲座编号 (L1.1, L2.3 等)
        lecture_match = re.search(r'L(\d+)\.?(\d*)', filename, re.IGNORECASE)
        if lecture_match:
            info['lecture_number'] = f"L{lecture_match.group(1)}"
            if lecture_match.group(2):
                info['session'] = int(lecture_match.group(2))
        
        # 提取周次信息 (W14L1)
        week_match = re.search(r'W(\d+)L(\d+)', filename, re.IGNORECASE)
        if week_match:
            info['week'] = int(week_match.group(1))
            info['session'] = int(week_match.group(2))
        
        # 提取日期
        date_match = re.search(r'(\d{8})', filename)
        if date_match:
            try:
                date_obj = datetime.strptime(date_match.group(1), '%Y%m%d')
                info['date'] = date_obj.strftime('%Y-%m-%d')
            except ValueError:
                pass
        
        return info
    
    def _analyze_learning_progression(self) -> Dict:
        """分析学习进程"""
        progression = {
            'foundational_topics': [],
            'intermediate_topics': [],
            'advanced_topics': [],
            'clinical_topics': []
        }
        
        # 定义难度级别关键词
        level_keywords = {
            'foundational': ['overview', 'introduction', 'basic', 'diversity', 'structure', 'challenge'],
            'intermediate': ['pathogenesis', 'development', 'activation', 'regulation', 'mediated'],
            'advanced': ['evolution', 'tolerance', 'immunodeficiency', 'tumor', 'oncogenic'],
            'clinical': ['therapy', 'vaccination', 'immunotherapy', 'treatment', 'clinical']
        }
        
        files = list(self.classes_dir.glob("*"))
        for file in files:
            if file.is_file():
                topic = self._extract_topic_advanced(file.name).lower()
                filename_lower = file.name.lower()
                
                for level, keywords in level_keywords.items():
                    if any(keyword in filename_lower or keyword in topic for keyword in keywords):
                        progression[f"{level}_topics"].append({
                            'filename': file.name,
                            'topic': topic,
                            'lecture_info': self._extract_lecture_info(file.name)
                        })
                        break
        
        return progression
    
    def _analyze_content_themes(self) -> Dict:
        """分析内容主题"""
        themes = {
            'host_pathogen_interactions': [],
            'immune_system_components': [],
            'disease_mechanisms': [],
            'therapeutic_approaches': [],
            'diagnostic_methods': []
        }
        
        theme_keywords = {
            'host_pathogen_interactions': ['infection', 'pathogenesis', 'entry', 'challenge', 'diversity'],
            'immune_system_components': ['lymphocyte', 'dendritic', 'antibody', 'complement', 'cytokines'],
            'disease_mechanisms': ['autoimmune', 'hypersensitivity', 'tumor', 'immunodeficiency', 'inflammation'],
            'therapeutic_approaches': ['therapy', 'vaccination', 'immunotherapy', 'antimicrobial', 'treatment'],
            'diagnostic_methods': ['tolerance', 'regulation', 'development', 'evolution', 'replication']
        }
        
        files = list(self.classes_dir.glob("*"))
        for file in files:
            if file.is_file():
                filename_lower = file.name.lower()
                
                for theme, keywords in theme_keywords.items():
                    if any(keyword in filename_lower for keyword in keywords):
                        themes[theme].append({
                            'filename': file.name,
                            'topic': self._extract_topic_advanced(file.name),
                            'relevance_score': sum(1 for kw in keywords if kw in filename_lower)
                        })
        
        # 按相关性排序
        for theme in themes:
            themes[theme].sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return themes
    
    def _analyze_temporal_patterns(self) -> Dict:
        """分析时间模式"""
        patterns = {
            'creation_timeline': [],
            'modification_timeline': [],
            'course_schedule': {},
            'seasonal_distribution': {}
        }
        
        files = list(self.classes_dir.glob("*"))
        for file in files:
            if file.is_file():
                stat = file.stat()
                modified_date = datetime.fromtimestamp(stat.st_mtime)
                
                patterns['modification_timeline'].append({
                    'filename': file.name,
                    'modified_date': modified_date.isoformat(),
                    'topic': self._extract_topic_advanced(file.name)
                })
                
                # 分析课程安排
                lecture_info = self._extract_lecture_info(file.name)
                if lecture_info['week']:
                    week = lecture_info['week']
                    if week not in patterns['course_schedule']:
                        patterns['course_schedule'][week] = []
                    patterns['course_schedule'][week].append({
                        'filename': file.name,
                        'session': lecture_info['session'],
                        'topic': self._extract_topic_advanced(file.name)
                    })
        
        # 排序时间线
        patterns['modification_timeline'].sort(key=lambda x: x['modified_date'])
        
        return patterns
    
    def _analyze_file_metrics(self) -> Dict:
        """分析文件指标"""
        metrics = {
            'size_distribution': {},
            'format_analysis': {},
            'naming_patterns': {},
            'content_density': {}
        }
        
        files = list(self.classes_dir.glob("*"))
        sizes = []
        formats = defaultdict(list)
        
        for file in files:
            if file.is_file():
                size_mb = file.stat().st_size / (1024 * 1024)
                sizes.append(size_mb)
                format_type = file.suffix.lower()
                formats[format_type].append({
                    'filename': file.name,
                    'size_mb': round(size_mb, 2),
                    'topic': self._extract_topic_advanced(file.name)
                })
        
        if sizes:
            metrics['size_distribution'] = {
                'average_size_mb': round(sum(sizes) / len(sizes), 2),
                'min_size_mb': round(min(sizes), 2),
                'max_size_mb': round(max(sizes), 2),
                'total_size_mb': round(sum(sizes), 2)
            }
        
        metrics['format_analysis'] = dict(formats)
        
        return metrics
    
    def _analyze_curriculum_structure(self) -> Dict:
        """分析课程结构"""
        structure = {
            'course_modules': {},
            'learning_objectives': [],
            'prerequisite_chain': [],
            'assessment_points': []
        }
        
        # 根据讲座编号分析模块
        files = list(self.classes_dir.glob("*"))
        modules = defaultdict(list)
        
        for file in files:
            if file.is_file():
                lecture_info = self._extract_lecture_info(file.name)
                lecture_num = lecture_info['lecture_number']
                
                if lecture_num != 'Unknown':
                    modules[lecture_num].append({
                        'filename': file.name,
                        'topic': self._extract_topic_advanced(file.name),
                        'session': lecture_info.get('session'),
                        'week': lecture_info.get('week')
                    })
        
        structure['course_modules'] = dict(modules)
        
        # 分析学习目标
        structure['learning_objectives'] = [
            "理解医学微生物学的基本概念和原理",
            "掌握人体免疫系统的结构和功能",
            "了解病原微生物的致病机制",
            "学习免疫反应的调节和病理",
            "掌握临床免疫学的应用"
        ]
        
        return structure
    
    def _generate_recommendations(self) -> Dict:
        """生成改进建议"""
        recommendations = {
            'content_organization': [],
            'learning_enhancement': [],
            'resource_optimization': [],
            'technology_integration': []
        }
        
        files = list(self.classes_dir.glob("*"))
        
        # 分析文件命名一致性
        naming_patterns = set()
        for file in files:
            if file.is_file():
                # 提取命名模式
                pattern = re.sub(r'\d+', 'X', file.name)
                pattern = re.sub(r'\.(pdf|pptx?|ppsx)$', '.EXT', pattern, flags=re.IGNORECASE)
                naming_patterns.add(pattern)
        
        if len(naming_patterns) > 5:
            recommendations['content_organization'].append(
                "建议统一文件命名规范，当前存在多种命名模式"
            )
        
        # 分析内容分布
        file_types = self._count_file_types(files)
        if '.pdf' in file_types and '.pptx' in file_types:
            recommendations['resource_optimization'].append(
                "考虑统一课件格式，或为不同格式制定明确的使用场景"
            )
        
        recommendations['learning_enhancement'].extend([
            "建议增加互动式学习内容和案例研究",
            "考虑添加自测题目和练习材料",
            "建议制作课程大纲和学习路径图"
        ])
        
        recommendations['technology_integration'].extend([
            "考虑开发在线学习平台集成",
            "建议添加多媒体内容和虚拟实验",
            "可以考虑移动学习应用的开发"
        ])
        
        return recommendations
    
    def generate_comprehensive_report(self, output_file: Optional[str] = None) -> str:
        """生成全面的分析报告"""
        analysis = self.comprehensive_analysis()
        
        report = f"""
# MI2 医学微生物学与免疫学 - 课件内容全面分析报告

## 📊 课程概览
- **课程名称**: {analysis['overview']['course_name']}
- **分析时间**: {analysis['overview']['analysis_date']}
- **文件总数**: {analysis['overview']['total_files']}
- **总容量**: {analysis['overview']['total_size_mb']:.2f} MB

## 📁 文件类型分布
"""
        
        for file_type, count in analysis['overview']['file_types'].items():
            report += f"- {file_type}: {count} 个文件\n"
        
        report += f"""
## 🔬 课程分类学分析

### 医学微生物学
"""
        
        microbiology = analysis['course_taxonomy']['medical_microbiology']
        for subcategory, data in microbiology.items():
            if data['topics']:
                report += f"""
#### {data['description']}
文件数量: {len(data['topics'])}
主要内容:
"""
                for topic in data['topics'][:3]:
                    report += f"- {topic['extracted_topic']}\n"
                if len(data['topics']) > 3:
                    report += f"- ... 还有 {len(data['topics']) - 3} 个相关文件\n"
        
        report += f"""
### 免疫学
"""
        
        immunology = analysis['course_taxonomy']['immunology']
        for subcategory, data in immunology.items():
            if data['topics']:
                report += f"""
#### {data['description']}
文件数量: {len(data['topics'])}
主要内容:
"""
                for topic in data['topics'][:3]:
                    report += f"- {topic['extracted_topic']}\n"
                if len(data['topics']) > 3:
                    report += f"- ... 还有 {len(data['topics']) - 3} 个相关文件\n"
        
        report += f"""
## 📚 学习进程分析
"""
        
        progression = analysis['learning_progression']
        levels = {
            'foundational_topics': '基础课程',
            'intermediate_topics': '中级课程', 
            'advanced_topics': '高级课程',
            'clinical_topics': '临床应用'
        }
        
        for level, topics in progression.items():
            if topics:
                report += f"""
### {levels[level]}
文件数量: {len(topics)}
"""
                for topic in topics[:3]:
                    report += f"- {topic['topic']}\n"
        
        report += f"""
## 🎯 内容主题分析
"""
        
        themes = analysis['content_themes']
        theme_names = {
            'host_pathogen_interactions': '宿主-病原体相互作用',
            'immune_system_components': '免疫系统组成',
            'disease_mechanisms': '疾病机制',
            'therapeutic_approaches': '治疗方法',
            'diagnostic_methods': '诊断方法'
        }
        
        for theme, topics in themes.items():
            if topics:
                report += f"""
### {theme_names[theme]}
相关文件: {len(topics)}
重点内容:
"""
                for topic in topics[:3]:
                    report += f"- {topic['topic']} (相关性: {topic['relevance_score']})\n"
        
        report += f"""
## 💡 改进建议

### 内容组织优化
"""
        for rec in analysis['recommendations']['content_organization']:
            report += f"- {rec}\n"
        
        report += f"""
### 学习体验提升
"""
        for rec in analysis['recommendations']['learning_enhancement']:
            report += f"- {rec}\n"
        
        report += f"""
### 资源优化
"""
        for rec in analysis['recommendations']['resource_optimization']:
            report += f"- {rec}\n"
        
        report += f"""
### 技术集成
"""
        for rec in analysis['recommendations']['technology_integration']:
            report += f"- {rec}\n"
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
        
        return report
    
    def save_detailed_analysis(self, output_file: str):
        """保存详细分析结果"""
        analysis = self.comprehensive_analysis()
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)

def main():
    print("🎓 MI2 医学微生物学与免疫学课件内容分析器")
    print("=" * 50)
    
    analyzer = AdvancedClassContentAnalyzer("/Users/<USER>/phc-mbse/phc-mbse/class-test-system/classes")
    
    # 生成全面报告
    print("📝 生成全面分析报告...")
    report = analyzer.generate_comprehensive_report(
        "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/comprehensive_analysis_report.md"
    )
    
    # 保存详细分析数据
    print("💾 保存详细分析数据...")
    analyzer.save_detailed_analysis(
        "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/detailed_analysis.json"
    )
    
    print("\n" + "=" * 50)
    print("✅ 分析完成!")
    print("📊 全面报告: comprehensive_analysis_report.md")
    print("📋 详细数据: detailed_analysis.json")
    print("=" * 50)
    
    # 显示简要摘要
    print("\n📈 分析摘要:")
    overview = analyzer._generate_overview()
    print(f"- 总文件数: {overview['total_files']}")
    print(f"- 总容量: {overview['total_size_mb']:.2f} MB")
    print(f"- 文件类型: {len(overview['file_types'])} 种")

if __name__ == "__main__":
    main()
