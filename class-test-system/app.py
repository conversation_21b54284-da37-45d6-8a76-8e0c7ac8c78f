#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MI2 考试系统 Web 应用
基于Flask的考试管理和答题界面
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, session
import json
import os
from datetime import datetime
from pathlib import Path
from exam_generator import ExamGenerator

app = Flask(__name__)
app.secret_key = 'mi2_exam_system_secret_key'

# 全局变量
exam_generator = None


def init_exam_generator():
    """初始化考试生成器"""
    global exam_generator
    try:
        exam_generator = ExamGenerator()
        return True
    except Exception as e:
        print(f"初始化考试生成器失败: {e}")
        return False


@app.route('/')
def index():
    """首页"""
    return render_template('index.html')


@app.route('/start_exam', methods=['GET', 'POST'])
def start_exam():
    """开始考试"""
    if request.method == 'POST':
        student_id = request.form.get('student_id')
        exam_type = request.form.get('exam_type', 'regular')
        
        if not student_id:
            return jsonify({'error': '请输入学生ID'}), 400
        
        try:
            # 生成考试
            exam_data = exam_generator.generate_exam(student_id, exam_type)
            
            # 保存到session
            session['exam_data'] = exam_data
            session['student_id'] = student_id
            session['start_time'] = datetime.now().isoformat()
            
            return redirect(url_for('exam_interface'))
            
        except Exception as e:
            return jsonify({'error': f'生成考试失败: {str(e)}'}), 500
    
    return render_template('start_exam.html')


@app.route('/exam')
def exam_interface():
    """考试界面"""
    if 'exam_data' not in session:
        return redirect(url_for('start_exam'))
    
    exam_data = session['exam_data']
    return render_template('exam.html', exam_data=exam_data)


@app.route('/submit_exam', methods=['POST'])
def submit_exam():
    """提交考试"""
    if 'exam_data' not in session:
        return jsonify({'error': '未找到考试数据'}), 400
    
    exam_data = session['exam_data']
    student_answers = request.json.get('answers', [])
    
    try:
        # 评估考试
        result = exam_generator.evaluate_exam(exam_data['exam_id'], student_answers)
        
        # 生成报告
        report = exam_generator.generate_report(result)
        
        # 清除session
        session.clear()
        
        return jsonify({
            'success': True,
            'score': result.score,
            'passing': result.passing,
            'report': report,
            'detailed_results': result.detailed_results
        })
        
    except Exception as e:
        return jsonify({'error': f'提交考试失败: {str(e)}'}), 500


@app.route('/results/<exam_id>')
def view_results(exam_id):
    """查看考试结果"""
    try:
        result_file = Path('results') / f'{exam_id}_result.json'
        if not result_file.exists():
            return "考试结果未找到", 404
        
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        return render_template('results.html', result=result_data)
        
    except Exception as e:
        return f"加载结果失败: {str(e)}", 500


@app.route('/admin')
def admin_panel():
    """管理员面板"""
    return render_template('admin.html')


@app.route('/api/stats')
def get_stats():
    """获取统计数据"""
    try:
        stats = {
            'total_questions': 0,
            'exams_taken': 0,
            'average_score': 0,
            'module_distribution': {}
        }
        
        # 统计题库
        for module, questions in exam_generator.question_banks.items():
            stats['total_questions'] += len(questions)
            stats['module_distribution'][module] = len(questions)
        
        # 统计考试
        exam_dir = Path('exams')
        if exam_dir.exists():
            stats['exams_taken'] = len(list(exam_dir.glob('*.json')))
        
        # 统计平均分
        result_dir = Path('results')
        if result_dir.exists():
            scores = []
            for result_file in result_dir.glob('*_result.json'):
                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        result_data = json.load(f)
                        scores.append(result_data.get('score', 0))
                except:
                    continue
            
            if scores:
                stats['average_score'] = sum(scores) / len(scores)
        
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


# 模板函数
@app.template_filter('datetime')
def datetime_filter(value):
    """格式化时间"""
    if isinstance(value, str):
        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    return value


if __name__ == '__main__':
    # 初始化考试生成器
    if init_exam_generator():
        print("考试系统启动成功！")
        print("访问 http://localhost:5000 开始使用")
        
        # 创建必要的目录
        os.makedirs('exams', exist_ok=True)
        os.makedirs('results', exist_ok=True)
        os.makedirs('templates', exist_ok=True)
        
        app.run(debug=True, port=5000)
    else:
        print("考试系统启动失败！请检查配置文件和题库。") 