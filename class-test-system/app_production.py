#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MI2 考试系统 Web 应用 - 生产环境版本
基于Flask的考试管理和答题界面
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, session
import json
import os
import logging
from datetime import datetime
from pathlib import Path
from exam_generator import ExamGenerator

# 创建Flask应用
app = Flask(__name__)

# 生产环境配置
class ProductionConfig:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'mi2-exam-production-secret-key-please-change-this'
    DEBUG = False
    TESTING = False
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = '/var/log/mi2-exam/app.log'
    
    # 性能配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 会话配置
    SESSION_COOKIE_SECURE = False  # 如果使用HTTPS则设为True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = 7200  # 2小时

app.config.from_object(ProductionConfig)

# 配置日志
if not app.debug:
    # 创建日志目录
    log_dir = Path('/var/log/mi2-exam')
    log_dir.mkdir(exist_ok=True)
    
    # 配置文件日志
    file_handler = logging.FileHandler('/var/log/mi2-exam/app.log')
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    
    # 配置控制台日志
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s'
    ))
    console_handler.setLevel(logging.INFO)
    app.logger.addHandler(console_handler)
    
    app.logger.setLevel(logging.INFO)
    app.logger.info('MI2 Exam System startup')

# 全局变量
exam_generator = None

def init_exam_generator():
    """初始化考试生成器"""
    global exam_generator
    try:
        exam_generator = ExamGenerator()
        app.logger.info('考试生成器初始化成功')
        return True
    except Exception as e:
        app.logger.error(f'初始化考试生成器失败: {e}')
        return False

@app.before_first_request
def initialize():
    """应用启动时初始化"""
    # 创建必要的目录
    for directory in ['exams', 'results', 'static/css', 'static/js']:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    # 初始化考试生成器
    if not init_exam_generator():
        app.logger.error('考试系统初始化失败')

@app.errorhandler(404)
def not_found_error(error):
    """404错误处理"""
    app.logger.warning(f'404错误: {request.url}')
    return render_template('error.html', 
                         error_code=404, 
                         error_message='页面未找到'), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    app.logger.error(f'500错误: {error}')
    return render_template('error.html', 
                         error_code=500, 
                         error_message='服务器内部错误'), 500

@app.route('/health')
def health_check():
    """健康检查端点"""
    try:
        # 检查考试生成器状态
        if exam_generator is None:
            raise Exception("考试生成器未初始化")
        
        # 检查题库
        total_questions = sum(len(questions) for questions in exam_generator.question_banks.values())
        if total_questions == 0:
            raise Exception("题库为空")
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0',
            'total_questions': total_questions
        })
    except Exception as e:
        app.logger.error(f'健康检查失败: {e}')
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500

@app.route('/')
def index():
    """首页"""
    app.logger.info(f'首页访问: {request.remote_addr}')
    return render_template('index.html')

@app.route('/start_exam', methods=['GET', 'POST'])
def start_exam():
    """开始考试"""
    if request.method == 'POST':
        student_id = request.form.get('student_id', '').strip()
        exam_type = request.form.get('exam_type', 'regular')
        
        if not student_id:
            app.logger.warning(f'考试开始失败: 学生ID为空 - {request.remote_addr}')
            return jsonify({'error': '请输入学生ID'}), 400
        
        try:
            app.logger.info(f'开始生成考试: 学生={student_id}, 类型={exam_type}')
            
            # 生成考试
            exam_data = exam_generator.generate_exam(student_id, exam_type)
            
            # 保存到session
            session['exam_data'] = exam_data
            session['student_id'] = student_id
            session['start_time'] = datetime.now().isoformat()
            session.permanent = True
            
            app.logger.info(f'考试生成成功: {exam_data["exam_id"]}')
            return redirect(url_for('exam_interface'))
            
        except Exception as e:
            app.logger.error(f'生成考试失败: {e} - 学生={student_id}')
            return jsonify({'error': f'生成考试失败: {str(e)}'}), 500
    
    return render_template('start_exam.html')

@app.route('/exam')
def exam_interface():
    """考试界面"""
    if 'exam_data' not in session:
        app.logger.warning(f'考试界面访问失败: 无考试数据 - {request.remote_addr}')
        return redirect(url_for('start_exam'))
    
    exam_data = session['exam_data']
    app.logger.info(f'考试界面访问: {exam_data["exam_id"]}')
    return render_template('exam.html', exam_data=exam_data)

@app.route('/submit_exam', methods=['POST'])
def submit_exam():
    """提交考试"""
    if 'exam_data' not in session:
        app.logger.warning(f'考试提交失败: 无考试数据 - {request.remote_addr}')
        return jsonify({'error': '未找到考试数据'}), 400
    
    exam_data = session['exam_data']
    student_answers = request.json.get('answers', [])
    
    try:
        app.logger.info(f'开始评估考试: {exam_data["exam_id"]}')
        
        # 评估考试
        result = exam_generator.evaluate_exam(exam_data['exam_id'], student_answers)
        
        # 生成报告
        report = exam_generator.generate_report(result)
        
        app.logger.info(f'考试评估完成: {exam_data["exam_id"]}, 分数: {result.score:.1f}')
        
        # 清除session
        session.clear()
        
        return jsonify({
            'success': True,
            'score': result.score,
            'passing': result.passing,
            'report': report,
            'detailed_results': result.detailed_results
        })
        
    except Exception as e:
        app.logger.error(f'提交考试失败: {e} - 考试ID: {exam_data.get("exam_id", "unknown")}')
        return jsonify({'error': f'提交考试失败: {str(e)}'}), 500

@app.route('/results/<exam_id>')
def view_results(exam_id):
    """查看考试结果"""
    try:
        result_file = Path('results') / f'{exam_id}_result.json'
        if not result_file.exists():
            app.logger.warning(f'结果文件未找到: {exam_id}')
            return "考试结果未找到", 404
        
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        app.logger.info(f'查看考试结果: {exam_id}')
        return render_template('results.html', result=result_data)
        
    except Exception as e:
        app.logger.error(f'加载结果失败: {e} - 考试ID: {exam_id}')
        return f"加载结果失败: {str(e)}", 500

@app.route('/admin')
def admin_panel():
    """管理员面板"""
    app.logger.info(f'管理员面板访问: {request.remote_addr}')
    return render_template('admin.html')

@app.route('/api/stats')
def get_stats():
    """获取统计数据"""
    try:
        stats = {
            'total_questions': 0,
            'exams_taken': 0,
            'average_score': 0,
            'module_distribution': {}
        }
        
        # 统计题库
        if exam_generator and exam_generator.question_banks:
            for module, questions in exam_generator.question_banks.items():
                stats['total_questions'] += len(questions)
                stats['module_distribution'][module] = len(questions)
        
        # 统计考试
        exam_dir = Path('exams')
        if exam_dir.exists():
            stats['exams_taken'] = len(list(exam_dir.glob('*.json')))
        
        # 统计平均分
        result_dir = Path('results')
        if result_dir.exists():
            scores = []
            for result_file in result_dir.glob('*_result.json'):
                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        result_data = json.load(f)
                        scores.append(result_data.get('score', 0))
                except:
                    continue
            
            if scores:
                stats['average_score'] = sum(scores) / len(scores)
        
        return jsonify(stats)
        
    except Exception as e:
        app.logger.error(f'获取统计数据失败: {e}')
        return jsonify({'error': str(e)}), 500

# 模板函数
@app.template_filter('datetime')
def datetime_filter(value):
    """格式化时间"""
    if isinstance(value, str):
        try:
            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return value
    return value

# 请求钩子
@app.before_request
def log_request():
    """记录请求"""
    if not request.path.startswith('/static') and request.path != '/health':
        app.logger.info(f'{request.method} {request.path} - {request.remote_addr}')

@app.after_request
def after_request(response):
    """请求后处理"""
    # 添加安全头
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    return response

if __name__ == '__main__':
    # 生产环境不应该直接运行Flask开发服务器
    # 使用Gunicorn启动：gunicorn -c gunicorn_config.py app_production:app
    app.logger.warning('警告: 在生产环境中请使用Gunicorn启动应用')
    app.run(host='0.0.0.0', port=5000, debug=False) 