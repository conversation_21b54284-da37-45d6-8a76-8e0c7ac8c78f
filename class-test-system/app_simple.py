#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MI2 考试系统 - 简化版本（本地测试用）
"""

from flask import Flask, render_template, request, redirect, url_for, session, jsonify
import json
import os
import uuid
from datetime import datetime
import random

app = Flask(__name__)
app.secret_key = 'mi2-exam-secret-key-2024'

# 创建必要目录
for directory in ['results', 'exams']:
    os.makedirs(directory, exist_ok=True)

# 题库配置
DEFAULT_CONFIG = {
    "exam_settings": {
        "time_limit": 120,
        "questions_per_exam": 8,
        "passing_score": 60
    },
    "question_banks": {
        "microbiology": {
            "name": "微生物学与感染免疫",
            "questions": [
                {
                    "id": "micro_001",
                    "type": "single_choice",
                    "question": "以下哪种细胞是先天免疫系统的重要组成部分？",
                    "options": ["T细胞", "B细胞", "巨噬细胞", "浆细胞"],
                    "correct_answer": [2],
                    "explanation": "巨噬细胞是先天免疫系统的重要组成部分，能够吞噬病原体并激活免疫反应。"
                },
                {
                    "id": "micro_002",
                    "type": "single_choice",
                    "question": "疫苗的主要作用机制是什么？",
                    "options": ["直接杀死病原体", "激活获得性免疫反应", "增强先天免疫", "抑制炎症反应"],
                    "correct_answer": [1],
                    "explanation": "疫苗通过激活获得性免疫反应，产生记忆细胞和抗体，提供长期保护。"
                },
                {
                    "id": "micro_003",
                    "type": "single_choice",
                    "question": "自身免疫病的主要特征是什么？",
                    "options": ["免疫系统攻击自身组织", "免疫系统功能低下", "病原体感染", "过敏反应"],
                    "correct_answer": [0],
                    "explanation": "自身免疫病是免疫系统错误地攻击自身正常组织和器官的疾病。"
                },
                {
                    "id": "micro_004",
                    "type": "single_choice",
                    "question": "1型糖尿病的发病机制主要涉及：",
                    "options": ["B细胞功能异常", "T细胞攻击胰岛β细胞", "NK细胞过度激活", "补体系统缺陷"],
                    "correct_answer": [1],
                    "explanation": "1型糖尿病是T细胞介导的自身免疫性疾病，主要攻击胰岛β细胞。"
                }
            ]
        },
        "adaptive_immunity": {
            "name": "适应性免疫与免疫应答",
            "questions": [
                {
                    "id": "adapt_001",
                    "type": "single_choice",
                    "question": "以下哪种细胞主要负责细胞免疫？",
                    "options": ["B细胞", "T细胞", "NK细胞", "肥大细胞"],
                    "correct_answer": [1],
                    "explanation": "T细胞主要负责细胞免疫，包括CD8+ T细胞的细胞毒性作用和CD4+ T细胞的辅助功能。"
                },
                {
                    "id": "adapt_002",
                    "type": "multiple_choice",
                    "question": "抗体的主要功能包括：",
                    "options": ["中和病原体", "激活补体", "促进吞噬作用", "直接杀死细胞"],
                    "correct_answer": [0, 1, 2],
                    "explanation": "抗体具有中和、激活补体、促进吞噬等多种功能，但不能直接杀死细胞。"
                },
                {
                    "id": "adapt_003",
                    "type": "single_choice",
                    "question": "免疫记忆的主要特点是什么？",
                    "options": ["反应更快更强", "持续时间短", "非特异性", "只存在于T细胞"],
                    "correct_answer": [0],
                    "explanation": "免疫记忆使机体在再次接触相同抗原时产生更快更强的免疫反应。"
                },
                {
                    "id": "adapt_004",
                    "type": "single_choice",
                    "question": "抗体类别转换主要发生在：",
                    "options": ["T细胞", "B细胞", "NK细胞", "巨噬细胞"],
                    "correct_answer": [1],
                    "explanation": "抗体类别转换是B细胞在激活过程中改变所产生抗体类型的过程。"
                }
            ]
        }
    }
}

@app.route('/')
def index():
    """主页"""
    return render_template('index_simple.html', config=DEFAULT_CONFIG)

@app.route('/start_exam')
def start_exam():
    """开始考试"""
    exam_id = str(uuid.uuid4())
    session['exam_id'] = exam_id
    session['start_time'] = datetime.now().isoformat()
    
    # 收集所有题目
    questions = []
    for bank_data in DEFAULT_CONFIG['question_banks'].values():
        questions.extend(bank_data['questions'])
    
    # 随机选择题目
    max_questions = DEFAULT_CONFIG['exam_settings']['questions_per_exam']
    if len(questions) > max_questions:
        questions = random.sample(questions, max_questions)
    
    session['questions'] = questions
    session['answers'] = {}
    
    return render_template('start_exam_simple.html', 
                         questions=questions,
                         config=DEFAULT_CONFIG,
                         exam_id=exam_id)

@app.route('/submit_exam', methods=['POST'])
def submit_exam():
    """提交考试"""
    if 'exam_id' not in session:
        return redirect(url_for('index'))
    
    # 获取答案
    answers = {}
    for key, value in request.form.items():
        if key.startswith('question_'):
            question_id = key.replace('question_', '')
            if question_id not in answers:
                answers[question_id] = []
            answers[question_id].append(value)
    
    # 计算分数
    questions = session.get('questions', [])
    total_score = 0
    max_score = len(questions) * 10
    
    for question in questions:
        question_id = question['id']
        if question_id in answers:
            user_answer = answers[question_id]
            correct_answer = question['correct_answer']
            
            if question['type'] == 'single_choice':
                if len(user_answer) == 1 and int(user_answer[0]) in correct_answer:
                    total_score += 10
            elif question['type'] == 'multiple_choice':
                user_indices = [int(x) for x in user_answer]
                if set(user_indices) == set(correct_answer):
                    total_score += 10
    
    # 保存结果
    result = {
        'exam_id': session['exam_id'],
        'start_time': session['start_time'],
        'end_time': datetime.now().isoformat(),
        'answers': answers,
        'score': total_score,
        'max_score': max_score,
        'percentage': round(total_score / max_score * 100, 2) if max_score > 0 else 0,
        'questions': questions
    }
    
    result_file = os.path.join('results', f"{session['exam_id']}.json")
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    # 清除会话
    session.clear()
    
    return jsonify({
        'success': True,
        'score': total_score,
        'max_score': max_score,
        'percentage': result['percentage'],
        'exam_id': result['exam_id']
    })

@app.route('/health')
def health_check():
    """健康检查"""
    return "MI2 考试系统运行正常", 200

@app.route('/admin')
def admin_panel():
    """简单的管理面板"""
    return "<h1>管理面板</h1><p>功能开发中...</p>"

@app.route('/api/stats')
def get_stats():
    """获取统计数据"""
    total_questions = 0
    for bank_data in DEFAULT_CONFIG['question_banks'].values():
        total_questions += len(bank_data['questions'])
    
    return jsonify({
        'total_questions': total_questions,
        'exams_taken': len(os.listdir('results')) if os.path.exists('results') else 0,
        'modules': list(DEFAULT_CONFIG['question_banks'].keys())
    })

@app.errorhandler(404)
def not_found(error):
    return "页面未找到", 404

@app.errorhandler(500)
def internal_error(error):
    return "服务器内部错误", 500

if __name__ == '__main__':
    print("🧬 MI2 考试系统启动中...")
    print("访问 http://localhost:5001 开始使用")
    print("健康检查: http://localhost:5001/health")
    print("按 Ctrl+C 停止服务")
    
    app.run(debug=True, host='0.0.0.0', port=5001) 