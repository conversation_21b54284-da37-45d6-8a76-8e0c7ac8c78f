#!/usr/bin/env python3
"""
课件内容分析器
用于解析classes目录下的医学和微生物学课件内容
"""

import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json

class ClassContentAnalyzer:
    def __init__(self, classes_dir: str):
        self.classes_dir = Path(classes_dir)
        self.course_categories = {
            'immunology': {
                'keywords': ['immune', 'immunity', 'antibod', 'antigen', 'lymphocyte', 'dendritic', 'cytokines', 'complement', 'vaccination', 'autoimmune', 'tolerance', 'hypersensitivity', 'immunodeficiency', 'immunotherapy'],
                'files': [],
                'description': '免疫学 - 研究人体免疫系统的结构和功能'
            },
            'bacteriology': {
                'keywords': ['bacterial', 'bacteria', 'antimicrobial', 'pathogenesis', 'respiratory', 'gastro', 'urogenital'],
                'files': [],
                'description': '细菌学 - 研究细菌的生物学特性、致病机制和防治'
            },
            'virology': {
                'keywords': ['virus', 'viral', 'diversity', 'biology', 'entry', 'genome', 'replication', 'pathogenesis', 'latent', 'oncogenic', 'evolution'],
                'files': [],
                'description': '病毒学 - 研究病毒的结构、复制和致病机制'
            },
            'infectious_diseases': {
                'keywords': ['infection', 'infectious', 'disease', 'microorganism', 'challenge', 'acute', 'persistent'],
                'files': [],
                'description': '感染性疾病 - 研究各种病原体引起的感染性疾病'
            },
            'mycology': {
                'keywords': ['fungi', 'fungal'],
                'files': [],
                'description': '真菌学 - 研究真菌的生物学特性和致病机制'
            },
            'general': {
                'keywords': [],
                'files': [],
                'description': '综合性医学课程内容'
            }
        }
        
    def analyze_files(self) -> Dict:
        """分析所有课件文件"""
        if not self.classes_dir.exists():
            return {"error": f"Directory {self.classes_dir} does not exist"}
            
        files = list(self.classes_dir.glob("*"))
        analysis_result = {
            'total_files': len(files),
            'analysis_date': datetime.now().isoformat(),
            'categories': {},
            'file_details': [],
            'course_structure': {},
            'weekly_schedule': {}
        }
        
        # 分析每个文件
        for file_path in files:
            if file_path.is_file():
                file_info = self._analyze_single_file(file_path)
                analysis_result['file_details'].append(file_info)
                
                # 按类别分类
                category = self._categorize_file(file_path.name)
                self.course_categories[category]['files'].append(file_info)
        
        # 整理分类结果
        for category, data in self.course_categories.items():
            if data['files']:
                analysis_result['categories'][category] = {
                    'description': data['description'],
                    'file_count': len(data['files']),
                    'files': data['files']
                }
        
        # 分析课程结构
        analysis_result['course_structure'] = self._analyze_course_structure()
        analysis_result['weekly_schedule'] = self._extract_weekly_schedule()
        
        return analysis_result
    
    def _analyze_single_file(self, file_path: Path) -> Dict:
        """分析单个文件的详细信息"""
        stat = file_path.stat()
        
        file_info = {
            'filename': file_path.name,
            'file_type': file_path.suffix.lower(),
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'modified_date': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'course_code': self._extract_course_code(file_path.name),
            'lecture_number': self._extract_lecture_number(file_path.name),
            'topic': self._extract_topic(file_path.name),
            'date_from_filename': self._extract_date_from_filename(file_path.name)
        }
        
        return file_info
    
    def _categorize_file(self, filename: str) -> str:
        """根据文件名关键词对文件进行分类"""
        filename_lower = filename.lower()
        
        for category, data in self.course_categories.items():
            if category == 'general':
                continue
            for keyword in data['keywords']:
                if keyword in filename_lower:
                    return category
        
        return 'general'
    
    def _extract_course_code(self, filename: str) -> str:
        """提取课程代码"""
        # 匹配 MI2, L1.1 等模式
        patterns = [
            r'(MI2)',
            r'(L\d+\.?\d*)',
            r'(T\d+\.?\d*)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "Unknown"
    
    def _extract_lecture_number(self, filename: str) -> str:
        """提取讲座编号"""
        # 匹配各种讲座编号模式
        patterns = [
            r'L(\d+\.?\d*)',
            r'W(\d+)L(\d+)',  # Week X Lecture Y
            r'T(\d+\.?\d*)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return "Unknown"
    
    def _extract_topic(self, filename: str) -> str:
        """提取主题"""
        # 移除文件扩展名和常见前缀
        topic = re.sub(r'\.(pdf|pptx?|ppsx)$', '', filename, flags=re.IGNORECASE)
        topic = re.sub(r'^(MI2\s*[-\s]*|L\d+\.?\d*\s*[-\s]*|T\d+\.?\d*\s*[-\s]*)', '', topic, flags=re.IGNORECASE)
        topic = re.sub(r'^\d{8}\s*', '', topic)  # 移除日期
        topic = re.sub(r'2025\s*', '', topic)    # 移除年份
        
        return topic.strip()
    
    def _extract_date_from_filename(self, filename: str) -> str:
        """从文件名中提取日期"""
        # 匹配 YYYYMMDD 格式
        date_match = re.search(r'(\d{8})', filename)
        if date_match:
            date_str = date_match.group(1)
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d')
                return date_obj.strftime('%Y-%m-%d')
            except ValueError:
                pass
        
        # 匹配 2025 年份
        if '2025' in filename:
            return '2025'
            
        return "Unknown"
    
    def _analyze_course_structure(self) -> Dict:
        """分析课程结构"""
        structure = {
            'by_lecture': {},
            'by_week': {},
            'by_topic': {}
        }
        
        for category_data in self.course_categories.values():
            for file_info in category_data['files']:
                lecture_num = file_info['lecture_number']
                topic = file_info['topic']
                
                # 按讲座分组
                if lecture_num not in structure['by_lecture']:
                    structure['by_lecture'][lecture_num] = []
                structure['by_lecture'][lecture_num].append(file_info)
                
                # 按主题分组
                if topic not in structure['by_topic']:
                    structure['by_topic'][topic] = []
                structure['by_topic'][topic].append(file_info)
        
        return structure
    
    def _extract_weekly_schedule(self) -> Dict:
        """提取周次安排"""
        weekly_schedule = {}
        
        for category_data in self.course_categories.values():
            for file_info in category_data['files']:
                filename = file_info['filename']
                
                # 匹配 W14L1 格式 (Week 14 Lecture 1)
                week_match = re.search(r'W(\d+)L(\d+)', filename, re.IGNORECASE)
                if week_match:
                    week_num = int(week_match.group(1))
                    lecture_num = int(week_match.group(2))
                    
                    if week_num not in weekly_schedule:
                        weekly_schedule[week_num] = {}
                    
                    weekly_schedule[week_num][lecture_num] = file_info
        
        return weekly_schedule
    
    def generate_report(self, output_file: Optional[str] = None) -> str:
        """生成分析报告"""
        analysis = self.analyze_files()
        
        report = f"""
# 课件内容分析报告
生成时间: {analysis['analysis_date']}
总文件数: {analysis['total_files']}

## 课程分类概览
"""
        
        for category, data in analysis['categories'].items():
            report += f"""
### {data['description']}
- 文件数量: {data['file_count']}
- 主要内容:
"""
            for file_info in data['files'][:5]:  # 显示前5个文件
                report += f"  - {file_info['filename']} ({file_info['topic']})\n"
            
            if len(data['files']) > 5:
                report += f"  - ... 还有 {len(data['files']) - 5} 个文件\n"
        
        report += f"""
## 课程结构分析

### 按讲座编号
"""
        for lecture, files in sorted(analysis['course_structure']['by_lecture'].items()):
            if lecture != "Unknown":
                report += f"- {lecture}: {len(files)} 个文件\n"
        
        if analysis['weekly_schedule']:
            report += f"""
### 周次安排
"""
            for week in sorted(analysis['weekly_schedule'].keys()):
                lectures = analysis['weekly_schedule'][week]
                report += f"- 第{week}周: {len(lectures)} 个讲座\n"
                for lecture_num in sorted(lectures.keys()):
                    file_info = lectures[lecture_num]
                    report += f"  - 讲座{lecture_num}: {file_info['topic']}\n"
        
        report += f"""
## 文件类型分布
"""
        file_types = {}
        for file_info in analysis['file_details']:
            file_type = file_info['file_type']
            if file_type not in file_types:
                file_types[file_type] = 0
            file_types[file_type] += 1
        
        for file_type, count in sorted(file_types.items()):
            report += f"- {file_type}: {count} 个文件\n"
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
        
        return report
    
    def save_analysis_json(self, output_file: str):
        """保存分析结果为JSON格式"""
        analysis = self.analyze_files()
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)

def main():
    # 分析课件内容
    analyzer = ClassContentAnalyzer("/Users/<USER>/phc-mbse/phc-mbse/class-test-system/classes")
    
    # 生成报告
    report = analyzer.generate_report("/Users/<USER>/phc-mbse/phc-mbse/class-test-system/classes_analysis_report.md")
    print(report)
    
    # 保存详细的JSON分析结果
    analyzer.save_analysis_json("/Users/<USER>/phc-mbse/phc-mbse/class-test-system/classes_analysis.json")
    
    print("\n✅ 分析完成!")
    print("📊 详细报告已保存到: classes_analysis_report.md")
    print("📋 JSON数据已保存到: classes_analysis.json")

if __name__ == "__main__":
    main()
