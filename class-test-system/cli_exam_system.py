#!/usr/bin/env python3
"""
命令行考试系统
提供交互式的命令行考试功能
"""

import json
import random
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict
from intelligent_exam_system import ExamGenerator, ExamManager, StudentAnswer

class CLIExamSystem:
    """命令行考试系统"""
    
    def __init__(self):
        self.knowledge_base_file = "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/knowledge_base.json"
        self.exam_generator = None
        self.exam_manager = None
        self.current_student = None
        self.initialize_system()
    
    def initialize_system(self):
        """初始化系统"""
        try:
            if not Path(self.knowledge_base_file).exists():
                raise FileNotFoundError("知识库文件不存在")
            
            self.exam_generator = ExamGenerator(self.knowledge_base_file)
            self.exam_manager = ExamManager("/Users/<USER>/phc-mbse/phc-mbse/class-test-system/exam_data")
            print("✅ 系统初始化成功")
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            raise
    
    def print_header(self):
        """打印系统头部信息"""
        print("\n" + "="*60)
        print("🎓 MI2 医学微生物学与免疫学 - 智能考试系统")
        print("="*60)
        print("📚 基于38个课件自动生成考试题目")
        print("🔬 涵盖微生物学、免疫学、临床医学")
        print("⚡ 智能评分和学习分析")
        print("="*60)
    
    def login(self):
        """学生登录"""
        print("\n📝 学生登录")
        print("-" * 30)
        
        while True:
            student_id = input("请输入学生ID (或输入 'quit' 退出): ").strip()
            
            if student_id.lower() == 'quit':
                return False
            
            if student_id:
                self.current_student = student_id
                print(f"✅ 欢迎，{student_id}！")
                return True
            else:
                print("❌ 请输入有效的学生ID")
    
    def show_main_menu(self):
        """显示主菜单"""
        while True:
            print(f"\n🏠 主菜单 - 当前用户: {self.current_student}")
            print("-" * 40)
            print("1. 📋 参加快速测验 (10题)")
            print("2. 📝 参加标准考试 (20题)")
            print("3. 🎯 参加高难度考试 (15题)")
            print("4. 🔧 自定义考试")
            print("5. 📊 查看历史成绩")
            print("6. 📈 查看考试统计")
            print("7. 🚪 退出登录")
            print("8. ❌ 退出系统")
            
            choice = input("\n请选择功能 (1-8): ").strip()
            
            if choice == '1':
                self.take_quick_quiz()
            elif choice == '2':
                self.take_standard_exam()
            elif choice == '3':
                self.take_advanced_exam()
            elif choice == '4':
                self.take_custom_exam()
            elif choice == '5':
                self.show_history()
            elif choice == '6':
                self.show_statistics()
            elif choice == '7':
                return True  # 重新登录
            elif choice == '8':
                return False  # 退出系统
            else:
                print("❌ 无效选择，请重试")
    
    def take_quick_quiz(self):
        """快速测验"""
        print("\n🚀 快速测验 - 10道题目，基础难度")
        print("-" * 40)
        
        questions = self.exam_generator.generate_questions(
            num_questions=10,
            difficulty_distribution={'basic': 0.8, 'intermediate': 0.2, 'advanced': 0.0},
            question_types={'multiple_choice': 0.6, 'true_false': 0.4, 'short_answer': 0.0}
        )
        
        exam_id = self.exam_manager.create_exam(
            title="快速测验",
            description="10道基础题目的快速测验",
            questions=questions,
            time_limit=15
        )
        
        self.conduct_exam(exam_id, questions)
    
    def take_standard_exam(self):
        """标准考试"""
        print("\n📝 标准考试 - 20道题目，综合难度")
        print("-" * 40)
        
        questions = self.exam_generator.generate_questions(
            num_questions=20,
            difficulty_distribution={'basic': 0.5, 'intermediate': 0.35, 'advanced': 0.15},
            question_types={'multiple_choice': 0.65, 'true_false': 0.25, 'short_answer': 0.1}
        )
        
        exam_id = self.exam_manager.create_exam(
            title="标准考试",
            description="20道题目的综合考试",
            questions=questions,
            time_limit=40
        )
        
        self.conduct_exam(exam_id, questions)
    
    def take_advanced_exam(self):
        """高难度考试"""
        print("\n🎯 高难度考试 - 15道题目，挑战难度")
        print("-" * 40)
        
        questions = self.exam_generator.generate_questions(
            num_questions=15,
            difficulty_distribution={'basic': 0.2, 'intermediate': 0.4, 'advanced': 0.4},
            question_types={'multiple_choice': 0.5, 'true_false': 0.2, 'short_answer': 0.2, 'essay': 0.1}
        )
        
        exam_id = self.exam_manager.create_exam(
            title="高难度考试",
            description="15道高难度题目的挑战考试",
            questions=questions,
            time_limit=45
        )
        
        self.conduct_exam(exam_id, questions)
    
    def take_custom_exam(self):
        """自定义考试"""
        print("\n🔧 自定义考试设置")
        print("-" * 30)
        
        try:
            # 获取用户配置
            num_questions = int(input("题目数量 (5-30): ") or "15")
            num_questions = max(5, min(30, num_questions))
            
            print("\n难度分布 (总和应为1.0):")
            basic_ratio = float(input("基础题比例 (0-1, 默认0.5): ") or "0.5")
            intermediate_ratio = float(input("中等题比例 (0-1, 默认0.3): ") or "0.3")
            advanced_ratio = 1.0 - basic_ratio - intermediate_ratio
            
            print(f"高级题比例: {advanced_ratio:.2f}")
            
            print("\n学科权重:")
            micro_weight = float(input("微生物学权重 (0-1, 默认0.5): ") or "0.5")
            immuno_weight = float(input("免疫学权重 (0-1, 默认0.4): ") or "0.4")
            general_weight = 1.0 - micro_weight - immuno_weight
            
            print(f"综合医学权重: {general_weight:.2f}")
            
            time_limit = int(input("考试时间限制 (分钟, 默认30): ") or "30")
            
            # 生成题目
            questions = self.exam_generator.generate_questions(
                num_questions=num_questions,
                difficulty_distribution={
                    'basic': basic_ratio,
                    'intermediate': intermediate_ratio,
                    'advanced': advanced_ratio
                },
                category_weights={
                    'medical_microbiology': micro_weight,
                    'immunology': immuno_weight,
                    'general_medicine': general_weight
                }
            )
            
            exam_id = self.exam_manager.create_exam(
                title="自定义考试",
                description=f"{num_questions}道题目的自定义考试",
                questions=questions,
                time_limit=time_limit
            )
            
            self.conduct_exam(exam_id, questions)
            
        except ValueError:
            print("❌ 输入格式错误，请重试")
        except Exception as e:
            print(f"❌ 创建考试失败: {e}")
    
    def conduct_exam(self, exam_id: str, questions: List):
        """进行考试"""
        print(f"\n📋 开始考试 - 共{len(questions)}道题目")
        print("-" * 40)
        
        input("按回车键开始考试...")
        
        start_time = time.time()
        student_answers = []
        
        for i, question in enumerate(questions):
            print(f"\n{'='*50}")
            print(f"第 {i+1}/{len(questions)} 题 ({question.points}分)")
            print(f"类别: {question.category} | 难度: {question.difficulty}")
            print(f"{'='*50}")
            print(f"\n{question.question_text}\n")
            
            question_start_time = time.time()
            
            if question.type == 'multiple_choice':
                for j, option in enumerate(question.options):
                    print(f"{chr(65+j)}. {option}")
                
                while True:
                    answer = input(f"\n请选择答案 (A-{chr(64+len(question.options))}): ").strip().upper()
                    if answer and answer in [chr(65+k) for k in range(len(question.options))]:
                        answer_index = ord(answer) - 65
                        student_answer = question.options[answer_index]
                        break
                    else:
                        print("❌ 无效选择，请重试")
            
            elif question.type == 'true_false':
                print("A. 正确")
                print("B. 错误")
                
                while True:
                    answer = input("\n请选择答案 (A/B): ").strip().upper()
                    if answer in ['A', 'B']:
                        student_answer = '正确' if answer == 'A' else '错误'
                        break
                    else:
                        print("❌ 无效选择，请重试")
            
            elif question.type in ['short_answer', 'essay']:
                student_answer = input("\n请输入答案: ").strip()
                if not student_answer:
                    student_answer = "[未回答]"
            
            question_time = int(time.time() - question_start_time)
            is_correct = student_answer == question.correct_answer
            points_earned = question.points if is_correct else 0
            
            student_answers.append(StudentAnswer(
                question_id=question.id,
                student_answer=student_answer,
                is_correct=is_correct,
                points_earned=points_earned,
                time_spent=question_time
            ))
            
            # 显示即时反馈
            if is_correct:
                print("✅ 回答正确！")
            else:
                print("❌ 回答错误")
                print(f"正确答案: {question.correct_answer}")
                if question.explanation:
                    print(f"解释: {question.explanation}")
        
        # 提交考试
        total_time = int(time.time() - start_time)
        result = self.exam_manager.submit_exam(exam_id, self.current_student, student_answers)
        
        # 显示结果
        self.show_exam_result(result, total_time)
    
    def show_exam_result(self, result, total_time):
        """显示考试结果"""
        print(f"\n{'='*60}")
        print("🎉 考试完成！")
        print(f"{'='*60}")
        
        print(f"📊 考试成绩:")
        print(f"   得分: {result.total_score}/{result.max_score}")
        print(f"   百分比: {result.percentage:.1f}%")
        print(f"   用时: {total_time//60}分{total_time%60}秒")
        
        # 等级评定
        if result.percentage >= 90:
            grade = "优秀 (A)"
            emoji = "🏆"
        elif result.percentage >= 80:
            grade = "良好 (B)"
            emoji = "🥇"
        elif result.percentage >= 70:
            grade = "中等 (C)"
            emoji = "🥈"
        elif result.percentage >= 60:
            grade = "及格 (D)"
            emoji = "🥉"
        else:
            grade = "不及格 (F)"
            emoji = "📚"
        
        print(f"   等级: {emoji} {grade}")
        
        # 表现分析
        if result.performance_analysis:
            analysis = result.performance_analysis
            
            print(f"\n📈 表现分析:")
            
            # 类别表现
            if 'category_performance' in analysis:
                print(f"   📚 学科表现:")
                for category, perf in analysis['category_performance'].items():
                    if perf['total'] > 0:
                        accuracy = perf['correct'] / perf['total'] * 100
                        category_name = {
                            'medical_microbiology': '医学微生物学',
                            'immunology': '免疫学',
                            'general_medicine': '综合医学'
                        }.get(category, category)
                        print(f"     - {category_name}: {accuracy:.1f}% ({perf['correct']}/{perf['total']})")
            
            # 难度表现
            if 'difficulty_performance' in analysis:
                print(f"   🎯 难度表现:")
                for difficulty, perf in analysis['difficulty_performance'].items():
                    if perf['total'] > 0:
                        accuracy = perf['correct'] / perf['total'] * 100
                        difficulty_name = {'basic': '基础', 'intermediate': '中等', 'advanced': '高级'}.get(difficulty, difficulty)
                        print(f"     - {difficulty_name}: {accuracy:.1f}% ({perf['correct']}/{perf['total']})")
            
            # 学习建议
            weak_areas = analysis.get('weak_areas', [])
            strong_areas = analysis.get('strong_areas', [])
            
            if weak_areas or strong_areas:
                print(f"\n💡 学习建议:")
                if strong_areas:
                    print(f"   ✅ 优势领域: {', '.join(strong_areas)}")
                if weak_areas:
                    print(f"   📖 需要加强: {', '.join(weak_areas)}")
                    print(f"   🎯 建议重点复习相关课件内容")
        
        print(f"\n{'='*60}")
        input("按回车键返回主菜单...")
    
    def show_history(self):
        """显示历史成绩"""
        print(f"\n📊 {self.current_student} 的历史成绩")
        print("-" * 40)
        
        student_results = []
        for exam_id, results in self.exam_manager.results.items():
            for result in results:
                if result.student_id == self.current_student:
                    exam = self.exam_manager.exams.get(exam_id)
                    student_results.append({
                        'exam_title': exam.title if exam else '未知考试',
                        'score': result.total_score,
                        'max_score': result.max_score,
                        'percentage': result.percentage,
                        'date': result.completed_at[:10],
                        'time': result.completed_at[11:19]
                    })
        
        if not student_results:
            print("📝 暂无考试记录")
        else:
            student_results.sort(key=lambda x: x['date'] + x['time'], reverse=True)
            
            print(f"共找到 {len(student_results)} 条记录:\n")
            for i, result in enumerate(student_results, 1):
                print(f"{i}. {result['exam_title']}")
                print(f"   得分: {result['score']}/{result['max_score']} ({result['percentage']:.1f}%)")
                print(f"   时间: {result['date']} {result['time']}")
                print()
        
        input("按回车键返回主菜单...")
    
    def show_statistics(self):
        """显示考试统计"""
        print("\n📈 考试系统统计信息")
        print("-" * 40)
        
        total_exams = len(self.exam_manager.exams)
        total_participants = sum(len(results) for results in self.exam_manager.results.values())
        
        print(f"📋 总考试数: {total_exams}")
        print(f"👥 总参与人次: {total_participants}")
        
        if total_participants > 0:
            all_scores = []
            for results in self.exam_manager.results.values():
                all_scores.extend([r.percentage for r in results])
            
            if all_scores:
                avg_score = sum(all_scores) / len(all_scores)
                max_score = max(all_scores)
                min_score = min(all_scores)
                
                print(f"📊 平均分: {avg_score:.1f}%")
                print(f"🏆 最高分: {max_score:.1f}%")
                print(f"📚 最低分: {min_score:.1f}%")
                
                # 分数分布
                score_ranges = {
                    '90-100': 0, '80-89': 0, '70-79': 0, '60-69': 0, '0-59': 0
                }
                
                for score in all_scores:
                    if score >= 90:
                        score_ranges['90-100'] += 1
                    elif score >= 80:
                        score_ranges['80-89'] += 1
                    elif score >= 70:
                        score_ranges['70-79'] += 1
                    elif score >= 60:
                        score_ranges['60-69'] += 1
                    else:
                        score_ranges['0-59'] += 1
                
                print(f"\n📈 分数分布:")
                for range_name, count in score_ranges.items():
                    percentage = count / len(all_scores) * 100
                    print(f"   {range_name}分: {count}人 ({percentage:.1f}%)")
        
        input("按回车键返回主菜单...")
    
    def run(self):
        """运行考试系统"""
        try:
            self.print_header()
            
            while True:
                if not self.login():
                    break
                
                if not self.show_main_menu():
                    break
            
            print("\n👋 感谢使用MI2智能考试系统！")
            print("📚 继续努力学习，祝您学业进步！")
            
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
        except Exception as e:
            print(f"\n❌ 系统错误: {e}")

def main():
    """主函数"""
    try:
        system = CLIExamSystem()
        system.run()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请确保已运行 document_content_parser.py 生成知识库")

if __name__ == "__main__":
    main()
