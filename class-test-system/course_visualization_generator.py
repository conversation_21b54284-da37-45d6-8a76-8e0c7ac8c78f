#!/usr/bin/env python3
"""
课程可视化生成器
创建课程结构的可视化图表
"""

import json
from pathlib import Path
import re

class CourseVisualizationGenerator:
    def __init__(self, analysis_file: str):
        with open(analysis_file, 'r', encoding='utf-8') as f:
            self.analysis_data = json.load(f)
    
    def generate_mermaid_diagram(self) -> str:
        """生成Mermaid格式的课程结构图"""
        
        diagram = """
---
title: MI2 医学微生物学与免疫学 - 课程结构图
---
graph TB
    classDef medicalMicro fill:#ffeeee,stroke:#ff0000,stroke-width:2px
    classDef immunology fill:#eeeeff,stroke:#0000ff,stroke-width:2px
    classDef foundation fill:#eeffee,stroke:#00aa00,stroke-width:2px
    classDef intermediate fill:#ffffee,stroke:#aaaa00,stroke-width:2px
    classDef advanced fill:#ffeeaa,stroke:#ff8800,stroke-width:2px
    classDef clinical fill:#eeaaff,stroke:#8800ff,stroke-width:2px
    
    MI2["MI2 医学微生物学与免疫学<br/>38个课件文件<br/>395.19 MB"]
    
    %% 主要分类
    MM["医学微生物学"]
    IM["免疫学"]
    
    MI2 --> MM
    MI2 --> IM
    
    %% 医学微生物学分支
    BAC["细菌学<br/>9个文件"]
    VIR["病毒学<br/>5个文件"]
    MYC["真菌学<br/>2个文件"]
    
    MM --> BAC
    MM --> VIR
    MM --> MYC
    
    %% 免疫学分支
    INN["先天性免疫<br/>4个文件"]
    ADA["适应性免疫<br/>4个文件"]
    PATH["免疫病理学<br/>3个文件"]
    CLIN["临床免疫学<br/>3个文件"]
    
    IM --> INN
    IM --> ADA
    IM --> PATH
    IM --> CLIN
    
    %% 学习层次
    FOUND["基础课程<br/>4个文件"]
    INTER["中级课程<br/>6个文件"]
    ADV["高级课程<br/>4个文件"]
    CLAP["临床应用<br/>3个文件"]
    
    MI2 -.-> FOUND
    MI2 -.-> INTER
    MI2 -.-> ADV
    MI2 -.-> CLAP
    
    class MM,BAC,VIR,MYC medicalMicro
    class IM,INN,ADA,PATH,CLIN immunology
    class FOUND foundation
    class INTER intermediate
    class ADV advanced
    class CLAP clinical
"""
        return diagram
    
    def generate_timeline_diagram(self) -> str:
        """生成课程时间线图"""
        
        # 提取讲座信息
        lectures = {}
        course_modules = self.analysis_data['curriculum_structure']['course_modules']
        
        for lecture_num, files in course_modules.items():
            if lecture_num != 'Unknown' and files:
                # 提取数字进行排序
                match = re.search(r'L(\d+)', lecture_num)
                if match:
                    order = int(match.group(1))
                    lectures[order] = {
                        'lecture_num': lecture_num,
                        'files': files,
                        'topics': [f['topic'] for f in files]
                    }
        
        timeline = """
---
title: MI2 课程时间线
---
timeline
    title MI2 医学微生物学与免疫学 - 课程进度
    
"""
        
        sorted_lectures = sorted(lectures.items())
        
        for order, data in sorted_lectures[:10]:  # 显示前10个讲座
            lecture_num = data['lecture_num']
            topics = data['topics']
            main_topic = topics[0] if topics else "未知主题"
            
            timeline += f"""    section {lecture_num}
        {main_topic[:30]}... : {len(data['files'])}个文件
"""
            if len(topics) > 1:
                timeline += f"        {topics[1][:30]}... : 相关内容\n"
        
        return timeline
    
    def generate_pie_chart(self) -> str:
        """生成内容分布饼状图"""
        
        # 统计各类内容的文件数量
        categories = self.analysis_data['course_taxonomy']
        
        pie_chart = """
---
title: 课程内容分布
---
pie title 课程内容分类分布
"""
        
        # 医学微生物学
        if 'medical_microbiology' in categories:
            micro = categories['medical_microbiology']
            for category, data in micro.items():
                count = len(data['topics'])
                if count > 0:
                    name = data['description'].split(' - ')[0]
                    pie_chart += f'    "{name}" : {count}\n'
        
        # 免疫学
        if 'immunology' in categories:
            immuno = categories['immunology']
            for category, data in immuno.items():
                count = len(data['topics'])
                if count > 0:
                    name = data['description'].split(' - ')[0]
                    pie_chart += f'    "{name}" : {count}\n'
        
        return pie_chart
    
    def generate_comprehensive_visualization(self) -> str:
        """生成综合可视化文档"""
        
        doc = f"""# MI2 医学微生物学与免疫学 - 课程可视化分析

## 课程结构总览

{self.generate_mermaid_diagram()}

## 课程内容分布

{self.generate_pie_chart()}

## 课程进度时间线

{self.generate_timeline_diagram()}

## 关键统计信息

### 文件类型分布
"""
        
        # 添加文件类型统计
        overview = self.analysis_data['overview']
        for file_type, count in overview['file_types'].items():
            doc += f"- **{file_type}**: {count} 个文件\n"
        
        doc += f"""
### 课程规模
- **总文件数**: {overview['total_files']} 个
- **总容量**: {overview['total_size_mb']:.2f} MB
- **平均文件大小**: {overview['total_size_mb']/overview['total_files']:.2f} MB

### 学习层次分布
"""
        
        # 添加学习层次统计
        progression = self.analysis_data['learning_progression']
        levels = {
            'foundational_topics': '基础课程',
            'intermediate_topics': '中级课程',
            'advanced_topics': '高级课程',
            'clinical_topics': '临床应用'
        }
        
        for level, name in levels.items():
            count = len(progression[level])
            doc += f"- **{name}**: {count} 个文件\n"
        
        doc += """
## 主要学习主题

### 微生物学核心内容
1. **细菌学**: 细菌结构、致病机制、感染治疗
2. **病毒学**: 病毒生物学、致病性、进化
3. **真菌学**: 真菌感染和临床意义

### 免疫学核心内容
1. **先天性免疫**: 非特异性防御机制
2. **适应性免疫**: T细胞、B细胞免疫应答
3. **免疫病理**: 自身免疫、过敏、免疫缺陷
4. **临床免疫**: 疫苗、免疫治疗

## 学习建议

### 学习路径推荐
1. **第一阶段**: 从基础课程开始，建立基本概念
2. **第二阶段**: 学习中级课程，深入理解机制
3. **第三阶段**: 掌握高级内容，理解复杂相互作用
4. **第四阶段**: 应用临床知识，连接理论与实践

### 重点关注领域
- 宿主-病原体相互作用机制
- 免疫系统的协调与调节
- 疾病发生发展过程
- 现代治疗方法与策略
"""
        
        return doc

def main():
    print("📊 生成课程可视化分析...")
    
    # 读取分析数据
    analysis_file = "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/detailed_analysis.json"
    
    if not Path(analysis_file).exists():
        print(f"❌ 找不到分析文件: {analysis_file}")
        print("请先运行 advanced_classes_analyzer.py")
        return
    
    generator = CourseVisualizationGenerator(analysis_file)
    
    # 生成可视化文档
    visualization_doc = generator.generate_comprehensive_visualization()
    
    # 保存文档
    output_file = "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/course_visualization.md"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(visualization_doc)
    
    print(f"✅ 课程可视化分析已生成: {output_file}")
    
    # 单独生成Mermaid图表文件
    mermaid_file = "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/course_structure.mmd"
    with open(mermaid_file, 'w', encoding='utf-8') as f:
        f.write(generator.generate_mermaid_diagram())
    
    print(f"📈 Mermaid图表已生成: {mermaid_file}")

if __name__ == "__main__":
    main()
