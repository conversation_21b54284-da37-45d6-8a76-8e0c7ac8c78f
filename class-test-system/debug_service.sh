#!/bin/bash

# MI2 考试系统服务诊断脚本
echo "🔍 MI2 考试系统服务诊断"
echo "======================"

DEPLOY_DIR="/opt/mi2-exam-system"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用 sudo 运行此脚本"
    exit 1
fi

echo "📁 1. 检查部署目录和文件..."
cd "$DEPLOY_DIR"
ls -la

echo ""
echo "📋 2. 检查关键文件是否存在..."
echo "检查 app.py: $([ -f app.py ] && echo '✅ 存在' || echo '❌ 缺失')"
echo "检查 config.json: $([ -f config.json ] && echo '✅ 存在' || echo '❌ 缺失')"
echo "检查 gunicorn_config.py: $([ -f gunicorn_config.py ] && echo '✅ 存在' || echo '❌ 缺失')"
echo "检查 templates 目录: $([ -d templates ] && echo '✅ 存在' || echo '❌ 缺失')"

echo ""
echo "🐍 3. 检查 Python 环境..."
echo "Python 版本: $(python3 --version)"
echo "pip 版本: $(pip3 --version)"

echo ""
echo "📦 4. 检查依赖安装..."
python3 -c "import flask; print('Flask 版本:', flask.__version__)" 2>/dev/null || echo "❌ Flask 未安装"
python3 -c "import gunicorn; print('Gunicorn 版本:', gunicorn.__version__)" 2>/dev/null || echo "❌ Gunicorn 未安装"

echo ""
echo "🚀 5. 手动测试应用启动..."
echo "测试 Python 应用语法..."
python3 -m py_compile app.py
if [ $? -eq 0 ]; then
    echo "✅ app.py 语法正确"
else
    echo "❌ app.py 语法错误"
fi

echo ""
echo "🔧 6. 测试 Gunicorn 配置..."
python3 -c "
import sys
sys.path.insert(0, '$DEPLOY_DIR')
try:
    import app
    print('✅ 应用模块加载成功')
    print('Flask 应用对象:', app.app)
except Exception as e:
    print('❌ 应用模块加载失败:', e)
"

echo ""
echo "🌐 7. 检查端口占用..."
echo "端口 5000 占用情况:"
netstat -tlnp | grep :5000 || echo "端口 5000 未被占用"

echo ""
echo "📊 8. 检查服务状态..."
systemctl status mi2-exam --no-pager

echo ""
echo "📋 9. 查看最新的错误日志..."
echo "应用日志 (最后 20 行):"
journalctl -u mi2-exam -n 20 --no-pager

echo ""
echo "🔍 10. 建议的修复步骤:"
echo "1. 如果依赖缺失，运行: pip3 install flask gunicorn"
echo "2. 如果应用语法错误，检查 app.py 文件"
echo "3. 如果端口被占用，停止占用进程或更改端口"
echo "4. 如果权限问题，运行: chown -R root:root $DEPLOY_DIR"
echo "5. 手动启动测试: cd $DEPLOY_DIR && python3 app.py"
echo ""
echo "运行完诊断后，可以使用 fix_gunicorn.sh 脚本进行自动修复" 