#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MI2 考试系统演示脚本
展示从知识点提取到考试生成的完整流程
"""

import json
import random
from pathlib import Path
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_section(title):
    """打印分节标题"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_subsection(title):
    """打印子节标题"""
    print(f"\n--- {title} ---")

def demo_knowledge_extraction():
    """演示知识点提取"""
    print_section("1. 知识点提取演示")
    
    try:
        from knowledge_extractor import KnowledgeExtractor
        
        print("正在初始化知识点提取器...")
        extractor = KnowledgeExtractor()
        
        print("正在分析classes目录下的课程文件...")
        report = extractor.analyze_all_files()
        
        print_subsection("分析结果摘要")
        print(f"📁 总文件数: {report['total_files']}")
        print(f"💡 总知识点数: {report['total_knowledge_points']}")
        print(f"❓ 总题目数: {report['total_questions']}")
        
        print_subsection("模块分布")
        for module_key, module_data in report['modules'].items():
            print(f"  📚 {module_data['name']}: {module_data['knowledge_points_count']} 个知识点, {module_data['questions_count']} 道题目")
        
        print_subsection("示例知识点")
        if extractor.knowledge_points:
            sample_kp = extractor.knowledge_points[0]
            print(f"  标题: {sample_kp.title}")
            print(f"  类别: {sample_kp.category}")
            print(f"  难度: {sample_kp.difficulty}")
            print(f"  关键词: {', '.join(sample_kp.keywords)}")
            print(f"  来源: {sample_kp.source_file}")
        
        print_subsection("示例题目")
        if extractor.exam_questions:
            sample_q = extractor.exam_questions[0]
            print(f"  题目: {sample_q.question}")
            print(f"  类型: {sample_q.type}")
            print(f"  难度: {sample_q.difficulty}")
            if sample_q.options:
                for i, option in enumerate(sample_q.options):
                    marker = "✓" if i == sample_q.correct_answer else " "
                    print(f"    {marker} {chr(65+i)}. {option}")
        
        # 保存结果
        saved_files = extractor.save_results()
        print_subsection("保存的文件")
        for desc, filepath in saved_files.items():
            print(f"  📄 {desc}: {filepath}")
        
        return True
        
    except Exception as e:
        logger.error(f"知识点提取演示失败: {e}")
        return False

def demo_enhanced_exam_system():
    """演示增强版考试系统"""
    print_section("2. 增强版考试系统演示")
    
    try:
        from enhanced_exam_system import EnhancedExamSystem
        
        print("正在初始化增强版考试系统...")
        exam_system = EnhancedExamSystem()
        
        print_subsection("系统状态")
        print(f"💡 知识点数量: {len(exam_system.knowledge_points)}")
        print(f"📚 题库模块数: {len(exam_system.question_banks)}")
        
        total_questions = sum(len(questions) for questions in exam_system.question_banks.values())
        print(f"❓ 总题目数: {total_questions}")
        
        print_subsection("题库分布")
        for module, questions in exam_system.question_banks.items():
            print(f"  📖 {module}: {len(questions)} 道题目")
        
        # 创建演示学生
        student_id = "demo_student_001"
        print_subsection(f"为学生 {student_id} 创建个性化考试")
        
        # 创建诊断性考试
        print("创建诊断性考试...")
        session_id = exam_system.create_personalized_exam(student_id, "diagnostic")
        session = exam_system.get_exam_session(session_id)
        
        if session:
            print(f"✅ 考试创建成功")
            print(f"  会话ID: {session_id}")
            print(f"  题目数量: {len(session.questions)}")
            print(f"  考试时长: {session.exam_config.duration_minutes} 分钟")
            print(f"  及格分数: {session.exam_config.passing_score}%")
            
            print_subsection("题目预览")
            for i, question in enumerate(session.questions[:3]):  # 只显示前3题
                print(f"  题目 {i+1}: {question.get('question', 'N/A')}")
                print(f"    类型: {question.get('type', 'unknown')}")
                print(f"    难度: {question.get('difficulty', 'unknown')}")
                print(f"    模块: {question.get('module', 'unknown')}")
                if question.get('options'):
                    for j, option in enumerate(question['options'][:2]):  # 只显示前2个选项
                        print(f"      {chr(65+j)}. {option}")
                    if len(question['options']) > 2:
                        print(f"      ... (还有 {len(question['options'])-2} 个选项)")
                print()
            
            # 模拟答题
            print_subsection("模拟答题过程")
            for i, question in enumerate(session.questions[:5]):  # 只答前5题
                # 生成随机答案
                if question.get("type") == "multiple_choice":
                    answer = random.randint(0, len(question.get('options', [])) - 1)
                elif question.get("type") == "multiple_select":
                    num_options = len(question.get('options', []))
                    answer = random.sample(range(num_options), random.randint(1, min(3, num_options)))
                else:
                    answer = "这是一个示例答案，展示主观题的回答。"
                
                time_spent = random.randint(30, 180)  # 30秒到3分钟
                success = exam_system.submit_answer(session_id, i, answer, time_spent)
                
                print(f"  题目 {i+1}: {question.get('type', 'unknown')} - {'✅' if success else '❌'}")
            
            # 完成考试
            print_subsection("完成考试并生成结果")
            results = exam_system.complete_exam(session_id)
            
            print("📊 考试结果:")
            print(f"  总分: {results['total_score']}/{results['max_score']}")
            print(f"  百分比: {results['percentage_score']}%")
            print(f"  是否通过: {'✅ 通过' if results['passing'] else '❌ 未通过'}")
            print(f"  正确率: {results['accuracy']}%")
            print(f"  答题时间: {results['total_time']} 秒")
            
            print_subsection("模块成绩分析")
            for module, module_data in results['module_scores'].items():
                if module_data['max_score'] > 0:
                    percentage = module_data['score'] / module_data['max_score'] * 100
                    print(f"  📖 {module}: {module_data['score']}/{module_data['max_score']} ({percentage:.1f}%)")
            
            # 显示学习进度
            print_subsection("学生学习进度分析")
            progress = exam_system.get_student_progress(student_id)
            
            print(f"  学生ID: {progress['student_id']}")
            print(f"  考试次数: {progress['total_exams']}")
            print(f"  平均分: {progress['average_score']}%")
            print(f"  改进趋势: {progress['improvement_trend']}")
            
            if progress['recommendations']:
                print("  📝 学习建议:")
                for rec in progress['recommendations']:
                    print(f"    • {rec}")
            
            # 生成学习计划
            if hasattr(exam_system, 'generate_study_plan'):
                print_subsection("个性化学习计划")
                study_plan = exam_system.generate_study_plan(student_id)
                
                if 'error' not in study_plan:
                    print(f"  计划周期: {study_plan['duration_weeks']} 周")
                    print("  📅 周学习目标:")
                    for goal in study_plan['weekly_goals'][:2]:  # 只显示前2周
                        print(f"    第{goal['week']}周: {goal['focus_area']}")
                        print(f"      推荐学习时间: {goal['recommended_study_time']}")
        
        return True
        
    except Exception as e:
        logger.error(f"增强版考试系统演示失败: {e}")
        return False

def demo_question_bank_analysis():
    """演示题库分析"""
    print_section("3. 题库质量分析")
    
    try:
        results_dir = Path("results")
        if not results_dir.exists():
            print("❌ 结果目录不存在，请先运行知识点提取")
            return False
        
        # 分析生成的题库
        question_files = list(results_dir.glob("question_bank_*.json"))
        
        print(f"📚 找到 {len(question_files)} 个题库文件")
        
        total_questions = 0
        difficulty_stats = {"basic": 0, "intermediate": 0, "advanced": 0}
        type_stats = {}
        
        for question_file in question_files:
            with open(question_file, 'r', encoding='utf-8') as f:
                bank_data = json.load(f)
            
            module_name = bank_data.get('name', 'Unknown')
            questions = bank_data.get('questions', [])
            
            print_subsection(f"{module_name} 题库分析")
            print(f"  题目数量: {len(questions)}")
            
            # 统计难度分布
            module_difficulty = {"basic": 0, "intermediate": 0, "advanced": 0}
            module_types = {}
            
            for q in questions:
                difficulty = q.get('difficulty', 'unknown')
                q_type = q.get('type', 'unknown')
                
                if difficulty in module_difficulty:
                    module_difficulty[difficulty] += 1
                    difficulty_stats[difficulty] += 1
                
                module_types[q_type] = module_types.get(q_type, 0) + 1
                type_stats[q_type] = type_stats.get(q_type, 0) + 1
            
            print("  难度分布:")
            for diff, count in module_difficulty.items():
                if count > 0:
                    percentage = count / len(questions) * 100
                    print(f"    {diff}: {count} ({percentage:.1f}%)")
            
            print("  题型分布:")
            for q_type, count in module_types.items():
                percentage = count / len(questions) * 100
                print(f"    {q_type}: {count} ({percentage:.1f}%)")
            
            total_questions += len(questions)
        
        print_subsection("总体统计")
        print(f"📊 总题目数: {total_questions}")
        print("🎯 总体难度分布:")
        for diff, count in difficulty_stats.items():
            if count > 0:
                percentage = count / total_questions * 100
                print(f"  {diff}: {count} ({percentage:.1f}%)")
        
        print("📝 总体题型分布:")
        for q_type, count in type_stats.items():
            percentage = count / total_questions * 100
            print(f"  {q_type}: {count} ({percentage:.1f}%)")
        
        return True
        
    except Exception as e:
        logger.error(f"题库分析失败: {e}")
        return False

def main():
    """主演示函数"""
    print_section("MI2 考试系统完整演示")
    print("🧬 Medical Immunology 2 - 智能考试系统")
    print("基于课程内容自动提取知识点，生成个性化考试")
    
    # 检查classes目录
    classes_dir = Path("classes")
    if not classes_dir.exists():
        print("❌ classes目录不存在，请确保课程文件已放置在正确位置")
        return
    
    course_files = list(classes_dir.glob("*.pdf")) + list(classes_dir.glob("*.pptx")) + list(classes_dir.glob("*.ppsx"))
    print(f"📁 找到 {len(course_files)} 个课程文件")
    
    # 运行演示
    success_count = 0
    
    # 1. 知识点提取演示
    if demo_knowledge_extraction():
        success_count += 1
    
    # 2. 增强版考试系统演示
    if demo_enhanced_exam_system():
        success_count += 1
    
    # 3. 题库分析演示
    if demo_question_bank_analysis():
        success_count += 1
    
    # 总结
    print_section("演示总结")
    print(f"✅ 成功完成 {success_count}/3 个演示模块")
    
    if success_count == 3:
        print("🎉 所有功能演示成功！")
        print("\n📋 系统功能总结:")
        print("  1. ✅ 自动从38个课程文件中提取知识点")
        print("  2. ✅ 生成多样化的考试题目")
        print("  3. ✅ 创建个性化考试")
        print("  4. ✅ 智能评分和分析")
        print("  5. ✅ 学习进度跟踪")
        print("  6. ✅ 个性化学习建议")
        
        print("\n🚀 下一步:")
        print("  • 运行 'python web_exam_app.py' 启动Web界面")
        print("  • 访问 http://localhost:5000 开始使用")
        print("  • 查看 results/ 目录中的详细分析结果")
    else:
        print("⚠️  部分功能演示失败，请检查错误信息")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    main()
