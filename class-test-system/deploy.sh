#!/bin/bash

# MI2 考试系统服务器部署脚本
# 适用于 CentOS/RHEL 系统

echo "🧬 MI2 Medical Immunology 2 考试系统 - 服务器部署"
echo "=================================================="
echo "服务器IP: ***********"
echo "部署目录: /opt/mi2-exam-system"
echo

# 设置部署变量
DEPLOY_DIR="/opt/mi2-exam-system"
SERVICE_NAME="mi2-exam"
PORT="5000"
NGINX_PORT="80"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

echo "🔧 1. 更新系统并安装基础软件..."
yum update -y
yum install -y python3 python3-pip git wget unzip nginx firewalld

echo "📁 2. 创建部署目录..."
mkdir -p $DEPLOY_DIR
cd $DEPLOY_DIR

echo "📦 3. 安装Python依赖..."
pip3 install --upgrade pip
pip3 install flask gunicorn

echo "🗂️ 4. 创建项目结构..."
mkdir -p templates question_banks exams results static/css static/js

echo "📝 5. 创建生产环境配置..."

# 创建Gunicorn配置文件
cat > gunicorn_config.py << 'EOF'
# Gunicorn配置文件
bind = "127.0.0.1:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
daemon = False
user = "root"
group = "root"
pidfile = "/var/run/mi2-exam.pid"
access_log = "/var/log/mi2-exam-access.log"
error_log = "/var/log/mi2-exam-error.log"
log_level = "info"
EOF

# 创建系统服务配置
cat > /etc/systemd/system/${SERVICE_NAME}.service << EOF
[Unit]
Description=MI2 Exam System
After=network.target

[Service]
Type=notify
User=root
Group=root
WorkingDirectory=${DEPLOY_DIR}
Environment=PATH=${DEPLOY_DIR}/venv/bin
ExecStart=/usr/bin/python3 -m gunicorn --config gunicorn_config.py app:app
ExecReload=/bin/kill -s HUP \$MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

echo "🌐 6. 配置Nginx反向代理..."
cat > /etc/nginx/conf.d/mi2-exam.conf << EOF
server {
    listen 80;
    server_name ***********;
    
    # 客户端最大请求大小
    client_max_body_size 50M;
    
    # 静态文件
    location /static/ {
        alias ${DEPLOY_DIR}/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # 主应用
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "OK";
        add_header Content-Type text/plain;
    }
}
EOF

echo "🔥 7. 配置防火墙..."
systemctl start firewalld
systemctl enable firewalld
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --permanent --add-service=ssh
firewall-cmd --reload

echo "📊 8. 创建日志目录..."
mkdir -p /var/log/mi2-exam
touch /var/log/mi2-exam-access.log
touch /var/log/mi2-exam-error.log

echo "🎯 9. 创建生产环境配置文件..."
cat > production_config.py << 'EOF'
# 生产环境配置
import os

class ProductionConfig:
    # 安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'mi2-exam-production-secret-key-change-this'
    
    # 数据库配置（如果需要）
    # DATABASE_URL = os.environ.get('DATABASE_URL')
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = '/var/log/mi2-exam/app.log'
    
    # 性能配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 会话配置
    SESSION_COOKIE_SECURE = False  # 如果使用HTTPS则设为True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 应用配置
    DEBUG = False
    TESTING = False
EOF

echo "🔧 10. 创建部署工具脚本..."
cat > manage.sh << 'EOF'
#!/bin/bash

SERVICE_NAME="mi2-exam"

case "$1" in
    start)
        echo "启动MI2考试系统..."
        systemctl start $SERVICE_NAME
        systemctl start nginx
        echo "服务已启动"
        ;;
    stop)
        echo "停止MI2考试系统..."
        systemctl stop $SERVICE_NAME
        echo "服务已停止"
        ;;
    restart)
        echo "重启MI2考试系统..."
        systemctl restart $SERVICE_NAME
        systemctl restart nginx
        echo "服务已重启"
        ;;
    status)
        echo "服务状态："
        systemctl status $SERVICE_NAME
        echo "Nginx状态："
        systemctl status nginx
        ;;
    logs)
        echo "查看应用日志："
        journalctl -u $SERVICE_NAME -f
        ;;
    nginx-logs)
        echo "查看Nginx日志："
        tail -f /var/log/nginx/access.log /var/log/nginx/error.log
        ;;
    update)
        echo "更新系统..."
        systemctl stop $SERVICE_NAME
        # 这里可以添加更新代码的逻辑
        systemctl start $SERVICE_NAME
        echo "更新完成"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|nginx-logs|update}"
        exit 1
        ;;
esac
EOF

chmod +x manage.sh

echo "✅ 11. 重新加载系统服务..."
systemctl daemon-reload
systemctl enable ${SERVICE_NAME}
systemctl enable nginx

echo ""
echo "🎉 部署脚本执行完成！"
echo ""
echo "📋 接下来的步骤："
echo "1. 将MI2考试系统文件复制到 ${DEPLOY_DIR}"
echo "2. 运行: cd ${DEPLOY_DIR} && ./manage.sh start"
echo "3. 访问: http://***********"
echo ""
echo "🔧 管理命令："
echo "- 启动服务: ./manage.sh start"
echo "- 停止服务: ./manage.sh stop"
echo "- 重启服务: ./manage.sh restart"
echo "- 查看状态: ./manage.sh status"
echo "- 查看日志: ./manage.sh logs"
echo ""
echo "📊 监控地址："
echo "- 应用访问: http://***********"
echo "- 健康检查: http://***********/health"
echo ""

exit 0 