#!/bin/bash

# MI2 考试系统直接部署脚本
echo "🧬 MI2 考试系统 - 直接部署修复"
echo "=============================="

# 设置变量
DEPLOY_DIR="/opt/mi2-exam-system"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用 sudo 运行此脚本"
    exit 1
fi

echo "📁 停止服务..."
systemctl stop mi2-exam 2>/dev/null || true

echo "📦 创建应用文件..."

# 创建主应用文件
cat > "$DEPLOY_DIR/app.py" << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MI2 Medical Immunology 2 考试系统
主应用文件
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
import json
import os
import uuid
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'mi2-exam-secret-key-change-this-in-production'

# 配置文件路径
CONFIG_FILE = 'config.json'
RESULTS_DIR = 'results'
EXAMS_DIR = 'exams'

# 创建必要目录
for directory in [RESULTS_DIR, EXAMS_DIR]:
    os.makedirs(directory, exist_ok=True)

# 默认配置
DEFAULT_CONFIG = {
    "exam_settings": {
        "time_limit": 120,
        "questions_per_exam": 10,
        "passing_score": 60,
        "allow_review": True,
        "show_results": True
    },
    "question_banks": {
        "microbiology": {
            "name": "微生物学与感染免疫",
            "description": "病原微生物识别、感染免疫机制、疫苗与免疫预防",
            "questions": [
                {
                    "id": "micro_001",
                    "type": "single_choice",
                    "question": "以下哪种细胞是先天免疫系统的重要组成部分？",
                    "options": [
                        "T细胞",
                        "B细胞",
                        "巨噬细胞",
                        "浆细胞"
                    ],
                    "correct_answer": [2],
                    "explanation": "巨噬细胞是先天免疫系统的重要组成部分，能够吞噬病原体并激活免疫反应。"
                },
                {
                    "id": "micro_002",
                    "type": "single_choice",
                    "question": "疫苗的主要作用机制是什么？",
                    "options": [
                        "直接杀死病原体",
                        "激活获得性免疫反应",
                        "增强先天免疫",
                        "抑制炎症反应"
                    ],
                    "correct_answer": [1],
                    "explanation": "疫苗通过激活获得性免疫反应，产生记忆细胞和抗体，提供长期保护。"
                }
            ]
        },
        "adaptive_immunity": {
            "name": "适应性免疫与免疫应答",
            "description": "T细胞和B细胞功能、抗体产生、免疫记忆",
            "questions": [
                {
                    "id": "adapt_001",
                    "type": "single_choice",
                    "question": "以下哪种细胞主要负责细胞免疫？",
                    "options": [
                        "B细胞",
                        "T细胞",
                        "NK细胞",
                        "肥大细胞"
                    ],
                    "correct_answer": [1],
                    "explanation": "T细胞主要负责细胞免疫，包括CD8+ T细胞的细胞毒性作用和CD4+ T细胞的辅助功能。"
                },
                {
                    "id": "adapt_002",
                    "type": "multiple_choice",
                    "question": "抗体的主要功能包括：",
                    "options": [
                        "中和病原体",
                        "激活补体",
                        "促进吞噬作用",
                        "直接杀死细胞"
                    ],
                    "correct_answer": [0, 1, 2],
                    "explanation": "抗体具有中和、激活补体、促进吞噬等多种功能，但不能直接杀死细胞。"
                }
            ]
        }
    }
}

def load_config():
    """加载配置文件"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning(f"配置文件 {CONFIG_FILE} 不存在，使用默认配置")
            return DEFAULT_CONFIG
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return DEFAULT_CONFIG

def save_config(config):
    """保存配置文件"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {e}")
        return False

@app.route('/')
def index():
    """主页"""
    try:
        config = load_config()
        return render_template('index.html', config=config)
    except Exception as e:
        logger.error(f"主页加载失败: {e}")
        return f"系统错误: {str(e)}", 500

@app.route('/start_exam')
def start_exam():
    """开始考试"""
    try:
        config = load_config()
        
        # 生成考试ID
        exam_id = str(uuid.uuid4())
        session['exam_id'] = exam_id
        session['start_time'] = datetime.now().isoformat()
        
        # 生成考试题目
        questions = []
        for bank_name, bank_data in config['question_banks'].items():
            questions.extend(bank_data['questions'])
        
        # 限制题目数量
        max_questions = config['exam_settings']['questions_per_exam']
        if len(questions) > max_questions:
            import random
            questions = random.sample(questions, max_questions)
        
        session['questions'] = questions
        session['current_question'] = 0
        session['answers'] = {}
        
        return render_template('start_exam.html', 
                             questions=questions,
                             config=config,
                             exam_id=exam_id)
    except Exception as e:
        logger.error(f"开始考试失败: {e}")
        return f"系统错误: {str(e)}", 500

@app.route('/submit_exam', methods=['POST'])
def submit_exam():
    """提交考试"""
    try:
        if 'exam_id' not in session:
            return redirect(url_for('index'))
        
        # 获取答案
        answers = {}
        for key, value in request.form.items():
            if key.startswith('question_'):
                question_id = key.replace('question_', '')
                if question_id not in answers:
                    answers[question_id] = []
                answers[question_id].append(value)
        
        # 计算分数
        questions = session.get('questions', [])
        total_score = 0
        max_score = len(questions) * 10  # 每题10分
        
        for question in questions:
            question_id = question['id']
            if question_id in answers:
                user_answer = answers[question_id]
                correct_answer = question['correct_answer']
                
                if question['type'] == 'single_choice':
                    if len(user_answer) == 1 and int(user_answer[0]) in correct_answer:
                        total_score += 10
                elif question['type'] == 'multiple_choice':
                    user_indices = [int(x) for x in user_answer]
                    if set(user_indices) == set(correct_answer):
                        total_score += 10
        
        # 保存结果
        result = {
            'exam_id': session['exam_id'],
            'start_time': session['start_time'],
            'end_time': datetime.now().isoformat(),
            'answers': answers,
            'score': total_score,
            'max_score': max_score,
            'percentage': round(total_score / max_score * 100, 2) if max_score > 0 else 0,
            'questions': questions
        }
        
        result_file = os.path.join(RESULTS_DIR, f"{session['exam_id']}.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 清除会话
        session.clear()
        
        return jsonify({
            'success': True,
            'score': total_score,
            'max_score': max_score,
            'percentage': result['percentage'],
            'exam_id': result['exam_id']
        })
        
    except Exception as e:
        logger.error(f"提交考试失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/health')
def health_check():
    """健康检查"""
    return "OK", 200

@app.errorhandler(404)
def not_found(error):
    return "页面未找到", 404

@app.errorhandler(500)
def internal_error(error):
    return "服务器内部错误", 500

if __name__ == '__main__':
    # 开发模式
    app.run(debug=True, host='0.0.0.0', port=5000)
else:
    # 生产模式
    app.run(debug=False, host='127.0.0.1', port=5000)
EOF

echo "📝 创建模板文件..."

# 创建主页模板
cat > "$DEPLOY_DIR/templates/index.html" << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MI2 医学免疫学考试系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        
        .exam-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .info-item {
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .start-btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .footer {
            margin-top: 30px;
            color: #888;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧬 MI2 医学免疫学考试系统</h1>
        <p class="subtitle">Medical Immunology 2 - 在线考试平台</p>
        
        <div class="exam-info">
            <div class="info-item">📝 考试时间: {{ config.exam_settings.time_limit }} 分钟</div>
            <div class="info-item">📊 题目数量: {{ config.exam_settings.questions_per_exam }} 道</div>
            <div class="info-item">✅ 及格分数: {{ config.exam_settings.passing_score }} 分</div>
        </div>
        
        <button class="start-btn" onclick="startExam()">开始考试</button>
        
        <div class="footer">
            <p>© 2024 MI2 考试系统 - 祝您考试顺利！</p>
        </div>
    </div>

    <script>
        function startExam() {
            if (confirm('确定要开始考试吗？考试开始后不能暂停。')) {
                window.location.href = '/start_exam';
            }
        }
    </script>
</body>
</html>
EOF

# 创建考试页面模板
cat > "$DEPLOY_DIR/templates/start_exam.html" << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MI2 考试 - 正在进行</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .exam-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .exam-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .exam-title {
            font-size: 1.8em;
            color: #333;
        }
        
        .timer {
            font-size: 1.5em;
            color: #e74c3c;
            font-weight: bold;
        }
        
        .question-container {
            margin-bottom: 30px;
        }
        
        .question {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .question-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 15px;
        }
        
        .options {
            list-style: none;
        }
        
        .option {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #eee;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            border-color: #4ECDC4;
            background: #f0f8ff;
        }
        
        .option.selected {
            border-color: #4ECDC4;
            background: #e8f4fd;
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
            display: block;
            margin: 30px auto 0;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #eee;
            border-radius: 3px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ECDC4, #44A08D);
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="exam-container">
        <div class="exam-header">
            <h1 class="exam-title">MI2 医学免疫学考试</h1>
            <div class="timer" id="timer">{{ config.exam_settings.time_limit }}:00</div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress" style="width: 0%"></div>
        </div>
        
        <form id="examForm" method="POST" action="/submit_exam">
            <div class="question-container">
                {% for question in questions %}
                <div class="question">
                    <h3 class="question-title">{{ loop.index }}. {{ question.question }}</h3>
                    <ul class="options">
                        {% for option in question.options %}
                        <li class="option" onclick="selectOption(this, '{{ question.id }}', {{ loop.index0 }}, '{{ question.type }}')">
                            <input type="{% if question.type == 'multiple_choice' %}checkbox{% else %}radio{% endif %}" 
                                   name="question_{{ question.id }}" 
                                   value="{{ loop.index0 }}" 
                                   style="display: none;">
                            {{ option }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                {% endfor %}
            </div>
            
            <button type="submit" class="submit-btn">提交考试</button>
        </form>
    </div>

    <script>
        let timeLeft = {{ config.exam_settings.time_limit }} * 60;
        let totalQuestions = {{ questions|length }};
        let answeredQuestions = 0;
        
        function updateTimer() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('timer').textContent = 
                minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
            
            if (timeLeft <= 0) {
                submitExam();
            }
            timeLeft--;
        }
        
        function selectOption(element, questionId, optionIndex, questionType) {
            const input = element.querySelector('input');
            
            if (questionType === 'single_choice') {
                // 单选题：清除同组其他选项的选中状态
                const siblings = element.parentNode.querySelectorAll('.option');
                siblings.forEach(sibling => {
                    sibling.classList.remove('selected');
                    sibling.querySelector('input').checked = false;
                });
                
                element.classList.add('selected');
                input.checked = true;
            } else {
                // 多选题：切换选中状态
                if (element.classList.contains('selected')) {
                    element.classList.remove('selected');
                    input.checked = false;
                } else {
                    element.classList.add('selected');
                    input.checked = true;
                }
            }
            
            updateProgress();
        }
        
        function updateProgress() {
            const checkedInputs = document.querySelectorAll('input[type="radio"]:checked, input[type="checkbox"]:checked');
            const questionIds = new Set();
            
            checkedInputs.forEach(input => {
                const questionId = input.name.replace('question_', '');
                questionIds.add(questionId);
            });
            
            answeredQuestions = questionIds.size;
            const progress = (answeredQuestions / totalQuestions) * 100;
            document.getElementById('progress').style.width = progress + '%';
        }
        
        function submitExam() {
            if (confirm('确定要提交考试吗？')) {
                document.getElementById('examForm').submit();
            }
        }
        
        // 启动计时器
        const timer = setInterval(updateTimer, 1000);
        
        // 表单提交处理
        document.getElementById('examForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            fetch('/submit_exam', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`考试完成！\n得分：${data.score}/${data.max_score}\n百分比：${data.percentage}%`);
                    window.location.href = '/';
                } else {
                    alert('提交失败：' + data.error);
                }
            })
            .catch(error => {
                alert('网络错误：' + error);
            });
        });
    </script>
</body>
</html>
EOF

echo "📄 创建配置文件..."

# 创建配置文件
cat > "$DEPLOY_DIR/config.json" << 'EOF'
{
  "exam_settings": {
    "time_limit": 120,
    "questions_per_exam": 10,
    "passing_score": 60,
    "allow_review": true,
    "show_results": true
  },
  "question_banks": {
    "microbiology": {
      "name": "微生物学与感染免疫",
      "description": "病原微生物识别、感染免疫机制、疫苗与免疫预防",
      "questions": [
        {
          "id": "micro_001",
          "type": "single_choice",
          "question": "以下哪种细胞是先天免疫系统的重要组成部分？",
          "options": [
            "T细胞",
            "B细胞",
            "巨噬细胞",
            "浆细胞"
          ],
          "correct_answer": [2],
          "explanation": "巨噬细胞是先天免疫系统的重要组成部分，能够吞噬病原体并激活免疫反应。"
        },
        {
          "id": "micro_002",
          "type": "single_choice",
          "question": "疫苗的主要作用机制是什么？",
          "options": [
            "直接杀死病原体",
            "激活获得性免疫反应",
            "增强先天免疫",
            "抑制炎症反应"
          ],
          "correct_answer": [1],
          "explanation": "疫苗通过激活获得性免疫反应，产生记忆细胞和抗体，提供长期保护。"
        },
        {
          "id": "micro_003",
          "type": "multiple_choice",
          "question": "先天免疫系统的特点包括：",
          "options": [
            "反应迅速",
            "具有记忆性",
            "非特异性识别",
            "能够学习和适应"
          ],
          "correct_answer": [0, 2],
          "explanation": "先天免疫系统反应迅速且非特异性，但不具有记忆性。"
        }
      ]
    },
    "adaptive_immunity": {
      "name": "适应性免疫与免疫应答",
      "description": "T细胞和B细胞功能、抗体产生、免疫记忆",
      "questions": [
        {
          "id": "adapt_001",
          "type": "single_choice",
          "question": "以下哪种细胞主要负责细胞免疫？",
          "options": [
            "B细胞",
            "T细胞",
            "NK细胞",
            "肥大细胞"
          ],
          "correct_answer": [1],
          "explanation": "T细胞主要负责细胞免疫，包括CD8+ T细胞的细胞毒性作用和CD4+ T细胞的辅助功能。"
        },
        {
          "id": "adapt_002",
          "type": "multiple_choice",
          "question": "抗体的主要功能包括：",
          "options": [
            "中和病原体",
            "激活补体",
            "促进吞噬作用",
            "直接杀死细胞"
          ],
          "correct_answer": [0, 1, 2],
          "explanation": "抗体具有中和、激活补体、促进吞噬等多种功能，但不能直接杀死细胞。"
        },
        {
          "id": "adapt_003",
          "type": "single_choice",
          "question": "免疫记忆的主要特点是什么？",
          "options": [
            "反应更快更强",
            "持续时间短",
            "非特异性",
            "只存在于T细胞"
          ],
          "correct_answer": [0],
          "explanation": "免疫记忆使机体在再次接触相同抗原时产生更快更强的免疫反应。"
        }
      ]
    }
  }
}
EOF

echo "🔧 设置权限..."
chown -R root:root "$DEPLOY_DIR"
chmod -R 755 "$DEPLOY_DIR"
chmod +x "$DEPLOY_DIR/manage.sh"

echo "🚀 启动服务..."
systemctl start nginx
systemctl start mi2-exam

echo "⏳ 等待服务启动..."
sleep 5

echo "📊 检查服务状态..."
systemctl status mi2-exam --no-pager
systemctl status nginx --no-pager

echo ""
echo "🎉 直接部署完成！"
echo "访问地址: http://1.94.49.155"
echo "健康检查: http://1.94.49.155/health"
echo ""
echo "如果仍有问题，请检查日志："
echo "- 应用日志: journalctl -u mi2-exam -f"
echo "- Nginx日志: tail -f /var/log/nginx/error.log"
echo "- Python错误: tail -f /var/log/mi2-exam-error.log" 