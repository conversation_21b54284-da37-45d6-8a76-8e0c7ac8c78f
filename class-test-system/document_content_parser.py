#!/usr/bin/env python3
"""
文档内容解析器
从PDF和PPT文件中提取知识点和关键概念
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Set, Optional
import json
from datetime import datetime
from collections import defaultdict

class DocumentContentParser:
    def __init__(self, classes_dir: str):
        self.classes_dir = Path(classes_dir)
        self.knowledge_points = defaultdict(list)
        self.concepts = defaultdict(set)
        self.definitions = defaultdict(list)
        self.key_terms = set()
        
    def extract_knowledge_points(self) -> Dict:
        """从所有文档中提取知识点"""
        print("🔍 开始解析文档内容...")
        
        results = {
            'extraction_date': datetime.now().isoformat(),
            'total_files_processed': 0,
            'knowledge_categories': {},
            'key_concepts': {},
            'definitions': {},
            'exam_topics': [],
            'difficulty_levels': {}
        }
        
        files = list(self.classes_dir.glob("*"))
        
        for file_path in files:
            if file_path.is_file() and self._is_supported_format(file_path):
                print(f"📄 处理文件: {file_path.name}")
                file_knowledge = self._extract_from_filename(file_path)
                self._categorize_knowledge(file_knowledge, file_path.name)
                results['total_files_processed'] += 1
        
        # 整理提取的知识点
        results['knowledge_categories'] = self._organize_knowledge_by_category()
        results['key_concepts'] = self._extract_key_concepts()
        results['definitions'] = self._generate_definitions()
        results['exam_topics'] = self._generate_exam_topics()
        results['difficulty_levels'] = self._assign_difficulty_levels()
        
        return results
    
    def _is_supported_format(self, file_path: Path) -> bool:
        """检查是否为支持的文件格式"""
        supported_formats = {'.pdf', '.ppt', '.pptx', '.ppsx'}
        return file_path.suffix.lower() in supported_formats
    
    def _extract_from_filename(self, file_path: Path) -> Dict:
        """从文件名中提取知识点信息"""
        filename = file_path.name
        
        # 基础信息提取
        knowledge = {
            'filename': filename,
            'topic': self._extract_main_topic(filename),
            'subtopics': self._extract_subtopics(filename),
            'keywords': self._extract_keywords(filename),
            'lecture_info': self._extract_lecture_info(filename),
            'estimated_content': self._estimate_content_based_on_topic(filename)
        }
        
        return knowledge
    
    def _extract_main_topic(self, filename: str) -> str:
        """提取主要主题"""
        # 清理文件名
        topic = re.sub(r'\.(pdf|pptx?|ppsx)$', '', filename, flags=re.IGNORECASE)
        topic = re.sub(r'^(MI2\s*[-\s]*|L\d+\.?\d*\s*[-\s]*|T\d+\.?\d*\s*[-\s]*)', '', topic, flags=re.IGNORECASE)
        topic = re.sub(r'^\d{8}\s*', '', topic)
        topic = re.sub(r'2025\s*', '', topic)
        topic = re.sub(r'\(\d+\)', '', topic)
        
        return topic.strip()
    
    def _extract_subtopics(self, filename: str) -> List[str]:
        """提取子主题"""
        main_topic = self._extract_main_topic(filename).lower()
        subtopics = []
        
        # 基于主题推断子主题
        topic_mappings = {
            'bacterial': ['bacterial_structure', 'bacterial_genetics', 'bacterial_metabolism', 'pathogenesis', 'antibiotic_resistance'],
            'virus': ['viral_structure', 'viral_replication', 'viral_pathogenesis', 'viral_evolution', 'antiviral_therapy'],
            'immune': ['innate_immunity', 'adaptive_immunity', 'antibody_response', 'cellular_immunity', 'immune_regulation'],
            'infection': ['host_pathogen_interaction', 'disease_mechanisms', 'clinical_manifestations', 'diagnosis', 'treatment'],
            'fungi': ['fungal_biology', 'fungal_pathogenesis', 'antifungal_therapy', 'opportunistic_infections'],
            'cytokines': ['cytokine_types', 'signaling_pathways', 'immune_regulation', 'inflammatory_response'],
            'antibody': ['antibody_structure', 'antibody_function', 'immunoglobulin_classes', 'antibody_production'],
            'lymphocyte': ['t_cells', 'b_cells', 'nk_cells', 'lymphocyte_development', 'activation'],
            'hypersensitivity': ['type_i_allergy', 'type_ii_cytotoxic', 'type_iii_immune_complex', 'type_iv_delayed'],
            'autoimmune': ['autoimmune_mechanisms', 'molecular_mimicry', 'autoantibodies', 'treatment_strategies'],
            'immunodeficiency': ['primary_immunodeficiency', 'secondary_immunodeficiency', 'hiv_aids', 'treatment'],
            'vaccination': ['vaccine_types', 'immune_memory', 'herd_immunity', 'vaccine_development'],
            'complement': ['complement_pathways', 'complement_functions', 'complement_deficiencies'],
            'inflammation': ['acute_inflammation', 'chronic_inflammation', 'inflammatory_mediators', 'resolution']
        }
        
        for key, topics in topic_mappings.items():
            if key in main_topic:
                subtopics.extend(topics)
                break
        
        return subtopics
    
    def _extract_keywords(self, filename: str) -> List[str]:
        """提取关键词"""
        filename_lower = filename.lower()
        
        # 医学微生物学关键词
        microbiology_keywords = [
            'bacteria', 'virus', 'fungi', 'pathogen', 'infection', 'antimicrobial',
            'pathogenesis', 'virulence', 'resistance', 'genetics', 'metabolism',
            'structure', 'replication', 'evolution', 'diversity', 'entry'
        ]
        
        # 免疫学关键词
        immunology_keywords = [
            'immune', 'immunity', 'antibody', 'antigen', 'lymphocyte', 'cytokine',
            'complement', 'inflammation', 'tolerance', 'autoimmune', 'allergy',
            'hypersensitivity', 'immunodeficiency', 'vaccination', 'memory',
            'innate', 'adaptive', 'cellular', 'humoral', 'regulation'
        ]
        
        # 临床关键词
        clinical_keywords = [
            'therapy', 'treatment', 'diagnosis', 'clinical', 'disease',
            'symptoms', 'prevention', 'prognosis', 'epidemiology'
        ]
        
        all_keywords = microbiology_keywords + immunology_keywords + clinical_keywords
        found_keywords = []
        
        for keyword in all_keywords:
            if keyword in filename_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _extract_lecture_info(self, filename: str) -> Dict:
        """提取讲座信息"""
        info: Dict = {
            'lecture_number': None,
            'week': None,
            'session': None,
            'sequence': None
        }
        
        # 提取讲座编号
        lecture_match = re.search(r'L(\d+)\.?(\d*)', filename, re.IGNORECASE)
        if lecture_match:
            info['lecture_number'] = int(lecture_match.group(1))
            if lecture_match.group(2):
                info['session'] = int(lecture_match.group(2))
        
        # 提取周次
        week_match = re.search(r'W(\d+)', filename, re.IGNORECASE)
        if week_match:
            info['week'] = int(week_match.group(1))
        
        return info
    
    def _estimate_content_based_on_topic(self, filename: str) -> Dict:
        """基于主题估计内容"""
        main_topic = self._extract_main_topic(filename).lower()
        
        content_templates = {
            'bacterial_diversity': {
                'concepts': ['gram_positive', 'gram_negative', 'cell_wall_structure', 'morphology', 'classification'],
                'learning_objectives': [
                    '理解细菌的分类和命名',
                    '掌握革兰氏染色的原理和意义',
                    '了解细菌细胞壁的结构差异',
                    '认识常见细菌的形态特征'
                ],
                'key_points': [
                    '细菌按细胞壁结构分为革兰氏阳性和阴性',
                    '革兰氏阳性菌细胞壁厚，含肽聚糖多',
                    '革兰氏阴性菌有外膜结构',
                    '细菌形态包括球菌、杆菌、螺旋菌'
                ]
            },
            'viral_pathogenesis': {
                'concepts': ['viral_entry', 'replication_cycle', 'host_response', 'viral_proteins', 'pathogenic_mechanisms'],
                'learning_objectives': [
                    '理解病毒感染的分子机制',
                    '掌握病毒复制周期各阶段',
                    '了解宿主对病毒感染的反应',
                    '认识病毒致病的主要途径'
                ],
                'key_points': [
                    '病毒需要宿主细胞才能复制',
                    '病毒感染包括吸附、侵入、复制、组装、释放',
                    '病毒蛋白质具有多种功能',
                    '宿主免疫反应是抗病毒的主要机制'
                ]
            },
            'immune_system_overview': {
                'concepts': ['innate_immunity', 'adaptive_immunity', 'immune_cells', 'immune_organs', 'immune_response'],
                'learning_objectives': [
                    '理解免疫系统的基本组成',
                    '掌握先天性和适应性免疫的特点',
                    '了解主要免疫细胞的功能',
                    '认识免疫器官的作用'
                ],
                'key_points': [
                    '免疫系统分为先天性和适应性免疫',
                    '先天性免疫反应快速但非特异性',
                    '适应性免疫具有特异性和记忆性',
                    '免疫细胞协调工作维护机体健康'
                ]
            }
        }
        
        # 匹配最相关的内容模板
        for template_key, template in content_templates.items():
            if any(keyword in main_topic for keyword in template_key.split('_')):
                return template
        
        # 默认内容结构
        return {
            'concepts': ['basic_concept', 'mechanism', 'clinical_relevance'],
            'learning_objectives': [f'理解{main_topic}的基本概念和原理'],
            'key_points': [f'{main_topic}是重要的医学概念']
        }
    
    def _categorize_knowledge(self, knowledge: Dict, filename: str):
        """将知识点分类存储"""
        topic = knowledge['topic']
        
        # 按学科分类
        if any(kw in filename.lower() for kw in ['bacterial', 'virus', 'fungi', 'pathogen']):
            category = 'medical_microbiology'
        elif any(kw in filename.lower() for kw in ['immune', 'antibody', 'lymphocyte', 'cytokine']):
            category = 'immunology'
        else:
            category = 'general_medicine'
        
        self.knowledge_points[category].append(knowledge)
        
        # 收集概念和术语
        for concept in knowledge.get('estimated_content', {}).get('concepts', []):
            self.concepts[category].add(concept)
        
        for keyword in knowledge.get('keywords', []):
            self.key_terms.add(keyword)
    
    def _organize_knowledge_by_category(self) -> Dict:
        """按类别组织知识点"""
        organized = {}
        
        for category, knowledge_list in self.knowledge_points.items():
            organized[category] = {
                'description': self._get_category_description(category),
                'file_count': len(knowledge_list),
                'topics': [kp['topic'] for kp in knowledge_list],
                'concepts': list(self.concepts[category]),
                'knowledge_points': knowledge_list
            }
        
        return organized
    
    def _get_category_description(self, category: str) -> str:
        """获取类别描述"""
        descriptions = {
            'medical_microbiology': '医学微生物学 - 研究病原微生物的生物学特性和致病机制',
            'immunology': '免疫学 - 研究人体免疫系统的结构、功能和调节机制',
            'general_medicine': '综合医学 - 跨学科的医学概念和临床应用'
        }
        return descriptions.get(category, category)
    
    def _extract_key_concepts(self) -> Dict:
        """提取关键概念"""
        key_concepts = {
            'microbiology_concepts': {
                'pathogen_types': ['bacteria', 'viruses', 'fungi', 'parasites'],
                'infection_mechanisms': ['adhesion', 'invasion', 'toxin_production', 'immune_evasion'],
                'antimicrobial_concepts': ['antibiotics', 'antivirals', 'antifungals', 'resistance_mechanisms'],
                'diagnostic_methods': ['culture', 'pcr', 'serology', 'rapid_tests']
            },
            'immunology_concepts': {
                'immune_components': ['innate_immunity', 'adaptive_immunity', 'cellular_immunity', 'humoral_immunity'],
                'immune_cells': ['t_cells', 'b_cells', 'dendritic_cells', 'macrophages', 'neutrophils'],
                'immune_molecules': ['antibodies', 'cytokines', 'complement', 'mhc_molecules'],
                'immune_disorders': ['autoimmunity', 'immunodeficiency', 'hypersensitivity', 'transplant_rejection']
            },
            'clinical_concepts': {
                'disease_processes': ['acute_infection', 'chronic_infection', 'latent_infection', 'opportunistic_infection'],
                'treatment_approaches': ['antimicrobial_therapy', 'immunotherapy', 'vaccination', 'supportive_care'],
                'prevention_strategies': ['infection_control', 'vaccination_programs', 'public_health_measures']
            }
        }
        
        return key_concepts
    
    def _generate_definitions(self) -> Dict:
        """生成术语定义"""
        definitions = {
            'pathogen': '能够引起疾病的微生物，包括细菌、病毒、真菌和寄生虫',
            'antibody': '由B细胞产生的免疫球蛋白，能特异性结合抗原',
            'antigen': '能够刺激免疫反应并与抗体或T细胞受体结合的物质',
            'vaccine': '含有病原体抗原的制品，用于刺激机体产生免疫保护',
            'inflammation': '机体对刺激的防御反应，表现为红、肿、热、痛',
            'immunity': '机体识别和清除外来异物以维护自身稳定的能力',
            'infection': '病原微生物侵入机体并在体内繁殖引起的病理过程',
            'virulence': '病原体致病力的强弱程度',
            'resistance': '病原体对抗微生物药物的耐受能力',
            'cytokine': '由免疫细胞分泌的信号分子，调节免疫反应'
        }
        
        return definitions
    
    def _generate_exam_topics(self) -> List[Dict]:
        """生成考试主题"""
        exam_topics = []
        
        for category, knowledge in self.knowledge_points.items():
            for kp in knowledge:
                topic_data = {
                    'category': category,
                    'main_topic': kp['topic'],
                    'subtopics': kp['subtopics'],
                    'learning_objectives': kp.get('estimated_content', {}).get('learning_objectives', []),
                    'key_concepts': kp.get('estimated_content', {}).get('concepts', []),
                    'difficulty_level': self._assess_difficulty(kp),
                    'exam_weight': self._calculate_exam_weight(kp)
                }
                exam_topics.append(topic_data)
        
        return exam_topics
    
    def _assess_difficulty(self, knowledge_point: Dict) -> str:
        """评估难度等级"""
        topic = knowledge_point['topic'].lower()
        
        # 基础概念
        basic_keywords = ['overview', 'introduction', 'basic', 'structure', 'classification']
        # 中等概念
        intermediate_keywords = ['function', 'mechanism', 'process', 'development', 'regulation']
        # 高级概念
        advanced_keywords = ['pathogenesis', 'evolution', 'complex', 'advanced', 'clinical']
        
        if any(kw in topic for kw in advanced_keywords):
            return 'advanced'
        elif any(kw in topic for kw in intermediate_keywords):
            return 'intermediate'
        else:
            return 'basic'
    
    def _calculate_exam_weight(self, knowledge_point: Dict) -> float:
        """计算考试权重"""
        # 基于主题重要性和复杂度计算权重
        base_weight = 1.0
        
        # 核心主题增加权重
        core_topics = ['pathogenesis', 'immune', 'infection', 'treatment']
        if any(topic in knowledge_point['topic'].lower() for topic in core_topics):
            base_weight += 0.5
        
        # 根据子主题数量调整权重
        subtopic_count = len(knowledge_point.get('subtopics', []))
        base_weight += subtopic_count * 0.1
        
        return min(base_weight, 2.0)  # 最大权重限制为2.0
    
    def _assign_difficulty_levels(self) -> Dict:
        """分配难度等级"""
        difficulty_distribution = {
            'basic': {'count': 0, 'topics': []},
            'intermediate': {'count': 0, 'topics': []},
            'advanced': {'count': 0, 'topics': []}
        }
        
        for category, knowledge_list in self.knowledge_points.items():
            for kp in knowledge_list:
                difficulty = self._assess_difficulty(kp)
                difficulty_distribution[difficulty]['count'] += 1
                difficulty_distribution[difficulty]['topics'].append(kp['topic'])
        
        return difficulty_distribution
    
    def save_knowledge_base(self, output_file: str):
        """保存知识库"""
        knowledge_base = self.extract_knowledge_points()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(knowledge_base, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 知识库已保存到: {output_file}")
        return knowledge_base

def main():
    print("📚 开始解析课件文档内容...")
    print("=" * 50)
    
    parser = DocumentContentParser("/Users/<USER>/phc-mbse/phc-mbse/class-test-system/classes")
    
    # 提取知识点并保存
    knowledge_base = parser.save_knowledge_base(
        "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/knowledge_base.json"
    )
    
    # 显示统计信息
    print(f"\n📊 知识点提取统计:")
    print(f"- 处理文件数: {knowledge_base['total_files_processed']}")
    print(f"- 知识类别数: {len(knowledge_base['knowledge_categories'])}")
    print(f"- 考试主题数: {len(knowledge_base['exam_topics'])}")
    
    for category, data in knowledge_base['knowledge_categories'].items():
        print(f"- {data['description']}: {data['file_count']} 个文件")
    
    print("\n🎯 难度分布:")
    for level, data in knowledge_base['difficulty_levels'].items():
        print(f"- {level}: {data['count']} 个主题")

if __name__ == "__main__":
    main()
