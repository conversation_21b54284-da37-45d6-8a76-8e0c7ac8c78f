#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MI2 增强版考试系统
基于提取的知识点生成个性化考试
"""

import json
import random
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class StudentProfile:
    """学生档案"""
    student_id: str
    name: str
    weak_areas: List[str]
    strong_areas: List[str]
    previous_scores: Dict[str, float]
    learning_style: str  # visual, auditory, kinesthetic
    difficulty_preference: str  # basic, intermediate, advanced

@dataclass
class ExamConfig:
    """考试配置"""
    exam_type: str
    total_questions: int
    duration_minutes: int
    passing_score: int
    module_weights: Dict[str, float]
    difficulty_distribution: Dict[str, float]
    question_type_distribution: Dict[str, float]
    adaptive_difficulty: bool = True

@dataclass
class ExamSession:
    """考试会话"""
    session_id: str
    student_id: str
    exam_config: ExamConfig
    questions: List[Dict]
    start_time: datetime
    current_question: int
    answers: List[Dict]
    time_spent_per_question: List[int]
    is_completed: bool = False

class EnhancedExamSystem:
    """增强版考试系统"""
    
    def __init__(self, knowledge_base_dir: str = "results", question_banks_dir: str = "question_banks"):
        self.knowledge_base_dir = Path(knowledge_base_dir)
        self.question_banks_dir = Path(question_banks_dir)
        self.knowledge_points = []
        self.question_banks = {}
        self.student_profiles = {}
        self.active_sessions = {}
        
        # 加载数据
        self._load_knowledge_base()
        self._load_question_banks()
        self._load_student_profiles()
        
        # 预定义考试配置
        self.exam_configs = {
            "diagnostic": ExamConfig(
                exam_type="diagnostic",
                total_questions=20,
                duration_minutes=30,
                passing_score=60,
                module_weights={
                    "microbiology": 0.4,
                    "innate_immunity": 0.15,
                    "adaptive_immunity": 0.25,
                    "immune_pathology": 0.15,
                    "clinical_immunology": 0.05
                },
                difficulty_distribution={"basic": 0.5, "intermediate": 0.3, "advanced": 0.2},
                question_type_distribution={"multiple_choice": 0.7, "multiple_select": 0.2, "short_answer": 0.1}
            ),
            "regular": ExamConfig(
                exam_type="regular",
                total_questions=50,
                duration_minutes=120,
                passing_score=60,
                module_weights={
                    "microbiology": 0.4,
                    "innate_immunity": 0.15,
                    "adaptive_immunity": 0.25,
                    "immune_pathology": 0.15,
                    "clinical_immunology": 0.05
                },
                difficulty_distribution={"basic": 0.3, "intermediate": 0.5, "advanced": 0.2},
                question_type_distribution={"multiple_choice": 0.4, "multiple_select": 0.2, "short_answer": 0.25, "essay": 0.1, "case_analysis": 0.05}
            ),
            "final": ExamConfig(
                exam_type="final",
                total_questions=80,
                duration_minutes=180,
                passing_score=60,
                module_weights={
                    "microbiology": 0.4,
                    "innate_immunity": 0.15,
                    "adaptive_immunity": 0.25,
                    "immune_pathology": 0.15,
                    "clinical_immunology": 0.05
                },
                difficulty_distribution={"basic": 0.2, "intermediate": 0.5, "advanced": 0.3},
                question_type_distribution={"multiple_choice": 0.3, "multiple_select": 0.2, "short_answer": 0.2, "essay": 0.2, "case_analysis": 0.1}
            )
        }
    
    def _load_knowledge_base(self):
        """加载知识库"""
        try:
            knowledge_file = self.knowledge_base_dir / "extracted_knowledge_points.json"
            if knowledge_file.exists():
                with open(knowledge_file, 'r', encoding='utf-8') as f:
                    self.knowledge_points = json.load(f)
                logger.info(f"加载了 {len(self.knowledge_points)} 个知识点")
            else:
                logger.warning(f"知识库文件不存在: {knowledge_file}")
        except Exception as e:
            logger.error(f"加载知识库失败: {e}")
    
    def _load_question_banks(self):
        """加载题库"""
        try:
            # 加载原有题库
            for bank_file in self.question_banks_dir.glob("*.json"):
                with open(bank_file, 'r', encoding='utf-8') as f:
                    bank_data = json.load(f)
                    module_name = bank_data.get("module", bank_file.stem)
                    self.question_banks[module_name] = bank_data.get("questions", [])
            
            # 加载生成的题库
            for bank_file in self.knowledge_base_dir.glob("question_bank_*.json"):
                with open(bank_file, 'r', encoding='utf-8') as f:
                    bank_data = json.load(f)
                    module_name = bank_data.get("module", bank_file.stem.replace("question_bank_", ""))
                    if module_name not in self.question_banks:
                        self.question_banks[module_name] = []
                    self.question_banks[module_name].extend(bank_data.get("questions", []))
            
            total_questions = sum(len(questions) for questions in self.question_banks.values())
            logger.info(f"加载了 {len(self.question_banks)} 个题库，共 {total_questions} 道题目")
            
        except Exception as e:
            logger.error(f"加载题库失败: {e}")
    
    def _load_student_profiles(self):
        """加载学生档案"""
        try:
            profiles_file = Path("student_profiles.json")
            if profiles_file.exists():
                with open(profiles_file, 'r', encoding='utf-8') as f:
                    profiles_data = json.load(f)
                    for profile_data in profiles_data:
                        profile = StudentProfile(**profile_data)
                        self.student_profiles[profile.student_id] = profile
                logger.info(f"加载了 {len(self.student_profiles)} 个学生档案")
        except Exception as e:
            logger.warning(f"加载学生档案失败: {e}")
    
    def create_personalized_exam(self, student_id: str, exam_type: str = "regular") -> str:
        """创建个性化考试"""
        if exam_type not in self.exam_configs:
            raise ValueError(f"不支持的考试类型: {exam_type}")
        
        config = self.exam_configs[exam_type]
        student_profile = self.student_profiles.get(student_id)
        
        # 生成考试会话ID
        session_id = f"exam_{student_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 选择题目
        selected_questions = self._select_questions(config, student_profile)
        
        # 创建考试会话
        session = ExamSession(
            session_id=session_id,
            student_id=student_id,
            exam_config=config,
            questions=selected_questions,
            start_time=datetime.now(),
            current_question=0,
            answers=[],
            time_spent_per_question=[]
        )
        
        self.active_sessions[session_id] = session
        
        logger.info(f"为学生 {student_id} 创建了 {exam_type} 考试，会话ID: {session_id}")
        return session_id
    
    def _select_questions(self, config: ExamConfig, student_profile: Optional[StudentProfile]) -> List[Dict]:
        """选择考试题目"""
        selected_questions = []
        
        # 按模块分配题目数量
        for module, weight in config.module_weights.items():
            module_question_count = int(config.total_questions * weight)
            if module_question_count == 0:
                continue
                
            module_questions = self.question_banks.get(module, [])
            if not module_questions:
                logger.warning(f"模块 {module} 没有可用题目")
                continue
            
            # 根据难度分布选择题目
            difficulty_questions = {"basic": [], "intermediate": [], "advanced": []}
            for q in module_questions:
                difficulty = q.get("difficulty", "basic")
                if difficulty in difficulty_questions:
                    difficulty_questions[difficulty].append(q)
            
            # 按难度分配题目
            for difficulty, dist_weight in config.difficulty_distribution.items():
                difficulty_count = max(1, int(module_question_count * dist_weight))
                available_questions = difficulty_questions.get(difficulty, [])
                
                if available_questions:
                    # 如果有学生档案，优先选择薄弱领域的题目
                    if student_profile and student_profile.weak_areas:
                        weak_questions = [q for q in available_questions 
                                        if any(weak in q.get("topic", "").lower() 
                                             for weak in student_profile.weak_areas)]
                        if weak_questions:
                            available_questions = weak_questions
                    
                    selected = random.sample(available_questions, 
                                           min(difficulty_count, len(available_questions)))
                    selected_questions.extend(selected)
        
        # 随机打乱题目顺序
        random.shuffle(selected_questions)
        
        # 确保题目数量正确
        if len(selected_questions) > config.total_questions:
            selected_questions = selected_questions[:config.total_questions]
        elif len(selected_questions) < config.total_questions:
            # 如果题目不够，从所有题库中随机补充
            all_questions = []
            for questions in self.question_banks.values():
                all_questions.extend(questions)
            
            remaining_count = config.total_questions - len(selected_questions)
            additional_questions = random.sample(all_questions, 
                                               min(remaining_count, len(all_questions)))
            selected_questions.extend(additional_questions)
        
        return selected_questions
    
    def get_exam_session(self, session_id: str) -> Optional[ExamSession]:
        """获取考试会话"""
        return self.active_sessions.get(session_id)
    
    def submit_answer(self, session_id: str, question_index: int, answer: Any, time_spent: int) -> bool:
        """提交答案"""
        session = self.active_sessions.get(session_id)
        if not session:
            return False
        
        # 记录答案
        answer_record = {
            "question_index": question_index,
            "answer": answer,
            "timestamp": datetime.now().isoformat(),
            "time_spent": time_spent
        }
        
        session.answers.append(answer_record)
        session.time_spent_per_question.append(time_spent)
        session.current_question = question_index + 1
        
        return True
    
    def complete_exam(self, session_id: str) -> Dict[str, Any]:
        """完成考试并生成结果"""
        session = self.active_sessions.get(session_id)
        if not session:
            raise ValueError(f"考试会话不存在: {session_id}")
        
        session.is_completed = True
        
        # 计算成绩
        results = self._calculate_results(session)
        
        # 保存考试记录
        self._save_exam_record(session, results)
        
        # 更新学生档案
        self._update_student_profile(session.student_id, results)
        
        return results
    
    def _calculate_results(self, session: ExamSession) -> Dict[str, Any]:
        """计算考试结果"""
        total_score = 0
        max_score = 0
        correct_answers = 0
        module_scores = {}
        
        for i, answer_record in enumerate(session.answers):
            if i >= len(session.questions):
                continue
                
            question = session.questions[i]
            answer = answer_record["answer"]
            
            # 计算单题得分
            question_score = self._score_question(question, answer)
            total_score += question_score
            
            # 计算最大可能得分
            question_type = question.get("type", "multiple_choice")
            if question_type in ["multiple_choice", "multiple_select"]:
                max_score += 2 if question_type == "multiple_choice" else 3
            elif question_type == "short_answer":
                max_score += 5
            elif question_type == "essay":
                max_score += 15
            elif question_type == "case_analysis":
                max_score += 20
            
            # 统计正确答案
            if question_score > 0:
                correct_answers += 1
            
            # 按模块统计
            module = question.get("module", "unknown")
            if module not in module_scores:
                module_scores[module] = {"score": 0, "max_score": 0, "questions": 0}
            module_scores[module]["score"] += question_score
            module_scores[module]["max_score"] += max_score - (total_score - question_score)
            module_scores[module]["questions"] += 1
        
        # 计算百分比得分
        percentage_score = (total_score / max_score * 100) if max_score > 0 else 0
        
        return {
            "session_id": session.session_id,
            "student_id": session.student_id,
            "exam_type": session.exam_config.exam_type,
            "total_score": total_score,
            "max_score": max_score,
            "percentage_score": round(percentage_score, 2),
            "correct_answers": correct_answers,
            "total_questions": len(session.questions),
            "accuracy": round(correct_answers / len(session.questions) * 100, 2),
            "passing": percentage_score >= session.exam_config.passing_score,
            "module_scores": module_scores,
            "total_time": sum(session.time_spent_per_question),
            "average_time_per_question": round(sum(session.time_spent_per_question) / len(session.time_spent_per_question), 2) if session.time_spent_per_question else 0,
            "completion_time": datetime.now().isoformat()
        }
    
    def _score_question(self, question: Dict, answer: Any) -> float:
        """为单个题目评分"""
        question_type = question.get("type", "multiple_choice")
        
        if question_type == "multiple_choice":
            correct_answer = question.get("correct_answer")
            return 2.0 if answer == correct_answer else 0.0
        
        elif question_type == "multiple_select":
            correct_answers = set(question.get("correct_answers", []))
            student_answers = set(answer) if isinstance(answer, list) else set()
            
            if student_answers == correct_answers:
                return 3.0
            elif student_answers.intersection(correct_answers):
                return 1.5  # 部分正确
            else:
                return 0.0
        
        elif question_type in ["short_answer", "essay", "case_analysis"]:
            # 主观题需要人工评分，这里返回预设分数
            if question_type == "short_answer":
                return 3.0  # 假设部分正确
            elif question_type == "essay":
                return 10.0  # 假设良好
            elif question_type == "case_analysis":
                return 15.0  # 假设良好
        
        return 0.0

    def _save_exam_record(self, session: ExamSession, results: Dict[str, Any]):
        """保存考试记录"""
        try:
            records_dir = Path("exam_records")
            records_dir.mkdir(exist_ok=True)

            record = {
                "session_id": session.session_id,
                "student_id": session.student_id,
                "exam_config": asdict(session.exam_config),
                "questions": session.questions,
                "answers": session.answers,
                "time_spent_per_question": session.time_spent_per_question,
                "start_time": session.start_time.isoformat(),
                "results": results
            }

            record_file = records_dir / f"{session.session_id}.json"
            with open(record_file, 'w', encoding='utf-8') as f:
                json.dump(record, f, ensure_ascii=False, indent=2)

            logger.info(f"考试记录已保存: {record_file}")

        except Exception as e:
            logger.error(f"保存考试记录失败: {e}")

    def _update_student_profile(self, student_id: str, results: Dict[str, Any]):
        """更新学生档案"""
        try:
            if student_id not in self.student_profiles:
                # 创建新的学生档案
                self.student_profiles[student_id] = StudentProfile(
                    student_id=student_id,
                    name=f"Student_{student_id}",
                    weak_areas=[],
                    strong_areas=[],
                    previous_scores={},
                    learning_style="visual",
                    difficulty_preference="intermediate"
                )

            profile = self.student_profiles[student_id]

            # 更新历史成绩
            exam_type = results["exam_type"]
            profile.previous_scores[exam_type] = results["percentage_score"]

            # 分析薄弱和强势领域
            weak_areas = []
            strong_areas = []

            for module, module_data in results["module_scores"].items():
                if module_data["max_score"] > 0:
                    module_percentage = module_data["score"] / module_data["max_score"] * 100
                    if module_percentage < 60:
                        weak_areas.append(module)
                    elif module_percentage > 80:
                        strong_areas.append(module)

            profile.weak_areas = list(set(profile.weak_areas + weak_areas))
            profile.strong_areas = list(set(profile.strong_areas + strong_areas))

            # 保存更新的档案
            self._save_student_profiles()

        except Exception as e:
            logger.error(f"更新学生档案失败: {e}")

    def _save_student_profiles(self):
        """保存学生档案"""
        try:
            profiles_data = [asdict(profile) for profile in self.student_profiles.values()]
            with open("student_profiles.json", 'w', encoding='utf-8') as f:
                json.dump(profiles_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存学生档案失败: {e}")

    def get_student_progress(self, student_id: str) -> Dict[str, Any]:
        """获取学生学习进度"""
        profile = self.student_profiles.get(student_id)
        if not profile:
            return {"error": "学生档案不存在"}

        # 加载历史考试记录
        records_dir = Path("exam_records")
        student_records = []

        if records_dir.exists():
            for record_file in records_dir.glob(f"exam_{student_id}_*.json"):
                try:
                    with open(record_file, 'r', encoding='utf-8') as f:
                        record = json.load(f)
                        student_records.append(record)
                except Exception as e:
                    logger.warning(f"读取考试记录失败: {e}")

        # 分析进度
        progress = {
            "student_id": student_id,
            "profile": asdict(profile),
            "exam_history": student_records,
            "total_exams": len(student_records),
            "average_score": 0,
            "improvement_trend": "stable",
            "recommendations": []
        }

        if student_records:
            scores = [record["results"]["percentage_score"] for record in student_records]
            progress["average_score"] = round(sum(scores) / len(scores), 2)

            # 分析改进趋势
            if len(scores) >= 2:
                recent_avg = sum(scores[-3:]) / min(3, len(scores))
                early_avg = sum(scores[:3]) / min(3, len(scores))

                if recent_avg > early_avg + 5:
                    progress["improvement_trend"] = "improving"
                elif recent_avg < early_avg - 5:
                    progress["improvement_trend"] = "declining"

        # 生成学习建议
        recommendations = []
        if profile.weak_areas:
            recommendations.append(f"建议加强以下领域的学习: {', '.join(profile.weak_areas)}")

        if progress["average_score"] < 60:
            recommendations.append("总体成绩偏低，建议增加基础知识的复习时间")
        elif progress["average_score"] > 85:
            recommendations.append("成绩优秀，可以尝试更有挑战性的题目")

        progress["recommendations"] = recommendations

        return progress
