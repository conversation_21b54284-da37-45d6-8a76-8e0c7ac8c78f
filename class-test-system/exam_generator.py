#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MI2 考试生成器 - Medical Immunology 2 Exam Generator
基于MI2课程内容的智能考试生成系统
"""

import json
import random
import os
import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class Question:
    """题目数据结构"""
    id: str
    type: str
    difficulty: str
    topic: str
    question: str
    question_en: str
    options: Optional[List[str]] = None
    correct_answer: Optional[int] = None
    correct_answers: Optional[List[int]] = None
    answer_key: Optional[str] = None
    answer_key_en: Optional[str] = None
    explanation: Optional[str] = None
    explanation_en: Optional[str] = None
    module: Optional[str] = None


@dataclass
class ExamConfig:
    """考试配置"""
    total_questions: int
    duration_minutes: int
    passing_score: int
    module_weights: Dict[str, float]
    difficulty_weights: Dict[str, float]
    type_distribution: Dict[str, float]


@dataclass
class StudentAnswer:
    """学生答案"""
    question_id: str
    answer: Any
    time_spent: int = 0  # 答题时间（秒）


@dataclass
class ExamResult:
    """考试结果"""
    student_id: str
    exam_id: str
    questions: List[Question]
    answers: List[StudentAnswer]
    score: float
    total_points: int
    earned_points: int
    passing: bool
    time_taken: int
    detailed_results: Dict[str, Any]
    recommendations: List[str]


class ExamGenerator:
    """考试生成器"""
    
    def __init__(self, config_path: str = "config.json"):
        """初始化考试生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self.question_banks_path = Path("question_banks")
        self.config = self._load_config()
        self.question_banks = self._load_question_banks()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {self.config_path} 未找到")
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _load_question_banks(self) -> Dict[str, List[Question]]:
        """加载所有题库"""
        question_banks = {}
        
        if not self.question_banks_path.exists():
            raise FileNotFoundError(f"题库目录 {self.question_banks_path} 未找到")
        
        for bank_file in self.question_banks_path.glob("*.json"):
            try:
                with open(bank_file, 'r', encoding='utf-8') as f:
                    bank_data = json.load(f)
                    
                module_name = bank_data.get('module', bank_file.stem)
                questions = []
                
                for q_data in bank_data.get('questions', []):
                    q_data['module'] = module_name
                    questions.append(Question(**q_data))
                
                question_banks[module_name] = questions
                
            except (json.JSONDecodeError, TypeError) as e:
                print(f"警告：题库文件 {bank_file} 格式错误: {e}")
                continue
        
        return question_banks
    
    def generate_exam(self, 
                     student_id: str,
                     exam_type: str = "regular",
                     custom_config: Optional[Dict] = None) -> Dict[str, Any]:
        """生成考试
        
        Args:
            student_id: 学生ID
            exam_type: 考试类型 (regular, midterm, final)
            custom_config: 自定义配置
            
        Returns:
            考试数据
        """
        # 使用默认配置或自定义配置
        exam_config = self._get_exam_config(exam_type, custom_config)
        
        # 选择题目
        selected_questions = self._select_questions(exam_config)
        
        # 生成考试ID
        exam_id = f"{exam_type}_{student_id}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 打乱题目顺序
        random.shuffle(selected_questions)
        
        # 构建考试数据
        exam_data = {
            "exam_id": exam_id,
            "student_id": student_id,
            "exam_type": exam_type,
            "created_at": datetime.datetime.now().isoformat(),
            "duration_minutes": exam_config.duration_minutes,
            "passing_score": exam_config.passing_score,
            "total_questions": len(selected_questions),
            "questions": [self._format_question_for_exam(q) for q in selected_questions],
            "config": asdict(exam_config)
        }
        
        # 保存考试
        self._save_exam(exam_data)
        
        return exam_data
    
    def _get_exam_config(self, exam_type: str, custom_config: Optional[Dict]) -> ExamConfig:
        """获取考试配置"""
        base_config = self.config.get("exam_settings", {})
        
        # 根据考试类型调整配置
        if exam_type == "midterm":
            base_config = {**base_config, "questions_per_exam": 30, "duration_minutes": 90}
        elif exam_type == "final":
            base_config = {**base_config, "questions_per_exam": 80, "duration_minutes": 180}
        
        # 应用自定义配置
        if custom_config:
            base_config.update(custom_config)
        
        return ExamConfig(
            total_questions=base_config.get("questions_per_exam", 50),
            duration_minutes=base_config.get("default_duration", 120),
            passing_score=base_config.get("passing_score", 60),
            module_weights=self.config.get("course_modules", {}),
            difficulty_weights=self.config.get("difficulty_levels", {}),
            type_distribution=base_config.get("question_distribution", {})
        )
    
    def _select_questions(self, config: ExamConfig) -> List[Question]:
        """选择题目"""
        selected_questions = []
        
        # 按模块分配题目数量
        for module, module_config in config.module_weights.items():
            if module not in self.question_banks:
                continue
                
            module_questions = int(config.total_questions * module_config.get("weight", 0.2))
            module_bank = self.question_banks[module]
            
            # 按难度分配
            for difficulty, diff_config in config.difficulty_weights.items():
                diff_questions = int(module_questions * diff_config.get("weight", 0.33))
                
                # 筛选符合条件的题目
                candidates = [q for q in module_bank if q.difficulty == difficulty]
                
                if candidates:
                    # 按题型分配
                    for q_type, type_ratio in config.type_distribution.items():
                        type_questions = max(1, int(diff_questions * type_ratio))
                        type_candidates = [q for q in candidates if q.type == q_type]
                        
                        if type_candidates:
                            selected = random.sample(
                                type_candidates,
                                min(type_questions, len(type_candidates))
                            )
                            selected_questions.extend(selected)
        
        # 确保题目数量足够
        while len(selected_questions) < config.total_questions:
            all_questions = [q for bank in self.question_banks.values() for q in bank]
            remaining_questions = [q for q in all_questions if q not in selected_questions]
            
            if not remaining_questions:
                break
                
            selected_questions.append(random.choice(remaining_questions))
        
        return selected_questions[:config.total_questions]
    
    def _format_question_for_exam(self, question: Question) -> Dict[str, Any]:
        """格式化题目用于考试"""
        q_data = {
            "id": question.id,
            "type": question.type,
            "difficulty": question.difficulty,
            "topic": question.topic,
            "question": question.question,
            "question_en": question.question_en,
            "module": question.module
        }
        
        if question.options:
            q_data["options"] = question.options
        
        if question.type in ["short_answer", "essay", "case_analysis"]:
            q_data["word_limit"] = self.config["question_types"][question.type].get("word_limit", 150)
        
        return q_data
    
    def _save_exam(self, exam_data: Dict[str, Any]):
        """保存考试数据"""
        exam_dir = Path("exams")
        exam_dir.mkdir(exist_ok=True)
        
        exam_file = exam_dir / f"{exam_data['exam_id']}.json"
        
        with open(exam_file, 'w', encoding='utf-8') as f:
            json.dump(exam_data, f, ensure_ascii=False, indent=2)
    
    def evaluate_exam(self, exam_id: str, student_answers: List[Dict[str, Any]]) -> ExamResult:
        """评估考试结果
        
        Args:
            exam_id: 考试ID
            student_answers: 学生答案列表
            
        Returns:
            考试结果
        """
        # 加载考试数据
        exam_data = self._load_exam(exam_id)
        if not exam_data:
            raise ValueError(f"考试 {exam_id} 未找到")
        
        # 加载完整题目信息（包含答案）
        questions = self._load_questions_with_answers(exam_data["questions"])
        
        # 评估答案
        results = self._evaluate_answers(questions, student_answers)
        
        # 生成详细结果
        detailed_results = self._generate_detailed_results(results)
        
        # 生成建议
        recommendations = self._generate_recommendations(detailed_results)
        
        # 计算总分
        total_points = sum(self.config["question_types"][q.type]["points"] for q in questions)
        earned_points = sum(result["points"] for result in results)
        score = (earned_points / total_points) * 100 if total_points > 0 else 0
        
        # 创建考试结果
        exam_result = ExamResult(
            student_id=exam_data["student_id"],
            exam_id=exam_id,
            questions=questions,
            answers=[StudentAnswer(**ans) for ans in student_answers],
            score=score,
            total_points=total_points,
            earned_points=earned_points,
            passing=score >= exam_data["passing_score"],
            time_taken=sum(ans.get("time_spent", 0) for ans in student_answers),
            detailed_results=detailed_results,
            recommendations=recommendations
        )
        
        # 保存结果
        self._save_exam_result(exam_result)
        
        return exam_result
    
    def _load_exam(self, exam_id: str) -> Optional[Dict[str, Any]]:
        """加载考试数据"""
        exam_file = Path("exams") / f"{exam_id}.json"
        
        if not exam_file.exists():
            return None
        
        with open(exam_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_questions_with_answers(self, exam_questions: List[Dict]) -> List[Question]:
        """加载带答案的完整题目信息"""
        questions = []
        
        for eq in exam_questions:
            # 从题库中找到完整的题目信息
            for module_questions in self.question_banks.values():
                for q in module_questions:
                    if q.id == eq["id"]:
                        questions.append(q)
                        break
        
        return questions
    
    def _evaluate_answers(self, questions: List[Question], student_answers: List[Dict]) -> List[Dict]:
        """评估学生答案"""
        results = []
        answer_dict = {ans["question_id"]: ans for ans in student_answers}
        
        for question in questions:
            student_answer = answer_dict.get(question.id)
            question_points = self.config["question_types"][question.type]["points"]
            
            if not student_answer:
                # 未答题
                results.append({
                    "question_id": question.id,
                    "type": question.type,
                    "correct": False,
                    "points": 0,
                    "max_points": question_points,
                    "feedback": "未答题"
                })
                continue
            
            # 根据题型评估
            if question.type == "multiple_choice":
                correct = student_answer["answer"] == question.correct_answer
                points = question_points if correct else 0
            elif question.type == "multiple_select":
                correct = set(student_answer["answer"]) == set(question.correct_answers)
                points = question_points if correct else 0
            elif question.type in ["short_answer", "essay", "case_analysis"]:
                # 简答题和论述题需要人工评分，这里给出建议分数
                points = self._evaluate_text_answer(question, student_answer["answer"])
                correct = points >= question_points * 0.6
            else:
                correct = False
                points = 0
            
            results.append({
                "question_id": question.id,
                "type": question.type,
                "correct": correct,
                "points": points,
                "max_points": question_points,
                "student_answer": student_answer["answer"],
                "correct_answer": question.correct_answer or question.correct_answers or question.answer_key,
                "explanation": question.explanation,
                "feedback": self._generate_feedback(question, student_answer, correct)
            })
        
        return results
    
    def _evaluate_text_answer(self, question: Question, student_answer: str) -> int:
        """评估文本答案（简化版本）"""
        if not student_answer or not student_answer.strip():
            return 0
        
        # 基于答案长度和关键词的简单评分
        answer_key = question.answer_key or question.answer_key_en or ""
        max_points = self.config["question_types"][question.type]["points"]
        
        # 关键词匹配（简化版）
        key_terms = []
        if "MHC" in answer_key:
            key_terms.extend(["MHC", "主要组织相容性", "抗原呈递"])
        if "T细胞" in answer_key:
            key_terms.extend(["T细胞", "T cell", "细胞毒性"])
        if "免疫" in answer_key:
            key_terms.extend(["免疫", "immune", "抗体"])
        
        # 简单的关键词匹配评分
        matched_terms = sum(1 for term in key_terms if term in student_answer)
        if not key_terms:
            # 如果没有关键词，基于长度评分
            word_limit = self.config["question_types"][question.type].get("word_limit", 150)
            length_score = min(len(student_answer.split()) / word_limit, 1.0)
            return int(max_points * length_score * 0.7)  # 最多70%分数
        
        keyword_score = matched_terms / len(key_terms)
        return int(max_points * keyword_score * 0.8)  # 最多80%分数
    
    def _generate_feedback(self, question: Question, student_answer: Dict, correct: bool) -> str:
        """生成反馈"""
        if correct:
            return "正确！" + (question.explanation or "")
        else:
            feedback = "答案不正确。"
            if question.explanation:
                feedback += f"\n解释：{question.explanation}"
            return feedback
    
    def _generate_detailed_results(self, results: List[Dict]) -> Dict[str, Any]:
        """生成详细结果分析"""
        total_questions = len(results)
        correct_questions = sum(1 for r in results if r["correct"])
        
        # 按模块统计
        module_stats = {}
        # 按难度统计
        difficulty_stats = {}
        # 按题型统计
        type_stats = {}
        
        for result in results:
            # 这里需要从题目信息中获取模块、难度等信息
            # 简化版本，实际应用中需要更完整的实现
            pass
        
        return {
            "total_questions": total_questions,
            "correct_questions": correct_questions,
            "accuracy": correct_questions / total_questions if total_questions > 0 else 0,
            "module_stats": module_stats,
            "difficulty_stats": difficulty_stats,
            "type_stats": type_stats
        }
    
    def _generate_recommendations(self, detailed_results: Dict) -> List[str]:
        """生成学习建议"""
        recommendations = []
        
        accuracy = detailed_results.get("accuracy", 0)
        
        if accuracy < 0.6:
            recommendations.append("需要加强基础知识的学习，建议重点复习课程基本概念。")
        elif accuracy < 0.8:
            recommendations.append("基础知识掌握较好，建议加强对机制理解和应用能力的训练。")
        else:
            recommendations.append("表现优秀！建议继续保持，可以尝试更有挑战性的学习内容。")
        
        return recommendations
    
    def _save_exam_result(self, exam_result: ExamResult):
        """保存考试结果"""
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)
        
        result_file = results_dir / f"{exam_result.exam_id}_result.json"
        
        # 将结果转换为JSON格式
        result_data = asdict(exam_result)
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    def generate_report(self, exam_result: ExamResult) -> str:
        """生成考试报告"""
        report_lines = []
        
        # 标题
        report_lines.append("="*60)
        report_lines.append("MI2 Medical Immunology 2 - 考试成绩报告")
        report_lines.append("="*60)
        report_lines.append("")
        
        # 基本信息
        report_lines.append(f"学生ID: {exam_result.student_id}")
        report_lines.append(f"考试ID: {exam_result.exam_id}")
        report_lines.append(f"考试时间: {exam_result.time_taken // 60}分{exam_result.time_taken % 60}秒")
        report_lines.append(f"总分: {exam_result.score:.1f}分")
        report_lines.append(f"获得分数: {exam_result.earned_points}/{exam_result.total_points}")
        report_lines.append(f"是否通过: {'是' if exam_result.passing else '否'}")
        report_lines.append("")
        
        # 详细分析
        report_lines.append("详细分析:")
        report_lines.append("-" * 40)
        accuracy = exam_result.detailed_results.get("accuracy", 0)
        report_lines.append(f"答题准确率: {accuracy:.1%}")
        report_lines.append("")
        
        # 学习建议
        report_lines.append("学习建议:")
        report_lines.append("-" * 40)
        for rec in exam_result.recommendations:
            report_lines.append(f"• {rec}")
        report_lines.append("")
        
        return "\n".join(report_lines)


def main():
    """主函数 - 示例用法"""
    # 创建考试生成器
    generator = ExamGenerator()
    
    # 生成考试
    exam = generator.generate_exam(
        student_id="student_001",
        exam_type="regular"
    )
    
    print("考试生成成功！")
    print(f"考试ID: {exam['exam_id']}")
    print(f"题目数量: {exam['total_questions']}")
    print(f"考试时长: {exam['duration_minutes']}分钟")
    
    # 示例答案（实际使用中由学生答题界面提供）
    sample_answers = [
        {"question_id": exam["questions"][0]["id"], "answer": 1, "time_spent": 30},
        {"question_id": exam["questions"][1]["id"], "answer": 2, "time_spent": 45},
        # ... 更多答案
    ]
    
    # 评估结果
    # result = generator.evaluate_exam(exam["exam_id"], sample_answers)
    # print(f"考试结果: {result.score:.1f}分")
    # print(generator.generate_report(result))


if __name__ == "__main__":
    main() 