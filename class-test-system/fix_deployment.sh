#!/bin/bash

# MI2 考试系统部署修复脚本
echo "🔧 MI2 考试系统 - 修复部署"
echo "=========================="

# 设置变量
DEPLOY_DIR="/opt/mi2-exam-system"
LOCAL_DIR="/opt/mi2-exam-deployment"  # 假设这是原始部署包的位置

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用 sudo 运行此脚本"
    exit 1
fi

echo "📁 停止服务..."
systemctl stop mi2-exam 2>/dev/null || true
systemctl stop nginx 2>/dev/null || true

echo "📦 复制应用文件..."

# 复制核心Python文件
if [ -f "$LOCAL_DIR/app.py" ]; then
    cp "$LOCAL_DIR/app.py" "$DEPLOY_DIR/"
    echo "✅ 复制 app.py"
else
    echo "❌ 未找到 app.py"
fi

if [ -f "$LOCAL_DIR/exam_generator.py" ]; then
    cp "$LOCAL_DIR/exam_generator.py" "$DEPLOY_DIR/"
    echo "✅ 复制 exam_generator.py"
else
    echo "❌ 未找到 exam_generator.py"
fi

if [ -f "$LOCAL_DIR/config.json" ]; then
    cp "$LOCAL_DIR/config.json" "$DEPLOY_DIR/"
    echo "✅ 复制 config.json"
else
    echo "❌ 未找到 config.json"
fi

# 复制模板文件
if [ -d "$LOCAL_DIR/templates" ]; then
    cp -r "$LOCAL_DIR/templates/"* "$DEPLOY_DIR/templates/"
    echo "✅ 复制模板文件"
else
    echo "❌ 未找到 templates 目录"
fi

# 复制题库文件
if [ -d "$LOCAL_DIR/question_banks" ]; then
    cp -r "$LOCAL_DIR/question_banks/"* "$DEPLOY_DIR/question_banks/"
    echo "✅ 复制题库文件"
else
    echo "❌ 未找到 question_banks 目录"
fi

# 设置权限
chown -R root:root "$DEPLOY_DIR"
chmod -R 755 "$DEPLOY_DIR"
chmod +x "$DEPLOY_DIR/manage.sh"

echo "🚀 启动服务..."
systemctl start nginx
systemctl start mi2-exam

echo "⏳ 等待服务启动..."
sleep 5

echo "📊 检查服务状态..."
systemctl status mi2-exam --no-pager
systemctl status nginx --no-pager

echo ""
echo "🎉 修复完成！"
echo "访问地址: http://1.94.49.155"
echo "健康检查: http://1.94.49.155/health"
echo ""
echo "如果仍有问题，请检查日志："
echo "- 应用日志: journalctl -u mi2-exam -f"
echo "- Nginx日志: tail -f /var/log/nginx/error.log" 