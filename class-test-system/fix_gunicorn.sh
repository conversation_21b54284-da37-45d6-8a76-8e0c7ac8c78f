#!/bin/bash

# MI2 考试系统 Gunicorn 修复脚本
echo "🔧 MI2 考试系统 Gunicorn 修复"
echo "============================"

DEPLOY_DIR="/opt/mi2-exam-system"

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用 sudo 运行此脚本"
    exit 1
fi

echo "📁 停止服务..."
systemctl stop mi2-exam

echo "📦 1. 重新安装 Python 依赖..."
pip3 install --upgrade flask gunicorn

echo "🔧 2. 修复 Gunicorn 配置..."
cat > "$DEPLOY_DIR/gunicorn_config.py" << 'EOF'
# 修复后的 Gunicorn 配置
bind = "127.0.0.1:5000"
workers = 2
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
daemon = False
pidfile = "/var/run/mi2-exam.pid"
access_log = "/var/log/mi2-exam-access.log"
error_log = "/var/log/mi2-exam-error.log"
loglevel = "info"
EOF

echo "🔧 3. 修复系统服务配置..."
cat > /etc/systemd/system/mi2-exam.service << 'EOF'
[Unit]
Description=MI2 Exam System
After=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/mi2-exam-system
ExecStart=/usr/bin/python3 -m gunicorn --config gunicorn_config.py app:app
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStartSec=60
TimeoutStopSec=10
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

echo "🔧 4. 创建简化的应用文件..."
cat > "$DEPLOY_DIR/app.py" << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, request, redirect, url_for, session, jsonify
import json
import os
import uuid
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/var/log/mi2-exam-app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'mi2-exam-secret-key-2024'

# 创建必要目录
for directory in ['results', 'exams']:
    os.makedirs(directory, exist_ok=True)

# 默认配置
DEFAULT_CONFIG = {
    "exam_settings": {
        "time_limit": 120,
        "questions_per_exam": 8,
        "passing_score": 60
    },
    "question_banks": {
        "microbiology": {
            "name": "微生物学与感染免疫",
            "questions": [
                {
                    "id": "micro_001",
                    "type": "single_choice",
                    "question": "以下哪种细胞是先天免疫系统的重要组成部分？",
                    "options": ["T细胞", "B细胞", "巨噬细胞", "浆细胞"],
                    "correct_answer": [2],
                    "explanation": "巨噬细胞是先天免疫系统的重要组成部分，能够吞噬病原体并激活免疫反应。"
                },
                {
                    "id": "micro_002",
                    "type": "single_choice",
                    "question": "疫苗的主要作用机制是什么？",
                    "options": ["直接杀死病原体", "激活获得性免疫反应", "增强先天免疫", "抑制炎症反应"],
                    "correct_answer": [1],
                    "explanation": "疫苗通过激活获得性免疫反应，产生记忆细胞和抗体，提供长期保护。"
                },
                {
                    "id": "micro_003",
                    "type": "single_choice",
                    "question": "自身免疫病的主要特征是什么？",
                    "options": ["免疫系统攻击自身组织", "免疫系统功能低下", "病原体感染", "过敏反应"],
                    "correct_answer": [0],
                    "explanation": "自身免疫病是免疫系统错误地攻击自身正常组织和器官的疾病。"
                },
                {
                    "id": "micro_004",
                    "type": "single_choice",
                    "question": "1型糖尿病的发病机制主要涉及：",
                    "options": ["B细胞功能异常", "T细胞攻击胰岛β细胞", "NK细胞过度激活", "补体系统缺陷"],
                    "correct_answer": [1],
                    "explanation": "1型糖尿病是T细胞介导的自身免疫性疾病，主要攻击胰岛β细胞。"
                }
            ]
        },
        "adaptive_immunity": {
            "name": "适应性免疫与免疫应答",
            "questions": [
                {
                    "id": "adapt_001",
                    "type": "single_choice",
                    "question": "以下哪种细胞主要负责细胞免疫？",
                    "options": ["B细胞", "T细胞", "NK细胞", "肥大细胞"],
                    "correct_answer": [1],
                    "explanation": "T细胞主要负责细胞免疫，包括CD8+ T细胞的细胞毒性作用和CD4+ T细胞的辅助功能。"
                },
                {
                    "id": "adapt_002",
                    "type": "multiple_choice",
                    "question": "抗体的主要功能包括：",
                    "options": ["中和病原体", "激活补体", "促进吞噬作用", "直接杀死细胞"],
                    "correct_answer": [0, 1, 2],
                    "explanation": "抗体具有中和、激活补体、促进吞噬等多种功能，但不能直接杀死细胞。"
                },
                {
                    "id": "adapt_003",
                    "type": "single_choice",
                    "question": "免疫记忆的主要特点是什么？",
                    "options": ["反应更快更强", "持续时间短", "非特异性", "只存在于T细胞"],
                    "correct_answer": [0],
                    "explanation": "免疫记忆使机体在再次接触相同抗原时产生更快更强的免疫反应。"
                },
                {
                    "id": "adapt_004",
                    "type": "single_choice",
                    "question": "抗体类别转换主要发生在：",
                    "options": ["T细胞", "B细胞", "NK细胞", "巨噬细胞"],
                    "correct_answer": [1],
                    "explanation": "抗体类别转换是B细胞在激活过程中改变所产生抗体类型的过程。"
                }
            ]
        }
    }
}

@app.route('/')
def index():
    """主页"""
    logger.info("访问主页")
    return render_template('index.html', config=DEFAULT_CONFIG)

@app.route('/start_exam')
def start_exam():
    """开始考试"""
    logger.info("开始考试")
    exam_id = str(uuid.uuid4())
    session['exam_id'] = exam_id
    session['start_time'] = datetime.now().isoformat()
    
    # 收集所有题目
    questions = []
    for bank_data in DEFAULT_CONFIG['question_banks'].values():
        questions.extend(bank_data['questions'])
    
    # 限制题目数量
    max_questions = DEFAULT_CONFIG['exam_settings']['questions_per_exam']
    if len(questions) > max_questions:
        import random
        questions = random.sample(questions, max_questions)
    
    session['questions'] = questions
    session['answers'] = {}
    
    return render_template('start_exam.html', 
                         questions=questions,
                         config=DEFAULT_CONFIG,
                         exam_id=exam_id)

@app.route('/submit_exam', methods=['POST'])
def submit_exam():
    """提交考试"""
    logger.info("提交考试")
    if 'exam_id' not in session:
        return redirect(url_for('index'))
    
    # 获取答案
    answers = {}
    for key, value in request.form.items():
        if key.startswith('question_'):
            question_id = key.replace('question_', '')
            if question_id not in answers:
                answers[question_id] = []
            answers[question_id].append(value)
    
    # 计算分数
    questions = session.get('questions', [])
    total_score = 0
    max_score = len(questions) * 10
    
    for question in questions:
        question_id = question['id']
        if question_id in answers:
            user_answer = answers[question_id]
            correct_answer = question['correct_answer']
            
            if question['type'] == 'single_choice':
                if len(user_answer) == 1 and int(user_answer[0]) in correct_answer:
                    total_score += 10
            elif question['type'] == 'multiple_choice':
                user_indices = [int(x) for x in user_answer]
                if set(user_indices) == set(correct_answer):
                    total_score += 10
    
    # 保存结果
    result = {
        'exam_id': session['exam_id'],
        'start_time': session['start_time'],
        'end_time': datetime.now().isoformat(),
        'answers': answers,
        'score': total_score,
        'max_score': max_score,
        'percentage': round(total_score / max_score * 100, 2) if max_score > 0 else 0
    }
    
    result_file = os.path.join('results', f"{session['exam_id']}.json")
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    # 清除会话
    session.clear()
    
    return jsonify({
        'success': True,
        'score': total_score,
        'max_score': max_score,
        'percentage': result['percentage']
    })

@app.route('/health')
def health_check():
    """健康检查"""
    return "OK", 200

@app.errorhandler(404)
def not_found(error):
    return "页面未找到", 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"服务器错误: {error}")
    return "服务器内部错误", 500

if __name__ == '__main__':
    logger.info("启动 MI2 考试系统...")
    app.run(debug=True, host='0.0.0.0', port=5000)
EOF

echo "🔧 5. 创建日志文件..."
touch /var/log/mi2-exam-app.log
touch /var/log/mi2-exam-access.log
touch /var/log/mi2-exam-error.log
chmod 644 /var/log/mi2-exam-*.log

echo "🔧 6. 设置权限..."
chown -R root:root "$DEPLOY_DIR"
chmod -R 755 "$DEPLOY_DIR"

echo "🔄 7. 重新加载系统服务..."
systemctl daemon-reload

echo "🧪 8. 手动测试启动..."
cd "$DEPLOY_DIR"
timeout 10 python3 app.py > /tmp/app_test.log 2>&1 &
TEST_PID=$!
sleep 5
kill $TEST_PID 2>/dev/null || true

echo "结果："
if grep -q "启动 MI2 考试系统" /tmp/app_test.log; then
    echo "✅ 应用可以正常启动"
else
    echo "❌ 应用启动有问题，检查日志："
    cat /tmp/app_test.log
fi

echo ""
echo "🚀 9. 启动服务..."
systemctl start mi2-exam
sleep 5

echo "📊 10. 检查服务状态..."
systemctl status mi2-exam --no-pager

echo ""
echo "🌐 11. 测试访问..."
curl -s http://localhost:5000/health
if [ $? -eq 0 ]; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
fi

echo ""
echo "🎉 修复完成！"
echo "访问地址: http://***********"
echo "健康检查: http://***********/health"
echo ""
echo "如果仍有问题，请检查日志："
echo "- 系统日志: journalctl -u mi2-exam -f"
echo "- 应用日志: tail -f /var/log/mi2-exam-app.log"
echo "- 错误日志: tail -f /var/log/mi2-exam-error.log" 