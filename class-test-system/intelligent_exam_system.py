#!/usr/bin/env python3
"""
智能考试系统
基于提取的知识点生成考试题目，管理考试过程并分析结果
"""

import json
import random
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import defaultdict
import re

@dataclass
class Question:
    """考试题目数据类"""
    id: str
    type: str  # 'multiple_choice', 'true_false', 'short_answer', 'essay'
    category: str
    topic: str
    difficulty: str
    question_text: str
    options: List[str]  # 对于选择题
    correct_answer: str
    explanation: str
    points: int
    tags: List[str]

@dataclass
class ExamSession:
    """考试会话数据类"""
    id: str
    title: str
    description: str
    questions: List[Question]
    time_limit: int  # 分钟
    total_points: int
    created_at: str
    status: str  # 'draft', 'active', 'completed'

@dataclass
class StudentAnswer:
    """学生答案数据类"""
    question_id: str
    student_answer: str
    is_correct: bool
    points_earned: int
    time_spent: int  # 秒

@dataclass
class ExamResult:
    """考试结果数据类"""
    exam_id: str
    student_id: str
    answers: List[StudentAnswer]
    total_score: int
    max_score: int
    percentage: float
    time_taken: int
    completed_at: str
    performance_analysis: Dict

class ExamGenerator:
    """考试题目生成器"""
    
    def __init__(self, knowledge_base_file: str):
        with open(knowledge_base_file, 'r', encoding='utf-8') as f:
            self.knowledge_base = json.load(f)
        
        # 题目模板
        self.question_templates = {
            'multiple_choice': [
                "关于{topic}，以下说法正确的是：",
                "在{topic}中，{concept}的主要特征是：",
                "{topic}的{aspect}包括：",
                "以下关于{topic}的描述，哪项是错误的？"
            ],
            'true_false': [
                "{statement}",
                "关于{topic}：{statement}",
                "在{topic}中，{statement}"
            ],
            'short_answer': [
                "请简述{topic}的主要{aspect}。",
                "解释{concept}在{topic}中的作用。",
                "列举{topic}的{count}个主要{aspect}。",
                "比较{concept1}和{concept2}的异同。"
            ],
            'essay': [
                "详细论述{topic}的{aspect}及其临床意义。",
                "分析{topic}在{context}中的重要性。",
                "评价{topic}的现状和发展趋势。"
            ]
        }
        
        # 干扰项生成策略
        self.distractor_strategies = [
            'similar_concept',
            'opposite_concept',
            'related_field',
            'common_misconception'
        ]
    
    def generate_questions(self, 
                         num_questions: int = 20,
                         difficulty_distribution: Optional[Dict[str, float]] = None,
                         category_weights: Optional[Dict[str, float]] = None,
                         question_types: Optional[Dict[str, float]] = None) -> List[Question]:
        """生成考试题目"""
        
        if difficulty_distribution is None:
            difficulty_distribution = {'basic': 0.5, 'intermediate': 0.3, 'advanced': 0.2}
        
        if category_weights is None:
            category_weights = {'medical_microbiology': 0.4, 'immunology': 0.4, 'general_medicine': 0.2}
        
        if question_types is None:
            question_types = {'multiple_choice': 0.6, 'true_false': 0.2, 'short_answer': 0.15, 'essay': 0.05}
        
        questions = []
        exam_topics = self.knowledge_base['exam_topics']
        
        # 按难度和类别分配题目数量
        for i in range(num_questions):
            # 选择难度
            difficulty = self._weighted_choice(difficulty_distribution)
            
            # 选择类别
            category = self._weighted_choice(category_weights)
            
            # 选择题目类型
            question_type = self._weighted_choice(question_types)
            
            # 选择主题
            suitable_topics = [
                topic for topic in exam_topics 
                if topic['category'] == category and topic['difficulty_level'] == difficulty
            ]
            
            if not suitable_topics:
                # 如果没有合适的主题，选择任意主题
                suitable_topics = [topic for topic in exam_topics if topic['category'] == category]
            
            if suitable_topics:
                topic = random.choice(suitable_topics)
                question = self._generate_single_question(topic, question_type, difficulty)
                if question:
                    questions.append(question)
        
        return questions
    
    def _weighted_choice(self, weights: Dict[str, float]) -> str:
        """基于权重随机选择"""
        choices = list(weights.keys())
        weights_list = list(weights.values())
        return random.choices(choices, weights=weights_list)[0]
    
    def _generate_single_question(self, topic: Dict, question_type: str, difficulty: str) -> Optional[Question]:
        """生成单个题目"""
        try:
            if question_type == 'multiple_choice':
                return self._generate_multiple_choice(topic, difficulty)
            elif question_type == 'true_false':
                return self._generate_true_false(topic, difficulty)
            elif question_type == 'short_answer':
                return self._generate_short_answer(topic, difficulty)
            elif question_type == 'essay':
                return self._generate_essay(topic, difficulty)
            else:
                return None
        except Exception as e:
            print(f"生成题目时出错: {e}")
            return None
    
    def _generate_multiple_choice(self, topic: Dict, difficulty: str) -> Question:
        """生成选择题"""
        main_topic = topic['main_topic']
        concepts = topic.get('key_concepts', [])
        
        # 选择一个概念作为考查重点
        target_concept = random.choice(concepts) if concepts else main_topic
        
        # 生成题目文本
        template = random.choice(self.question_templates['multiple_choice'])
        question_text = template.format(
            topic=main_topic,
            concept=target_concept,
            aspect="特点"
        )
        
        # 生成正确答案和干扰项
        correct_answer, options = self._generate_mc_options(target_concept, main_topic)
        
        return Question(
            id=str(uuid.uuid4()),
            type='multiple_choice',
            category=topic['category'],
            topic=main_topic,
            difficulty=difficulty,
            question_text=question_text,
            options=options,
            correct_answer=correct_answer,
            explanation=f"关于{target_concept}的正确理解是{correct_answer}",
            points=self._calculate_points(difficulty),
            tags=[target_concept, main_topic]
        )
    
    def _generate_mc_options(self, concept: str, topic: str) -> Tuple[str, List[str]]:
        """生成选择题选项"""
        # 根据概念生成选项
        concept_options = {
            'bacterial_structure': [
                "具有细胞壁、细胞膜和核质体",
                "具有线粒体和内质网",
                "具有叶绿体和液泡",
                "具有细胞核和细胞质"
            ],
            'viral_replication': [
                "需要宿主细胞的酶和核糖体",
                "可以在无细胞环境中复制",
                "只需要ATP即可完成复制",
                "通过二分裂方式复制"
            ],
            'immune_response': [
                "包括先天性免疫和适应性免疫",
                "只包括抗体介导的免疫",
                "只包括细胞介导的免疫",
                "只包括补体介导的免疫"
            ],
            'antibody': [
                "由B细胞产生的免疫球蛋白",
                "由T细胞产生的细胞因子",
                "由巨噬细胞产生的酶类",
                "由中性粒细胞产生的毒素"
            ]
        }
        
        # 如果有预定义选项，使用预定义选项
        if concept in concept_options:
            options = concept_options[concept].copy()
            correct_answer = options[0]
            random.shuffle(options)
        else:
            # 生成通用选项
            correct_answer = f"{concept}的正确特征"
            options = [
                correct_answer,
                f"{concept}的错误特征A",
                f"{concept}的错误特征B",
                f"{concept}的错误特征C"
            ]
            random.shuffle(options)
        
        return correct_answer, options
    
    def _generate_true_false(self, topic: Dict, difficulty: str) -> Question:
        """生成判断题"""
        main_topic = topic['main_topic']
        
        # 生成陈述句
        statements = [
            f"{main_topic}是医学微生物学的重要组成部分",
            f"{main_topic}在临床诊断中具有重要意义",
            f"{main_topic}的研究有助于疾病预防和治疗",
            f"{main_topic}与免疫系统密切相关"
        ]
        
        statement = random.choice(statements)
        is_true = random.choice([True, False])
        
        if not is_true:
            # 生成错误陈述
            statement = statement.replace("重要", "不重要").replace("有助于", "无助于")
        
        template = random.choice(self.question_templates['true_false'])
        question_text = template.format(topic=main_topic, statement=statement)
        
        return Question(
            id=str(uuid.uuid4()),
            type='true_false',
            category=topic['category'],
            topic=main_topic,
            difficulty=difficulty,
            question_text=question_text,
            options=['正确', '错误'],
            correct_answer='正确' if is_true else '错误',
            explanation=f"该陈述是{'正确' if is_true else '错误'}的",
            points=self._calculate_points(difficulty) // 2,
            tags=[main_topic]
        )
    
    def _generate_short_answer(self, topic: Dict, difficulty: str) -> Question:
        """生成简答题"""
        main_topic = topic['main_topic']
        aspects = ['定义', '特点', '分类', '功能', '意义']
        
        template = random.choice(self.question_templates['short_answer'])
        question_text = template.format(
            topic=main_topic,
            aspect=random.choice(aspects),
            concept=main_topic,
            count=random.choice(['3', '4', '5'])
        )
        
        # 生成参考答案
        key_points = topic.get('learning_objectives', [])
        if key_points:
            correct_answer = "; ".join(key_points[:3])
        else:
            correct_answer = f"{main_topic}的主要{random.choice(aspects)}包括..."
        
        return Question(
            id=str(uuid.uuid4()),
            type='short_answer',
            category=topic['category'],
            topic=main_topic,
            difficulty=difficulty,
            question_text=question_text,
            options=[],
            correct_answer=correct_answer,
            explanation=f"该题考查对{main_topic}的理解",
            points=self._calculate_points(difficulty) * 2,
            tags=[main_topic]
        )
    
    def _generate_essay(self, topic: Dict, difficulty: str) -> Question:
        """生成论述题"""
        main_topic = topic['main_topic']
        
        template = random.choice(self.question_templates['essay'])
        question_text = template.format(
            topic=main_topic,
            aspect="机制和临床应用",
            context="现代医学"
        )
        
        return Question(
            id=str(uuid.uuid4()),
            type='essay',
            category=topic['category'],
            topic=main_topic,
            difficulty=difficulty,
            question_text=question_text,
            options=[],
            correct_answer=f"关于{main_topic}的综合论述应包括基本概念、机制原理、临床意义等方面",
            explanation=f"该题考查对{main_topic}的综合理解和分析能力",
            points=self._calculate_points(difficulty) * 3,
            tags=[main_topic, '综合分析']
        )
    
    def _calculate_points(self, difficulty: str) -> int:
        """根据难度计算分值"""
        point_mapping = {
            'basic': 2,
            'intermediate': 3,
            'advanced': 5
        }
        return point_mapping.get(difficulty, 2)

class ExamManager:
    """考试管理器"""
    
    def __init__(self, data_dir: str = "exam_data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.exams_file = self.data_dir / "exams.json"
        self.results_file = self.data_dir / "results.json"
        
        self.exams = self._load_exams()
        self.results = self._load_results()
    
    def _load_exams(self) -> Dict[str, ExamSession]:
        """加载考试数据"""
        if self.exams_file.exists():
            with open(self.exams_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return {exam_id: ExamSession(**exam_data) for exam_id, exam_data in data.items()}
        return {}
    
    def _load_results(self) -> Dict[str, List[ExamResult]]:
        """加载考试结果"""
        if self.results_file.exists():
            with open(self.results_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                results = defaultdict(list)
                for exam_id, exam_results in data.items():
                    for result_data in exam_results:
                        results[exam_id].append(ExamResult(**result_data))
                return dict(results)
        return defaultdict(list)
    
    def create_exam(self, 
                   title: str,
                   description: str,
                   questions: List[Question],
                   time_limit: int = 60) -> str:
        """创建考试"""
        exam_id = str(uuid.uuid4())
        total_points = sum(q.points for q in questions)
        
        exam = ExamSession(
            id=exam_id,
            title=title,
            description=description,
            questions=questions,
            time_limit=time_limit,
            total_points=total_points,
            created_at=datetime.now().isoformat(),
            status='draft'
        )
        
        self.exams[exam_id] = exam
        self._save_exams()
        
        return exam_id
    
    def start_exam(self, exam_id: str) -> bool:
        """开始考试"""
        if exam_id in self.exams:
            self.exams[exam_id].status = 'active'
            self._save_exams()
            return True
        return False
    
    def submit_exam(self, exam_id: str, student_id: str, answers: List[StudentAnswer]) -> ExamResult:
        """提交考试答案"""
        exam = self.exams.get(exam_id)
        if not exam:
            raise ValueError(f"考试 {exam_id} 不存在")
        
        # 计算分数
        total_score = sum(answer.points_earned for answer in answers)
        percentage = (total_score / exam.total_points) * 100 if exam.total_points > 0 else 0
        
        # 分析表现
        performance_analysis = self._analyze_performance(exam, answers)
        
        result = ExamResult(
            exam_id=exam_id,
            student_id=student_id,
            answers=answers,
            total_score=total_score,
            max_score=exam.total_points,
            percentage=percentage,
            time_taken=sum(answer.time_spent for answer in answers),
            completed_at=datetime.now().isoformat(),
            performance_analysis=performance_analysis
        )
        
        self.results[exam_id].append(result)
        self._save_results()
        
        return result
    
    def _analyze_performance(self, exam: ExamSession, answers: List[StudentAnswer]) -> Dict:
        """分析考试表现"""
        analysis = {
            'category_performance': defaultdict(lambda: {'correct': 0, 'total': 0}),
            'difficulty_performance': defaultdict(lambda: {'correct': 0, 'total': 0}),
            'question_type_performance': defaultdict(lambda: {'correct': 0, 'total': 0}),
            'weak_areas': [],
            'strong_areas': [],
            'time_analysis': {}
        }
        
        question_map = {q.id: q for q in exam.questions}
        
        for answer in answers:
            question = question_map.get(answer.question_id)
            if question:
                # 按类别分析
                cat_perf = analysis['category_performance'][question.category]
                cat_perf['total'] += 1
                if answer.is_correct:
                    cat_perf['correct'] += 1
                
                # 按难度分析
                diff_perf = analysis['difficulty_performance'][question.difficulty]
                diff_perf['total'] += 1
                if answer.is_correct:
                    diff_perf['correct'] += 1
                
                # 按题型分析
                type_perf = analysis['question_type_performance'][question.type]
                type_perf['total'] += 1
                if answer.is_correct:
                    type_perf['correct'] += 1
        
        # 识别薄弱和强势领域
        for category, perf in analysis['category_performance'].items():
            accuracy = perf['correct'] / perf['total'] if perf['total'] > 0 else 0
            if accuracy < 0.6:
                analysis['weak_areas'].append(category)
            elif accuracy > 0.8:
                analysis['strong_areas'].append(category)
        
        return dict(analysis)
    
    def get_exam_statistics(self, exam_id: str) -> Dict:
        """获取考试统计信息"""
        exam_results = self.results.get(exam_id, [])
        if not exam_results:
            return {}
        
        scores = [result.percentage for result in exam_results]
        
        return {
            'participant_count': len(exam_results),
            'average_score': sum(scores) / len(scores),
            'highest_score': max(scores),
            'lowest_score': min(scores),
            'pass_rate': len([s for s in scores if s >= 60]) / len(scores) * 100,
            'score_distribution': {
                '90-100': len([s for s in scores if s >= 90]),
                '80-89': len([s for s in scores if 80 <= s < 90]),
                '70-79': len([s for s in scores if 70 <= s < 80]),
                '60-69': len([s for s in scores if 60 <= s < 70]),
                '0-59': len([s for s in scores if s < 60])
            }
        }
    
    def _save_exams(self):
        """保存考试数据"""
        data = {exam_id: asdict(exam) for exam_id, exam in self.exams.items()}
        with open(self.exams_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _save_results(self):
        """保存考试结果"""
        data = {exam_id: [asdict(result) for result in results] 
                for exam_id, results in self.results.items()}
        with open(self.results_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

def main():
    """主函数 - 演示考试系统功能"""
    print("🎓 智能考试系统")
    print("=" * 50)
    
    # 初始化系统
    knowledge_base_file = "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/knowledge_base.json"
    
    if not Path(knowledge_base_file).exists():
        print("❌ 找不到知识库文件，请先运行 document_content_parser.py")
        return
    
    # 创建考试生成器和管理器
    exam_generator = ExamGenerator(knowledge_base_file)
    exam_manager = ExamManager("/Users/<USER>/phc-mbse/phc-mbse/class-test-system/exam_data")
    
    # 生成示例考试
    print("📝 生成考试题目...")
    questions = exam_generator.generate_questions(
        num_questions=20,
        difficulty_distribution={'basic': 0.6, 'intermediate': 0.3, 'advanced': 0.1},
        category_weights={'medical_microbiology': 0.5, 'immunology': 0.4, 'general_medicine': 0.1}
    )
    
    print(f"✅ 生成了 {len(questions)} 道题目")
    
    # 创建考试
    exam_id = exam_manager.create_exam(
        title="MI2 医学微生物学与免疫学期末考试",
        description="涵盖医学微生物学和免疫学核心概念的综合考试",
        questions=questions,
        time_limit=120
    )
    
    print(f"📋 创建考试成功，考试ID: {exam_id}")
    
    # 显示考试信息
    exam = exam_manager.exams[exam_id]
    print(f"📊 考试信息:")
    print(f"- 标题: {exam.title}")
    print(f"- 题目数量: {len(exam.questions)}")
    print(f"- 总分: {exam.total_points}")
    print(f"- 时间限制: {exam.time_limit} 分钟")
    
    # 显示题目分布
    type_counts = defaultdict(int)
    difficulty_counts = defaultdict(int)
    category_counts = defaultdict(int)
    
    for q in questions:
        type_counts[q.type] += 1
        difficulty_counts[q.difficulty] += 1
        category_counts[q.category] += 1
    
    print(f"\n📈 题目分布:")
    print(f"题型分布: {dict(type_counts)}")
    print(f"难度分布: {dict(difficulty_counts)}")
    print(f"类别分布: {dict(category_counts)}")
    
    # 显示几个示例题目
    print(f"\n📝 示例题目:")
    for i, q in enumerate(questions[:3]):
        print(f"\n{i+1}. [{q.type}] {q.question_text}")
        if q.options:
            for j, option in enumerate(q.options):
                print(f"   {chr(65+j)}. {option}")
        print(f"   正确答案: {q.correct_answer}")
        print(f"   分值: {q.points}分")

if __name__ == "__main__":
    main()
