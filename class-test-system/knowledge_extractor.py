#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MI2 知识点提取器
从classes目录下的文件中提取知识点，生成考试题库
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class KnowledgePoint:
    """知识点数据结构"""
    id: str
    title: str
    content: str
    category: str
    subcategory: str
    difficulty: str
    keywords: List[str]
    source_file: str
    page_number: Optional[int] = None

@dataclass
class ExamQuestion:
    """考试题目数据结构"""
    id: str
    type: str
    difficulty: str
    topic: str
    question: str
    question_en: str
    options: Optional[List[str]] = None
    correct_answer: Optional[int] = None
    correct_answers: Optional[List[int]] = None
    answer_key: Optional[str] = None
    answer_key_en: Optional[str] = None
    explanation: Optional[str] = None
    explanation_en: Optional[str] = None
    module: Optional[str] = None

class KnowledgeExtractor:
    """知识点提取器"""
    
    def __init__(self, classes_dir: str = "classes"):
        self.classes_dir = Path(classes_dir)
        self.knowledge_points = []
        self.exam_questions = []
        
        # 定义课程模块映射
        self.module_mapping = {
            "microbiology": {
                "name": "医学微生物学",
                "name_en": "Medical Microbiology", 
                "description": "细菌学、病毒学、真菌学、寄生虫学",
                "weight": 0.4,
                "keywords": ["bacteria", "virus", "fungi", "parasite", "细菌", "病毒", "真菌", "寄生虫", "antimicrobial", "pathogenesis", "respiratory", "gastro", "urogenital"]
            },
            "innate_immunity": {
                "name": "先天免疫",
                "name_en": "Innate Immunity",
                "description": "炎症反应、补体系统、细胞介导免疫",
                "weight": 0.15,
                "keywords": ["innate", "inflammation", "complement", "先天", "炎症", "补体", "dendritic", "immune system"]
            },
            "adaptive_immunity": {
                "name": "适应性免疫", 
                "name_en": "Adaptive Immunity",
                "description": "T细胞免疫、B细胞免疫、MHC系统、细胞因子",
                "weight": 0.25,
                "keywords": ["adaptive", "T cell", "B cell", "MHC", "cytokine", "适应性", "T细胞", "B细胞", "细胞因子", "lymphocyte", "antibod", "antigen", "activation", "mediate"]
            },
            "immune_pathology": {
                "name": "免疫病理学",
                "name_en": "Immune Pathology", 
                "description": "超敏反应、自身免疫、免疫缺陷、肿瘤免疫",
                "weight": 0.15,
                "keywords": ["hypersensitivity", "autoimmune", "immunodeficiency", "tumor", "超敏", "自身免疫", "免疫缺陷", "肿瘤", "tolerance"]
            },
            "clinical_immunology": {
                "name": "临床免疫学",
                "name_en": "Clinical Immunology",
                "description": "疫苗学、免疫治疗、免疫技术、免疫耐受", 
                "weight": 0.05,
                "keywords": ["vaccine", "immunotherapy", "tolerance", "疫苗", "免疫治疗", "免疫耐受", "vaccination"]
            }
        }
        
    def analyze_all_files(self) -> Dict[str, Any]:
        """分析所有课程文件"""
        if not self.classes_dir.exists():
            raise FileNotFoundError(f"课程目录 {self.classes_dir} 不存在")
            
        logger.info(f"开始分析课程目录: {self.classes_dir}")
        
        # 获取所有课程文件
        course_files = []
        for ext in ['*.pdf', '*.pptx', '*.ppsx']:
            course_files.extend(self.classes_dir.glob(ext))
            
        logger.info(f"找到 {len(course_files)} 个课程文件")
        
        # 分析每个文件
        for filepath in course_files:
            logger.info(f"分析文件: {filepath.name}")
            knowledge_points = self.extract_knowledge_from_filename(filepath)
            self.knowledge_points.extend(knowledge_points)
            
            # 生成考试题目
            questions = self.generate_questions_from_knowledge(knowledge_points)
            self.exam_questions.extend(questions)
            
        # 生成分析报告
        analysis_report = {
            "total_files": len(course_files),
            "total_knowledge_points": len(self.knowledge_points),
            "total_questions": len(self.exam_questions),
            "modules": {},
            "file_analysis": []
        }
        
        # 统计各模块知识点
        for module_key, module_info in self.module_mapping.items():
            module_kps = [kp for kp in self.knowledge_points if kp.category == module_key]
            module_questions = [q for q in self.exam_questions if q.module == module_key]
            analysis_report["modules"][module_key] = {
                "name": module_info["name"],
                "name_en": module_info["name_en"],
                "knowledge_points_count": len(module_kps),
                "questions_count": len(module_questions),
                "weight": module_info["weight"]
            }
            
        # 文件分析详情
        for filepath in course_files:
            module, topic, file_type = self.analyze_filename(filepath.name)
            analysis_report["file_analysis"].append({
                "filename": filepath.name,
                "module": module,
                "topic": topic,
                "type": file_type,
                "size_mb": round(filepath.stat().st_size / (1024*1024), 2)
            })
            
        return analysis_report
    
    def analyze_filename(self, filename: str) -> Tuple[str, str, str]:
        """分析文件名，提取模块、主题和类型信息"""
        filename_lower = filename.lower()
        
        # 确定模块
        module = "general"
        for mod_key, mod_info in self.module_mapping.items():
            for keyword in mod_info["keywords"]:
                if keyword.lower() in filename_lower:
                    module = mod_key
                    break
            if module != "general":
                break
                
        # 提取主题
        topic = filename.replace('.pdf', '').replace('.pptx', '').replace('.ppsx', '')
        
        # 确定文件类型
        file_type = "lecture"
        if "exam" in filename_lower:
            file_type = "exam"
        elif any(x in filename_lower for x in ["l1", "l2", "l3", "l4", "l5"]):
            file_type = "lecture"
            
        return module, topic, file_type
    
    def extract_knowledge_from_filename(self, filepath: Path) -> List[KnowledgePoint]:
        """从文件名提取基础知识点信息"""
        filename = filepath.name
        module, topic, file_type = self.analyze_filename(filename)
        
        knowledge_points = []
        
        # 基于文件名生成知识点
        kp_id = f"kp_{len(self.knowledge_points):04d}"
        
        # 根据文件名推断内容
        content_hints = self._generate_content_hints(filename, module)
        
        for i, hint in enumerate(content_hints):
            kp = KnowledgePoint(
                id=f"{kp_id}_{i:02d}",
                title=hint["title"],
                content=hint["content"], 
                category=module,
                subcategory=hint["subcategory"],
                difficulty=hint["difficulty"],
                keywords=hint["keywords"],
                source_file=filename
            )
            knowledge_points.append(kp)
            
        return knowledge_points
    
    def _generate_content_hints(self, filename: str, module: str) -> List[Dict[str, Any]]:
        """基于文件名生成内容提示"""
        hints = []
        filename_lower = filename.lower()
        
        # 根据具体文件名生成知识点
        if "bacterial diversity" in filename_lower or "l1.2" in filename_lower:
            hints.extend([
                {
                    "title": "细菌多样性与结构",
                    "content": "细菌具有多样的形态和结构，包括球菌、杆菌、螺旋菌等。细菌细胞壁主要由肽聚糖组成。",
                    "subcategory": "细菌学",
                    "difficulty": "basic",
                    "keywords": ["细菌", "多样性", "结构", "肽聚糖"]
                }
            ])
        elif "bacterial growth" in filename_lower or "l1.3" in filename_lower:
            hints.extend([
                {
                    "title": "细菌生长与代谢",
                    "content": "细菌通过二分裂进行繁殖，生长曲线包括迟缓期、对数期、稳定期和衰亡期。",
                    "subcategory": "细菌学",
                    "difficulty": "intermediate",
                    "keywords": ["细菌生长", "代谢", "生长曲线"]
                }
            ])
        elif "bacterial genetics" in filename_lower or "l2.1" in filename_lower:
            hints.extend([
                {
                    "title": "细菌遗传学",
                    "content": "细菌遗传物质包括染色体DNA和质粒。基因转移方式包括转化、转导和接合。",
                    "subcategory": "细菌学",
                    "difficulty": "advanced",
                    "keywords": ["细菌遗传", "质粒", "基因转移"]
                }
            ])
        elif "virus" in filename_lower and "diversity" in filename_lower:
            hints.extend([
                {
                    "title": "病毒多样性与生物学",
                    "content": "病毒具有多样的结构和基因组类型，包括DNA病毒和RNA病毒，有包膜和无包膜病毒。",
                    "subcategory": "病毒学",
                    "difficulty": "basic",
                    "keywords": ["病毒", "多样性", "DNA", "RNA"]
                }
            ])
        elif "virus entry" in filename_lower:
            hints.extend([
                {
                    "title": "病毒入侵机制",
                    "content": "病毒通过受体结合、膜融合或胞吞作用进入宿主细胞。",
                    "subcategory": "病毒学",
                    "difficulty": "intermediate",
                    "keywords": ["病毒入侵", "受体结合", "膜融合"]
                }
            ])
        elif "immune system overview" in filename_lower:
            hints.extend([
                {
                    "title": "免疫系统概述",
                    "content": "免疫系统包括先天免疫和适应性免疫，由免疫器官、免疫细胞和免疫分子组成。",
                    "subcategory": "免疫学基础",
                    "difficulty": "basic",
                    "keywords": ["免疫系统", "先天免疫", "适应性免疫"]
                }
            ])
        elif "hypersensitivity" in filename_lower:
            hints.extend([
                {
                    "title": "超敏反应",
                    "content": "超敏反应分为I型（IgE介导）、II型（细胞毒性）、III型（免疫复合物）、IV型（迟发性）。",
                    "subcategory": "免疫病理",
                    "difficulty": "advanced",
                    "keywords": ["超敏反应", "IgE", "细胞毒性", "免疫复合物"]
                }
            ])
        elif "autoimmune" in filename_lower:
            hints.extend([
                {
                    "title": "自身免疫疾病",
                    "content": "自身免疫疾病是免疫系统错误攻击自身组织的疾病，包括器官特异性和系统性疾病。",
                    "subcategory": "免疫病理",
                    "difficulty": "advanced",
                    "keywords": ["自身免疫", "器官特异性", "系统性"]
                }
            ])
        
        # 如果没有特定提示，生成通用提示
        if not hints:
            hints.append({
                "title": f"课程内容 - {filename.replace('.pdf', '').replace('.pptx', '').replace('.ppsx', '')}",
                "content": f"来自文件 {filename} 的课程内容，包含重要的医学微生物学和免疫学知识点。",
                "subcategory": "通用",
                "difficulty": "basic",
                "keywords": ["课程内容", "医学", "微生物学", "免疫学"]
            })
            
        return hints

    def generate_questions_from_knowledge(self, knowledge_points: List[KnowledgePoint]) -> List[ExamQuestion]:
        """从知识点生成考试题目"""
        questions = []

        for kp in knowledge_points:
            # 为每个知识点生成1-3个题目
            question_templates = self._get_question_templates_for_knowledge(kp)

            for i, template in enumerate(question_templates):
                question = ExamQuestion(
                    id=f"q_{len(self.exam_questions) + len(questions):04d}_{i:02d}",
                    type=template["type"],
                    difficulty=template["difficulty"],
                    topic=kp.subcategory,
                    question=template["question"],
                    question_en=template["question_en"],
                    options=template.get("options"),
                    correct_answer=template.get("correct_answer"),
                    correct_answers=template.get("correct_answers"),
                    answer_key=template.get("answer_key"),
                    answer_key_en=template.get("answer_key_en"),
                    explanation=template.get("explanation"),
                    explanation_en=template.get("explanation_en"),
                    module=kp.category
                )
                questions.append(question)

        return questions

    def _get_question_templates_for_knowledge(self, kp: KnowledgePoint) -> List[Dict[str, Any]]:
        """为知识点生成题目模板"""
        templates = []

        if kp.category == "microbiology":
            if "细菌" in kp.title or "bacteria" in kp.title.lower():
                templates.extend([
                    {
                        "type": "multiple_choice",
                        "difficulty": "basic",
                        "question": "细菌细胞壁的主要成分是什么？",
                        "question_en": "What is the main component of bacterial cell walls?",
                        "options": [
                            "肽聚糖 (Peptidoglycan)",
                            "纤维素 (Cellulose)",
                            "几丁质 (Chitin)",
                            "磷脂 (Phospholipids)"
                        ],
                        "correct_answer": 0,
                        "explanation": "肽聚糖是细菌细胞壁的主要结构成分，为细菌提供形状和保护。",
                        "explanation_en": "Peptidoglycan is the main structural component of bacterial cell walls."
                    },
                    {
                        "type": "multiple_select",
                        "difficulty": "intermediate",
                        "question": "细菌的致病因子包括哪些？（多选）",
                        "question_en": "Which are bacterial virulence factors? (Multiple select)",
                        "options": [
                            "外毒素 (Exotoxins)",
                            "内毒素 (Endotoxins)",
                            "荚膜 (Capsule)",
                            "鞭毛 (Flagella)",
                            "质粒 (Plasmids)"
                        ],
                        "correct_answers": [0, 1, 2],
                        "explanation": "外毒素、内毒素和荚膜都是重要的细菌致病因子。",
                        "explanation_en": "Exotoxins, endotoxins, and capsules are important bacterial virulence factors."
                    }
                ])
            elif "病毒" in kp.title or "virus" in kp.title.lower():
                templates.extend([
                    {
                        "type": "multiple_choice",
                        "difficulty": "basic",
                        "question": "病毒的基本结构包括：",
                        "question_en": "The basic structure of viruses includes:",
                        "options": [
                            "核酸和蛋白质外壳",
                            "细胞壁和细胞膜",
                            "细胞核和细胞质",
                            "线粒体和核糖体"
                        ],
                        "correct_answer": 0,
                        "explanation": "病毒的基本结构由核酸（DNA或RNA）和蛋白质外壳组成。",
                        "explanation_en": "Viruses consist of nucleic acid (DNA or RNA) and a protein coat."
                    }
                ])

        elif kp.category == "adaptive_immunity":
            if "MHC" in kp.title or "抗原呈递" in kp.title:
                templates.extend([
                    {
                        "type": "multiple_choice",
                        "difficulty": "advanced",
                        "question": "MHC I类分子主要呈递什么类型的抗原？",
                        "question_en": "What type of antigens do MHC class I molecules primarily present?",
                        "options": [
                            "内源性抗原",
                            "外源性抗原",
                            "自身抗原",
                            "肿瘤抗原"
                        ],
                        "correct_answer": 0,
                        "explanation": "MHC I类分子主要呈递内源性抗原给CD8+ T细胞。",
                        "explanation_en": "MHC class I molecules primarily present endogenous antigens to CD8+ T cells."
                    }
                ])
            elif "T细胞" in kp.title or "B细胞" in kp.title:
                templates.extend([
                    {
                        "type": "short_answer",
                        "difficulty": "intermediate",
                        "question": "简述T细胞和B细胞在适应性免疫中的主要功能差异。",
                        "question_en": "Briefly describe the main functional differences between T cells and B cells in adaptive immunity.",
                        "answer_key": "T细胞主要介导细胞免疫，包括细胞毒性T细胞杀伤靶细胞和辅助T细胞调节免疫反应；B细胞主要介导体液免疫，分化为浆细胞产生抗体。",
                        "answer_key_en": "T cells mediate cellular immunity, including cytotoxic T cells killing target cells and helper T cells regulating immune responses; B cells mediate humoral immunity by differentiating into plasma cells that produce antibodies."
                    }
                ])

        elif kp.category == "immune_pathology":
            if "超敏反应" in kp.title or "hypersensitivity" in kp.title.lower():
                templates.extend([
                    {
                        "type": "multiple_choice",
                        "difficulty": "intermediate",
                        "question": "I型超敏反应主要由哪种抗体介导？",
                        "question_en": "Which antibody primarily mediates Type I hypersensitivity?",
                        "options": [
                            "IgE",
                            "IgG",
                            "IgM",
                            "IgA"
                        ],
                        "correct_answer": 0,
                        "explanation": "I型超敏反应（过敏反应）主要由IgE抗体介导。",
                        "explanation_en": "Type I hypersensitivity (allergic reaction) is primarily mediated by IgE antibodies."
                    }
                ])

        # 如果没有特定模板，生成通用模板
        if not templates:
            templates.append({
                "type": "multiple_choice",
                "difficulty": "basic",
                "question": f"关于{kp.title}，以下哪项描述是正确的？",
                "question_en": f"Regarding {kp.title}, which description is correct?",
                "options": [
                    "选项A - 需要根据具体内容填写",
                    "选项B - 需要根据具体内容填写",
                    "选项C - 需要根据具体内容填写",
                    "选项D - 需要根据具体内容填写"
                ],
                "correct_answer": 0,
                "explanation": f"关于{kp.title}的详细解释需要根据具体内容补充。",
                "explanation_en": f"Detailed explanation about {kp.title} needs to be supplemented based on specific content."
            })

        return templates

    def save_results(self, output_dir: str = "results"):
        """保存分析结果"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 保存知识点数据
        knowledge_points_data = [asdict(kp) for kp in self.knowledge_points]
        with open(output_path / "extracted_knowledge_points.json", 'w', encoding='utf-8') as f:
            json.dump(knowledge_points_data, f, ensure_ascii=False, indent=2)

        # 保存题目数据
        questions_data = [asdict(q) for q in self.exam_questions]
        with open(output_path / "generated_questions.json", 'w', encoding='utf-8') as f:
            json.dump(questions_data, f, ensure_ascii=False, indent=2)

        # 按模块保存题库
        for module_key, module_info in self.module_mapping.items():
            module_questions = [q for q in self.exam_questions if q.module == module_key]
            if module_questions:
                module_data = {
                    "module": module_key,
                    "name": module_info["name"],
                    "name_en": module_info["name_en"],
                    "description": module_info["description"],
                    "questions": [asdict(q) for q in module_questions]
                }

                filename = f"question_bank_{module_key}.json"
                with open(output_path / filename, 'w', encoding='utf-8') as f:
                    json.dump(module_data, f, ensure_ascii=False, indent=2)

        logger.info(f"结果已保存到 {output_path}")

        return {
            "knowledge_points_file": str(output_path / "extracted_knowledge_points.json"),
            "questions_file": str(output_path / "generated_questions.json"),
            "output_directory": str(output_path)
        }

def main():
    """主函数"""
    try:
        extractor = KnowledgeExtractor()

        # 分析所有文件
        report = extractor.analyze_all_files()

        # 保存结果
        saved_files = extractor.save_results()

        # 打印摘要
        print("\n" + "="*60)
        print("MI2 知识点提取和题库生成完成")
        print("="*60)
        print(f"总文件数: {report['total_files']}")
        print(f"总知识点数: {report['total_knowledge_points']}")
        print(f"总题目数: {report['total_questions']}")
        print("\n模块分布:")
        for module_key, module_data in report['modules'].items():
            print(f"  {module_data['name']}: {module_data['knowledge_points_count']} 个知识点, {module_data['questions_count']} 道题目")

        print(f"\n结果文件:")
        for desc, filepath in saved_files.items():
            print(f"  {desc}: {filepath}")

    except Exception as e:
        logger.error(f"处理过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
