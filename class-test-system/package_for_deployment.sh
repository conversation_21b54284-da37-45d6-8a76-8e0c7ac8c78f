#!/bin/bash

# MI2 考试系统部署打包脚本
echo "🧬 MI2 考试系统 - 创建部署包"
echo "=============================="

# 创建部署包目录
PACKAGE_DIR="mi2-exam-deployment"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
PACKAGE_NAME="mi2-exam-system-${TIMESTAMP}"

echo "📦 创建部署包: ${PACKAGE_NAME}"

# 清理之前的包
rm -rf ${PACKAGE_DIR}
rm -f *.tar.gz

# 创建打包目录
mkdir -p ${PACKAGE_DIR}

echo "📁 复制系统文件..."

# 复制核心文件
cp config.json ${PACKAGE_DIR}/
cp exam_generator.py ${PACKAGE_DIR}/
cp app_production.py ${PACKAGE_DIR}/app.py  # 使用生产版本作为主应用
cp requirements.txt ${PACKAGE_DIR}/
cp deploy.sh ${PACKAGE_DIR}/
cp README.md ${PACKAGE_DIR}/

# 复制题库
cp -r question_banks ${PACKAGE_DIR}/

# 复制模板
cp -r templates ${PACKAGE_DIR}/

# 创建部署说明文件
cat > ${PACKAGE_DIR}/DEPLOYMENT_GUIDE.md << 'EOF'
# MI2 考试系统服务器部署指南

## 🚀 快速部署步骤

### 1. 上传文件到服务器
```bash
# 解压部署包
tar -xzf mi2-exam-system-*.tar.gz
cd mi2-exam-system-*

# 移动到部署目录
sudo mv * /opt/mi2-exam-system/
cd /opt/mi2-exam-system
```

### 2. 运行部署脚本
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本（需要root权限）
sudo ./deploy.sh
```

### 3. 启动服务
```bash
# 启动服务
./manage.sh start

# 检查状态
./manage.sh status
```

### 4. 访问系统
- 浏览器访问：http://***********
- 健康检查：http://***********/health

## 🔧 管理命令

- `./manage.sh start` - 启动服务
- `./manage.sh stop` - 停止服务  
- `./manage.sh restart` - 重启服务
- `./manage.sh status` - 查看状态
- `./manage.sh logs` - 查看日志

## 📋 注意事项

1. 确保服务器已开放80端口
2. 确保Python3已安装
3. 如有问题查看日志：`./manage.sh logs`

## 🔒 安全建议

1. 修改默认SECRET_KEY
2. 配置HTTPS证书
3. 定期备份题库和结果数据
4. 监控系统资源使用情况

EOF

echo "⚙️ 创建启动脚本..."

# 创建简化的启动脚本
cat > ${PACKAGE_DIR}/quick_start.sh << 'EOF'
#!/bin/bash

echo "🧬 MI2 考试系统 - 快速启动"
echo "=========================="

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用 sudo 运行此脚本"
    exit 1
fi

echo "📦 安装依赖..."
pip3 install -r requirements.txt

echo "📁 创建目录..."
mkdir -p exams results static/css static/js

echo "🚀 启动服务..."
python3 app.py

EOF

chmod +x ${PACKAGE_DIR}/quick_start.sh

echo "📄 创建版本信息..."

# 创建版本信息文件
cat > ${PACKAGE_DIR}/VERSION << EOF
MI2 Medical Immunology 2 考试系统
版本: 1.0.0
构建时间: $(date)
部署目标: ***********
题库模块: 3个 (微生物学、适应性免疫、免疫病理学)
题目总数: 28道
支持题型: 单选、多选、简答、论述、病例分析
EOF

echo "🗂️ 文件清单:"
echo "----------------------------------------"
ls -la ${PACKAGE_DIR}/
echo "----------------------------------------"

echo "📦 创建压缩包..."
tar -czf ${PACKAGE_NAME}.tar.gz ${PACKAGE_DIR}

echo "✅ 部署包创建完成！"
echo ""
echo "📋 部署步骤："
echo "1. 将 ${PACKAGE_NAME}.tar.gz 上传到服务器"
echo "2. 在服务器上解压：tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "3. 运行部署脚本：sudo ./deploy.sh"
echo "4. 启动服务：./manage.sh start"
echo "5. 访问：http://***********"
echo ""
echo "📊 包信息："
echo "- 文件名: ${PACKAGE_NAME}.tar.gz"
echo "- 大小: $(du -h ${PACKAGE_NAME}.tar.gz | cut -f1)"
echo "- 内容: $(ls ${PACKAGE_DIR} | wc -l) 个文件/目录"
echo ""

# 清理临时目录
rm -rf ${PACKAGE_DIR}

echo "🎉 准备就绪！现在可以上传 ${PACKAGE_NAME}.tar.gz 到服务器了。" 