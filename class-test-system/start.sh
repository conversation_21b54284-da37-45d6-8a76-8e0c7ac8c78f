#!/bin/bash

# MI2 考试系统快速启动脚本
echo "🧬 MI2 Medical Immunology 2 考试系统"
echo "=================================="
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查pip是否安装
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖包..."
pip3 install -r requirements.txt

# 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p exams
mkdir -p results

# 启动系统
echo "🚀 启动考试系统..."
echo "系统将在 http://localhost:5000 启动"
echo "按 Ctrl+C 停止系统"
echo

python3 app.py 