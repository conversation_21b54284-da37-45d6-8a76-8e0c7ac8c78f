<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MI2 增强版考试系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .stats-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
        }
        .module-badge {
            background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            margin: 5px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-microscope me-2"></i>
                MI2 增强版考试系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能特色</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#stats">系统统计</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/start_exam">开始考试</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主页面 -->
    <section class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">
                <i class="fas fa-brain me-3"></i>
                MI2 医学免疫学智能考试系统
            </h1>
            <p class="lead mb-5">
                基于人工智能的个性化考试平台，涵盖医学微生物学与免疫学全部知识点
            </p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="/start_exam" class="btn btn-light btn-lg px-4 me-md-2">
                            <i class="fas fa-play me-2"></i>开始考试
                        </a>
                        <a href="#stats" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-chart-bar me-2"></i>查看统计
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能特色 -->
    <section id="features" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-star text-warning me-2"></i>
                系统特色功能
            </h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">智能题目生成</h5>
                            <p class="card-text">
                                基于课程内容自动提取知识点，生成多样化题目类型，包括单选、多选、简答、论述和病例分析。
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-user-graduate fa-3x text-success mb-3"></i>
                            <h5 class="card-title">个性化考试</h5>
                            <p class="card-text">
                                根据学生的学习进度和薄弱环节，智能调整题目难度和内容分布，提供个性化的考试体验。
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                            <h5 class="card-title">学习分析</h5>
                            <p class="card-text">
                                详细的成绩分析和学习建议，帮助学生了解知识掌握情况，制定针对性的学习计划。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 系统统计 -->
    <section id="stats" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-database text-primary me-2"></i>
                系统数据统计
            </h2>
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <i class="fas fa-lightbulb fa-2x mb-3"></i>
                            <h3 class="card-title">{{ stats.knowledge_points or 0 }}</h3>
                            <p class="card-text">知识点</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <i class="fas fa-question-circle fa-2x mb-3"></i>
                            <h3 class="card-title">{{ stats.total_questions or 0 }}</h3>
                            <p class="card-text">题目总数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <i class="fas fa-folder fa-2x mb-3"></i>
                            <h3 class="card-title">{{ stats.question_banks or 0 }}</h3>
                            <p class="card-text">题库模块</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card stats-card text-center">
                        <div class="card-body">
                            <i class="fas fa-file-pdf fa-2x mb-3"></i>
                            <h3 class="card-title">38</h3>
                            <p class="card-text">课程文件</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 课程模块 -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-book-medical text-success me-2"></i>
                课程模块覆盖
            </h2>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-bacteria text-danger me-2"></i>
                                医学微生物学 (40%)
                            </h5>
                            <p class="card-text">细菌学、病毒学、真菌学、寄生虫学</p>
                            <div class="module-badge">细菌多样性</div>
                            <div class="module-badge">病毒复制</div>
                            <div class="module-badge">真菌感染</div>
                            <div class="module-badge">抗微生物治疗</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-shield-alt text-primary me-2"></i>
                                适应性免疫 (25%)
                            </h5>
                            <p class="card-text">T细胞免疫、B细胞免疫、MHC系统、细胞因子</p>
                            <div class="module-badge">T细胞激活</div>
                            <div class="module-badge">B细胞抗体</div>
                            <div class="module-badge">MHC复合体</div>
                            <div class="module-badge">细胞因子</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                免疫病理学 (15%)
                            </h5>
                            <p class="card-text">超敏反应、自身免疫、免疫缺陷、肿瘤免疫</p>
                            <div class="module-badge">超敏反应</div>
                            <div class="module-badge">自身免疫</div>
                            <div class="module-badge">免疫缺陷</div>
                            <div class="module-badge">肿瘤免疫</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-heartbeat text-info me-2"></i>
                                先天免疫 (15%)
                            </h5>
                            <p class="card-text">炎症反应、补体系统、细胞介导免疫</p>
                            <div class="module-badge">先天免疫</div>
                            <div class="module-badge">炎症反应</div>
                            <div class="module-badge">补体系统</div>
                            <div class="module-badge">树突细胞</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 开始考试按钮 -->
    <section class="py-5 bg-primary text-white text-center">
        <div class="container">
            <h2 class="mb-4">准备好开始您的MI2学习之旅了吗？</h2>
            <p class="lead mb-4">选择适合您的考试类型，开始个性化的学习体验</p>
            <a href="/start_exam" class="btn btn-light btn-lg px-5">
                <i class="fas fa-rocket me-2"></i>
                立即开始考试
            </a>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">
                <i class="fas fa-copyright me-1"></i>
                2024 MI2 医学免疫学考试系统 - 基于人工智能的个性化学习平台
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后获取系统状态
        document.addEventListener('DOMContentLoaded', function() {
            fetch('/api/system_status')
                .then(response => response.json())
                .then(data => {
                    console.log('系统状态:', data);
                })
                .catch(error => {
                    console.error('获取系统状态失败:', error);
                });
        });
    </script>
</body>
</html>
