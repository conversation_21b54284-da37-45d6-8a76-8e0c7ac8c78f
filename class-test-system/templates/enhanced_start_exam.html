<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开始考试 - MI2 增强版考试系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .exam-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .exam-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        .exam-type-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .diagnostic { background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%); }
        .regular { background: linear-gradient(45deg, #a8edea 0%, #fed6e3 100%); }
        .final { background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%); }
        .form-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 40px;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-microscope me-2"></i>
                MI2 增强版考试系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home me-1"></i>返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- 页面标题 -->
                <div class="text-center mb-5">
                    <h1 class="display-5 fw-bold text-primary">
                        <i class="fas fa-clipboard-check me-3"></i>
                        开始考试
                    </h1>
                    <p class="lead text-muted">选择考试类型并输入您的学生信息</p>
                </div>

                <!-- 考试类型选择 -->
                <div class="row mb-5">
                    <div class="col-md-4 mb-4">
                        <div class="card exam-card h-100" data-exam-type="diagnostic">
                            <div class="exam-type-badge diagnostic text-white">诊断性</div>
                            <div class="card-body text-center">
                                <i class="fas fa-stethoscope fa-3x text-info mb-3"></i>
                                <h5 class="card-title">诊断性考试</h5>
                                <p class="card-text">
                                    <strong>20道题 | 30分钟</strong><br>
                                    快速评估您的知识水平，识别薄弱环节
                                </p>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>基础知识评估</li>
                                    <li><i class="fas fa-check text-success me-2"></i>个性化分析</li>
                                    <li><i class="fas fa-check text-success me-2"></i>学习建议</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card exam-card h-100" data-exam-type="regular">
                            <div class="exam-type-badge regular text-dark">常规</div>
                            <div class="card-body text-center">
                                <i class="fas fa-book-open fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">常规考试</h5>
                                <p class="card-text">
                                    <strong>50道题 | 120分钟</strong><br>
                                    全面的知识点覆盖，适合期中复习
                                </p>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>全面知识覆盖</li>
                                    <li><i class="fas fa-check text-success me-2"></i>多种题型</li>
                                    <li><i class="fas fa-check text-success me-2"></i>详细反馈</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card exam-card h-100" data-exam-type="final">
                            <div class="exam-type-badge final text-dark">期末</div>
                            <div class="card-body text-center">
                                <i class="fas fa-graduation-cap fa-3x text-warning mb-3"></i>
                                <h5 class="card-title">期末考试</h5>
                                <p class="card-text">
                                    <strong>80道题 | 180分钟</strong><br>
                                    综合性考试，涵盖全学期内容
                                </p>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>综合性评估</li>
                                    <li><i class="fas fa-check text-success me-2"></i>高难度题目</li>
                                    <li><i class="fas fa-check text-success me-2"></i>完整报告</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 考试表单 -->
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="form-container">
                            <h3 class="text-center mb-4">
                                <i class="fas fa-user-edit me-2"></i>
                                考试信息
                            </h3>
                            <form id="examForm" method="POST">
                                <div class="mb-4">
                                    <label for="student_id" class="form-label">
                                        <i class="fas fa-id-card me-2"></i>学生ID
                                    </label>
                                    <input type="text" class="form-control form-control-lg" 
                                           id="student_id" name="student_id" 
                                           placeholder="请输入您的学生ID (例如: student_001)" 
                                           required>
                                    <div class="form-text text-light">
                                        请输入您的唯一学生标识符
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="exam_type" class="form-label">
                                        <i class="fas fa-clipboard-list me-2"></i>考试类型
                                    </label>
                                    <select class="form-select form-select-lg" id="exam_type" name="exam_type" required>
                                        <option value="">请选择考试类型</option>
                                        <option value="diagnostic">诊断性考试 (20题, 30分钟)</option>
                                        <option value="regular">常规考试 (50题, 120分钟)</option>
                                        <option value="final">期末考试 (80题, 180分钟)</option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="agree_terms" required>
                                        <label class="form-check-label" for="agree_terms">
                                            我已阅读并同意考试规则和注意事项
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-light btn-lg">
                                        <i class="fas fa-play me-2"></i>
                                        开始考试
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 考试须知 -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    考试须知
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-clock text-primary me-2"></i>时间管理</h6>
                                        <ul>
                                            <li>请合理分配答题时间</li>
                                            <li>系统会自动保存您的答案</li>
                                            <li>超时后将自动提交</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-shield-alt text-success me-2"></i>考试环境</h6>
                                        <ul>
                                            <li>请确保网络连接稳定</li>
                                            <li>建议使用Chrome或Firefox浏览器</li>
                                            <li>考试期间请勿刷新页面</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 考试类型卡片点击事件
        document.querySelectorAll('.exam-card').forEach(card => {
            card.addEventListener('click', function() {
                const examType = this.dataset.examType;
                document.getElementById('exam_type').value = examType;
                
                // 移除其他卡片的选中状态
                document.querySelectorAll('.exam-card').forEach(c => c.classList.remove('border-primary'));
                // 添加当前卡片的选中状态
                this.classList.add('border-primary');
            });
        });

        // 表单提交处理
        document.getElementById('examForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const studentId = document.getElementById('student_id').value;
            const examType = document.getElementById('exam_type').value;
            const agreeTerms = document.getElementById('agree_terms').checked;
            
            if (!studentId || !examType || !agreeTerms) {
                alert('请填写完整信息并同意考试须知');
                return;
            }
            
            // 显示加载状态
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在创建考试...';
            submitBtn.disabled = true;
            
            // 提交表单
            const formData = new FormData(this);
            fetch('/start_exam', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    window.location.href = '/exam';
                } else {
                    return response.json().then(data => {
                        throw new Error(data.error || '创建考试失败');
                    });
                }
            })
            .catch(error => {
                alert('错误: ' + error.message);
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
