<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MI2 Medical Immunology 2 - 考试系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin-top: 50px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
            color: #667eea;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .stats-section {
            background: #e8f4f8;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="main-container">
                    <!-- Header -->
                    <div class="header">
                        <h1>🧬 MI2 Medical Immunology 2</h1>
                        <p>医学免疫学2 - 智能考试系统</p>
                        <p class="text-muted">Medical Immunology 2 - Intelligent Exam System</p>
                    </div>

                    <!-- Features -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="feature-card">
                                <div class="feature-icon">📝</div>
                                <h4>智能出题</h4>
                                <p>基于MI2课程大纲，智能生成涵盖微生物学、免疫学、病理学等核心内容的考试题目</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-card">
                                <div class="feature-icon">🎯</div>
                                <h4>多样题型</h4>
                                <p>包含单选题、多选题、简答题、论述题和病例分析等多种题型，全面评估学习效果</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-card">
                                <div class="feature-icon">📊</div>
                                <h4>详细分析</h4>
                                <p>提供详细的成绩分析和学习建议，帮助学生了解知识掌握情况</p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-md-6 text-center">
                            <a href="{{ url_for('start_exam') }}" class="btn btn-primary btn-lg">
                                🚀 开始考试
                            </a>
                        </div>
                        <div class="col-md-6 text-center">
                            <a href="{{ url_for('admin_panel') }}" class="btn btn-outline-primary btn-lg">
                                ⚙️ 管理面板
                            </a>
                        </div>
                    </div>

                    <!-- Course Modules -->
                    <div class="stats-section">
                        <h4 class="text-center mb-4">📚 课程模块</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">🦠 微生物学 (Microbiology)</li>
                                    <li class="list-group-item">🛡️ 先天免疫 (Innate Immunity)</li>
                                    <li class="list-group-item">🎯 适应性免疫 (Adaptive Immunity)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">🔬 免疫病理学 (Immune Pathology)</li>
                                    <li class="list-group-item">💊 临床免疫学 (Clinical Immunology)</li>
                                    <li class="list-group-item">🧪 免疫技术 (Immunological Techniques)</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="text-center mt-4">
                        <p class="text-muted">
                            <small>© 2024 MI2 Exam System | 基于MI2课程内容构建</small>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载统计数据
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                if (data.total_questions) {
                    document.querySelector('.stats-section h4').innerHTML = 
                        `📚 课程模块 (共 ${data.total_questions} 道题目)`;
                }
            })
            .catch(error => console.log('获取统计数据失败:', error));
    </script>
</body>
</html> 