<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MI2 医学免疫学考试系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        
        .exam-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .info-item {
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .start-btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        
        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .admin-btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 1.0em;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        
        .admin-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .stats {
            background: #e8f4f8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .stats h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .modules {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        
        .module {
            background: white;
            padding: 10px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .footer {
            margin-top: 30px;
            color: #888;
            font-size: 0.9em;
        }
        
        .health-check {
            margin-top: 20px;
        }
        
        .health-check a {
            color: #667eea;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧬 MI2 医学免疫学考试系统</h1>
        <p class="subtitle">Medical Immunology 2 - 在线考试平台</p>
        
        <div class="exam-info">
            <div class="info-item">📝 考试时间: {{ config.exam_settings.time_limit }} 分钟</div>
            <div class="info-item">📊 题目数量: {{ config.exam_settings.questions_per_exam }} 道</div>
            <div class="info-item">✅ 及格分数: {{ config.exam_settings.passing_score }} 分</div>
        </div>
        
        <div>
            <a href="{{ url_for('start_exam') }}" class="start-btn">🚀 开始考试</a>
            <a href="{{ url_for('admin_panel') }}" class="admin-btn">⚙️ 管理面板</a>
        </div>
        
        <div class="stats">
            <h3>📚 课程模块</h3>
            <div class="modules">
                <div class="module">
                    <strong>🦠 微生物学与感染免疫</strong>
                    <br>病原体识别、疫苗机制、感染免疫
                </div>
                <div class="module">
                    <strong>🛡️ 适应性免疫与免疫应答</strong>
                    <br>T细胞、B细胞、抗体功能
                </div>
            </div>
        </div>
        
        <div class="health-check">
            <a href="{{ url_for('health_check') }}">🔍 系统健康检查</a>
        </div>
        
        <div class="footer">
            <p>© 2024 MI2 考试系统 - 祝您考试顺利！</p>
        </div>
    </div>

    <script>
        // 获取统计数据
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                if (data.total_questions) {
                    document.querySelector('.stats h3').textContent = `📚 课程模块 (共 ${data.total_questions} 道题目)`;
                }
            })
            .catch(error => console.log('获取统计数据失败:', error));
    </script>
</body>
</html> 