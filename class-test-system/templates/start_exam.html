<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开始考试 - MI2 考试系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
        }
        .exam-form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 50px;
            max-width: 600px;
            width: 100%;
        }
        .form-title {
            text-align: center;
            margin-bottom: 40px;
            color: #2c3e50;
        }
        .form-title h2 {
            font-weight: 700;
            margin-bottom: 10px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 8px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
        }
        .exam-type-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .exam-type-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .exam-type-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .exam-type-card input[type="radio"] {
            margin-right: 10px;
        }
        .btn-start {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 15px 40px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            width: 100%;
            color: white;
            font-size: 18px;
            margin-top: 20px;
        }
        .btn-start:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
        .info-card {
            background: #e8f4f8;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .info-card h5 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .info-list {
            margin-bottom: 0;
        }
        .info-list li {
            margin-bottom: 8px;
            color: #555;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="exam-form-container">
                    <div class="form-title">
                        <h2>🧬 开始考试</h2>
                        <p class="text-muted">Medical Immunology 2 - 考试系统</p>
                    </div>

                    <!-- 考试信息 -->
                    <div class="info-card">
                        <h5>📋 考试说明</h5>
                        <ul class="info-list">
                            <li>🕐 考试时间：根据选择的考试类型确定</li>
                            <li>📝 题目类型：单选题、多选题、简答题、论述题、病例分析</li>
                            <li>🎯 评分标准：客观题自动评分，主观题提供参考答案</li>
                            <li>📊 成绩报告：考试结束后立即生成详细分析报告</li>
                            <li>⚠️ 注意事项：考试过程中请勿刷新页面或关闭浏览器</li>
                        </ul>
                    </div>

                    <!-- 考试表单 -->
                    <form method="POST" action="{{ url_for('start_exam') }}">
                        <!-- 学生信息 -->
                        <div class="form-group">
                            <label for="student_id" class="form-label">学生ID *</label>
                            <input type="text" class="form-control" id="student_id" name="student_id" 
                                   placeholder="请输入您的学生ID" required>
                            <div class="form-text">例如：2024001, student_001</div>
                        </div>

                        <!-- 考试类型 -->
                        <div class="form-group">
                            <label class="form-label">考试类型 *</label>
                            
                            <div class="exam-type-card" onclick="selectExamType('regular')">
                                <input type="radio" name="exam_type" value="regular" id="regular" checked>
                                <label for="regular">
                                    <strong>📝 常规考试</strong>
                                    <div class="text-muted">
                                        <small>50道题 | 120分钟 | 涵盖所有模块</small>
                                    </div>
                                </label>
                            </div>

                            <div class="exam-type-card" onclick="selectExamType('midterm')">
                                <input type="radio" name="exam_type" value="midterm" id="midterm">
                                <label for="midterm">
                                    <strong>📚 期中考试</strong>
                                    <div class="text-muted">
                                        <small>30道题 | 90分钟 | 前半学期内容</small>
                                    </div>
                                </label>
                            </div>

                            <div class="exam-type-card" onclick="selectExamType('final')">
                                <input type="radio" name="exam_type" value="final" id="final">
                                <label for="final">
                                    <strong>🎓 期末考试</strong>
                                    <div class="text-muted">
                                        <small>80道题 | 180分钟 | 全学期综合考试</small>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- 开始考试按钮 -->
                        <button type="submit" class="btn btn-start">
                            🚀 开始考试
                        </button>
                    </form>

                    <!-- 返回链接 -->
                    <div class="back-link">
                        <a href="{{ url_for('index') }}">← 返回首页</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function selectExamType(type) {
            // 清除所有选中状态
            document.querySelectorAll('.exam-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 设置选中状态
            document.getElementById(type).checked = true;
            document.getElementById(type).closest('.exam-type-card').classList.add('selected');
        }

        // 初始化选中状态
        document.addEventListener('DOMContentLoaded', function() {
            const checkedRadio = document.querySelector('input[name="exam_type"]:checked');
            if (checkedRadio) {
                checkedRadio.closest('.exam-type-card').classList.add('selected');
            }
        });

        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const studentId = document.getElementById('student_id').value.trim();
            
            if (!studentId) {
                e.preventDefault();
                alert('请输入学生ID');
                document.getElementById('student_id').focus();
                return;
            }

            // 显示加载状态
            const submitBtn = document.querySelector('.btn-start');
            submitBtn.innerHTML = '🔄 正在生成考试...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html> 