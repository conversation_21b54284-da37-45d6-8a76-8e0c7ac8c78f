<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MI2 考试 - 正在进行</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .exam-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .exam-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .exam-title {
            font-size: 1.8em;
            color: #333;
        }
        
        .timer {
            font-size: 1.5em;
            color: #e74c3c;
            font-weight: bold;
        }
        
        .question-container {
            margin-bottom: 30px;
        }
        
        .question {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .question-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 15px;
        }
        
        .question-type {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            margin-bottom: 10px;
            display: inline-block;
        }
        
        .options {
            list-style: none;
        }
        
        .option {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #eee;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            border-color: #4ECDC4;
            background: #f0f8ff;
        }
        
        .option.selected {
            border-color: #4ECDC4;
            background: #e8f4fd;
        }
        
        .option input {
            margin-right: 10px;
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.2em;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
            display: block;
            margin: 30px auto 0;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ECDC4, #44A08D);
            transition: width 0.3s;
            width: 0%;
        }
        
        .result-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .result-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 500px;
            text-align: center;
        }
        
        .result-score {
            font-size: 2em;
            color: #4ECDC4;
            margin: 20px 0;
        }
        
        .back-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="exam-container">
        <div class="exam-header">
            <h1 class="exam-title">MI2 医学免疫学考试</h1>
            <div class="timer" id="timer">{{ config.exam_settings.time_limit }}:00</div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress"></div>
        </div>
        
        <form id="examForm" method="POST" action="{{ url_for('submit_exam') }}">
            <div class="question-container">
                {% for question in questions %}
                <div class="question">
                    <div class="question-type">
                        {% if question.type == 'single_choice' %}单选题{% else %}多选题{% endif %}
                    </div>
                    <h3 class="question-title">{{ loop.index }}. {{ question.question }}</h3>
                    <ul class="options">
                        {% for option in question.options %}
                        <li class="option" onclick="selectOption(this, '{{ question.id }}', {{ loop.index0 }}, '{{ question.type }}')">
                            <input type="{% if question.type == 'multiple_choice' %}checkbox{% else %}radio{% endif %}" 
                                   name="question_{{ question.id }}" 
                                   value="{{ loop.index0 }}" 
                                   id="q{{ question.id }}_{{ loop.index0 }}">
                            <label for="q{{ question.id }}_{{ loop.index0 }}">{{ option }}</label>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                {% endfor %}
            </div>
            
            <button type="submit" class="submit-btn">📝 提交考试</button>
        </form>
    </div>

    <!-- 结果模态框 -->
    <div id="resultModal" class="result-modal">
        <div class="result-content">
            <h2>🎉 考试完成！</h2>
            <div class="result-score" id="scoreDisplay">--/--</div>
            <p id="percentageDisplay">---%</p>
            <p id="passMessage">--</p>
            <a href="{{ url_for('index') }}" class="back-btn">返回首页</a>
        </div>
    </div>

    <script>
        let timeLeft = {{ config.exam_settings.time_limit }} * 60;
        let totalQuestions = {{ questions|length }};
        let answeredQuestions = 0;
        
        // 计时器
        function updateTimer() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('timer').textContent = 
                minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
            
            if (timeLeft <= 0) {
                submitExam();
            }
            timeLeft--;
        }
        
        // 选择答案
        function selectOption(element, questionId, optionIndex, questionType) {
            const input = element.querySelector('input');
            
            if (questionType === 'single_choice') {
                // 单选题：清除同组其他选项
                const siblings = element.parentNode.querySelectorAll('.option');
                siblings.forEach(sibling => {
                    sibling.classList.remove('selected');
                    sibling.querySelector('input').checked = false;
                });
                
                element.classList.add('selected');
                input.checked = true;
            } else {
                // 多选题：切换选中状态
                if (element.classList.contains('selected')) {
                    element.classList.remove('selected');
                    input.checked = false;
                } else {
                    element.classList.add('selected');
                    input.checked = true;
                }
            }
            
            updateProgress();
        }
        
        // 更新进度条
        function updateProgress() {
            const checkedInputs = document.querySelectorAll('input[type="radio"]:checked, input[type="checkbox"]:checked');
            const questionIds = new Set();
            
            checkedInputs.forEach(input => {
                const questionId = input.name.replace('question_', '');
                questionIds.add(questionId);
            });
            
            answeredQuestions = questionIds.size;
            const progress = (answeredQuestions / totalQuestions) * 100;
            document.getElementById('progress').style.width = progress + '%';
        }
        
        // 提交考试
        function submitExam() {
            const formData = new FormData(document.getElementById('examForm'));
            
            fetch('{{ url_for("submit_exam") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(data);
                } else {
                    alert('提交失败：' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                alert('网络错误：' + error);
            });
        }
        
        // 显示结果
        function showResult(data) {
            document.getElementById('scoreDisplay').textContent = data.score + '/' + data.max_score;
            document.getElementById('percentageDisplay').textContent = data.percentage + '%';
            
            const passing = data.percentage >= {{ config.exam_settings.passing_score }};
            document.getElementById('passMessage').textContent = passing ? '恭喜通过！' : '未达及格线，继续努力！';
            document.getElementById('passMessage').style.color = passing ? '#4ECDC4' : '#e74c3c';
            
            document.getElementById('resultModal').style.display = 'block';
        }
        
        // 启动计时器
        const timer = setInterval(updateTimer, 1000);
        
        // 表单提交处理
        document.getElementById('examForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (answeredQuestions < totalQuestions) {
                if (!confirm(`您还有 ${totalQuestions - answeredQuestions} 道题未回答，确定要提交吗？`)) {
                    return;
                }
            }
            
            clearInterval(timer);
            submitExam();
        });
        
        // 页面离开警告
        window.addEventListener('beforeunload', function(e) {
            if (answeredQuestions > 0) {
                e.preventDefault();
                e.returnValue = '考试正在进行中，确定要离开吗？';
            }
        });
    </script>
</body>
</html> 