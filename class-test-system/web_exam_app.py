#!/usr/bin/env python3
"""
Web考试界面
提供交互式的在线考试功能
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
import uuid
from intelligent_exam_system import ExamGenerator, ExamManager, StudentAnswer
from werkzeug.security import check_password_hash, generate_password_hash

app = Flask(__name__)
app.secret_key = 'mi2_exam_system_secret_key'

# 全局变量
exam_generator = None
exam_manager = None

def initialize_system():
    """初始化考试系统"""
    global exam_generator, exam_manager
    
    knowledge_base_file = "/Users/<USER>/phc-mbse/phc-mbse/class-test-system/knowledge_base.json"
    
    if not Path(knowledge_base_file).exists():
        raise FileNotFoundError("知识库文件不存在，请先运行 document_content_parser.py")
    
    exam_generator = ExamGenerator(knowledge_base_file)
    exam_manager = ExamManager("/Users/<USER>/phc-mbse/phc-mbse/class-test-system/exam_data")

@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        student_id = request.form.get('student_id')
        if student_id:
            session['student_id'] = student_id
            session['login_time'] = datetime.now().isoformat()
            return redirect(url_for('dashboard'))
        else:
            return render_template('login.html', error='请输入学生ID')
    
    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    """学生控制台"""
    if 'student_id' not in session:
        return redirect(url_for('login'))
    
    # 获取可用考试列表
    available_exams = []
    for exam_id, exam in exam_manager.exams.items():
        if exam.status == 'active' or exam.status == 'draft':
            available_exams.append({
                'id': exam_id,
                'title': exam.title,
                'description': exam.description,
                'question_count': len(exam.questions),
                'time_limit': exam.time_limit,
                'total_points': exam.total_points,
                'status': exam.status
            })
    
    return render_template('dashboard.html', 
                         student_id=session['student_id'],
                         exams=available_exams)

@app.route('/create_exam', methods=['GET', 'POST'])
def create_exam():
    """创建考试"""
    if request.method == 'POST':
        data = request.get_json()
        
        # 生成题目
        questions = exam_generator.generate_questions(
            num_questions=data.get('question_count', 10),
            difficulty_distribution={
                'basic': data.get('basic_ratio', 0.6),
                'intermediate': data.get('intermediate_ratio', 0.3),
                'advanced': data.get('advanced_ratio', 0.1)
            },
            category_weights={
                'medical_microbiology': data.get('microbiology_weight', 0.5),
                'immunology': data.get('immunology_weight', 0.4),
                'general_medicine': data.get('general_weight', 0.1)
            },
            question_types={
                'multiple_choice': data.get('mc_ratio', 0.7),
                'true_false': data.get('tf_ratio', 0.2),
                'short_answer': data.get('sa_ratio', 0.1)
            }
        )
        
        # 创建考试
        exam_id = exam_manager.create_exam(
            title=data.get('title', 'MI2考试'),
            description=data.get('description', ''),
            questions=questions,
            time_limit=data.get('time_limit', 60)
        )
        
        # 启动考试
        exam_manager.start_exam(exam_id)
        
        return jsonify({'success': True, 'exam_id': exam_id})
    
    return render_template('create_exam.html')

@app.route('/exam/<exam_id>')
def take_exam(exam_id):
    """参加考试"""
    if 'student_id' not in session:
        return redirect(url_for('login'))
    
    exam = exam_manager.exams.get(exam_id)
    if not exam:
        return "考试不存在", 404
    
    # 将题目转换为JSON格式以便前端使用
    questions_data = []
    for i, q in enumerate(exam.questions):
        questions_data.append({
            'id': q.id,
            'number': i + 1,
            'type': q.type,
            'question': q.question_text,
            'options': q.options,
            'points': q.points
        })
    
    return render_template('exam.html', 
                         exam=exam,
                         questions=questions_data,
                         exam_id=exam_id)

@app.route('/submit_exam/<exam_id>', methods=['POST'])
def submit_exam(exam_id):
    """提交考试答案"""
    if 'student_id' not in session:
        return jsonify({'error': '未登录'}), 401
    
    data = request.get_json()
    answers_data = data.get('answers', [])
    
    exam = exam_manager.exams.get(exam_id)
    if not exam:
        return jsonify({'error': '考试不存在'}), 404
    
    # 处理答案
    student_answers = []
    question_map = {q.id: q for q in exam.questions}
    
    for answer_data in answers_data:
        question_id = answer_data['question_id']
        student_answer = answer_data['answer']
        time_spent = answer_data.get('time_spent', 0)
        
        question = question_map.get(question_id)
        if question:
            is_correct = student_answer == question.correct_answer
            points_earned = question.points if is_correct else 0
            
            student_answers.append(StudentAnswer(
                question_id=question_id,
                student_answer=student_answer,
                is_correct=is_correct,
                points_earned=points_earned,
                time_spent=time_spent
            ))
    
    # 提交结果
    result = exam_manager.submit_exam(exam_id, session['student_id'], student_answers)
    
    return jsonify({
        'success': True,
        'score': result.total_score,
        'max_score': result.max_score,
        'percentage': result.percentage,
        'result_id': f"{exam_id}_{session['student_id']}"
    })

@app.route('/result/<exam_id>')
def exam_result(exam_id):
    """查看考试结果"""
    if 'student_id' not in session:
        return redirect(url_for('login'))
    
    # 查找学生的考试结果
    exam_results = exam_manager.results.get(exam_id, [])
    student_result = None
    
    for result in exam_results:
        if result.student_id == session['student_id']:
            student_result = result
            break
    
    if not student_result:
        return "未找到考试结果", 404
    
    exam = exam_manager.exams.get(exam_id)
    
    # 准备详细结果数据
    question_map = {q.id: q for q in exam.questions}
    detailed_results = []
    
    for answer in student_result.answers:
        question = question_map.get(answer.question_id)
        if question:
            detailed_results.append({
                'question': question.question_text,
                'student_answer': answer.student_answer,
                'correct_answer': question.correct_answer,
                'is_correct': answer.is_correct,
                'points_earned': answer.points_earned,
                'max_points': question.points,
                'explanation': question.explanation
            })
    
    return render_template('result.html',
                         exam=exam,
                         result=student_result,
                         detailed_results=detailed_results)

@app.route('/statistics/<exam_id>')
def exam_statistics(exam_id):
    """考试统计信息"""
    stats = exam_manager.get_exam_statistics(exam_id)
    exam = exam_manager.exams.get(exam_id)
    
    return render_template('statistics.html', 
                         exam=exam,
                         stats=stats)

@app.route('/api/create_sample_exam')
def create_sample_exam():
    """创建示例考试"""
    try:
        # 生成示例题目
        questions = exam_generator.generate_questions(
            num_questions=15,
            difficulty_distribution={'basic': 0.6, 'intermediate': 0.3, 'advanced': 0.1},
            category_weights={'medical_microbiology': 0.5, 'immunology': 0.4, 'general_medicine': 0.1}
        )
        
        # 创建考试
        exam_id = exam_manager.create_exam(
            title="MI2 医学微生物学与免疫学模拟考试",
            description="涵盖医学微生物学和免疫学核心概念的模拟考试，用于自测和练习",
            questions=questions,
            time_limit=90
        )
        
        # 启动考试
        exam_manager.start_exam(exam_id)
        
        return jsonify({
            'success': True,
            'exam_id': exam_id,
            'message': '示例考试创建成功'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    try:
        # 初始化系统
        initialize_system()
        
        # 创建模板目录
        templates_dir = Path("templates")
        templates_dir.mkdir(exist_ok=True)
        
        print("🌐 启动Web考试系统...")
        print("📱 访问地址: http://localhost:5000")
        print("🎓 功能包括:")
        print("  - 学生登录和考试")
        print("  - 在线答题界面")
        print("  - 自动评分和结果分析")
        print("  - 考试统计和管理")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请确保已运行 document_content_parser.py 生成知识库")
