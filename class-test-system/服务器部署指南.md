# 🚀 MI2考试系统服务器部署指南

## 📋 服务器信息
- **服务器IP**: ***********
- **用户名**: root
- **系统**: CentOS/RHEL (华东-上海一)

## 🎯 部署步骤总览

### 步骤1: 准备部署包
```bash
# 在本地运行打包脚本
cd class-test-system
./package_for_deployment.sh
```

### 步骤2: 连接服务器
```bash
# SSH连接服务器（首次需要设置密码）
ssh root@***********
```

### 步骤3: 上传部署包
```bash
# 方法一：使用SCP上传（在本地运行）
scp mi2-exam-system-*.tar.gz root@***********:/opt/

# 方法二：使用图形化工具（如FileZilla、WinSCP）
# 服务器地址：***********
# 用户名：root
# 上传到：/opt/ 目录
```

### 步骤4: 服务器部署
```bash
# 在服务器上执行
cd /opt
tar -xzf mi2-exam-system-*.tar.gz
cd mi2-exam-system-*

# 运行部署脚本
chmod +x deploy.sh
./deploy.sh
```

### 步骤5: 启动服务
```bash
# 启动MI2考试系统
./manage.sh start

# 检查服务状态
./manage.sh status
```

### 步骤6: 访问系统
- 🌐 **主页面**: http://***********
- ✅ **健康检查**: http://***********/health

---

## 🔧 常用管理命令

```bash
# 启动服务
./manage.sh start

# 停止服务
./manage.sh stop

# 重启服务
./manage.sh restart

# 查看状态
./manage.sh status

# 查看应用日志
./manage.sh logs

# 查看Nginx日志
./manage.sh nginx-logs
```

---

## 🎉 部署完成后的验证

### 1. 检查服务状态
```bash
./manage.sh status
```
应该显示服务正在运行

### 2. 访问健康检查
```bash
curl http://***********/health
```
应该返回JSON格式的健康信息

### 3. 访问网页界面
在浏览器中打开 `http://***********`
应该看到MI2考试系统首页

---

## 🔒 安全配置建议

### 1. 修改默认密钥
```bash
# 编辑应用配置
vi /opt/mi2-exam-system/app.py

# 修改这一行：
SECRET_KEY = 'your-unique-secret-key-here'
```

### 2. 配置防火墙
```bash
# 检查防火墙状态
firewall-cmd --list-all

# 如需开放其他端口
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --reload
```

### 3. 配置HTTPS（可选）
如需HTTPS访问，可以：
- 申请SSL证书
- 配置Nginx SSL
- 修改防火墙规则开放443端口

---

## 🐛 常见问题解决

### 问题1: 服务启动失败
```bash
# 查看详细错误日志
journalctl -u mi2-exam -f

# 检查Python依赖
pip3 list | grep -i flask
```

### 问题2: 无法访问网页
```bash
# 检查Nginx状态
systemctl status nginx

# 检查端口占用
netstat -tulpn | grep :80
```

### 问题3: 题库加载失败
```bash
# 检查文件权限
ls -la /opt/mi2-exam-system/question_banks/

# 检查文件格式
python3 -m json.tool question_banks/microbiology.json
```

---

## 📊 监控和维护

### 日志文件位置
- 应用日志: `/var/log/mi2-exam/app.log`
- Nginx访问日志: `/var/log/nginx/access.log`
- Nginx错误日志: `/var/log/nginx/error.log`
- 系统服务日志: `journalctl -u mi2-exam`

### 定期维护
```bash
# 清理过期的考试文件（每月）
find /opt/mi2-exam-system/exams -name "*.json" -mtime +30 -delete

# 备份题库和结果（每周）
tar -czf backup-$(date +%Y%m%d).tar.gz question_banks results

# 查看系统资源使用
top
df -h
free -h
```

---

## 🎯 成功部署的标志

✅ 服务状态显示 `active (running)`  
✅ 健康检查返回 `{"status": "healthy"}`  
✅ 网页界面正常显示  
✅ 可以创建和提交考试  
✅ 日志文件正常记录  

---

## 📞 需要帮助？

如果遇到问题，请提供以下信息：
- 错误消息的完整内容
- 相关日志文件内容
- 执行的具体步骤
- 服务器系统信息

**祝您部署顺利！🎉** 