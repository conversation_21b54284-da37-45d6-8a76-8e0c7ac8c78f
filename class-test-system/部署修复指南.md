# MI2 考试系统部署修复指南

## 🔧 问题诊断

如果部署后页面空白，通常是因为以下原因：

1. **应用文件缺失** - 核心的 `app.py` 文件没有正确复制到服务器
2. **模板文件缺失** - HTML模板文件没有被复制
3. **配置文件缺失** - 题库配置文件缺失
4. **服务启动失败** - Python应用无法启动

## 🚀 快速修复方法

### 方法一：使用直接部署脚本（推荐）

1. **将修复脚本复制到服务器**：
   ```bash
   # 上传 direct_deploy.sh 到服务器
   scp direct_deploy.sh root@***********:/opt/
   ```

2. **在服务器上运行修复脚本**：
   ```bash
   # 登录服务器
   ssh root@***********
   
   # 运行修复脚本
   cd /opt
   chmod +x direct_deploy.sh
   ./direct_deploy.sh
   ```

3. **验证修复结果**：
   ```bash
   # 检查服务状态
   systemctl status mi2-exam
   systemctl status nginx
   
   # 检查应用日志
   journalctl -u mi2-exam -f
   ```

### 方法二：手动复制文件

如果你已经有了完整的部署包，可以手动复制：

1. **检查部署目录**：
   ```bash
   cd /opt/mi2-exam-system
   ls -la
   ```

2. **复制缺失的文件**：
   ```bash
   # 从部署包目录复制
   cp /opt/mi2-exam-deployment/app.py ./
   cp /opt/mi2-exam-deployment/config.json ./
   cp -r /opt/mi2-exam-deployment/templates/* ./templates/
   cp -r /opt/mi2-exam-deployment/question_banks/* ./question_banks/
   ```

3. **重启服务**：
   ```bash
   ./manage.sh restart
   ```

## 🔍 故障排除

### 1. 检查文件完整性

```bash
cd /opt/mi2-exam-system
ls -la

# 应该看到以下文件：
# app.py - 主应用文件
# config.json - 配置文件
# templates/ - 模板目录（非空）
# question_banks/ - 题库目录（非空）
```

### 2. 检查服务状态

```bash
# 检查应用服务
systemctl status mi2-exam

# 检查nginx服务
systemctl status nginx

# 查看详细日志
journalctl -u mi2-exam -f
```

### 3. 检查端口占用

```bash
# 检查5000端口
netstat -tlnp | grep 5000

# 检查80端口
netstat -tlnp | grep 80
```

### 4. 手动启动测试

```bash
cd /opt/mi2-exam-system

# 停止服务
systemctl stop mi2-exam

# 手动启动测试
python3 app.py
```

### 5. 检查Python依赖

```bash
# 安装依赖
pip3 install flask gunicorn

# 检查Python版本
python3 --version
```

## 🌐 访问测试

修复完成后，访问以下地址测试：

- **主页**：http://***********
- **健康检查**：http://***********/health

## 📋 常见错误及解决方案

### 错误1：ImportError: No module named 'flask'
```bash
# 解决方案
pip3 install flask
```

### 错误2：template not found
```bash
# 解决方案：检查模板目录
ls -la /opt/mi2-exam-system/templates/
# 应该包含 index.html 和 start_exam.html
```

### 错误3：config.json not found
```bash
# 解决方案：创建默认配置
cd /opt/mi2-exam-system
# 运行 direct_deploy.sh 会自动创建
```

### 错误4：Permission denied
```bash
# 解决方案：设置正确权限
chown -R root:root /opt/mi2-exam-system
chmod -R 755 /opt/mi2-exam-system
```

## 🔧 完整重新部署

如果问题仍然存在，可以完全重新部署：

```bash
# 1. 停止服务
systemctl stop mi2-exam nginx

# 2. 清空部署目录
rm -rf /opt/mi2-exam-system/*

# 3. 运行直接部署脚本
cd /opt
./direct_deploy.sh

# 4. 检查结果
curl http://localhost/health
```

## 📞 技术支持

如果以上方法都无法解决问题，请检查：

1. **系统日志**：`journalctl -u mi2-exam -f`
2. **Nginx日志**：`tail -f /var/log/nginx/error.log`
3. **Python错误日志**：`tail -f /var/log/mi2-exam-error.log`
4. **防火墙设置**：`firewall-cmd --list-all`

## 🎯 验证清单

- [ ] app.py 文件存在且可执行
- [ ] templates 目录包含 HTML 文件
- [ ] config.json 文件存在且格式正确
- [ ] Python依赖已安装
- [ ] 服务正常启动
- [ ] 端口80和5000正常监听
- [ ] 防火墙配置正确
- [ ] 可以访问主页和健康检查页面

完成以上检查后，系统应该能够正常运行。 