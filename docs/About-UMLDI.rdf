<?xml version="1.0"?>
<rdf:RDF xmlns="https://www.omg.org/spec/UMLDI/About-UMLDI#"
     xml:base="https://www.omg.org/spec/UMLDI/About-UMLDI"
     xmlns:sm="https://www.omg.org/techprocess/ab/SpecificationMetadata/"
     xmlns:dct="https://purl.org/dc/terms/"
     xmlns:owl="http://www.w3.org/2002/07/owl#"
     xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
     xmlns:xml="http://www.w3.org/XML/1998/namespace"
     xmlns:xsd="http://www.w3.org/2001/XMLSchema#"
     xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
     xmlns:skos="https://www.w3.org/2004/02/skos/core#">
    <owl:Ontology rdf:about="https://www.omg.org/spec/UMLDI/About-UMLDI">
        <owl:versionIRI rdf:resource="https://www.omg.org/spec/UMLDI/1.0/About-UMLDI"/>
        <owl:imports rdf:resource="https://www.omg.org/techprocess/ab/SpecificationMetadata/"/>
        <rdfs:label>About the UML Diagram Interchange Specification Version 1.0</rdfs:label>
        <owl:versionInfo>Generated on 2025-06-28T11:40:49-0400</owl:versionInfo>
        <dct:license rdf:resource="https://www.omg.org/techprocess/ab/SpecificationMetadata/MITLicense"/>
        <sm:creationDate rdf:datatype="http://www.w3.org/2001/XMLSchema#dateTime">2006-04-03T00:00:00-0400</sm:creationDate>
        <sm:filename>About-UMLDI</sm:filename>
    </owl:Ontology>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Annotation properties
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->


    


    <!-- https://purl.org/dc/terms/license -->


    <owl:AnnotationProperty rdf:about="https://purl.org/dc/terms/license"/>
    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/category -->


    <owl:AnnotationProperty rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/category"/>
    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/creationDate -->


    <owl:AnnotationProperty rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/creationDate"/>
    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/filename -->


    <owl:AnnotationProperty rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/filename"/>
    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/publicationDate -->


    <owl:AnnotationProperty rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/publicationDate"/>
    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/specificationAbbreviation -->


    <owl:AnnotationProperty rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/specificationAbbreviation"/>
    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/specificationAbstract -->


    <owl:AnnotationProperty rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/specificationAbstract"/>
    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/specificationTitle -->


    <owl:AnnotationProperty rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/specificationTitle"/>
    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/specificationURL -->


    <owl:AnnotationProperty rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/specificationURL"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Classes
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->


    


    <!-- https://www.omg.org/techprocess/ab/SpecificationMetadata/Specification -->


    <owl:Class rdf:about="https://www.omg.org/techprocess/ab/SpecificationMetadata/Specification"/>
    


    <!-- 
    ///////////////////////////////////////////////////////////////////////////////////////
    //
    // Individuals
    //
    ///////////////////////////////////////////////////////////////////////////////////////
     -->


    


    <!-- https://www.omg.org/spec/UMLDI -->


    <owl:NamedIndividual rdf:about="https://www.omg.org/spec/UMLDI">
        <rdf:type rdf:resource="https://www.omg.org/techprocess/ab/SpecificationMetadata/Specification"/>
        <sm:category>modeling</sm:category>
        <sm:category>platform</sm:category>
        <sm:publicationDate rdf:datatype="http://www.w3.org/2001/XMLSchema#dateTime">2006-04-03T00:00:00-0400</sm:publicationDate>
        <sm:specificationAbbreviation>UMLDI</sm:specificationAbbreviation>
        <sm:specificationAbstract>The goal of this specification is to enable a smooth and seamless exchange of documents compliant to the UML standard (in the following referred to as UML models) between different software tools. While this certainly includes tools for developing UML models, it also includes tools such as whiteboard tools, code generators, word processing tools, and desktop publishing tools.</sm:specificationAbstract>
        <sm:specificationTitle>UML Diagram Interchange</sm:specificationTitle>
        <sm:specificationURL rdf:resource="https://www.omg.org/spec/UMLDI/About-UMLDI"/>
    </owl:NamedIndividual>
</rdf:RDF>



<!-- Generated by the OWL API (version 5.1.20.2022-02-19T16:38:31Z) https://github.com/owlcs/owlapi/ -->


