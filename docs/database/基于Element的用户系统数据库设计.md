# 基于Element的用户系统数据库设计

## 📋 设计概述

**数据库类型**：SQLite（初期实现）  
**设计理念**：统一Element存储 + 关键字段索引 + 关系管理  
**核心原则**：万物皆Element，所有用户相关数据都基于Element模型

## 🏗️ 核心设计思路

### 统一存储策略
```
Element完整数据(JSON) + 关键字段提取(索引) + 关系管理(独立表)
```

### 架构分层
```
应用查询层 (UserElement, RoleElement查询)
    ↓
领域映射层 (SecurityDomain, 用户领域映射)
    ↓  
Element存储层 (统一Element表 + 关系表)
    ↓
SQLite物理层 (JSON存储 + B-Tree索引)
```

## 📊 核心表结构设计

### 1. elements - Element统一存储表

```sql
CREATE TABLE elements (
    -- 核心标识字段
    id TEXT PRIMARY KEY,                    -- Element.id
    tag TEXT NOT NULL,                      -- Element.tag
    local_name TEXT NOT NULL,               -- Element.local_name
    
    -- 领域和类型字段（索引优化）
    domain_type TEXT,                       -- 领域类型：security, modeling, workflow
    entity_type TEXT,                       -- 实体类型：user, role, permission, project
    element_type TEXT NOT NULL DEFAULT 'leaf',  -- Element.element_type
    status TEXT NOT NULL DEFAULT 'active',      -- Element.status
    
    -- 层次结构字段
    parent_id TEXT,                         -- Element.parent_id
    depth_level INTEGER DEFAULT 0,         -- Element.position.depth_level
    
    -- 语义信息字段（查询优化）
    domain_category TEXT,                   -- Element.semantic_info.domain_category
    domain_code TEXT,                       -- Element.semantic_info.domain_code
    semantic_tags TEXT,                     -- Element.semantic_info.semantic_tags (JSON)
    
    -- 安全和业务字段（用户系统特化）
    username TEXT,                          -- 用户名（仅user类型）
    email TEXT,                            -- 邮箱（仅user类型）
    password_hash TEXT,                     -- 密码哈希（仅user类型）
    role_name TEXT,                        -- 角色名（仅role类型）
    permission_name TEXT,                   -- 权限名（仅permission类型）
    
    -- 完整Element数据
    element_data TEXT NOT NULL,             -- 完整Element JSON数据
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (parent_id) REFERENCES elements(id) ON DELETE SET NULL
);
```

### 2. element_relationships - Element关系表

```sql
CREATE TABLE element_relationships (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- 关系基本信息
    source_id TEXT NOT NULL,               -- 源Element ID
    target_id TEXT NOT NULL,               -- 目标Element ID
    relation_type TEXT NOT NULL,           -- 关系类型：user_role, role_permission, etc.
    
    -- 关系属性
    relation_data TEXT,                    -- 关系的额外数据（JSON）
    is_direct BOOLEAN DEFAULT 1,          -- 是否直接关系
    strength REAL DEFAULT 1.0,            -- 关系强度
    
    -- 时间戳
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (source_id) REFERENCES elements(id) ON DELETE CASCADE,
    FOREIGN KEY (target_id) REFERENCES elements(id) ON DELETE CASCADE,
    
    -- 唯一约束
    UNIQUE(source_id, target_id, relation_type)
);
```

### 3. element_attributes - Element属性表（性能优化）

```sql
CREATE TABLE element_attributes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    element_id TEXT NOT NULL,              -- Element ID
    attribute_name TEXT NOT NULL,          -- 属性名
    attribute_value TEXT,                  -- 属性值
    data_type TEXT DEFAULT 'string',      -- 数据类型
    is_required BOOLEAN DEFAULT 0,        -- 是否必需
    is_encrypted BOOLEAN DEFAULT 0,       -- 是否加密
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (element_id) REFERENCES elements(id) ON DELETE CASCADE,
    UNIQUE(element_id, attribute_name)
);
```

### 4. user_sessions - 用户会话表

```sql
CREATE TABLE user_sessions (
    id TEXT PRIMARY KEY,                   -- Session ID
    user_element_id TEXT NOT NULL,        -- 用户Element ID
    
    -- 会话信息
    access_token_hash TEXT,               -- 访问Token哈希
    refresh_token_hash TEXT,              -- 刷新Token哈希
    
    -- 客户端信息
    ip_address TEXT,
    user_agent TEXT,
    device_fingerprint TEXT,
    
    -- 时间管理
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_active_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    
    -- 状态
    is_active BOOLEAN DEFAULT 1,
    
    FOREIGN KEY (user_element_id) REFERENCES elements(id) ON DELETE CASCADE
);
```

### 5. audit_logs - 审计日志表

```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- 操作主体
    user_element_id TEXT,                  -- 操作用户
    target_element_id TEXT,                -- 目标Element
    
    -- 操作信息
    action TEXT NOT NULL,                  -- 操作类型：create, read, update, delete
    resource_type TEXT,                    -- 资源类型
    operation_result TEXT,                 -- 操作结果：success, failure, error
    
    -- 详细信息
    details TEXT,                          -- 操作详情（JSON）
    ip_address TEXT,
    user_agent TEXT,
    
    -- 时间戳
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_element_id) REFERENCES elements(id) ON DELETE SET NULL,
    FOREIGN KEY (target_element_id) REFERENCES elements(id) ON DELETE SET NULL
);
```

## 🚀 索引设计

### 核心查询索引

```sql
-- 基础查询索引
CREATE INDEX idx_elements_domain_entity ON elements(domain_type, entity_type);
CREATE INDEX idx_elements_status ON elements(status);
CREATE INDEX idx_elements_parent ON elements(parent_id);

-- 用户系统特化索引
CREATE UNIQUE INDEX idx_elements_username ON elements(username) WHERE username IS NOT NULL;
CREATE UNIQUE INDEX idx_elements_email ON elements(email) WHERE email IS NOT NULL;
CREATE INDEX idx_elements_role_name ON elements(role_name) WHERE role_name IS NOT NULL;
CREATE INDEX idx_elements_permission_name ON elements(permission_name) WHERE permission_name IS NOT NULL;

-- 语义查询索引
CREATE INDEX idx_elements_domain_category ON elements(domain_category);
CREATE INDEX idx_elements_semantic_tags ON elements(semantic_tags);

-- 时间查询索引
CREATE INDEX idx_elements_created_at ON elements(created_at);
CREATE INDEX idx_elements_updated_at ON elements(updated_at);
```

### 关系查询索引

```sql
-- 关系查询优化
CREATE INDEX idx_relationships_source ON element_relationships(source_id, relation_type);
CREATE INDEX idx_relationships_target ON element_relationships(target_id, relation_type);
CREATE INDEX idx_relationships_type ON element_relationships(relation_type);

-- 属性查询优化
CREATE INDEX idx_attributes_element_name ON element_attributes(element_id, attribute_name);
CREATE INDEX idx_attributes_name_value ON element_attributes(attribute_name, attribute_value);

-- 会话查询优化
CREATE INDEX idx_sessions_user ON user_sessions(user_element_id, is_active);
CREATE INDEX idx_sessions_token ON user_sessions(access_token_hash);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);

-- 审计查询优化
CREATE INDEX idx_audit_user_time ON audit_logs(user_element_id, timestamp);
CREATE INDEX idx_audit_target_action ON audit_logs(target_element_id, action);
```

## 📝 数据插入示例

### 创建UserElement

```sql
-- 1. 插入用户Element
INSERT INTO elements (
    id, tag, local_name,
    domain_type, entity_type, element_type,
    domain_category, domain_code, semantic_tags,
    username, email, password_hash,
    element_data
) VALUES (
    'user_001',
    'security:user',
    'user',
    'security',
    'user',
    'leaf',
    'user_management',
    'USR',
    '["security:user", "org:华望", "active:user"]',
    'zhangsan',
    '<EMAIL>',
    '$2b$12$...',  -- BCrypt哈希
    '{
        "id": "user_001",
        "tag": "security:user",
        "local_name": "user",
        "element_type": "leaf",
        "custom_data": {
            "profile": {
                "display_name": "张三",
                "department": "生物医学工程",
                "phone": "***********"
            },
            "security": {
                "last_login": "2024-12-09T10:00:00Z",
                "login_count": 42,
                "account_locked": false
            }
        },
        "semantic_info": {
            "domain_category": "user_management",
            "semantic_tags": ["security:user", "org:华望", "active:user"]
        }
    }'
);

-- 2. 插入用户属性
INSERT INTO element_attributes (element_id, attribute_name, attribute_value, data_type) VALUES
('user_001', 'display_name', '张三', 'string'),
('user_001', 'department', '生物医学工程', 'string'),
('user_001', 'phone', '***********', 'string'),
('user_001', 'last_login_ip', '*************', 'string');
```

### 创建RoleElement

```sql
-- 插入角色Element
INSERT INTO elements (
    id, tag, local_name,
    domain_type, entity_type, element_type,
    domain_category, domain_code, semantic_tags,
    role_name,
    element_data
) VALUES (
    'role_admin',
    'security:role',
    'role',
    'security',
    'role',
    'container',
    'access_control',
    'ROL',
    '["security:role", "level:admin", "scope:system"]',
    'system_admin',
    '{
        "id": "role_admin",
        "tag": "security:role",
        "local_name": "role",
        "element_type": "container",
        "custom_data": {
            "role_info": {
                "display_name": "系统管理员",
                "description": "拥有系统完全访问权限",
                "level": "admin",
                "scope": "system"
            }
        }
    }'
);
```

### 建立用户-角色关系

```sql
INSERT INTO element_relationships (
    source_id, target_id, relation_type,
    relation_data
) VALUES (
    'user_001',
    'role_admin',
    'user_role',
    '{
        "granted_at": "2024-12-09T10:00:00Z",
        "granted_by": "system",
        "expires_at": null,
        "is_primary": true
    }'
);
```

## 🔍 常用查询模式

### 用户认证查询

```sql
-- 通过用户名或邮箱查找用户
SELECT id, password_hash, element_data 
FROM elements 
WHERE domain_type = 'security' 
  AND entity_type = 'user' 
  AND status = 'active'
  AND (username = ? OR email = ?);
```

### 用户权限查询

```sql
-- 查询用户的所有权限
WITH user_roles AS (
    SELECT r.target_id as role_id
    FROM element_relationships r
    WHERE r.source_id = ? 
      AND r.relation_type = 'user_role'
),
role_permissions AS (
    SELECT r.target_id as permission_id
    FROM element_relationships r
    JOIN user_roles ur ON r.source_id = ur.role_id
    WHERE r.relation_type = 'role_permission'
)
SELECT e.id, e.permission_name, e.element_data
FROM elements e
JOIN role_permissions rp ON e.id = rp.permission_id
WHERE e.entity_type = 'permission' AND e.status = 'active';
```

### 语义标签查询

```sql
-- 查询特定语义标签的Element
SELECT id, tag, element_data
FROM elements
WHERE semantic_tags LIKE '%"security:user"%'
  AND domain_type = 'security'
  AND status = 'active';
```

### Element层次查询

```sql
-- 查询用户的所有子Element
WITH RECURSIVE element_tree AS (
    SELECT id, parent_id, tag, depth_level, element_data
    FROM elements
    WHERE id = ?
    
    UNION ALL
    
    SELECT e.id, e.parent_id, e.tag, e.depth_level, e.element_data
    FROM elements e
    JOIN element_tree et ON e.parent_id = et.id
)
SELECT * FROM element_tree;
```

## ⚡ 性能优化策略

### 1. 查询优化
- **预编译语句**：所有查询使用参数化查询
- **索引覆盖**：常用查询字段建立覆盖索引
- **分页查询**：大结果集使用LIMIT+OFFSET分页

### 2. JSON字段优化
```sql
-- 使用JSON函数优化查询
SELECT id, 
       JSON_EXTRACT(element_data, '$.custom_data.profile.display_name') as display_name
FROM elements 
WHERE JSON_EXTRACT(element_data, '$.semantic_info.domain_category') = 'user_management';
```

### 3. 缓存策略
- **用户缓存**：活跃用户信息缓存30分钟
- **权限缓存**：用户权限信息缓存15分钟  
- **角色缓存**：角色定义缓存60分钟

### 4. 批量操作
```sql
-- 批量插入Element
INSERT INTO elements (id, tag, local_name, domain_type, entity_type, element_data) 
VALUES 
    (?, ?, ?, ?, ?, ?),
    (?, ?, ?, ?, ?, ?),
    (?, ?, ?, ?, ?, ?);
```

## 🔧 迁移和扩展策略

### 数据库迁移脚本

```sql
-- V1.0.0 初始架构
CREATE TABLE IF NOT EXISTS schema_migrations (
    version TEXT PRIMARY KEY,
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

INSERT INTO schema_migrations (version, description) 
VALUES ('1.0.0', '基于Element的用户系统初始架构');
```

### 扩展到其他数据库

**PostgreSQL扩展**：
```sql
-- 利用PostgreSQL的JSONB类型
ALTER TABLE elements ALTER COLUMN element_data TYPE JSONB;
CREATE INDEX idx_elements_jsonb_gin ON elements USING GIN (element_data);
```

**MySQL扩展**：
```sql
-- 利用MySQL的JSON类型和虚拟列
ALTER TABLE elements ADD COLUMN username_virtual VARCHAR(255) 
    AS (JSON_UNQUOTE(JSON_EXTRACT(element_data, '$.custom_data.username'))) VIRTUAL;
```

## 📊 存储容量估算

### 单个Element存储大小
- **基础字段**：~200 bytes
- **JSON数据**：~2-5 KB（用户Element）
- **索引开销**：~300 bytes
- **总计**：~3-6 KB per Element

### 系统容量规划
| 用户规模 | Elements数量 | 预估存储 | 建议配置 |
|---------|-------------|----------|----------|
| 1,000用户 | ~5,000 | ~25 MB | SQLite默认 |
| 10,000用户 | ~50,000 | ~250 MB | SQLite + WAL模式 |
| 100,000用户 | ~500,000 | ~2.5 GB | 迁移PostgreSQL |

这个设计既保持了Element模型的完整性，又针对用户系统的查询模式进行了优化，为后续扩展到其他数据库打下了良好基础！ 