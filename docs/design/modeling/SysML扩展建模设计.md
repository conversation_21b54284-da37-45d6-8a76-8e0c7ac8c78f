# SysML扩展建模设计

## 📋 项目概述

**文档名称**：SysML扩展建模设计  
**设计时间**：2025年6月30日  
**版本**：v1.0  
**目标**：基于UML核心建模系统，扩展支持SysML（Systems Modeling Language）的9图1表，提供完整的系统工程建模能力

## 🎯 SysML概述

### SysML简介
SysML（Systems Modeling Language）是基于UML的系统建模语言扩展，专门用于系统工程建模。SysML提供了9种图形类型和1种表格类型，**全部可以基于UML基础图形进行扩展和定制**。

### SysML 9图1表架构
```
SysML建模视图体系：
├── 结构图（Structure Diagrams）
│   ├── 1. 块定义图（Block Definition Diagram, bdd）    # 基于UML类图扩展
│   ├── 2. 内部块图（Internal Block Diagram, ibd）     # 基于UML复合结构图扩展  
│   └── 3. 参数图（Parametric Diagram, par）           # SysML特有图形
├── 行为图（Behavior Diagrams）
│   ├── 4. 活动图（Activity Diagram, act）             # 基于UML活动图扩展
│   ├── 5. 时序图（Sequence Diagram, sd）              # 基于UML时序图扩展
│   ├── 6. 状态机图（State Machine Diagram, stm）     # 基于UML状态图扩展
│   └── 7. 用例图（Use Case Diagram, uc）             # 基于UML用例图扩展
├── 需求图（Requirements Diagram）
│   └── 8. 需求图（Requirements Diagram, req）        # SysML特有图形
├── 分配图（Allocation）
│   └── 9. 分配表（Allocation Table）                 # SysML特有表格
└── 包图（Package Diagrams）
    └── 包图（Package Diagram, pkg）                   # 基于UML包图扩展
```

## 🏗️ SysML扩展架构设计

### 扩展架构
```
┌─────────────────────────────────────────────────────────┐
│                    SysML建模层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  SysML引擎  │ │  约束求解器 │ │  需求管理器 │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
                            │ 扩展
┌─────────────────────────────────────────────────────────┐
│                    UML基础层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  UML元模型  │ │  图形引擎   │ │  渲染引擎   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### SysML扩展组件
```python
class SysMLExtension:
    """SysML扩展管理器"""
    
    def __init__(self, uml_metamodel: UMLMetaModel):
        self.base_metamodel = uml_metamodel
        self.sysml_stereotypes = {}
        self.sysml_elements = {}
        self.constraint_solver = ConstraintSolver()
        self.requirements_manager = RequirementsManager()
        self._initialize_sysml_elements()
    
    def _initialize_sysml_elements(self):
        """初始化SysML特有元素"""
        # SysML块（Block）- 扩展自UML类
        self.sysml_elements.update({
            'Block': BlockElement,
            'ValueType': ValueTypeElement,
            'Constraint': ConstraintElement,
            'Requirement': RequirementElement,
            'TestCase': TestCaseElement,
            'Problem': ProblemElement,
            'Rationale': RationaleElement
        })
        
        # SysML关系 - 扩展自UML关系
        self.sysml_elements.update({
            'Satisfy': SatisfyRelationship,
            'Verify': VerifyRelationship,
            'Trace': TraceRelationship,
            'Refine': RefineRelationship,
            'DeriveReqt': DeriveReqtRelationship,
            'Copy': CopyRelationship,
            'Allocate': AllocateRelationship
        })
        
        # SysML构造型
        self.sysml_stereotypes.update({
            '«block»': 'Block',
            '«valueType»': 'ValueType', 
            '«constraint»': 'Constraint',
            '«requirement»': 'Requirement',
            '«testCase»': 'TestCase',
            '«problem»': 'Problem',
            '«rationale»': 'Rationale',
            '«flowProperty»': 'FlowProperty',
            '«flowSpecification»': 'FlowSpecification'
        })
```

## 🔧 SysML 9图1表具体实现

### 1. 块定义图（Block Definition Diagram）
```python
class BlockDefinitionDiagram(UMLDiagramType):
    """块定义图 - 基于UML类图扩展"""
    
    def get_diagram_type(self) -> str:
        return 'BlockDefinitionDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['Block', 'ValueType', 'Constraint', 'Interface', 'Signal', 'DataType']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Association', 'Generalization', 'Dependency', 'Aggregation', 'Composition']

class BlockElement(ClassElement):
    """SysML块元素 - 扩展UML类"""
    
    def __init__(self, properties: Dict):
        super().__init__(properties)
        self.stereotype = '«block»'
        
        # SysML块特有属性
        self.value_properties = properties.get('value_properties', [])
        self.flow_properties = properties.get('flow_properties', [])
        self.constraint_properties = properties.get('constraint_properties', [])
        self.part_properties = properties.get('part_properties', [])
        self.reference_properties = properties.get('reference_properties', [])
        
    def add_value_property(self, name: str, value_type: str, unit: str = None):
        """添加值属性"""
        value_prop = {
            'name': name,
            'type': value_type,
            'unit': unit,
            'multiplicity': '1',
            'default_value': None
        }
        self.value_properties.append(value_prop)
    
    def add_flow_property(self, name: str, item_type: str, direction: str):
        """添加流属性"""
        if direction not in ['in', 'out', 'inout']:
            raise ValueError(f"无效的流方向: {direction}")
            
        flow_prop = {
            'name': name,
            'type': item_type,
            'direction': direction,
            'stereotype': '«flowProperty»'
        }
        self.flow_properties.append(flow_prop)
```

### 2. 内部块图（Internal Block Diagram）
```python
class InternalBlockDiagram(UMLDiagramType):
    """内部块图 - 基于UML复合结构图扩展"""
    
    def get_diagram_type(self) -> str:
        return 'InternalBlockDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['PartProperty', 'ReferenceProperty', 'Port', 'FlowPort', 'ProxyPort']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Connector', 'ItemFlow', 'Binding']

class FlowPort(UMLElement):
    """流端口"""
    
    def __init__(self, properties: Dict):
        super().__init__(properties)
        self.direction = properties.get('direction', 'inout')
        self.flow_specification = properties.get('flow_specification')
        self.is_atomic = properties.get('is_atomic', False)
        self.is_conjugated = properties.get('is_conjugated', False)
```

### 3. 参数图（Parametric Diagram）
```python
class ParametricDiagram(UMLDiagramType):
    """参数图 - SysML特有图形"""
    
    def get_diagram_type(self) -> str:
        return 'ParametricDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['ConstraintProperty', 'ConstraintParameter', 'ValueProperty']
    
    def get_supported_relationships(self) -> List[str]:
        return ['ConstraintBinding', 'ParameterBinding']

class ConstraintProperty(UMLElement):
    """约束属性"""
    
    def __init__(self, properties: Dict):
        super().__init__(properties)
        self.constraint_definition = properties.get('constraint_definition')
        self.parameters = properties.get('parameters', [])
        self.constraint_expression = properties.get('constraint_expression', '')
```

### 4. 需求图（Requirements Diagram）
```python
class RequirementsDiagram(UMLDiagramType):
    """需求图 - SysML特有图形"""
    
    def get_diagram_type(self) -> str:
        return 'RequirementsDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['Requirement', 'TestCase', 'Problem', 'Rationale']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Satisfy', 'Verify', 'Trace', 'Refine', 'DeriveReqt', 'Copy']

class RequirementElement(UMLElement):
    """需求元素"""
    
    def __init__(self, properties: Dict):
        super().__init__(properties)
        self.stereotype = '«requirement»'
        
        # 需求属性
        self.requirement_id = properties.get('requirement_id', '')
        self.text = properties.get('text', '')
        self.priority = properties.get('priority', 'medium')
        self.status = properties.get('status', 'proposed')
        self.verification_method = properties.get('verification_method', '')
        self.risk = properties.get('risk', 'low')
        
        # 追溯信息
        self.derived_from = properties.get('derived_from', [])
        self.satisfies = properties.get('satisfies', [])
        self.verified_by = properties.get('verified_by', [])
```

### 5. 分配表（Allocation Table）
```python
class AllocationTable(UMLDiagramType):
    """分配表 - SysML特有表格"""
    
    def get_diagram_type(self) -> str:
        return 'AllocationTable'
    
    def get_supported_elements(self) -> List[str]:
        return ['AllocationEntry']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Allocate']

class AllocationMatrix:
    """分配矩阵管理器"""
    
    def __init__(self):
        self.allocations = []
    
    def add_allocation(self, source_type: str, source_id: str, 
                      target_type: str, target_id: str, 
                      allocation_type: str = 'structural'):
        """添加分配"""
        allocation = {
            'source_type': source_type,
            'source_id': source_id,
            'target_type': target_type,
            'target_id': target_id,
            'allocation_type': allocation_type,
            'timestamp': datetime.now().isoformat()
        }
        self.allocations.append(allocation)
    
    def generate_matrix(self, source_elements: List, target_elements: List) -> Dict:
        """生成分配矩阵"""
        matrix = {}
        
        # 初始化矩阵
        for source in source_elements:
            matrix[source.id] = {}
            for target in target_elements:
                matrix[source.id][target.id] = None
        
        # 填充分配关系
        for allocation in self.allocations:
            source_id = allocation['source_id']
            target_id = allocation['target_id']
            
            if source_id in matrix and target_id in matrix[source_id]:
                matrix[source_id][target_id] = allocation['allocation_type']
        
        return matrix
```

## 🔧 SysML专业功能

### 1. 约束求解器
```python
class ConstraintSolver:
    """SysML约束求解器"""
    
    def __init__(self):
        self.constraints = []
        self.variables = {}
        self.equations = []
    
    def solve(self, constraint_properties: List[ConstraintProperty]) -> Dict:
        """求解约束方程组"""
        # 收集约束表达式
        equations = []
        variables = set()
        
        for constraint in constraint_properties:
            equation = constraint.constraint_expression
            equations.append(equation)
            
            # 提取变量
            constraint_vars = self._extract_variables(equation)
            variables.update(constraint_vars)
        
        # 数值求解
        solution = self._solve_equations(equations, list(variables))
        
        return {
            'variables': list(variables),
            'equations': equations,
            'solution': solution,
            'status': 'solved' if solution else 'unsolved'
        }
    
    def _extract_variables(self, expression: str) -> Set[str]:
        """从表达式中提取变量"""
        import re
        # 简化实现：提取字母变量
        variables = re.findall(r'[a-zA-Z_][a-zA-Z0-9_]*', expression)
        return set(variables)
    
    def _solve_equations(self, equations: List[str], variables: List[str]) -> Dict:
        """求解方程组"""
        # 简化实现：返回示例解
        return {var: f"solution_for_{var}" for var in variables}
```

### 2. 需求管理器
```python
class RequirementsManager:
    """SysML需求管理器"""
    
    def __init__(self):
        self.requirements = []
        self.traceability_links = []
        self.verification_cases = []
    
    def validate(self, requirements: List[RequirementElement], 
                elements: List[UMLElement], 
                relationships: List[UMLRelationship]) -> List[str]:
        """验证需求"""
        violations = []
        
        for req in requirements:
            # 检查需求完整性
            req_violations = self._validate_requirement_completeness(req)
            violations.extend(req_violations)
            
            # 检查追溯关系
            trace_violations = self._validate_traceability(req, relationships)
            violations.extend(trace_violations)
            
            # 检查验证覆盖
            verify_violations = self._validate_verification_coverage(req, relationships)
            violations.extend(verify_violations)
        
        return violations
    
    def _validate_requirement_completeness(self, requirement: RequirementElement) -> List[str]:
        """验证需求完整性"""
        violations = []
        
        if not requirement.requirement_id:
            violations.append(f"需求 {requirement.name} 缺少需求ID")
        
        if not requirement.text:
            violations.append(f"需求 {requirement.name} 缺少需求文本")
        
        if not requirement.verification_method:
            violations.append(f"需求 {requirement.name} 缺少验证方法")
        
        return violations
    
    def _validate_traceability(self, requirement: RequirementElement, 
                              relationships: List[UMLRelationship]) -> List[str]:
        """验证追溯关系"""
        violations = []
        
        # 检查是否有满足关系
        has_satisfy = any(
            isinstance(rel, SatisfyRelationship) and rel.target_id == requirement.id
            for rel in relationships
        )
        
        if not has_satisfy:
            violations.append(f"需求 {requirement.name} 没有被任何设计元素满足")
        
        return violations
    
    def generate_traceability_matrix(self, requirements: List[RequirementElement],
                                   design_elements: List[UMLElement],
                                   relationships: List[UMLRelationship]) -> Dict:
        """生成追溯矩阵"""
        matrix = {}
        
        for req in requirements:
            matrix[req.id] = {
                'name': req.name,
                'satisfied_by': [],
                'verified_by': [],
                'derived_from': [],
                'refined_by': []
            }
            
            for rel in relationships:
                if rel.target_id == req.id:
                    if isinstance(rel, SatisfyRelationship):
                        matrix[req.id]['satisfied_by'].append(rel.source_id)
                    elif isinstance(rel, VerifyRelationship):
                        matrix[req.id]['verified_by'].append(rel.source_id)
                    elif isinstance(rel, DeriveReqtRelationship):
                        matrix[req.id]['derived_from'].append(rel.source_id)
                    elif isinstance(rel, RefineRelationship):
                        matrix[req.id]['refined_by'].append(rel.source_id)
        
        return matrix
```

### 3. SysML关系类型
```python
class SatisfyRelationship(UMLRelationship):
    """满足关系 - 设计元素满足需求"""
    
    def get_relationship_type(self) -> str:
        return 'Satisfy'
    
    def validate(self, source_element: UMLElement, target_element: UMLElement) -> List[str]:
        violations = []
        
        # 满足关系的目标必须是需求
        if not isinstance(target_element, RequirementElement):
            violations.append(f"满足关系的目标必须是需求元素")
        
        return violations

class VerifyRelationship(UMLRelationship):
    """验证关系 - 测试用例验证需求"""
    
    def get_relationship_type(self) -> str:
        return 'Verify'

class TraceRelationship(UMLRelationship):
    """追溯关系 - 通用追溯链接"""
    
    def get_relationship_type(self) -> str:
        return 'Trace'

class RefineRelationship(UMLRelationship):
    """细化关系 - 需求细化关系"""
    
    def get_relationship_type(self) -> str:
        return 'Refine'

class AllocateRelationship(UMLRelationship):
    """分配关系 - 元素分配关系"""
    
    def __init__(self, source_id: str, target_id: str, properties: Dict = None):
        super().__init__(source_id, target_id, properties)
        self.allocation_type = properties.get('allocation_type', 'structural') if properties else 'structural'
        self.allocation_properties = properties.get('allocation_properties', {}) if properties else {}
    
    def get_relationship_type(self) -> str:
        return 'Allocate'
```

## 📋 SysML应用场景

### 系统工程建模
- **⚙️ 系统架构**：复杂系统的层次化建模
- **🔧 子系统分解**：系统功能和结构分解
- **📊 接口定义**：系统间接口和数据流建模
- **🎯 需求工程**：需求捕获、分析和追溯

### 生物医学系统建模
- **🧬 生物过程建模**：生物化学过程的SysML建模
- **🏥 医疗设备系统**：医疗设备的系统级设计
- **📈 药物研发流程**：药物研发的系统化建模
- **🔬 实验系统设计**：实验设备和流程建模

### MBSE工程应用
- **🚀 航空航天**：航空航天系统工程
- **🚗 汽车工程**：汽车电子系统设计
- **🏭 制造系统**：智能制造系统建模
- **🌐 物联网系统**：IoT系统架构设计

---

**文档版本**：v1.0  
**最后更新**：2025年6月30日  
**相关文档**：
- UML核心建模系统设计.md
- 视图组件定制系统设计.md
- 前端Vue集成实现设计.md 