# UML核心建模系统设计

## 📋 项目概述

**文档名称**：UML核心建模系统设计  
**设计时间**：2025年6月30日  
**版本**：v1.0  
**目标**：构建基于UML 2.5标准的核心建模引擎，支持14种标准UML图形的创建、编辑和验证

## 🎯 核心目标

### UML标准合规性
- **✅ UML 2.5标准**：完全符合最新UML标准规范
- **📋 UML-DI支持**：符合UML图形交换标准
- **🔄 XMI兼容**：标准的模型交换格式
- **🏗️ MOF基础**：基于元对象设施的架构

### 核心功能
- **📊 完整UML支持**：14种标准UML图形的完整支持
- **🔧 智能验证**：实时的模型一致性检查
- **📐 自动布局**：多种专业布局算法
- **💾 标准格式**：XMI、UML-DI等标准格式支持

## 🏗️ 系统架构设计

### 分层架构
```
┌─────────────────────────────────────────────────────────┐
│                    建模引擎层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  UML引擎    │ │  图形引擎   │ │  验证引擎   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                    渲染引擎层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  SVG渲染    │ │  Canvas渲染 │ │  布局引擎   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                    数据管理层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  元模型库   │ │  模型存储   │ │  版本管理   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### 技术架构栈
```
UML建模技术栈：
├── Eclipse UML2             # UML元模型实现
├── XMI                     # 模型交换格式
├── UML-DI                  # 图形交换标准
├── PlantUML                # 文本到图形转换
├── Graphviz                # 图形布局算法
└── ANTLR                   # 语法解析器

可视化技术栈：
├── SVG                     # 矢量图形
├── Canvas 2D/WebGL         # 高性能渲染
├── Force-Directed Layout   # 自动布局
├── Dagre                   # 有向图布局
└── Cytoscape.js            # 图形分析和可视化
```

## 🔧 核心模块设计

### 1. UML元模型引擎

#### UML元模型管理器
```python
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod

class UMLMetaModel:
    """UML元模型管理器"""
    
    def __init__(self):
        self.elements = {}
        self.relationships = {}
        self.stereotypes = {}
        self.profiles = {}
        self._initialize_core_elements()
    
    def _initialize_core_elements(self):
        """初始化UML核心元素"""
        # 核心元素类型
        self.elements.update({
            # 结构元素
            'Class': ClassElement,
            'Interface': InterfaceElement,
            'Component': ComponentElement,
            'Package': PackageElement,
            'Actor': ActorElement,
            
            # 行为元素
            'UseCase': UseCaseElement,
            'Activity': ActivityElement,
            'State': StateElement,
            'Sequence': SequenceElement,
            
            # 关系元素
            'Association': AssociationRelationship,
            'Generalization': GeneralizationRelationship,
            'Dependency': DependencyRelationship,
            'Realization': RealizationRelationship
        })
    
    def create_element(self, element_type: str, properties: Dict) -> 'UMLElement':
        """创建UML元素"""
        if element_type not in self.elements:
            raise ValueError(f"未知的UML元素类型: {element_type}")
        
        element_class = self.elements[element_type]
        return element_class(properties)
    
    def validate_model(self, model: 'UMLModel') -> List[str]:
        """验证UML模型"""
        violations = []
        
        # 检查元素完整性
        for element in model.elements:
            element_violations = self._validate_element(element)
            violations.extend(element_violations)
        
        # 检查关系一致性
        for relationship in model.relationships:
            rel_violations = self._validate_relationship(relationship)
            violations.extend(rel_violations)
        
        return violations

class UMLElement(ABC):
    """UML元素基类"""
    
    def __init__(self, properties: Dict):
        self.id = properties.get('id')
        self.name = properties.get('name', '')
        self.stereotype = properties.get('stereotype')
        self.tagged_values = properties.get('tagged_values', {})
        self.documentation = properties.get('documentation', '')
        
        # 图形属性
        self.visual_properties = {
            'x': properties.get('x', 0),
            'y': properties.get('y', 0),
            'width': properties.get('width', 100),
            'height': properties.get('height', 50),
            'fill_color': properties.get('fill_color', '#ffffff'),
            'stroke_color': properties.get('stroke_color', '#000000'),
            'stroke_width': properties.get('stroke_width', 1)
        }
    
    @abstractmethod
    def get_element_type(self) -> str:
        """获取元素类型"""
        pass
    
    @abstractmethod
    def validate(self) -> List[str]:
        """验证元素"""
        pass
    
    def to_xmi(self) -> str:
        """导出为XMI格式"""
        pass
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'type': self.get_element_type(),
            'stereotype': self.stereotype,
            'tagged_values': self.tagged_values,
            'documentation': self.documentation,
            'visual_properties': self.visual_properties
        }

class ClassElement(UMLElement):
    """UML类元素"""
    
    def __init__(self, properties: Dict):
        super().__init__(properties)
        self.attributes = properties.get('attributes', [])
        self.operations = properties.get('operations', [])
        self.is_abstract = properties.get('is_abstract', False)
        self.visibility = properties.get('visibility', 'public')
    
    def get_element_type(self) -> str:
        return 'Class'
    
    def validate(self) -> List[str]:
        violations = []
        
        if not self.name:
            violations.append(f"类 {self.id} 缺少名称")
        
        # 验证属性
        for attr in self.attributes:
            if not attr.get('name'):
                violations.append(f"类 {self.name} 的属性缺少名称")
        
        # 验证操作
        for op in self.operations:
            if not op.get('name'):
                violations.append(f"类 {self.name} 的操作缺少名称")
        
        return violations
    
    def add_attribute(self, name: str, type_name: str, visibility: str = 'private'):
        """添加属性"""
        attribute = {
            'name': name,
            'type': type_name,
            'visibility': visibility,
            'multiplicity': '1',
            'default_value': None
        }
        self.attributes.append(attribute)
    
    def add_operation(self, name: str, return_type: str = 'void', 
                     parameters: List[Dict] = None, visibility: str = 'public'):
        """添加操作"""
        operation = {
            'name': name,
            'return_type': return_type,
            'parameters': parameters or [],
            'visibility': visibility,
            'is_abstract': False,
            'is_static': False
        }
        self.operations.append(operation)
```

#### UML关系管理器
```python
class UMLRelationship(ABC):
    """UML关系基类"""
    
    def __init__(self, source_id: str, target_id: str, properties: Dict = None):
        self.id = properties.get('id') if properties else None
        self.source_id = source_id
        self.target_id = target_id
        self.name = properties.get('name', '') if properties else ''
        self.stereotype = properties.get('stereotype') if properties else None
        
        # 图形属性
        self.visual_properties = {
            'waypoints': properties.get('waypoints', []) if properties else [],
            'stroke_color': properties.get('stroke_color', '#000000') if properties else '#000000',
            'stroke_width': properties.get('stroke_width', 1) if properties else 1,
            'stroke_style': properties.get('stroke_style', 'solid') if properties else 'solid'
        }
    
    @abstractmethod
    def get_relationship_type(self) -> str:
        """获取关系类型"""
        pass
    
    @abstractmethod
    def validate(self, source_element: UMLElement, target_element: UMLElement) -> List[str]:
        """验证关系"""
        pass

class AssociationRelationship(UMLRelationship):
    """关联关系"""
    
    def __init__(self, source_id: str, target_id: str, properties: Dict = None):
        super().__init__(source_id, target_id, properties)
        self.source_multiplicity = properties.get('source_multiplicity', '1') if properties else '1'
        self.target_multiplicity = properties.get('target_multiplicity', '1') if properties else '1'
        self.source_role = properties.get('source_role', '') if properties else ''
        self.target_role = properties.get('target_role', '') if properties else ''
        self.is_navigable = properties.get('is_navigable', True) if properties else True
    
    def get_relationship_type(self) -> str:
        return 'Association'
    
    def validate(self, source_element: UMLElement, target_element: UMLElement) -> List[str]:
        violations = []
        
        # 检查元素类型兼容性
        if not self._can_associate(source_element, target_element):
            violations.append(f"元素类型 {source_element.get_element_type()} 和 {target_element.get_element_type()} 不能建立关联关系")
        
        return violations
    
    def _can_associate(self, source: UMLElement, target: UMLElement) -> bool:
        """检查是否可以建立关联"""
        associable_types = ['Class', 'Interface', 'Component', 'Actor']
        return (source.get_element_type() in associable_types and 
                target.get_element_type() in associable_types)

class GeneralizationRelationship(UMLRelationship):
    """泛化关系（继承）"""
    
    def get_relationship_type(self) -> str:
        return 'Generalization'
    
    def validate(self, source_element: UMLElement, target_element: UMLElement) -> List[str]:
        violations = []
        
        # 检查循环继承
        if self._creates_cycle(source_element, target_element):
            violations.append(f"泛化关系创建了循环继承")
        
        # 检查类型兼容性
        if source_element.get_element_type() != target_element.get_element_type():
            violations.append(f"泛化关系的源和目标必须是相同类型的元素")
        
        return violations
    
    def _creates_cycle(self, source: UMLElement, target: UMLElement) -> bool:
        """检查是否创建循环继承"""
        # 简化实现，实际需要遍历整个继承树
        return source.id == target.id
```

### 2. 图形渲染引擎

#### 可视化渲染器
```python
class DiagramRenderer:
    """图形渲染器"""
    
    def __init__(self, renderer_type: str = 'svg'):
        self.renderer_type = renderer_type
        self.layout_engine = LayoutEngine()
        self.style_manager = StyleManager()
        
    def render_diagram(self, diagram: 'UMLDiagram') -> Dict:
        """渲染UML图"""
        # 应用布局算法
        layout_result = self.layout_engine.apply_layout(diagram)
        
        # 应用样式
        styled_elements = self.style_manager.apply_styles(
            diagram.elements, diagram.style_profile
        )
        
        # 渲染图形
        if self.renderer_type == 'svg':
            return self._render_svg(styled_elements, layout_result)
        elif self.renderer_type == 'canvas':
            return self._render_canvas(styled_elements, layout_result)
        else:
            raise ValueError(f"不支持的渲染器类型: {self.renderer_type}")
    
    def _render_svg(self, elements: List[UMLElement], layout: Dict) -> str:
        """渲染为SVG格式"""
        svg_content = ['<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 800">']
        
        # 渲染元素
        for element in elements:
            svg_element = self._element_to_svg(element, layout)
            svg_content.append(svg_element)
        
        # 渲染关系
        for relationship in layout.get('relationships', []):
            svg_relationship = self._relationship_to_svg(relationship)
            svg_content.append(svg_relationship)
        
        svg_content.append('</svg>')
        return '\n'.join(svg_content)
    
    def _element_to_svg(self, element: UMLElement, layout: Dict) -> str:
        """将UML元素转换为SVG"""
        pos = layout['positions'].get(element.id, {'x': 0, 'y': 0})
        visual = element.visual_properties
        
        if element.get_element_type() == 'Class':
            return self._render_class_svg(element, pos, visual)
        elif element.get_element_type() == 'Actor':
            return self._render_actor_svg(element, pos, visual)
        # ... 其他元素类型
        
        return ''
    
    def _render_class_svg(self, class_element: ClassElement, pos: Dict, visual: Dict) -> str:
        """渲染类元素为SVG"""
        x, y = pos['x'], pos['y']
        width, height = visual['width'], visual['height']
        
        # 计算分隔线位置
        title_height = 30
        attr_height = len(class_element.attributes) * 20 + 10
        
        svg_parts = [
            # 类框
            f'<rect x="{x}" y="{y}" width="{width}" height="{height}" '
            f'fill="{visual["fill_color"]}" stroke="{visual["stroke_color"]}" '
            f'stroke-width="{visual["stroke_width"]}"/>',
            
            # 类名
            f'<text x="{x + width/2}" y="{y + title_height/2}" '
            f'text-anchor="middle" dominant-baseline="middle" '
            f'font-family="Arial" font-size="12" font-weight="bold">'
            f'{class_element.name}</text>',
            
            # 分隔线1
            f'<line x1="{x}" y1="{y + title_height}" x2="{x + width}" y2="{y + title_height}" '
            f'stroke="{visual["stroke_color"]}" stroke-width="1"/>',
        ]
        
        # 属性列表
        attr_y = y + title_height + 15
        for attr in class_element.attributes:
            visibility_symbol = self._get_visibility_symbol(attr.get('visibility', 'private'))
            attr_text = f"{visibility_symbol}{attr['name']}: {attr['type']}"
            svg_parts.append(
                f'<text x="{x + 5}" y="{attr_y}" '
                f'font-family="Arial" font-size="10">{attr_text}</text>'
            )
            attr_y += 20
        
        # 分隔线2
        if class_element.operations:
            sep_y = y + title_height + attr_height
            svg_parts.append(
                f'<line x1="{x}" y1="{sep_y}" x2="{x + width}" y2="{sep_y}" '
                f'stroke="{visual["stroke_color"]}" stroke-width="1"/>'
            )
            
            # 操作列表
            op_y = sep_y + 15
            for op in class_element.operations:
                visibility_symbol = self._get_visibility_symbol(op.get('visibility', 'public'))
                op_text = f"{visibility_symbol}{op['name']}(): {op['return_type']}"
                svg_parts.append(
                    f'<text x="{x + 5}" y="{op_y}" '
                    f'font-family="Arial" font-size="10">{op_text}</text>'
                )
                op_y += 20
        
        return '\n'.join(svg_parts)
    
    def _get_visibility_symbol(self, visibility: str) -> str:
        """获取可见性符号"""
        symbols = {
            'public': '+',
            'private': '-',
            'protected': '#',
            'package': '~'
        }
        return symbols.get(visibility, '+')

class LayoutEngine:
    """布局引擎"""
    
    def __init__(self):
        self.algorithms = {
            'hierarchical': HierarchicalLayout(),
            'force_directed': ForceDirectedLayout(),
            'circular': CircularLayout(),
            'grid': GridLayout()
        }
    
    def apply_layout(self, diagram: 'UMLDiagram', algorithm: str = 'hierarchical') -> Dict:
        """应用布局算法"""
        if algorithm not in self.algorithms:
            algorithm = 'hierarchical'
        
        layout_algo = self.algorithms[algorithm]
        return layout_algo.calculate_layout(diagram.elements, diagram.relationships)

class HierarchicalLayout:
    """层次化布局算法"""
    
    def calculate_layout(self, elements: List[UMLElement], 
                        relationships: List[UMLRelationship]) -> Dict:
        """计算层次化布局"""
        positions = {}
        
        # 构建层次结构
        levels = self._build_hierarchy(elements, relationships)
        
        # 分层排列
        y_offset = 50
        level_height = 150
        
        for level_index, level_elements in enumerate(levels):
            y = y_offset + level_index * level_height
            x_offset = 50
            element_width = 200
            element_spacing = 50
            
            for element_index, element in enumerate(level_elements):
                x = x_offset + element_index * (element_width + element_spacing)
                positions[element.id] = {'x': x, 'y': y}
        
        return {
            'positions': positions,
            'relationships': self._calculate_relationship_paths(relationships, positions)
        }
```

### 3. UML图形类型支持

#### 14种UML图形类型
```python
class UMLDiagramType(ABC):
    """UML图形类型基类"""
    
    @abstractmethod
    def get_diagram_type(self) -> str:
        """获取图形类型"""
        pass
    
    @abstractmethod
    def get_supported_elements(self) -> List[str]:
        """获取支持的元素类型"""
        pass
    
    @abstractmethod
    def get_supported_relationships(self) -> List[str]:
        """获取支持的关系类型"""
        pass
    
    @abstractmethod
    def validate_diagram(self, diagram: 'UMLDiagram') -> List[str]:
        """验证图形"""
        pass

# 1. 类图
class ClassDiagram(UMLDiagramType):
    """类图"""
    
    def get_diagram_type(self) -> str:
        return 'ClassDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['Class', 'Interface', 'Package', 'Enumeration', 'DataType']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Association', 'Generalization', 'Realization', 'Dependency', 'Aggregation', 'Composition']

# 2. 用例图
class UseCaseDiagram(UMLDiagramType):
    """用例图"""
    
    def get_diagram_type(self) -> str:
        return 'UseCaseDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['Actor', 'UseCase', 'System']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Association', 'Include', 'Extend', 'Generalization']

# 3. 时序图
class SequenceDiagram(UMLDiagramType):
    """时序图"""
    
    def get_diagram_type(self) -> str:
        return 'SequenceDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['Lifeline', 'ExecutionSpecification', 'Message', 'InteractionFragment']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Message', 'SelfMessage']

# 4. 活动图
class ActivityDiagram(UMLDiagramType):
    """活动图"""
    
    def get_diagram_type(self) -> str:
        return 'ActivityDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['Action', 'DecisionNode', 'MergeNode', 'ForkNode', 'JoinNode', 'InitialNode', 'FinalNode']
    
    def get_supported_relationships(self) -> List[str]:
        return ['ControlFlow', 'ObjectFlow']

# 5. 状态机图
class StateMachineDiagram(UMLDiagramType):
    """状态机图"""
    
    def get_diagram_type(self) -> str:
        return 'StateMachineDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['State', 'PseudoState', 'FinalState', 'Region']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Transition']

# 6. 组件图
class ComponentDiagram(UMLDiagramType):
    """组件图"""
    
    def get_diagram_type(self) -> str:
        return 'ComponentDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['Component', 'Interface', 'Port', 'Artifact']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Dependency', 'Realization', 'Usage']

# 7. 部署图
class DeploymentDiagram(UMLDiagramType):
    """部署图"""
    
    def get_diagram_type(self) -> str:
        return 'DeploymentDiagram'
    
    def get_supported_elements(self) -> List[str]:
        return ['Node', 'Device', 'ExecutionEnvironment', 'Artifact']
    
    def get_supported_relationships(self) -> List[str]:
        return ['Deployment', 'Communication', 'Dependency']

# 8-14. 其他UML图形类型
class PackageDiagram(UMLDiagramType):
    """包图"""
    def get_diagram_type(self) -> str:
        return 'PackageDiagram'

class ObjectDiagram(UMLDiagramType):
    """对象图"""
    def get_diagram_type(self) -> str:
        return 'ObjectDiagram'

class CommunicationDiagram(UMLDiagramType):
    """通信图"""
    def get_diagram_type(self) -> str:
        return 'CommunicationDiagram'

class InteractionOverviewDiagram(UMLDiagramType):
    """交互概览图"""
    def get_diagram_type(self) -> str:
        return 'InteractionOverviewDiagram'

class TimingDiagram(UMLDiagramType):
    """时序图"""
    def get_diagram_type(self) -> str:
        return 'TimingDiagram'

class CompositeStructureDiagram(UMLDiagramType):
    """复合结构图"""
    def get_diagram_type(self) -> str:
        return 'CompositeStructureDiagram'

class ProfileDiagram(UMLDiagramType):
    """概要图"""
    def get_diagram_type(self) -> str:
        return 'ProfileDiagram'

class UMLDiagramFactory:
    """UML图形工厂"""
    
    def __init__(self):
        self.diagram_types = {
            'ClassDiagram': ClassDiagram(),
            'UseCaseDiagram': UseCaseDiagram(),
            'SequenceDiagram': SequenceDiagram(),
            'ActivityDiagram': ActivityDiagram(),
            'StateMachineDiagram': StateMachineDiagram(),
            'ComponentDiagram': ComponentDiagram(),
            'DeploymentDiagram': DeploymentDiagram(),
            'PackageDiagram': PackageDiagram(),
            'ObjectDiagram': ObjectDiagram(),
            'CommunicationDiagram': CommunicationDiagram(),
            'InteractionOverviewDiagram': InteractionOverviewDiagram(),
            'TimingDiagram': TimingDiagram(),
            'CompositeStructureDiagram': CompositeStructureDiagram(),
            'ProfileDiagram': ProfileDiagram()
        }
    
    def create_diagram(self, diagram_type: str, name: str) -> 'UMLDiagram':
        """创建UML图"""
        if diagram_type not in self.diagram_types:
            raise ValueError(f"不支持的图形类型: {diagram_type}")
        
        diagram_type_obj = self.diagram_types[diagram_type]
        return UMLDiagram(name, diagram_type_obj)
    
    def get_supported_types(self) -> List[str]:
        """获取支持的图形类型"""
        return list(self.diagram_types.keys())

class UMLDiagram:
    """UML图形模型"""
    
    def __init__(self, name: str, diagram_type: UMLDiagramType):
        self.id = None  # 将在保存时生成
        self.name = name
        self.diagram_type = diagram_type
        self.elements = []
        self.relationships = []
        self.style_profile = 'default'
        self.metadata = {
            'created_at': None,
            'modified_at': None,
            'created_by': None,
            'version': '1.0'
        }
    
    def add_element(self, element: UMLElement):
        """添加元素"""
        if element.get_element_type() not in self.diagram_type.get_supported_elements():
            raise ValueError(f"图形类型 {self.diagram_type.get_diagram_type()} 不支持元素类型 {element.get_element_type()}")
        
        self.elements.append(element)
    
    def add_relationship(self, relationship: UMLRelationship):
        """添加关系"""
        if relationship.get_relationship_type() not in self.diagram_type.get_supported_relationships():
            raise ValueError(f"图形类型 {self.diagram_type.get_diagram_type()} 不支持关系类型 {relationship.get_relationship_type()}")
        
        self.relationships.append(relationship)
    
    def validate(self) -> List[str]:
        """验证图形"""
        violations = []
        
        # 图形类型特定验证
        type_violations = self.diagram_type.validate_diagram(self)
        violations.extend(type_violations)
        
        # 元素验证
        for element in self.elements:
            element_violations = element.validate()
            violations.extend(element_violations)
        
        # 关系验证
        for relationship in self.relationships:
            source_element = self._find_element(relationship.source_id)
            target_element = self._find_element(relationship.target_id)
            
            if source_element and target_element:
                rel_violations = relationship.validate(source_element, target_element)
                violations.extend(rel_violations)
            else:
                violations.append(f"关系 {relationship.id} 的源或目标元素不存在")
        
        return violations
    
    def _find_element(self, element_id: str) -> Optional[UMLElement]:
        """查找元素"""
        for element in self.elements:
            if element.id == element_id:
                return element
        return None
    
    def to_xmi(self) -> str:
        """导出为XMI格式"""
        # XMI导出实现
        pass
    
    def to_json(self) -> Dict:
        """转换为JSON格式"""
        return {
            'id': self.id,
            'name': self.name,
            'type': self.diagram_type.get_diagram_type(),
            'elements': [element.to_dict() for element in self.elements],
            'relationships': [
                {
                    'id': rel.id,
                    'type': rel.get_relationship_type(),
                    'source_id': rel.source_id,
                    'target_id': rel.target_id,
                    'visual_properties': rel.visual_properties
                }
                for rel in self.relationships
            ],
            'style_profile': self.style_profile,
            'metadata': self.metadata
        }
```

## 🎯 UML标准支持

### UML 2.5 元素类型
```
结构建模元素：
├── Class（类）
├── Interface（接口）
├── Component（组件）
├── Package（包）
├── Object（对象）
├── Node（节点）
├── Device（设备）
├── ExecutionEnvironment（执行环境）
├── Artifact（制品）
└── Port（端口）

行为建模元素：
├── UseCase（用例）
├── Actor（参与者）
├── Activity（活动）
├── Action（动作）
├── State（状态）
├── Transition（转换）
├── Lifeline（生命线）
├── Message（消息）
├── Interaction（交互）
└── Event（事件）

关系类型：
├── Association（关联）
├── Aggregation（聚合）
├── Composition（组合）
├── Generalization（泛化）
├── Realization（实现）
├── Dependency（依赖）
├── Usage（使用）
├── Include（包含）
├── Extend（扩展）
└── Deployment（部署）
```

### UML-DI图形交换标准
```python
class UMLDIExporter:
    """UML-DI格式导出器"""
    
    def __init__(self):
        self.namespace = "http://www.eclipse.org/uml2/5.0.0/UML"
        self.di_namespace = "http://www.eclipse.org/uml2/5.0.0/UML/DI"
    
    def export_diagram(self, diagram: UMLDiagram) -> str:
        """导出为UML-DI格式"""
        xmi_content = [
            '<?xml version="1.0" encoding="UTF-8"?>',
            f'<xmi:XMI xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:uml="{self.namespace}" xmlns:umldi="{self.di_namespace}">',
            '  <uml:Model xmi:id="model" name="UMLModel">',
        ]
        
        # 导出模型元素
        for element in diagram.elements:
            xmi_content.append(self._export_element(element))
        
        # 导出关系
        for relationship in diagram.relationships:
            xmi_content.append(self._export_relationship(relationship))
        
        xmi_content.extend([
            '  </uml:Model>',
            '  <umldi:UMLDiagram xmi:id="diagram" name="' + diagram.name + '">',
        ])
        
        # 导出图形信息
        for element in diagram.elements:
            xmi_content.append(self._export_diagram_element(element))
        
        xmi_content.extend([
            '  </umldi:UMLDiagram>',
            '</xmi:XMI>'
        ])
        
        return '\n'.join(xmi_content)
    
    def _export_element(self, element: UMLElement) -> str:
        """导出元素为XMI"""
        element_type = element.get_element_type().lower()
        attributes = f'xmi:id="{element.id}" name="{element.name}"'
        
        if hasattr(element, 'visibility'):
            attributes += f' visibility="{element.visibility}"'
        
        return f'    <packagedElement xmi:type="uml:{element_type}" {attributes}/>'
    
    def _export_diagram_element(self, element: UMLElement) -> str:
        """导出图形元素"""
        visual = element.visual_properties
        bounds = f'x="{visual["x"]}" y="{visual["y"]}" width="{visual["width"]}" height="{visual["height"]}"'
        
        return f'    <umldi:UMLShape xmi:id="shape_{element.id}" modelElement="{element.id}" {bounds}/>'
```

## 📋 功能特性

### 核心功能
- **📊 14种UML图形**：完整支持所有标准UML图形类型
- **🔧 实时验证**：模型一致性和完整性检查
- **📐 智能布局**：层次化、力导向、网格等布局算法
- **💾 标准格式**：XMI、UML-DI标准格式支持
- **🎨 样式系统**：丰富的视觉样式配置

### 高级功能
- **🔄 模型转换**：UML模型到代码、文档的转换
- **📚 元模型管理**：灵活的元模型扩展机制
- **🏗️ 架构验证**：架构约束和设计模式检查
- **📊 度量分析**：模型复杂度和质量度量

---

**文档版本**：v1.0  
**最后更新**：2025年6月30日  
**相关文档**：
- SysML扩展建模设计.md
- 视图组件定制系统设计.md
- 前端Vue集成实现设计.md 