# 前端Vue集成实现设计

## 📋 项目概述

**文档名称**：前端Vue集成实现设计  
**设计时间**：2025年6月30日  
**版本**：v1.0  
**目标**：基于Vue 3和TypeScript构建建模系统的前端界面，提供流畅的用户体验和强大的建模功能

## 🎯 前端架构设计

### 技术栈
```
前端技术栈：
├── Vue 3 + TypeScript        # 核心框架
├── Element Plus              # UI组件库  
├── Konva.js                  # 2D Canvas图形库
├── D3.js                     # 数据可视化
├── Pinia                     # 状态管理
├── Vue Router 4              # 路由管理
├── Vite                      # 构建工具
├── Vitest                    # 单元测试
└── Cypress                   # E2E测试
```

### 组件架构
```
前端组件架构：
├── 🎨 建模组件 (Modeling)
│   ├── DiagramEditor         # 图形编辑器
│   ├── ToolPalette          # 工具面板
│   ├── PropertyPanel        # 属性面板
│   └── LayerManager         # 图层管理
├── 🎭 可视化组件 (Visualization)
│   ├── UMLRenderer          # UML渲染器
│   ├── SysMLRenderer        # SysML渲染器
│   ├── ThemeSelector        # 主题选择器
│   └── ViewControls         # 视图控制
├── 📚 知识组件 (Knowledge)
│   ├── KnowledgeExplorer    # 知识浏览器
│   ├── DocumentViewer       # 文档查看器
│   ├── SearchInterface      # 搜索界面
│   └── TagManager           # 标签管理
└── 🔧 通用组件 (Common)
    ├── Layout               # 布局组件
    ├── Navigation           # 导航组件
    ├── FileManager          # 文件管理
    └── UserInterface        # 用户界面
```

## 🎨 核心建模组件

### 1. 图形编辑器组件
```vue
<!-- DiagramEditor.vue -->
<template>
  <div class="diagram-editor">
    <div class="editor-toolbar">
      <DiagramToolbar 
        :tools="availableTools"
        :active-tool="activeTool"
        @tool-select="handleToolSelect"
      />
    </div>
    
    <div class="editor-workspace">
      <div class="editor-canvas-container">
        <konva-stage
          ref="stage"
          :config="stageConfig"
          @mousedown="handleMouseDown"
          @mousemove="handleMouseMove"
          @mouseup="handleMouseUp"
        >
          <konva-layer ref="backgroundLayer">
            <GridBackground :grid-size="gridSize" />
          </konva-layer>
          
          <konva-layer ref="elementsLayer">
            <UMLElement
              v-for="element in diagram.elements"
              :key="element.id"
              :element="element"
              :selected="selectedElements.includes(element.id)"
              @select="handleElementSelect"
              @update="handleElementUpdate"
            />
          </konva-layer>
          
          <konva-layer ref="relationshipsLayer">
            <UMLRelationship
              v-for="relationship in diagram.relationships"
              :key="relationship.id"
              :relationship="relationship"
              @select="handleRelationshipSelect"
            />
          </konva-layer>
        </konva-stage>
      </div>
      
      <div class="editor-minimap">
        <DiagramMinimap :diagram="diagram" :viewport="viewport" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useDiagramStore } from '@/stores/diagram'
import { useModelingEngine } from '@/composables/useModelingEngine'
import { UMLDiagram, UMLElement, UMLRelationship } from '@/types/modeling'

interface Props {
  diagramId: string
  readOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readOnly: false
})

// 状态管理
const diagramStore = useDiagramStore()
const modelingEngine = useModelingEngine()

// 响应式状态
const stage = ref()
const activeTool = ref('select')
const selectedElements = ref<string[]>([])
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })

// 计算属性
const diagram = computed(() => diagramStore.getDiagram(props.diagramId))
const stageConfig = computed(() => ({
  width: 1200,
  height: 800,
  draggable: true,
  scaleX: viewport.value.scale,
  scaleY: viewport.value.scale,
  x: viewport.value.x,
  y: viewport.value.y
}))

const viewport = ref({
  x: 0,
  y: 0,
  scale: 1
})

const availableTools = computed(() => {
  const tools = ['select', 'pan', 'zoom']
  
  if (diagram.value) {
    const supportedElements = diagram.value.diagram_type.get_supported_elements()
    tools.push(...supportedElements.map(type => type.toLowerCase()))
  }
  
  return tools
})

// 事件处理
const handleToolSelect = (tool: string) => {
  activeTool.value = tool
  selectedElements.value = []
}

const handleMouseDown = (event: any) => {
  if (activeTool.value === 'pan') {
    isDragging.value = true
    dragStart.value = { x: event.evt.clientX, y: event.evt.clientY }
  } else if (activeTool.value !== 'select') {
    // 创建新元素
    createElementAtPosition(event.evt.layerX, event.evt.layerY)
  }
}

const handleMouseMove = (event: any) => {
  if (isDragging.value && activeTool.value === 'pan') {
    const dx = event.evt.clientX - dragStart.value.x
    const dy = event.evt.clientY - dragStart.value.y
    
    viewport.value.x += dx
    viewport.value.y += dy
    
    dragStart.value = { x: event.evt.clientX, y: event.evt.clientY }
  }
}

const handleMouseUp = () => {
  isDragging.value = false
}

const handleElementSelect = (elementId: string, multiSelect = false) => {
  if (multiSelect) {
    if (selectedElements.value.includes(elementId)) {
      selectedElements.value = selectedElements.value.filter(id => id !== elementId)
    } else {
      selectedElements.value.push(elementId)
    }
  } else {
    selectedElements.value = [elementId]
  }
}

const handleElementUpdate = (elementId: string, updates: Partial<UMLElement>) => {
  diagramStore.updateElement(props.diagramId, elementId, updates)
}

const createElementAtPosition = async (x: number, y: number) => {
  if (!diagram.value || activeTool.value === 'select' || activeTool.value === 'pan') {
    return
  }
  
  try {
    const elementType = activeTool.value
    const element = await modelingEngine.createElement(elementType, {
      x: x - viewport.value.x,
      y: y - viewport.value.y,
      name: `New${elementType.charAt(0).toUpperCase() + elementType.slice(1)}`
    })
    
    diagramStore.addElement(props.diagramId, element)
    activeTool.value = 'select'
  } catch (error) {
    console.error('Failed to create element:', error)
  }
}

// 生命周期
onMounted(() => {
  if (!diagram.value) {
    diagramStore.loadDiagram(props.diagramId)
  }
})

// 监听器
watch(() => props.diagramId, (newId) => {
  if (newId) {
    diagramStore.loadDiagram(newId)
    selectedElements.value = []
  }
})
</script>

<style scoped>
.diagram-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  height: 60px;
  border-bottom: 1px solid #e0e0e0;
  background: #f5f5f5;
}

.editor-workspace {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

.editor-canvas-container {
  flex: 1;
  background: #ffffff;
  background-image: radial-gradient(circle, #e0e0e0 1px, transparent 1px);
  background-size: 20px 20px;
}

.editor-minimap {
  width: 200px;
  height: 150px;
  position: absolute;
  top: 10px;
  right: 10px;
  border: 1px solid #ccc;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
}
</style>
```

### 2. UML元素组件
```vue
<!-- UMLElement.vue -->
<template>
  <konva-group
    :config="groupConfig"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @transformend="handleTransform"
    @click="handleClick"
    @dblclick="handleDoubleClick"
  >
    <!-- 元素主体 -->
    <konva-rect
      :config="rectConfig"
    />
    
    <!-- 构造型 -->
    <konva-text
      v-if="showStereotype"
      :config="stereotypeConfig"
    />
    
    <!-- 元素名称 -->
    <konva-text
      :config="nameConfig"
    />
    
    <!-- 分隔线 -->
    <konva-line
      v-for="(line, index) in separatorLines"
      :key="`separator-${index}`"
      :config="line"
    />
    
    <!-- 属性列表 -->
    <konva-text
      v-for="(attr, index) in attributeTexts"
      :key="`attr-${index}`"
      :config="attr"
    />
    
    <!-- 操作列表 -->
    <konva-text
      v-for="(op, index) in operationTexts"
      :key="`op-${index}`"
      :config="op"
    />
    
    <!-- 选择框 -->
    <konva-rect
      v-if="selected"
      :config="selectionConfig"
    />
  </konva-group>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { UMLElement as UMLElementType } from '@/types/modeling'
import { useTheme } from '@/composables/useTheme'

interface Props {
  element: UMLElementType
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false
})

const emit = defineEmits<{
  select: [elementId: string]
  update: [elementId: string, updates: Partial<UMLElementType>]
  edit: [elementId: string]
}>()

const theme = useTheme()
const isDragging = ref(false)

// 计算属性
const groupConfig = computed(() => ({
  x: props.element.visual_properties.x,
  y: props.element.visual_properties.y,
  draggable: true
}))

const rectConfig = computed(() => ({
  width: props.element.visual_properties.width,
  height: props.element.visual_properties.height,
  fill: props.element.visual_properties.fill_color,
  stroke: props.element.visual_properties.stroke_color,
  strokeWidth: props.element.visual_properties.stroke_width,
  cornerRadius: props.element.visual_properties.border_radius || 0
}))

const showStereotype = computed(() => {
  return props.element.stereotype && 
         props.element.get_element_type() !== 'Class'
})

const stereotypeConfig = computed(() => ({
  x: 5,
  y: 5,
  text: props.element.stereotype || '',
  fontSize: 10,
  fontStyle: 'italic',
  fill: theme.value.textColor
}))

const nameConfig = computed(() => ({
  x: 5,
  y: showStereotype.value ? 20 : 5,
  text: props.element.name,
  fontSize: 12,
  fontStyle: 'bold',
  fill: theme.value.textColor,
  width: props.element.visual_properties.width - 10
}))

const separatorLines = computed(() => {
  const lines = []
  const width = props.element.visual_properties.width
  let y = showStereotype.value ? 35 : 25
  
  if (props.element.get_element_type() === 'Class') {
    // 属性分隔线
    if (props.element.attributes.length > 0) {
      lines.push({
        points: [0, y, width, y],
        stroke: props.element.visual_properties.stroke_color,
        strokeWidth: 1
      })
      y += props.element.attributes.length * 15 + 10
    }
    
    // 操作分隔线
    if (props.element.operations.length > 0) {
      lines.push({
        points: [0, y, width, y],
        stroke: props.element.visual_properties.stroke_color,
        strokeWidth: 1
      })
    }
  }
  
  return lines
})

const attributeTexts = computed(() => {
  if (props.element.get_element_type() !== 'Class') return []
  
  const texts = []
  let y = showStereotype.value ? 50 : 40
  
  for (const attr of props.element.attributes) {
    const visibility = getVisibilitySymbol(attr.visibility)
    const text = `${visibility}${attr.name}: ${attr.type}`
    
    texts.push({
      x: 5,
      y: y,
      text: text,
      fontSize: 10,
      fill: theme.value.textColor
    })
    
    y += 15
  }
  
  return texts
})

const operationTexts = computed(() => {
  if (props.element.get_element_type() !== 'Class') return []
  
  const texts = []
  let y = showStereotype.value ? 50 : 40
  y += props.element.attributes.length * 15 + 15
  
  for (const op of props.element.operations) {
    const visibility = getVisibilitySymbol(op.visibility)
    const text = `${visibility}${op.name}(): ${op.return_type}`
    
    texts.push({
      x: 5,
      y: y,
      text: text,
      fontSize: 10,
      fill: theme.value.textColor
    })
    
    y += 15
  }
  
  return texts
})

const selectionConfig = computed(() => ({
  x: -2,
  y: -2,
  width: props.element.visual_properties.width + 4,
  height: props.element.visual_properties.height + 4,
  stroke: theme.value.selectionColor,
  strokeWidth: 2,
  dash: [5, 5],
  fill: 'transparent'
}))

// 工具函数
const getVisibilitySymbol = (visibility: string): string => {
  const symbols = {
    'public': '+',
    'private': '-',
    'protected': '#',
    'package': '~'
  }
  return symbols[visibility] || '+'
}

// 事件处理
const handleDragStart = () => {
  isDragging.value = true
}

const handleDragEnd = (event: any) => {
  isDragging.value = false
  
  const newX = event.target.x()
  const newY = event.target.y()
  
  emit('update', props.element.id, {
    visual_properties: {
      ...props.element.visual_properties,
      x: newX,
      y: newY
    }
  })
}

const handleTransform = (event: any) => {
  const node = event.target
  const newWidth = Math.max(100, node.width() * node.scaleX())
  const newHeight = Math.max(50, node.height() * node.scaleY())
  
  // 重置缩放
  node.scaleX(1)
  node.scaleY(1)
  
  emit('update', props.element.id, {
    visual_properties: {
      ...props.element.visual_properties,
      width: newWidth,
      height: newHeight
    }
  })
}

const handleClick = () => {
  if (!isDragging.value) {
    emit('select', props.element.id)
  }
}

const handleDoubleClick = () => {
  emit('edit', props.element.id)
}
</script>
```

### 3. 工具面板组件
```vue
<!-- ToolPalette.vue -->
<template>
  <div class="tool-palette">
    <div class="palette-header">
      <h3>工具</h3>
      <el-button 
        size="small" 
        :icon="collapsed ? 'ArrowRight' : 'ArrowDown'"
        @click="collapsed = !collapsed"
      />
    </div>
    
    <div v-show="!collapsed" class="palette-content">
      <!-- 基本工具 -->
      <div class="tool-group">
        <div class="group-title">基本工具</div>
        <div class="tool-grid">
          <ToolButton
            v-for="tool in basicTools"
            :key="tool.id"
            :tool="tool"
            :active="activeTool === tool.id"
            @click="handleToolSelect(tool.id)"
          />
        </div>
      </div>
      
      <!-- UML元素 -->
      <div class="tool-group" v-if="diagramType">
        <div class="group-title">{{ diagramType }} 元素</div>
        <div class="tool-grid">
          <ToolButton
            v-for="element in availableElements"
            :key="element.type"
            :tool="element"
            :active="activeTool === element.type"
            @click="handleToolSelect(element.type)"
          />
        </div>
      </div>
      
      <!-- 关系 -->
      <div class="tool-group" v-if="diagramType">
        <div class="group-title">关系</div>
        <div class="tool-grid">
          <ToolButton
            v-for="relation in availableRelationships"
            :key="relation.type"
            :tool="relation"
            :active="activeTool === relation.type"
            @click="handleToolSelect(relation.type)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useDiagramStore } from '@/stores/diagram'

interface Tool {
  id: string
  name: string
  icon: string
  tooltip: string
}

interface Props {
  diagramId?: string
  activeTool: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  toolSelect: [toolId: string]
}>()

const diagramStore = useDiagramStore()
const collapsed = ref(false)

// 基本工具
const basicTools: Tool[] = [
  { id: 'select', name: '选择', icon: 'mouse-pointer', tooltip: '选择和移动元素' },
  { id: 'pan', name: '平移', icon: 'hand', tooltip: '平移画布' },
  { id: 'zoom', name: '缩放', icon: 'search', tooltip: '缩放画布' }
]

// 计算属性
const diagram = computed(() => 
  props.diagramId ? diagramStore.getDiagram(props.diagramId) : null
)

const diagramType = computed(() => 
  diagram.value?.diagram_type.get_diagram_type()
)

const availableElements = computed(() => {
  if (!diagram.value) return []
  
  const elements = diagram.value.diagram_type.get_supported_elements()
  return elements.map(type => ({
    type: type.toLowerCase(),
    name: type,
    icon: getElementIcon(type),
    tooltip: `创建${type}`
  }))
})

const availableRelationships = computed(() => {
  if (!diagram.value) return []
  
  const relationships = diagram.value.diagram_type.get_supported_relationships()
  return relationships.map(type => ({
    type: type.toLowerCase(),
    name: type,
    icon: getRelationshipIcon(type),
    tooltip: `创建${type}关系`
  }))
})

// 工具函数
const getElementIcon = (elementType: string): string => {
  const icons = {
    'Class': 'rectangle',
    'Interface': 'circle',
    'Actor': 'user',
    'UseCase': 'ellipse',
    'Package': 'folder',
    'Component': 'box',
    'Node': 'server',
    'Block': 'square',
    'Requirement': 'file-text'
  }
  return icons[elementType] || 'rectangle'
}

const getRelationshipIcon = (relationshipType: string): string => {
  const icons = {
    'Association': 'arrow-right',
    'Generalization': 'triangle',
    'Dependency': 'arrow-right-dashed',
    'Realization': 'arrow-right-dotted',
    'Aggregation': 'diamond',
    'Composition': 'diamond-filled'
  }
  return icons[relationshipType] || 'arrow-right'
}

// 事件处理
const handleToolSelect = (toolId: string) => {
  emit('toolSelect', toolId)
}
</script>

<style scoped>
.tool-palette {
  width: 250px;
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.palette-header {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.palette-content {
  flex: 1;
  overflow-y: auto;
}

.tool-group {
  margin-bottom: 20px;
}

.group-title {
  padding: 8px 12px;
  background: #e9ecef;
  font-weight: bold;
  font-size: 12px;
  color: #495057;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 4px;
  padding: 8px;
}
</style>
```

## 📊 状态管理设计

### Pinia Store
```typescript
// stores/diagram.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { UMLDiagram, UMLElement, UMLRelationship } from '@/types/modeling'
import { diagramAPI } from '@/api/diagram'

export const useDiagramStore = defineStore('diagram', () => {
  // 状态
  const diagrams = ref<Map<string, UMLDiagram>>(new Map())
  const activeDiagramId = ref<string | null>(null)
  const selectedElements = ref<string[]>([])
  const clipboard = ref<any[]>([])
  const undoStack = ref<any[]>([])
  const redoStack = ref<any[]>([])

  // 计算属性
  const activeDiagram = computed(() => 
    activeDiagramId.value ? diagrams.value.get(activeDiagramId.value) : null
  )

  const diagramList = computed(() => 
    Array.from(diagrams.value.values())
  )

  // 动作
  const loadDiagram = async (diagramId: string) => {
    try {
      const diagram = await diagramAPI.getDiagram(diagramId)
      diagrams.value.set(diagramId, diagram)
      activeDiagramId.value = diagramId
    } catch (error) {
      console.error('Failed to load diagram:', error)
      throw error
    }
  }

  const createDiagram = async (type: string, name: string) => {
    try {
      const diagram = await diagramAPI.createDiagram({ type, name })
      diagrams.value.set(diagram.id, diagram)
      activeDiagramId.value = diagram.id
      return diagram
    } catch (error) {
      console.error('Failed to create diagram:', error)
      throw error
    }
  }

  const saveDiagram = async (diagramId: string) => {
    const diagram = diagrams.value.get(diagramId)
    if (!diagram) return

    try {
      const savedDiagram = await diagramAPI.saveDiagram(diagram)
      diagrams.value.set(diagramId, savedDiagram)
    } catch (error) {
      console.error('Failed to save diagram:', error)
      throw error
    }
  }

  const addElement = (diagramId: string, element: UMLElement) => {
    const diagram = diagrams.value.get(diagramId)
    if (!diagram) return

    // 记录操作用于撤销
    recordAction('addElement', { diagramId, element })
    
    diagram.elements.push(element)
    diagrams.value.set(diagramId, { ...diagram })
  }

  const updateElement = (diagramId: string, elementId: string, updates: Partial<UMLElement>) => {
    const diagram = diagrams.value.get(diagramId)
    if (!diagram) return

    const elementIndex = diagram.elements.findIndex(e => e.id === elementId)
    if (elementIndex === -1) return

    const oldElement = diagram.elements[elementIndex]
    recordAction('updateElement', { diagramId, elementId, oldElement })

    diagram.elements[elementIndex] = { ...oldElement, ...updates }
    diagrams.value.set(diagramId, { ...diagram })
  }

  const deleteElement = (diagramId: string, elementId: string) => {
    const diagram = diagrams.value.get(diagramId)
    if (!diagram) return

    const elementIndex = diagram.elements.findIndex(e => e.id === elementId)
    if (elementIndex === -1) return

    const element = diagram.elements[elementIndex]
    recordAction('deleteElement', { diagramId, element })

    diagram.elements.splice(elementIndex, 1)
    
    // 删除相关关系
    diagram.relationships = diagram.relationships.filter(
      r => r.source_id !== elementId && r.target_id !== elementId
    )
    
    diagrams.value.set(diagramId, { ...diagram })
  }

  const addRelationship = (diagramId: string, relationship: UMLRelationship) => {
    const diagram = diagrams.value.get(diagramId)
    if (!diagram) return

    recordAction('addRelationship', { diagramId, relationship })
    
    diagram.relationships.push(relationship)
    diagrams.value.set(diagramId, { ...diagram })
  }

  const recordAction = (action: string, data: any) => {
    undoStack.value.push({ action, data, timestamp: Date.now() })
    redoStack.value = [] // 清空重做栈
    
    // 限制撤销栈大小
    if (undoStack.value.length > 100) {
      undoStack.value.shift()
    }
  }

  const undo = () => {
    const lastAction = undoStack.value.pop()
    if (!lastAction) return

    redoStack.value.push(lastAction)
    
    // 执行反向操作
    switch (lastAction.action) {
      case 'addElement':
        deleteElement(lastAction.data.diagramId, lastAction.data.element.id)
        break
      case 'deleteElement':
        addElement(lastAction.data.diagramId, lastAction.data.element)
        break
      case 'updateElement':
        updateElement(
          lastAction.data.diagramId, 
          lastAction.data.elementId, 
          lastAction.data.oldElement
        )
        break
    }
  }

  const redo = () => {
    const lastAction = redoStack.value.pop()
    if (!lastAction) return

    undoStack.value.push(lastAction)
    
    // 重新执行操作
    switch (lastAction.action) {
      case 'addElement':
        addElement(lastAction.data.diagramId, lastAction.data.element)
        break
      case 'deleteElement':
        deleteElement(lastAction.data.diagramId, lastAction.data.element.id)
        break
    }
  }

  return {
    // 状态
    diagrams,
    activeDiagramId,
    selectedElements,
    clipboard,
    
    // 计算属性
    activeDiagram,
    diagramList,
    
    // 动作
    loadDiagram,
    createDiagram,
    saveDiagram,
    addElement,
    updateElement,
    deleteElement,
    addRelationship,
    undo,
    redo
  }
})
```

## 🔧 组合式API设计

### 建模引擎组合函数
```typescript
// composables/useModelingEngine.ts
import { ref } from 'vue'
import { UMLEngine, SysMLEngine } from '@/services/modeling'
import { UMLElement, UMLDiagram, ValidationResult } from '@/types/modeling'

export function useModelingEngine() {
  const umlEngine = new UMLEngine()
  const sysmlEngine = new SysMLEngine(umlEngine)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const createElement = async (
    elementType: string, 
    properties: Record<string, any>
  ): Promise<UMLElement> => {
    isLoading.value = true
    error.value = null

    try {
      const element = await umlEngine.createElement(elementType, properties)
      return element
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const createDiagram = async (
    diagramType: string, 
    name: string
  ): Promise<UMLDiagram> => {
    isLoading.value = true
    error.value = null

    try {
      let diagram: UMLDiagram

      if (diagramType.startsWith('SysML')) {
        diagram = await sysmlEngine.createDiagram(diagramType, name)
      } else {
        diagram = await umlEngine.createDiagram(diagramType, name)
      }

      return diagram
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const validateDiagram = async (diagram: UMLDiagram): Promise<ValidationResult> => {
    isLoading.value = true
    error.value = null

    try {
      const result = await umlEngine.validateDiagram(diagram)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const renderDiagram = async (
    diagram: UMLDiagram, 
    options: Record<string, any> = {}
  ) => {
    isLoading.value = true
    error.value = null

    try {
      const renderResult = await umlEngine.renderDiagram(diagram, options)
      return renderResult
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    createElement,
    createDiagram,
    validateDiagram,
    renderDiagram
  }
}
```

## 📋 功能特性

### 核心功能
- **🎨 可视化建模**：拖拽式建模界面
- **🔧 实时编辑**：即时保存和同步
- **🎭 主题切换**：多种视觉主题
- **📱 响应式设计**：适配不同屏幕尺寸
- **⚡ 高性能渲染**：基于Canvas的高效渲染

### 用户体验
- **🖱️ 直观操作**：简单易用的交互设计
- **⌨️ 快捷键支持**：提高操作效率
- **🔄 撤销重做**：完整的操作历史
- **📋 复制粘贴**：元素的复制和粘贴
- **🔍 缩放平移**：灵活的视图控制

---

**文档版本**：v1.0  
**最后更新**：2025年6月30日  
**相关文档**：
- UML核心建模系统设计.md
- SysML扩展建模设计.md
- 视图组件定制系统设计.md
- 建模系统总体架构.md 