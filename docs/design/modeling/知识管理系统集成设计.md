# 知识管理系统集成设计

## 📋 项目概述

**文档名称**：知识管理系统集成设计  
**设计时间**：2025年6月30日  
**版本**：v1.0  
**目标**：将UML/SysML建模系统与传统知识管理系统深度集成，实现建模与知识管理的无缝结合

## 🎯 集成架构设计

### 核心理念
- **📚 知识为主线**：传统知识管理为核心，建模为增强工具
- **🔧 双向同步**：建模数据与知识库的双向实时同步
- **🏷️ 智能关联**：基于AI的自动标签和关系发现
- **🔍 统一搜索**：跨建模和文档的统一搜索体验
- **📊 可视化增强**：通过建模提升知识可视化能力

### 集成架构
```
知识管理与建模集成架构：
┌─────────────────────────────────────────────────────────┐
│                    用户交互层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  统一界面   │ │  搜索门户   │ │  导航中心   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                    集成服务层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  映射服务   │ │  同步服务   │ │  关联服务   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                    知识处理层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  建模引擎   │ │  知识引擎   │ │  AI分析引擎 │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                    存储管理层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  模型存储   │ │  文档存储   │ │  关系图谱   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

## 🔗 知识映射与关联

### 文档-模型映射器
```python
class DocumentModelMapper:
    """文档-模型映射器"""
    
    def __init__(self):
        self.nlp_processor = NLPProcessor()
        self.similarity_calculator = SimilarityCalculator()
        self.mapping_cache = MappingCache()
        self.mapping_rules = MappingRules()
    
    def create_mapping(self, document: Document, diagram: UMLDiagram) -> Mapping:
        """创建文档-模型映射"""
        mapping = Mapping(
            document_id=document.id,
            diagram_id=diagram.id,
            timestamp=datetime.now()
        )
        
        # 提取文档关键信息
        doc_entities = self.nlp_processor.extract_entities(document.content)
        doc_concepts = self.nlp_processor.extract_concepts(document.content)
        doc_relationships = self.nlp_processor.extract_relationships(document.content)
        
        # 匹配模型元素
        for element in diagram.elements:
            similarities = self._calculate_element_similarities(
                element, doc_entities, doc_concepts
            )
            
            if similarities['max_similarity'] > 0.7:  # 高相似度阈值
                mapping.add_element_mapping(
                    element_id=element.id,
                    document_sections=similarities['matched_sections'],
                    similarity_score=similarities['max_similarity'],
                    mapping_type='direct'
                )
        
        # 匹配关系
        for relationship in diagram.relationships:
            rel_matches = self._match_relationship_to_text(
                relationship, doc_relationships
            )
            
            if rel_matches:
                mapping.add_relationship_mapping(
                    relationship_id=relationship.id,
                    text_relationships=rel_matches,
                    confidence=rel_matches[0]['confidence']
                )
        
        # 应用领域规则
        self.mapping_rules.apply_domain_rules(mapping, document, diagram)
        
        return mapping
    
    def _calculate_element_similarities(self, element: UMLElement, 
                                     entities: List[str], 
                                     concepts: List[str]) -> Dict:
        """计算元素相似度"""
        similarities = []
        matched_sections = []
        
        element_name = element.name.lower()
        element_type = element.get_element_type().lower()
        
        # 名称匹配
        for entity in entities:
            similarity = self.similarity_calculator.calculate_semantic_similarity(
                element_name, entity['text'].lower()
            )
            if similarity > 0.6:
                similarities.append(similarity)
                matched_sections.append({
                    'text': entity['text'],
                    'start': entity['start'],
                    'end': entity['end'],
                    'type': 'entity_match'
                })
        
        # 概念匹配
        for concept in concepts:
            similarity = self.similarity_calculator.calculate_concept_similarity(
                element, concept
            )
            if similarity > 0.5:
                similarities.append(similarity)
                matched_sections.append({
                    'text': concept['text'],
                    'context': concept['context'],
                    'type': 'concept_match'
                })
        
        return {
            'max_similarity': max(similarities) if similarities else 0.0,
            'avg_similarity': sum(similarities) / len(similarities) if similarities else 0.0,
            'matched_sections': matched_sections
        }
    
    def auto_generate_model_from_document(self, document: Document, 
                                        target_diagram_type: str = 'ClassDiagram') -> UMLDiagram:
        """从文档自动生成模型"""
        # 提取结构化信息
        entities = self.nlp_processor.extract_entities(document.content)
        relationships = self.nlp_processor.extract_relationships(document.content)
        processes = self.nlp_processor.extract_processes(document.content)
        
        # 创建图形
        diagram = UMLDiagram(
            name=f"Generated from {document.title}",
            diagram_type=target_diagram_type
        )
        
        if target_diagram_type == 'ClassDiagram':
            # 生成类图
            for entity in entities:
                if entity['category'] in ['ORGANIZATION', 'CONCEPT', 'OBJECT']:
                    class_element = ClassElement(
                        name=entity['text'],
                        stereotype='«entity»' if entity['category'] == 'CONCEPT' else None
                    )
                    diagram.add_element(class_element)
            
            # 生成关系
            for rel in relationships:
                source_elem = diagram.find_element_by_name(rel['source'])
                target_elem = diagram.find_element_by_name(rel['target'])
                
                if source_elem and target_elem:
                    relationship = AssociationRelationship(
                        source_id=source_elem.id,
                        target_id=target_elem.id,
                        name=rel['type']
                    )
                    diagram.add_relationship(relationship)
        
        elif target_diagram_type == 'ActivityDiagram':
            # 生成活动图
            for process in processes:
                activity = ActivityElement(
                    name=process['name'],
                    description=process['description']
                )
                diagram.add_element(activity)
        
        return diagram

class MappingRules:
    """映射规则管理器"""
    
    def __init__(self):
        self.domain_rules = {}
        self._load_biomedical_rules()
    
    def _load_biomedical_rules(self):
        """加载生物医学领域规则"""
        self.domain_rules['biomedical'] = {
            'entity_mappings': {
                'protein': 'Block',
                'gene': 'Block', 
                'cell': 'Block',
                'pathway': 'Activity',
                'interaction': 'Association',
                'regulation': 'Dependency'
            },
            'stereotype_mappings': {
                'protein': '«protein»',
                'gene': '«gene»',
                'cell_type': '«cellType»',
                'drug': '«drug»',
                'disease': '«disease»'
            },
            'relationship_patterns': [
                {
                    'pattern': r'(\w+)\s+regulates\s+(\w+)',
                    'relationship_type': 'Dependency',
                    'stereotype': '«regulate»'
                },
                {
                    'pattern': r'(\w+)\s+interacts\s+with\s+(\w+)',
                    'relationship_type': 'Association',
                    'stereotype': '«interact»'
                }
            ]
        }
    
    def apply_domain_rules(self, mapping: 'Mapping', document: Document, 
                          diagram: UMLDiagram):
        """应用领域规则"""
        domain = self._detect_domain(document)
        
        if domain in self.domain_rules:
            rules = self.domain_rules[domain]
            
            # 应用实体映射规则
            for element_mapping in mapping.element_mappings:
                element = diagram.get_element(element_mapping.element_id)
                if element:
                    self._apply_entity_rules(element, rules['entity_mappings'])
                    self._apply_stereotype_rules(element, rules['stereotype_mappings'])
            
            # 应用关系模式
            self._apply_relationship_patterns(mapping, document, rules['relationship_patterns'])
```

### 智能标签系统
```python
class IntelligentTagSystem:
    """智能标签系统"""
    
    def __init__(self):
        self.tag_extractor = TagExtractor()
        self.tag_classifier = TagClassifier()
        self.tag_hierarchy = TagHierarchy()
        self.suggestion_engine = TagSuggestionEngine()
    
    def generate_tags_for_diagram(self, diagram: UMLDiagram, 
                                 context_documents: List[Document] = None) -> List[Tag]:
        """为图形生成标签"""
        tags = []
        
        # 基于图形结构的标签
        structural_tags = self._extract_structural_tags(diagram)
        tags.extend(structural_tags)
        
        # 基于元素内容的标签
        content_tags = self._extract_content_tags(diagram)
        tags.extend(content_tags)
        
        # 基于关联文档的标签
        if context_documents:
            document_tags = self._extract_document_context_tags(diagram, context_documents)
            tags.extend(document_tags)
        
        # 基于领域知识的标签
        domain_tags = self._extract_domain_tags(diagram)
        tags.extend(domain_tags)
        
        # 去重和评分
        unique_tags = self._deduplicate_and_score(tags)
        
        # 层次化组织
        hierarchical_tags = self.tag_hierarchy.organize_tags(unique_tags)
        
        return hierarchical_tags
    
    def _extract_structural_tags(self, diagram: UMLDiagram) -> List[Tag]:
        """提取结构性标签"""
        tags = []
        
        # 图形类型标签
        diagram_type = diagram.diagram_type.get_diagram_type()
        tags.append(Tag(
            name=f"diagram:{diagram_type.lower()}",
            category='structure',
            confidence=1.0,
            source='diagram_type'
        ))
        
        # 元素数量标签
        element_count = len(diagram.elements)
        if element_count > 20:
            tags.append(Tag(name="complexity:high", category='structure', confidence=0.9))
        elif element_count > 10:
            tags.append(Tag(name="complexity:medium", category='structure', confidence=0.9))
        else:
            tags.append(Tag(name="complexity:low", category='structure', confidence=0.9))
        
        # 关系密度标签
        relationship_density = len(diagram.relationships) / max(len(diagram.elements), 1)
        if relationship_density > 2:
            tags.append(Tag(name="coupling:high", category='structure', confidence=0.8))
        elif relationship_density > 1:
            tags.append(Tag(name="coupling:medium", category='structure', confidence=0.8))
        
        # 元素类型统计
        element_types = {}
        for element in diagram.elements:
            elem_type = element.get_element_type()
            element_types[elem_type] = element_types.get(elem_type, 0) + 1
        
        for elem_type, count in element_types.items():
            if count > 3:  # 频繁出现的元素类型
                tags.append(Tag(
                    name=f"element:{elem_type.lower()}",
                    category='composition',
                    confidence=min(0.9, count * 0.1)
                ))
        
        return tags
    
    def _extract_content_tags(self, diagram: UMLDiagram) -> List[Tag]:
        """提取内容标签"""
        tags = []
        all_text = []
        
        # 收集所有文本内容
        for element in diagram.elements:
            all_text.append(element.name)
            if hasattr(element, 'documentation') and element.documentation:
                all_text.append(element.documentation)
        
        combined_text = ' '.join(all_text)
        
        # 使用NLP提取关键词
        keywords = self.tag_extractor.extract_keywords(combined_text)
        
        for keyword in keywords:
            if keyword['score'] > 0.6:  # 高相关性关键词
                tags.append(Tag(
                    name=f"keyword:{keyword['text'].lower()}",
                    category='content',
                    confidence=keyword['score'],
                    source='nlp_extraction'
                ))
        
        # 提取命名实体
        entities = self.tag_extractor.extract_named_entities(combined_text)
        
        for entity in entities:
            tags.append(Tag(
                name=f"entity:{entity['text'].lower()}",
                category='entity',
                confidence=entity['confidence'],
                source='ner'
            ))
        
        return tags
    
    def _extract_domain_tags(self, diagram: UMLDiagram) -> List[Tag]:
        """提取领域标签"""
        tags = []
        
        # 生物医学领域检测
        biomedical_indicators = [
            'protein', 'gene', 'cell', 'molecular', 'biological',
            'medical', 'clinical', 'therapeutic', 'drug', 'disease',
            'pathway', 'enzyme', 'receptor', 'antibody', 'virus'
        ]
        
        all_text = self._get_diagram_text(diagram).lower()
        
        biomedical_matches = 0
        for indicator in biomedical_indicators:
            if indicator in all_text:
                biomedical_matches += 1
                tags.append(Tag(
                    name=f"biomedical:{indicator}",
                    category='domain',
                    confidence=0.8,
                    source='domain_detection'
                ))
        
        if biomedical_matches > 2:
            tags.append(Tag(
                name="domain:biomedical",
                category='domain',
                confidence=min(0.95, biomedical_matches * 0.1),
                source='domain_detection'
            ))
        
        # 系统工程领域检测
        sysml_indicators = ['requirement', 'block', 'allocation', 'verification', 'constraint']
        sysml_matches = sum(1 for indicator in sysml_indicators if indicator in all_text)
        
        if sysml_matches > 1:
            tags.append(Tag(
                name="domain:systems_engineering",
                category='domain',
                confidence=min(0.9, sysml_matches * 0.2),
                source='domain_detection'
            ))
        
        return tags

class TagSuggestionEngine:
    """标签建议引擎"""
    
    def __init__(self):
        self.tag_cooccurrence = TagCooccurrenceMatrix()
        self.user_patterns = UserTaggingPatterns()
    
    def suggest_tags(self, current_tags: List[Tag], 
                    user_id: str = None, 
                    context: Dict = None) -> List[TagSuggestion]:
        """建议标签"""
        suggestions = []
        
        # 基于标签共现的建议
        cooccurrence_suggestions = self.tag_cooccurrence.get_related_tags(current_tags)
        suggestions.extend(cooccurrence_suggestions)
        
        # 基于用户历史的建议
        if user_id:
            user_suggestions = self.user_patterns.get_user_suggestions(user_id, current_tags)
            suggestions.extend(user_suggestions)
        
        # 基于上下文的建议
        if context:
            context_suggestions = self._get_context_suggestions(current_tags, context)
            suggestions.extend(context_suggestions)
        
        # 排序和去重
        unique_suggestions = self._deduplicate_suggestions(suggestions)
        sorted_suggestions = sorted(unique_suggestions, 
                                  key=lambda x: x.confidence, reverse=True)
        
        return sorted_suggestions[:10]  # 返回前10个建议
```

## 🔍 统一搜索系统

### 跨域搜索引擎
```python
class UnifiedSearchEngine:
    """统一搜索引擎"""
    
    def __init__(self):
        self.document_indexer = DocumentIndexer()
        self.model_indexer = ModelIndexer()
        self.semantic_search = SemanticSearchEngine()
        self.search_ranker = SearchRanker()
        self.query_expander = QueryExpander()
    
    def search(self, query: str, filters: Dict = None, 
               user_context: Dict = None) -> SearchResult:
        """统一搜索"""
        # 查询扩展
        expanded_query = self.query_expander.expand(query)
        
        # 多源搜索
        document_results = self._search_documents(expanded_query, filters)
        model_results = self._search_models(expanded_query, filters)
        semantic_results = self._semantic_search(expanded_query, filters)
        
        # 结果融合
        fused_results = self._fuse_results(
            document_results, model_results, semantic_results
        )
        
        # 个性化排序
        if user_context:
            ranked_results = self.search_ranker.personalized_rank(
                fused_results, user_context
            )
        else:
            ranked_results = self.search_ranker.rank(fused_results)
        
        # 构建搜索结果
        search_result = SearchResult(
            query=query,
            expanded_query=expanded_query,
            total_results=len(ranked_results),
            results=ranked_results,
            facets=self._generate_facets(ranked_results),
            suggestions=self._generate_suggestions(query, ranked_results)
        )
        
        return search_result
    
    def _search_documents(self, query: str, filters: Dict = None) -> List[DocumentResult]:
        """搜索文档"""
        # 全文搜索
        text_results = self.document_indexer.search(query)
        
        # 应用过滤器
        if filters:
            text_results = self._apply_document_filters(text_results, filters)
        
        # 转换为统一格式
        document_results = []
        for result in text_results:
            document_results.append(DocumentResult(
                type='document',
                id=result['id'],
                title=result['title'],
                content_snippet=result['snippet'],
                score=result['score'],
                metadata=result['metadata'],
                highlights=result['highlights']
            ))
        
        return document_results
    
    def _search_models(self, query: str, filters: Dict = None) -> List[ModelResult]:
        """搜索模型"""
        # 模型元素搜索
        element_results = self.model_indexer.search_elements(query)
        
        # 模型关系搜索
        relationship_results = self.model_indexer.search_relationships(query)
        
        # 图形搜索
        diagram_results = self.model_indexer.search_diagrams(query)
        
        # 合并结果
        all_model_results = element_results + relationship_results + diagram_results
        
        # 应用过滤器
        if filters:
            all_model_results = self._apply_model_filters(all_model_results, filters)
        
        # 转换为统一格式
        model_results = []
        for result in all_model_results:
            model_results.append(ModelResult(
                type='model',
                subtype=result['type'],  # element, relationship, diagram
                id=result['id'],
                name=result['name'],
                description=result.get('description', ''),
                score=result['score'],
                diagram_id=result.get('diagram_id'),
                metadata=result['metadata']
            ))
        
        return model_results
    
    def _semantic_search(self, query: str, filters: Dict = None) -> List[SemanticResult]:
        """语义搜索"""
        # 向量化查询
        query_vector = self.semantic_search.vectorize_query(query)
        
        # 相似度搜索
        similar_items = self.semantic_search.find_similar(query_vector)
        
        # 转换为统一格式
        semantic_results = []
        for item in similar_items:
            semantic_results.append(SemanticResult(
                type='semantic',
                id=item['id'],
                similarity_score=item['similarity'],
                content=item['content'],
                metadata=item['metadata']
            ))
        
        return semantic_results
    
    def _fuse_results(self, *result_lists) -> List[SearchResult]:
        """融合搜索结果"""
        all_results = []
        
        for results in result_lists:
            all_results.extend(results)
        
        # 去重（基于ID和类型）
        unique_results = {}
        for result in all_results:
            key = f"{result.type}:{result.id}"
            if key not in unique_results or result.score > unique_results[key].score:
                unique_results[key] = result
        
        return list(unique_results.values())

class ModelIndexer:
    """模型索引器"""
    
    def __init__(self):
        self.element_index = ElementIndex()
        self.relationship_index = RelationshipIndex()
        self.diagram_index = DiagramIndex()
    
    def index_diagram(self, diagram: UMLDiagram):
        """索引图形"""
        # 索引图形本身
        self.diagram_index.add_document({
            'id': diagram.id,
            'name': diagram.name,
            'type': diagram.diagram_type.get_diagram_type(),
            'description': diagram.description,
            'tags': diagram.tags,
            'element_count': len(diagram.elements),
            'relationship_count': len(diagram.relationships),
            'created_at': diagram.created_at,
            'updated_at': diagram.updated_at
        })
        
        # 索引元素
        for element in diagram.elements:
            self._index_element(element, diagram.id)
        
        # 索引关系
        for relationship in diagram.relationships:
            self._index_relationship(relationship, diagram.id)
    
    def _index_element(self, element: UMLElement, diagram_id: str):
        """索引元素"""
        content = self._extract_element_content(element)
        
        self.element_index.add_document({
            'id': element.id,
            'diagram_id': diagram_id,
            'name': element.name,
            'type': element.get_element_type(),
            'stereotype': element.stereotype,
            'content': content,
            'attributes': [attr.name for attr in getattr(element, 'attributes', [])],
            'operations': [op.name for op in getattr(element, 'operations', [])],
            'tags': element.tags if hasattr(element, 'tags') else []
        })
    
    def _extract_element_content(self, element: UMLElement) -> str:
        """提取元素内容"""
        content_parts = [element.name]
        
        if hasattr(element, 'documentation') and element.documentation:
            content_parts.append(element.documentation)
        
        if hasattr(element, 'attributes'):
            for attr in element.attributes:
                content_parts.append(f"{attr.name} {attr.type}")
        
        if hasattr(element, 'operations'):
            for op in element.operations:
                content_parts.append(f"{op.name} {op.return_type}")
        
        return ' '.join(content_parts)
```

## 📊 可视化知识图谱

### 知识图谱构建器
```python
class KnowledgeGraphBuilder:
    """知识图谱构建器"""
    
    def __init__(self):
        self.graph_db = Neo4jConnector()
        self.entity_extractor = EntityExtractor()
        self.relationship_extractor = RelationshipExtractor()
        self.graph_analyzer = GraphAnalyzer()
    
    def build_knowledge_graph(self, documents: List[Document], 
                            diagrams: List[UMLDiagram]) -> KnowledgeGraph:
        """构建知识图谱"""
        knowledge_graph = KnowledgeGraph()
        
        # 从文档构建图谱
        for document in documents:
            self._add_document_to_graph(document, knowledge_graph)
        
        # 从模型构建图谱
        for diagram in diagrams:
            self._add_diagram_to_graph(diagram, knowledge_graph)
        
        # 发现跨域关系
        cross_domain_relations = self._discover_cross_domain_relations(
            documents, diagrams
        )
        
        for relation in cross_domain_relations:
            knowledge_graph.add_relationship(relation)
        
        # 图谱分析和优化
        self.graph_analyzer.analyze_and_optimize(knowledge_graph)
        
        return knowledge_graph
    
    def _add_document_to_graph(self, document: Document, graph: KnowledgeGraph):
        """将文档添加到图谱"""
        # 添加文档节点
        doc_node = DocumentNode(
            id=document.id,
            title=document.title,
            type=document.type,
            content_summary=document.summary[:200] if document.summary else '',
            tags=document.tags
        )
        graph.add_node(doc_node)
        
        # 提取并添加实体
        entities = self.entity_extractor.extract(document.content)
        for entity in entities:
            entity_node = EntityNode(
                id=f"entity_{entity['id']}",
                name=entity['text'],
                type=entity['label'],
                confidence=entity['confidence'],
                source_document=document.id
            )
            graph.add_node(entity_node)
            
            # 建立文档-实体关系
            contains_rel = ContainsRelationship(
                source_id=doc_node.id,
                target_id=entity_node.id,
                positions=entity['positions']
            )
            graph.add_relationship(contains_rel)
        
        # 提取并添加概念关系
        relationships = self.relationship_extractor.extract(document.content)
        for rel in relationships:
            source_entity = graph.find_entity_by_name(rel['source'])
            target_entity = graph.find_entity_by_name(rel['target'])
            
            if source_entity and target_entity:
                concept_rel = ConceptualRelationship(
                    source_id=source_entity.id,
                    target_id=target_entity.id,
                    relationship_type=rel['type'],
                    confidence=rel['confidence'],
                    evidence=rel['evidence']
                )
                graph.add_relationship(concept_rel)
    
    def _add_diagram_to_graph(self, diagram: UMLDiagram, graph: KnowledgeGraph):
        """将图形添加到图谱"""
        # 添加图形节点
        diagram_node = DiagramNode(
            id=diagram.id,
            name=diagram.name,
            type=diagram.diagram_type.get_diagram_type(),
            element_count=len(diagram.elements),
            relationship_count=len(diagram.relationships)
        )
        graph.add_node(diagram_node)
        
        # 添加模型元素节点
        for element in diagram.elements:
            model_node = ModelElementNode(
                id=element.id,
                name=element.name,
                type=element.get_element_type(),
                stereotype=element.stereotype,
                diagram_id=diagram.id
            )
            graph.add_node(model_node)
            
            # 建立图形-元素关系
            contains_rel = ContainsRelationship(
                source_id=diagram_node.id,
                target_id=model_node.id
            )
            graph.add_relationship(contains_rel)
        
        # 添加模型关系
        for relationship in diagram.relationships:
            model_rel = ModelRelationship(
                source_id=relationship.source_id,
                target_id=relationship.target_id,
                relationship_type=relationship.get_relationship_type(),
                stereotype=getattr(relationship, 'stereotype', None),
                diagram_id=diagram.id
            )
            graph.add_relationship(model_rel)
    
    def _discover_cross_domain_relations(self, documents: List[Document], 
                                       diagrams: List[UMLDiagram]) -> List[Relationship]:
        """发现跨域关系"""
        cross_relations = []
        
        for document in documents:
            for diagram in diagrams:
                # 基于名称匹配发现关系
                name_matches = self._find_name_matches(document, diagram)
                cross_relations.extend(name_matches)
                
                # 基于语义相似度发现关系
                semantic_matches = self._find_semantic_matches(document, diagram)
                cross_relations.extend(semantic_matches)
                
                # 基于共现模式发现关系
                cooccurrence_matches = self._find_cooccurrence_matches(document, diagram)
                cross_relations.extend(cooccurrence_matches)
        
        return cross_relations
    
    def visualize_knowledge_graph(self, graph: KnowledgeGraph, 
                                 focus_nodes: List[str] = None) -> Dict:
        """可视化知识图谱"""
        if focus_nodes:
            # 提取子图
            subgraph = graph.extract_subgraph(focus_nodes, max_depth=2)
        else:
            subgraph = graph
        
        # 计算布局
        layout = self._calculate_graph_layout(subgraph)
        
        # 生成可视化数据
        vis_data = {
            'nodes': [],
            'edges': [],
            'layout': layout,
            'statistics': self._calculate_graph_statistics(subgraph)
        }
        
        # 节点数据
        for node in subgraph.nodes:
            vis_node = {
                'id': node.id,
                'label': node.name if hasattr(node, 'name') else node.id,
                'type': node.type,
                'size': self._calculate_node_size(node, subgraph),
                'color': self._get_node_color(node),
                'position': layout['positions'][node.id],
                'metadata': node.get_metadata()
            }
            vis_data['nodes'].append(vis_node)
        
        # 边数据
        for edge in subgraph.relationships:
            vis_edge = {
                'id': edge.id,
                'source': edge.source_id,
                'target': edge.target_id,
                'label': edge.relationship_type,
                'type': edge.relationship_type,
                'weight': getattr(edge, 'weight', 1.0),
                'color': self._get_edge_color(edge)
            }
            vis_data['edges'].append(vis_edge)
        
        return vis_data

class KnowledgeGraphAnalyzer:
    """知识图谱分析器"""
    
    def analyze_centrality(self, graph: KnowledgeGraph) -> Dict[str, float]:
        """分析中心性"""
        # 度中心性
        degree_centrality = {}
        for node in graph.nodes:
            degree = graph.get_node_degree(node.id)
            degree_centrality[node.id] = degree / (len(graph.nodes) - 1)
        
        # 介数中心性（简化计算）
        betweenness_centrality = self._calculate_betweenness_centrality(graph)
        
        # 特征向量中心性
        eigenvector_centrality = self._calculate_eigenvector_centrality(graph)
        
        return {
            'degree': degree_centrality,
            'betweenness': betweenness_centrality,
            'eigenvector': eigenvector_centrality
        }
    
    def find_communities(self, graph: KnowledgeGraph) -> List[List[str]]:
        """发现社区"""
        # 使用Louvain算法进行社区发现
        communities = []
        
        # 简化实现：基于连通性的社区发现
        visited = set()
        
        for node in graph.nodes:
            if node.id not in visited:
                community = self._dfs_community(graph, node.id, visited)
                if len(community) > 1:
                    communities.append(community)
        
        return communities
    
    def suggest_new_connections(self, graph: KnowledgeGraph) -> List[SuggestedConnection]:
        """建议新连接"""
        suggestions = []
        
        # 基于共同邻居的建议
        for node1 in graph.nodes:
            for node2 in graph.nodes:
                if node1.id != node2.id and not graph.has_relationship(node1.id, node2.id):
                    common_neighbors = graph.get_common_neighbors(node1.id, node2.id)
                    
                    if len(common_neighbors) >= 2:  # 至少有2个共同邻居
                        suggestion = SuggestedConnection(
                            source_id=node1.id,
                            target_id=node2.id,
                            confidence=len(common_neighbors) / 10.0,
                            reason='common_neighbors',
                            evidence=common_neighbors
                        )
                        suggestions.append(suggestion)
        
        return sorted(suggestions, key=lambda x: x.confidence, reverse=True)
```

## 📋 功能特性总结

### 核心集成能力
- **🔗 深度映射**：文档与模型的智能映射
- **🏷️ 智能标签**：基于AI的自动标签生成
- **🔍 统一搜索**：跨文档和模型的统一搜索
- **📊 知识图谱**：可视化的知识关系网络
- **🔄 双向同步**：实时的数据同步机制

### 增强功能
- **🧠 语义理解**：基于NLP的语义分析
- **📈 关系发现**：自动发现隐式关系
- **🎯 个性化推荐**：基于用户行为的智能推荐
- **📊 分析洞察**：深度的知识分析和洞察
- **🔧 自定义规则**：可配置的映射和分析规则

### 应用价值
- **📚 知识整合**：传统文档与现代建模的无缝整合
- **🔍 发现价值**：从数据中发现新的知识价值
- **⚡ 效率提升**：显著提升知识管理和建模效率
- **🎯 精准定位**：快速定位相关知识和信息
- **🌐 全景视图**：提供知识的全景视图和深度洞察

---

**文档版本**：v1.0  
**最后更新**：2025年6月30日  
**相关文档**：
- UML核心建模系统设计.md
- SysML扩展建模设计.md
- 视图组件定制系统设计.md
- 前端Vue集成实现设计.md
- 建模系统总体架构.md 