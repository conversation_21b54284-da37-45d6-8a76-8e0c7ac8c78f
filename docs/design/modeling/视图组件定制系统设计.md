# 视图组件定制系统设计

## 📋 项目概述

**文档名称**：视图组件定制系统设计  
**设计时间**：2025年6月30日  
**版本**：v1.0  
**目标**：为UML/SysML建模系统提供高度可定制的视图组件架构，支持主题、模板和个性化定制

## 🎯 设计目标

### 核心能力
- **🎨 高度可定制**：支持视觉样式、布局、交互的完全定制
- **📚 组件复用**：基于组件注册表的模块化架构
- **🎭 主题系统**：丰富的主题和样式管理
- **📋 模板机制**：保存和复用自定义配置
- **🔧 扩展性**：支持自定义组件和渲染器

### 设计原则
- **分离关注点**：视觉、布局、交互逻辑分离
- **组合优于继承**：基于组合的灵活架构
- **配置驱动**：通过配置实现定制，而非硬编码
- **性能优化**：延迟加载和渲染优化

## 🏗️ 组件架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                    定制化层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  主题管理   │ │  模板引擎   │ │  样式编辑器 │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                    组件管理层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  组件注册表 │ │  组件工厂   │ │  组件缓存   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                    渲染引擎层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  SVG渲染器  │ │ Canvas渲染器│ │  布局引擎   │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### 核心组件
```python
class ViewComponentRegistry:
    """视图组件注册表"""
    
    def __init__(self):
        self.components = {}
        self.renderers = {}
        self.themes = {}
        self.templates = {}
        self._initialize_default_components()
    
    def _initialize_default_components(self):
        """初始化默认组件"""
        # UML基础组件
        self.register_component('class', ClassViewComponent())
        self.register_component('interface', InterfaceViewComponent())
        self.register_component('actor', ActorViewComponent())
        self.register_component('usecase', UseCaseViewComponent())
        
        # SysML扩展组件
        self.register_component('block', BlockViewComponent())
        self.register_component('requirement', RequirementViewComponent())
        self.register_component('constraint', ConstraintViewComponent())
        self.register_component('flowport', FlowPortViewComponent())
        
        # 关系组件
        self.register_component('association', AssociationViewComponent())
        self.register_component('generalization', GeneralizationViewComponent())
        self.register_component('satisfy', SatisfyViewComponent())
        self.register_component('allocate', AllocateViewComponent())
    
    def register_component(self, component_type: str, component: 'ViewComponent'):
        """注册视图组件"""
        self.components[component_type] = component
    
    def register_renderer(self, renderer_name: str, renderer: 'ComponentRenderer'):
        """注册渲染器"""
        self.renderers[renderer_name] = renderer
    
    def register_theme(self, theme_name: str, theme: 'ComponentTheme'):
        """注册主题"""
        self.themes[theme_name] = theme
    
    def create_component(self, component_type: str, element: UMLElement, 
                        theme: str = 'default') -> 'ViewComponent':
        """创建视图组件实例"""
        if component_type not in self.components:
            raise ValueError(f"未注册的组件类型: {component_type}")
        
        component_class = self.components[component_type]
        component_instance = component_class.create_instance(element)
        
        # 应用主题
        if theme in self.themes:
            component_instance.apply_theme(self.themes[theme])
        
        return component_instance
```

## 🎨 视图组件基类

### 组件基础架构
```python
class ViewComponent(ABC):
    """视图组件基类"""
    
    def __init__(self, element: UMLElement):
        self.element = element
        self.visual_config = {}
        self.layout_config = {}
        self.interaction_config = {}
        self.custom_properties = {}
    
    @abstractmethod
    def render(self, renderer: 'ComponentRenderer') -> Dict:
        """渲染组件"""
        pass
    
    @abstractmethod
    def get_bounds(self) -> Dict:
        """获取组件边界"""
        pass
    
    def apply_theme(self, theme: 'ComponentTheme'):
        """应用主题"""
        theme.apply_to_component(self)
    
    def set_visual_property(self, property_name: str, value: Any):
        """设置视觉属性"""
        self.visual_config[property_name] = value
    
    def set_layout_property(self, property_name: str, value: Any):
        """设置布局属性"""
        self.layout_config[property_name] = value
    
    def add_interaction(self, event_type: str, handler: Callable):
        """添加交互处理"""
        if event_type not in self.interaction_config:
            self.interaction_config[event_type] = []
        self.interaction_config[event_type].append(handler)
    
    def clone(self) -> 'ViewComponent':
        """克隆组件"""
        cloned = self.__class__(self.element)
        cloned.visual_config = self.visual_config.copy()
        cloned.layout_config = self.layout_config.copy()
        cloned.interaction_config = self.interaction_config.copy()
        cloned.custom_properties = self.custom_properties.copy()
        return cloned
```

### SysML块视图组件
```python
class BlockViewComponent(ViewComponent):
    """SysML块视图组件"""
    
    def __init__(self, element: BlockElement):
        super().__init__(element)
        self.compartments = {
            'values': True,
            'parts': True,
            'references': True,
            'flows': True,
            'constraints': True
        }
        self.visual_config.update({
            'show_stereotype': True,
            'show_compartments': True,
            'compartment_separator_style': 'solid',
            'stereotype_position': 'top',
            'fill_color': '#E6F3FF',
            'border_color': '#0066CC',
            'text_color': '#000000',
            'font_family': 'Arial',
            'font_size': 10,
            'border_width': 2,
            'border_radius': 0
        })
    
    def render(self, renderer: 'ComponentRenderer') -> Dict:
        """渲染块组件"""
        block = self.element
        bounds = self.get_bounds()
        
        render_data = {
            'type': 'block',
            'bounds': bounds,
            'visual': self.visual_config,
            'compartments': []
        }
        
        # 渲染构造型
        if self.visual_config.get('show_stereotype', True):
            render_data['stereotype'] = {
                'text': '«block»',
                'position': self.visual_config.get('stereotype_position', 'top')
            }
        
        # 渲染标题
        render_data['title'] = {
            'text': block.name,
            'font_weight': 'bold'
        }
        
        # 渲染值属性区间
        if self.compartments.get('values', True) and block.value_properties:
            values_compartment = {
                'type': 'values',
                'title': 'values',
                'items': []
            }
            
            for value_prop in block.value_properties:
                item_text = f"{value_prop['name']}: {value_prop['type']}"
                if value_prop.get('unit'):
                    item_text += f" [{value_prop['unit']}]"
                
                values_compartment['items'].append({
                    'text': item_text,
                    'visibility': 'public'
                })
            
            render_data['compartments'].append(values_compartment)
        
        # 渲染部件区间
        if self.compartments.get('parts', True) and block.part_properties:
            parts_compartment = {
                'type': 'parts',
                'title': 'parts',
                'items': []
            }
            
            for part_prop in block.part_properties:
                item_text = f"{part_prop['name']}: {part_prop['type']}"
                if part_prop.get('multiplicity', '1') != '1':
                    item_text += f" [{part_prop['multiplicity']}]"
                
                parts_compartment['items'].append({
                    'text': item_text,
                    'visibility': 'private',
                    'aggregation': part_prop.get('aggregation', 'composite')
                })
            
            render_data['compartments'].append(parts_compartment)
        
        # 渲染流属性区间
        if self.compartments.get('flows', True) and block.flow_properties:
            flows_compartment = {
                'type': 'flows',
                'title': 'flowProperties',
                'items': []
            }
            
            for flow_prop in block.flow_properties:
                direction_symbol = self._get_flow_direction_symbol(flow_prop['direction'])
                item_text = f"{direction_symbol} {flow_prop['name']}: {flow_prop['type']}"
                
                flows_compartment['items'].append({
                    'text': item_text,
                    'visibility': 'public',
                    'direction': flow_prop['direction']
                })
            
            render_data['compartments'].append(flows_compartment)
        
        return render_data
    
    def get_bounds(self) -> Dict:
        """计算块的边界"""
        base_width = 150
        base_height = 60
        
        # 根据内容调整尺寸
        compartment_count = sum(1 for enabled in self.compartments.values() if enabled)
        height = base_height + compartment_count * 80
        
        # 根据最长文本调整宽度
        max_text_length = len(self.element.name)
        for value_prop in self.element.value_properties:
            text_length = len(f"{value_prop['name']}: {value_prop['type']}")
            max_text_length = max(max_text_length, text_length)
        
        width = max(base_width, max_text_length * 8)
        
        return {
            'x': self.element.visual_properties.get('x', 0),
            'y': self.element.visual_properties.get('y', 0),
            'width': width,
            'height': height
        }
    
    def _get_flow_direction_symbol(self, direction: str) -> str:
        """获取流方向符号"""
        symbols = {
            'in': '→',
            'out': '←',
            'inout': '↔'
        }
        return symbols.get(direction, '↔')
    
    def toggle_compartment(self, compartment_type: str):
        """切换区间显示"""
        if compartment_type in self.compartments:
            self.compartments[compartment_type] = not self.compartments[compartment_type]
    
    def set_compartment_style(self, compartment_type: str, style: Dict):
        """设置区间样式"""
        if compartment_type not in self.custom_properties:
            self.custom_properties[compartment_type] = {}
        self.custom_properties[compartment_type].update(style)
```

### 需求视图组件
```python
class RequirementViewComponent(ViewComponent):
    """SysML需求视图组件"""
    
    def __init__(self, element: RequirementElement):
        super().__init__(element)
        self.visual_config.update({
            'show_stereotype': True,
            'show_id': True,
            'show_text': True,
            'show_properties': True,
            'fill_color': '#FFF2CC',
            'border_color': '#D6B656',
            'text_color': '#000000',
            'border_width': 2,
            'border_radius': 5,
            'priority_colors': {
                'high': '#FF6B6B',
                'medium': '#FFE66D',
                'low': '#4ECDC4'
            },
            'status_icons': {
                'proposed': '📝',
                'approved': '✅',
                'implemented': '🔧',
                'verified': '✔️',
                'obsolete': '❌'
            }
        })
    
    def render(self, renderer: 'ComponentRenderer') -> Dict:
        """渲染需求组件"""
        requirement = self.element
        bounds = self.get_bounds()
        
        # 根据优先级设置颜色
        priority_color = self.visual_config['priority_colors'].get(
            requirement.priority, '#FFE66D'
        )
        
        # 获取状态图标
        status_icon = self.visual_config['status_icons'].get(
            requirement.status, '📝'
        )
        
        render_data = {
            'type': 'requirement',
            'bounds': bounds,
            'visual': {
                **self.visual_config,
                'fill_color': priority_color
            },
            'sections': []
        }
        
        # 渲染构造型和需求ID
        header_text = '«requirement»'
        if self.visual_config.get('show_id', True) and requirement.requirement_id:
            header_text += f"\n{requirement.requirement_id}"
        
        render_data['header'] = {
            'text': header_text,
            'font_weight': 'bold',
            'status_icon': status_icon
        }
        
        # 渲染需求名称
        render_data['title'] = {
            'text': requirement.name,
            'font_weight': 'bold'
        }
        
        # 渲染需求文本
        if self.visual_config.get('show_text', True) and requirement.text:
            render_data['sections'].append({
                'type': 'text',
                'content': requirement.text,
                'style': 'italic'
            })
        
        # 渲染需求属性
        if self.visual_config.get('show_properties', True):
            properties_section = {
                'type': 'properties',
                'items': [
                    f"优先级: {requirement.priority}",
                    f"状态: {requirement.status}"
                ]
            }
            
            if requirement.verification_method:
                properties_section['items'].append(
                    f"验证方法: {requirement.verification_method}"
                )
            
            if requirement.risk:
                properties_section['items'].append(f"风险: {requirement.risk}")
            
            render_data['sections'].append(properties_section)
        
        return render_data
    
    def get_bounds(self) -> Dict:
        """计算需求的边界"""
        base_width = 200
        base_height = 100
        
        # 根据文本内容调整高度
        text_lines = 1  # 标题
        if self.element.text:
            text_lines += len(self.element.text) // 30 + 1  # 估算文本行数
        
        if self.visual_config.get('show_properties', True):
            text_lines += 4  # 属性行数
        
        height = base_height + text_lines * 15
        
        return {
            'x': self.element.visual_properties.get('x', 0),
            'y': self.element.visual_properties.get('y', 0),
            'width': base_width,
            'height': height
        }
    
    def set_priority_color(self, priority: str, color: str):
        """设置优先级颜色"""
        self.visual_config['priority_colors'][priority] = color
    
    def toggle_section(self, section_type: str):
        """切换显示区段"""
        show_key = f'show_{section_type}'
        if show_key in self.visual_config:
            self.visual_config[show_key] = not self.visual_config[show_key]
```

## 🎭 主题系统设计

### 主题基础架构
```python
class ComponentTheme:
    """组件主题"""
    
    def __init__(self, name: str, theme_config: Dict):
        self.name = name
        self.config = theme_config
        self.metadata = {
            'description': '',
            'author': '',
            'version': '1.0',
            'created_at': datetime.now().isoformat()
        }
    
    def apply_to_component(self, component: ViewComponent):
        """将主题应用到组件"""
        component_type = component.element.get_element_type().lower()
        
        if component_type in self.config:
            type_config = self.config[component_type]
            component.visual_config.update(type_config.get('visual', {}))
            component.layout_config.update(type_config.get('layout', {}))
    
    def get_component_config(self, component_type: str) -> Dict:
        """获取组件配置"""
        return self.config.get(component_type.lower(), {})
    
    def set_component_config(self, component_type: str, config: Dict):
        """设置组件配置"""
        self.config[component_type.lower()] = config

class SysMLThemeManager:
    """SysML主题管理器"""
    
    def __init__(self):
        self.themes = {}
        self._initialize_default_themes()
    
    def _initialize_default_themes(self):
        """初始化默认主题"""
        
        # 经典SysML主题
        classic_theme = ComponentTheme('classic', {
            'block': {
                'visual': {
                    'fill_color': '#E6F3FF',
                    'border_color': '#0066CC',
                    'border_width': 2,
                    'font_family': 'Arial',
                    'font_size': 10,
                    'border_radius': 0
                },
                'layout': {
                    'padding': 5,
                    'margin': 10
                }
            },
            'requirement': {
                'visual': {
                    'fill_color': '#FFF2CC',
                    'border_color': '#D6B656',
                    'border_width': 2,
                    'border_radius': 5
                }
            },
            'constraint': {
                'visual': {
                    'fill_color': '#F0F8FF',
                    'border_color': '#4682B4',
                    'border_style': 'dashed'
                }
            }
        })
        
        # 现代扁平主题
        modern_theme = ComponentTheme('modern', {
            'block': {
                'visual': {
                    'fill_color': '#FFFFFF',
                    'border_color': '#333333',
                    'border_width': 1,
                    'border_radius': 8,
                    'shadow': True,
                    'font_family': 'Segoe UI',
                    'font_size': 11
                },
                'layout': {
                    'padding': 8,
                    'margin': 12
                }
            },
            'requirement': {
                'visual': {
                    'fill_color': '#F8F9FA',
                    'border_color': '#6C757D',
                    'border_width': 1,
                    'border_radius': 8,
                    'shadow': True
                }
            }
        })
        
        # 高对比度主题
        high_contrast_theme = ComponentTheme('high_contrast', {
            'block': {
                'visual': {
                    'fill_color': '#000000',
                    'border_color': '#FFFFFF',
                    'text_color': '#FFFFFF',
                    'border_width': 3,
                    'font_family': 'Arial',
                    'font_size': 12,
                    'font_weight': 'bold'
                }
            },
            'requirement': {
                'visual': {
                    'fill_color': '#FFFFFF',
                    'border_color': '#000000',
                    'text_color': '#000000',
                    'border_width': 3
                }
            }
        })
        
        # 彩色编码主题
        color_coded_theme = ComponentTheme('color_coded', {
            'block': {
                'visual': {
                    'fill_color': '#E8F5E8',
                    'border_color': '#4CAF50',
                    'border_width': 2
                }
            },
            'requirement': {
                'visual': {
                    'fill_color': '#FFF3E0',
                    'border_color': '#FF9800',
                    'border_width': 2
                }
            },
            'constraint': {
                'visual': {
                    'fill_color': '#F3E5F5',
                    'border_color': '#9C27B0',
                    'border_width': 2
                }
            },
            'interface': {
                'visual': {
                    'fill_color': '#E3F2FD',
                    'border_color': '#2196F3',
                    'border_width': 2
                }
            }
        })
        
        self.themes.update({
            'classic': classic_theme,
            'modern': modern_theme,
            'high_contrast': high_contrast_theme,
            'color_coded': color_coded_theme
        })
    
    def register_theme(self, theme: ComponentTheme):
        """注册主题"""
        self.themes[theme.name] = theme
    
    def get_theme(self, theme_name: str) -> ComponentTheme:
        """获取主题"""
        return self.themes.get(theme_name, self.themes['classic'])
    
    def get_available_themes(self) -> List[str]:
        """获取可用主题列表"""
        return list(self.themes.keys())
    
    def create_custom_theme(self, name: str, base_theme: str, 
                           customizations: Dict) -> ComponentTheme:
        """创建自定义主题"""
        base = self.themes.get(base_theme, self.themes['classic'])
        
        # 深度合并配置
        custom_config = self._deep_merge(base.config.copy(), customizations)
        
        custom_theme = ComponentTheme(name, custom_config)
        self.register_theme(custom_theme)
        
        return custom_theme
    
    def _deep_merge(self, base: Dict, override: Dict) -> Dict:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def export_theme(self, theme_name: str) -> str:
        """导出主题为JSON"""
        import json
        if theme_name in self.themes:
            theme = self.themes[theme_name]
            theme_data = {
                'name': theme.name,
                'config': theme.config,
                'metadata': theme.metadata
            }
            return json.dumps(theme_data, indent=2, ensure_ascii=False)
        return None
    
    def import_theme(self, theme_json: str) -> ComponentTheme:
        """从JSON导入主题"""
        import json
        theme_data = json.loads(theme_json)
        
        theme = ComponentTheme(theme_data['name'], theme_data['config'])
        theme.metadata = theme_data.get('metadata', {})
        
        self.register_theme(theme)
        return theme
```

## 🔧 自定义构建器

### 组件定制构建器
```python
class CustomViewBuilder:
    """自定义视图构建器"""
    
    def __init__(self, registry: ViewComponentRegistry):
        self.registry = registry
        self.templates = {}
    
    def create_custom_block_view(self, element: BlockElement, 
                                customizations: Dict) -> BlockViewComponent:
        """创建自定义块视图"""
        component = BlockViewComponent(element)
        
        # 应用自定义配置
        if 'compartments' in customizations:
            component.compartments.update(customizations['compartments'])
        
        if 'visual' in customizations:
            component.visual_config.update(customizations['visual'])
        
        if 'layout' in customizations:
            component.layout_config.update(customizations['layout'])
        
        # 应用自定义交互
        if 'interactions' in customizations:
            for event_type, handlers in customizations['interactions'].items():
                for handler in handlers:
                    component.add_interaction(event_type, handler)
        
        return component
    
    def create_custom_requirement_view(self, element: RequirementElement,
                                     customizations: Dict) -> RequirementViewComponent:
        """创建自定义需求视图"""
        component = RequirementViewComponent(element)
        
        # 应用自定义配置
        if 'visual' in customizations:
            component.visual_config.update(customizations['visual'])
        
        if 'sections' in customizations:
            component.custom_properties['sections'] = customizations['sections']
        
        # 自定义优先级颜色
        if 'priority_colors' in customizations:
            component.visual_config['priority_colors'].update(
                customizations['priority_colors']
            )
        
        return component
    
    def save_as_template(self, template_name: str, component: ViewComponent):
        """保存为模板"""
        template_config = {
            'component_type': component.element.get_element_type(),
            'visual': component.visual_config.copy(),
            'layout': component.layout_config.copy(),
            'custom_properties': component.custom_properties.copy(),
            'created_at': datetime.now().isoformat()
        }
        
        # 特殊处理SysML块组件
        if isinstance(component, BlockViewComponent):
            template_config['compartments'] = component.compartments.copy()
        
        self.templates[template_name] = template_config
    
    def apply_template(self, component: ViewComponent, template_name: str):
        """应用模板"""
        if template_name in self.templates:
            template = self.templates[template_name]
            
            # 验证组件类型匹配
            if template['component_type'] == component.element.get_element_type():
                component.visual_config.update(template.get('visual', {}))
                component.layout_config.update(template.get('layout', {}))
                component.custom_properties.update(template.get('custom_properties', {}))
                
                # 特殊处理SysML块组件
                if isinstance(component, BlockViewComponent) and 'compartments' in template:
                    component.compartments.update(template['compartments'])
    
    def get_template_list(self) -> List[Dict]:
        """获取模板列表"""
        return [
            {
                'name': name,
                'component_type': config['component_type'],
                'created_at': config.get('created_at', ''),
                'description': config.get('description', '')
            }
            for name, config in self.templates.items()
        ]
    
    def create_component_wizard(self, element: UMLElement) -> Dict:
        """创建组件向导配置"""
        component_type = element.get_element_type().lower()
        
        wizard_config = {
            'component_type': component_type,
            'steps': []
        }
        
        if component_type == 'block':
            wizard_config['steps'] = [
                {
                    'title': '基本设置',
                    'fields': [
                        {'name': 'show_stereotype', 'type': 'boolean', 'default': True},
                        {'name': 'show_compartments', 'type': 'boolean', 'default': True},
                        {'name': 'stereotype_position', 'type': 'select', 
                         'options': ['top', 'bottom'], 'default': 'top'}
                    ]
                },
                {
                    'title': '区间设置',
                    'fields': [
                        {'name': 'values', 'type': 'boolean', 'default': True},
                        {'name': 'parts', 'type': 'boolean', 'default': True},
                        {'name': 'flows', 'type': 'boolean', 'default': True},
                        {'name': 'constraints', 'type': 'boolean', 'default': True}
                    ]
                },
                {
                    'title': '视觉样式',
                    'fields': [
                        {'name': 'fill_color', 'type': 'color', 'default': '#E6F3FF'},
                        {'name': 'border_color', 'type': 'color', 'default': '#0066CC'},
                        {'name': 'border_width', 'type': 'number', 'default': 2},
                        {'name': 'font_family', 'type': 'select', 
                         'options': ['Arial', 'Segoe UI', 'Times New Roman'], 'default': 'Arial'}
                    ]
                }
            ]
        elif component_type == 'requirement':
            wizard_config['steps'] = [
                {
                    'title': '显示设置',
                    'fields': [
                        {'name': 'show_stereotype', 'type': 'boolean', 'default': True},
                        {'name': 'show_id', 'type': 'boolean', 'default': True},
                        {'name': 'show_text', 'type': 'boolean', 'default': True},
                        {'name': 'show_properties', 'type': 'boolean', 'default': True}
                    ]
                },
                {
                    'title': '优先级颜色',
                    'fields': [
                        {'name': 'high_priority_color', 'type': 'color', 'default': '#FF6B6B'},
                        {'name': 'medium_priority_color', 'type': 'color', 'default': '#FFE66D'},
                        {'name': 'low_priority_color', 'type': 'color', 'default': '#4ECDC4'}
                    ]
                }
            ]
        
        return wizard_config
```

## 📋 功能特性总结

### 核心能力
- **🎨 完全可定制**：视觉、布局、交互的全面定制
- **📚 组件化架构**：模块化、可复用的组件设计
- **🎭 丰富主题**：内置多种专业主题，支持自定义
- **📋 模板系统**：保存和复用配置模板
- **🔧 扩展性强**：支持自定义组件和渲染器

### 使用场景
- **个人定制**：用户根据喜好定制界面
- **企业标准**：统一的企业视觉标准
- **项目模板**：特定项目的标准化配置
- **无障碍设计**：高对比度、大字体等无障碍支持

---

**文档版本**：v1.0  
**最后更新**：2025年6月30日  
**相关文档**：
- UML核心建模系统设计.md
- SysML扩展建模设计.md
- 前端Vue集成实现设计.md 