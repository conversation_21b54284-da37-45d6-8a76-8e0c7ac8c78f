# 生物医学MBSE可视化组件库 - 快速修复指南

## 🚨 紧急修复

### 当前错误
```
SyntaxError: The requested module '/node_modules/.vite/deps/@element-plus_icons-vue.js?v=252085ff' 
does not provide an export named 'Microscope' (at App.vue:63:54)
```

### 修复步骤

#### 1. 修复图标导入错误

**文件位置**：`src/App.vue`  
**问题行**：第63行

**当前代码**：
```javascript
import { House, Tools, Document, DataAnalysis, View, Microscope } from '@element-plus/icons-vue'
```

**修复代码**：
```javascript
import { House, Tools, Document, DataAnalysis, View, Operation } from '@element-plus/icons-vue'
```

**模板修改**：
```vue
<!-- 将第25行的 Microscope 替换为 Operation -->
<el-button @click="$router.push('/biomedical')">
  <el-icon><Operation /></el-icon>
  生物医学
</el-button>
```

#### 2. 验证修复

1. 保存文件后，页面应该能正常加载
2. 访问 http://localhost:3000 检查首页
3. 点击导航栏各按钮确认路由正常

## 🔧 组件兼容性检查

### 可能的问题点

1. **Three.js 组件加载**
   - 如果3D组件报错，可以先注释掉 MolecularViewer 组件
   - 逐步测试各个可视化组件

2. **X6 流程编辑器**
   - 检查 FlowEditor 组件是否正常渲染
   - 如有问题，可以临时显示占位符

3. **G6 网络图**
   - 确认 NetworkGraph 组件加载正常
   - 检查示例数据是否正确

### 临时降级方案

如果某个组件无法正常工作，可以创建占位符组件：

```vue
<!-- 临时占位符示例 -->
<template>
  <div class="component-placeholder">
    <el-card>
      <div style="text-align: center; padding: 40px;">
        <el-icon size="48px"><Loading /></el-icon>
        <h3>{{ componentName }} 组件加载中...</h3>
        <p>正在优化兼容性，请稍后再试</p>
      </div>
    </el-card>
  </div>
</template>
```

## 📋 测试检查单

- [ ] 首页能正常访问
- [ ] 导航栏按钮正常工作
- [ ] 工作区页面能加载
- [ ] 可视化演示页面能打开
- [ ] 3D分子查看器能渲染
- [ ] 网络图组件正常显示
- [ ] 流程编辑器可以使用
- [ ] 主题切换功能正常
- [ ] 控制台无严重错误

## 🛠️ 调试工具

### 浏览器控制台检查
```javascript
// 检查组件是否正确导入
console.log('组件库：', window.Vue)
console.log('Element Plus：', window.ElementPlus)

// 检查路由状态
console.log('当前路由：', this.$route)
```

### Vite 开发工具
```bash
# 检查依赖
npm list @antv/g6 @antv/x6 three

# 清理缓存重新启动
npm run dev -- --force
```

## 📞 问题排查

### 如果修复后仍有问题

1. **清理浏览器缓存**
   - 强制刷新：Ctrl+Shift+R
   - 清理Local Storage

2. **重启开发服务器**
   ```bash
   # 停止服务器（Ctrl+C）
   # 清理node_modules（如果需要）
   rm -rf node_modules package-lock.json
   npm install
   npm run dev
   ```

3. **回退方案**
   - 如果问题严重，可以暂时注释掉可视化组件
   - 先确保基础页面正常，再逐步启用功能

### 联系方式
如需进一步支持，请提供：
- 具体错误信息截图
- 浏览器控制台日志
- 操作系统和浏览器版本

---

**优先级**：🔴 高  
**预计修复时间**：5-10分钟  
**测试确认**：修复后需要完整测试所有页面功能 