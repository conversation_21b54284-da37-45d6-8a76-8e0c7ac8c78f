# UML 2.5 抽象类列表

UML元模型中的抽象类是不能直接实例化的类，它们通常用于定义公共属性和行为，由具体类继承实现。以下是UML 2.5规范中定义的所有抽象类，按照结构进行分类。

## 1. 结构类抽象元素

### 1.1 分类器相关
- **Classifier**：所有分类器的基类，表示对实例按照特征进行分类
- **BehavioredClassifier**：可以拥有行为的分类器
- **EncapsulatedClassifier**：封装的分类器，可以拥有端口
- **StructuredClassifier**：结构化分类器，可以包含内部结构

### 1.2 特征相关
- **Feature**：分类器拥有的特征的抽象基类
- **BehavioralFeature**：行为特征，如操作和接收
- **StructuralFeature**：结构特征，如属性
- **ConnectableElement**：可连接元素，能够参与连接器
- **Pin**：动作的输入和输出参数

### 1.3 基础元素
- **Element**：所有UML元素的抽象基类
- **NamedElement**：具有名称的元素
- **Namespace**：具有命名空间的元素
- **PackageableElement**：可放入包中的元素
- **ParameterableElement**：可参数化的元素
- **TemplateableElement**：可模板化的元素
- **TypedElement**：具有类型的元素
- **Type**：所有类型的抽象基类
- **MultiplicityElement**：具有多重性的元素
- **RedefinableElement**：可重定义的元素

## 2. 关系类抽象元素

### 2.1 关系基础
- **Relationship**：关系的抽象基类
- **DirectedRelationship**：有方向的关系
- **DeployedArtifact**：可部署的构件
- **DeploymentTarget**：部署目标

## 3. 行为类抽象元素

### 3.1 一般行为
- **Behavior**：行为的抽象基类
- **Event**：事件的抽象基类
- **MessageEvent**：消息事件

### 3.2 活动相关
- **ActivityNode**：活动节点的抽象基类
- **ActivityEdge**：活动边的抽象基类
- **ActivityGroup**：活动组的抽象基类
- **ControlNode**：控制节点的抽象基类
- **FinalNode**：最终节点的抽象基类
- **ObjectNode**：对象节点的抽象基类
- **ExecutableNode**：可执行节点的抽象基类

### 3.3 交互相关
- **InteractionFragment**：交互片段的抽象基类
- **ExecutionSpecification**：执行规约的抽象基类
- **MessageEnd**：消息端点的抽象基类
- **Vertex**：顶点的抽象基类，用于状态机

## 4. 操作类抽象元素

### 4.1 操作相关
- **Action**：操作的抽象基类
- **CallAction**：调用操作的抽象基类
- **InvocationAction**：调用行为的抽象基类
- **LinkAction**：链接操作的抽象基类
- **StructuralFeatureAction**：结构特征操作的抽象基类
- **VariableAction**：变量操作的抽象基类
- **WriteLinkAction**：写入链接操作的抽象基类
- **WriteStructuralFeatureAction**：写入结构特征操作的抽象基类
- **WriteVariableAction**：写入变量操作的抽象基类

## 5. 值规约类抽象元素

### 5.1 值规约相关
- **ValueSpecification**：值规约的抽象基类
- **LiteralSpecification**：字面值规约的抽象基类
- **Observation**：观察的抽象基类

## 抽象类的重要性

UML 元模型中的抽象类具有以下重要作用：

1. **分类学结构**：建立了元素之间的分类体系
2. **公共属性和行为**：定义了共享的特性和方法
3. **元模型完整性**：确保元模型的一致性和完整性
4. **扩展机制**：支持通过元模型扩展和自定义

这些抽象类不能直接用于建模，但它们为具体的建模元素提供了基础结构和行为，理解这些抽象类有助于更深入地把握UML的设计理念和内部组织结构。 