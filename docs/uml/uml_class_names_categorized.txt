# UML类型术语分类列表

## 1. 结构类元素 (Structural Elements)
### 1.1 分类器 (Classifiers)
- Class
- Interface
- Component
- DataType
- PrimitiveType
- Enumeration
- Signal
- Collaboration
- Actor
- UseCase
- Artifact
- Node
- Device
- ExecutionEnvironment
- InformationItem
- StructuredClassifier
- EncapsulatedClassifier
- BehavioredClassifier
- Classifier

### 1.2 特征 (Features)
- Property
- Operation
- Parameter
- ParameterSet
- Reception
- StructuralFeature
- BehavioralFeature
- Feature
- Slot
- Port
- ConnectableElement
- Pin
- InputPin
- OutputPin
- ValuePin

### 1.3 其他结构元素
- Package
- Model
- Profile
- Stereotype
- InstanceSpecification
- EnumerationLiteral
- Type
- TypedElement
- RedefinableElement
- Namespace
- NamedElement
- Element
- MultiplicityElement
- ParameterableElement
- PackageableElement
- ConnectableElementTemplateParameter
- ClassifierTemplateParameter
- OperationTemplateParameter

## 2. 关系类元素 (Relationship Elements)
### 2.1 关联关系
- Association
- AssociationClass
- Connector
- ConnectorEnd
- Extension
- ExtensionEnd
- LinkAction
- LinkEndData
- LinkEndCreationData
- LinkEndDestructionData

### 2.2 依赖关系
- Dependency
- Abstraction
- Realization
- Usage
- InterfaceRealization
- ComponentRealization
- Substitution
- ProtocolConformance
- Manifestation
- DeployedArtifact
- Deployment
- DeploymentSpecification
- DeploymentTarget

### 2.3 泛化关系
- Generalization
- GeneralizationSet
- Include
- Extend
- ExtensionPoint
- PackageImport
- PackageMerge
- ProfileApplication

### 2.4 其他关系
- DirectedRelationship
- Relationship
- InformationFlow

## 3. 行为类元素 (Behavioral Elements)
### 3.1 状态机相关
- StateMachine
- State
- FinalState
- Pseudostate
- Region
- Transition
- Vertex
- Trigger
- ProtocolStateMachine
- ProtocolTransition
- ConnectionPointReference

### 3.2 活动相关
- Activity
- ActivityNode
- ActivityEdge
- ActivityGroup
- ActivityPartition
- ActivityParameterNode
- ControlNode
- InitialNode
- FinalNode
- ActivityFinalNode
- FlowFinalNode
- ForkNode
- JoinNode
- MergeNode
- DecisionNode
- ControlFlow
- ObjectFlow
- ObjectNode
- CentralBufferNode
- DataStoreNode
- InterruptibleActivityRegion
- Variable
- ExceptionHandler
- ExecutableNode
- StructuredActivityNode
- ConditionalNode
- LoopNode
- SequenceNode
- ExpansionRegion
- ExpansionNode

### 3.3 交互相关
- Interaction
- Lifeline
- Message
- MessageEnd
- MessageOccurrenceSpecification
- MessageEvent
- ExecutionSpecification
- ActionExecutionSpecification
- BehaviorExecutionSpecification
- ExecutionOccurrenceSpecification
- InteractionUse
- InteractionOperand
- InteractionFragment
- CombinedFragment
- OccurrenceSpecification
- DestructionOccurrenceSpecification
- StateInvariant
- InteractionConstraint
- GeneralOrdering
- Gate
- PartDecomposition
- ConsiderIgnoreFragment
- Continuation

### 3.4 行为和事件
- Behavior
- OpaqueBehavior
- FunctionBehavior
- Event
- TimeEvent
- ChangeEvent
- CallEvent
- SignalEvent
- AnyReceiveEvent

## 4. 操作类元素 (Action Elements)
### 4.1 基本操作
- Action
- OpaqueAction
- InvocationAction
- CallAction
- CallBehaviorAction
- CallOperationAction
- SendSignalAction
- BroadcastSignalAction
- SendObjectAction
- StartObjectBehaviorAction
- StartClassifierBehaviorAction
- TestIdentityAction
- UnmarshallAction
- ReadSelfAction
- ReplyAction
- RaiseExceptionAction
- ReduceAction
- ActionInputPin

### 4.2 对象操作
- CreateObjectAction
- DestroyObjectAction
- ReadIsClassifiedObjectAction
- ReclassifyObjectAction
- ReadExtentAction
- CreateLinkAction
- CreateLinkObjectAction
- DestroyLinkAction
- ReadLinkAction
- ReadLinkObjectEndAction
- ReadLinkObjectEndQualifierAction
- ClearAssociationAction

### 4.3 特征操作
- StructuralFeatureAction
- ReadStructuralFeatureAction
- AddStructuralFeatureValueAction
- RemoveStructuralFeatureValueAction
- ClearStructuralFeatureAction
- WriteStructuralFeatureAction

### 4.4 变量操作
- VariableAction
- ReadVariableAction
- AddVariableValueAction
- RemoveVariableValueAction
- ClearVariableAction
- WriteVariableAction
- ValueSpecificationAction

## 5. 值规约类元素 (Value Specification Elements)
- ValueSpecification
- Expression
- OpaqueExpression
- StringExpression
- LiteralSpecification
- LiteralString
- LiteralInteger
- LiteralBoolean
- LiteralNull
- LiteralReal
- LiteralUnlimitedNatural
- InstanceValue
- Observation
- TimeObservation
- DurationObservation
- Duration
- TimeExpression
- Interval
- TimeInterval
- DurationInterval
- TimeConstraint
- DurationConstraint
- IntervalConstraint
- Constraint
- Comment
- Image

## 6. 模板类元素 (Template Elements)
- TemplateableElement
- TemplateSignature
- RedefinableTemplateSignature
- TemplateParameter
- TemplateParameterSubstitution
- TemplateBinding
- Clause
- QualifierValue 