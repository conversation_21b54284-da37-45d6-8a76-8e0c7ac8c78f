# UML 2.5 类型术语综合列表

本文档整合了UML 2.5元模型中定义的所有类型，并标注了抽象类(用[A]表示)。

## 1. 结构类元素 (Structural Elements)

### 1.1 分类器 (Classifiers)
- **[A] Classifier**：所有分类器的基类，表示对实例按照特征进行分类
- **[A] BehavioredClassifier**：可以拥有行为的分类器
- **[A] EncapsulatedClassifier**：封装的分类器，可以拥有端口
- **[A] StructuredClassifier**：结构化分类器，可以包含内部结构
- Class
- Interface
- Component
- DataType
- PrimitiveType
- Enumeration
- Signal
- Collaboration
- Actor
- UseCase
- Artifact
- Node
- Device
- ExecutionEnvironment
- InformationItem

### 1.2 特征 (Features)
- **[A] Feature**：分类器拥有的特征的抽象基类
- **[A] BehavioralFeature**：行为特征，如操作和接收
- **[A] StructuralFeature**：结构特征，如属性
- **[A] ConnectableElement**：可连接元素，能够参与连接器
- **[A] Pin**：动作的输入和输出参数
- Property
- Operation
- Parameter
- ParameterSet
- Reception
- Slot
- Port
- InputPin
- OutputPin
- ValuePin

### 1.3 基础元素
- **[A] Element**：所有UML元素的抽象基类
- **[A] NamedElement**：具有名称的元素
- **[A] Namespace**：具有命名空间的元素
- **[A] PackageableElement**：可放入包中的元素
- **[A] ParameterableElement**：可参数化的元素
- **[A] TemplateableElement**：可模板化的元素
- **[A] TypedElement**：具有类型的元素
- **[A] Type**：所有类型的抽象基类
- **[A] MultiplicityElement**：具有多重性的元素
- **[A] RedefinableElement**：可重定义的元素
- Package
- Model
- Profile
- Stereotype
- InstanceSpecification
- EnumerationLiteral
- ConnectableElementTemplateParameter
- ClassifierTemplateParameter
- OperationTemplateParameter

## 2. 关系类元素 (Relationship Elements)

### 2.1 关联关系
- Association
- AssociationClass
- Connector
- ConnectorEnd
- Extension
- ExtensionEnd
- **[A] LinkAction**：链接操作的抽象基类
- LinkEndData
- LinkEndCreationData
- LinkEndDestructionData

### 2.2 依赖关系
- Dependency
- Abstraction
- Realization
- Usage
- InterfaceRealization
- ComponentRealization
- Substitution
- ProtocolConformance
- Manifestation
- **[A] DeployedArtifact**：可部署的构件
- Deployment
- DeploymentSpecification
- **[A] DeploymentTarget**：部署目标

### 2.3 泛化关系
- Generalization
- GeneralizationSet
- Include
- Extend
- ExtensionPoint
- PackageImport
- PackageMerge
- ProfileApplication

### 2.4 其他关系
- **[A] DirectedRelationship**：有方向的关系
- **[A] Relationship**：关系的抽象基类
- InformationFlow

## 3. 行为类元素 (Behavioral Elements)

### 3.1 状态机相关
- StateMachine
- State
- FinalState
- Pseudostate
- Region
- Transition
- **[A] Vertex**：顶点的抽象基类，用于状态机
- Trigger
- ProtocolStateMachine
- ProtocolTransition
- ConnectionPointReference

### 3.2 活动相关
- Activity
- **[A] ActivityNode**：活动节点的抽象基类
- **[A] ActivityEdge**：活动边的抽象基类
- **[A] ActivityGroup**：活动组的抽象基类
- ActivityPartition
- ActivityParameterNode
- **[A] ControlNode**：控制节点的抽象基类
- InitialNode
- **[A] FinalNode**：最终节点的抽象基类
- ActivityFinalNode
- FlowFinalNode
- ForkNode
- JoinNode
- MergeNode
- DecisionNode
- ControlFlow
- ObjectFlow
- **[A] ObjectNode**：对象节点的抽象基类
- CentralBufferNode
- DataStoreNode
- InterruptibleActivityRegion
- Variable
- ExceptionHandler
- **[A] ExecutableNode**：可执行节点的抽象基类
- StructuredActivityNode
- ConditionalNode
- LoopNode
- SequenceNode
- ExpansionRegion
- ExpansionNode

### 3.3 交互相关
- Interaction
- Lifeline
- Message
- **[A] MessageEnd**：消息端点的抽象基类
- MessageOccurrenceSpecification
- **[A] MessageEvent**：消息事件
- **[A] ExecutionSpecification**：执行规约的抽象基类
- ActionExecutionSpecification
- BehaviorExecutionSpecification
- ExecutionOccurrenceSpecification
- InteractionUse
- InteractionOperand
- **[A] InteractionFragment**：交互片段的抽象基类
- CombinedFragment
- OccurrenceSpecification
- DestructionOccurrenceSpecification
- StateInvariant
- InteractionConstraint
- GeneralOrdering
- Gate
- PartDecomposition
- ConsiderIgnoreFragment
- Continuation

### 3.4 行为和事件
- **[A] Behavior**：行为的抽象基类
- OpaqueBehavior
- FunctionBehavior
- **[A] Event**：事件的抽象基类
- TimeEvent
- ChangeEvent
- CallEvent
- SignalEvent
- AnyReceiveEvent

## 4. 操作类元素 (Action Elements)

### 4.1 基本操作
- **[A] Action**：操作的抽象基类
- OpaqueAction
- **[A] InvocationAction**：调用行为的抽象基类
- **[A] CallAction**：调用操作的抽象基类
- CallBehaviorAction
- CallOperationAction
- SendSignalAction
- BroadcastSignalAction
- SendObjectAction
- StartObjectBehaviorAction
- StartClassifierBehaviorAction
- TestIdentityAction
- UnmarshallAction
- ReadSelfAction
- ReplyAction
- RaiseExceptionAction
- ReduceAction
- ActionInputPin

### 4.2 对象操作
- CreateObjectAction
- DestroyObjectAction
- ReadIsClassifiedObjectAction
- ReclassifyObjectAction
- ReadExtentAction
- CreateLinkAction
- CreateLinkObjectAction
- DestroyLinkAction
- ReadLinkAction
- ReadLinkObjectEndAction
- ReadLinkObjectEndQualifierAction
- ClearAssociationAction

### 4.3 特征操作
- **[A] StructuralFeatureAction**：结构特征操作的抽象基类
- ReadStructuralFeatureAction
- AddStructuralFeatureValueAction
- RemoveStructuralFeatureValueAction
- ClearStructuralFeatureAction
- **[A] WriteStructuralFeatureAction**：写入结构特征操作的抽象基类

### 4.4 变量操作
- **[A] VariableAction**：变量操作的抽象基类
- ReadVariableAction
- AddVariableValueAction
- RemoveVariableValueAction
- ClearVariableAction
- **[A] WriteVariableAction**：写入变量操作的抽象基类
- ValueSpecificationAction
- **[A] WriteLinkAction**：写入链接操作的抽象基类

## 5. 值规约类元素 (Value Specification Elements)
- **[A] ValueSpecification**：值规约的抽象基类
- Expression
- OpaqueExpression
- StringExpression
- **[A] LiteralSpecification**：字面值规约的抽象基类
- LiteralString
- LiteralInteger
- LiteralBoolean
- LiteralNull
- LiteralReal
- LiteralUnlimitedNatural
- InstanceValue
- **[A] Observation**：观察的抽象基类
- TimeObservation
- DurationObservation
- Duration
- TimeExpression
- Interval
- TimeInterval
- DurationInterval
- TimeConstraint
- DurationConstraint
- IntervalConstraint
- Constraint
- Comment
- Image

## 6. 模板类元素 (Template Elements)
- TemplateSignature
- RedefinableTemplateSignature
- TemplateParameter
- TemplateParameterSubstitution
- TemplateBinding
- Clause
- QualifierValue

## UML元模型中抽象类的重要性

UML 元模型中的抽象类具有以下重要作用：

1. **分类学结构**：建立了元素之间的分类体系
2. **公共属性和行为**：定义了共享的特性和方法
3. **元模型完整性**：确保元模型的一致性和完整性
4. **扩展机制**：支持通过元模型扩展和自定义

理解这些抽象类有助于更深入地把握UML的设计理念和内部组织结构，虽然它们不能直接用于建模，但为具体的建模元素提供了基础结构和行为。 