# UML 2.5 描述性类型整理

本文档整理了UML 2.5元模型中从属于主要元素的描述性类型，这些类型通常用于提供附加信息、约束或详细说明。

## 1. 从属于分类器的描述性类型

### 1.1 分类器附属类型
- **EnumerationLiteral**：从属于Enumeration，表示枚举的字面值
- **Feature**：从属于Classifier，描述分类器的特征
  - **StructuralFeature**：描述分类器的结构特征
    - **Property**：描述类的属性或关联端点
  - **BehavioralFeature**：描述分类器的行为特征
    - **Operation**：描述分类器可以执行的功能
    - **Reception**：描述类可以接收的信号

### 1.2 元素实例化相关
- **InstanceSpecification**：类的实例说明
  - **Slot**：实例中属性值的容器

### 1.3 模板与参数化
- **TemplateParameter**：类模板中的参数
- **ClassifierTemplateParameter**：专门用于分类器的模板参数
- **OperationTemplateParameter**：专门用于操作的模板参数
- **ConnectableElementTemplateParameter**：专门用于可连接元素的模板参数

## 2. 从属于关系的描述性类型

### 2.1 关联关系附属类型
- **AssociationClass**：同时作为关联和类的元素
- **ConnectorEnd**：从属于Connector，表示连接器的端点
- **LinkEndData**：描述链接端点的数据
  - **LinkEndCreationData**：创建链接时的端点数据
  - **LinkEndDestructionData**：销毁链接时的端点数据

### 2.2 泛化关系附属类型
- **GeneralizationSet**：表示一组泛化关系的集合
- **ExtensionPoint**：从属于UseCase，表示用例的扩展点

### 2.3 部署关系附属类型
- **DeploymentSpecification**：部署的具体规范说明

## 3. 从属于行为的描述性类型

### 3.1 状态机附属类型
- **Region**：从属于State或StateMachine，表示区域
- **Pseudostate**：表示状态机中的伪状态（如初始、历史等）
- **ConnectionPointReference**：引用进入/退出点
- **Trigger**：触发转换的条件

### 3.2 活动附属类型
- **ActivityParameterNode**：活动参数的节点表示
- **ActivityPartition**：活动的分区（泳道）
- **InterruptibleActivityRegion**：可中断的活动区域
- **ExceptionHandler**：活动中的异常处理器
- **ExpansionRegion**：扩展区域
- **ExpansionNode**：扩展节点

### 3.3 交互附属类型
- **Lifeline**：描述交互参与者在时间轴上的生命线
- **Message**：交互中对象之间的通信
- **Gate**：交互片段的形式参数
- **InteractionOperand**：组合片段中的操作数
- **StateInvariant**：状态约束
- **GeneralOrdering**：事件发生的顺序约束
- **ExecutionOccurrenceSpecification**：执行发生的说明
- **MessageOccurrenceSpecification**：消息发生的说明
- **DestructionOccurrenceSpecification**：对象销毁的说明

## 4. 从属于操作的描述性类型

### 4.1 针对特定操作的Pin类型
- **InputPin**：操作的输入参数
- **OutputPin**：操作的输出参数
- **ValuePin**：提供固定值的输入引脚
- **ActionInputPin**：由嵌套动作提供值的输入引脚

### 4.2 操作参数相关
- **Parameter**：操作或行为的参数
- **ParameterSet**：参数的逻辑分组
- **QualifierValue**：关联端点限定符的值

## 5. 从属于值规约的描述性类型

### 5.1 值规约具体类型
- **LiteralSpecification**：字面值的抽象基类
  - **LiteralBoolean**：布尔字面值
  - **LiteralInteger**：整数字面值
  - **LiteralReal**：实数字面值
  - **LiteralString**：字符串字面值
  - **LiteralNull**：空值
  - **LiteralUnlimitedNatural**：无限自然数值
- **Expression**：表达式规约
  - **StringExpression**：字符串表达式
  - **OpaqueExpression**：不透明（特定语言）表达式
- **InstanceValue**：实例值

### 5.2 约束与观察
- **Constraint**：对元素的约束
  - **IntervalConstraint**：区间约束
    - **TimeConstraint**：时间约束
    - **DurationConstraint**：持续时间约束
- **Observation**：对值的观察
  - **TimeObservation**：时间的观察
  - **DurationObservation**：持续时间的观察
- **Interval**：区间值规约
  - **TimeInterval**：时间区间
  - **DurationInterval**：持续时间区间
- **TimeExpression**：时间表达式
- **Duration**：持续时间

### 5.3 注释与图像
- **Comment**：对元素的注释说明
- **Image**：视觉表示

## 6. 从属于模板的描述性类型

- **TemplateBinding**：模板绑定
- **TemplateParameterSubstitution**：模板参数替换
- **TemplateSignature**：模板签名
  - **RedefinableTemplateSignature**：可重定义的模板签名

## 小结

这些描述性类型提供了UML建模过程中元素的附加信息和详细说明，它们通常不能独立存在，而是依附于某个主要元素类型。了解这些描述性类型的从属关系，有助于正确使用UML进行精确建模。 