# UML 2.5 元模型完整类型体系

本文档整合了UML 2.5元模型中的所有类型，包括核心术语（标准类型）和描述性类型，并用[A]标注抽象类，用[D]标注描述性类型。

## 1. 核心基础元素

### 1.1 根基类
- **[A] Element**：所有UML元素的抽象基类
- **[A] NamedElement**：具有名称的元素
- **[A] ParameterableElement**：可参数化的元素
- **[A] TypedElement**：具有类型的元素
- **[A] Type**：所有类型的抽象基类
- **[D] Comment**：对元素的注释说明
- **[D] Image**：元素的视觉表示

### 1.2 命名空间相关
- **[A] Namespace**：具有命名空间的元素
- **[A] PackageableElement**：可放入包中的元素
- **[A] TemplateableElement**：可模板化的元素
- **[A] RedefinableElement**：可重定义的元素
- **[A] MultiplicityElement**：具有多重性的元素
- **Package**：包，组织元素的容器
- **Model**：模型，系统的顶层描述

### 1.3 Profile与扩展机制核心元素
- **Profile**：配置文件，一种特殊的包（Package），用于定义一组相关的构造型（Stereotype）、标签定义（TagDefinition）和其他扩展。
- **Stereotype**：构造型，一种特殊的类（Class），用于扩展UML元模型中的元素（Metaclass），为其添加新的语义、属性（称为标签值）或约束。
- **Extension**：扩展关系，一种特殊的关联（Association），用于连接构造型（Stereotype）和它所扩展的一个或多个元模型类（Metaclass）。它表明构造型可以应用于这些元模型类的实例。
- **[D] ExtensionEnd**：扩展端点，作为扩展关系（Extension）的成员端（ownedEnd），通常是一个指向被扩展元模型类的属性（Property）。它指定了被扩展的元类以及构造型与元类实例之间的多重性。
- **[D] ProfileApplication**：配置文件应用，表示一个包（通常是模型或另一个包）应用了某个Profile。这使得该包内的元素可以使用Profile中定义的Stereotype。
- **[D] ElementImport**：元素导入，允许一个命名空间（Namespace）导入另一个命名空间中的特定可被打包元素（PackageableElement）。被导入的元素在该命名空间中可见。
- **[D] PackageImport**：包导入，允许一个命名空间（Namespace）导入另一个包（Package）的所有公共成员。这使得被导入包的公共内容在该命名空间中可见，常用于跨包/Profile的元素引用。

## 2. 分类器与特征

### 2.1 分类器核心
- **[A] Classifier**：所有分类器的基类，表示对实例按照特征进行分类
- **[A] BehavioredClassifier**：可以拥有行为的分类器
- **[A] EncapsulatedClassifier**：封装的分类器，可以拥有端口
- **[A] StructuredClassifier**：结构化分类器，可以包含内部结构
- **Class**：类，对象的描述符
- **Interface**：接口，提供服务的契约
- **DataType**：数据类型，无标识的值类型
- **PrimitiveType**：原始类型，基本数据类型
- **Enumeration**：枚举，有限的字面值集合
- **[D] EnumerationLiteral**：从属于Enumeration的字面值

### 2.2 其他分类器
- **Signal**：信号，可以触发异步通信的事件
- **Component**：组件，系统的模块化部分
- **Actor**：角色，外部与系统交互的实体
- **UseCase**：用例，系统提供给外部的功能
- **Artifact**：构件，软件的物理实体
- **Node**：节点，计算资源
- **Device**：设备，物理计算资源
- **ExecutionEnvironment**：执行环境，软件执行的环境
- **InformationItem**：信息项，抽象的信息元素
- **Collaboration**：协作，角色间的交互

### 2.3 分类器特征
- **[A] Feature**：从属于分类器的特征抽象基类
- **[A] BehavioralFeature**：描述分类器的行为特征
- **[A] StructuralFeature**：描述分类器的结构特征
- **Operation**：操作，分类器可以执行的功能
- **Property**：属性，分类器的结构特征
- **Parameter**：参数，操作或行为的形式参数
- **[D] ParameterSet**：参数的逻辑分组
- **Reception**：接收，分类器接收信号的说明

### 2.4 连接相关
- **[A] ConnectableElement**：可连接元素，能够参与连接器
- **Port**：端口，封装分类器与外部交互的点
- **Connector**：连接器，链接两个或多个连接元素
- **[D] ConnectorEnd**：连接器的端点说明
- **[D] ConnectableElementTemplateParameter**：可连接元素的模板参数

### 2.5 实例相关
- **InstanceSpecification**：类的实例说明
- **[D] Slot**：实例中属性值的容器

## 3. 关系元素

### 3.1 关系基础
- **[A] Relationship**：关系的抽象基类
- **[A] DirectedRelationship**：有方向的关系
- **InformationFlow**：信息流，信息传递

### 3.2 关联关系
- **Association**：关联，类之间的语义关系
- **[D] AssociationClass**：同时作为关联和类的元素
- **Extension**：扩展，构造型与元类的关系
- **[D] ExtensionEnd**：扩展的端点

### 3.3 依赖关系
- **Dependency**：依赖，元素间的使用关系
- **Abstraction**：抽象，高层与低层元素间的关系
- **Realization**：实现，接口与实现类的关系
- **Usage**：使用，客户端使用供应元
- **InterfaceRealization**：接口实现关系
- **ComponentRealization**：组件实现关系
- **Substitution**：替代，契约满足关系
- **ProtocolConformance**：协议一致性
- **Manifestation**：显示，逻辑元素到物理元素的映射

### 3.4 泛化关系
- **Generalization**：泛化，类之间的继承关系
- **[D] GeneralizationSet**：泛化关系的分组
- **Include**：包含，用例包含关系
- **Extend**：扩展，用例扩展关系
- **[D] ExtensionPoint**：用例的扩展点

### 3.5 包关系
- **PackageImport**：包导入关系
- **PackageMerge**：包合并关系
- **ProfileApplication**：配置文件应用关系

### 3.6 部署关系
- **[A] DeployedArtifact**：可部署的构件
- **[A] DeploymentTarget**：部署目标
- **Deployment**：部署关系
- **[D] DeploymentSpecification**：部署的详细规范

## 4. 行为元素

### 4.1 行为基础
- **[A] Behavior**：行为的抽象基类
- **OpaqueBehavior**：不透明行为
- **FunctionBehavior**：函数行为

### 4.2 状态机
- **StateMachine**：状态机，基于状态的行为模型
- **ProtocolStateMachine**：协议状态机
- **State**：状态，对象生命周期中的情况
- **FinalState**：终结状态
- **[A] Vertex**：状态机图中的顶点
- **[D] Pseudostate**：伪状态（初始、历史等）
- **[D] Region**：状态机或状态的区域
- **Transition**：转换，从源状态到目标状态
- **ProtocolTransition**：协议转换
- **[D] Trigger**：触发转换的条件
- **[D] ConnectionPointReference**：连接点引用

### 4.3 活动
- **Activity**：活动，行为的参数化序列
- **[A] ActivityNode**：活动节点抽象基类
- **[A] ActivityEdge**：活动边抽象基类
- **[A] ActivityGroup**：活动组抽象基类
- **[D] ActivityPartition**：活动的分区（泳道）
- **[D] ActivityParameterNode**：活动参数节点
- **[D] InterruptibleActivityRegion**：可中断活动区域
- **[D] ExceptionHandler**：活动中的异常处理
- **[D] Variable**：活动中的变量

#### 4.3.1 活动节点
- **[A] ControlNode**：控制节点的抽象基类
- **InitialNode**：初始节点
- **[A] FinalNode**：终结节点的抽象基类
- **ActivityFinalNode**：活动终结节点
- **FlowFinalNode**：流终结节点
- **ForkNode**：分叉节点
- **JoinNode**：结合节点
- **MergeNode**：合并节点
- **DecisionNode**：决策节点
- **[A] ObjectNode**：对象节点的抽象基类
- **CentralBufferNode**：中心缓冲节点
- **DataStoreNode**：数据存储节点
- **[A] ExecutableNode**：可执行节点的抽象基类
- **StructuredActivityNode**：结构化活动节点
- **ConditionalNode**：条件节点
- **LoopNode**：循环节点
- **SequenceNode**：序列节点
- **[D] ExpansionRegion**：扩展区域
- **[D] ExpansionNode**：扩展节点

#### 4.3.2 活动边
- **ControlFlow**：控制流
- **ObjectFlow**：对象流

### 4.4 交互
- **Interaction**：交互，对象间消息交换的规范
- **[A] InteractionFragment**：交互片段的抽象基类
- **CombinedFragment**：组合片段
- **[D] InteractionOperand**：交互操作数
- **InteractionUse**：交互使用
- **[D] Gate**：交互片段的形式参数
- **[D] Lifeline**：生命线
- **[D] Message**：消息
- **[A] MessageEnd**：消息端点的抽象基类
- **[D] MessageOccurrenceSpecification**：消息发生规范
- **[A] MessageEvent**：消息事件
- **OccurrenceSpecification**：发生规范
- **[D] DestructionOccurrenceSpecification**：销毁发生规范
- **[A] ExecutionSpecification**：执行规范的抽象基类
- **BehaviorExecutionSpecification**：行为执行规范
- **ActionExecutionSpecification**：动作执行规范
- **[D] ExecutionOccurrenceSpecification**：执行发生规范
- **[D] StateInvariant**：状态不变式
- **[D] InteractionConstraint**：交互约束
- **[D] GeneralOrdering**：一般顺序约束
- **[D] PartDecomposition**：部分分解
- **[D] ConsiderIgnoreFragment**：考虑/忽略片段
- **[D] Continuation**：延续

### 4.5 事件
- **[A] Event**：事件的抽象基类
- **TimeEvent**：时间事件
- **ChangeEvent**：变更事件
- **CallEvent**：调用事件
- **SignalEvent**：信号事件
- **AnyReceiveEvent**：任意接收事件

## 5. 动作元素

### 5.1 动作基础
- **[A] Action**：动作的抽象基类
- **OpaqueAction**：不透明动作
- **RaiseExceptionAction**：引发异常动作
- **ReplyAction**：回复动作
- **UnmarshallAction**：解组动作
- **ReduceAction**：归约动作
- **TestIdentityAction**：测试标识动作
- **ReadSelfAction**：读取自身动作

### 5.2 调用动作
- **[A] InvocationAction**：调用动作的抽象基类
- **[A] CallAction**：调用操作的抽象基类
- **CallBehaviorAction**：调用行为动作
- **CallOperationAction**：调用操作动作
- **SendSignalAction**：发送信号动作
- **BroadcastSignalAction**：广播信号动作
- **SendObjectAction**：发送对象动作
- **StartObjectBehaviorAction**：启动对象行为动作
- **StartClassifierBehaviorAction**：启动分类器行为动作

### 5.3 对象动作
- **CreateObjectAction**：创建对象动作
- **DestroyObjectAction**：销毁对象动作
- **ReadIsClassifiedObjectAction**：读取对象分类动作
- **ReclassifyObjectAction**：重分类对象动作
- **ReadExtentAction**：读取范围动作

### 5.4 链接动作
- **[A] LinkAction**：链接操作的抽象基类
- **[A] WriteLinkAction**：写入链接操作的抽象基类
- **CreateLinkAction**：创建链接动作
- **DestroyLinkAction**：销毁链接动作
- **ReadLinkAction**：读取链接动作
- **ClearAssociationAction**：清除关联动作
- **ReadLinkObjectEndAction**：读取链接对象端点动作
- **ReadLinkObjectEndQualifierAction**：读取链接对象端点限定符动作
- **CreateLinkObjectAction**：创建链接对象动作
- **[D] LinkEndData**：链接端点数据
- **[D] LinkEndCreationData**：链接端点创建数据
- **[D] LinkEndDestructionData**：链接端点销毁数据

### 5.5 结构特征动作
- **[A] StructuralFeatureAction**：结构特征操作的抽象基类
- **[A] WriteStructuralFeatureAction**：写入结构特征操作的抽象基类
- **ReadStructuralFeatureAction**：读取结构特征动作
- **AddStructuralFeatureValueAction**：添加结构特征值动作
- **RemoveStructuralFeatureValueAction**：移除结构特征值动作
- **ClearStructuralFeatureAction**：清除结构特征动作

### 5.6 变量动作
- **[A] VariableAction**：变量操作的抽象基类
- **[A] WriteVariableAction**：写入变量操作的抽象基类
- **ReadVariableAction**：读取变量动作
- **AddVariableValueAction**：添加变量值动作
- **RemoveVariableValueAction**：移除变量值动作
- **ClearVariableAction**：清除变量动作

### 5.7 Pin类型
- **[A] Pin**：动作的输入输出抽象基类
- **[D] InputPin**：动作的输入参数
- **[D] OutputPin**：动作的输出参数
- **[D] ValuePin**：提供固定值的输入引脚
- **[D] ActionInputPin**：由嵌套动作提供值的输入引脚
- **ValueSpecificationAction**：值规约动作

## 6. 值规约元素

### 6.1 值规约基础
- **[A] ValueSpecification**：值规约的抽象基类
- **[D] InstanceValue**：实例值
- **Expression**：表达式
- **OpaqueExpression**：不透明表达式
- **StringExpression**：字符串表达式

### 6.2 字面值规约
- **[A] LiteralSpecification**：字面值规约的抽象基类
- **LiteralBoolean**：布尔字面值
- **LiteralInteger**：整数字面值
- **LiteralString**：字符串字面值
- **LiteralReal**：实数字面值
- **LiteralNull**：空值
- **LiteralUnlimitedNatural**：无限自然数值

### 6.3 时间与约束
- **[A] Observation**：观察的抽象基类
- **TimeObservation**：时间观察
- **DurationObservation**：持续时间观察
- **Duration**：持续时间
- **TimeExpression**：时间表达式
- **Interval**：区间
- **TimeInterval**：时间区间
- **DurationInterval**：持续时间区间
- **TimeConstraint**：时间约束
- **DurationConstraint**：持续时间约束
- **IntervalConstraint**：区间约束
- **Constraint**：约束

## 7. 模板元素

### 7.1 模板基础
- **TemplateSignature**：模板签名
- **RedefinableTemplateSignature**：可重定义的模板签名
- **TemplateParameter**：模板参数
- **[D] TemplateParameterSubstitution**：模板参数替换
- **[D] TemplateBinding**：模板绑定
- **[D] ClassifierTemplateParameter**：分类器的模板参数
- **[D] OperationTemplateParameter**：操作的模板参数

### 7.2 其他
- **[D] Clause**：条款，用于条件节点
- **[D] QualifierValue**：限定符值

## 8. UML元模型中类型分类的意义 (章节号更新)

UML元模型中的类型分为以下几种：

1.  **抽象类[A]**：定义了概念框架但不能直接实例化
2.  **具体类**：可直接实例化的元素类型
3.  **描述性类型[D]**：为主要元素提供附加信息或约束的类型

理解这些类型及其分类对于正确理解UML规范、高效使用UML建模工具以及自定义UML概要都具有重要意义。抽象类定义了基本结构，具体类提供了实际的建模元素，而描述性类型则提供了更精细的建模能力。 