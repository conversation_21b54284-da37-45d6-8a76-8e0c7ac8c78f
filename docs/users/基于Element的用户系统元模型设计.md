# 基于Element的用户系统元模型设计

## 📋 设计概述

**文档名称**：基于Element的用户系统元模型设计  
**设计时间**：2024年12月  
**版本**：v1.0  
**目标**：将用户管理系统构建在Element核心元模型之上，实现统一的元元模型层

## 🎯 设计理念

### 核心思想
将所有系统数据都基于`Element`核心模型进行扩展，构建统一的元元模型架构：

```
┌─────────────────────────────────────────────────────────┐
│                    元元模型架构                           │
├─────────────────────────────────────────────────────────┤
│                   应用数据层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 用户数据    │ │ 项目数据    │ │ 模型数据    │        │
│  │ UserElement │ │ProjectElement│ │ModelElement │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                  领域模型层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │ 安全模型    │ │ 建模模型    │ │ 工作流模型   │        │
│  │SecurityDomain│ │ModelingDomain│ │WorkflowDomain│       │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                  核心元模型层                            │
│  ┌─────────────────────────────────────────────────────┐ │
│  │                Element 核心模型                      │ │
│  │ • 统一ID管理    • 元数据支持    • 语义信息           │ │
│  │ • 属性系统      • 引用关系      • 验证框架           │ │
│  │ • 序列化支持    • 变更追踪      • 扩展机制           │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 设计优势
1. **统一性**：所有数据使用相同的基础结构
2. **语义化**：利用Element的语义信息能力
3. **可扩展**：灵活的属性和自定义数据支持
4. **可追踪**：完整的变更历史和元数据
5. **可验证**：统一的验证框架
6. **MBSE集成**：天然支持建模系统集成

## 🏗️ 元模型扩展架构

### 1. Element扩展基类

```python
# backend/core/models/extended_element.py
from typing import Dict, List, Any, Optional, Type
from datetime import datetime
from abc import ABC, abstractmethod
from .element import Element, ElementType, ElementStatus, Attribute

class DomainElement(Element):
    """
    领域元素基类 - Element的领域扩展
    
    为特定领域（如用户管理、项目管理等）提供专门的扩展
    """
    
    # 领域类型注册表
    _domain_registry: Dict[str, Type['DomainElement']] = {}
    
    def __init__(self, 
                 element_id: str,
                 domain_type: str,
                 entity_type: str,
                 tag: Optional[str] = None):
        # 使用领域和实体类型构建tag
        computed_tag = tag or f"{domain_type}:{entity_type}"
        super().__init__(element_id, computed_tag, ElementType.CONTAINER)
        
        # 领域特定属性
        self.domain_type = domain_type
        self.entity_type = entity_type
        
        # 设置领域语义信息
        self.set_semantic_info(
            domain_category=domain_type,
            domain_code=domain_type.upper()[:3],
            confidence_score=100.0
        )
        
        # 添加实体类型标签
        self.add_tag(f"entity:{entity_type}")
        self.add_tag(f"domain:{domain_type}")
        
        # 领域特定数据
        self.domain_properties: Dict[str, Any] = {}
        self.business_rules: List[str] = []
        self.relationships: Dict[str, List[str]] = {}
    
    @classmethod
    def register_domain(cls, domain_type: str, domain_class: Type['DomainElement']):
        """注册领域类型"""
        cls._domain_registry[domain_type] = domain_class
    
    @classmethod
    def create_domain_element(cls, domain_type: str, entity_type: str, 
                            element_id: str, **kwargs) -> 'DomainElement':
        """创建领域元素"""
        domain_class = cls._domain_registry.get(domain_type, DomainElement)
        return domain_class(element_id, domain_type, entity_type, **kwargs)
    
    def set_domain_property(self, key: str, value: Any) -> 'DomainElement':
        """设置领域属性"""
        old_value = self.domain_properties.get(key)
        self.domain_properties[key] = value
        
        # 记录到Element的自定义数据中
        self.set_custom_property(f"domain:{key}", value)
        
        # 更新元数据
        self.metadata.updated_at = datetime.now()
        
        return self
    
    def add_business_rule(self, rule: str) -> 'DomainElement':
        """添加业务规则"""
        if rule not in self.business_rules:
            self.business_rules.append(rule)
            self.add_tag(f"rule:{rule}")
        return self
    
    def add_relationship(self, relation_type: str, target_id: str) -> 'DomainElement':
        """添加关系"""
        if relation_type not in self.relationships:
            self.relationships[relation_type] = []
        
        if target_id not in self.relationships[relation_type]:
            self.relationships[relation_type].append(target_id)
            
            # 添加引用
            self.add_reference(target_id, relation_type)
        
        return self
    
    def get_related_elements(self, relation_type: str) -> List[str]:
        """获取相关元素"""
        return self.relationships.get(relation_type, [])
    
    @abstractmethod
    def validate_domain_rules(self) -> bool:
        """验证领域特定规则"""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """扩展的字典转换"""
        base_dict = super().to_dict()
        base_dict.update({
            'domain_type': self.domain_type,
            'entity_type': self.entity_type,
            'domain_properties': self.domain_properties,
            'business_rules': self.business_rules,
            'relationships': self.relationships
        })
        return base_dict
```

### 2. 用户管理领域扩展

```python
# backend/domains/security/models.py
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum
import bcrypt
import secrets
from core.models.extended_element import DomainElement

class SecurityElementType(Enum):
    """安全元素类型"""
    USER = "user"
    ROLE = "role"
    PERMISSION = "permission"
    SESSION = "session"
    AUDIT_LOG = "audit_log"

class SecurityDomain(DomainElement):
    """安全领域基类"""
    
    def __init__(self, element_id: str, entity_type: str, tag: Optional[str] = None):
        super().__init__(element_id, "security", entity_type, tag)
        
        # 安全特定属性
        self.security_level = "standard"
        self.encryption_enabled = False
        self.audit_enabled = True
    
    def set_security_level(self, level: str) -> 'SecurityDomain':
        """设置安全级别"""
        self.security_level = level
        self.add_tag(f"security_level:{level}")
        return self
    
    def enable_encryption(self) -> 'SecurityDomain':
        """启用加密"""
        self.encryption_enabled = True
        self.add_tag("encrypted")
        return self
    
    def validate_domain_rules(self) -> bool:
        """验证安全领域规则"""
        return True

class UserElement(SecurityDomain):
    """用户元素 - 基于Element的用户模型"""
    
    def __init__(self, element_id: str, username: str, email: str):
        super().__init__(element_id, SecurityElementType.USER.value)
        
        # 基本用户信息作为Element属性
        self.add_attribute("username", username, data_type="string", is_required=True)
        self.add_attribute("email", email, data_type="email", is_required=True)
        self.add_attribute("is_active", True, data_type="boolean")
        self.add_attribute("is_verified", False, data_type="boolean")
        self.add_attribute("last_login", None, data_type="datetime")
        
        # 安全相关属性
        self.password_hash = ""
        self.salt = ""
        self.failed_login_attempts = 0
        self.locked_until: Optional[datetime] = None
        
        # 用户特定的领域属性
        self.set_domain_property("profile", {})
        self.set_domain_property("preferences", {})
        self.set_domain_property("workspace_config", {})
        
        # 设置用户特定的语义信息
        self.semantic_info.semantic_tags.extend([
            "identity", "authentication", "authorization"
        ])
        
        # 添加用户特定的业务规则
        self.add_business_rule("unique_username")
        self.add_business_rule("unique_email")
        self.add_business_rule("password_policy")
    
    def set_password(self, password: str) -> 'UserElement':
        """设置密码（哈希存储）"""
        self.salt = secrets.token_hex(32)
        self.password_hash = bcrypt.hashpw(
            (password + self.salt).encode('utf-8'), 
            bcrypt.gensalt()
        ).decode('utf-8')
        
        # 更新元数据
        self.metadata.updated_at = datetime.now()
        self.add_tag("password_set")
        
        return self
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        if not self.password_hash or not self.salt:
            return False
        
        return bcrypt.checkpw(
            (password + self.salt).encode('utf-8'),
            self.password_hash.encode('utf-8')
        )
    
    def set_profile_info(self, first_name: str = "", last_name: str = "", 
                        organization: str = "", department: str = "") -> 'UserElement':
        """设置用户资料"""
        profile = {
            "first_name": first_name,
            "last_name": last_name,
            "organization": organization,
            "department": department,
            "updated_at": datetime.now().isoformat()
        }
        
        self.set_domain_property("profile", profile)
        
        # 添加为Element属性便于搜索
        if first_name:
            self.add_attribute("first_name", first_name)
        if last_name:
            self.add_attribute("last_name", last_name)
        if organization:
            self.add_attribute("organization", organization)
            self.add_tag(f"org:{organization}")
        
        return self
    
    def assign_role(self, role_id: str) -> 'UserElement':
        """分配角色"""
        self.add_relationship("has_role", role_id)
        self.add_tag(f"role:{role_id}")
        return self
    
    def revoke_role(self, role_id: str) -> 'UserElement':
        """撤销角色"""
        roles = self.get_related_elements("has_role")
        if role_id in roles:
            roles.remove(role_id)
            self.relationships["has_role"] = roles
        return self
    
    def get_roles(self) -> List[str]:
        """获取用户角色"""
        return self.get_related_elements("has_role")
    
    def record_login(self, ip_address: str, user_agent: str) -> 'UserElement':
        """记录登录"""
        login_time = datetime.now()
        self.add_attribute("last_login", login_time, data_type="datetime")
        
        # 记录登录历史
        login_record = {
            "timestamp": login_time.isoformat(),
            "ip_address": ip_address,
            "user_agent": user_agent
        }
        
        login_history = self.get_domain_property("login_history", [])
        login_history.append(login_record)
        
        # 只保留最近50次登录记录
        if len(login_history) > 50:
            login_history = login_history[-50:]
        
        self.set_domain_property("login_history", login_history)
        self.failed_login_attempts = 0  # 重置失败次数
        
        return self
    
    def record_failed_login(self) -> 'UserElement':
        """记录登录失败"""
        self.failed_login_attempts += 1
        
        # 账户锁定逻辑
        if self.failed_login_attempts >= 5:
            self.locked_until = datetime.now().replace(
                minute=datetime.now().minute + 30  # 锁定30分钟
            )
            self.add_tag("locked")
        
        return self
    
    def is_locked(self) -> bool:
        """检查账户是否被锁定"""
        if self.locked_until and datetime.now() < self.locked_until:
            return True
        
        # 锁定期已过，重置状态
        if self.locked_until:
            self.locked_until = None
            self.failed_login_attempts = 0
            self.semantic_info.semantic_tags = [
                tag for tag in self.semantic_info.semantic_tags if tag != "locked"
            ]
        
        return False
    
    def validate_domain_rules(self) -> bool:
        """验证用户领域规则"""
        # 验证用户名唯一性（需要在服务层实现）
        # 验证邮箱格式
        # 验证密码策略
        return True
    
    def get_username(self) -> str:
        """获取用户名"""
        return self.get_attribute_value("username", "")
    
    def get_email(self) -> str:
        """获取邮箱"""
        return self.get_attribute_value("email", "")
    
    def is_active(self) -> bool:
        """检查用户是否激活"""
        return self.get_attribute_value("is_active", False)
    
    def is_verified(self) -> bool:
        """检查用户是否已验证"""
        return self.get_attribute_value("is_verified", False)

class RoleElement(SecurityDomain):
    """角色元素 - 基于Element的角色模型"""
    
    def __init__(self, element_id: str, role_name: str, display_name: str = ""):
        super().__init__(element_id, SecurityElementType.ROLE.value)
        
        # 角色基本信息
        self.add_attribute("role_name", role_name, data_type="string", is_required=True)
        self.add_attribute("display_name", display_name or role_name, data_type="string")
        self.add_attribute("is_system_role", False, data_type="boolean")
        self.add_attribute("is_active", True, data_type="boolean")
        
        # 角色描述
        self.set_text_content("")  # 角色描述
        
        # 角色特定属性
        self.set_domain_property("permissions", [])
        self.set_domain_property("inheritance", [])
        
        # 添加角色语义标签
        self.add_tag("authorization")
        self.add_tag(f"role_type:custom")
        
        # 业务规则
        self.add_business_rule("unique_role_name")
        self.add_business_rule("permission_inheritance")
    
    def set_description(self, description: str) -> 'RoleElement':
        """设置角色描述"""
        self.set_text_content(description)
        return self
    
    def set_system_role(self, is_system: bool = True) -> 'RoleElement':
        """设置为系统角色"""
        self.add_attribute("is_system_role", is_system, data_type="boolean")
        if is_system:
            self.add_tag("system_role")
        return self
    
    def grant_permission(self, permission_id: str) -> 'RoleElement':
        """授予权限"""
        self.add_relationship("has_permission", permission_id)
        return self
    
    def revoke_permission(self, permission_id: str) -> 'RoleElement':
        """撤销权限"""
        permissions = self.get_related_elements("has_permission")
        if permission_id in permissions:
            permissions.remove(permission_id)
            self.relationships["has_permission"] = permissions
        return self
    
    def get_permissions(self) -> List[str]:
        """获取角色权限"""
        return self.get_related_elements("has_permission")
    
    def inherit_from(self, parent_role_id: str) -> 'RoleElement':
        """继承父角色"""
        self.add_relationship("inherits_from", parent_role_id)
        return self
    
    def validate_domain_rules(self) -> bool:
        """验证角色领域规则"""
        return True

class PermissionElement(SecurityDomain):
    """权限元素 - 基于Element的权限模型"""
    
    def __init__(self, element_id: str, permission_name: str, 
                 resource: str, action: str):
        super().__init__(element_id, SecurityElementType.PERMISSION.value)
        
        # 权限基本信息
        self.add_attribute("permission_name", permission_name, data_type="string", is_required=True)
        self.add_attribute("resource", resource, data_type="string", is_required=True)
        self.add_attribute("action", action, data_type="string", is_required=True)
        self.add_attribute("scope", "global", data_type="string")
        
        # 权限类型
        self.set_domain_property("permission_type", "functional")
        self.set_domain_property("conditions", [])
        
        # 语义标签
        self.add_tag("authorization")
        self.add_tag(f"resource:{resource}")
        self.add_tag(f"action:{action}")
        
        # 业务规则
        self.add_business_rule("unique_permission_combination")
    
    def set_scope(self, scope: str) -> 'PermissionElement':
        """设置权限范围"""
        self.add_attribute("scope", scope, data_type="string")
        self.add_tag(f"scope:{scope}")
        return self
    
    def add_condition(self, condition: str) -> 'PermissionElement':
        """添加权限条件"""
        conditions = self.get_domain_property("conditions", [])
        if condition not in conditions:
            conditions.append(condition)
            self.set_domain_property("conditions", conditions)
            self.add_tag(f"condition:{condition}")
        return self
    
    def get_resource(self) -> str:
        """获取资源类型"""
        return self.get_attribute_value("resource", "")
    
    def get_action(self) -> str:
        """获取操作类型"""
        return self.get_attribute_value("action", "")
    
    def validate_domain_rules(self) -> bool:
        """验证权限领域规则"""
        return True
```

### 3. 用户服务层扩展

```python
# backend/services/user_element_service.py
from typing import List, Optional, Dict, Any
from datetime import datetime
from domains.security.models import UserElement, RoleElement, PermissionElement
from core.models.element import ElementCollection

class UserElementService:
    """用户元素服务 - 基于Element的用户管理"""
    
    def __init__(self, element_store: ElementCollection):
        self.store = element_store
    
    def create_user(self, username: str, email: str, password: str, 
                   profile_info: Dict[str, str] = None) -> UserElement:
        """创建用户元素"""
        # 生成用户ID
        user_id = f"user_{int(datetime.now().timestamp() * 1000000)}"
        
        # 创建用户元素
        user = UserElement(user_id, username, email)
        user.set_password(password)
        
        # 设置用户资料
        if profile_info:
            user.set_profile_info(**profile_info)
        
        # 添加到存储
        self.store.add_element(user)
        
        return user
    
    def authenticate_user(self, login: str, password: str) -> Optional[UserElement]:
        """认证用户"""
        # 查找用户（通过用户名或邮箱）
        user = self.find_user_by_login(login)
        
        if not user:
            return None
        
        # 检查账户状态
        if not user.is_active():
            return None
        
        # 检查账户锁定
        if user.is_locked():
            return None
        
        # 验证密码
        if user.verify_password(password):
            return user
        else:
            # 记录失败登录
            user.record_failed_login()
            return None
    
    def find_user_by_login(self, login: str) -> Optional[UserElement]:
        """通过用户名或邮箱查找用户"""
        all_elements = self.store.get_all_elements()
        
        for element in all_elements:
            if (isinstance(element, UserElement) and 
                (element.get_username() == login or element.get_email() == login)):
                return element
        
        return None
    
    def find_user_by_id(self, user_id: str) -> Optional[UserElement]:
        """通过ID查找用户"""
        element = self.store.get_element(user_id)
        return element if isinstance(element, UserElement) else None
    
    def update_user_profile(self, user_id: str, profile_data: Dict[str, Any]) -> bool:
        """更新用户资料"""
        user = self.find_user_by_id(user_id)
        if not user:
            return False
        
        # 更新资料信息
        user.set_profile_info(**profile_data)
        
        return True
    
    def assign_role_to_user(self, user_id: str, role_id: str) -> bool:
        """为用户分配角色"""
        user = self.find_user_by_id(user_id)
        role = self.store.get_element(role_id)
        
        if user and isinstance(role, RoleElement):
            user.assign_role(role_id)
            return True
        
        return False
    
    def get_users_by_organization(self, organization: str) -> List[UserElement]:
        """按组织查找用户"""
        all_elements = self.store.get_all_elements()
        users = []
        
        for element in all_elements:
            if (isinstance(element, UserElement) and 
                element.get_attribute_value("organization") == organization):
                users.append(element)
        
        return users
    
    def get_users_with_role(self, role_id: str) -> List[UserElement]:
        """获取具有特定角色的用户"""
        all_elements = self.store.get_all_elements()
        users = []
        
        for element in all_elements:
            if (isinstance(element, UserElement) and 
                role_id in element.get_roles()):
                users.append(element)
        
        return users
    
    def search_users(self, query: str) -> List[UserElement]:
        """搜索用户"""
        all_elements = self.store.get_all_elements()
        results = []
        
        query_lower = query.lower()
        
        for element in all_elements:
            if isinstance(element, UserElement):
                # 搜索用户名、邮箱、姓名等
                if (query_lower in element.get_username().lower() or
                    query_lower in element.get_email().lower() or
                    query_lower in element.get_attribute_value("first_name", "").lower() or
                    query_lower in element.get_attribute_value("last_name", "").lower()):
                    results.append(element)
        
        return results
```

### 4. 查询和操作接口

```python
# backend/api/element_query_api.py
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Query, Path
from core.models.element import ElementCollection
from domains.security.models import UserElement, RoleElement, PermissionElement

router = APIRouter(prefix="/api/elements", tags=["元素查询"])

class ElementQueryService:
    """元素查询服务"""
    
    def __init__(self, element_store: ElementCollection):
        self.store = element_store
    
    def query_by_domain(self, domain_type: str) -> List[Dict[str, Any]]:
        """按领域查询元素"""
        results = []
        all_elements = self.store.get_all_elements()
        
        for element in all_elements:
            if hasattr(element, 'domain_type') and element.domain_type == domain_type:
                results.append(element.to_dict())
        
        return results
    
    def query_by_semantic_tag(self, tag: str) -> List[Dict[str, Any]]:
        """按语义标签查询"""
        results = []
        all_elements = self.store.get_all_elements()
        
        for element in all_elements:
            if tag in element.semantic_info.semantic_tags:
                results.append(element.to_dict())
        
        return results
    
    def query_by_relationship(self, relation_type: str, 
                            target_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """按关系查询"""
        results = []
        all_elements = self.store.get_all_elements()
        
        for element in all_elements:
            if hasattr(element, 'relationships'):
                relations = element.relationships.get(relation_type, [])
                if target_id:
                    if target_id in relations:
                        results.append(element.to_dict())
                else:
                    if relations:  # 有该类型关系
                        results.append(element.to_dict())
        
        return results
    
    def full_text_search(self, query: str) -> List[Dict[str, Any]]:
        """全文搜索"""
        results = []
        all_elements = self.store.get_all_elements()
        query_lower = query.lower()
        
        for element in all_elements:
            # 搜索内容
            searchable_content = [
                element.tag,
                element.text_content,
                element.metadata.description,
                ' '.join(element.semantic_info.semantic_tags)
            ]
            
            # 搜索属性值
            for attr in element.attributes.values():
                if isinstance(attr.value, str):
                    searchable_content.append(attr.value)
            
            # 检查是否匹配
            if any(query_lower in content.lower() for content in searchable_content if content):
                results.append(element.to_dict())
        
        return results

@router.get("/domain/{domain_type}")
async def get_elements_by_domain(domain_type: str = Path(..., description="领域类型")):
    """获取指定领域的所有元素"""
    # 实现查询逻辑
    pass

@router.get("/search")
async def search_elements(
    q: str = Query(..., description="搜索查询"),
    domain: Optional[str] = Query(None, description="限制搜索的领域"),
    tag: Optional[str] = Query(None, description="语义标签过滤")
):
    """搜索元素"""
    # 实现搜索逻辑
    pass

@router.get("/relationships/{relation_type}")
async def get_elements_by_relationship(
    relation_type: str = Path(..., description="关系类型"),
    target_id: Optional[str] = Query(None, description="目标元素ID")
):
    """按关系查询元素"""
    # 实现关系查询逻辑
    pass
```

## 📊 数据存储策略

### 1. Element存储模式

```python
# backend/storage/element_storage.py
from typing import Dict, List, Any, Optional
import json
from sqlalchemy import Column, String, Text, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from core.models.element import Element

Base = declarative_base()

class ElementEntity(Base):
    """Element的数据库实体"""
    __tablename__ = 'elements'
    
    id = Column(String, primary_key=True)
    tag = Column(String, index=True)
    element_type = Column(String, index=True)
    domain_type = Column(String, index=True, nullable=True)
    entity_type = Column(String, index=True, nullable=True)
    
    # Element完整数据的JSON存储
    element_data = Column(JSON)
    
    # 快速查询字段
    text_content = Column(Text)
    semantic_tags = Column(JSON)  # 语义标签数组
    attributes = Column(JSON)     # 属性键值对
    relationships = Column(JSON)  # 关系映射
    
    # 时间戳
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
    
    @classmethod
    def from_element(cls, element: Element) -> 'ElementEntity':
        """从Element创建实体"""
        entity = cls()
        entity.id = element.id
        entity.tag = element.tag
        entity.element_type = element.element_type.value
        
        # 提取领域信息
        if hasattr(element, 'domain_type'):
            entity.domain_type = element.domain_type
            entity.entity_type = element.entity_type
        
        # 存储完整数据
        entity.element_data = element.to_dict()
        
        # 提取快速查询字段
        entity.text_content = element.text_content
        entity.semantic_tags = element.semantic_info.semantic_tags
        entity.attributes = {k: v.value for k, v in element.attributes.items()}
        
        if hasattr(element, 'relationships'):
            entity.relationships = element.relationships
        
        entity.created_at = element.metadata.created_at
        entity.updated_at = element.metadata.updated_at
        
        return entity
    
    def to_element(self) -> Element:
        """转换为Element对象"""
        return Element.from_dict(self.element_data)
```

### 2. 查询优化策略

```python
# backend/storage/element_repository.py
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, text
from .element_storage import ElementEntity

class ElementRepository:
    """Element仓储"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def find_by_domain(self, domain_type: str, entity_type: Optional[str] = None) -> List[ElementEntity]:
        """按领域查找"""
        query = self.db.query(ElementEntity).filter(ElementEntity.domain_type == domain_type)
        
        if entity_type:
            query = query.filter(ElementEntity.entity_type == entity_type)
        
        return query.all()
    
    def find_by_semantic_tag(self, tag: str) -> List[ElementEntity]:
        """按语义标签查找"""
        return self.db.query(ElementEntity).filter(
            ElementEntity.semantic_tags.contains([tag])
        ).all()
    
    def find_by_relationship(self, relation_type: str, target_id: Optional[str] = None) -> List[ElementEntity]:
        """按关系查找"""
        if target_id:
            # 查找与特定目标有关系的元素
            return self.db.query(ElementEntity).filter(
                text(f"JSON_EXTRACT(relationships, '$.{relation_type}') LIKE '%{target_id}%'")
            ).all()
        else:
            # 查找有该类型关系的所有元素
            return self.db.query(ElementEntity).filter(
                text(f"JSON_EXTRACT(relationships, '$.{relation_type}') IS NOT NULL")
            ).all()
    
    def full_text_search(self, query: str, domain_type: Optional[str] = None) -> List[ElementEntity]:
        """全文搜索"""
        base_query = self.db.query(ElementEntity)
        
        # 构建搜索条件
        search_conditions = [
            ElementEntity.tag.contains(query),
            ElementEntity.text_content.contains(query),
            text(f"JSON_SEARCH(element_data, 'one', '%{query}%') IS NOT NULL")
        ]
        
        search_filter = or_(*search_conditions)
        
        if domain_type:
            base_query = base_query.filter(ElementEntity.domain_type == domain_type)
        
        return base_query.filter(search_filter).all()
    
    def find_user_by_login(self, login: str) -> Optional[ElementEntity]:
        """查找用户（特化查询）"""
        return self.db.query(ElementEntity).filter(
            and_(
                ElementEntity.domain_type == "security",
                ElementEntity.entity_type == "user",
                or_(
                    text(f"JSON_EXTRACT(attributes, '$.username') = '{login}'"),
                    text(f"JSON_EXTRACT(attributes, '$.email') = '{login}'")
                )
            )
        ).first()
    
    def find_users_with_role(self, role_id: str) -> List[ElementEntity]:
        """查找具有特定角色的用户"""
        return self.db.query(ElementEntity).filter(
            and_(
                ElementEntity.domain_type == "security",
                ElementEntity.entity_type == "user",
                text(f"JSON_EXTRACT(relationships, '$.has_role') LIKE '%{role_id}%'")
            )
        ).all()
```

## 🚀 实施方案

### 第一阶段：基础架构迁移
1. **Element扩展基类**：实现DomainElement基类
2. **安全领域模型**：创建UserElement、RoleElement、PermissionElement
3. **存储层适配**：实现Element的数据库存储
4. **基础服务层**：用户管理服务的Element版本

### 第二阶段：功能完善
1. **查询接口**：丰富的Element查询API
2. **关系管理**：用户-角色-权限关系处理
3. **搜索功能**：语义化搜索和全文搜索
4. **验证框架**：领域规则验证

### 第三阶段：系统集成
1. **其他领域**：项目管理、建模等领域的Element化
2. **跨域关系**：不同领域间的Element关系
3. **元数据管理**：统一的元数据和语义管理
4. **性能优化**：大规模Element的查询优化

## 📈 优势总结

### 1. 统一性
- 所有数据使用相同的Element基础结构
- 统一的ID管理、元数据、时间戳等
- 一致的序列化和验证机制

### 2. 语义化
- 丰富的语义标签和分类系统
- 领域特定的语义信息
- 支持本体映射和知识图谱

### 3. 可扩展性
- 灵活的属性系统支持任意扩展
- 自定义数据字段
- 领域特定的业务规则

### 4. 关系性
- 完整的引用和关系系统
- 支持复杂的多对多关系
- 关系类型的语义化

### 5. 可追踪性
- 完整的变更历史
- 元数据记录
- 审计追踪

### 6. MBSE集成
- 天然支持建模系统集成
- 与XML处理系统无缝对接
- 支持模型驱动的开发

这种基于Element的设计真正实现了**万物皆Element**的理念，为整个生物医学MBSE建模平台提供了统一、强大、可扩展的数据基础！ 