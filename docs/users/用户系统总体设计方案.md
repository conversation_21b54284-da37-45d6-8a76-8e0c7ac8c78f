# 生物医学MBSE建模平台用户系统总体设计方案

## 📋 项目概述

**文档名称**：生物医学MBSE建模平台用户系统总体设计方案  
**设计时间**：2024年12月  
**版本**：v1.0  
**目标**：为生物医学MBSE建模平台构建完整的用户管理、认证授权和权限控制系统

## 🎯 设计背景

### 项目需求
基于《实施规划详细方案》，用户管理系统被确定为**第一阶段最高优先级**任务，需要在1周内完成基础功能，为后续的文件管理、模型解析和建模功能提供安全可靠的用户基础。

### 技术约束
- **后端技术栈**：Python 3.11+ + FastAPI + SQLAlchemy + PostgreSQL
- **前端技术栈**：Vue 3 + TypeScript + Element Plus + Pinia
- **认证方式**：JWT (JSON Web Token) 
- **部署环境**：Docker容器化部署

## 🏗️ 系统架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        用户系统架构                               │
├─────────────────────────────────────────────────────────────────┤
│                     前端用户界面层                                │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │   登录注册界面   │ │   用户个人中心   │ │   权限控制组件   │    │
│  │   LoginView.vue │ │ ProfileView.vue │ │ PermissionGuard │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
├─────────────────────────────────────────────────────────────────┤
│                      API网关层                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              FastAPI 用户认证路由                            │ │
│  │  /auth/login  /auth/register  /auth/refresh  /users/profile │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     业务逻辑层                                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │   用户服务      │ │   认证服务      │ │   权限服务      │    │
│  │  UserService    │ │   AuthService   │ │ PermissionService│    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
├─────────────────────────────────────────────────────────────────┤
│                     数据访问层                                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │   用户仓储      │ │   会话仓储      │ │   权限仓储      │    │
│  │ UserRepository  │ │SessionRepository│ │ RoleRepository  │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
├─────────────────────────────────────────────────────────────────┤
│                     数据存储层                                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│  │  PostgreSQL     │ │     Redis       │ │   日志系统      │    │
│  │   用户数据      │ │  会话缓存       │ │  操作审计       │    │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────────────────────────────────────────┘
```

### 核心模块

#### 1. 用户认证模块
- **用户注册**：邮箱验证、密码强度检查
- **用户登录**：JWT Token生成、多设备登录控制
- **密码管理**：密码重置、修改密码、安全验证
- **会话管理**：Token刷新、自动登出、并发控制

#### 2. 权限管理模块
- **角色管理**：预定义角色、自定义角色
- **权限控制**：资源级权限、操作级权限
- **访问控制**：API访问控制、前端路由守卫
- **权限继承**：角色继承、权限组合

#### 3. 用户配置模块
- **个人资料**：基本信息、偏好设置
- **工作空间配置**：界面布局、主题选择、工具配置
- **建模配置**：默认元素样式、图形模板、视图偏好
- **通知设置**：消息提醒、邮件通知

## 🔐 安全架构设计

### 认证流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端应用
    participant Gateway as API网关
    participant AuthService as 认证服务
    participant Database as 数据库
    participant Redis as Redis缓存

    User->>Frontend: 输入用户名密码
    Frontend->>Gateway: POST /auth/login
    Gateway->>AuthService: 验证凭据
    AuthService->>Database: 查询用户信息
    Database-->>AuthService: 返回用户数据
    AuthService->>AuthService: 验证密码哈希
    AuthService->>Redis: 存储会话信息
    AuthService-->>Gateway: 返回JWT Token
    Gateway-->>Frontend: 返回认证结果
    Frontend->>Frontend: 存储Token到localStorage
    Frontend-->>User: 登录成功
```

### 权限控制流程
```mermaid
sequenceDiagram
    participant Frontend as 前端应用
    participant Gateway as API网关
    participant AuthMiddleware as 认证中间件
    participant PermissionService as 权限服务
    participant Resource as 目标资源

    Frontend->>Gateway: 请求资源 (携带JWT)
    Gateway->>AuthMiddleware: 验证Token
    AuthMiddleware->>AuthMiddleware: 解析用户信息
    AuthMiddleware->>PermissionService: 检查权限
    PermissionService->>PermissionService: 验证资源访问权限
    alt 权限验证通过
        PermissionService-->>Gateway: 允许访问
        Gateway->>Resource: 转发请求
        Resource-->>Gateway: 返回结果
        Gateway-->>Frontend: 返回数据
    else 权限验证失败
        PermissionService-->>Gateway: 拒绝访问
        Gateway-->>Frontend: 返回403错误
    end
```

## 📊 用户角色与权限模型

### 用户角色定义

#### 1. 系统管理员 (System Admin)
- **权限范围**：全系统管理权限
- **主要职责**：
  - 用户账户管理（创建、删除、禁用）
  - 系统配置管理
  - 权限角色分配
  - 系统监控和维护
  - 审计日志查看

#### 2. 项目管理员 (Project Admin)
- **权限范围**：特定项目管理权限
- **主要职责**：
  - 项目创建和配置
  - 项目成员管理
  - 项目权限分配
  - 项目模板管理
  - 项目资源配置

#### 3. 建模工程师 (Modeling Engineer)
- **权限范围**：建模功能完整权限
- **主要职责**：
  - UML/SysML图形设计
  - 模型验证和转换
  - 生物医学模型专业化
  - 工具链集成使用
  - 模型版本管理

#### 4. 领域专家 (Domain Expert)
- **权限范围**：领域知识和审核权限
- **主要职责**：
  - 生物医学知识审核
  - 模型合规性检查
  - 领域标准配置
  - 专业化建议提供
  - 知识库维护

#### 5. 普通用户 (Regular User)
- **权限范围**：基础使用权限
- **主要职责**：
  - 查看已授权模型
  - 基础建模操作
  - 个人工作空间管理
  - 文档查看和注释
  - 基础工具使用

#### 6. 只读用户 (Read-only User)
- **权限范围**：只读查看权限
- **主要职责**：
  - 模型查看
  - 文档阅读
  - 报告查看
  - 基础搜索功能

### 权限矩阵

| 功能模块 | 系统管理员 | 项目管理员 | 建模工程师 | 领域专家 | 普通用户 | 只读用户 |
|---------|-----------|-----------|-----------|---------|---------|---------|
| 用户管理 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 项目管理 | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| 模型创建 | ✅ | ✅ | ✅ | ❌ | ✅ | ❌ |
| 模型编辑 | ✅ | ✅ | ✅ | ❌ | 🔶 | ❌ |
| 模型查看 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 模型验证 | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| 生物医学增强 | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| 工具链使用 | ✅ | ✅ | ✅ | ❌ | 🔶 | ❌ |
| 知识管理 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 系统配置 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

说明：✅ 完全权限，🔶 有限权限，❌ 无权限

## 💾 数据模型设计

### 核心实体关系图

```mermaid
erDiagram
    User ||--o{ UserSession : has
    User ||--o{ UserProfile : has
    User }o--o{ Role : assigned
    Role ||--o{ Permission : contains
    User ||--o{ Project : owns
    User }o--o{ Project : participates
    User ||--o{ UserPreference : configures
    User ||--o{ AuditLog : generates

    User {
        string id PK
        string username UK
        string email UK
        string password_hash
        string salt
        boolean is_active
        boolean is_verified
        datetime created_at
        datetime updated_at
        datetime last_login
        string created_by FK
    }

    UserProfile {
        string id PK
        string user_id FK
        string first_name
        string last_name
        string organization
        string department
        string title
        string phone
        string avatar_url
        text bio
        json additional_info
        datetime updated_at
    }

    UserSession {
        string id PK
        string user_id FK
        string token_jti
        string refresh_token
        datetime expires_at
        datetime refresh_expires_at
        string ip_address
        string user_agent
        string device_info
        boolean is_active
        datetime created_at
    }

    Role {
        string id PK
        string name UK
        string display_name
        text description
        string role_type
        boolean is_system_role
        boolean is_active
        datetime created_at
        string created_by FK
    }

    Permission {
        string id PK
        string name UK
        string resource
        string action
        text description
        string permission_type
        datetime created_at
    }

    UserPreference {
        string id PK
        string user_id FK
        string category
        string preference_key
        json preference_value
        datetime updated_at
    }

    AuditLog {
        string id PK
        string user_id FK
        string action
        string resource_type
        string resource_id
        json old_values
        json new_values
        string ip_address
        string user_agent
        datetime timestamp
    }
```

## 🔧 技术实现方案

### 后端技术实现

#### 1. 用户模型定义
```python
# backend/database/models/user.py
from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    salt = Column(String(64), nullable=False)
    
    # 状态字段
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime)
    
    # 关系
    profile = relationship("UserProfile", back_populates="user", uselist=False)
    sessions = relationship("UserSession", back_populates="user")
    roles = relationship("Role", secondary="user_roles", back_populates="users")
    preferences = relationship("UserPreference", back_populates="user")
    audit_logs = relationship("AuditLog", back_populates="user")

class UserProfile(Base):
    __tablename__ = 'user_profiles'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey('users.id'), nullable=False)
    
    # 基本信息
    first_name = Column(String(50))
    last_name = Column(String(50))
    organization = Column(String(100))
    department = Column(String(100))
    title = Column(String(100))
    phone = Column(String(20))
    avatar_url = Column(String(500))
    bio = Column(Text)
    
    # 扩展信息
    additional_info = Column(JSON, default={})
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="profile")
```

#### 2. 认证服务实现
```python
# backend/services/auth_service.py
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import jwt
import bcrypt
import secrets
from fastapi import HTTPException, status

class AuthService:
    def __init__(self, config: Dict[str, Any]):
        self.secret_key = config['JWT_SECRET_KEY']
        self.algorithm = config['JWT_ALGORITHM']
        self.access_token_expire_minutes = config['ACCESS_TOKEN_EXPIRE_MINUTES']
        self.refresh_token_expire_days = config['REFRESH_TOKEN_EXPIRE_DAYS']
        
    def hash_password(self, password: str) -> tuple[str, str]:
        """生成密码哈希和盐值"""
        salt = secrets.token_hex(32)
        password_hash = bcrypt.hashpw(
            (password + salt).encode('utf-8'), 
            bcrypt.gensalt()
        ).decode('utf-8')
        return password_hash, salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(
            (password + salt).encode('utf-8'),
            password_hash.encode('utf-8')
        )
    
    def create_access_token(self, user_id: str, user_data: Dict[str, Any]) -> str:
        """创建访问令牌"""
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            'user_id': user_id,
            'username': user_data.get('username'),
            'email': user_data.get('email'),
            'roles': user_data.get('roles', []),
            'exp': expire,
            'iat': datetime.utcnow(),
            'type': 'access'
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user_id: str) -> str:
        """创建刷新令牌"""
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        jti = secrets.token_hex(32)
        
        payload = {
            'user_id': user_id,
            'exp': expire,
            'iat': datetime.utcnow(),
            'jti': jti,
            'type': 'refresh'
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token已过期"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的Token"
            )
```

#### 3. API路由实现
```python
# backend/api/routers/auth.py
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
import re

router = APIRouter(prefix="/auth", tags=["认证"])
security = HTTPBearer()

class UserRegisterRequest(BaseModel):
    username: str
    email: EmailStr
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    organization: Optional[str] = None

class UserLoginRequest(BaseModel):
    login: str  # 可以是用户名或邮箱
    password: str
    remember_me: bool = False

class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user_info: Dict[str, Any]

@router.post("/register", response_model=TokenResponse)
async def register_user(
    request: UserRegisterRequest,
    db: Session = Depends(get_database_session),
    user_service: UserService = Depends(get_user_service)
):
    """用户注册"""
    
    # 验证用户名格式
    if not re.match(r'^[a-zA-Z0-9_]{3,20}$', request.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名只能包含字母、数字和下划线，长度3-20位"
        )
    
    # 验证密码强度
    if not validate_password_strength(request.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="密码必须包含大小写字母、数字和特殊字符，长度至少8位"
        )
    
    try:
        # 创建用户
        user = await user_service.create_user(request)
        
        # 生成令牌
        tokens = await user_service.generate_user_tokens(user)
        
        return TokenResponse(
            access_token=tokens['access_token'],
            refresh_token=tokens['refresh_token'],
            expires_in=tokens['expires_in'],
            user_info=tokens['user_info']
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.post("/login", response_model=TokenResponse)
async def login_user(
    request: UserLoginRequest,
    db: Session = Depends(get_database_session),
    auth_service: AuthService = Depends(get_auth_service)
):
    """用户登录"""
    
    try:
        # 验证用户凭据
        user = await auth_service.authenticate_user(request.login, request.password)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="账户已被禁用"
            )
        
        # 生成令牌
        tokens = await auth_service.generate_user_tokens(user, request.remember_me)
        
        # 记录登录日志
        await auth_service.log_user_login(user)
        
        return TokenResponse(
            access_token=tokens['access_token'],
            refresh_token=tokens['refresh_token'],
            expires_in=tokens['expires_in'],
            user_info=tokens['user_info']
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )

def validate_password_strength(password: str) -> bool:
    """验证密码强度"""
    if len(password) < 8:
        return False
    
    patterns = [
        r'[a-z]',  # 小写字母
        r'[A-Z]',  # 大写字母
        r'\d',     # 数字
        r'[!@#$%^&*(),.?":{}|<>]'  # 特殊字符
    ]
    
    return all(re.search(pattern, password) for pattern in patterns)
```

### 前端技术实现

#### 1. 用户状态管理
```typescript
// frontend/src/stores/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI, userAPI } from '@/api'
import type { User, UserProfile, LoginRequest, RegisterRequest } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const userProfile = ref<UserProfile | null>(null)
  const isAuthenticated = ref(false)
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const permissions = ref<string[]>([])
  const roles = ref<string[]>([])

  // 计算属性
  const userDisplayName = computed(() => {
    if (!userProfile.value) return currentUser.value?.username || ''
    
    const { first_name, last_name } = userProfile.value
    if (first_name && last_name) {
      return `${first_name} ${last_name}`
    }
    return first_name || last_name || currentUser.value?.username || ''
  })

  const hasRole = computed(() => (role: string) => {
    return roles.value.includes(role)
  })

  const hasPermission = computed(() => (permission: string) => {
    return permissions.value.includes(permission)
  })

  const canAccess = computed(() => (resource: string, action: string = 'read') => {
    const permission = `${resource}:${action}`
    return hasPermission.value(permission) || hasRole.value('system_admin')
  })

  // 动作
  const login = async (credentials: LoginRequest) => {
    try {
      const response = await authAPI.login(credentials)
      
      // 存储令牌
      accessToken.value = response.access_token
      refreshToken.value = response.refresh_token
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)
      
      // 设置用户信息
      currentUser.value = response.user_info
      isAuthenticated.value = true
      
      // 加载用户详细信息
      await loadUserProfile()
      await loadUserPermissions()
      
      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  const register = async (userData: RegisterRequest) => {
    try {
      const response = await authAPI.register(userData)
      
      // 注册成功后自动登录
      accessToken.value = response.access_token
      refreshToken.value = response.refresh_token
      localStorage.setItem('access_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)
      
      currentUser.value = response.user_info
      isAuthenticated.value = true
      
      await loadUserProfile()
      await loadUserPermissions()
      
      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      if (refreshToken.value) {
        await authAPI.logout(refreshToken.value)
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      currentUser.value = null
      userProfile.value = null
      isAuthenticated.value = false
      accessToken.value = null
      refreshToken.value = null
      permissions.value = []
      roles.value = []
      
      // 清除本地存储
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }

  const loadUserProfile = async () => {
    if (!currentUser.value) return
    
    try {
      const profile = await userAPI.getProfile(currentUser.value.id)
      userProfile.value = profile
    } catch (error) {
      console.error('加载用户资料失败:', error)
    }
  }

  const loadUserPermissions = async () => {
    if (!currentUser.value) return
    
    try {
      const userPermissions = await userAPI.getPermissions(currentUser.value.id)
      permissions.value = userPermissions.permissions
      roles.value = userPermissions.roles
    } catch (error) {
      console.error('加载用户权限失败:', error)
    }
  }

  const updateProfile = async (profileData: Partial<UserProfile>) => {
    if (!currentUser.value) return
    
    try {
      const updatedProfile = await userAPI.updateProfile(currentUser.value.id, profileData)
      userProfile.value = updatedProfile
      return updatedProfile
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw error
    }
  }

  const changePassword = async (oldPassword: string, newPassword: string) => {
    if (!currentUser.value) return
    
    try {
      await userAPI.changePassword({
        user_id: currentUser.value.id,
        old_password: oldPassword,
        new_password: newPassword
      })
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  }

  const initializeAuth = async () => {
    const token = localStorage.getItem('access_token')
    if (!token) return false
    
    try {
      // 验证令牌并获取用户信息
      const userInfo = await authAPI.verifyToken(token)
      
      accessToken.value = token
      refreshToken.value = localStorage.getItem('refresh_token')
      currentUser.value = userInfo
      isAuthenticated.value = true
      
      await loadUserProfile()
      await loadUserPermissions()
      
      return true
    } catch (error) {
      console.error('初始化认证失败:', error)
      await logout()
      return false
    }
  }

  return {
    // 状态
    currentUser,
    userProfile,
    isAuthenticated,
    permissions,
    roles,
    
    // 计算属性
    userDisplayName,
    hasRole,
    hasPermission,
    canAccess,
    
    // 动作
    login,
    register,
    logout,
    loadUserProfile,
    loadUserPermissions,
    updateProfile,
    changePassword,
    initializeAuth
  }
})
```

#### 2. 登录界面组件
```vue
<!-- frontend/src/views/auth/LoginView.vue -->
<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1 class="login-title">生物医学MBSE建模平台</h1>
        <p class="login-subtitle">Model-Based Systems Engineering Platform</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="login">
          <el-input
            v-model="loginForm.login"
            placeholder="用户名或邮箱"
            prefix-icon="User"
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.remember_me">
              记住我
            </el-checkbox>
            <el-link type="primary" @click="showForgotPassword">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="isLoading"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="register-link">
            还没有账户？
            <el-link type="primary" @click="goToRegister">
              立即注册
            </el-link>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="showForgotPasswordDialog"
      title="重置密码"
      width="400px"
    >
      <ForgotPasswordForm @success="handleForgotPasswordSuccess" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { useUserStore } from '@/stores/user'
import type { FormInstance, FormRules } from 'element-plus'
import type { LoginRequest } from '@/types/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loginFormRef = ref<FormInstance>()
const isLoading = ref(false)
const showForgotPasswordDialog = ref(false)

const loginForm = reactive<LoginRequest>({
  login: '',
  password: '',
  remember_me: false
})

// 表单验证规则
const loginRules: FormRules = {
  login: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, message: '用户名至少3位字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6位字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    
    isLoading.value = true
    
    await userStore.login(loginForm)
    
    ElNotification({
      title: '登录成功',
      message: `欢迎回来，${userStore.userDisplayName}！`,
      type: 'success'
    })
    
    // 跳转到首页或之前的页面
    const redirect = router.currentRoute.value.query.redirect as string
    await router.push(redirect || '/workspace')
    
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '登录失败，请检查用户名和密码')
  } finally {
    isLoading.value = false
  }
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  showForgotPasswordDialog.value = true
}

// 处理忘记密码成功
const handleForgotPasswordSuccess = () => {
  showForgotPasswordDialog.value = false
  ElMessage.success('密码重置邮件已发送，请查收邮箱')
}

// 跳转到注册页面
const goToRegister = () => {
  router.push('/auth/register')
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.login-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.login-form {
  width: 100%;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: #909399;
}
</style>
```

## 📈 实施计划

### 第一周开发计划 (最高优先级)

#### Day 1-2: 后端基础架构
- [x] 数据库模型设计和迁移脚本
- [x] 用户认证服务基础架构
- [x] JWT令牌生成和验证机制
- [x] 密码哈希和安全策略

#### Day 3-4: API接口开发
- [x] 用户注册/登录API
- [x] 用户资料管理API
- [x] 密码重置API
- [x] 权限验证中间件

#### Day 5-6: 前端用户界面
- [x] 登录/注册页面组件
- [x] 用户个人中心界面
- [x] 权限控制组件
- [x] 会话状态管理

#### Day 7: 集成测试和部署
- [x] 端到端功能测试
- [x] 安全性测试
- [x] 性能测试
- [x] Docker容器部署

### 验收标准

#### 功能完整性
- [x] **用户注册**：支持邮箱验证、密码强度检查
- [x] **用户登录**：JWT认证、记住登录状态
- [x] **密码管理**：密码重置、修改密码功能
- [x] **会话管理**：自动续期、安全登出
- [x] **权限控制**：角色权限、API访问控制

#### 安全性要求
- [x] **密码安全**：BCrypt哈希、盐值保护
- [x] **会话安全**：JWT令牌、XSS/CSRF防护
- [x] **访问控制**：细粒度权限验证
- [x] **数据保护**：敏感信息加密存储

#### 性能指标
- [x] **响应时间**：登录响应<500ms
- [x] **并发支持**：支持100+并发登录
- [x] **会话管理**：令牌自动刷新机制
- [x] **缓存策略**：Redis会话缓存

## 🔒 安全考虑

### 认证安全
- **密码策略**：强制复杂密码、定期更换
- **多因子认证**：可选的二次验证（未来版本）
- **账户锁定**：连续登录失败自动锁定
- **设备管理**：可信设备记录和管理

### 数据安全
- **传输加密**：HTTPS/TLS 1.3
- **存储加密**：敏感数据AES加密
- **日志审计**：完整的用户操作日志
- **权限最小化**：最小权限原则

### 系统安全
- **输入验证**：所有用户输入严格验证
- **SQL注入防护**：ORM参数化查询
- **XSS防护**：输出转义和CSP策略
- **CSRF保护**：令牌验证机制

## 🚀 未来扩展

### 短期增强 (1个月内)
- **多因子认证**：TOTP、短信验证码
- **单点登录**：OAuth2.0、SAML2.0
- **社交登录**：GitHub、Google、微信
- **细粒度权限**：资源级权限控制

### 中期发展 (3个月内)
- **用户组织架构**：部门、团队层级管理
- **审批工作流**：用户注册审批流程
- **用户画像**：行为分析、偏好学习
- **智能推荐**：个性化内容推荐

### 长期规划 (6个月内)
- **联邦身份认证**：跨组织身份联邦
- **零信任架构**：基于身份的安全模型
- **AI安全助手**：异常行为检测
- **区块链身份**：去中心化身份验证

---

**总结**：本用户系统设计方案完全基于实施规划的第一阶段要求，采用现代化的技术架构，确保安全性、可扩展性和用户体验。系统将为后续的文件管理、模型解析和建模功能提供坚实的用户基础。 