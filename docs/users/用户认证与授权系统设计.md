# 用户认证与授权系统设计

## 📋 项目概述

**文档名称**：用户认证与授权系统设计  
**设计时间**：2024年12月  
**版本**：v1.0  
**目标**：为生物医学MBSE建模平台构建安全、可扩展的认证授权系统

## 🎯 系统目标

### 核心需求
- **🔐 安全认证**：基于JWT的无状态认证机制
- **🔑 精细授权**：基于RBAC的细粒度权限控制
- **📱 多端支持**：Web、移动端、API多端统一认证
- **🛡️ 安全防护**：完整的安全防护策略
- **⚡ 高性能**：高并发下的认证性能保证

### 技术要求
- **标准合规**：遵循OAuth 2.0、JWT、RBAC标准
- **无状态设计**：支持分布式部署和水平扩展
- **缓存优化**：Redis缓存提升认证性能
- **审计日志**：完整的安全审计追踪

## 🏗️ 认证系统架构

### 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                    认证授权系统架构                       │
├─────────────────────────────────────────────────────────┤
│                     客户端层                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  Web应用    │ │  移动应用   │ │  第三方应用 │        │
│  │  (Vue 3)    │ │  (未来)     │ │  (API调用)  │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                     网关层                               │
│  ┌─────────────────────────────────────────────────────┐ │
│  │               认证中间件                             │ │
│  │  Token验证  │  权限检查  │  限流控制  │  审计日志    │ │
│  └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                     认证服务层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  认证管理器 │ │  令牌管理器 │ │  权限引擎   │        │
│  │AuthManager  │ │TokenManager │ │PermissionEngine│     │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│                     数据层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  用户数据库 │ │  权限数据库 │ │  会话缓存   │        │
│  │ PostgreSQL  │ │ PostgreSQL  │ │   Redis     │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

## 🔐 JWT认证机制

### Token结构设计

#### Access Token (访问令牌)
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": "uuid-string",
    "username": "john_doe",
    "email": "<EMAIL>",
    "roles": ["modeling_engineer", "domain_expert"],
    "permissions": [
      "projects:read",
      "projects:write",
      "models:create",
      "models:edit"
    ],
    "iss": "biomedical-mbse-platform",
    "aud": "platform-users",
    "iat": **********,
    "exp": **********,
    "type": "access"
  }
}
```

#### Refresh Token (刷新令牌)
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": "uuid-string",
    "jti": "unique-token-id",
    "iss": "biomedical-mbse-platform",
    "aud": "platform-users", 
    "iat": **********,
    "exp": **********,
    "type": "refresh"
  }
}
```

### Token管理器实现

```python
# backend/services/token_manager.py
import jwt
import redis
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class TokenConfig:
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 60
    refresh_token_expire_days: int = 30
    issuer: str = "biomedical-mbse-platform"
    audience: str = "platform-users"

class TokenManager:
    def __init__(self, config: TokenConfig, redis_client: redis.Redis):
        self.config = config
        self.redis = redis_client
        
    def create_token_pair(self, user_data: Dict[str, Any]) -> Dict[str, str]:
        """创建访问令牌和刷新令牌对"""
        access_token = self._create_access_token(user_data)
        refresh_token = self._create_refresh_token(user_data['user_id'])
        
        # 存储刷新令牌到Redis
        self._store_refresh_token(refresh_token, user_data['user_id'])
        
        return {
            'access_token': access_token,
            'refresh_token': refresh_token,
            'token_type': 'bearer',
            'expires_in': self.config.access_token_expire_minutes * 60
        }
    
    def _create_access_token(self, user_data: Dict[str, Any]) -> str:
        """创建访问令牌"""
        now = datetime.utcnow()
        expire = now + timedelta(minutes=self.config.access_token_expire_minutes)
        
        payload = {
            'user_id': user_data['user_id'],
            'username': user_data['username'],
            'email': user_data['email'],
            'roles': user_data.get('roles', []),
            'permissions': user_data.get('permissions', []),
            'iss': self.config.issuer,
            'aud': self.config.audience,
            'iat': now.timestamp(),
            'exp': expire.timestamp(),
            'type': 'access'
        }
        
        return jwt.encode(payload, self.config.secret_key, algorithm=self.config.algorithm)
    
    def _create_refresh_token(self, user_id: str) -> str:
        """创建刷新令牌"""
        now = datetime.utcnow()
        expire = now + timedelta(days=self.config.refresh_token_expire_days)
        jti = secrets.token_urlsafe(32)
        
        payload = {
            'user_id': user_id,
            'jti': jti,
            'iss': self.config.issuer,
            'aud': self.config.audience,
            'iat': now.timestamp(),
            'exp': expire.timestamp(),
            'type': 'refresh'
        }
        
        return jwt.encode(payload, self.config.secret_key, algorithm=self.config.algorithm)
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(
                token, 
                self.config.secret_key, 
                algorithms=[self.config.algorithm],
                audience=self.config.audience,
                issuer=self.config.issuer
            )
            
            # 检查令牌类型
            if payload.get('type') not in ['access', 'refresh']:
                raise jwt.InvalidTokenError("Invalid token type")
            
            return payload
            
        except jwt.ExpiredSignatureError:
            raise TokenExpiredException("Token已过期")
        except jwt.InvalidTokenError as e:
            raise TokenInvalidException(f"无效Token: {str(e)}")
    
    def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
        """使用刷新令牌获取新的访问令牌"""
        try:
            # 验证刷新令牌
            payload = self.verify_token(refresh_token)
            
            if payload.get('type') != 'refresh':
                raise TokenInvalidException("非刷新令牌")
            
            user_id = payload['user_id']
            jti = payload['jti']
            
            # 检查令牌是否在黑名单中
            if self._is_token_blacklisted(jti):
                raise TokenInvalidException("令牌已被撤销")
            
            # 从数据库获取最新用户信息
            user_data = self._get_user_data(user_id)
            
            # 生成新的访问令牌
            access_token = self._create_access_token(user_data)
            
            return {
                'access_token': access_token,
                'token_type': 'bearer',
                'expires_in': self.config.access_token_expire_minutes * 60
            }
            
        except Exception as e:
            raise TokenRefreshException(f"令牌刷新失败: {str(e)}")
    
    def revoke_token(self, token: str) -> bool:
        """撤销令牌"""
        try:
            payload = self.verify_token(token)
            
            if payload.get('type') == 'refresh':
                # 将刷新令牌加入黑名单
                jti = payload['jti']
                exp = payload['exp']
                ttl = int(exp - datetime.utcnow().timestamp())
                
                if ttl > 0:
                    self.redis.setex(f"blacklist:{jti}", ttl, "revoked")
                
            return True
            
        except Exception as e:
            print(f"Token revocation failed: {e}")
            return False
    
    def _store_refresh_token(self, token: str, user_id: str):
        """存储刷新令牌信息"""
        payload = jwt.decode(token, self.config.secret_key, algorithms=[self.config.algorithm])
        jti = payload['jti']
        exp = payload['exp']
        
        # 计算TTL
        ttl = int(exp - datetime.utcnow().timestamp())
        
        if ttl > 0:
            # 存储令牌映射
            self.redis.setex(f"refresh_token:{jti}", ttl, user_id)
            
            # 存储用户活跃令牌
            self.redis.sadd(f"user_tokens:{user_id}", jti)
            self.redis.expire(f"user_tokens:{user_id}", ttl)
    
    def _is_token_blacklisted(self, jti: str) -> bool:
        """检查令牌是否被列入黑名单"""
        return self.redis.exists(f"blacklist:{jti}")
    
    def _get_user_data(self, user_id: str) -> Dict[str, Any]:
        """获取用户数据（需要注入用户服务）"""
        # 这里应该调用用户服务获取最新的用户信息
        pass

class TokenExpiredException(Exception):
    pass

class TokenInvalidException(Exception):
    pass

class TokenRefreshException(Exception):
    pass
```

## 🔑 RBAC权限模型

### 权限模型设计

```python
# backend/models/rbac_models.py
from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

# 用户-角色关联表
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', String, ForeignKey('users.id'), primary_key=True),
    Column('role_id', String, ForeignKey('roles.id'), primary_key=True),
    Column('assigned_at', DateTime, default=datetime.utcnow),
    Column('assigned_by', String, ForeignKey('users.id')),
    Column('expires_at', DateTime, nullable=True)
)

# 角色-权限关联表
role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', String, ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', String, ForeignKey('permissions.id'), primary_key=True),
    Column('granted_at', DateTime, default=datetime.utcnow),
    Column('granted_by', String, ForeignKey('users.id'))
)

class Role(Base):
    __tablename__ = 'roles'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(50), unique=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 角色类型
    role_type = Column(String(20), default='custom')  # system, custom
    is_system_role = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    
    # 继承关系
    parent_role_id = Column(String, ForeignKey('roles.id'), nullable=True)
    parent_role = relationship("Role", remote_side=[id], backref="child_roles")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String, ForeignKey('users.id'))
    
    # 关系
    users = relationship("User", secondary=user_roles, back_populates="roles")
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")

class Permission(Base):
    __tablename__ = 'permissions'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 权限定义
    resource = Column(String(50), nullable=False)  # 资源类型
    action = Column(String(50), nullable=False)    # 操作类型
    scope = Column(String(50), default='global')   # 权限范围
    
    # 权限类型
    permission_type = Column(String(20), default='functional')  # functional, data, administrative
    is_system_permission = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")

class ResourcePermission(Base):
    __tablename__ = 'resource_permissions'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey('users.id'), nullable=False)
    resource_type = Column(String(50), nullable=False)
    resource_id = Column(String, nullable=False)
    permission = Column(String(50), nullable=False)
    
    # 授权信息
    granted_by = Column(String, ForeignKey('users.id'))
    granted_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)
    
    # 关系
    user = relationship("User", foreign_keys=[user_id])
    granter = relationship("User", foreign_keys=[granted_by])
```

### 权限引擎实现

```python
# backend/services/permission_engine.py
from typing import List, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass

class PermissionResult(Enum):
    GRANTED = "granted"
    DENIED = "denied"
    CONDITIONAL = "conditional"

@dataclass
class PermissionContext:
    user_id: str
    resource_type: str
    resource_id: Optional[str] = None
    action: str = "read"
    additional_context: Dict[str, Any] = None

class PermissionEngine:
    def __init__(self, db_session, cache_manager):
        self.db = db_session
        self.cache = cache_manager
        
    def check_permission(self, context: PermissionContext) -> PermissionResult:
        """检查用户权限"""
        
        # 1. 检查缓存
        cache_key = self._build_cache_key(context)
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return PermissionResult(cached_result)
        
        # 2. 系统管理员直接通过
        if self._is_system_admin(context.user_id):
            result = PermissionResult.GRANTED
            self.cache.set(cache_key, result.value, ttl=300)
            return result
        
        # 3. 检查角色权限
        role_result = self._check_role_permissions(context)
        if role_result == PermissionResult.GRANTED:
            self.cache.set(cache_key, result.value, ttl=300)
            return role_result
        
        # 4. 检查资源级权限
        resource_result = self._check_resource_permissions(context)
        if resource_result == PermissionResult.GRANTED:
            self.cache.set(cache_key, result.value, ttl=300)
            return resource_result
        
        # 5. 检查条件权限
        conditional_result = self._check_conditional_permissions(context)
        
        # 6. 缓存结果
        final_result = conditional_result or PermissionResult.DENIED
        self.cache.set(cache_key, final_result.value, ttl=300)
        
        return final_result
    
    def _check_role_permissions(self, context: PermissionContext) -> PermissionResult:
        """检查角色权限"""
        
        # 获取用户角色
        user_roles = self._get_user_roles(context.user_id)
        
        # 构建权限标识符
        permission_name = f"{context.resource_type}:{context.action}"
        
        # 检查角色是否包含所需权限
        for role in user_roles:
            if self._role_has_permission(role.id, permission_name):
                return PermissionResult.GRANTED
        
        return PermissionResult.DENIED
    
    def _check_resource_permissions(self, context: PermissionContext) -> PermissionResult:
        """检查资源级权限"""
        
        if not context.resource_id:
            return PermissionResult.DENIED
        
        # 查询用户对特定资源的权限
        resource_permission = self.db.query(ResourcePermission).filter(
            ResourcePermission.user_id == context.user_id,
            ResourcePermission.resource_type == context.resource_type,
            ResourcePermission.resource_id == context.resource_id,
            ResourcePermission.permission == context.action,
            ResourcePermission.is_active == True
        ).first()
        
        if resource_permission:
            # 检查权限是否过期
            if resource_permission.expires_at and resource_permission.expires_at < datetime.utcnow():
                return PermissionResult.DENIED
            return PermissionResult.GRANTED
        
        return PermissionResult.DENIED
    
    def _check_conditional_permissions(self, context: PermissionContext) -> PermissionResult:
        """检查条件权限"""
        
        # 实现业务特定的条件权限逻辑
        # 例如：项目成员可以查看项目内容
        if context.resource_type == "projects" and context.action == "read":
            if self._is_project_member(context.user_id, context.resource_id):
                return PermissionResult.CONDITIONAL
        
        # 例如：模型创建者可以编辑自己的模型
        if context.resource_type == "models" and context.action in ["edit", "delete"]:
            if self._is_model_owner(context.user_id, context.resource_id):
                return PermissionResult.CONDITIONAL
        
        return PermissionResult.DENIED
    
    def get_user_permissions(self, user_id: str) -> List[str]:
        """获取用户的所有权限"""
        
        cache_key = f"user_permissions:{user_id}"
        cached_permissions = self.cache.get(cache_key)
        if cached_permissions:
            return cached_permissions
        
        permissions = set()
        
        # 1. 从角色获取权限
        user_roles = self._get_user_roles(user_id)
        for role in user_roles:
            role_permissions = self._get_role_permissions(role.id)
            permissions.update(role_permissions)
        
        # 2. 从资源权限获取
        resource_permissions = self.db.query(ResourcePermission).filter(
            ResourcePermission.user_id == user_id,
            ResourcePermission.is_active == True
        ).all()
        
        for rp in resource_permissions:
            if not rp.expires_at or rp.expires_at > datetime.utcnow():
                permissions.add(f"{rp.resource_type}:{rp.permission}")
        
        permission_list = list(permissions)
        self.cache.set(cache_key, permission_list, ttl=300)
        
        return permission_list
    
    def grant_resource_permission(self, user_id: str, resource_type: str, 
                                resource_id: str, permission: str, 
                                granted_by: str, expires_at: Optional[datetime] = None) -> bool:
        """授予资源权限"""
        
        try:
            resource_permission = ResourcePermission(
                user_id=user_id,
                resource_type=resource_type,
                resource_id=resource_id,
                permission=permission,
                granted_by=granted_by,
                expires_at=expires_at
            )
            
            self.db.add(resource_permission)
            self.db.commit()
            
            # 清除缓存
            self._clear_user_permission_cache(user_id)
            
            return True
            
        except Exception as e:
            self.db.rollback()
            print(f"Failed to grant permission: {e}")
            return False
    
    def revoke_resource_permission(self, user_id: str, resource_type: str, 
                                 resource_id: str, permission: str) -> bool:
        """撤销资源权限"""
        
        try:
            resource_permission = self.db.query(ResourcePermission).filter(
                ResourcePermission.user_id == user_id,
                ResourcePermission.resource_type == resource_type,
                ResourcePermission.resource_id == resource_id,
                ResourcePermission.permission == permission,
                ResourcePermission.is_active == True
            ).first()
            
            if resource_permission:
                resource_permission.is_active = False
                self.db.commit()
                
                # 清除缓存
                self._clear_user_permission_cache(user_id)
                
                return True
            
            return False
            
        except Exception as e:
            self.db.rollback()
            print(f"Failed to revoke permission: {e}")
            return False
    
    def _build_cache_key(self, context: PermissionContext) -> str:
        """构建缓存键"""
        parts = [
            "perm",
            context.user_id,
            context.resource_type,
            context.resource_id or "global",
            context.action
        ]
        return ":".join(parts)
    
    def _clear_user_permission_cache(self, user_id: str):
        """清除用户权限缓存"""
        patterns = [
            f"user_permissions:{user_id}",
            f"perm:{user_id}:*"
        ]
        for pattern in patterns:
            self.cache.delete_pattern(pattern)
```

## 🛡️ 安全防护策略

### 认证中间件
```python
# backend/middleware/auth_middleware.py
from fastapi import Request, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import time
import hashlib

class AuthMiddleware:
    def __init__(self, token_manager, permission_engine, rate_limiter):
        self.token_manager = token_manager
        self.permission_engine = permission_engine
        self.rate_limiter = rate_limiter
        self.security = HTTPBearer()
    
    async def __call__(self, request: Request, call_next):
        """认证中间件主函数"""
        
        # 1. 速率限制检查
        await self._check_rate_limit(request)
        
        # 2. 跳过不需要认证的路径
        if self._is_public_path(request.url.path):
            return await call_next(request)
        
        # 3. 提取和验证Token
        token = await self._extract_token(request)
        user_context = await self._verify_token(token)
        
        # 4. 权限检查
        await self._check_permissions(request, user_context)
        
        # 5. 设置用户上下文
        request.state.user = user_context
        
        # 6. 记录审计日志
        await self._log_access(request, user_context)
        
        response = await call_next(request)
        return response
    
    async def _check_rate_limit(self, request: Request):
        """速率限制检查"""
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        
        # 基于IP的限制
        ip_key = f"rate_limit:ip:{client_ip}"
        if not await self.rate_limiter.allow_request(ip_key, max_requests=100, window=60):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="请求过于频繁，请稍后再试"
            )
        
        # 基于User-Agent的限制（防止爬虫）
        ua_hash = hashlib.md5(user_agent.encode()).hexdigest()
        ua_key = f"rate_limit:ua:{ua_hash}"
        if not await self.rate_limiter.allow_request(ua_key, max_requests=1000, window=3600):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="检测到异常请求模式，请联系管理员"
            )
    
    def _is_public_path(self, path: str) -> bool:
        """检查是否为公开路径"""
        public_paths = [
            "/auth/login",
            "/auth/register",
            "/auth/forgot-password",
            "/auth/reset-password",
            "/health",
            "/docs",
            "/openapi.json"
        ]
        return any(path.startswith(public_path) for public_path in public_paths)
    
    async def _extract_token(self, request: Request) -> str:
        """提取Token"""
        
        # 1. 从Authorization Header提取
        auth_header = request.headers.get("authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]
        
        # 2. 从Cookie提取（可选）
        token = request.cookies.get("access_token")
        if token:
            return token
        
        # 3. 从查询参数提取（仅用于特殊场景）
        token = request.query_params.get("access_token")
        if token:
            return token
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少认证Token"
        )
    
    async def _verify_token(self, token: str) -> Dict[str, Any]:
        """验证Token"""
        try:
            payload = self.token_manager.verify_token(token)
            
            if payload.get('type') != 'access':
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的Token类型"
                )
            
            return payload
            
        except TokenExpiredException:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token已过期，请重新登录"
            )
        except TokenInvalidException as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"无效Token: {str(e)}"
            )
    
    async def _check_permissions(self, request: Request, user_context: Dict[str, Any]):
        """检查权限"""
        
        method = request.method
        path = request.url.path
        
        # 解析资源和操作
        resource_type, action = self._parse_resource_action(method, path)
        
        if resource_type and action:
            permission_context = PermissionContext(
                user_id=user_context['user_id'],
                resource_type=resource_type,
                action=action
            )
            
            result = self.permission_engine.check_permission(permission_context)
            
            if result == PermissionResult.DENIED:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足，无法访问该资源"
                )
    
    def _parse_resource_action(self, method: str, path: str) -> tuple:
        """解析资源类型和操作"""
        
        # HTTP方法到操作的映射
        method_action_map = {
            'GET': 'read',
            'POST': 'create',
            'PUT': 'update',
            'PATCH': 'update',
            'DELETE': 'delete'
        }
        
        action = method_action_map.get(method, 'read')
        
        # 路径到资源类型的映射
        if path.startswith('/api/projects'):
            return 'projects', action
        elif path.startswith('/api/models'):
            return 'models', action
        elif path.startswith('/api/diagrams'):
            return 'diagrams', action
        elif path.startswith('/api/users') and action in ['create', 'update', 'delete']:
            return 'users', action
        elif path.startswith('/api/admin'):
            return 'admin', action
        
        return None, None
    
    async def _log_access(self, request: Request, user_context: Dict[str, Any]):
        """记录访问日志"""
        log_data = {
            'user_id': user_context['user_id'],
            'username': user_context['username'],
            'method': request.method,
            'path': request.url.path,
            'ip_address': request.client.host,
            'user_agent': request.headers.get('user-agent', ''),
            'timestamp': time.time()
        }
        
        # 异步写入日志
        await self._write_audit_log(log_data)
```

### 安全配置
```python
# backend/config/security_config.py
from pydantic import BaseSettings
import secrets

class SecurityConfig(BaseSettings):
    # JWT配置
    JWT_SECRET_KEY: str = secrets.token_urlsafe(32)
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30
    
    # 密码策略
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_NUMBERS: bool = True
    PASSWORD_REQUIRE_SPECIAL: bool = True
    PASSWORD_MAX_AGE_DAYS: int = 90
    
    # 账户锁定策略
    MAX_LOGIN_ATTEMPTS: int = 5
    ACCOUNT_LOCKOUT_DURATION_MINUTES: int = 30
    
    # 会话管理
    MAX_CONCURRENT_SESSIONS: int = 5
    SESSION_TIMEOUT_MINUTES: int = 120
    
    # 速率限制
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = 100
    RATE_LIMIT_BURST_SIZE: int = 20
    
    # CORS配置
    CORS_ALLOW_ORIGINS: list = ["http://localhost:3000", "https://your-domain.com"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    CORS_ALLOW_HEADERS: list = ["*"]
    
    # 安全头配置
    SECURITY_HEADERS: dict = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=********; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'"
    }
    
    class Config:
        env_file = ".env"
        case_sensitive = True
```

## 📊 监控与审计

### 审计日志系统
```python
# backend/services/audit_service.py
from typing import Dict, Any, Optional
from datetime import datetime
import json

class AuditService:
    def __init__(self, db_session, log_storage):
        self.db = db_session
        self.storage = log_storage
    
    async def log_authentication_event(self, event_type: str, user_id: Optional[str], 
                                     ip_address: str, user_agent: str, 
                                     additional_data: Dict[str, Any] = None):
        """记录认证事件"""
        
        event = {
            'event_type': f"auth.{event_type}",
            'user_id': user_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'timestamp': datetime.utcnow().isoformat(),
            'additional_data': additional_data or {}
        }
        
        await self._write_audit_log(event)
    
    async def log_authorization_event(self, user_id: str, resource_type: str, 
                                    resource_id: Optional[str], action: str, 
                                    result: str, ip_address: str):
        """记录授权事件"""
        
        event = {
            'event_type': 'authorization.check',
            'user_id': user_id,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'action': action,
            'result': result,
            'ip_address': ip_address,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        await self._write_audit_log(event)
    
    async def log_data_access(self, user_id: str, operation: str, 
                            table_name: str, record_id: Optional[str],
                            old_values: Optional[Dict] = None,
                            new_values: Optional[Dict] = None):
        """记录数据访问事件"""
        
        event = {
            'event_type': f"data.{operation}",
            'user_id': user_id,
            'table_name': table_name,
            'record_id': record_id,
            'old_values': old_values,
            'new_values': new_values,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        await self._write_audit_log(event)
    
    async def _write_audit_log(self, event: Dict[str, Any]):
        """写入审计日志"""
        
        # 1. 写入数据库
        audit_log = AuditLog(
            user_id=event.get('user_id'),
            event_type=event['event_type'],
            ip_address=event.get('ip_address'),
            user_agent=event.get('user_agent'),
            event_data=event,
            timestamp=datetime.utcnow()
        )
        
        self.db.add(audit_log)
        
        # 2. 写入日志文件
        await self.storage.write_log(json.dumps(event))
        
        # 3. 对于敏感操作，实时告警
        if self._is_sensitive_event(event):
            await self._send_security_alert(event)
    
    def _is_sensitive_event(self, event: Dict[str, Any]) -> bool:
        """判断是否为敏感事件"""
        sensitive_events = [
            'auth.failed_login',
            'auth.account_locked',
            'authorization.denied',
            'data.delete',
            'admin.user_created',
            'admin.permission_granted'
        ]
        return event['event_type'] in sensitive_events
    
    async def _send_security_alert(self, event: Dict[str, Any]):
        """发送安全告警"""
        # 实现告警逻辑，如发送邮件、Slack通知等
        pass
```

## 📈 性能优化

### 缓存策略
```python
# backend/services/cache_manager.py
import redis
import json
import pickle
from typing import Any, Optional
from datetime import timedelta

class CacheManager:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.default_ttl = 300  # 5分钟
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            value = self.redis.get(key)
            if value:
                return pickle.loads(value)
            return None
        except Exception as e:
            print(f"Cache get error: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存值"""
        try:
            ttl = ttl or self.default_ttl
            serialized_value = pickle.dumps(value)
            return self.redis.setex(key, ttl, serialized_value)
        except Exception as e:
            print(f"Cache set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            return bool(self.redis.delete(key))
        except Exception as e:
            print(f"Cache delete error: {e}")
            return False
    
    async def delete_pattern(self, pattern: str) -> int:
        """按模式删除缓存"""
        try:
            keys = self.redis.keys(pattern)
            if keys:
                return self.redis.delete(*keys)
            return 0
        except Exception as e:
            print(f"Cache delete pattern error: {e}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            return bool(self.redis.exists(key))
        except Exception as e:
            print(f"Cache exists error: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1, ttl: int = None) -> int:
        """递增计数器"""
        try:
            value = self.redis.incr(key, amount)
            if ttl:
                self.redis.expire(key, ttl)
            return value
        except Exception as e:
            print(f"Cache increment error: {e}")
            return 0
```

## 🚀 部署配置

### Docker配置
```dockerfile
# Dockerfile.auth-service
FROM python:3.11-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV JWT_SECRET_KEY=${JWT_SECRET_KEY}
ENV REDIS_URL=${REDIS_URL}
ENV DATABASE_URL=${DATABASE_URL}

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 环境配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  auth-service:
    build:
      context: .
      dockerfile: Dockerfile.auth-service
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=****************************************/auth_db
    depends_on:
      - redis
      - postgres
    ports:
      - "8000:8000"
    networks:
      - auth-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - auth-network
    volumes:
      - redis_data:/data

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=auth_db
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    networks:
      - auth-network
    volumes:
      - postgres_data:/var/lib/postgresql/data

networks:
  auth-network:
    driver: bridge

volumes:
  redis_data:
  postgres_data:
```

## 📋 总结

本认证授权系统设计提供了：

### 核心特性
- **🔐 JWT无状态认证**：支持分布式部署
- **🔑 RBAC权限模型**：灵活的角色权限管理
- **🛡️ 多层安全防护**：全面的安全策略
- **📊 完整审计追踪**：详细的操作记录
- **⚡ 高性能缓存**：Redis缓存优化

### 安全保障
- **密码安全**：强密码策略和哈希保护
- **令牌安全**：JWT签名验证和黑名单机制
- **会话安全**：并发控制和超时管理
- **访问控制**：细粒度权限验证
- **审计监控**：实时安全事件监控

### 扩展能力
- **水平扩展**：无状态设计支持集群部署
- **权限扩展**：支持复杂的业务权限需求
- **集成能力**：预留第三方认证集成接口
- **监控告警**：完整的安全监控体系

该系统为生物医学MBSE建模平台提供了企业级的认证授权解决方案，确保系统安全性和可扩展性。 